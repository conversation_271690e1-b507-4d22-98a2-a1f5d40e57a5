/**
 * DataService class for handling all data API calls
 * Provides methods to fetch market data including options, OHLC, futures, and quotes
 */
class DataService {
    
    /**
     * Initialize DataService with base URL
     * @param {string} baseUrl - Base URL for API endpoints (default: '/data')
     */
    constructor(baseUrl = '/data') {
        this.baseUrl = baseUrl;
    }

    /**
     * Get expiration dates for a given underlying symbol
     * @param {string} symbol - The underlying symbol (e.g., 'NIFTY', 'BANKNIFTY')
     * @returns {Promise<Object>} Promise resolving to object with symbol and expirations array
     * @throws {Error} If the API request fails
     */
    async getExpirations(symbol) {
        try {
            const response = await fetch(`${this.baseUrl}/expirations/${symbol}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching expirations:', error);
            throw error;
        }
    }

    /**
     * Get option chain data for a specific symbol and expiration date
     * @param {string} symbol - The underlying symbol (e.g., 'NIFTY', 'BANKNIFTY')
     * @param {string} expirationDate - Expiration date in YYYY-MM-DD format
     * @returns {Promise<Object>} Promise resolving to option chain data
     * @throws {Error} If the API request fails
     */
    async getOptionChain(symbol, expirationDate) {
        try {
            const response = await fetch(`${this.baseUrl}/options/${symbol}/${expirationDate}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching option chain:', error);
            throw error;
        }
    }

    /**
     * Get latest OHLC (Open, High, Low, Close) data for a symbol
     * @param {string} symbol - The symbol to fetch OHLC data for
     * @param {string} segment - Market segment (default: 'NSE_EQ')
     * @returns {Promise<Object>} Promise resolving to latest OHLC data
     * @throws {Error} If the API request fails
     */
    async getOhlc(symbol, segment = 'NSE_EQ') {
        try {
            const response = await fetch(`${this.baseUrl}/ohlc/latest/${symbol}?segment=${segment}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching OHLC:', error);
            throw error;
        }
    }

    /**
     * Get intraday OHLC data (minute candles) for a symbol within a date range
     * @param {string} symbol - The symbol to fetch intraday data for
     * @param {string} from - Start date in YYYY-MM-DD format
     * @param {string} to - End date in YYYY-MM-DD format
     * @param {number} interval - Time interval in minutes (1, 5, 15, 25, or 60)
     * @returns {Promise<Object>} Promise resolving to intraday OHLC data
     * @throws {Error} If the API request fails
     */
    async getOhlcIntraday(symbol, from, to, interval) {
        try {
            const response = await fetch(`${this.baseUrl}/ohlc/intraday/${symbol}/${from}/${to}/${interval}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching intraday OHLC:', error);
            throw error;
        }
    }

    /**
     * Get historical daily OHLC data for a symbol within a date range
     * @param {string} symbol - The symbol to fetch historical data for
     * @param {string} from - Start date in YYYY-MM-DD format
     * @param {string} to - End date in YYYY-MM-DD format
     * @param {number} expiry_code - Expiry code (0 for stocks, 1/2/3 for derivatives)
     * @returns {Promise<Object>} Promise resolving to historical OHLC data
     * @throws {Error} If the API request fails
     */
    async getOhlcHistorical(symbol, from, to, expiry_code = 0) {
        try {
            const response = await fetch(`${this.baseUrl}/ohlc/historical/${symbol}/${from}/${to}?expiry_code=${expiry_code}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching historical OHLC:', error);
            throw error;
        }
    }

    /**
     * Get all futures contracts for a given underlying symbol
     * @param {string} symbol - The underlying symbol (case-insensitive)
     * @returns {Promise<Array>} Promise resolving to array of futures contracts with expiry, security_id, display_name, and ticker_data
     * @throws {Error} If the API request fails
     */
    async getFutures(symbol) {
        try {
            const response = await fetch(`${this.baseUrl}/futures/${symbol}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching futures:', error);
            throw error;
        }
    }

    /**
     * Get Last Traded Price (LTP) data for a symbol
     * @param {string} symbol - The symbol to fetch LTP data for
     * @param {string} segment - Market segment (default: 'NSE_EQ')
     * @returns {Promise<Object>} Promise resolving to LTP (ticker) data
     * @throws {Error} If the API request fails
     * @note This may work only during market hours
     */
    async getLtp(symbol, segment = 'NSE_EQ') {
        try {
            const response = await fetch(`${this.baseUrl}/ltp/symbol/${symbol}?segment=${segment}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching LTP:', error);
            throw error;
        }
    }

    /**
     * Get full quote data for a symbol
     * @param {string} symbol - The symbol to fetch quote data for
     * @param {string} segment - Market segment (default: 'NSE_EQ')
     * @returns {Promise<Object>} Promise resolving to full quote data
     * @throws {Error} If the API request fails
     * @note This may work only during market hours
     */
    async getQuote(symbol, segment = 'NSE_EQ') {
        try {
            const response = await fetch(`${this.baseUrl}/quote/symbol/${symbol}?segment=${segment}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching quote:', error);
            throw error;
        }
    }

    // MULTIPLE METHODS - More efficient for multiple symbols

    /**
     * Get expiration dates for multiple symbols in a single API call
     * @param {Array<string>} symbols - Array of underlying symbols
     * @returns {Promise<Object>} Promise resolving to object with symbol as key and expiration data as value
     * @throws {Error} If the API request fails
     */
    async getMultipleExpirations(symbols) {
        try {
            const response = await fetch(`${this.baseUrl}/expirations`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ symbols })
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching multiple expirations:', error);
            throw error;
        }
    }

    /**
     * Get option chain data for multiple symbols with the same expiry in a single API call
     * @param {Array<string>} symbols - Array of underlying symbols
     * @param {string} expiry - Expiration date in YYYY-MM-DD format
     * @returns {Promise<Object>} Promise resolving to object with symbol as key and option chain as value
     * @throws {Error} If the API request fails
     */
    async getMultipleOptionChain(symbols, expiry) {
        try {
            const response = await fetch(`${this.baseUrl}/options`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ symbols, expiry })
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching multiple option chain:', error);
            throw error;
        }
    }

    /**
     * Get LTP data for multiple symbols in a single API call
     * @param {Array<string>} symbols - Array of symbols
     * @param {string} segment - Market segment (default: 'NSE_EQ')
     * @returns {Promise<Object>} Promise resolving to object with symbol as key and LTP data as value
     * @throws {Error} If the API request fails
     */
    async getMultipleLtp(symbols, segment = 'NSE_EQ') {
        try {
            const response = await fetch(`${this.baseUrl}/ltp/symbol`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ symbols, segment })
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching multiple LTP:', error);
            throw error;
        }
    }

    /**
     * Get futures data for multiple symbols in a single API call
     * @param {Array<string>} symbols - Array of underlying symbols
     * @returns {Promise<Object>} Promise resolving to object with symbol as key and futures data as value
     * @throws {Error} If the API request fails
     */
    async getMultipleFutures(symbols) {
        try {
            const response = await fetch(`${this.baseUrl}/futures`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ symbols })
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching multiple futures:', error);
            throw error;
        }
    }

    /**
     * Get OHLC data for multiple symbols in a single API call
     * @param {Array<string>} symbols - Array of symbols
     * @param {string} segment - Market segment (default: 'NSE_EQ')
     * @returns {Promise<Object>} Promise resolving to object with symbol as key and OHLC data as value
     * @throws {Error} If the API request fails
     */
    async getMultipleOhlc(symbols, segment = 'NSE_EQ') {
        try {
            const response = await fetch(`${this.baseUrl}/ohlc/latest`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ symbols, segment })
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching multiple OHLC:', error);
            throw error;
        }
    }

    /**
     * Get quote data for multiple symbols in a single API call
     * @param {Array<string>} symbols - Array of symbols
     * @param {string} segment - Market segment (default: 'NSE_EQ')
     * @returns {Promise<Object>} Promise resolving to object with symbol as key and quote data as value
     * @throws {Error} If the API request fails
     */
    async getMultipleQuote(symbols, segment = 'NSE_EQ') {
        try {
            const response = await fetch(`${this.baseUrl}/quote/symbol`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ symbols, segment })
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching multiple quote:', error);
            throw error;
        }
    }

}

// Initialize the DataService instance for global use
const dataService = new DataService();