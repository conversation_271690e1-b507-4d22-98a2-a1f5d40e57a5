let audioContext,
  player,
  recorder,
  isRecording = !1,
  analyser,
  canvas,
  canvasContext,
  animationId,
  websocket,
  accumulatedAudioData = [],
  sendInterval = 600,
  currentTranscriptElement = null,
  audioPlayerNode;
async function getMicrophonePermission() {
  try {
    return await navigator.mediaDevices.getUserMedia({ audio: !0 });
  } catch (e) {}
}
async function initializeRecorder() {
  var e = await getMicrophonePermission();
  e &&
    ("suspended" === (audioContext = new AudioContext({ sampleRate: 24e3, latencyHint: "playback" })).state && (await audioContext.resume()),
    await audioContext.audioWorklet.addModule("/static/js/recorder-worklet.js?v=3feab753-3fd0-4162-9709-265f5422ed3a"),
    (e = audioContext.createMediaStreamSource(e)),
    (recorder = new AudioWorkletNode(audioContext, "audio-recorder")),
    e.connect(recorder),
    recorder.connect(audioContext.destination),
    (analyser = new AudioAnalyser(audioContext)),
    e.connect(analyser.frequencyAnalyser),
    connectWebSocket(),
    (recorder.port.onmessage = (e) => {
      var t = e.data;
      accumulatedAudioData.push(t.buffer);
    }),
    startVisualizer(),
    startSendingAudioData());
}
async function initializeAudioPlayer() {
  await audioContext.audioWorklet.addModule("/static/js/player-worklet.js?v=3feab753-3fd0-4162-9709-265f5422ed3a"),
    (audioPlayerNode = new AudioWorkletNode(audioContext, "audio-player")).connect(audioContext.destination),
    (audioPlayerNode.port.onmessage = (e) => {
      var {} = e.data;
    });
}
function sendAudioToPlayer(e) {
  audioPlayerNode && audioPlayerNode.port.postMessage({ audioData: e });
}
function clearAudioPlayerQueue() {
  audioPlayerNode && audioPlayerNode.port.postMessage({ clear: !0 });
}
function concatenateBuffers(e) {
  var t,
    a = e.reduce((e, t) => e + t.byteLength, 0),
    n = new Uint8Array(a);
  let r = 0;
  for (t of e) n.set(new Uint8Array(t), r), (r += t.byteLength);
  return n.buffer;
}
function startSendingAudioData() {
  setInterval(() => {
    var e;
    0 < accumulatedAudioData.length &&
      ((e = arrayBufferToBase64(concatenateBuffers(accumulatedAudioData))), websocket && websocket.readyState === WebSocket.OPEN && websocket.send(JSON.stringify({ audio_data: e })), (accumulatedAudioData = []));
  }, sendInterval);
}
function arrayBufferToBase64(e) {
  let t = "";
  var a = new Uint8Array(e),
    n = a.byteLength;
  for (let r = 0; r < n; r++) t += String.fromCharCode(a[r]);
  return btoa(t);
}
function base64ToPCM16(e) {
  var t = atob(e),
    a = t.length,
    n = new Uint8Array(a);
  for (let o = 0; o < a; o++) n[o] = t.charCodeAt(o);
  var r = new Int16Array(n.buffer),
    i = new Float32Array(r.length);
  for (let s = 0; s < r.length; s++) i[s] = r[s] / 32768;
  return i;
}
function connectWebSocket() {
  let e = document.getElementById("progress-bar"),
    t = document.getElementById("progress-fill");
  (e.style.display = "flex"),
    (t.style.width = "0%"),
    ((websocket = new WebSocket(`${window.wsScheme}://${window.location.host}/ai-agent/rtmps/agent`)).onopen = () => {
      (t.style.width = "100%"),
        setTimeout(() => {
          e.style.display = "none";
        }, 1e3),
        setTimeout(() => {
          disconnectWebSocket();
        }, 2e5);
    }),
    (websocket.onclose = () => {}),
    (websocket.onerror = (e) => {}),
    (websocket.onmessage = (e) => {
      try {
        var t = e.data,
          a = JSON.parse(t);
        switch (a.type) {
          case "response.content_part.added":
            addNewTranscript();
            break;
          case "response.audio_transcript.delta":
            displayTranscript(a.delta);
            break;
          case "conversation.item.input_audio_transcription.completed":
            break;
          case "input_audio_buffer.speech_started":
            clearAudioPlayerQueue();
            break;
          case "response.audio.delta":
            sendAudioToPlayer(base64ToPCM16(a.delta));
        }
      } catch {}
    });
}
function disconnectWebSocket() {
  websocket && websocket.readyState === WebSocket.OPEN && websocket.close(), alert("session has ended."), (window.location.href = "https://theaibull.com/?ref=ai-agent");
}
function addNewTranscript(e) {
  var t = document.getElementById("transcript-content");
  (currentTranscriptElement = document.createElement("div")).classList.add("transcript-item"), t.appendChild(currentTranscriptElement), (t.scrollTop = t.scrollHeight);
}
function displayTranscript(e) {
  var t;
  currentTranscriptElement && e && ((currentTranscriptElement.textContent += e), ((t = document.getElementById("transcript-content")).scrollTop = t.scrollHeight));
}
class AudioAnalyser {
  constructor(e) {
    (this.binRanges = []),
      (this.barData = new Float32Array(0)),
      (this.nodes = []),
      (this.frequencyAnalyser = e.createAnalyser()),
      (this.frequencyAnalyser.smoothingTimeConstant = 0.85),
      (this.frequencyAnalyser.minDecibels = -90),
      (this.frequencyAnalyser.maxDecibels = -10),
      (this.frequencyAnalyser.fftSize = 2048),
      (this.freqData = new Uint8Array(this.frequencyAnalyser.frequencyBinCount)),
      this.nodes.push(this.frequencyAnalyser),
      (this.levelsAnalyser = e.createAnalyser()),
      (this.levelsAnalyser.smoothingTimeConstant = 0),
      (this.levelsAnalyser.minDecibels = -90),
      (this.levelsAnalyser.maxDecibels = -10),
      (this.levelsAnalyser.fftSize = 32),
      (this.timeDomainData = new Float32Array(this.levelsAnalyser.fftSize)),
      this.nodes.push(this.levelsAnalyser);
  }
  computeRanges(t) {
    if (this.binRanges.length !== t) {
      (this.barData = new Float32Array(t)), (this.binRanges = new Array(t));
      var a = this.freqData.length,
        n = this.frequencyAnalyser.context.sampleRate / this.frequencyAnalyser.fftSize;
      for (let e = 0; e < t; e++) {
        var r = Math.log10(20),
          i = Math.log10(2e4),
          o = r + (i - r) * (e / t),
          i = r + (i - r) * ((e + 1) / t),
          r = Math.floor(Math.pow(10, o) / n),
          o = Math.ceil(Math.pow(10, i) / n);
        this.binRanges[e] = { startBin: Math.max(r, 0), endBin: Math.min(o, a - 1) };
      }
    }
  }
  getAudioLevel() {
    this.levelsAnalyser.getFloatTimeDomainData(this.timeDomainData);
    let e = 0;
    for (let a = 0; a < this.timeDomainData.length; a++) e = Math.max(e, this.timeDomainData[a]);
    var t = (20 * Math.log10(e + 1e-8) + 90) / 80;
    return Math.min(Math.max(t, 0), 1);
  }
  getWaveformData(e) {
    this.frequencyAnalyser.getByteFrequencyData(this.freqData), this.computeRanges(e);
    for (let r = 0; r < this.binRanges.length; r++) {
      var { startBin: a, endBin: n } = this.binRanges[r];
      let e = 0;
      for (let t = a; t <= n; t++) e = Math.max(e, this.freqData[t]);
      this.barData[r] = e / 255;
    }
    return this.barData;
  }
}
function startVisualizer() {
  (canvas = document.getElementById("visualizer")), (canvasContext = canvas.getContext("2d"));
  var e = window.devicePixelRatio || 1,
    t = window.innerWidth;
  (canvas.width = t * e), (canvas.height = 200 * e), canvasContext.scale(e, e), (canvas.style.width = t + "px"), (canvas.style.height = "200px"), drawVisualizer();
}
function drawVisualizer() {
  var e = analyser.getWaveformData(50),
    t = (canvasContext.clearRect(0, 0, canvas.width, canvas.height), canvas.width / 50);
  for (let r = 0; r < 50; r++) {
    var a = e[r] * canvas.height,
      n = r * t;
    (canvasContext.fillStyle = "rgb(0, 150, 255)"), canvasContext.fillRect(n, canvas.height - a, t - 2, a);
  }
  animationId = requestAnimationFrame(drawVisualizer);
}
let button = document.getElementById("start");
function stopRecording() {
  audioContext && audioContext.suspend();
}
button.addEventListener("click", async () => {
  isRecording
    ? (stopRecording(), (button.style.color = "#888"), (button.innerHTML = "mic_off"))
    : ((recorder && audioPlayerNode) || (await initializeRecorder()), await audioContext.resume(), await initializeAudioPlayer(), (button.style.color = "#ff3b3b"), (button.innerHTML = "mic")),
    (isRecording = !isRecording);
});
