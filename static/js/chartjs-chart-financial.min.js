/*! * @license * chartjs-chart-financial * http://chartjs.org/ * Version: 0.2.1 * * Copyright 2024 Chart.js Contributors * Released under the MIT license * https://github.com/chartjs/chartjs-chart-financial/blob/master/LICENSE.md */ !function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("chart.js"),require("chart.js/helpers")):"function"==typeof define&&define.amd?define(["chart.js","chart.js/helpers"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).Chart,e.Chart.helpers)}(this,function(e,t){"use strict";class l extends e.Bar<PERSON>ontroller{static overrides={label:"",parsing:!1,hover:{mode:"label"},animations:{numbers:{type:"number",properties:["x","y","base","width","open","high","low","close"]}},scales:{x:{type:"timeseries",offset:!0,ticks:{major:{enabled:!0},source:"data",maxRotation:0,autoSkip:!0,autoSkipPadding:75,sampleSize:100}},y:{type:"linear"}},plugins:{tooltip:{intersect:!1,mode:"index",callbacks:{label(l){let s=l.parsed;if(!t.isNullOrUndef(s.y))return e.defaults.plugins.tooltip.callbacks.label(l);let{o:o,h:a,l:i,c:r}=s;return`O: ${o} H: ${a} L: ${i} C: ${r}`}}}}};getLabelAndValue(e){let t=this.getParsed(e),l=this._cachedMeta.iScale.axis,{o:s,h:o,l:a,c:i}=t,r=`O: ${s} H: ${o} L: ${a} C: ${i}`;return{label:`${this._cachedMeta.iScale.getLabelForValue(t[l])}`,value:r}}getUserBounds(e){let{min:t,max:l,minDefined:s,maxDefined:o}=e.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:o?l:Number.POSITIVE_INFINITY}}getMinMax(e){let t=this._cachedMeta,l=t._parsed,s=t.iScale.axis,o=this._getOtherScale(e),{min:a,max:i}=this.getUserBounds(o);if(l.length<2)return{min:0,max:1};if(e===t.iScale)return{min:l[0][s],max:l[l.length-1][s]};let r=l.filter(({x:e})=>e>=a&&e<i),n=Number.POSITIVE_INFINITY,d=Number.NEGATIVE_INFINITY;for(let h=0;h<r.length;h++){let u=r[h];n=Math.min(n,u.l),d=Math.max(d,u.h)}return{min:n,max:d}}calculateElementProperties(e,t,l,s){let o=this._cachedMeta.vScale,a=o.getBasePixel(),i=this._calculateBarIndexPixels(e,t,s),r=this.chart.data.datasets[this.index].data[e],n=o.getPixelForValue(r.o),d=o.getPixelForValue(r.h),h=o.getPixelForValue(r.l),u=o.getPixelForValue(r.c);return{base:l?a:h,x:i.center,y:(h+d)/2,width:i.size,open:n,high:d,low:h,close:u}}draw(){let e=this.chart,l=this._cachedMeta.data;t.clipArea(e.ctx,e.chartArea);for(let s=0;s<l.length;++s)l[s].draw(this._ctx);t.unclipArea(e.ctx)}}function s(e,t,l,s){let o=null===t,a=null===l,i=!(!e||o&&a)&&function e(t,l){let{x:s,y:o,base:a,width:i,height:r}=t.getProps(["x","low","high","width","height"],l),n,d,h,u,c;return t.horizontal?(c=r/2,n=Math.min(s,a),d=Math.max(s,a),h=o-c,u=o+c):(n=s-(c=i/2),d=s+c,h=Math.min(o,a),u=Math.max(o,a)),{left:n,top:h,right:d,bottom:u}}(e,s);return i&&(o||t>=i.left&&t<=i.right)&&(a||l>=i.top&&l<=i.bottom)}class o extends e.BarElement{static defaults={backgroundColors:{up:"rgba(75, 192, 192, 0.5)",down:"rgba(255, 99, 132, 0.5)",unchanged:"rgba(201, 203, 207, 0.5)"},borderColors:{up:"rgb(75, 192, 192)",down:"rgb(255, 99, 132)",unchanged:"rgb(201, 203, 207)"}};height(){return this.base-this.y}inRange(e,t,l){return s(this,e,t,l)}inXRange(e,t){return s(this,e,null,t)}inYRange(e,t){return s(this,null,e,t)}getRange(e){return"x"===e?this.width/2:this.height/2}getCenterPoint(e){let{x:t,low:l,high:s}=this.getProps(["x","low","high"],e);return{x:t,y:(s+l)/2}}tooltipPosition(e){let{x:t,open:l,close:s}=this.getProps(["x","open","close"],e);return{x:t,y:(l+s)/2}}}class a extends o{static id="candlestick";static defaults={...o.defaults,borderWidth:1};draw(l){let{x:s,open:o,high:a,low:i,close:r}=this,n,d=this.options.borderColors;"string"==typeof d&&(d={up:d,down:d,unchanged:d}),r<o?(n=t.valueOrDefault(d?d.up:void 0,e.defaults.elements.candlestick.borderColors.up),l.fillStyle=t.valueOrDefault(this.options.backgroundColors?this.options.backgroundColors.up:void 0,e.defaults.elements.candlestick.backgroundColors.up)):r>o?(n=t.valueOrDefault(d?d.down:void 0,e.defaults.elements.candlestick.borderColors.down),l.fillStyle=t.valueOrDefault(this.options.backgroundColors?this.options.backgroundColors.down:void 0,e.defaults.elements.candlestick.backgroundColors.down)):(n=t.valueOrDefault(d?d.unchanged:void 0,e.defaults.elements.candlestick.borderColors.unchanged),l.fillStyle=t.valueOrDefault(this.backgroundColors?this.backgroundColors.unchanged:void 0,e.defaults.elements.candlestick.backgroundColors.unchanged)),l.lineWidth=t.valueOrDefault(this.options.borderWidth,e.defaults.elements.candlestick.borderWidth),l.strokeStyle=n,l.beginPath(),l.moveTo(s,a),l.lineTo(s,Math.min(o,r)),l.moveTo(s,i),l.lineTo(s,Math.max(o,r)),l.stroke(),l.fillRect(s-this.width/2,r,this.width,o-r),l.strokeRect(s-this.width/2,r,this.width,o-r),l.closePath()}}class i extends l{static id="candlestick";static defaults={...l.defaults,dataElementType:a.id};static defaultRoutes=e.BarController.defaultRoutes;updateElements(e,t,l,s){let o="reset"===s,a=this._getRuler(),{sharedOptions:i,includeOptions:r}=this._getSharedOptions(t,s);for(let n=t;n<t+l;n++){let d=i||this.resolveDataElementOptions(n,s),h=this.calculateElementProperties(n,a,o,d);r&&(h.options=d),this.updateElement(e[n],n,h,s)}}}let r=e.Chart.defaults;class n extends o{static id="ohlc";static defaults={...o.defaults,lineWidth:2,armLength:null,armLengthRatio:.8};draw(e){let{x:l,open:s,high:o,low:a,close:i}=this,n=t.valueOrDefault(this.armLengthRatio,r.elements.ohlc.armLengthRatio),d=t.valueOrDefault(this.armLength,r.elements.ohlc.armLength);null===d&&(d=this.width*n*.5),e.strokeStyle=i<s?t.valueOrDefault(this.options.borderColors?this.options.borderColors.up:void 0,r.elements.ohlc.borderColors.up):i>s?t.valueOrDefault(this.options.borderColors?this.options.borderColors.down:void 0,r.elements.ohlc.borderColors.down):t.valueOrDefault(this.options.borderColors?this.options.borderColors.unchanged:void 0,r.elements.ohlc.borderColors.unchanged),e.lineWidth=t.valueOrDefault(this.lineWidth,r.elements.ohlc.lineWidth),e.beginPath(),e.moveTo(l,o),e.lineTo(l,a),e.moveTo(l-d,s),e.lineTo(l,s),e.moveTo(l+d,i),e.lineTo(l,i),e.stroke()}}class d extends l{static id="ohlc";static defaults={...l.defaults,dataElementType:n.id,datasets:{barPercentage:1,categoryPercentage:1}};updateElements(e,t,l,s){let o="reset"===s,a=this._getRuler(),{sharedOptions:i,includeOptions:r}=this._getSharedOptions(t,s);for(let n=t;n<t+l;n++){let d=i||this.resolveDataElementOptions(n,s),h=this.calculateElementProperties(n,a,o,d);r&&(h.options=d),this.updateElement(e[n],n,h,s)}}}e.Chart.register(i,d,a,n)});