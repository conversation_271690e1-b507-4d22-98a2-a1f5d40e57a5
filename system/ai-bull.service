[Unit]
Description=AI Bull Server
After=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/app/ai-bull
Environment="PATH=/root/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
ExecStart=/bin/bash -c 'poetry run uvicorn app.main:app --port 61616 --workers 2'
ExecStop=/bin/bash -c 'pkill -9 -f uvicorn || true && ps -ef | grep python | awk -F " " '\''{print $2}'\'' | xargs kill -9 || true'
Restart=no
StandardOutput=append:/var/log/ai-bull.log
StandardError=append:/var/log/ai-bull.log

[Install]
WantedBy=multi-user.target