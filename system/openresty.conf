user  root root;
worker_processes  auto;

events {
    worker_connections  1024;
}


http {
    http2 on;
    http3 on;
    quic_retry on;

    http3_hq on;

    ssl_early_data on;
    ssl_session_tickets on;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;

    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/x-javascript
        application/json
        application/xml
        application/xml+rss
        application/atom+xml
        application/xhtml+xml
        application/rss+xml
        application/vnd.ms-fontobject
        application/font-sfnt
        application/font-woff
        application/font-woff2
        application/octet-stream
        application/pdf
        image/svg+xml
        image/x-icon
        image/vnd.microsoft.icon
        application/x-font-ttf
        application/x-font-opentype
        font/ttf
        font/otf
        font/opentype;
    gzip_min_length 1000;
    sendfile on;
	tcp_nopush on;
    keepalive_timeout  65;
	types_hash_max_size 2048;
    include       mime.types;
    server_names_hash_bucket_size 64;
    default_type  application/octet-stream;

    lua_shared_dict auto_ssl 1m;
    lua_shared_dict auto_ssl_settings 64k;

    resolver *******;

    error_log /usr/local/openresty/nginx/logs/error.log debug;
    log_format upstream_log '$remote_addr - $remote_user [$time_local] "$request" $status $body_bytes_sent "$http_referer" "$http_user_agent" upstream=$upstream upstream_response_time=$upstream_response_time upstream_connect_time=$upstream_connect_time upstream_header_time=$upstream_header_time';
    access_log /usr/local/openresty/nginx/logs/access.log upstream_log;

    map $host $server_version {
        default                   "ai-bull-in/1.0.0";
        "us.theaibull.com"        "ai-bull-us/1.0.0";
    }

    more_set_headers "Server: $server_version";

    map $request_uri $upstream {
        default python-server;
    }

    upstream python-server {
        server 127.0.0.1:61616;
    }

    init_by_lua_block {
        auto_ssl = (require "resty.auto-ssl").new()

        auto_ssl:set("allow_domain", function(domain, auto_ssl, ssl_options, renewal)
            return ngx.re.match(domain, "^(theaibull.com|us.theaibull.com|www.theaibull.com)$", "ijo")
        end)

        auto_ssl:set("dir", "/opt/app/ssl")

        auto_ssl:set("storage_adapter", "resty.auto-ssl.storage_adapters.file")

        auto_ssl:init()
    }

    init_worker_by_lua_block {
        auto_ssl:init_worker()
    }

    server {
        listen 127.0.0.1:8999;

        client_body_buffer_size 128k;
        client_max_body_size 128k;

        location / {
            content_by_lua_block {
                auto_ssl:hook_server()
            }
        }
    }

    server {
        listen 80;

        location /.well-known/acme-challenge/ {
            content_by_lua_block {
                auto_ssl:challenge_server()
            }
        }

        location / {
            return 301 https://$host$request_uri;
        }
    }

    server {
        listen 443 ssl;
        server_name www.theaibull.com;

        ssl_certificate_by_lua_block {
            auto_ssl:ssl_certificate()
        }

        ssl_certificate /etc/ssl/resty-auto-ssl-fallback.crt;
        ssl_certificate_key /etc/ssl/resty-auto-ssl-fallback.key;

        location / {
            return 301 https://theaibull.com$request_uri;
        }
    }


    server {
        listen 443 ssl;

	    listen 443 quic reuseport;

        server_name theaibull.com us.theaibull.com;


        ssl_certificate_by_lua_block {
            auto_ssl:ssl_certificate()
        }

        client_max_body_size 200M;

        client_body_timeout 300s;

        error_page 500 502 503 504 /5xx.json;

        location /5xx.json {
            internal;
            default_type application/json;
            return 200 '{"message":"isr","status_code":$status}';
        }

        ssl_certificate /etc/ssl/resty-auto-ssl-fallback.crt;
        ssl_certificate_key /etc/ssl/resty-auto-ssl-fallback.key;

        location / {
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header Referrer-Policy "strict-origin-when-cross-origin" always;
            add_header Permissions-Policy "geolocation=(), microphone=(), camera=(), fullscreen=(self), payment=()" always;
            add_header Strict-Transport-Security "max-age=********; includeSubDomains; preload" always;
            add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://assets.theaibull.com https://cdn.jsdelivr.net https://accounts.google.com https://www.googletagmanager.com https://www.google-analytics.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://accounts.google.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: https://theaibull.s3.ap-south-1.amazonaws.com https://www.google-analytics.com; connect-src 'self' https: wss: wss://streamer.emc2.io https://streamer.emc2.io https://www.google-analytics.com; frame-src 'self' https://accounts.google.com; frame-ancestors 'self' https://accounts.google.com; base-uri 'self'; form-action 'self'" always;

	        add_header Alt-Svc 'h3=":443"; ma=86400' always;

            proxy_pass http://$upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout       180s;
            proxy_send_timeout          180s;
            proxy_read_timeout          180s;
            send_timeout                180s;

            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            add_header X-Upstream-Type "Erlang/OTP 28.0" always;
            add_header X-Envoy-Connect-Time $upstream_connect_time always;
            add_header X-Envoy-Header-Time $upstream_header_time always;
            add_header X-Envoy-Status $upstream_status always;

            if ($request_uri ~* \.(png|jpg|jpeg|gif|webp|ico|svg)$) {
                add_header Cache-Control "public, max-age=3600" always;
                expires 1h;
            }
        }
    }

}
