{"arrowParens": "always", "bracketSpacing": true, "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "singleAttributePerLine": false, "bracketSameLine": false, "jsxBracketSameLine": false, "jsxSingleQuote": false, "printWidth": 200, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": false, "tabWidth": 2, "trailingComma": "es5", "useTabs": false, "embeddedLanguageFormatting": "auto", "vueIndentScriptAndStyle": false, "experimentalTernaries": false, "parser": "html"}