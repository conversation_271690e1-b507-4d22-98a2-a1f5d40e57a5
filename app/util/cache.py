import functools
import json
from datetime import datetime, timedelta

from app.util.db.database import DB

# Removed synchronous cache_collection usage


async def set_cache(key: str, value, timeout: int = None) -> None:
    """
    Set a cache entry in the MongoDB async cache collection.
    If timeout is provided, the document will expire after timeout seconds.
    """
    if isinstance(value, (dict, list)):
        value = json.dumps(value)
    doc = {"key": key, "value": value}
    if timeout:
        doc["expiresAt"] = datetime.utcnow() + timedelta(seconds=timeout)
    await DB.CacheAsync.update_one({"key": key}, {"$set": doc}, upsert=True)


async def get_cache(key: str):
    """
    Retrieve a cache entry from the MongoDB async cache collection.
    """
    doc = await DB.CacheAsync.find_one({"key": key})
    if doc:
        return doc["value"]
    return None


DEFAULT_CACHE_TIMEOUT = 21600  # 6 hours timeout in seconds


def db_cache(timeout: int = DEFAULT_CACHE_TIMEOUT, key_func=None):
    """
    Decorator to cache async route results in the DB.
    """

    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = key_func(*args, **kwargs) if key_func else f"{func.__name__}:{args}:{kwargs}"
            cached_result = await get_cache(cache_key)

            if cached_result is not None:
                # Fix: Parse JSON string back to object if needed
                if isinstance(cached_result, str):
                    try:
                        cached_result = json.loads(cached_result)
                    except json.JSONDecodeError:
                        pass  # Keep as string if not valid JSON
                return cached_result

            result = await func(*args, **kwargs)
            await set_cache(cache_key, result, timeout=timeout)
            return result

        return wrapper

    return decorator
