from typing import Any, Dict, Union
import httpx
from jose import jwt
from datetime import datetime


async def is_app_authenticated(app: Dict[str, Any]) -> Union[Dict[str, Any], bool]:
    """Check if the access token is still valid and return profile if authenticated else return False"""
    try:
        access_token = app.get("fields", {}).get("access_token")

        # If we have a token, check if it's valid
        if access_token:
            # First check token expiration
            decoded_token = jwt.decode(access_token, options={"verify_signature": False}, key="")
            exp_time = decoded_token.get("exp", 0)

            # If token is expired, return False immediately
            if datetime.now().timestamp() >= exp_time:
                return False

        # With or without token, try to authenticate via API
        async with httpx.AsyncClient() as client:
            try:
                request_data = {"params": {}, "auth": app.get("fields", {})}
                response = await client.post(
                    f"https://apps.theaibull.com/functions/exec/{app.get('key')}/get_profile",
                    headers={"Accept": "application/json", "Content-Type": "application/json"},
                    json=request_data,
                    timeout=10.0,
                )

                response_data = response.json()

                if response_data["result"]["status"] == "Success":
                    return response_data["result"]["profile"]
                else:
                    print(f"API verification failed: {response_data['result']}")
                    return False
            except Exception as api_error:
                print(f"API verification failed: {api_error}")
                return False

    except Exception as e:
        print(f"Error in authentication process: {e}")
        return False


async def make_trading_api_call(app: Dict[str, Any], endpoint: str, request_data: Dict[str, Any], timeout: float = 30.0) -> Dict[str, Any]:
    try:
       async with httpx.AsyncClient() as client:
            response = await client.post(
                f"https://apps.theaibull.com/functions/exec/{app.get('key')}/{endpoint}",
                headers={"Accept": "application/json", "Content-Type": "application/json"},
                json=request_data,
                timeout=timeout,
            )

            # Raise an exception for HTTP errors
            response.raise_for_status()

            return response.json()

    except httpx.TimeoutException as error:
        print(f"Timeout error for {endpoint}: {error}")
        return {"result": {"error": "Request timed out", "status": "Failure"}}
    except httpx.HTTPStatusError as error:
        print(f"HTTP error for {endpoint}: {error}")
        return {"result": {"error": str(error), "status": "Failure"}}
    except httpx.RequestError as error:
        print(f"Request error for {endpoint}: {error}")
        return {"result": {"error": "Failed to connect to server", "status": "Failure"}}
    except Exception as error:
        print(f"Unexpected error for {endpoint}: {error}")
        return {"result": {"error": str(error), "status": "Failure"}}
