import asyncio
import re
from datetime import datetime
from http.client import HTT<PERSON><PERSON>x<PERSON>
from typing import Any, Dict, List, Optional

import httpx
from async_lru import alru_cache

from app.util.cache import db_cache

API_BASE_URL = "https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/ohlcd"


@db_cache(timeout=43_200, key_func=lambda symbol=None, start_date=None, end_date=None: f"ohlc_data:{symbol}:{start_date}:{end_date}")
async def get_ohlc_data_for_symbol(symbol: str, start_date: str, end_date: str) -> dict[str, object]:
    """
    Fetches OHLC data for the underlying symbol for the given date.
    """

    # Validate if the date is not in future
    if datetime.strptime(start_date, "%Y-%m-%d") > datetime.now():
        return {}

    url = f"{API_BASE_URL}/{symbol}?start={start_date}&end={end_date}"
    async with httpx.AsyncClient(timeout=30.0) as client:
        resp = await client.get(url)
        resp.raise_for_status()
        return resp.json()


async def get_spot_price(symbol_ohlc_data: dict[str, object], time: str, date: str = None) -> float:
    """
    Extracts the spot price for the given time from symbol OHLC data.
    Assumes symbol_ohlc_data contains minute-wise price data keyed by unix timestamp (seconds).
    If date is provided, combines date and time to compute timestamp.
    """
    # If date is not provided, try to infer from keys
    if date is None:
        if symbol_ohlc_data and len(symbol_ohlc_data) > 0:
            # Get first key and infer date
            first_ts = int(list(symbol_ohlc_data.keys())[0])
            dt = datetime.fromtimestamp(first_ts)
            date = dt.strftime("%Y-%m-%d")
        else:
            return 0.0
    # Convert time string to timestamp for the given date
    try:
        dt = datetime.strptime(f"{date} {time}", "%Y-%m-%d %H:%M")
        ts = int(dt.timestamp())
        # Find the closest timestamp in the OHLC data
        if str(ts) in symbol_ohlc_data:
            return symbol_ohlc_data[str(ts)]["c"]
        else:
            # Fallback: find the closest earlier timestamp
            available_ts = sorted(int(k) for k in symbol_ohlc_data.keys())
            closest_ts = max([t for t in available_ts if t <= ts], default=None)
            if closest_ts is not None:
                return symbol_ohlc_data[str(closest_ts)]["c"]
    except Exception:
        return 0.0
    return 0.0


@db_cache(
    timeout=43_200,
    key_func=lambda symbol=None, option_type=None, strike=None, expiration_date=None, start=None, end=None: f"ohlc_option:{symbol}:{option_type}:{strike}:{expiration_date}:{start}:{end}",
)
async def get_ohlc_data_for_option(symbol: str, option_type: str, strike: str, expiration_date: str, start: str, end: str) -> dict[str, object]:
    """
    Constructs the option symbol and fetches OHLC data for the option contract for the given date range.
    Handles expiration_date in formats like "2025-07-31" or "31-Jul-2025".
    Example symbol: SY-NIFTY-Jul2025-24850-PE_2025-07-31
    """

    # Validate if the date is not in future
    if datetime.strptime(start, "%Y-%m-%d") > datetime.now() or datetime.strptime(end, "%Y-%m-%d") > datetime.now():
        return {}

    try:
        if re.match(r"\d{2}-[A-Za-z]{3}-\d{4}", expiration_date):
            dt = datetime.strptime(expiration_date, "%d-%b-%Y")
        elif re.match(r"\d{4}-\d{2}-\d{2}", expiration_date):
            dt = datetime.strptime(expiration_date, "%Y-%m-%d")
        else:
            raise ValueError("Unknown expiration date format")
        expiry_month_year = dt.strftime("%b%Y").capitalize()
        expiry_date_str = dt.strftime("%Y-%m-%d")
    except Exception:
        expiry_month_year = expiration_date
        expiry_date_str = expiration_date
    option_symbol = f"SY-{symbol}-{expiry_month_year}-{strike}-{option_type}_{expiry_date_str}"
    url = f"{API_BASE_URL}/{option_symbol}?start={start}&end={end}"
    async with httpx.AsyncClient(timeout=60.0) as client:
        resp = await client.get(url)
        # if exception, return empty dict
        if resp.status_code != 200:
            print(f"Error fetching OHLC data for option {option_symbol}: {resp.status_code} - {resp.text}")
            return {}
        return resp.json()


# Cache the option chain API call.
@db_cache(timeout=43_200, key_func=lambda symbol=None, expiration_date_str=None, entry_date_str=None: f"option_chain:{symbol}:{expiration_date_str}:{entry_date_str}")
async def get_option_chain_data(symbol: str, expiration_date_str: str, entry_date_str: str) -> dict:
    url = f"https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/{symbol}"
    params = {"expiryDate": expiration_date_str, "date": entry_date_str}

    # Ensure entry_date_str is in DD-MM-YYYY format
    if re.match(r"\d{4}-\d{2}-\d{2}", entry_date_str):
        dt_entry = datetime.strptime(entry_date_str, "%Y-%m-%d")
        params["date"] = dt_entry.strftime("%d-%m-%Y")
    elif re.match(r"\d{2}-[A-Za-z]{3}-\d{4}", entry_date_str):
        dt_entry = datetime.strptime(entry_date_str, "%d-%b-%Y")
        params["date"] = dt_entry.strftime("%d-%m-%Y")
    else:
        params["date"] = entry_date_str  # fallback

    # Print url and params for debugging
    print(f"Fetching option chain data from {url} with params {params}")

    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.get(url, params=params)
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail="Error fetching option chain data.")
        return response.json()


# Get all strikes for a given symbol and expiration date
@alru_cache(maxsize=32)
async def get_all_strikes_for_symbol(symbol: str, expiration_date_str: str, entry_date_str: str) -> dict:
    """
    Fetches all available CE and PE strikes for the given symbol and expiration date.
    Returns: {
        "ce_strikes": [{"strikePrice": float, "lastPrice": float}],
        "pe_strikes": [{"strikePrice": float, "lastPrice": float}]
    }
    """
    option_chain = await get_option_chain_data(symbol, expiration_date_str, entry_date_str)
    ce_strikes = []
    pe_strikes = []
    for record in option_chain.get("records", {}).get("data", []):
        expiry = record.get("expiryDate")
        if expiry == expiration_date_str:
            if "CE" in record:
                ce = record["CE"]
                ce_strikes.append({"strikePrice": ce.get("strikePrice"), "lastPrice": ce.get("lastPrice")})
            if "PE" in record:
                pe = record["PE"]
                pe_strikes.append({"strikePrice": pe.get("strikePrice"), "lastPrice": pe.get("lastPrice")})
    return {"ce_strikes": ce_strikes, "pe_strikes": pe_strikes}


async def get_strike_from_config(
    spot_price: float,
    option_type: str,
    strike_config: Optional[Dict[str, Any]] = None,
    symbol: Optional[str] = None,
    expiration_date: Optional[str] = None,
    entry_date: Optional[str] = None,
) -> float:
    """
    Returns the strike price based on config. Supports ATM, OTM, ITM, and premium-based selection.
    Fetches strikes internally using get_all_strikes_for_symbol.
    """
    condition = strike_config.get("condition", "is")
    value = strike_config.get("value", "ATM")

    # Fetch strikes with premiums
    strikes_data = await get_all_strikes_for_symbol(symbol, expiration_date, entry_date)
    strikes_list = strikes_data.get(f"{option_type}_strikes", [])

    # For premium-based selection
    if condition == "premium":
        criterion = strike_config.get("criterion", "closest")
        if isinstance(value, (str, float, int)):
            try:
                target_premium = float(value)
                if criterion == "lte":
                    # Largest premium less than or equal to target
                    filtered = [x for x in strikes_list if float(x.get("lastPrice", 0)) <= target_premium]
                    if filtered:
                        closest = max(filtered, key=lambda x: float(x.get("lastPrice", 0)))
                        return float(closest.get("strikePrice", spot_price))
                elif criterion == "gte":
                    # Smallest premium greater than or equal to target
                    filtered = [x for x in strikes_list if float(x.get("lastPrice", 0)) >= target_premium]
                    if filtered:
                        closest = min(filtered, key=lambda x: float(x.get("lastPrice", 0)))
                        return float(closest.get("strikePrice", spot_price))
                # Default: closest to target
                closest = min(strikes_list, key=lambda x: abs(float(x.get("lastPrice", 0)) - target_premium))
                return float(closest.get("strikePrice", spot_price))
            except Exception:
                pass

    # For "is" condition (ATM, OTM, ITM, direct value)
    if isinstance(value, str) and value.upper() == "ATM":
        # Find closest to spot price
        closest = min(strikes_list, key=lambda x: abs(x.get("strikePrice", 0) - spot_price)) if strikes_list else None
        return float(closest.get("strikePrice", spot_price)) if closest else spot_price

    # OTM/ITM logic (e.g., OTM5, ITM2)
    if isinstance(value, str) and (value.startswith("OTM") or value.startswith("ITM")):
        try:
            offset = int(value[3:])
            sorted_strikes = sorted(strikes_list, key=lambda x: x.get("strikePrice", 0))
            atm_idx = min(range(len(sorted_strikes)), key=lambda i: abs(sorted_strikes[i].get("strikePrice", 0) - spot_price))
            if value.startswith("OTM"):
                idx = atm_idx + offset if option_type == "ce" else atm_idx - offset
            else:  # ITM
                idx = atm_idx - offset if option_type == "ce" else atm_idx + offset
            idx = max(0, min(idx, len(sorted_strikes) - 1))
            return float(sorted_strikes[idx].get("strikePrice", spot_price))
        except Exception:
            pass

    return None


def get_strike_prices_for_day(strikes: dict[str, list[float]], spot_price: float, entry_legs: list[dict[str, object]]) -> dict[str, float]:
    """
    For each leg, computes the strike price using get_strike_from_config and returns a dict mapping leg id to strike price.
    """
    from app.util.ohlc_dhan import get_strike_from_config

    strike_prices: dict[str, float] = {}
    for leg in entry_legs:
        leg_id = str(leg.get("id"))
        option_type = str(leg.get("optionType", ""))
        strike_config = leg.get("strikeConfig", {})
        if not isinstance(strike_config, dict):
            strike_config = {}
        strike_value = str(strike_config.get("value", "ATM"))
        strike = get_strike_from_config(strikes, spot_price, strike_config)
        strike_prices[leg_id] = strike
    return strike_prices


# Example main function to test
async def main():
    symbol = "NIFTY"
    date = "2025-07-23"
    option_type = "PE"
    strike = "24850"
    expiration_date = "31-Jul-2025"
    start = "2025-07-23"
    end = "2025-07-23"
    print("Testing get_ohlc_data_for_symbol...")
    ohlc_symbol = await get_ohlc_data_for_symbol(symbol, date)
    print(ohlc_symbol)
    # print("Testing get_ohlc_data_for_option...")
    # ohlc_option = await get_ohlc_data_for_option(symbol, option_type, strike, expiration_date, start, end)
    # print(ohlc_option)

    print("Testing get_spot_price...")
    spot_price = await get_spot_price(ohlc_symbol, "09:15", date)
    print(f"Spot price at 09:15 on {date}: {spot_price}")

    print("Testing get_all_strikes_for_symbol...")
    strikes = await get_all_strikes_for_symbol(symbol, expiration_date, date)
    print(f"CE Strikes: {strikes['ce_strikes']}")
    print(f"PE Strikes: {strikes['pe_strikes']}")

    # Example usage of get_strike_from_config
    strike_value = "OTM5"  # Example config value
    strike_config_ce = {"optionType": "ce", "value": strike_value}
    strike_config_pe = {"optionType": "pe", "value": strike_value}
    strike_prices = await get_strike_from_config(strikes, spot_price, strike_config=strike_config_ce)
    print(f"Computed strike price for {strike_value}: {strike_prices}")

    # Example usage of get_strike_from_config for both CE and PE
    ce_strike = await get_strike_from_config(strikes, spot_price, strike_config=strike_config_ce)
    pe_strike = await get_strike_from_config(strikes, spot_price, strike_config=strike_config_pe)
    print(f"Computed CE strike price for {strike_value}: {ce_strike}")
    print(f"Computed PE strike price for {strike_value}: {pe_strike}")

    # Example usage of get_strike_prices_for_day
    entry_legs = [
        {"id": "1", "optionType": "CE", "strikeConfig": {"value": "ATM"}},
        {"id": "2", "optionType": "PE", "strikeConfig": {"value": "OTM5"}},
    ]
    strike_prices_for_day = get_strike_prices_for_day(strikes, spot_price, entry_legs)
    print(f"Computed strike prices for day: {strike_prices_for_day}")


if __name__ == "__main__":
    asyncio.run(main())
