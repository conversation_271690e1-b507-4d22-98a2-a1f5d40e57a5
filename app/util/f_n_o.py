import uuid
from datetime import datetime
from typing import Any, Dict, Optional
import httpx

from app.util.lot_sizes import LotSizes
from app.util.stocks import get_options_data


def process_fno_position(position: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Process a single Dhan FNO position into a unified format for the frontend.
    """
    # Validate input
    if not position.get("trading_symbol") or not position.get("quantity"):
        print("Invalid position: missing trading_symbol or quantity")
        return None

    # Parse trading symbol
    parsed = parse_dhan_trading_symbol(position["trading_symbol"])
    if not parsed:
        return None

    # Determine lot size
    lot_size = LotSizes[parsed["symbol"]]

    # Determine side
    side = "buy" if position.get("buy_quantity", 0) > 0 and position.get("sell_quantity", 0) == 0 else "sell"

    # Primarily consider average_price; if it's falsy, then consider buy_price or sell_price
    avg_price = position.get("average_price")
    if avg_price:
        entry_price = avg_price
    else:
        entry_price = position.get("buy_price") if side == "buy" else position.get("sell_price")

    # Calculate premium_received
    quantity = position["quantity"] / lot_size
    premium_received = None
    if side == "buy":
        premium_received = -(entry_price * quantity * lot_size)  # Negative for buy
    elif side == "sell":
        premium_received = entry_price * quantity * lot_size  # Positive for sell

    # Create unified position
    return {
        "uid": str(uuid.uuid4()),
        "symbol": parsed["symbol"],
        "instrument": "option",
        "type": parsed["type"],
        "strike": parsed["strike"],
        "expiry": parsed["expiry"],
        "quantity": quantity,
        "entry_price": entry_price,
        "entry_date": position.get("entry_date", datetime.now().date().isoformat()),
        "side": side,
        "lot_size": lot_size,
        "premium_received": premium_received,
        "unrealised_pnl": position.get("unrealised_pnl", 0),
        "realised_pnl": position.get("realised_pnl", 0),
    }


def parse_dhan_trading_symbol(trading_symbol: str) -> Dict[str, Any]:
    """
    Parse trading symbol to extract symbol, strike, type, and expiry.
    """
    try:
        parts = trading_symbol.split("-")
        if len(parts) != 4:
            raise ValueError(f"Invalid trading symbol format: {trading_symbol}")

        symbol, expiry_str, strike_str, option_type = parts
        strike = float(strike_str)

        month_map = {"Jan": "01", "Feb": "02", "Mar": "03", "Apr": "04", "May": "05", "Jun": "06", "Jul": "07", "Aug": "08", "Sep": "09", "Oct": "10", "Nov": "11", "Dec": "12"}
        month = expiry_str[:3]
        year = expiry_str[3:]
        if month not in month_map or not year.isdigit():
            raise ValueError(f"Invalid expiry format in trading symbol: {expiry_str}")
        expiry = f"{year}-{month_map[month]}-29"  # Placeholder day; adjust to actual expiry

        return {"symbol": symbol, "strike": strike, "type": option_type, "expiry": expiry}
    except Exception as e:
        print(f"Error parsing trading symbol {trading_symbol}: {e}")
        return {}


def generate_uuid():
    """Generate a unique identifier."""
    return str(uuid.uuid4())


async def get_option_current_price(position):
    """Fetch the current price for a given symbol and instrument type."""
    try:
        options_data = get_options_data(position["symbol"])
        if options_data:
            # Find the matching option contract
            for contract in options_data.get("records", {}).get("data", []):
                option_data = contract.get(position["type"])
                if option_data:
                    return option_data.get("underlyingValue")
        return None
    except Exception as e:
        print(f"Error fetching options data: {e}")
        return None


async def calculate_portfolio_metrics(portfolio, stocks):
    """Calculate metrics for a portfolio with separate P&L and values for holdings/equity and F&O positions"""
    total_value = 0  # For stocks (holdings and equity positions)
    fno_total_value = 0  # For F&O positions
    stock_total_cost = 0
    fno_total_cost = 0
    stock_pl = 0
    fno_pl = 0

    # Calculate holdings metrics
    for holding in portfolio.get("holdings", []):
        cost_basis = holding["quantity"] * holding["avg_price"]
        holding["invested_amount"] = cost_basis
        stock_total_cost += cost_basis

        stock_data = stocks.get(holding["symbol"], {})

        # Check for missing symbols like ETFs and Bonds
        if not stock_data:
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"https://iam.theaibull.com/v1/wg7ttpouv7/symbol/price/{holding['symbol']}")
                    response.raise_for_status()
                    stock_data = response.json()
                    stocks[holding["symbol"]] = stock_data
            except httpx.RequestError as e:
                print(f"Error fetching stock data for {holding['symbol']}: {e}")
                stock_data = {}

        # Extract additional stock attributes
        holding["pe_ratio"] = stock_data.get("metadata", {}).get("pdSymbolPe")
        holding["sector_pe"] = stock_data.get("metadata", {}).get("pdSectorPe")
        holding["week_52_low"] = stock_data.get("priceInfo", {}).get("weekHighLow", {}).get("min")
        holding["week_52_high"] = stock_data.get("priceInfo", {}).get("weekHighLow", {}).get("max")
        holding["market_cap"] = (
            stock_data.get("securityInfo", {}).get("issuedSize") * stock_data.get("priceInfo", {}).get("lastPrice", 0) if stock_data.get("securityInfo", {}).get("issuedSize") else None
        )

        holding["sector"] = stock_data.get("industryInfo", {}).get("macro", "")
        holding["industry"] = stock_data.get("industryInfo", {}).get("basicIndustry", "")
        current_price = stock_data.get("priceInfo", {}).get("lastPrice")
        if current_price:
            market_value = holding["quantity"] * current_price
            pl = market_value - cost_basis
            pl_percentage = (pl / cost_basis * 100) if cost_basis > 0 else 0

            holding["current_price"] = current_price
            holding["market_value"] = market_value
            holding["pl"] = pl
            holding["pl_percentage"] = pl_percentage
            holding["status"] = "active"

            total_value += market_value
            stock_pl += pl
        else:
            holding["current_price"] = None
            holding["market_value"] = None
            holding["pl"] = None
            holding["pl_percentage"] = None
            holding["status"] = "unavailable"
            total_value += cost_basis

    # Calculate positions metrics (including equity)
    for position in portfolio.get("positions", []):
        lot_size = position.get("lot_size", 1)
        quantity = position["quantity"]
        entry_price = position["entry_price"]

        # Calculate initial cost/premium
        total_entry_value = entry_price * quantity * lot_size
        position["invested_amount"] = total_entry_value

        if position["instrument"] == "equity":
            # Equity positions are treated like holdings for P&L and value
            cost_basis = total_entry_value
            stock_total_cost += cost_basis

            stock_data = stocks.get(position["symbol"], {})
            position["sector"] = stock_data.get("industryInfo", {}).get("macro", "")
            position["industry"] = stock_data.get("industryInfo", {}).get("basicIndustry", "")
            position["market_cap"] = (
                stock_data.get("securityInfo", {}).get("issuedSize") * stock_data.get("priceInfo", {}).get("lastPrice", 0) if stock_data.get("securityInfo", {}).get("issuedSize") else None
            )
            current_price = stock_data.get("priceInfo", {}).get("lastPrice")
            if current_price:
                market_value = position["quantity"] * current_price
                pl = market_value - total_entry_value
                pl_percentage = (pl / total_entry_value * 100) if total_entry_value > 0 else 0

                position["current_price"] = current_price
                position["market_value"] = market_value
                position["pl"] = pl
                position["pl_percentage"] = pl_percentage
                position["status"] = "active"

                total_value += market_value
                stock_pl += pl
            else:
                position["current_price"] = None
                position["market_value"] = None
                position["pl"] = None
                position["pl_percentage"] = None
                position["status"] = "unavailable"
                total_value += total_entry_value
        else:
            # F&O positions (options and futures)
            cost_basis = total_entry_value if position["side"] == "buy" else -total_entry_value
            fno_total_cost += abs(cost_basis)

            current_price = None
            if position["instrument"] == "future":
                # For futures, get price from stocks data
                stock_data = stocks.get(position["symbol"], {})
                current_price = stock_data.get("priceInfo", {}).get("lastPrice")
            else:
                stock_data = stocks.get(position["symbol"], {})
                current_price = stock_data.get("priceInfo", {}).get("lastPrice")
                if not current_price:
                    current_price = await get_option_current_price(position)

            if current_price:
                current_value = current_price * quantity * lot_size

                if position.get("unrealised_pnl") is not None:
                    pl = position["unrealised_pnl"]

                if not pl:
                    if position["instrument"] == "option":
                        spot = current_price
                        strike = position["strike"]
                        premium = position["entry_price"]
                        intrinsic_payoff = max(spot - strike, 0) if position["type"] == "CE" else max(strike - spot, 0)
                        if position["side"] == "buy":
                            pl = (intrinsic_payoff - premium) * quantity * lot_size
                        else:
                            pl = (premium - intrinsic_payoff) * quantity * lot_size
                    else:
                        # For futures, include lot size in P/L calculation
                        if position["side"] == "sell":
                            pl = (strike - current_price) * quantity * lot_size
                        else:
                            pl = (current_price - strike) * quantity * lot_size

                pl_percentage = (pl / abs(cost_basis) * 100) if cost_basis != 0 else 0

                position["current_price"] = current_price
                position["pl"] = pl
                position["pl_percentage"] = pl_percentage
                position["status"] = "active"

                fno_total_value += abs(current_value)
                fno_pl += pl
            else:
                position["current_price"] = None
                position["pl"] = None
                position["pl_percentage"] = None
                position["status"] = "unavailable"
                fno_total_value += abs(cost_basis)

    # Calculate portfolio totals
    portfolio["total_value"] = total_value
    portfolio["fno_total_value"] = fno_total_value
    portfolio["stock_pl"] = stock_pl
    portfolio["stock_pl_percentage"] = (stock_pl / stock_total_cost * 100) if stock_total_cost > 0 else 0
    portfolio["fno_pl"] = fno_pl
    portfolio["fno_pl_percentage"] = (fno_pl / fno_total_cost * 100) if fno_total_cost > 0 else 0
    portfolio["holdings_count"] = len(portfolio.get("holdings", []))
    portfolio["positions_count"] = len(portfolio.get("positions", []))
    portfolio["total_pl"] = stock_pl + fno_pl  # Optional: Keep for backward compatibility
    portfolio["total_pl_percentage"] = ((stock_pl + fno_pl) / (stock_total_cost + fno_total_cost) * 100) if (stock_total_cost + fno_total_cost) > 0 else 0

    return portfolio
