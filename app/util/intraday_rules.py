# intraday_rules.py
import re
from typing import Any, Dict, List


def transform_rules_config(rules_config: str) -> List[str]:
    """
    Transform rules by replacing variable names and color values, and lowercasing everything.
    Uses a mapping for easier future updates.
    Returns a list of transformed rule strings.
    """
    replace_map = {
        "close": "c",
        "supertrend_color": "supertrend_dir",
        "red": "-1",
        "green": "1",
    }
    rules = []
    for line in rules_config.splitlines():
        line = line.strip()
        if not line or line.startswith("#"):
            continue
        line = line.lower()
        for old, new in replace_map.items():
            line = line.replace(old, new)
        # Replace single = with == for user-friendly syntax
        line = re.sub(r"(?<![<>=!])=(?!=)", "==", line)
        rules.append(line)
    return rules


def eval_arithmetic_expr(expr: str, ohlc: List[Dict[str, Any]], index: int) -> Any:
    """
    Evaluate simple arithmetic expressions like 0.8 * c, c * 0.8, c + 5, close - atr, c + atr, etc.
    Recursively supports expressions on both sides.
    """
    # Try variable op variable or expr op expr
    arith_match = re.match(r"^(.+)\s*([*/+-])\s*(.+)$", expr)
    if arith_match:
        left_expr = arith_match.group(1).strip()
        op = arith_match.group(2)
        right_expr = arith_match.group(3).strip()
        left_val = eval_value(ohlc, index, left_expr)
        right_val = eval_value(ohlc, index, right_expr)
        if left_val is None or right_val is None:
            return None
        try:
            left_val = float(left_val)
            right_val = float(right_val)
        except Exception:
            return None
        if op == "*":
            return left_val * right_val
        elif op == "/":
            return left_val / right_val if right_val != 0 else None
        elif op == "+":
            return left_val + right_val
        elif op == "-":
            return left_val - right_val
    return None


def eval_value(ohlc: List[Dict[str, Any]], index: int, value: str) -> Any:
    """
    Evaluate a value, supporting lookback like macd[-1] and simple arithmetic expressions (e.g., 0.8 * c).
    """
    value = value.strip()
    # Lookback support: e.g., macd[-1]
    lookback_match = re.match(r"([a-zA-Z_][a-zA-Z0-9_]*)\[(-?\d+)\]", value)
    if lookback_match:
        var = lookback_match.group(1)
        offset = int(lookback_match.group(2))
        idx = index + offset
        if 0 <= idx < len(ohlc):
            return ohlc[idx].get(var)
        else:
            return None
    # Arithmetic expression support
    arith_result = eval_arithmetic_expr(value, ohlc, index)
    if arith_result is not None:
        return arith_result
    # Try to convert to int or float
    try:
        if "." in value:
            return float(value)
        return int(value)
    except Exception:
        pass
    # Otherwise, treat as variable
    return ohlc[index].get(value)


def eval_rule(rule: str, ohlc: List[Dict[str, Any]], index: int) -> bool:
    """
    Evaluate a single rule string for a given tick.
    Supports multi-token RHS (e.g., vwap < c + 3).
    """
    tokens = re.split(r"\s+", rule)
    if len(tokens) < 3:
        return False
    lhs = tokens[0]
    operand = tokens[1]
    rhs = " ".join(tokens[2:])
    lhs_value = eval_value(ohlc, index, lhs)
    rhs_value = eval_value(ohlc, index, rhs)
    if operand in ("==", "="):
        return lhs_value == rhs_value
    elif operand == ">":
        return lhs_value > rhs_value
    elif operand == "<":
        return lhs_value < rhs_value
    elif operand == ">=":
        return lhs_value >= rhs_value
    elif operand == "<=":
        return lhs_value <= rhs_value
    elif operand == "!=" or operand == "<>":
        return lhs_value != rhs_value
    return False


def eval_rules(rules: List[str], ohlc: List[Dict[str, Any]], index: int) -> bool:
    """
    Evaluate all rules for a given tick. Return True if all pass.
    """
    for rule in rules:
        # if "vwap" in rule:
        #     print(f"VWAP rule passed for index {index}: {rule}")
        if not eval_rule(rule, ohlc, index):
            return False
        else:
            continue
    return True


def apply_intraday_rules(ohlc: Dict[str, Any], rules_config: str) -> List[str]:
    """
    For each timestamp in ohlc, check if all rules in rules_config are satisfied.
    Returns a list of timestamps (as strings) where all rules are True.
    """
    # Convert ohlc dict to list of dicts sorted by timestamp
    ohlc_items = sorted(ohlc.items())
    ohlc_list = [dict((k.lower(), v) for k, v in candle.items()) for ts, candle in ohlc_items]
    timestamps = [ts for ts, candle in ohlc_items]
    rules = transform_rules_config(rules_config)
    matching_timestamps = []
    for i in range(len(ohlc_list)):
        # Debug: print the original candle for this timestamp
        # timestamp = timestamps[i]
        # if timestamp == "1754022600":
        #     print(f"[DEBUG] Timestamp: {timestamps[i]}, Original candle: {ohlc_items[i][1]}")
        if eval_rules(rules, ohlc_list, i):
            matching_timestamps.append(timestamps[i])
    return matching_timestamps
