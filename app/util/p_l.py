# p_l.py
from typing import Any, Dict, List


def compute_p_l(ohlc: Dict[str, Any], matching_timestamps: List[str], trade: dict = None) -> Dict[str, Any]:
    """
    For each matching timestamp, simulate trade entry/exit and compute P/L details.
    Returns dict: {timestamp: {entry_price, exit_price, entry_time, exit_time, exit_reason, pl}}
    """
    results = {}
    ohlc_items = sorted(ohlc.items())
    ts_list = [ts for ts, _ in ohlc_items]
    ohlc_list = [candle for _, candle in ohlc_items]
    ts_to_idx = {ts: i for i, ts in enumerate(ts_list)}
    for ts in matching_timestamps:
        idx = ts_to_idx.get(ts)
        if idx is None:
            continue
        entry_candle = ohlc_list[idx]
        entry_price = entry_candle.get("c")
        entry_time = ts
        # Determine stop loss and profit target
        stop_loss = None
        profit_target = None
        exit_reason = None
        trade_type = (trade or {}).get("type", "buy")
        ticks_delayed = int((trade or {}).get("ticks_delayed", 1))
        stop_loss_mode = (trade or {}).get("stop_loss", "2xATR")
        book_profit_mode = (trade or {}).get("book_profit", "2xSL")
        fixed_stop_loss = (trade or {}).get("fixed_stop_loss")
        # Use ATR or fixed for stop loss
        atr = entry_candle.get("atr")
        if stop_loss_mode == "fixed" and fixed_stop_loss:
            stop_loss = float(fixed_stop_loss)
        elif "ATR" in stop_loss_mode and atr:
            mult = float(stop_loss_mode.replace("xATR", ""))
            stop_loss = float(atr) * mult
        # Book profit: can be xSL or xATR
        if "xSL" in book_profit_mode and stop_loss:
            mult = float(book_profit_mode.replace("xSL", ""))
            profit_target = stop_loss * mult
        elif "xATR" in book_profit_mode and atr:
            mult = float(book_profit_mode.replace("xATR", ""))
            profit_target = float(atr) * mult
        # Entry after ticks_delayed
        entry_idx = idx + ticks_delayed
        if entry_idx >= len(ohlc_list):
            continue
        entry_candle = ohlc_list[entry_idx]
        entry_price = entry_candle.get("o") or entry_candle.get("c")
        entry_time = ts_list[entry_idx]
        # Simulate forward for exit
        exit_price = None
        exit_time = None
        for j in range(entry_idx + 1, len(ohlc_list)):
            c = ohlc_list[j]
            tsj = ts_list[j]
            high = c.get("h")
            low = c.get("l")
            close = c.get("c")
            if trade_type == "buy":
                # Stop loss hit
                if stop_loss and low is not None and low <= entry_price - stop_loss:
                    exit_price = entry_price - stop_loss
                    exit_time = tsj
                    exit_reason = "stop_loss_hit"
                    break
                # Profit booked
                if profit_target and high is not None and high >= entry_price + profit_target:
                    exit_price = entry_price + profit_target
                    exit_time = tsj
                    exit_reason = "profit_booked"
                    break
            else:  # sell
                if stop_loss and high is not None and high >= entry_price + stop_loss:
                    exit_price = entry_price + stop_loss
                    exit_time = tsj
                    exit_reason = "stop_loss_hit"
                    break
                if profit_target and low is not None and low <= entry_price - profit_target:
                    exit_price = entry_price - profit_target
                    exit_time = tsj
                    exit_reason = "profit_booked"
                    break
        if exit_price is None:
            # If not exited, mark as open at last close
            exit_price = ohlc_list[-1].get("c")
            exit_time = ts_list[-1]
            exit_reason = "open"
        pl = (exit_price - entry_price) if trade_type == "buy" else (entry_price - exit_price)
        results[ts] = {
            "entry_price": entry_price,
            "entry_time": entry_time,
            "exit_price": exit_price,
            "exit_time": exit_time,
            "exit_reason": exit_reason,
            "pl": pl,
            "stop_loss": stop_loss,
            "book_profit": profit_target,
        }
    return results
