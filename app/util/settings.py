from datetime import datetime
from functools import lru_cache
from typing import Any, Dict, Optional

from pydantic import SecretStr
from pydantic_settings import BaseSettings


class AppSettings(BaseSettings):
    DOCS_URL: Optional[str] = None

    REDOC_URL: Optional[str] = None

    OPENAPI_URL: Optional[str] = None

    APP_NAME: str = "ai-bull"

    COPYRIGHT_YEAR: int = datetime.now().year

    APP_DEBUG: bool = False

    REGION: str = "prod"

    TEMPLATE_DIR: str = "app/templates"

    ENABLE_GZIP: bool = True

    # when we go for release just set it to true in app_settings
    DISABLE_BASIC_AUTH: bool = True

    # Mongo DB
    MONGO_DB_CONNECTION: str = "mongodb+srv://root:<EMAIL>/?retryWrites=true&w=majority&appName=aibull"

    # AIBull API URLs
    AIBULL_BASE_URL: str = "https://iam.theaibull.com/v1/wg7ttpouv7"
    AIBULL_JUG_URL: str = "https://c7p6m7rd1i.execute-api.ap-south-2.amazonaws.com/dev/"

    AIBULL_TECHNICAL_ALL_URL: str = f"{AIBULL_BASE_URL}/technical/all"
    AIBULL_LIVE_ANALYSIS_URL: str = f"{AIBULL_BASE_URL}/live-analysis/get"
    AIBULL_LIVE_ANALYSIS_REFRESH_URL: str = f"{AIBULL_BASE_URL}/live-analysis"
    AIBULL_STOCKS_ALL_URL: str = f"{AIBULL_BASE_URL}/stocks/all"
    AIBULL_MOST_ACTIVE_URL: str = f"{AIBULL_BASE_URL}/most-active"
    AIBULL_FUTURES_ALL_URL: str = f"{AIBULL_BASE_URL}/futures/all"
    AIBULL_SYMBOL_PRICE_URL: str = f"{AIBULL_BASE_URL}/symbol/price"
    AIBULL_SYMBOL_OPTIONS_URL: str = f"{AIBULL_BASE_URL}/symbol/options"
    AIBULL_FUTURES_URL: str = f"{AIBULL_BASE_URL}/futures"
    AIBULL_SEARCH_URL: str = f"{AIBULL_BASE_URL}/search/find"
    AIBULL_INDICES_OPTIONS_URL: str = f"{AIBULL_BASE_URL}/indices/options"

    # Screener
    AIBULL_SCREENER_INDIVIDUAL: str = f"{AIBULL_BASE_URL}/screener/details"

    OAI_REALTIME_EP: str = "wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-12-17"
    OAI_SECRET: SecretStr = SecretStr(
        "********************************************************************************************************************************************************************"
    )  # project ai-bool only 2 models are active with this key and 10$ credit limit

    class Config:
        env_file = ".env"
        validate_assignment = True
        str_strip_whitespace = True

    @property
    def fastapi_kwargs(self) -> Dict[str, Any]:
        return {"debug": self.APP_DEBUG, "docs_url": self.DOCS_URL, "openapi_url": self.OPENAPI_URL}

    MANTRA_AI_SERVER: str = "https://api.theaibull.com"  # Need to change this
    MANTRA_AI_APP: str = "ai_bull"

    DHAN_CLIENT_ID: str = "1106445822"  # This should be set in .env
    DHAN_ACCESS_TOKEN: Optional[str] = None  # This should be set in .


@lru_cache
def get_app_settings() -> AppSettings:
    return AppSettings()


app_settings = get_app_settings()
