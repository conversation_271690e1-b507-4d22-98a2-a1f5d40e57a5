import requests
from bs4 import <PERSON><PERSON>oup
from fastapi import HTTP<PERSON>x<PERSON>
from cachetools import T<PERSON><PERSON><PERSON>, cached
from datetime import timed<PERSON><PERSON>

# Initialize a cache with a 5-minute TTL (300 seconds)
cache: TTLCache[str, float] = TTLCache(maxsize=1, ttl=timedelta(minutes=5).total_seconds())


@cached(cache)
def tickertape_mmi():
    try:
        url = "https://www.tickertape.in/market-mood-index"
        response = requests.get(url)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, "html.parser")

        # Target the div with class 'mmi-value' and find the span with class 'number'
        score_element = soup.find("div", class_="mmi-value")
        if score_element:
            score_number = score_element.find("span", class_="number")
            if score_number and score_number.text:
                try:
                    score = float(score_number.text.strip())
                    if 0 <= score <= 100:
                        return score
                    else:
                        raise ValueError("Score out of valid range (0-100)")
                except ValueError:
                    raise ValueError("Invalid score format")
        raise ValueError("MMI value not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch Tickertape MMI: {str(e)}")
