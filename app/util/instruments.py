from io import BytesIO
import requests
import pandas as pd
import json
import os
import gzip
import redis
from io import StringIO
from app.util.db.redis import get_cache, set_cache
from app.util.apps import make_trading_api_call
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime


# Global cache for Redis clients
_redis_cache = {}


def get_redis_client(redis_url=None):
    """
    Initializes and returns a Redis client using the provided Redis URL or the environment-specific Redis URL.
    If a client for the given URL already exists, it returns the cached client.
    :param redis_url: The Redis connection URL. If None, fetch from environment variable "REDIS_CACHE".
    :return: An initialized Redis client.
    """
    if redis_url is None:
        redis_url = os.getenv("REDIS_URI", "redis://:rr95z28gbaw0@************:6379/3")

    if redis_url not in _redis_cache:
        _redis_cache[redis_url] = redis.StrictRedis.from_url(redis_url)

    return _redis_cache[redis_url]


def set_cache_hashmap(redis_key, mapping, timeout=None):
    """
    Sets a hashmap in Redis under the given key.

    :param redis_key: The Redis key where the hashmap will be stored.
    :param mapping: The dictionary containing key-value pairs to store in the hashmap.
    :param timeout: The optional expiration time in seconds for the Redis key.
    """
    redis_client = get_redis_client()

    # Store the hashmap in Redis using hset
    redis_client.hset(redis_key, mapping=mapping)

    # Optionally set the expiration time for the Redis key
    if timeout:
        redis_client.expire(redis_key, timeout)


def get_cache_hashmap(redis_key, field):
    """
    Retrieves a specific field from a hashmap in Redis under the given key.

    :param redis_key: The Redis key from which to retrieve the hashmap.
    :param field: The field (key) to retrieve from the hashmap.
    :return: The value associated with the given field, or None if the field does not exist.
    """
    redis_client = get_redis_client()

    # Retrieve the specific field value from the hashmap
    value = redis_client.hget(redis_key, field)

    # If the value is None, the field does not exist in the hashmap
    if value is None:
        return None

    return value.decode("utf-8")  # Decode the bytes to a string


def download_dhan_instruments():
    # Download the CSV content directly from the URL
    url = "https://images.dhan.co/api-data/api-scrip-master.csv"
    response = requests.get(url)

    if response.status_code == 200:

        csv_data = StringIO(response.text)
        df = pd.read_csv(csv_data)

        map_key = "dhan_instruments_nse"
        instruments = {}

        # Iterate over the DataFrame and store each SEM_CUSTOM_SYMBOL -> SEM_SMST_SECURITY_ID pair in a dict
        for index, row in df.iterrows():
            custom_symbol = row["SEM_CUSTOM_SYMBOL"]
            security_id = row["SEM_SMST_SECURITY_ID"]
            exchange = row["SEM_EXM_EXCH_ID"]

            if exchange == "NSE":
                set_cache(map_key, custom_symbol, security_id)

        print("Data cached in Redis successfully!")
    else:
        print("Failed to download the CSV file.")


def download_zerodha_instruments():
    # Download the CSV content directly from the URL
    url = "https://api.kite.trade/instruments"
    response = requests.get(url)

    if response.status_code == 200:

        csv_data = StringIO(response.text)
        df = pd.read_csv(csv_data)

        map_key = "zerodha_instruments"
        instruments = {}

        # Iterate over the DataFrame and store each trading_symbol -> instrument_token pair in a dict
        for index, row in df.iterrows():
            trading_symbol = row["tradingsymbol"]
            instrument_token = row["instrument_token"]
            instruments[trading_symbol] = instrument_token

        # Store the instruments as a hashmap in Redis using hset
        set_cache_hashmap(map_key, instruments)
        print("Data cached in Redis successfully!")
    else:
        print("Failed to download the CSV file.")


def download_upstox_instruments():
    # Download the JSON.gz file from the provided URL
    url = "https://assets.upstox.com/market-quote/instruments/exchange/NSE.json.gz"
    response = requests.get(url)

    if response.status_code == 200:
        compressed_file = BytesIO(response.content)
        with gzip.GzipFile(fileobj=compressed_file, mode="rb") as file:
            json_data = json.load(file)

        instruments = json_data or []
        map_key = "upstox_instruments"
        instrument_map = {}

        # Iterate through the instruments list and store each trading_symbol -> instrument_key pair in a dict
        for instrument in instruments:
            trading_symbol = instrument.get("trading_symbol")
            instrument_key = instrument.get("instrument_key")

            if trading_symbol and instrument_key:
                instrument_map[trading_symbol] = instrument_key

        # Store the instruments as a hashmap in Redis using hset
        set_cache_hashmap(map_key, instrument_map)
        print("Data cached in Redis successfully!")
    else:
        print("Failed to download the JSON.gz file.")


def get_instrument_id_for_leg(leg: Dict[str, Any], broker: str) -> Tuple[str, str]:
    """
    Generate broker-specific trading symbol format for a leg and retrieve the instrument ID from cache.

    Formats:
    Dhan: RELIANCE-Mar2025-1090-CE
    Upstox: RELIANCE 1090 CE 27 MAR 25
    Zerodha: RELIANCE25MAR1250CE
    AngelOne: RELIANCE27MAR251250CE (DDMMMYY format for date)
    ICICI Direct: RELIANCE 27MAR25 1090 CE (for options) or RELIANCE 27MAR25 FUT (for futures)

    For futures:
    Dhan: NIFTY MAY FUT
    Upstox: RELIANCE FUT 27 MAR 25
    Zerodha: RELIANCE25MARFUT
    AngelOne: RELIANCE27MAR25FUT (DDMMMYY format for date)
    ICICI Direct: RELIANCE 27MAR25 FUT

    Returns:
        tuple: (trading_symbol, instrument_id)
    """
    symbol = leg.get("symbol", "")
    leg_type = leg.get("type", "")

    # Convert expiry date from "27-Mar-2025" to required formats
    expiry_date = leg.get("expiryDate", "")
    if expiry_date:
        try:
            date_obj = datetime.strptime(expiry_date, "%d-%b-%Y")

            # Different date formats for each broker
            dhan_date = date_obj.strftime("%b%Y")  # Mar2025
            upstox_date = date_obj.strftime("%d %b %y").upper()  # 27 MAR 25
            zerodha_date = date_obj.strftime("%y%b").upper()  # 25MAR
            angelone_date = date_obj.strftime("%d%b%y").upper()  # 27MAR25
            icici_date = date_obj.strftime("%d%b%y").upper()  # 27MAR25
        except ValueError as e:
            print(f"Error converting expiry date: {e}")
            return "", ""
    else:
        raise ValueError("Invalid expiry date format")

    # Convert strike to int if it has no decimal part
    strike = leg.get("strike", 0)
    if strike and isinstance(strike, float) and strike.is_integer():
        strike = int(strike)
    strike_str = str(strike)

    trading_symbol = ""
    instrument_id = ""

    if broker == "upstox" or broker == "upstoxapi":
        if leg_type == "FUT":
            trading_symbol = f"{symbol} FUT {upstox_date}"
        else:
            trading_symbol = f"{symbol} {strike_str} {leg_type} {upstox_date}"
        # Get instrument_key from Redis cache
        instrument_id = get_cache("upstox_instruments", trading_symbol)

    elif broker == "zerodha" or broker == "zerodhaapi":
        if leg_type == "FUT":
            trading_symbol = f"{symbol}{zerodha_date}FUT"
        else:
            trading_symbol = f"{symbol}{zerodha_date}{strike_str}{leg_type}"
        # Get instrument_token from Redis cache
        instrument_id = get_cache("zerodha_instruments", trading_symbol)

    elif broker == "dhan" or broker == "dhanapi":
        if leg_type == "FUT":
            # For Dhan futures, custom symbol format is like: "NIFTY MAY FUT"
            trading_symbol = f"{symbol} {date_obj.strftime('%b').upper()} FUT"
        else:
            # For Dhan options, custom symbol format is like: "NIFTY 29 MAY 24000 CALL" or "NIFTY 29 MAY 23950 PUT"
            option_type = "CALL" if leg_type == "CE" else "PUT"
            trading_symbol = f"{symbol} {date_obj.strftime('%d')} {date_obj.strftime('%b').upper()} {strike_str} {option_type}"

        # Get security_id from Redis cache using the custom symbol
        instrument_id = get_cache("dhan_instruments_nse", trading_symbol)

    elif broker == "angelone" or broker == "angeloneapi":
        # AngelOne uses DDMMMYY format for date (e.g., 27MAR25)
        if leg_type == "FUT":
            trading_symbol = f"{symbol}{angelone_date}FUT"
        else:
            trading_symbol = f"{symbol}{angelone_date}{strike_str}{leg_type}"

        # Use Dhan's security_id for AngelOne as they use the same token system
        dhan_trading_symbol = ""
        if leg_type == "FUT":
            # For Dhan futures, custom symbol format is like: "NIFTY MAY FUT"
            dhan_trading_symbol = f"{symbol} {date_obj.strftime('%b').upper()} FUT"
        else:
            # For Dhan options, custom symbol format is like: "NIFTY 29 MAY 24000 CALL" or "NIFTY 29 MAY 23950 PUT"
            option_type = "CALL" if leg_type == "CE" else "PUT"
            dhan_trading_symbol = f"{symbol} {date_obj.strftime('%d')} {date_obj.strftime('%b').upper()} {strike_str} {option_type}"

        # Get security_id from Redis cache using the custom symbol
        instrument_id = get_cache("dhan_instruments_nse", dhan_trading_symbol)

    elif broker == "icicidirect" or broker == "icici-direct":
        # ICICI Direct uses a format like: "RELIANCE 27MAR25 1090 CE" for options or "RELIANCE 27MAR25 FUT" for futures
        if leg_type == "FUT":
            trading_symbol = f"{symbol} {icici_date} FUT"
        else:
            trading_symbol = f"{symbol} {icici_date} {strike_str} {leg_type}"

        # We use the security id from Dhan as ICICI Direct uses the same token system
        dhan_trading_symbol = ""
        if leg_type == "FUT":
            # For Dhan futures, custom symbol format is like: "NIFTY MAY FUT"
            dhan_trading_symbol = f"{symbol} {date_obj.strftime('%b').upper()} FUT"
        else:
            # For Dhan options, custom symbol format is like: "NIFTY 29 MAY 24000 CALL" or "NIFTY 29 MAY 23950 PUT"
            option_type = "CALL" if leg_type == "CE" else "PUT"
            dhan_trading_symbol = f"{symbol} {date_obj.strftime('%d')} {date_obj.strftime('%b').upper()} {strike_str} {option_type}"

        # Get security_id from Redis cache using the custom symbol
        instrument_id = get_cache("dhan_instruments_nse", dhan_trading_symbol)
    else:
        raise ValueError("Invalid broker")

    return trading_symbol, instrument_id or ""


async def calculate_dhan_margin(app: Dict[str, Any], legs: List[Dict[str, Any]], multiplier: int) -> Dict[str, Any]:
    """Calculate margin for Dhan app"""
    try:
        total_margin = 0
        leg_margins = []

        for leg in legs:
            trading_symbol, dhan_security_id = get_instrument_id_for_leg(leg, "dhan")

            if not trading_symbol or not dhan_security_id:
                print(f"Warning: No Dhan security ID found for {trading_symbol}")
                continue

            instrument = {
                "exchange_segment": "NSE_FNO",
                "transaction_type": "BUY" if leg["tr_type"] == "b" else "SELL",
                "quantity": str(leg["lots"] * leg.get("lot_size", 1) * multiplier),
                "product_type": "MARGIN",
                "security_id": str(dhan_security_id),
                "price": str(leg.get("op_pr") if leg["type"] != "FUT" else leg.get("entry_price", 0)),
            }

            request_data = {"params": instrument, "auth": app.get("fields", {})}
            # print(f"Sending request to Dhan for {trading_symbol}:", json.dumps(request_data, indent=2))

            response_data = await make_trading_api_call(app, "get_margin_details", request_data)
            # print(f"Received response from Dhan for {trading_symbol}:", json.dumps(response_data, indent=2))

            margin_data = response_data.get("result", {}).get("margin_info", {})
            total_leg_margin = margin_data.get("total_margin", 0.0)
            span_margin = margin_data.get("span_margin", 0.0)
            exposure_margin = margin_data.get("exposure_margin", 0.0)
            available_balance = margin_data.get("available_balance", 0.0)
            variable_margin = margin_data.get("variable_margin", 0.0)
            insufficient_balance = margin_data.get("insufficient_balance", 0.0)
            brokerage = margin_data.get("brokerage", 0.0)
            leverage = margin_data.get("leverage", "1X")

            total_margin += total_leg_margin

            leg_margins.append(
                {
                    "securityId": trading_symbol,
                    "total_margin": total_leg_margin,
                    "span_margin": span_margin,
                    "exposure_margin": exposure_margin,
                    "available_balance": available_balance,
                    "variable_margin": variable_margin,
                    "insufficient_balance": insufficient_balance,
                    "brokerage": brokerage,
                    "leverage": leverage,
                }
            )

        return {"app_name": app.get("name", ""), "margin": {"total": total_margin, "legs": leg_margins}}

    except Exception as e:
        print(f"Error calculating Dhan margin: {e}")
        return {"app_name": app.get("name", ""), "error": str(e)}


async def calculate_upstox_margin(app: Dict[str, Any], legs: List[Dict[str, Any]], multiplier: int) -> Dict[str, Any]:
    """Calculate margin for Upstox app"""
    try:
        total_margin = 0
        leg_margins = []

        instruments_data = []
        for leg in legs:
            trading_symbol, instrument_key = get_instrument_id_for_leg(leg, "upstox")

            if not trading_symbol or not instrument_key:
                print(f"Warning: No Upstox instrument key found for {trading_symbol}")
                continue

            # Build instrument data according to Upstox API requirements
            instrument = {
                "instrument_key": str(instrument_key),
                "quantity": leg["lots"] * leg.get("lot_size", 1) * multiplier,
                "transaction_type": "BUY" if leg["tr_type"] == "b" else "SELL",
                "product": "D",  # D for Delivery/NRML
            }
            instruments_data.append(instrument)

        if not instruments_data:
            return {"app_name": app.get("name", ""), "error": "No valid instruments found"}

        request_data = {"params": {"instruments_json": json.dumps(instruments_data)}, "auth": app.get("fields", {})}
        print(f"Sending request to Upstox:", json.dumps(request_data, indent=2))
        response_data = await make_trading_api_call(app, "get_margin_details", request_data)
        print(f"Received response from Upstox:", json.dumps(response_data, indent=2))
        margin_data = response_data.get("result", {})

        if margin_data.get("status") == "Success":
            margin_info = margin_data.get("margin_info", {})
            margins = margin_info.get("margins", [])
            total_margin = margin_info.get("final_margin", 0)

            for i, margin_detail in enumerate(margins):
                if i < len(instruments_data):  # Match margin detail with original instrument
                    leg_margins.append(
                        {
                            "securityId": instruments_data[i]["instrument_key"],
                            "total_margin": margin_detail.get("total_margin", 0),
                            "span_margin": margin_detail.get("span_margin", 0),
                            "exposure_margin": margin_detail.get("exposure_margin", 0),
                            "additional_margin": margin_detail.get("additional_margin", 0),
                            "net_buy_premium": margin_detail.get("net_buy_premium", 0),
                            "available_balance": margin_detail.get("available_balance", 0),
                            "variable_margin": margin_detail.get("variable_margin", 0),
                            "insufficient_balance": margin_detail.get("insufficient_balance", 0),
                            "brokerage": margin_detail.get("brokerage", 0),
                            "leverage": margin_detail.get("leverage", "1X"),
                        }
                    )

            return {"app_name": app.get("name", ""), "margin": {"total": total_margin, "legs": leg_margins}}
        else:
            return {"app_name": app.get("name", ""), "error": margin_data.get("message", "Failed to fetch margin details")}

    except Exception as e:
        print(f"Error calculating Upstox margin: {e}")
        return {"app_name": app.get("name", ""), "error": str(e)}


async def calculate_zerodha_margin(app: Dict[str, Any], legs: List[Dict[str, Any]], multiplier: int) -> Dict[str, Any]:
    """Calculate margin for Zerodha app"""
    try:
        total_margin = 0
        leg_margins = []

        instruments_data = []
        for leg in legs:
            trading_symbol, instrument_token = get_instrument_id_for_leg(leg, "zerodha")

            if not trading_symbol or not instrument_token:
                print(f"Warning: No Zerodha instrument token found for {trading_symbol}")
                continue

            # Build instrument data according to Zerodha API requirements
            instrument = {
                "exchange": "NFO",
                "tradingsymbol": trading_symbol,
                "transaction_type": "BUY" if leg["tr_type"] == "b" else "SELL",
                "variety": "regular",
                "product": "NRML",
                "order_type": "LIMIT",
                "quantity": int(leg["lots"] * leg.get("lot_size", 1) * multiplier),
                "price": float(leg.get("op_pr", 0) if leg["type"] != "FUT" else leg.get("entry_price", 0)),
            }
            instruments_data.append(instrument)

        if not instruments_data:
            return {"app_name": app.get("name", ""), "error": "No valid instruments found"}

        request_data = {"params": {"instruments_json": json.dumps(instruments_data)}, "auth": app.get("fields", {})}
        print(f"Sending request to Zerodha:", json.dumps(request_data, indent=2))
        response_data = await make_trading_api_call(app, "get_margin_details", request_data)
        margin_data = response_data.get("result", {})

        print(f"Received response from Zerodha:", json.dumps(response_data, indent=2))

        if margin_data.get("status") == "Success":
            margin_details = margin_data.get("margin_details", [])
            for i, detail in enumerate(margin_details):
                if i < len(instruments_data):  # Match margin detail with original instrument
                    margin = detail.get("total", 0)
                    total_margin += margin
                    leg_margins.append(
                        {
                            "securityId": detail["tradingsymbol"],
                            "total_margin": margin,
                            "span_margin": detail.get("span", 0),
                            "exposure_margin": detail.get("exposure", 0),
                            "option_premium": detail.get("option_premium", 0),
                            "additional": detail.get("additional", 0),
                            "available_balance": detail.get("available_balance", 0),
                            "variable_margin": detail.get("variable_margin", 0),
                            "insufficient_balance": detail.get("insufficient_balance", 0),
                            "brokerage": detail.get("brokerage", 0),
                            "leverage": detail.get("leverage", "1X"),
                        }
                    )

            return {"app_name": app.get("name", ""), "margin": {"total": total_margin, "legs": leg_margins}}
        else:
            return {"app_name": app.get("name", ""), "error": margin_data.get("message", "Failed to fetch margin details")}

    except Exception as e:
        print(f"Error calculating Zerodha margin: {e}")
        return {"app_name": app.get("name", ""), "error": str(e)}


async def calculate_angelone_margin(app: Dict[str, Any], legs: List[Dict[str, Any]], multiplier: int) -> Dict[str, Any]:
    """Calculate margin for AngelOne app"""
    try:
        total_margin = 0
        leg_margins = []

        instruments_data = []
        for leg in legs:
            trading_symbol, instrument_token = get_instrument_id_for_leg(leg, "angelone")

            if not trading_symbol or not instrument_token:
                print(f"Warning: No AngelOne instrument token found for {trading_symbol}")
                continue

            # Build instrument data according to AngelOne API requirements
            instrument = {
                "exchange": "NFO",
                "instrument_token": str(instrument_token),
                "transaction_type": "BUY" if leg["tr_type"] == "b" else "SELL",
                "product_type": "INTRADAY",  # For F&O, typically MARGIN Possible values: DELIVERY, CARRYFORWARD, MARGIN, INTRADAY, BO  Note: Got error for MARGIN
                "order_type": "LIMIT",  # Possible values: MARKET, LIMIT
                "quantity": str(leg["lots"] * leg.get("lot_size", 1) * multiplier),
                "price": str(leg.get("op_pr", 0) if leg["type"] != "FUT" else leg.get("entry_price", 0)),
            }
            instruments_data.append(instrument)

        if not instruments_data:
            return {"app_name": app.get("name", ""), "error": "No valid instruments found"}

        request_data = {"params": {"positions": instruments_data}, "auth": app.get("fields", {})}
        print(f"Sending request to AngelOne:", json.dumps(request_data, indent=2))
        response_data = await make_trading_api_call(app, "get_margin_details", request_data)
        print(f"Received response from AngelOne:", json.dumps(response_data, indent=2))

        margin_data = response_data.get("result", {})

        if margin_data.get("status") == "Success":
            margin_details = margin_data.get("margin_info", {})
            total_margin = margin_details.get("total_margin_required", 0)

            # Process margin details with the correct key names
            margin_components = margin_details.get("margin_components", {})

            # AngelOne returns a summary level margin details, so we need to return that
            leg_margins = [
                {
                    "securityId": "AngelOne Summary",
                    "total_margin": total_margin,
                    "net_premium": margin_components.get("net_premium", 0),
                    "span_margin": margin_components.get("span_margin", 0),
                    "exposure_margin": margin_components.get("exposure_margin", 0),
                    "margin_benefit": margin_components.get("margin_benefit", 0),
                    "delivery_margin": margin_components.get("delivery_margin", 0),
                    "non_nfo_margin": margin_components.get("non_nfo_margin", 0),
                    "tot_options_premium": margin_components.get("total_options_premium", 0),
                }
            ]

            return {"app_name": app.get("name", ""), "margin": {"total": total_margin, "legs": leg_margins}}
        else:
            return {"app_name": app.get("name", ""), "error": margin_data.get("message", "Failed to fetch margin details")}

    except Exception as e:
        print(f"Error calculating AngelOne margin: {e}")
        return {"app_name": app.get("name", ""), "error": str(e)}


async def calculate_icici_direct_margin(app: Dict[str, Any], legs: List[Dict[str, Any]], multiplier: int) -> Dict[str, Any]:
    """Calculate margin for ICICI Direct app"""
    try:
        positions_data = []

        for leg in legs:
            # Get the expiry date in YYYY-MM-DD format
            expiry_date = None
            if leg.get("expiryDate"):
                try:
                    date_obj = datetime.strptime(leg.get("expiryDate", ""), "%d-%b-%Y")
                    expiry_date = date_obj.strftime("%Y-%m-%d")
                except ValueError as e:
                    print(f"Error converting expiry date: {e}")
                    continue

            # Determine product type and right based on leg type
            product_type = "futures" if leg.get("type") == "FUT" else "options"
            right = "others" #For futures, we use others as there is no right
            if product_type == "options":
                right = "call" if leg.get("type") == "CE" else "put"

            # Build position data according to ICICI Direct API requirements
            position = {
                "stock_code": leg.get("symbol", ""),
                "exchange_code": "NFO",  # Assuming F&O trading
                "transaction_type": "BUY" if leg.get("tr_type") == "b" else "SELL",
                "quantity": str(leg.get("lots", 0) * leg.get("lot_size", 1) * multiplier),
                "price": str(leg.get("op_pr", 0) if leg.get("type") != "FUT" else leg.get("entry_price", 0)),
                "expiry_date": expiry_date,
                "product_type": product_type,
                "right": right,
            }

            # Add optional fields if applicable
            if leg.get("strike") and product_type == "options":
                position["strike_price"] = str(leg.get("strike", 0))

            positions_data.append(position)

        if not positions_data:
            return {"app_name": app.get("name", ""), "error": "No valid positions found"}

        # Default to NFO
        exchange_code = "NFO"

        # Prepare the API request data
        request_data = {
            "params": {
                "exchange_code": exchange_code,
                "positions": positions_data
            },
            "auth": app.get("fields", {})
        }

        print(f"Sending request to ICICI Direct:", json.dumps(request_data, indent=2))
        response_data = await make_trading_api_call(app, "get_margin_details", request_data)
        print(f"Received response from ICICI Direct:", json.dumps(response_data, indent=2))

        result = response_data.get("result", {})

        if result.get("status") == "Success":
            margin_info = result.get("margin_info", {})
            total_margin = margin_info.get("total_margin_required", 0)

            # Extract margin components
            margin_components = margin_info.get("margin_components", {})

            # Create leg margins from the margin breakup
            leg_margins = []
            for breakup in margin_info.get("margin_breakup", []):
                leg_margins.append({
                    "securityId": breakup.get("exchange", ""),
                    "total_margin": breakup.get("total_margin_required", 0),
                    "product_type": breakup.get("product_type", ""),
                })

            # If no breakup provided, create a summary entry
            if not leg_margins:
                leg_margins = [{
                    "securityId": "ICICI Direct Summary",
                    "total_margin": total_margin,
                    "span_margin": margin_components.get("span_margin", 0),
                    "exposure_margin": margin_components.get("exposure_margin", 0),
                    "net_premium": margin_components.get("net_premium", 0),
                    "margin_benefit": margin_components.get("margin_benefit", 0),
                    "delivery_margin": margin_components.get("delivery_margin", 0),
                    "non_nfo_margin": margin_components.get("non_nfo_margin", 0),
                    "total_options_premium": margin_components.get("total_options_premium", 0),
                }]

            return {"app_name": app.get("name", ""), "margin": {"total": total_margin, "legs": leg_margins}}
        else:
            return {"app_name": app.get("name", ""), "error": result.get("message", "Failed to fetch margin details")}

    except Exception as e:
        print(f"Error calculating ICICI Direct margin: {e}")
        return {"app_name": app.get("name", ""), "error": str(e)}

