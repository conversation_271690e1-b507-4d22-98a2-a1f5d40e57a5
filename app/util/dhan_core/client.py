import datetime
import os
from functools import lru_cache
from typing import Op<PERSON>
from typing import List
import pandas as pd
from dhanhq import dhanhq

from app.util.settings import app_settings

# Initialize the Dhan client with the provided credentials
print(
    f"Initializing Dhan client with provided credentials... Client Id: {app_settings.DHAN_CLIENT_ID} and Access Token Last 5: {app_settings.DHAN_ACCESS_TOKEN[-5:] if app_settings.DHAN_ACCESS_TOKEN else 'None'}"
)
dhan = dhanhq(app_settings.DHAN_CLIENT_ID, app_settings.DHAN_ACCESS_TOKEN, disable_ssl=True)

index_symbols = {
    "NIFTY": 13,
    "BANKNIFTY": 25,
    "FINNIFTY": 27,  # NIFTY Financial Services (commonly called FINNIFTY)
    "NIFTYNXT50": 38,
    "MIDCPNIFTY": 442,
    "NIFTY FIN SERVICE": 27,  # Alternative name for FINNIFTY
}

# Segment constants
INDEX_SEGMENT = "IDX_I"
NSE_SEGMENT = "NSE_EQ"
NSE_FNO_SEGMENT = "NSE_FNO"


def get_segment(symbol: str) -> str:
    """
    Return the correct segment for a given symbol.
    If symbol is a known index, return INDEX_SEGMENT, else NSE_SEGMENT.
    """
    symbol_upper = symbol.upper()
    if symbol_upper in index_symbols:
        return INDEX_SEGMENT
    return NSE_SEGMENT


@lru_cache(maxsize=1)
def get_security_list_df():
    """
    Fetch and cache the security list DataFrame from Dhan (detailed mode).
    Saves to a local file once per day to avoid repeated downloads.
    Returns a pandas DataFrame.
    """
    local_path = "security_id_list_daily.csv"
    today = datetime.date.today().isoformat()
    # Check if file exists and is from today
    if os.path.exists(local_path):
        mtime = datetime.date.fromtimestamp(os.path.getmtime(local_path))
        if mtime == datetime.date.today():
            return pd.read_csv(local_path)
    # Download from Dhan and save
    df = dhan.fetch_security_list(mode="detailed", filename=local_path)
    # If fetch_security_list does not save, save manually
    if isinstance(df, pd.DataFrame):
        df.to_csv(local_path, index=False)
    return df


def get_security_id(symbol: str, segment: str = "NSE_EQ") -> int:
    """
    Return the security_id for a given symbol by searching the security list CSV.
    Tries UNDERLYING_SYMBOL, SYMBOL_NAME, DISPLAY_NAME (case-insensitive, exact match) with correct SEGMENT (NSE_EQ for stocks).
    Falls back to index_symbols mapping, then returns symbol as-is if not found.
    Always returns an int if possible, else raises ValueError.
    """

    def get_exchange_id(segment: str) -> str:
        if segment.startswith("NSE"):  # e.g., NSE_EQ, NSE_FNO
            return "NSE"
        if segment.startswith("BSE"):  # e.g., BSE_EQ
            return "BSE"
        return segment

    symbol_upper = symbol.upper()
    # Try index_symbols mapping first for known indices
    if symbol_upper in index_symbols:
        return int(index_symbols[symbol_upper])
    exchange_id = get_exchange_id(segment)
    df = get_security_list_df()
    if df is not None:
        # Only consider rows with SEGMENT == 'NSE_EQ' and SERIES == 'EQ' for stocks
        stock_rows = df[(df["SEGMENT"] == "E") & (df["EXCH_ID"] == exchange_id)]
        for col in ["UNDERLYING_SYMBOL", "SYMBOL_NAME", "DISPLAY_NAME"]:
            matches = stock_rows[stock_rows[col].astype(str).str.upper() == symbol_upper]
            if not matches.empty:
                val = matches.iloc[0]["SECURITY_ID"]
                try:
                    return int(val)
                except Exception:
                    print("Could not convert security_id to int:", val)
    try:
        return int(symbol)
    except Exception:
        raise ValueError(f"Could not resolve security_id for symbol: {symbol}")


def _get_result_data(data):
    """
    Unwrap up to two levels of nested 'data' if present.
    """
    if isinstance(data, dict) and "data" in data:
        data = data["data"]
        if isinstance(data, dict) and "data" in data:
            data = data["data"]
    return data


def get_expirations(symbol: str, segment: str = "NSE_EQ"):
    security_id = get_security_id(symbol, segment=segment)
    resp = dhan.expiry_list(under_security_id=security_id, under_exchange_segment=segment)
    return _get_result_data(resp)


def get_option_chain(symbol: str, expiry: str, date: str = None):
    segment = get_segment(symbol)
    security_id = get_security_id(symbol)
    resp = dhan.option_chain(under_security_id=security_id, under_exchange_segment=segment, expiry=expiry)
    return _get_result_data(resp)


def transform_ohlc_data(data):
    """
    Transforms the API's array-based response into a dictionary keyed by epoch timestamp.
    """
    result = {}
    opens = data.get("open", [])
    highs = data.get("high", [])
    lows = data.get("low", [])
    closes = data.get("close", [])
    volumes = data.get("volume", [])
    timestamps = data.get("timestamp", [])

    for o, h, l, c, v, ts in zip(opens, highs, lows, closes, volumes, timestamps):
        epoch = int(ts)
        result[str(epoch)] = {"o": float(o), "h": float(h), "l": float(l), "c": float(c), "v": int(v)}
    return result


def ohlc_intraday(symbol: str, from_date: str, to_date: str, interval: int = 1, instrument_type: str = "EQUITY"):
    """
    Get intraday OHLC data (minute candles) for a symbol between from_date and to_date.
    interval: 1, 5, 15, 25, or 60 (minutes)
    Returns only the 'data' field from the Dhan API response.
    """
    segment = get_segment(symbol)
    security_id = get_security_id(symbol)
    resp = dhan.intraday_minute_data(security_id, segment, instrument_type, from_date, to_date, interval)
    data = _get_result_data(resp)
    return transform_ohlc_data(data)


def ohlc_historical(symbol: str, from_date: str, to_date: str, expiry_code: int = 0, instrument_type: str = "EQUITY"):
    """
    Get historical daily OHLC data for a symbol between from_date and to_date.
    expiry_code: 0 for stocks, 1/2/3 for derivatives if needed.
    Returns only the 'data' field from the Dhan API response.
    """
    segment = get_segment(symbol)
    security_id = get_security_id(symbol)
    resp = dhan.historical_daily_data(security_id, segment, instrument_type, from_date, to_date, expiry_code)
    data = _get_result_data(resp)
    return transform_ohlc_data(data)


def get_futures(symbol: str):
    """
    Return a list of all futures contracts for the given symbol (case-insensitive), with expiry, security_id, display_name, and ticker_data (LTP etc).
    """
    expiry_map = get_futures_expiry_map(symbol)
    segment = NSE_FNO_SEGMENT  # Dhan's segment for futures
    results = []
    for expiry, info in expiry_map.items():
        security_id = info["security_id"]
        display_name = info["display_name"]
        # Pass symbol and segment only, not security_id, to get_ticker_data
        ticker = get_ticker_data(symbol, segment=segment)
        results.append({"expiry": expiry, "security_id": security_id, "display_name": display_name, "ticker_data": ticker})
    return results


def get_quote_data(symbol: str, segment: str = "NSE_EQ"):
    security_id = get_security_id(symbol, segment=segment)
    resp = dhan.quote_data({segment: [security_id]})
    return _get_result_data(resp)


def get_ticker_data(symbol: str, segment: str = "NSE_EQ"):
    security_id = get_security_id(symbol, segment=segment)
    resp = dhan.ticker_data({segment: [security_id]})
    return _get_result_data(resp)


def get_ohlc_data(symbol: str, segment: str = "NSE_EQ"):
    security_id = get_security_id(symbol, segment=segment)
    resp = dhan.ohlc_data(securities={segment: [security_id]})
    data = _get_result_data(resp)
    return data


def get_futures_expiry_map(symbol: str):
    """
    For a given symbol, return a dict mapping expiry dates (as in option chain) to security IDs and display names.
    Matches symbol against SYMBOL_NAME, DISPLAY_NAME, or UNDERLYING_SYMBOL (case-insensitive, exact match), and INSTRUMENT == 'FUTSTK'.
    Only considers NSE futures.
    """
    df = get_security_list_df()
    if df is None:
        return {}
    symbol_upper = symbol.upper()
    # Only consider rows with INSTRUMENT == 'FUTSTK', EXCH_ID == 'NSE', and SM_EXPIRY_DATE not null
    fut_rows = df[
        (df["INSTRUMENT"] == "FUTSTK")
        & (df["EXCH_ID"] == "NSE")
        & (df["SM_EXPIRY_DATE"].notna())
        & (
            (df["SYMBOL_NAME"].astype(str).str.upper() == symbol_upper)
            | (df["DISPLAY_NAME"].astype(str).str.upper() == symbol_upper)
            | (df["UNDERLYING_SYMBOL"].astype(str).str.upper() == symbol_upper)
        )
    ]
    # Use SYMBOL_NAME, SECURITY_ID, SM_EXPIRY_DATE, DISPLAY_NAME for output
    result = {}
    for _, row in fut_rows.iterrows():
        try:
            dt = pd.to_datetime(row["SM_EXPIRY_DATE"])
            expiry_str = dt.strftime("%d-%b-%Y")
        except Exception:
            expiry_str = str(row["SM_EXPIRY_DATE"])
        result[expiry_str] = {"security_id": int(row["SECURITY_ID"]), "display_name": row["DISPLAY_NAME"]}
    return result


def get_quote_data_multiple(symbols: list[str], segment: str = "NSE_EQ"):
    """
    Get full quote data for multiple symbols in a given segment.
    Returns a list of quote data in the same order as input symbols.
    """
    security_ids = [get_security_id(symbol, segment=segment) for symbol in symbols]
    resp = dhan.quote_data({segment: security_ids})
    return _get_result_data(resp)


def get_ticker_data_multiple(symbols: list[str], segment: str = "NSE_EQ"):
    """
    Get LTP (ticker) data for multiple symbols in a given segment.
    Returns a list of ticker data in the same order as input symbols.
    """
    security_ids = [get_security_id(symbol, segment=segment) for symbol in symbols]
    resp = dhan.ticker_data({segment: security_ids})
    return _get_result_data(resp)


def get_ohlc_data_multiple(symbols: list[str], segment: str = "NSE_EQ"):
    """
    Get OHLC data for multiple symbols in a given segment.
    Returns a list of OHLC data in the same order as input symbols.
    """
    security_ids = [get_security_id(symbol, segment=segment) for symbol in symbols]
    resp = dhan.ohlc_data(securities={segment: security_ids})
    return _get_result_data(resp)


# def _transform_option_chain(raw_chain):
#     """
#     Transform the raw option chain response for a single expiry to a flat list of option records.
#     Handles both old and new Dhan formats. Removes expiryDates and only returns the data list.
#     """
#     if not isinstance(raw_chain, dict):
#         return raw_chain
#     # New Dhan format: {"data": {"last_price": ..., "oc": {...}}}
#     if "data" in raw_chain and isinstance(raw_chain["data"], dict) and "oc" in raw_chain["data"]:
#         oc = raw_chain["data"]["oc"]
#         data = []
#         for strike, opt in oc.items():
#             record = {"strikePrice": float(strike), "CE": opt.get("ce"), "PE": opt.get("pe")}
#             data.append(record)
#         return data
#     # Old formats
#     data = []
#     if "data" in raw_chain and isinstance(raw_chain["data"], list):
#         data = raw_chain["data"]
#     elif "option_chain" in raw_chain and isinstance(raw_chain["option_chain"], list):
#         data = raw_chain["option_chain"]
#     elif "data" in raw_chain and isinstance(raw_chain["data"], dict):
#         data = raw_chain["data"].get("data", [])
#     return data


## Lets deprecate this to get for ALL expirations which is very heavy
# def get_option_chain_with_all_expirations(symbol: str, expiry: Optional[str] = None):
#     """
#     Get option chain for a symbol for all available expirations.
#     If expiry is provided, return only for that expiry.
#     Otherwise, return a dict with all expiries and their option chains.
#     """
#     if expiry:
#         chain = get_option_chain(symbol, expiry)
#         return _transform_option_chain(chain)
#     expirations = get_expirations(symbol)
#     if isinstance(expirations, list) and expirations:
#         all_chains = {}
#         for exp in expirations:
#             chain = get_option_chain(symbol, exp)
#             all_chains[exp] = _transform_option_chain(chain)
#         return all_chains
#     return {"error": "No expiry provided and could not auto-detect expiry."}


# Main function to test the Dhan client
if __name__ == "__main__":

    def print_expirations(symbol, segment="NSE_EQ"):
        print(f"Expirations for {symbol} ({segment}):", get_expirations(symbol, segment=segment))

    def print_ohlc(symbol, segment="NSE_EQ"):
        print(f"OHLC for {symbol} ({segment}):", get_ohlc_data(symbol, segment=segment))

    def print_quote(symbol, segment="NSE_EQ"):
        print(f"Quote for {symbol} ({segment}):", get_quote_data(symbol, segment=segment))

    def print_ticker(symbol, segment="NSE_EQ"):
        print(f"Ticker for {symbol} ({segment}):", get_ticker_data(symbol, segment=segment))

    def print_quote_multiple(symbols, segment="NSE_EQ"):
        print(f"Quotes for {symbols} ({segment}):", get_quote_data_multiple(symbols, segment=segment))

    def print_ticker_multiple(symbols, segment="NSE_EQ"):
        print(f"Tickers for {symbols} ({segment}):", get_ticker_data_multiple(symbols, segment=segment))

    def print_ohlc_multiple(symbols, segment="NSE_EQ"):
        print(f"OHLCs for {symbols} ({segment}):", get_ohlc_data_multiple(symbols, segment=segment))

    # Example usage
    print_expirations("NIFTY")
    # print_ohlc("SBIN")
    # print_quote("SBIN")
    # print_ticker("SBIN")
    # print_expirations("SBIN", segment="BSE_EQ")
    # print_ohlc("SBIN", segment="BSE_EQ")
    # print_quote("SBIN", segment="BSE_EQ")
    # print_ticker("SBIN", segment="BSE_EQ")
    # print_quote_multiple(["SBIN", "RELIANCE"], segment="NSE_EQ")
    print_ticker_multiple(["SBIN", "RELIANCE"], segment="NSE_EQ")
    # Wait for one second to avoid rate limits
    import time

    time.sleep(1)

    print_ticker_multiple(["SBIN", "RELIANCE"], segment="BSE_EQ")

    # print_ohlc_multiple(["SBIN", "RELIANCE"], segment="NSE_EQ")
    # print_ohlc_multiple(["SBIN", "RELIANCE"], segment="BSE_EQ")
