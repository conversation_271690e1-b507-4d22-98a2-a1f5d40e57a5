from urllib.parse import urlparse

def format_text_to_url(name: str) -> str:
    return name.lower().replace("&", "and").replace(" ", "-")


def format_url_to_text(url: str) -> str:
    return url.replace("-", " ").replace("and", "&").title()


def format_phone_number(phone: str) -> str:
    digits = "".join(filter(str.isdigit, phone))
    if len(digits) == 12 and digits.startswith("91"):
        return f"+91-{digits[2:7]}-{digits[7:]}"
    if len(digits) == 10:
        return f"+91-{digits[0:5]}-{digits[5:]}"
    if len(digits) == 11 and digits.startswith("0"):
        return f"+91-{digits[1:6]}-{digits[6:]}"
    return phone  # fallback


def cdn(link: str) -> str:
    # Convert a relative or absolute URL to use the aibull CDN
    parsed = urlparse(link)
    path = parsed.path if parsed.scheme else link
    if not path.startswith("/"):
        path = "/" + path
    return f"https://aibull.b-cdn.net{path}"