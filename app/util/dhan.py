# data.py

from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Tuple
from urllib.parse import urlencode

import httpx

from app.util.cache import db_cache


def _get_full_url(symbol: str, start: Optional[str], end: Optional[str], interval: str) -> Tuple[str, Dict[str, Any]]:
    if "nasdaq" in symbol.lower():
        url = f"https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/yohlc?symbol={symbol}"
    else:
        url = f"https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/ohlcd/{symbol}"
    params = {"interval": interval}
    if start:
        params["start"] = start
    if end:
        params["end"] = end
    return url, params


async def _fetch_ohlc_data(symbol: str, start: Optional[str], end: Optional[str], interval: str = "1m") -> Dict[str, Any]:
    """
    Actually fetch OHLC data from the remote API (no cache).
    """
    url, params = _get_full_url(symbol, start, end, interval)
    full_url = f"{url}?{urlencode(params)}"
    print("Fetching full URL:", full_url)
    async with httpx.AsyncClient(timeout=30) as client:
        response = await client.get(url, params=params)
        if response.status_code != 200:
            raise Exception("Error fetching OHLC data.")
        return response.json()


@db_cache(timeout=43200, key_func=lambda symbol, start, end, interval="1m": f"ohlc_data:{symbol}:{start}:{end}:{interval}")
async def _fetch_ohlc_data_cached(symbol: str, start: Optional[str], end: Optional[str], interval: str = "1m") -> Dict[str, Any]:
    return await _fetch_ohlc_data(symbol, start, end, interval)


async def fetch_ohlc_data(symbol: str, start: Optional[str], end: Optional[str], interval: str = "1m") -> Dict[str, Any]:
    """
    Fetch OHLC data, using cache unless the requested end date is today (in IST).
    """
    from pytz import timezone

    ist = timezone("Asia/Kolkata")
    today_ist = datetime.now(ist).date()
    if end:
        try:
            req_date = datetime.strptime(end, "%Y-%m-%d").date()
        except Exception:
            req_date = None
    else:
        req_date = None
    if req_date == today_ist:
        # Don't use cache for today's data
        return await _fetch_ohlc_data(symbol, start, end, interval)
    else:
        return await _fetch_ohlc_data_cached(symbol, start, end, interval)


def filter_ohlc_data_for_date(processed: Dict[str, Any], date: str) -> Dict[str, Any]:
    """
    Filter processed OHLC data for a specific date (YYYY-MM-DD).
    If no data for the date, returns the most recent previous date's data.
    """
    from datetime import datetime

    import pytz

    ist = pytz.timezone("Asia/Kolkata")
    requested_date = datetime.strptime(date, "%Y-%m-%d").date()
    filtered = {}
    for k, v in processed.items():
        dt_ist = datetime.fromtimestamp(int(k), ist)
        if dt_ist.date() == requested_date:
            filtered[k] = v
    if not filtered:
        date_to_keys = {}
        for k, v in processed.items():
            dt_ist = datetime.fromtimestamp(int(k), ist)
            d = dt_ist.date()
            if d not in date_to_keys:
                date_to_keys[d] = {}
            date_to_keys[d][k] = v
        available_dates = [d for d in date_to_keys if d < requested_date]
        if available_dates:
            last_date = max(available_dates)
            filtered = date_to_keys[last_date]
        else:
            if date_to_keys:
                last_date = max(date_to_keys.keys())
                filtered = date_to_keys[last_date]
    return filtered
