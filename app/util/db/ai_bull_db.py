import certifi
from pymongo import MongoClient
from app.util.settings import app_settings
import httpx


async def get_portfolios(access_token: str):
    """
    Fetch portfolios using HTTP request similar to crud.js
    """
    base_url = f"{app_settings.MANTRA_AI_SERVER}/db/portfolios"

    params = {
        "limit": 100,  # Adjust as needed
        "sort": "desc",
        "sort_key": "created_at",
    }
    headers = {
        "Authorization": f"Bearer {access_token}",
        "X-Mantra-App": app_settings.MANTRA_AI_APP,
    }

    response = httpx.get(base_url, params=params, headers=headers)

    if response.status_code != 200:
        raise Exception(f"Error fetching portfolios: {response.text}")

    data = response.json()
    portfolios = data.get("results", [])

    # Remove _id from each portfolio
    for portfolio in portfolios:
        portfolio.pop("_id", None)

    return portfolios


async def get_portfolio(portfolio_uid: str, access_token: str):
    """
    Fetch a single portfolio by its UID using HTTP request
    Similar to crud.js getDocument method
    """
    base_url = f"{app_settings.MANTRA_AI_SERVER}/db/portfolios/{portfolio_uid}"

    headers = {
        "Authorization": f"Bearer {access_token}",
        "X-Mantra-App": app_settings.MANTRA_AI_APP,
    }

    params = {
        "filter": f"uid:{portfolio_uid}",
    }

    response = httpx.get(base_url, headers=headers, params=params)

    if response.status_code != 200:
        raise Exception(f"Error fetching portfolio: {response.text}")

    data = response.json()
    if data:
        data.pop("_id", None)
    return data

class MongoDBUtil:
    def __init__(self):
        if "srv" in app_settings.MONGO_DB_CONNECTION:
            self.client = MongoClient(app_settings.MONGO_DB_CONNECTION, tlsCAFile=certifi.where())
        else:
            self.client = MongoClient(app_settings.MONGO_DB_CONNECTION)
        # Send a ping to confirm a successful connection
        try:
            self.client.admin.command("ping")
            print("Pinged your deployment. You successfully connected to MongoDB!")
        except Exception as e:
            print(e)

        self.db = self.client.get_database("aibull")


def get_db():
    """
    Returns the MongoDB database instance.
    """
    return DB.db

DB = MongoDBUtil()
