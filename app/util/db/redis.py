import json
import os

import redis

# Global cache for Redis clients
_redis_cache = {}


def get_redis_client(redis_url=None):
    """
    Initializes and returns a Redis client using the provided Redis URL or the environment-specific Redis URL.
    If a client for the given URL already exists, it returns the cached client.

    :param redis_url: The Redis connection URL. If None, fetch from environment variable "REDIS_CACHE".
    :return: An initialized Redis client.
    """
    # If no Redis URL is provided, get it from the environment variable
    if redis_url is None:
        redis_url = os.getenv("REDIS_URI", "redis://:rr95z28gbaw0@65.2.123.221:6379/3")

    # Check if a Redis client for this URL is already cached
    if redis_url not in _redis_cache:
        # Initialize and cache the Redis client
        _redis_cache[redis_url] = redis.StrictRedis.from_url(redis_url)

    return _redis_cache[redis_url]


def set_cache(namespace, key, value, timeout=None):
    """
    Set a value in the cache with an optional timeout and a namespace.

    :param namespace: The namespace to be appended to the key.
    :param key: The key to store the value under.
    :param value: The value to store. Can be a string or a dictionary.
    :param timeout: The timeout (in seconds) for the cache. If None, the cache never expires.
    """
    # Get the Redis client
    redis_client = get_redis_client()

    # Create the namespaced key
    namespaced_key = f"{namespace}:{key}"

    # If the value is a dictionary, serialize it to JSON
    if isinstance(value, (dict, list)):
        value = json.dumps(value)

    # Set the value in Redis with optional timeout
    redis_client.set(name=namespaced_key, value=value, ex=timeout)


def get_cache(namespace, key):
    """
    Retrieve a value from the cache using a namespaced key.

    :param namespace: The namespace to be appended to the key.
    :param key: The key of the value to retrieve.
    :return: The value if found, otherwise None. Automatically deserializes JSON strings into dictionaries.
    """
    # Get the Redis client
    redis_client = get_redis_client()

    # Create the namespaced key
    namespaced_key = f"{namespace}:{key}"

    # Retrieve the value from Redis
    value = redis_client.get(name=namespaced_key)

    if value is None:
        return None

    # Decode the value from bytes to string
    value = value.decode("utf-8")

    # Try to deserialize the value as JSON, if possible
    try:
        value = json.loads(value)
    except json.JSONDecodeError:
        # If value is not a JSON string, return it as is (likely a regular string)
        pass

    return value


def get_cache_hashmap(redis_key, field):
    """
    Retrieves a specific field from a hashmap in Redis under the given key.

    :param redis_key: The Redis key from which to retrieve the hashmap.
    :param field: The field (key) to retrieve from the hashmap.
    :return: The value associated with the given field, or None if the field does not exist.
    """
    redis_client = get_redis_client()

    # Retrieve the specific field value from the hashmap
    value = redis_client.hget(redis_key, field)

    # If the value is None, the field does not exist in the hashmap
    if value is None:
        return None

    return value.decode("utf-8")  # Decode the bytes to a string
