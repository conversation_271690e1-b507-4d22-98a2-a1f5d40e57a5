from datetime import datetime

import certifi
from bson import ObjectId
from pymongo import AsyncMongoClient, MongoClient

from app.util.settings import app_settings


class MongoDBUtil:
    def __init__(self):
        self.adb_client = AsyncMongoClient(app_settings.MONGO_DB_CONNECTION, tlsCAFile=certifi.where())
        self.adb = self.adb_client.get_database("aibull")

        self.CacheAsync = self.adb.get_collection("cache")


def safe_documents(query_result):
    """
    Converts ObjectId fields to strings in the result of a MongoDB find query.
    """
    documents = list(query_result)
    for doc in documents:
        for key, value in doc.items():
            if isinstance(value, ObjectId):
                doc[key] = str(value)
            elif isinstance(value, datetime):
                doc[key] = value.isoformat()  # or str(value) if you prefer
    return documents


def safe_document(data):
    """
    Recursively convert ObjectId and datetime fields in the data to strings.
    """
    if isinstance(data, dict):
        return {key: safe_document(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [safe_document(item) for item in data]
    elif isinstance(data, ObjectId):
        return str(data)
    elif isinstance(data, datetime):
        return data.isoformat()
    else:
        return data


DB = MongoDBUtil()
