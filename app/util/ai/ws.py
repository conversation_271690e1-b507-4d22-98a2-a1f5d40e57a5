import json
import traceback
from typing import Any, Dict, List

import websockets
from fastapi import WebSocket

from app.util.ai.app_functions import handle_function_call


async def create_session_direct(websocket_openai: websockets.WebSocketClientProtocol, app_tools: List[Dict[str, Any]], platform: str):
    prompt: Dict[str, Any] = {
        "type": "session.update",
        "session": {
            "modalities": ["audio", "text"],
            "instructions": f"""
                You are a specialized AI trading assistant. 

                Always begin by politely greeting the user with a message like: 
                    'Hello! Welcome to your trading assistant.'
                
                Immediately after greeting, explicitly ask the user:

                "Which platform would you like to use for trading? Please select only one: {platform}."

                Based on the user's selection, strictly adhere to the following rules:

                1. **ONLY invoke functions associated with the user's chosen platform.**
                    - For example, if the user selects "zerodha," you MUST exclusively invoke tools with descriptions explicitly mentioning Zerodha Kite API.
                    - Never mix tools from different platforms.
                
                2. Do NOT provide independent responses or extra commentary beyond the specified tools.

                3. If a user's query or request does not relate explicitly to the chosen platform's provided tools, refrain from responding.

                4. Limit responses strictly to explaining or invoking the correct functions associated with the selected platform.
            """,
            "voice": "alloy",
            "input_audio_transcription": {"model": "whisper-1"},
            "tools": app_tools,
            "tool_choice": "auto",
        },
    }
    await websocket_openai.send(json.dumps(prompt))


async def send_audio_bytes(websocket_openai: websockets.WebSocketClientProtocol, audio_bytes: str):
    await websocket_openai.send(
        json.dumps(
            {
                "type": "input_audio_buffer.append",
                "audio": audio_bytes,
            }
        )
    )


async def listen_to_openai(websocket_openai: websockets.WebSocketClientProtocol, websocket_client: WebSocket, user_data: Dict[str, Any]):
    try:
        async for message in websocket_openai:
            openai_response = json.loads(message)
            if openai_response.get("type") == "response.output_item.done":
                item = openai_response.get("item", {})
                if item and item.get("type") == "function_call" and item.get("status") == "completed":
                    function_name = item.get("name")
                    try:
                        arguments = json.loads(item.get("arguments", "{}"))
                    except Exception as e:
                        arguments = {}
                    print(f"Function call: {function_name} with arguments: {arguments}")
                    platform, function_name = function_name.split("_", 1)
                    print(f"Function rewrite: {function_name} with arguments: {arguments}")

                    try:
                        result = handle_function_call(function_name=function_name, params=arguments, app_key=platform, user_data=user_data.get("User_Data"))  # type: ignore
                        output_payload: Dict[str, Any] = {
                            "type": "conversation.item.create",
                            "item": {"type": "function_call_output", "call_id": item.get("call_id"), "output": json.dumps(result)},
                        }
                    except Exception as e:
                        print(f"Error in function call: {e}")
                        output_payload = {
                            "type": "conversation.item.create",
                            "item": {"type": "function_call_output", "call_id": item.get("call_id"), "output": json.dumps({"error": "Function call failed"})},
                        }
                    await websocket_openai.send(json.dumps(output_payload))
                    await websocket_openai.send(json.dumps({"type": "response.create"}))
            await websocket_client.send_json(openai_response)
    except Exception as e:
        traceback.print_exc()
        print(f"Error in OpenAI listener: {e}")
