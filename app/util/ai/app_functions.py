from functools import lru_cache
from typing import Any, Dict, List, Optional

import requests
from pydantic import BaseModel

MANTRA_APPS_SERVER = "https://apps.kong.ai"
Mantra_Apps = None  # Global variable to store apps


class ExecuteFunctionRequest(BaseModel):
    params: Dict[str, Any]
    auth: Dict[str, Any]


@lru_cache(maxsize=32)
def get_apps():
    """
    Fetches the list of apps from the specified URL with caching.
    """
    global Mantra_Apps
    if Mantra_Apps is None:
        url = f"{MANTRA_APPS_SERVER}/apps/all"
        response = requests.get(url)
        response.raise_for_status()  # Raise an HTTPError for bad responses
        Mantra_Apps = response.json()
    return Mantra_Apps


def get_app(app_name: str) -> Optional[Dict[str, Any]]:
    """
    Retrieves the app information based on the app name.
    """
    apps = get_apps()
    for app in apps:
        if app["name"].lower() == app_name.lower():
            return app
    return None


def get_app_category(category: str = "Stocks") -> Optional[List[str]]:
    """
    get the apps with category and get the keys of it
    sample
    [
        {
            "name": "Zerodha",
            "category": "Stocks",
            "description": "Trade stocks, manage investments, and access market data with Zerodha.",
            "auth_type": "oauth2"
            "key": "zerodha",
        },
        {
            "name": "Upstox",
            "category": "Stocks",
            "description": "Trade stocks, manage investments, and access market data with Upstox.",
            "auth_type": "oauth2"
            "key": "upstox",
        }
    ]
    """
    apps = get_apps()
    app_keys: List[str] = []
    for app in apps:
        if app["category"].lower() == category.lower():
            app_keys.append(app["key"])
    return app_keys


def get_app_from_key(key: str) -> Optional[Dict[str, Any]]:
    """
    Retrieves the app information based on the app key.
    """
    apps = get_apps()
    for app in apps:
        if app["key"].lower() == key.lower():
            return app
    return None


def get_function_from_function_key(app_key: str, function_name: str) -> Optional[Dict[str, Any]]:
    """
    Retrieves the entire function information based on the app key and function name.

    :param app_key: The app key
    :param function_name: The function name
    :return: The entire function dictionary or None if not found
    """
    app = get_app_from_key(app_key)
    if app:
        for func in app.get("functions", []):
            if func["function"]["name"] == function_name:
                return func  # Return the entire function dictionary
    return None


def find_functions(app_functions: List[str]) -> List[Dict[str, Any]]:
    """
    Searches for the functions matching the given list of app_key:function_name strings and returns them in the specified format.

    :param app_functions: A list of strings in the format "app_key:function_name"
    :return: A list of dictionaries containing the functions' information
    """
    tools = []
    for app_function in app_functions:
        app_key, function_name = app_function.split(":")
        func = get_function_from_function_key(app_key, function_name)
        if func:
            tool = {  # type: ignore
                "type": func.get("type", "function"),
                "name": f"{app_key}_{func['function']['name']}",
                "description": func["function"]["description"],
                "parameters": func["function"]["parameters"],
            }
            tools.append(tool)  # type: ignore
    return tools  # type: ignore


def handle_function_call(function_name: str, params: Dict[str, Any], app_key: str, user_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Handles the function call by making a REST call with the bot_id and function parameters.

    :param function_name: The function to be called
    :param params: The parameters for the function call
    :param user_data: Optional pre-fetched user data
    :return: The response from the function call
    """

    function_info = None

    for app in user_data.get("apps", []):  # type: ignore
        if app_key == app.get("key"):  # type: ignore
            function_info = get_function_from_function_key(app_key, function_name)
            break

    if not app_key or not function_info:
        raise ValueError(f"Function not found in user data - {function_name}. Please ensure that you have added the application with the required capabilities.")

    # Combine function params and authentication params
    combined_request = ExecuteFunctionRequest(params=params, auth=app.get("fields", {}))  # type: ignore

    # Make the REST call to the server side to execute the function
    url = f"{MANTRA_APPS_SERVER}/functions/exec/{app_key}/{function_info['function']['name']}"
    print(f"Making a REST call to {url} with payload: {combined_request.model_dump()}\n\n")
    response = requests.post(url, json=combined_request.model_dump())
    response.raise_for_status()

    response = response.json()
    print(f"Response from the function call: {response}\n\n")

    output = response.get("result")
    return output
