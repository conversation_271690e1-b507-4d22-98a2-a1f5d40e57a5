from typing import Dict, List, Optional, Any


def generate_mutual_funds_faqs(
    path_param: str,
    seo_filter_mappings: Dict[str, Dict[str, Any]],
    seo_metadata: Dict[str, Any],
    fund_data: Optional[Dict[str, Any]] = None,
) -> List[Dict[str, str]]:
    keyword = fund_data.get("scheme_name", "Mutual Funds").lower() if fund_data else seo_metadata.get("title", "Mutual Funds").replace(" - Top Picks", "").replace(" - Best Picks", "").lower()

    base_faqs = [
        {
            "question": f"What are the top {keyword} to invest in for 2025?",
            "answer": (
                f"Top {keyword} for 2025 are selected based on historical performance, "
                "fund manager expertise, and metrics like NAV and AUM. Use our screener "
                "to identify funds with strong returns and consistent ratings."
            ),
        },
        {
            "question": f"Which {keyword} have delivered the highest 3-year returns?",
            "answer": (
                f"To find {keyword} with the highest 3-year returns, check funds with " "consistent performance above their benchmark. Sort by annualized returns " "and review risk-adjusted metrics."
            ),
        },
        {
            "question": f"How can I select the best {keyword} for my portfolio?",
            "answer": (
                f"Select the best {keyword} by comparing NAV, AUM, expense ratios, and past "
                "performance. Use our filtering tools to match funds with your risk appetite "
                "and investment objectives."
            ),
        },
        {
            "question": f"How do {keyword} compare in terms of NAV, AUM, and returns?",
            "answer": (f"Compare {keyword} by analyzing NAV for unit pricing, AUM for fund stability, " "and historical returns for performance. Our platform provides detailed comparisons."),
        },
    ]

    if path_param in seo_filter_mappings:
        mapping = seo_filter_mappings[path_param]
        category = mapping.get("category", "").lower()
        sub_category = mapping.get("sub_category", "").lower().replace(" fund", "")
        keyword = f"{sub_category} {category} funds".strip() or f"{category} funds"

        base_faqs = [
            {
                "question": f"What are the top {keyword} to invest in for 2025?",
                "answer": (f"Top {keyword} for 2025 are chosen based on high ratings, consistent returns, " "and robust AUM. Filter by performance metrics to find funds that suit your strategy."),
            },
            {
                "question": f"Which {keyword} offer the highest 3-year returns?",
                "answer": (f"{keyword.title()} with the highest 3-year returns typically outperform " "their benchmarks. Use our screener to sort by returns and evaluate consistency and risk."),
            },
            {
                "question": f"How do I choose the best {keyword} for my needs?",
                "answer": (f"Choose {keyword} by assessing NAV, AUM, expense ratios, and historical returns. " "Our advanced screener helps you filter funds based on your financial objectives."),
            },
            {
                "question": f"How do top {keyword} compare on key metrics?",
                "answer": (f"Compare top {keyword} by examining NAV for pricing, AUM for fund size, " "and returns for performance. Use our tools to analyze these metrics and select the best fit."),
            },
        ]

        category_faq = _get_category_specific_faq(category, keyword)
        if category_faq:
            base_faqs.append(category_faq)

    return base_faqs[:8]


def _get_category_specific_faq(category: str, keyword: str) -> Optional[Dict[str, str]]:
    category_faqs = {
        "equity": {
            "question": f"What are the advantages of investing in {keyword}?",
            "answer": (f"{keyword.title()} offer high growth potential for investors comfortable " "with market volatility. They are ideal for long-term goals due to their exposure to equities."),
        },
        "debt": {
            "question": f"Why are {keyword} preferred for stable returns?",
            "answer": (f"{keyword.title()} focus on fixed-income securities, providing lower risk " "and steady returns, making them suitable for conservative investors seeking stability."),
        },
        "hybrid": {
            "question": f"How do {keyword} manage risk and return?",
            "answer": (f"{keyword.title()} balance risk and return by investing in both equities and debt. " "This mix offers growth potential with reduced volatility for moderate-risk investors."),
        },
        "elss": {
            "question": f"How do {keyword} provide tax benefits?",
            "answer": (f"{keyword.title()} qualify for tax deductions under Section 80C, with a 3-year " "lock-in period, combining tax savings with potential equity-linked growth."),
        },
    }
    return category_faqs.get(category)
