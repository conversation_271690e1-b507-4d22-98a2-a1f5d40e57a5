"""
Arbitrage Utilities

This module contains functions for discovering and analyzing arbitrage opportunities
between different exchanges (NSE and BSE) and between stocks and futures.
"""

import asyncio
from typing import List, Dict, Any

from app.util.dhan_core.client import get_ticker_data_multiple, get_security_id, get_security_list_df, get_futures_expiry_map, dhan, _get_result_data


def get_common_securities_by_isin(limit: int = 50) -> List[Dict[str, Any]]:
    """
    Extract securities that are available on both NSE and BSE using ISIN matching
    This ensures we're comparing the same underlying company/security on both exchanges

    Args:
        limit: Maximum number of security pairs to return

    Returns:
        List of dictionaries with NSE and BSE security details for the same ISIN
    """
    try:
        df = get_security_list_df()
        if df is None:
            print("Warning: Could not load Dhan security list")
            return []  # Return empty list for fallback handling

        # Filter for equity stocks only with additional filters to exclude bonds/debentures
        equity_df = df[
            (df["SEGMENT"] == "E")  # Equity segment
            & (df["INSTRUMENT"] == "EQUITY")  # Only equity instruments
            & (~df["UNDERLYING_SYMBOL"].str.contains(r"^\d+", na=False))  # Exclude symbols starting with numbers
            & (~df["UNDERLYING_SYMBOL"].str.contains(r"[0-9]{2}[A-Z]$", na=False))  # Exclude bond-like patterns
            & (df["UNDERLYING_SYMBOL"].str.len() <= 20)  # Reasonable symbol length
            & (df["UNDERLYING_SYMBOL"].str.isalpha() | df["UNDERLYING_SYMBOL"].str.contains(r"^[A-Z]+$", na=False))  # Only alphabetic symbols
            & (df["ISIN"].notna())  # Must have ISIN
            & (df["ISIN"] != "")  # ISIN must not be empty
            & (df["ISIN"].str.len() == 12)  # ISIN should be 12 characters
        ].copy()

        # Get NSE securities with ISIN
        nse_securities = equity_df[equity_df["EXCH_ID"] == "NSE"][["ISIN", "UNDERLYING_SYMBOL", "SECURITY_ID"]].copy()
        nse_securities.columns = ["ISIN", "NSE_SYMBOL", "NSE_SECURITY_ID"]

        # Get BSE securities with ISIN
        bse_securities = equity_df[equity_df["EXCH_ID"] == "BSE"][["ISIN", "UNDERLYING_SYMBOL", "SECURITY_ID"]].copy()
        bse_securities.columns = ["ISIN", "BSE_SYMBOL", "BSE_SECURITY_ID"]

        # Find securities with same ISIN on both exchanges
        common_securities = nse_securities.merge(bse_securities, on="ISIN", how="inner")

        print(f"Found {len(common_securities)} securities with same ISIN on both NSE and BSE")

        # Sort securities by NSE symbol and limit the number of results
        sorted_securities = common_securities.sort_values("NSE_SYMBOL")
        limited_securities = sorted_securities.head(limit).to_dict("records")

        return limited_securities

    except Exception as e:
        print(f"Error extracting securities from Dhan CSV: {e}")
        import traceback

        traceback.print_exc()
        # Return empty list for fallback handling
        return []


async def get_exchange_arbitrage_data_from_dhan(securities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Fetch NSE and BSE data for given securities using Dhan API
    Uses ISIN-matched securities to ensure we're comparing the same company

    Args:
        securities: List of security dictionaries with NSE and BSE details for same ISIN

    Returns:
        List of dictionaries containing arbitrage opportunities with:
        - isin: ISIN number
        - nse_symbol: NSE trading symbol
        - bse_symbol: BSE trading symbol
        - nse_price: Current NSE price
        - bse_price: Current BSE price
        - difference: Absolute price difference
        - difference_percent: Percentage difference
        - action: Recommended trading action
    """
    try:
        if not securities:
            print("No securities provided for arbitrage analysis")
            return []

        print(f"Fetching arbitrage data for {len(securities)} ISIN-matched securities...")

        # Extract NSE and BSE symbols separately
        nse_symbols = [sec["NSE_SYMBOL"] for sec in securities]
        bse_symbols = [sec["BSE_SYMBOL"] for sec in securities]

        # Fetch NSE data
        nse_data = get_ticker_data_multiple(nse_symbols, segment="NSE_EQ")

        # Small delay 1 sec to respect rate limits
        await asyncio.sleep(1)

        # Fetch BSE data
        bse_data = get_ticker_data_multiple(bse_symbols, segment="BSE_EQ")

        # Check if data is valid
        if nse_data is None or bse_data is None:
            print("Warning: Received None data from Dhan API")
            return []

        # Extract actual data from nested structure
        nse_actual_data = nse_data["NSE_EQ"]
        bse_actual_data = bse_data["BSE_EQ"]

        print(f"Processing {len(nse_actual_data)} NSE stocks and {len(bse_actual_data)} BSE stocks")

        # Structure the data for template
        arbitrage_data = []

        for i, security in enumerate(securities):
            try:
                nse_symbol = security["NSE_SYMBOL"]
                bse_symbol = security["BSE_SYMBOL"]
                isin = security["ISIN"]

                # Use the security IDs from the CSV data
                nse_security_id = str(security["NSE_SECURITY_ID"])
                bse_security_id = str(security["BSE_SECURITY_ID"])

                # Get data using security IDs
                nse_item = nse_actual_data.get(nse_security_id, {}) if isinstance(nse_actual_data, dict) else {}
                bse_item = bse_actual_data.get(bse_security_id, {}) if isinstance(bse_actual_data, dict) else {}

                # Extract prices with multiple fallbacks
                nse_price = 0
                if nse_item and isinstance(nse_item, dict):
                    nse_price = float(nse_item.get("last_price", 0) or nse_item.get("LTP", 0) or nse_item.get("ltp", 0) or nse_item.get("lastPrice", 0) or 0)

                bse_price = 0
                if bse_item and isinstance(bse_item, dict):
                    bse_price = float(bse_item.get("last_price", 0) or bse_item.get("LTP", 0) or bse_item.get("ltp", 0) or bse_item.get("lastPrice", 0) or 0)

                if nse_price > 0 and bse_price > 0:
                    diff = abs(nse_price - bse_price)
                    diff_percent = (diff / max(nse_price, bse_price)) * 100

                    action = "No Action"
                    if nse_price > bse_price:
                        action = "Buy on BSE, Sell on NSE"
                    elif bse_price > nse_price:
                        action = "Buy on NSE, Sell on BSE"

                    arbitrage_data.append(
                        {
                            "isin": isin,
                            "symbol": nse_symbol,  # Use NSE symbol as primary display symbol
                            "nse_symbol": nse_symbol,
                            "bse_symbol": bse_symbol,
                            "nse_price": nse_price,
                            "bse_price": bse_price,
                            "difference": diff,
                            "difference_percent": diff_percent,
                            "action": action,
                        }
                    )
                else:
                    print(f"Skipping {nse_symbol}/{bse_symbol} (ISIN: {isin[:8]}...): Invalid prices (NSE: {nse_price}, BSE: {bse_price})")

            except Exception as symbol_error:
                print(f"Error processing security {security.get('NSE_SYMBOL', 'Unknown')}: {symbol_error}")
                continue

        print(f"Successfully processed {len(arbitrage_data)} arbitrage opportunities")
        return arbitrage_data

    except Exception as e:
        print(f"Error fetching arbitrage data: {e}")
        import traceback

        traceback.print_exc()
        return []


def calculate_arbitrage_metrics(nse_price: float, bse_price: float) -> Dict[str, Any]:
    """
    Calculate arbitrage metrics for given NSE and BSE prices

    Args:
        nse_price: NSE stock price
        bse_price: BSE stock price

    Returns:
        Dictionary with difference, percentage, and recommended action
    """
    if nse_price <= 0 or bse_price <= 0:
        return {"difference": 0, "difference_percent": 0, "action": "No Action"}

    diff = abs(nse_price - bse_price)
    diff_percent = (diff / max(nse_price, bse_price)) * 100

    action = "No Action"
    if nse_price > bse_price:
        action = "Buy on BSE, Sell on NSE"
    elif bse_price > nse_price:
        action = "Buy on NSE, Sell on BSE"

    return {"difference": diff, "difference_percent": diff_percent, "action": action}


def get_futures_arbitrage_symbols(limit: int = 100) -> List[str]:
    """
    Get a list of symbols that have both equity and futures contracts available.

    Args:
        limit: Maximum number of symbols to return

    Returns:
        List of symbol names that have futures contracts
    """
    try:
        df = get_security_list_df()
        if df is None:
            return []

        # Get all equity symbols from NSE_EQ using UNDERLYING_SYMBOL
        equity_symbols = set(df[(df["EXCH_ID"] == "NSE") & (df["INSTRUMENT"] == "EQUITY")]["UNDERLYING_SYMBOL"].dropna().str.upper().unique())
        
        # Filter out symbols containing "NSETEST"
        equity_symbols = {symbol for symbol in equity_symbols if "NSETEST" not in symbol}

        # Get all futures symbols from NSE_FNO using UNDERLYING_SYMBOL
        futures_symbols = set(df[(df["EXCH_ID"] == "NSE") & (df["INSTRUMENT"] == "FUTSTK")]["UNDERLYING_SYMBOL"].dropna().str.upper().unique())

        # Find common symbols (stocks that have futures)
        common_symbols = list(equity_symbols.intersection(futures_symbols))

        # Sort and limit
        common_symbols.sort()
        return common_symbols[:limit]

    except Exception as e:
        print(f"Error getting futures arbitrage symbols: {e}")
        import traceback

        traceback.print_exc()
        return []


async def get_futures_arbitrage_data_from_dhan(symbols: List[str]) -> List[Dict[str, Any]]:
    """
    Fetch stock and futures data for given symbols using Dhan API and calculate arbitrage opportunities.

    Args:
        symbols: List of symbol names to analyze

    Returns:
        List of dictionaries containing arbitrage opportunities with:
        - symbol: Stock symbol
        - stock_price: Current stock price
        - futures_price: Current futures price (nearest expiry)
        - difference: Absolute price difference
        - difference_percent: Percentage difference
        - action: Recommended trading action
        - expiry_date: Futures contract expiry date
    """
    try:
        if not symbols:
            print("No symbols provided for futures arbitrage analysis")
            return []

        print(f"Fetching futures arbitrage data for {len(symbols)} symbols...")

        # Fetch stock data (NSE_EQ) for all symbols at once
        print("Fetching stock data for all symbols...")
        stock_data = {}
        try:
            stock_data = get_ticker_data_multiple(symbols, segment="NSE_EQ")
            if stock_data and isinstance(stock_data, dict):
                print(f"Successfully fetched stock data for {len(symbols)} symbols")
            else:
                print("Warning: No stock data received from API")
        except Exception as e:
            print(f"Error fetching stock data: {e}")

        # Collect all futures security IDs for batch fetching
        print("Collecting futures security IDs...")
        futures_security_map = {}  # symbol -> (security_id, expiry_date, futures_info)
        valid_symbols = []

        for symbol in symbols:
            try:
                # Get futures expiry map for this symbol
                futures_expiry_map = get_futures_expiry_map(symbol)

                if not futures_expiry_map:
                    print(f"No futures contracts found for {symbol}")
                    continue

                # Get the nearest expiry (first in sorted order)
                nearest_expiry = min(futures_expiry_map.keys())
                futures_info = futures_expiry_map[nearest_expiry]
                futures_security_id = futures_info["security_id"]

                futures_security_map[symbol] = (futures_security_id, nearest_expiry, futures_info)
                valid_symbols.append(symbol)
            except Exception as e:
                print(f"Error collecting futures data for {symbol}: {e}")
                continue

        print(f"Collected futures data for {len(valid_symbols)} symbols")

        # Fetch futures data for all symbols at once using get_ticker_data_multiple
        print("Fetching futures data for all symbols...")
        futures_data = {}
        if futures_security_map:
            try:
                # Prepare security IDs for batch fetching (convert to strings)
                security_ids = [str(security_id) for security_id, _, _ in futures_security_map.values()]
                futures_data = get_ticker_data_multiple(security_ids, segment="NSE_FNO")
                if futures_data and isinstance(futures_data, dict):
                    print(f"Successfully fetched futures data for {len(security_ids)} contracts")
                else:
                    print("Warning: No futures data received from API")
            except Exception as e:
                print(f"Error fetching batch futures data: {e}")

        # Process data and calculate arbitrage opportunities
        print("Processing arbitrage data...")
        arbitrage_data = []

        for symbol in valid_symbols:
            try:
                futures_security_id, nearest_expiry, futures_info = futures_security_map[symbol]

                # Extract stock price
                stock_price = 0
                if stock_data and isinstance(stock_data, dict):
                    # Find stock data by security ID - data is nested under NSE_EQ
                    stock_security_id = str(get_security_id(symbol, segment="NSE_EQ"))
                    nse_eq_data = stock_data.get("NSE_EQ", {})
                    stock_item = nse_eq_data.get(stock_security_id, {})
                    if stock_item and isinstance(stock_item, dict):
                        stock_price = float(stock_item.get("last_price", 0) or 0)

                # Extract futures price from batch data
                futures_price = 0
                if futures_data and isinstance(futures_data, dict):
                    # Data is nested under NSE_FNO
                    nse_fno_data = futures_data.get("NSE_FNO", {})
                    futures_item = nse_fno_data.get(str(futures_security_id), {})
                    if futures_item and isinstance(futures_item, dict):
                        futures_price = float(futures_item.get("last_price", 0) or 0)

                # Calculate arbitrage metrics if both prices are valid
                if stock_price > 0 and futures_price > 0:
                    diff = abs(stock_price - futures_price)
                    diff_percent = (diff / stock_price) * 100

                    action = "No Action"
                    if stock_price > futures_price:
                        action = "Short Stock, Long Futures"
                    elif futures_price > stock_price:
                        action = "Long Stock, Short Futures"

                    arbitrage_data.append(
                        {
                            "symbol": symbol,
                            "stock_price": stock_price,
                            "futures_price": futures_price,
                            "difference": diff,
                            "difference_percent": diff_percent,
                            "action": action,
                            "expiry_date": nearest_expiry,
                            "futures_display_name": futures_info.get("display_name", ""),
                        }
                    )
                else:
                    print(f"Skipping {symbol}: Invalid prices (Stock: {stock_price}, Futures: {futures_price})")

            except Exception as e:
                print(f"Error processing {symbol}: {e}")
                continue

        # Sort by difference percentage (highest first)
        arbitrage_data.sort(key=lambda x: x["difference_percent"], reverse=True)

        print(f"Found {len(arbitrage_data)} futures arbitrage opportunities")
        return arbitrage_data

    except Exception as e:
        print(f"Error in get_futures_arbitrage_data_from_dhan: {e}")
        import traceback

        traceback.print_exc()
        return []