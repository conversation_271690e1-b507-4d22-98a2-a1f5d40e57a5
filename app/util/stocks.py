import json

import requests
from cachetools import <PERSON>TL<PERSON><PERSON>, cached
from fastapi import HTT<PERSON><PERSON>x<PERSON>

from app.util.db.redis import get_cache
from app.util.settings import app_settings

# Constants for Redis namespaces and keys.
HISTORICAL_NAMESPACE = "stocks"  # Each symbol's historical data is stored with key = symbol.
TECHNICAL_NAMESPACE = "technical"
TECHNICAL_GLOBAL_KEY = "all"  # Global key for technical analysis.
LIVE_ANALYSIS_NAMESPACE = "LIVE_ANALYSIS"  # Namespace for live analysis data.
LIVE_ANALYSIS_KEY = "data"
BSE_NAMESPACE = "BSE"
BSE_GLOBAL_KEY = "all"
SCREENER_NAMESPACE = "SCREENER"
FUTURES_NAMESPACE = "futures"  # Futures data per symbol.
FUTURES_GLOBAL_KEY = "all"  # Global key for futures data.
FUTURES_CACHE_TTL = 3600 * 24


def make_api_request(url: str):
    """
    Make an API request.

    Args:
        url: The URL to request

    Returns:
        The JSON response

    Raises:
        HTTPException: If the request fails.
    """
    try:
        response = requests.get(url, timeout=10)  # Add timeout
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print("Error fetching data", e)
        raise HTTPException(status_code=500, detail="Error fetching data for url: " + url)


# ---------------------------
# Technical Analysis Data
# ---------------------------
def get_all_stocks_technical_analysis_from_cache():
    """
    Retrieve the stocks technical analysis data from Redis cache.
    """
    try:
        global_tech = get_cache(TECHNICAL_NAMESPACE, TECHNICAL_GLOBAL_KEY)
        if global_tech is None:
            raise HTTPException(status_code=404, detail="No technical analysis data found in cache.")
        return global_tech
    except Exception as e:
        raise HTTPException(status_code=500, detail="Error fetching technical analysis from cache: " + str(e))


@cached(TTLCache(maxsize=1, ttl=3600))
def get_all_stocks_technical_analysis():
    """
    Fetch the stocks technical analysis data from the remote API.
    (Deprecated: use get_all_stocks_technical_analysis_from_cache instead when available.)
    """
    try:
        return make_api_request(app_settings.AIBULL_TECHNICAL_ALL_URL)
    except Exception:
        raise HTTPException(status_code=500, detail="Error fetching technical analysis data")


# ---------------------------
# Live Analysis Data
# ---------------------------
def get_all_stocks_live_analysis_from_cache():
    """
    Retrieve the stocks live analysis data from Redis cache.
    """
    try:
        live_data = get_cache(LIVE_ANALYSIS_NAMESPACE, LIVE_ANALYSIS_KEY)
        if live_data is None:
            raise HTTPException(status_code=404, detail="No live analysis data found in cache.")
        return live_data
    except Exception as e:
        raise HTTPException(status_code=500, detail="Error fetching live analysis from cache: " + str(e))


@cached(TTLCache(maxsize=1, ttl=3600))
def get_all_stocks_live_analysis():
    """
    Fetch the stocks live analysis data from the remote API.
    (Deprecated: use get_all_stocks_live_analysis_from_cache instead when available.)
    """
    try:
        return make_api_request(app_settings.AIBULL_LIVE_ANALYSIS_URL)
    except Exception:
        raise HTTPException(status_code=500, detail="Error fetching live analysis data")


# ---------------------------
# Historical Stocks Data
# ---------------------------
@cached(TTLCache(maxsize=1, ttl=3600))
def get_all_stocks():
    """
    Fetch the stocks historical/technical data from Redis.
    """
    try:
        stocks = get_cache(HISTORICAL_NAMESPACE, TECHNICAL_GLOBAL_KEY)
        if stocks is None:
            return {}
        return stocks
    except Exception as e:
        print("Error fetching stocks", e)
        return {}


# ---------------------------
# Most Active Stocks Data
# ---------------------------
@cached(TTLCache(maxsize=1, ttl=3600))
def get_most_active():
    """
    Fetch the most active stocks data from the remote API.
    """
    try:
        return make_api_request(app_settings.AIBULL_MOST_ACTIVE_URL)
    except Exception:
        return {}


NSE_MOST_ACTIVE_KEY = "NSE_MOST_ACTIVE"


@cached(TTLCache(maxsize=1, ttl=300))
def get_most_active_from_cache(type_param: str = "securities", sort_param: str = "value"):
    """
    Retrieve the most active stocks data from Redis cache.
    The cache key is constructed using the type and sort parameters.
    """
    cache_key = f"most-active-{type_param}-{sort_param}"
    try:
        # Use NSE_MOST_ACTIVE_KEY as the namespace.
        most_active = get_cache(NSE_MOST_ACTIVE_KEY, cache_key)
        if most_active is None:
            raise HTTPException(status_code=404, detail="No most active stocks data found in cache.")
        # If cached data is a JSON string, deserialize it.
        return json.loads(most_active) if isinstance(most_active, str) else most_active
    except Exception as e:
        raise HTTPException(status_code=500, detail="Error fetching most active stocks data from cache: " + str(e))


# ---------------------------
# Futures Data
# ---------------------------
def get_all_futures_from_cache():
    """
    Retrieve the futures data from Redis cache and process it by converting
    all price values to floats (if possible) to avoid type issues.
    """
    try:
        global_futures = get_cache(FUTURES_NAMESPACE, FUTURES_GLOBAL_KEY)
        if global_futures is None:
            raise HTTPException(status_code=404, detail="No cached futures data found.")
        processed_data = {}
        for symbol, fut_data in global_futures.items():
            if isinstance(fut_data, dict):
                converted = {}
                for expiry, price in fut_data.items():
                    try:
                        converted[expiry] = float(price)
                    except (ValueError, TypeError):
                        converted[expiry] = price
                processed_data[symbol] = converted
            else:
                processed_data[symbol] = fut_data
        return processed_data
    except Exception as e:
        raise HTTPException(status_code=500, detail="Error fetching futures data from cache: " + str(e))


def get_all_futures():
    """
    Fetch the futures data from the remote API and process it by converting
    all price values to floats (if possible) to avoid type issues.
    (Deprecated: use get_all_futures_from_cache instead when available.)
    """
    try:
        raw_data = make_api_request(app_settings.AIBULL_FUTURES_ALL_URL)
        processed_data = {}
        for symbol, fut_data in raw_data.items():
            if isinstance(fut_data, dict):
                converted = {}
                for expiry, price in fut_data.items():
                    try:
                        converted[expiry] = float(price)
                    except (ValueError, TypeError):
                        converted[expiry] = price
                processed_data[symbol] = converted
            else:
                processed_data[symbol] = fut_data
        return processed_data
    except Exception:
        raise HTTPException(status_code=500, detail="Error fetching futures data")


# ---------------------------
# BSE Data
# ---------------------------
@cached(TTLCache(maxsize=1, ttl=3600))
def get_bse():
    """
    Retrieves the BSE data from Redis using the composite key 'BSE:all'.
    """
    try:
        bse = get_cache(BSE_NAMESPACE, BSE_GLOBAL_KEY)
        if bse is None:
            return {}
        return bse
    except Exception as e:
        print("Error fetching bse", e)
        return {}


# ---------------------------
# Stock Screener Details
# ---------------------------
@cached(TTLCache(maxsize=1024, ttl=86400))
def get_stock_details(symbol: str):
    """
    Retrieve individual screener data for the given symbol directly from Redis.

    This function builds the Redis key using the SCREENER namespace and fetches
    the value. The result is then cached in memory for 24 hours.

    Args:
        symbol (str): The stock symbol for which to fetch screener data.

    Returns:
        dict: The screener data for the symbol.

    Raises:
        HTTPException: If no data is found for the symbol or if an error occurs.
    """
    try:
        details = get_cache(SCREENER_NAMESPACE, symbol)
        if details is None:
            raise HTTPException(status_code=404, detail="No screener data found for symbol: " + symbol)
        return details
    except Exception as e:
        raise HTTPException(status_code=500, detail="Error fetching screener data for symbol: " + symbol + ". " + str(e))


# ---------------------------
# Options Data
# ---------------------------
def get_options_data(symbol: str):
    """
    Get current options data for a given symbol.
    """
    try:
        return make_api_request(f"{app_settings.AIBULL_SYMBOL_OPTIONS_URL}/{symbol}")
    except Exception:
        return {}


# ---------------------------
# Refresh Function
# ---------------------------
def refresh():
    """
    Refresh all data caches by clearing cached data and re-fetching fresh data.

    Note: Screener data is now fetched individually from Redis.
    """
    try:
        # Clear in-memory caches.
        for func in [
            get_all_stocks_technical_analysis,
            get_all_stocks_live_analysis,
            get_all_stocks,
            get_most_active,
            get_all_futures,
            get_bse,
            get_stock_details,
        ]:
            if hasattr(func, "cache"):
                func.cache.clear()

        print("Loading technical analysis from cache...")
        stocks_tech = get_all_stocks_technical_analysis_from_cache()

        print("Loading live analysis from cache...")
        stocks_live = get_all_stocks_live_analysis_from_cache()

        print("Loading stocks from cache...")
        stocks = get_all_stocks()

        print("Loading most active stocks from cache...")
        # most_active = get_most_active()

        most_active = get_most_active_from_cache()

        print("Loading futures from cache...")
        futures = get_all_futures_from_cache()

        print("Loading BSE data...")
        bse = get_bse()

        print("Completed refresh")
        return {
            "stocks_technical_analysis": stocks_tech,
            "stocks_live_analysis": stocks_live,
            "stocks": stocks,
            "most_active": most_active,
            "futures": futures,
            "bse": bse,
        }
    except Exception as e:
        print("Error refreshing data:", e)
        raise HTTPException(status_code=500, detail="Error refreshing data")


# ---------------------------
# Test get base
# ---------------------------
if __name__ == "__main__":
    # Uncomment individual tests as needed:
    # print(get_all_stocks_technical_analysis())
    # print(get_all_stocks_live_analysis())
    # print(get_all_stocks())
    # print(get_most_active())
    # print(get_all_futures())
    # print(refresh())
    # print(get_options_data("AAPL"))
    # print(get_bse())

    # Test screener data for a specific symbol (direct Redis lookup)
    print(get_stock_details("20MICRONS"))
