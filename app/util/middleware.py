from typing import Awaitable, Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


class USMarketMiddleware(BaseHTTPMiddleware):

    async def dispatch(self, request: Request, call_next: Callable[[Request], Awaitable[Response]]) -> Response:
        host = request.headers.get("host", "").lower()
        is_us = "us.theaibull.com" in host

        # if running locally, override via ?us-market=true
        if not is_us and host.startswith(("localhost", "127.0.0.1")):
            is_us = request.query_params.get("us-market") == "true"

        request.state.is_us_market = is_us
        return await call_next(request)
