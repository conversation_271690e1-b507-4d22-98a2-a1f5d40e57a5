<!DOCTYPE html>
{% if compare_charts == "true" %}
<script>
    // NAV comparison chart configuration for multiple funds
    const getNavCompareChartConfig = (funds, timeRange = '1m') => {
        // Filter funds with valid nav_history
        const validFunds = funds.filter(fund => fund.nav_history && fund.nav_history.length > 0);

        // If no valid funds, return an empty config
        if (!validFunds.length) {
            return {
                type: "line",
                data: { labels: [], datasets: [] },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: { enabled: false },
                        zoom: { pan: { enabled: false }, zoom: { enabled: false } }
                    },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    }
                }
            };
        }

        // Define distinct businessColors for up to 5 funds
        const businessColors = [
            { border: "#FFC300", background: "rgba(255, 195, 0, 0.1)" },    // golden yellow
            { border: "#FF5733", background: "rgba(255, 87, 51, 0.1)" },   // vibrant orange
            { border: "#33FF57", background: "rgba(51, 255, 87, 0.1)" },   // bright green
            { border: "#3357FF", background: "rgba(51, 87, 255, 0.1)" },   // vivid blue
            { border: "#FF33A1", background: "rgba(255, 51, 161, 0.1)" },  // hot pink
        ];

        // Get all unique dates across all funds
        const allDates = [...new Set(
            validFunds.flatMap(fund => fund.nav_history.map(d => d.date))
        )].sort((a, b) => new Date(a) - new Date(b));

        // Filter dates based on time range
        const filteredDates = filterHistory(
            allDates.map(date => ({ date })),
            timeRange
        ).map(d => d.date);

        // Create datasets for each fund
        const datasets = validFunds.map((fund, index) => {
            const navMap = new Map(fund.nav_history.map(d => [d.date, parseFloat(d.nav)]));
            const data = filteredDates.map(date => navMap.get(date) || null); // Use null for missing dates
            return {
                label: fund.scheme_name || `Fund ${index + 1}`,
                data,
                borderColor: businessColors[index % businessColors.length].border,
                backgroundColor: businessColors[index % businessColors.length].background,
                borderWidth: 2,
                fill: true,
                pointBackgroundColor: businessColors[index % businessColors.length].border,
                pointBorderColor: "#ffffff",
                pointRadius: 0,
                pointHoverRadius: 6,
                pointHoverBackgroundColor: businessColors[index % businessColors.length].border,
                pointHoverBorderColor: "#ffffff",
                tension: 0.1
            };
        });

        return {
            type: "line",
            data: {
                labels: filteredDates,
                datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: "top",
                        labels: { font: { size: 12 } }
                    },
                    tooltip: {
                        backgroundColor: "rgba(0, 0, 0, 0.7)",
                        padding: 10,
                        titleFont: { size: 14 },
                        bodyFont: { size: 14 },
                        displayColors: true, // Show dataset colors in tooltip
                        mode: 'index', // Use 'index' mode to group tooltips by x-axis value
                        intersect: false, // Ensure tooltip appears even if not directly over a point
                        callbacks: {
                            title: function (tooltipItems) {
                                // Display the date as the tooltip title
                                return tooltipItems[0].label;
                            },
                            label: function (context) {
                                // Customize the label for each dataset
                                const value = context.parsed.y;
                                return `${context.dataset.label}: ₹${value ? value.toFixed(2) : 'N/A'}`;
                            },
                            footer: function (tooltipItems) {
                                // Optional: Add a footer if needed
                                return '';
                            }
                        }
                    },
                    zoom: {
                        pan: { enabled: true, mode: "x", modifierKey: "shift" },
                        zoom: {
                            wheel: { enabled: true },
                            pinch: { enabled: true },
                            mode: "x",
                            drag: {
                                enabled: true,
                                backgroundColor: "rgba(59, 130, 246, 0.3)",
                                borderColor: "rgba(59, 130, 246, 0.5)",
                                borderWidth: 1
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: { display: true, text: "Date", font: { size: 12, weight: "bold" } },
                        ticks: { maxRotation: 45, minRotation: 45, font: { size: 10 } },
                        grid: { display: false }
                    },
                    y: {
                        title: { display: true, text: "NAV (₹)", font: { size: 12, weight: "bold" } },
                        beginAtZero: false,
                        ticks: { font: { size: 10 }, callback: value => "₹" + value }
                    }
                },
                hover: { mode: 'nearest', intersect: false }
            }
        };
    };

    const deriveCompareSectorAllocation = (holdings) => {
        const sectorMap = new Map();
        holdings.forEach(holding => {
            const sectorName = holding.sector_name || 'Unknown';
            const allocation = parseFloat(holding.corpus_per) || 0;
            if (sectorMap.has(sectorName)) {
                sectorMap.set(sectorName, sectorMap.get(sectorName) + allocation);
            } else {
                sectorMap.set(sectorName, allocation);
            }
        });
        return Array.from(sectorMap, ([name, allocation]) => ({
            name,
            allocation
        }));
    };

    // Pie chart configuration for individual fund sector allocations
    const getSectorPieChartConfig = (fund) => {
        // Use sector_allocation if available, otherwise derive from holdings
        const sectorAllocation = deriveCompareSectorAllocation(fund.holdings || []);

        // Sort sectors by allocation
        const sortedSectors = sectorAllocation
            .sort((a, b) => parseFloat(b.allocation) - parseFloat(a.allocation));

        // Define colors for pie segments (business-friendly palette)
        const businessColors = [
            '#FFC300', // Golden Yellow
            '#FF5733', // Vibrant Orange
            '#33FF57', // Bright Green
            '#3357FF', // Vivid Blue
            '#FF33A1', // Hot Pink
            '#FBBF24', // Amber
            '#10B981', // Emerald
            '#8B5CF6', // Purple
            '#EC4899', // Pink
            '#6EE7B7', // Light Green
        ];

        return {
            type: 'doughnut',
            data: {
                labels: sortedSectors.map(s => `${s.name} (${parseFloat(s.allocation).toFixed(2)}%)`),
                datasets: [{
                    data: sortedSectors.map(s => parseFloat(s.allocation)),
                    backgroundColor: businessColors.slice(0, sortedSectors.length),
                    borderColor: '#ffffff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: { size: 10 },
                            boxWidth: 12,
                            padding: 8
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        padding: 10,
                        titleFont: { size: 12 },
                        bodyFont: { size: 12 },
                        callbacks: {
                            label: context => `${context.label}: ${context.parsed.toFixed(2)}%`
                        }
                    }
                },
                layout: {
                    padding: 10
                }
            }
        };
    };

    // Pie chart configuration for overall sector allocations across all funds
    const getOverallSectorPieChartConfig = (funds) => {
        // Aggregate sector allocations across all funds
        const sectorMap = new Map();
        funds.forEach(fund => {
            // Use sector_allocation if available, otherwise derive from holdings
            const sectorAllocation = deriveCompareSectorAllocation(fund.holdings || []);

            sectorAllocation.forEach(sector => {
                const sectorName = sector.name;
                const allocation = parseFloat(sector.allocation);
                if (sectorMap.has(sectorName)) {
                    sectorMap.set(sectorName, sectorMap.get(sectorName) + allocation);
                } else {
                    sectorMap.set(sectorName, allocation);
                }
            });
        });

        // Convert to array and sort by allocation
        const aggregatedSectors = Array.from(sectorMap, ([name, allocation]) => ({
            name,
            allocation
        }))
            .sort((a, b) => b.allocation - a.allocation);

        // Normalize allocations to sum to 100%
        const totalAllocation = aggregatedSectors.reduce((sum, s) => sum + s.allocation, 0);
        const normalizedSectors = aggregatedSectors.map(s => ({
            name: s.name,
            allocation: totalAllocation > 0 ? (s.allocation / totalAllocation) * 100 : s.allocation
        }));

        // Define colors for pie segments
        const businessColors = [
            '#FFC300', // Golden Yellow
            '#FF5733', // Vibrant Orange
            '#33FF57', // Bright Green
            '#3357FF', // Vivid Blue
            '#FF33A1', // Hot Pink
            '#FBBF24', // Amber
            '#10B981', // Emerald
            '#8B5CF6', // Purple
            '#EC4899', // Pink
            '#6EE7B7', // Light Green
        ];

        return {
            type: 'doughnut',
            data: {
                labels: normalizedSectors.map(s => `${s.name} (${parseFloat(s.allocation).toFixed(2)}%)`),
                datasets: [{
                    data: normalizedSectors.map(s => parseFloat(s.allocation)),
                    backgroundColor: businessColors.slice(0, normalizedSectors.length),
                    borderColor: '#ffffff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: { size: 12 },
                            boxWidth: 14,
                            padding: 10
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        padding: 10,
                        titleFont: { size: 14 },
                        bodyFont: { size: 14 },
                        callbacks: {
                            label: context => `${context.label}: ${context.parsed.toFixed(2)}%`
                        }
                    }
                },
                layout: {
                    padding: 10
                }
            }
        };
    };
</script>

{% else %}

<script>
    // Peer comparison chart configuration
    const getPeerComparisonChartConfig = (fundData, peers) => {
        const fundNames = [fundData.scheme_name, ...peers.map(p => p.scheme_name)];
        const return1yData = [parseFloat(fundData.return_stats[0].return1y) || 0, ...peers.map(p => parseFloat(p.return1y) || 0)];
        const return3yData = [parseFloat(fundData.return_stats[0].return3y) || 0, ...peers.map(p => parseFloat(p.return3y) || 0)];
        const return5yData = [parseFloat(fundData.return_stats[0].return5y) || 0, ...peers.map(p => parseFloat(p.return5y) || 0)];

        return {
            type: "bar",
            data: {
                labels: fundNames,
                datasets: [
                    {
                        label: "1Y Return (%)",
                        data: return1yData,
                        backgroundColor: "rgba(59, 130, 246, 0.6)",
                        borderColor: "#3B82F6",
                        borderWidth: 1
                    },
                    {
                        label: "3Y Return (%)",
                        data: return3yData,
                        backgroundColor: "rgba(16, 185, 129, 0.6)",
                        borderColor: "#10B981",
                        borderWidth: 1
                    },
                    {
                        label: "5Y Return (%)",
                        data: return5yData,
                        backgroundColor: "rgba(245, 158, 11, 0.6)",
                        borderColor: "#F59E0B",
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: "top", labels: { font: { size: 12 } } },
                    tooltip: { callbacks: { label: context => `${context.dataset.label}: ${context.parsed.y.toFixed(2)}%` } }
                },
                scales: {
                    x: { title: { display: true, text: "Fund Name", font: { size: 12, weight: "bold" } }, ticks: { font: { size: 10 }, maxRotation: 45, minRotation: 45 } },
                    y: { title: { display: true, text: "Returns (%)", font: { size: 12, weight: "bold" } }, beginAtZero: true, ticks: { font: { size: 10 }, callback: value => value + "%" } }
                }
            }
        };
    };

    // Expense ratio chart configuration (peer comparison)
    const getExpenseRatioChartConfig = (fundData, peers) => ({
        type: "bar",
        data: {
            labels: [fundData.scheme_name, ...peers.map(p => p.scheme_name)],
            datasets: [{
                label: "Expense Ratio (%)",
                data: [parseFloat(fundData.expense_ratio), ...peers.map(p => parseFloat(p.expense_ratio) || 0)],
                backgroundColor: ["#3B82F6", ...peers.map(() => "#A7F3D0")],
                borderColor: ["#2563EB", ...peers.map(() => "#6EE7B7")],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                tooltip: { callbacks: { label: context => `${context.dataset.label}: ${context.parsed.y.toFixed(2)}%` } }
            },
            scales: {
                x: { title: { display: true, text: "Fund Name", font: { size: 12, weight: "bold" } }, ticks: { font: { size: 10 }, maxRotation: 45, minRotation: 45 } },
                y: { title: { display: true, text: "Expense Ratio (%)", font: { size: 12, weight: "bold" } }, beginAtZero: true, ticks: { font: { size: 10 }, callback: value => value + "%" }, suggestedMax: 2 }
            }
        }
    });

    // Sector chart configuration
    const getSectorChartConfig = (sectorAllocation) => {
        sectorAllocation.sort((a, b) => b.allocation - a.allocation);
        return {
            type: "doughnut",
            data: {
                labels: sectorAllocation.map(s => `${s.name} (${parseFloat(s.allocation).toFixed(2)}%)`),
                datasets: [{
                    data: sectorAllocation.map(s => parseFloat(s.allocation)),
                    backgroundColor: ["#3B82F6", "#EF4444", "#FBBF24", "#10B981", "#8B5CF6", "#EC4899", "#22C55E", "#F59E0B", "#6EE7B7", "#93C5FD"],
                    borderColor: "#ffffff",
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: "right", labels: { font: { size: 12 } } },
                    tooltip: { callbacks: { label: context => `${context.label}: ${context.parsed.toFixed(2)}%` } }
                }
            }
        };
    };

    // NAV chart configuration
    const getNavChartConfig = (navHistory) => ({
        type: "line",
        data: {
            labels: navHistory.map(d => d.date),
            datasets: [{
                label: "NAV (₹)",
                data: navHistory.map(d => parseFloat(d.nav)),
                borderColor: "#3B82F6",
                backgroundColor: "rgba(59, 130, 246, 0.1)",
                borderWidth: 2,
                fill: true,
                pointBackgroundColor: "#3B82F6",
                pointBorderColor: "#ffffff",
                pointRadius: 0,
                pointHoverRadius: 6,
                pointHoverBackgroundColor: "#3B82F6",
                pointHoverBorderColor: "#ffffff",
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: true, position: "top", labels: { font: { size: 12 } } },
                tooltip: {
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    padding: 10,
                    titleFont: { size: 14 },
                    bodyFont: { size: 14 },
                    displayColors: false,
                    mode: 'nearest',
                    intersect: false,
                    callbacks: {
                        label: function (context) {
                            return `NAV: ₹${context.parsed.y.toFixed(2)}`;
                        },
                        title: function (context) {
                            return context[0].label;
                        }
                    }
                },
                zoom: {
                    pan: { enabled: true, mode: "x", modifierKey: "shift" },
                    zoom: {
                        wheel: { enabled: true },
                        pinch: { enabled: true },
                        mode: "x",
                        drag: {
                            enabled: true,
                            backgroundColor: "rgba(59, 130, 246, 0.3)",
                            borderColor: "rgba(59, 130, 246, 0.5)",
                            borderWidth: 1
                        }
                    }
                }
            },
            scales: {
                x: { title: { display: true, text: "Date", font: { size: 12, weight: "bold" } }, ticks: { maxRotation: 45, minRotation: 45, font: { size: 10 } }, grid: { display: false } },
                y: { title: { display: true, text: "NAV", font: { size: 12, weight: "bold" } }, beginAtZero: false, ticks: { font: { size: 10 }, callback: value => "₹" + value } }
            },
            hover: { mode: 'nearest', intersect: false }
        }
    });

    // Expense history chart configuration
    const getExpenseChartConfig = (expenseHistory) => ({
        type: "line",
        data: {
            labels: expenseHistory.map(d => d.as_on_date),
            datasets: [{
                label: "Expense Ratio (%)",
                data: expenseHistory.map(d => parseFloat(d.expense_ratio)),
                borderColor: "#8B5CF6",
                backgroundColor: "rgba(139, 92, 246, 0.1)",
                borderWidth: 2,
                fill: true,
                pointBackgroundColor: "#8B5CF6",
                pointBorderColor: "#ffffff",
                pointRadius: 0,
                pointHoverRadius: 6,
                pointHoverBackgroundColor: "#8B5CF6",
                pointHoverBorderColor: "#ffffff",
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: true, position: "top", labels: { font: { size: 12 } } },
                tooltip: {
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    padding: 10,
                    titleFont: { size: 14 },
                    bodyFont: { size: 14 },
                    displayColors: false,
                    mode: 'nearest',
                    intersect: false,
                    callbacks: {
                        label: function (context) {
                            return `Expense Ratio: ${context.parsed.y.toFixed(2)}%`;
                        },
                        title: function (context) {
                            const date = new Date(context[0].label);
                            return date.toLocaleDateString('en-IN', {
                                year: 'numeric',
                                month: 'long'
                            });
                        }
                    }
                },
                zoom: {
                    pan: { enabled: true, mode: "x", modifierKey: "shift" },
                    zoom: {
                        wheel: { enabled: true },
                        pinch: { enabled: true },
                        mode: "x",
                        drag: {
                            enabled: true,
                            backgroundColor: "rgba(239, 68, 68, 0.3)",
                            borderColor: "rgba(239, 68, 68, 0.5)",
                            borderWidth: 1
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: { display: true, text: "Month", font: { size: 12, weight: "bold" } },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 45,
                        font: { size: 10 },
                        callback: function (value, index, values) {
                            const date = new Date(this.getLabelForValue(value));
                            return date.toLocaleDateString('en-IN', { year: 'numeric', month: 'short' });
                        }
                    },
                    grid: { display: false }
                },
                y: { title: { display: true, text: "Expense Ratio (%)", font: { size: 12, weight: "bold" } }, beginAtZero: true, ticks: { font: { size: 10 }, callback: value => value + "%" }, suggestedMax: 2 }
            },
            hover: { mode: 'nearest', intersect: false }
        }
    });
</script>
{% endif %}
<script>

    // Setup zoom controls for NAV/Expense chart
    const setupZoomControls = (chart) => {
        document.getElementById("reset-zoom-btn").onclick = () => chart.resetZoom();
        document.getElementById("zoom-in-btn").onclick = () => chart.zoom(1.1);
        document.getElementById("zoom-out-btn").onclick = () => chart.zoom(0.9);
    };

    // Toggle time filter buttons visibility
    const toggleTimeFilterButtons = (show) => {
        const filterButtons = document.getElementById('time-filter-buttons');
        filterButtons.style.display = show ? 'flex' : 'none';
    };

    // Filter history for chart options (NAV chart and Expense Ratio chart)
    const filterHistory = (history, range) => {
        if (range === 'all') return history;
        const now = new Date();
        let cutoffDate;
        switch (range) {
            case '1m': cutoffDate = new Date(now.setMonth(now.getMonth() - 1)); break;
            case '6m': cutoffDate = new Date(now.setMonth(now.getMonth() - 6)); break;
            case '1y': cutoffDate = new Date(now.setFullYear(now.getFullYear() - 1)); break;
            case '3y': cutoffDate = new Date(now.setFullYear(now.getFullYear() - 3)); break;
            case '5y': cutoffDate = new Date(now.setFullYear(now.getFullYear() - 5)); break;
            default: return history;
        }
        return history.filter(d => new Date(d.date) >= cutoffDate);
    };

    // Update active filter button styling
    const updateFilterButtons = (activeButton) => {
        document.querySelectorAll('.time-filter').forEach(btn => {
            btn.classList.remove('bg-blue-600', 'text-white');
            btn.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
        });
        activeButton?.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
        activeButton?.classList.add('bg-blue-600', 'text-white');
    };

    // Update NAV chart
    const updateNavChart = (history, chart, type) => {
        if (type === 'nav') {
            chart.data.labels = history.map(d => d.date);
            chart.data.datasets[0].data = history.map(d => parseFloat(d.nav));
        } else {
            chart.data.labels = history.map(d => d.as_on_date);
            chart.data.datasets[0].data = history.map(d => parseFloat(d.expense_ratio));
        }
        chart.resetZoom();
        chart.update();
    };
</script>