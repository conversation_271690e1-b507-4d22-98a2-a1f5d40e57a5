<!-- FAQ Section -->
<div id="faqs" class="mt-8 text-sm rounded-2xl px-2 transition-all duration-300 scroll-mt-64">
    <h4
        class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b pb-2 border-gray-200 dark:border-gray-700">
        Frequently Asked Questions
    </h4>
    <div class="space-y-4">
        <details class="border rounded-lg bg-white dark:bg-gray-700">
            <summary class="cursor-pointer p-4 text-gray-900 dark:text-gray-100 font-medium">How
                is the overall rating
                calculated?</summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    The overall rating for {{ fund_data.scheme_name | default('this mutual
                    fund') }} is an aggregate
                    score based on performance metrics (returns over 1, 3, and 5 years),
                    risk-adjusted returns (Sharpe
                    and Sortino ratios), and consistency relative to its category. Ratings are
                    on a scale of 1 to 5,
                    with 5 being the highest.
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white dark:bg-gray-700">
            <summary class="cursor-pointer p-4 text-gray-900 dark:text-gray-100 font-medium">
                What are the top stocks of {{ fund_data.scheme_name | default('this mutual
                fund') }}?
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    {% if fund_data.holdings %}
                    The top stocks held by {{ fund_data.scheme_name | default('this mutual
                    fund') }} include {{
                    fund_data.holdings[:3] | map(attribute='company_name') | join(', ') |
                    default('N/A') }}.
                    {% else %}
                    No holding information is available for {{ fund_data.scheme_name |
                    default('this mutual fund') }}.
                    {% endif %}
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white dark:bg-gray-700">
            <summary class="cursor-pointer p-4 text-gray-900 dark:text-gray-100 font-medium">
                Which industries does {{ fund_data.scheme_name | default('this mutual fund') }}
                invest in?
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    {% if fund_data.holdings %}
                    {{ fund_data.scheme_name | default('This mutual fund') }} primarily invests
                    in the following
                    industries: {{ sector_allocation[:3] | map(attribute='name') | join(', ') |
                    default('N/A') }}.
                    {% else %}
                    No industry allocation information is available for {{ fund_data.scheme_name
                    | default('this mutual
                    fund') }}.
                    {% endif %}
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white dark:bg-gray-700">
            <summary class="cursor-pointer p-4 text-gray-900 dark:text-gray-100 font-medium">
                {% if fund_data.fund_manager_details | length > 1 %}
                Who are the fund managers of {{ fund_data.scheme_name | default('this mutual
                fund') }}?
                {% else %}
                Who is the fund manager of {{ fund_data.scheme_name | default('this mutual
                fund') }}?
                {% endif %}
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    {% if fund_data.fund_manager_details %}
                    The fund manager{% if fund_data.fund_manager_details | length > 1 %}s{%
                    endif %} of {{
                    fund_data.scheme_name | default('this mutual fund') }} {% if
                    fund_data.fund_manager_details | length
                    > 1 %}are{% else %}is{% endif %} {{ fund_data.fund_manager_details |
                    map(attribute='person_name') |
                    join(' and ') | default('N/A') }}.
                    {% else %}
                    No fund manager information is available for {{ fund_data.scheme_name |
                    default('this mutual fund')
                    }}.
                    {% endif %}
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white dark:bg-gray-700">
            <summary class="cursor-pointer p-4 text-gray-900 dark:text-gray-100 font-medium">
                How many years has the fund manager been managing {{ fund_data.scheme_name |
                default('this mutual fund')
                }}?
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6" id="faq-manager-tenure">
                    {% if fund_data.fund_manager_details %}
                    {% for manager in fund_data.fund_manager_details %}
                    <span class="manager-tenure" data-name="{{ manager.person_name }}"
                        data-start-date="{{ manager.date_from | default('N/A') }}">{{
                        manager.person_name }}:
                        Calculating...</span>
                    {% if not loop.last %}<br>{% endif %}
                    {% endfor %}
                    {% else %}
                    No tenure information is available for the fund manager(s) of {{
                    fund_data.scheme_name |
                    default('this mutual fund') }}.
                    {% endif %}
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white dark:bg-gray-700">
            <summary class="cursor-pointer p-4 text-gray-900 dark:text-gray-100 font-medium">
                What is the minimum investment for {{ fund_data.scheme_name | default('this
                mutual fund') }}?
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    The minimum investment for {{ fund_data.scheme_name | default('this mutual
                    fund') }} is ₹{{
                    fund_data.min_investment_amount | default('N/A') }} for lumpsum investments
                    and ₹{{
                    fund_data.min_sip_investment | default('N/A') }} for SIPs.
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white dark:bg-gray-700">
            <summary class="cursor-pointer p-4 text-gray-900 dark:text-gray-100 font-medium">
                What are the average 1-year returns for {{ fund_data.scheme_name | default('this
                mutual fund') }}?
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6" id="faq-1y-return"
                    data-launch-date="{{ fund_data.launch_date | default('N/A') }}"
                    data-return-1y="{% if fund_data.stats and fund_data.stats[0] %}{{ fund_data.stats[0].stat_1y }}{% else %}N/A{% endif %}">
                    Calculating...
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white dark:bg-gray-700">
            <summary class="cursor-pointer p-4 text-gray-900 dark:text-gray-100 font-medium">
                What are the average 3-year returns for {{ fund_data.scheme_name | default('this
                mutual fund') }}?
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6" id="faq-3y-return"
                    data-launch-date="{{ fund_data.launch_date | default('N/A') }}"
                    data-return-3y="{% if fund_data.stats and fund_data.stats[0] %}{{ fund_data.stats[0].stat_3y }}{% else %}N/A{% endif %}">
                    Calculating...
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white dark:bg-gray-700">
            <summary class="p-4 text-gray-900 dark:text-gray-100 cursor-pointer font-medium">
                What is the expense ratio of {{ fund_data.scheme_name | default('this mutual fund') }}, and how does it
                compare to its category?
            </summary>
            <div class="p-4 border-t">
                <p class="leading-6 text-gray-700 dark:text-gray-300">
                    The expense ratio of {{ fund_data.scheme_name | default('this mutual fund') }} is {% if
                    fund_data.expense_ratio %}{{ fund_data.expense_ratio }}%{% else %}N/A{% endif %}.
                    {% if fund_data.peerComparison %}
                    The average expense ratio for similar funds in the {{ fund_data.category | default('category') }} is
                    <span id="faq-avg-expense-ratio"
                        data-peer-comparison='{{ fund_data.peerComparison | tojson }}'>Calculating...</span>.
                    {% else %}
                    No category comparison data is available.
                    {% endif %}
                </p>
            </div>
        </details>
    </div>
</div>
<script>
    // Calculate years of tenure from a start date
    const calculateTenure = (startDateText) => {
        if (!startDateText || startDateText === 'N/A') return 'N/A';
        const startDate = new Date(startDateText.trim());
        if (isNaN(startDate.getTime())) return 'N/A';

        const currentDate = new Date();
        const yearsDiff = currentDate.getFullYear() - startDate.getFullYear();

        // Adjust for partial years
        const monthDiff = currentDate.getMonth() - startDate.getMonth();
        const dayDiff = currentDate.getDate() - startDate.getDate();
        const adjustment = (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) ? 1 : 0;

        return yearsDiff - adjustment;
    };

    // Calculate fund age in years
    const calculateFundAge = (launchDateText) => {
        if (!launchDateText || launchDateText === 'N/A') return 0;
        const launchDate = new Date(launchDateText.trim());
        if (isNaN(launchDate.getTime())) return 0;

        const currentDate = new Date();
        const yearsDiff = currentDate.getFullYear() - launchDate.getFullYear();

        // Adjust for partial years
        const monthDiff = currentDate.getMonth() - launchDate.getMonth();
        const dayDiff = currentDate.getDate() - launchDate.getDate();
        const adjustment = (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) ? 1 : 0;

        return yearsDiff - adjustment;
    };

    // Update manager tenure in the DOM
    const updateManagerTenures = () => {
        document.querySelectorAll('.manager-tenure').forEach(el => {
            const name = el.dataset.name;
            const startDate = el.dataset.startDate;
            const tenure = calculateTenure(startDate);
            el.textContent = tenure !== 'N/A' ? `${name} has been managing the fund for ${tenure} years.` : `Tenure information is not available for ${name}.`;
        });
    };

    // Update return FAQs
    const updateReturnFAQs = () => {
        const createReturnText = (years, returnValue, fundAge) => {
            const returnFloat = parseFloat(returnValue);
            if (fundAge >= years && returnValue !== 'N/A' && !isNaN(returnFloat)) {
                return `The average ${years}-year return for ${fundData.scheme_name || 'this mutual fund'} is ${returnFloat.toFixed(2)}%.`;
            } else if (fundAge < years) {
                return `The fund has not been in existence for ${years === 1 ? 'a full year' : `${years} years`}, so ${years}-year returns are not available.`;
            } else {
                return `No ${years}-year return data is available for ${fundData.scheme_name || 'this mutual fund'}.`;
            }
        };

        const updateReturnFAQ = (elementId, years, returnValue) => {
            const el = document.getElementById(elementId);
            if (el) {
                const fundAge = calculateFundAge(fundData.launch_date);
                el.textContent = createReturnText(years, returnValue, fundAge);
            }
        };

        updateReturnFAQ('faq-1y-return', 1, fundData.sip_return.return1y);
        updateReturnFAQ('faq-3y-return', 3, fundData.sip_return.return3y);
    };

    // Calculate average expense ratio and update FAQ
    const updateExpenseRatioFAQ = () => {
        const el = document.getElementById('faq-avg-expense-ratio');
        if (el) {
            const peerComparison = JSON.parse(el.dataset.peerComparison || '[]');
            const expenseRatios = peerComparison
                .map(item => parseFloat(item.expense_ratio))
                .filter(val => !isNaN(val));
            const avgExpense = expenseRatios.length > 0
                ? (expenseRatios.reduce((sum, val) => sum + val, 0) / expenseRatios.length).toFixed(2)
                : 'N/A';
            el.textContent = avgExpense !== 'N/A' ? `${avgExpense}%` : 'not available';
        }
    };
</script>