<script>
    // Constants for configuration
    const SLIDER_CONFIGS = {
        nav: { start: [0, 5000], range: { min: 0, max: 5000 }, step: 10, format: { to: value => Math.round(value), from: value => Number(value) } },
        fundSize: { start: [0, 50000], range: { min: 0, max: 50000 }, step: 100, format: { to: value => Math.round(value), from: value => Number(value) } },
        expenseRatio: { start: [0, 3], range: { min: 0, max: 3 }, step: 0.01, format: { to: value => Number(value).toFixed(2), from: value => Number(value) } },
    };

    const getFilterConfigs = (fundHousesData, categoriesData) => [
        {
            toggleId: 'fund-house-toggle',
            dropdownId: 'fund-house-dropdown',
            displayId: 'fund-house-display',
            allId: 'fund-house-all',
            checkboxesContainerId: 'fund-house-checkboxes',
            clearId: 'clear-fund-house',
            data: fundHousesData.fund_houses
                ? Object.entries(fundHousesData.fund_houses).map(([name, details]) => ({
                    name,
                    fund_count: details.fund_count,
                }))
                : [],
            name: 'fund-house',
            displayAllText: 'All Fund Houses',
        },
        {
            toggleId: 'category-toggle',
            dropdownId: 'category-dropdown',
            displayId: 'category-display',
            allId: 'category-all',
            checkboxesContainerId: 'category-checkboxes',
            clearId: 'clear-category',
            data: categoriesData.map(cat => ({ name: cat.name, fund_count: cat.fund_count })),
            name: 'category',
            displayAllText: 'All Categories',
        },
        {
            toggleId: 'sub-category-toggle',
            dropdownId: 'sub-category-dropdown',
            displayId: 'sub-category-display',
            allId: 'sub-category-all',
            checkboxesContainerId: 'sub-category-checkboxes',
            clearId: 'clear-sub-category',
            data: [],
            name: 'sub-category',
            displayAllText: 'All Sub Categories',
        },
        {
            toggleId: 'exit-load-toggle',
            dropdownId: 'exit-load-dropdown',
            displayId: 'exit-load-display',
            allId: 'exit-load-all',
            checkboxesContainerId: 'exit-load-checkboxes',
            clearId: 'clear-exit-load',
            data: ['0%', '1% within 365 days', '1% within 1 year', '2% within 1 year'],
            name: 'exit-load',
            displayAllText: 'All Exit Loads',
        },
        {
            toggleId: 'crisil-rating-toggle',
            dropdownId: 'crisil-rating-dropdown',
            displayId: 'crisil-rating-display',
            allId: 'crisil-rating-all',
            checkboxesContainerId: 'crisil-rating-checkboxes',
            clearId: 'clear-crisil-rating',
            data: [1, 2, 3, 4, 5].map(rating => ({ name: `${rating} ☆`, value: rating })),
            name: 'crisil-rating',
            displayAllText: 'All Ratings',
        },
        {
            toggleId: 'risk-toggle',
            dropdownId: 'risk-dropdown',
            displayId: 'risk-display',
            allId: 'risk-all',
            checkboxesContainerId: 'risk-checkboxes',
            clearId: 'clear-risk',
            data: ['Low', 'Moderate', 'High', 'Very High'],
            name: 'risk',
            displayAllText: 'All Risk Levels',
        },
        {
            toggleId: 'plan-toggle',
            dropdownId: 'plan-dropdown',
            displayId: 'plan-display',
            allId: 'plan-all',
            checkboxesContainerId: 'plan-checkboxes',
            clearId: 'clear-plan',
            data: ['Direct', 'Regular'],
            name: 'plan',
            displayAllText: 'All Plans',
        },
    ];

    // FilterManager class to handle all filter-related logic
    class FilterManager {
        constructor(configs) {
            this.filters = configs.reduce((acc, config) => {
                acc[config.name] = this.initCheckboxFilter(config);
                return acc;
            }, {});
            this.filterCache = new Map();
            this.isLoading = false;
            this.paginationManager = new PaginationManager({ filterManager: this });
        }

        // Initialize checkbox filter
        initCheckboxFilter({ toggleId, dropdownId, displayId, allId, checkboxesContainerId, clearId, data, name, displayAllText }) {
            const toggle = document.getElementById(toggleId);
            const dropdown = document.getElementById(dropdownId);
            const display = document.getElementById(displayId);
            const allCheckbox = document.getElementById(allId);
            const checkboxesContainer = document.getElementById(checkboxesContainerId);
            const clearButton = document.getElementById(clearId);
            let currentData = data;

            toggle.setAttribute('aria-expanded', 'false');
            toggle.setAttribute('aria-controls', dropdownId);

            const debouncedCloseDropdown = debounce((e) => {
                if (!toggle.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.classList.add('hidden');
                    toggle.setAttribute('aria-expanded', 'false');
                }
            }, 100);

            document.addEventListener('click', debouncedCloseDropdown);

            toggle.addEventListener('click', () => {
                dropdown.classList.toggle('hidden');
                toggle.setAttribute('aria-expanded', !dropdown.classList.contains('hidden'));
            });

            const createCheckboxItem = (item) => `
                <li class="py-1">
                    <label class="flex items-center text-sm text-gray-700 dark:text-gray-300">
                        <input type="checkbox" name="${name}" value="${item.value || item.name}" class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded" disabled aria-label="${item.name}">
                        ${item.name}${item.fund_count ? ` (${item.fund_count})` : ''}
                    </label>
                </li>
            `;

            const loadOptions = async () => {
                try {
                    const items = currentData.map(item => (typeof item === 'object' && item.name ? item : { name: item, value: item }));
                    checkboxesContainer.innerHTML = items.map(createCheckboxItem).join('');
                    initCheckboxEvents();
                } catch (error) {
                    console.error(`Error loading ${name}s:`, error);
                }
            };

            const initCheckboxEvents = () => {
                const checkboxes = checkboxesContainer.querySelectorAll(`input[name="${name}"]`);

                allCheckbox.addEventListener('change', () => {
                    const isChecked = allCheckbox.checked;
                    checkboxes.forEach(cb => {
                        cb.checked = false;
                        cb.disabled = isChecked;
                    });
                    updateDisplay();
                    if (name === 'category') this.updateSubCategoryFilter();
                    this.applyFilters(1);
                });

                checkboxesContainer.addEventListener('change', (e) => {
                    if (e.target.matches(`input[name="${name}"]`)) {
                        if (!e.target.checked && allCheckbox.checked) {
                            allCheckbox.checked = false;
                        }
                        updateDisplay();
                        if (name === 'category') this.updateSubCategoryFilter();
                        this.applyFilters(1);
                    }
                });

                clearButton.addEventListener('click', () => {
                    checkboxes.forEach(cb => {
                        cb.checked = false;
                        cb.disabled = true;
                    });
                    allCheckbox.checked = true;
                    updateDisplay();
                    if (name === 'category') this.updateSubCategoryFilter();
                    this.applyFilters(1);
                });
            };

            const updateDisplay = () => {
                const checkboxes = checkboxesContainer.querySelectorAll(`input[name="${name}"]`);
                const selected = Array.from(checkboxes).filter(cb => cb.checked).map(cb => cb.value);
                display.textContent = allCheckbox.checked || selected.length === 0 ? displayAllText : selected.join(', ');
                clearButton.classList.toggle('hidden', allCheckbox.checked || selected.length === 0);
                this.updateAppliedFilters();
            };

            const getSelected = () => {
                const checkboxes = checkboxesContainer.querySelectorAll(`input[name="${name}"]`);
                const selected = Array.from(checkboxes).filter(cb => cb.checked).map(cb => cb.value);
                return allCheckbox.checked || selected.length === 0 ? '' : selected.join(',');
            };

            const updateData = (newData) => {
                if (name === 'sub-category') {
                    currentData = newData;
                    loadOptions();
                    updateDisplay();
                }
            };

            loadOptions();
            return { getSelected, updateData, updateDisplay };
        }

        // Update sub-category filter based on category selection
        updateSubCategoryFilter() {
            const subCategoryContainer = document.querySelector('.sub-category-container');
            const selectedCategories = this.filters['category'].getSelected();
            subCategoryContainer.classList.toggle('hidden', !selectedCategories);

            if (selectedCategories) {
                const selectedCategoryNames = selectedCategories.split(',');
                const subCategories = categoriesData
                    .filter(cat => selectedCategoryNames.includes(cat.name))
                    .flatMap(cat => cat.subcategories)
                    .filter((value, index, self) => self.indexOf(value) === index);
                this.filters['sub-category'].updateData(subCategories);
            } else {
                this.filters['sub-category'].updateData([]);
                document.getElementById('sub-category-all').checked = true;
                document.getElementById('sub-category-display').textContent = 'All Sub Categories';
                document.getElementById('clear-sub-category').classList.add('hidden');
            }
        }

        // Apply filters
        async applyFilters(presetFilters = {}, resetPage = true) {
            if (this.isLoading) return;
            this.isLoading = true;
            const start = performance.now();

            const navSlider = document.getElementById('nav-range-slider');
            const fundSizeSlider = document.getElementById('fund-size-range-slider');
            const expenseRatioSlider = document.getElementById('expense-ratio-range-slider');

            let minNav = 0, maxNav = 5000, minFundSize = 0, maxFundSize = 50000, minExpenseRatio = 0, maxExpenseRatio = 3;
            if (navSlider.noUiSlider && fundSizeSlider.noUiSlider && expenseRatioSlider.noUiSlider) {
                [minNav, maxNav] = navSlider.noUiSlider.get().map(Number);
                [minFundSize, maxFundSize] = fundSizeSlider.noUiSlider.get().map(Number);
                [minExpenseRatio, maxExpenseRatio] = expenseRatioSlider.noUiSlider.get().map(Number);
            }

            const filters = {
                amc: this.filters['fund-house'].getSelected(),
                category: this.filters['category'].getSelected() || presetFilters.category,
                sub_category: this.filters['sub-category'].getSelected() || presetFilters.sub_category,
                exit_load: this.filters['exit-load'].getSelected() || undefined,
                rating: this.filters['crisil-rating'].getSelected() || presetFilters.rating,
                risk: this.filters['risk'].getSelected() || undefined,
                plan_type: this.filters['plan'].getSelected() || undefined,
                min_nav: minNav > 0 ? minNav : undefined,
                max_nav: maxNav < 5000 ? maxNav : undefined,
                min_aum: minFundSize > 0 ? minFundSize : undefined,
                max_aum: maxFundSize < 50000 ? maxFundSize : undefined,
                min_expense_ratio: minExpenseRatio > 0 ? minExpenseRatio : undefined,
                max_expense_ratio: maxExpenseRatio < 3 ? maxExpenseRatio : undefined,
                return3y_min: additionalFilterControls.return3y || presetFilters.return3y_min,
                return1y_min: additionalFilterControls.return1y || presetFilters.return1y_min,
                sort_key: sortState.key || presetFilters.sort_key || undefined,
                sort_direction: sortState.direction || presetFilters.sort_direction || 'asc',
                columns: ['logo_url', 'scheme_name', 'category', 'sub_category', 'nav', 'aum', 'rating', 'scheme_code', 'return_stats'],
                page: resetPage ? 1 : presetFilters.page || this.paginationManager.currentPage,
                limit: this.paginationManager.perPage
            };

            ELEMENTS.resultsBody.innerHTML = `
                <tr>
                    <td colspan="6" class="p-8 text-center text-gray-500 dark:text-gray-400">
                        <svg class="animate-spin h-8 w-8 mx-auto text-blue-500" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Loading...
                    </td>
                </tr>
            `;
            try {
                const data = await fetchData(`${BASE_URL}/search`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(filters),
                });
                allFunds = data.funds;
                totalFunds = data.count;
                renderResults(allFunds, totalFunds);
                updateSortIndicators();
                this.paginationManager.updateState(data.count);
                console.log(`applyFilters took ${performance.now() - start}ms`);
            } catch (error) {
                console.error('Filter error:', error, filters);
                this.showError(`Error loading funds: ${error.message}`, () => this.applyFilters());
            } finally {
                this.isLoading = false;
            }
        }

        // Update applied filters summary
        updateAppliedFilters() {
            const navSlider = document.getElementById('nav-range-slider');
            const fundSizeSlider = document.getElementById('fund-size-range-slider');
            const expenseRatioSlider = document.getElementById('expense-ratio-range-slider');

            const activeFilters = [];
            const addFilter = (type, text, clearAction) => activeFilters.push({ type, text, clearAction });

            Object.entries(this.filters).forEach(([name, filter]) => {
                const selected = filter.getSelected();
                if (selected) {
                    addFilter(
                        name,
                        `${name.replace('-', ' ').replace(/\b\w/g, c => c.toUpperCase())}: ${name === 'crisil-rating' ? `${selected} ☆` : selected}`,
                        () => {
                            this.updateFilterCheckboxUI(name); // Reset filter to "All"
                            this.applyFilters(1); // Refresh results
                        }
                    );
                }
            });

            if (navSlider.noUiSlider) {
                const [minNav, maxNav] = navSlider.noUiSlider.get();
                if (minNav > 0 || maxNav < 5000) {
                    addFilter('nav', `NAV: ₹${Math.round(minNav)} - ₹${Math.round(maxNav)}`, () => {
                        navSlider.noUiSlider.set([0, 5000]);
                        this.updateAppliedFilters();
                        this.applyFilters(1);
                    });
                }
            }

            if (fundSizeSlider.noUiSlider) {
                const [minFundSize, maxFundSize] = fundSizeSlider.noUiSlider.get();
                if (minFundSize > 0 || maxFundSize < 50000) {
                    addFilter('fund-size', `Fund Size: ₹${minFundSize.toLocaleString('en-IN')} - ₹${maxFundSize.toLocaleString('en-IN')} Cr`, () => {
                        fundSizeSlider.noUiSlider.set([0, 50000]);
                        this.updateAppliedFilters();
                        this.applyFilters(1);
                    });
                }
            }

            if (expenseRatioSlider.noUiSlider) {
                const [minExpense, maxExpense] = expenseRatioSlider.noUiSlider.get();
                if (minExpense > 0 || maxExpense < 3) {
                    addFilter('expense_ratio', `Expense Ratio: ${minExpense}% - ${maxExpense}%`, () => {
                        expenseRatioSlider.noUiSlider.set([0, 3]);
                        this.updateAppliedFilters();
                        this.applyFilters(1);
                    });
                }
            }

            if (additionalFilterControls.return3y) {
                addFilter('return3y', `3Y Return ≥ ${additionalFilterControls.return3y}%`, () => {
                    delete additionalFilterControls.return3y;
                    this.updateAppliedFilters();
                    this.applyFilters(1);
                });
            }

            if (additionalFilterControls.return1y) {
                addFilter('return1y', `1Y Return ≥ ${additionalFilterControls.return1y}%`, () => {
                    delete additionalFilterControls.return1y;
                    this.updateAppliedFilters();
                    this.applyFilters(1);
                });
            }

            const fragment = document.createDocumentFragment();
            activeFilters.forEach(filter => {
                const badge = document.createElement('div');
                badge.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs bg-gray-100 text-gray-800 dark:bg-blue-900 dark:text-blue-200 transition-all duration-200 ease-in-out hover:bg-gray-200 dark:hover:bg-blue-800';
                badge.innerHTML = `
                    <span>${filter.text}</span>
                    <button type="button" class="ml-1 text-red-400 hover:text-red-800">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                `;
                badge.querySelector('button').addEventListener('click', filter.clearAction);
                fragment.appendChild(badge);
            });

            ELEMENTS.filterBadges.innerHTML = '';
            ELEMENTS.filterBadges.appendChild(fragment);
            ELEMENTS.appliedFilters.classList.toggle('hidden', activeFilters.length === 0);
        }

        // Reset all filters
        resetAllFilters() {
            additionalFilterControls = {};
            const sliders = [
                { id: 'nav-range-slider', range: SLIDER_CONFIGS.nav.start },
                { id: 'fund-size-range-slider', range: SLIDER_CONFIGS.fundSize.start },
                { id: 'expense-ratio-range-slider', range: SLIDER_CONFIGS.expenseRatio.start },
            ];
            sliders.forEach(({ id, range }) => {
                const slider = document.getElementById(id);
                if (slider.noUiSlider) slider.noUiSlider.set(range);
            });

            Object.entries(this.filters).forEach(([name]) => this.updateFilterCheckboxUI(name));
            document.querySelector('.sub-category-container').classList.add('hidden');
            ELEMENTS.appliedFilters.classList.add('hidden');
            ELEMENTS.filterBadges.innerHTML = '';
            sortState = { key: null, direction: 'asc' };
            this.paginationManager.setPage(1); // Reset to page 1
            if (window.location.pathname.startsWith('/mutual-funds/')) {
                window.location.href = '/mutual-funds';
            }
        }

        // Update filter checkbox UI
        updateFilterCheckboxUI(filterType, selectedValue = '') {
            const allCheckbox = document.getElementById(`${filterType}-all`);
            const checkboxes = document.querySelectorAll(`#${filterType}-checkboxes input[name="${filterType}"]`);
            const display = document.getElementById(`${filterType}-display`);
            const clearButton = document.getElementById(`clear-${filterType}`);
            const container = document.querySelector(`.${filterType}-container`);

            // Reset all checkboxes
            checkboxes.forEach(cb => {
                cb.checked = false;
                cb.disabled = true;
            });

            if (selectedValue) {
                // Split comma-separated values into an array and trim whitespace
                const selectedValues = selectedValue.split(',').map(val => val.trim());

                // Update checkboxes for each selected value
                checkboxes.forEach(cb => {
                    let valueToMatch = cb.value;
                    if (filterType === 'sub-category') {
                        valueToMatch = valueToMatch.replace(' Fund', '');
                    }
                    cb.disabled = false;
                    if (selectedValues.includes(valueToMatch)) {
                        cb.checked = true;
                    }
                });

                // Update display text
                allCheckbox.checked = false;
                if (filterType === 'crisil-rating') {
                    display.textContent = selectedValues.map(val => `${val} ☆`).join(', ');
                } else {
                    display.textContent = selectedValues.join(', ');
                }
                clearButton.classList.remove('hidden');

                // Show sub-category container if applicable
                if (container && filterType === 'sub-category') {
                    container.classList.remove('hidden');
                }
            } else {
                // Reset to "All" state
                allCheckbox.checked = true;
                display.textContent = `All ${filterType.replace('-', ' ').replace(/\b\w/g, c => c.toUpperCase())}`;
                clearButton.classList.add('hidden');

                // Hide sub-category container if no category is selected
                if (container && filterType === 'sub-category' && !this.filters['category'].getSelected()) {
                    container.classList.add('hidden');
                }
            }

            // Update sub-category filter if category changes
            if (filterType === 'category') {
                this.updateSubCategoryFilter();
            }

            // Update applied filters UI
            this.updateAppliedFilters();
        }

        updateCardSelection(containerId = 'preset-cards', basePath = '/mutual-funds/') {
            const presetCardsContainer = document.getElementById(containerId);
            if (!presetCardsContainer) return;

            const cards = presetCardsContainer.querySelectorAll('a[data-url]');
            let activePresetId = window.location.pathname.split(basePath)[1] || '';
            if (activePresetId.includes('/')) activePresetId = activePresetId.split('/')[0];

            const ACTIVE_CLASSES = ['selected', 'border-2', 'shadow-lg', 'shadow-gray-400'];
            const DEFAULT_COLOR = 'blue';

            cards.forEach(card => {
                const cardUrl = card.getAttribute('data-url') || '';
                const isActive = activePresetId === cardUrl;

                // Extract border color efficiently
                const bgClass = [...card.classList].find(cls => cls.match(/^(bg|from)-/)) || `bg-${DEFAULT_COLOR}-50`;
                const colorKey = bgClass.match(/-(\w+)-/)?.[1] || DEFAULT_COLOR;
                const borderClass = `border-${colorKey}-700`;

                const classesToToggle = [...ACTIVE_CLASSES, borderClass];
                classesToToggle.forEach(cls => card.classList.toggle(cls, isActive));

                // Scroll to active card (only one should be active)
                if (isActive) {
                    card.scrollIntoView({ behavior: 'smooth', inline: 'center' });
                }
            });
        }

        // Show error message
        showError(message, retryAction) {
            ELEMENTS.resultsBody.innerHTML = `
            <tr>
                <td colspan="7" class="p-4 text-center text-red-500">
                    <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    ${message}
                    ${retryAction ? '<button class="mt-2 text-blue-600 hover:underline" onclick="filterManager.applyFilters()">Retry</button>' : ''}
                </td>
            </tr>
            `;
        }
    }

    // Initialize range filters
    const initRangeFilters = () => {
        const sliders = [
            {
                id: 'nav-range-slider',
                config: SLIDER_CONFIGS.nav,
                indicator: ELEMENTS.navRangeIndicator,
                format: ([min, max]) => `₹${min} - ₹${max}`,
            },
            {
                id: 'fund-size-range-slider',
                config: SLIDER_CONFIGS.fundSize,
                indicator: ELEMENTS.fundSizeRangeIndicator,
                format: ([min, max]) => `₹${min.toLocaleString('en-IN')} - ₹${max.toLocaleString('en-IN')}`,
            },
            {
                id: 'expense-ratio-range-slider',
                config: SLIDER_CONFIGS.expenseRatio,
                indicator: ELEMENTS.expenseRatioRangeIndicator,
                format: ([min, max]) => `${min}% - ${max}%`,
            },
        ];

        sliders.forEach(({ id, config, indicator, format }) => {
            const slider = document.getElementById(id);
            try {
                noUiSlider.create(slider, {
                    ...config,
                    connect: true,
                    behaviour: 'drag',
                });

                slider.noUiSlider.on('update', (values) => {
                    indicator.textContent = format(values);
                });

                slider.noUiSlider.on('end', () => {
                    filterManager.updateAppliedFilters();
                    filterManager.applyFilters(1);
                });
            } catch (error) {
                console.error(`Error initializing ${id} slider:`, error);
            }
        });
    };

    // Initialize sort listeners
    const initSortListeners = () => {
        document.querySelectorAll('th[data-sort-key]').forEach(header => {
            header.addEventListener('click', () => {
                const sortKey = header.dataset.sortKey;
                const currentDirection = sortState.key === sortKey ? sortState.direction : 'asc';
                sortState = {
                    key: sortKey,
                    direction: currentDirection === 'asc' ? 'desc' : 'asc',
                };
                updateSortIndicators();
                filterManager.applyFilters(1);
            });
        });

        ELEMENTS.sortFilter.addEventListener('change', (e) => {
            const value = e.target.value;
            let sortKey = 'popularity';
            let direction = 'desc';

            const sortMap = {
                'rating-high-low': { key: 'rating', direction: 'desc' },
                'returns-1m-high-low': { key: 'returns_1m', direction: 'desc' },
                'returns-6m-high-low': { key: 'returns_6m', direction: 'desc' },
                'returns-1y-high-low': { key: 'returns_1y', direction: 'desc' },
                'returns-3y-high-low': { key: 'returns_3y', direction: 'desc' },
            };

            if (sortMap[value]) {
                ({ key: sortKey, direction } = sortMap[value]);
            }

            sortState = { key: sortKey, direction };
            filterManager.applyFilters(1, {}, false);
        });
    };

    // Update sort indicators
    const updateSortIndicators = () => {
        document.querySelectorAll('th[data-sort-key]').forEach(th => {
            const indicator = th.querySelector('span');
            const key = th.getAttribute('data-sort-key');
            if (key === sortState.key) {
                indicator.textContent = sortState.direction === 'asc' ? '↑' : '↓';
                th.classList.remove('text-gray-500', 'dark:text-gray-400');
                th.classList.add('text-blue-600', 'dark:text-blue-400');
                indicator.classList.remove('text-transparent');
                indicator.classList.add('text-blue-600', 'dark:text-blue-400');
            } else {
                indicator.textContent = '';
                th.classList.remove('text-blue-600', 'dark:text-blue-400');
                th.classList.add('text-gray-500', 'dark:text-gray-400');
                indicator.classList.add('text-transparent');
            }
        });
    };

    // Handle prebuilt screeners
    const initPresetCards = (presetFilters) => {
        if (Object.keys(presetFilters).length > 0) {
            if (presetFilters.category) {
                filterManager.updateFilterCheckboxUI('category', presetFilters.category);
            }
            if (presetFilters.sub_category) {
                presetFilters.sub_category = presetFilters.sub_category.replace(" Fund", "");
                filterManager.updateSubCategoryFilter()
                filterManager.updateFilterCheckboxUI('sub-category', presetFilters.sub_category);
            }
            if (presetFilters.rating) {
                presetFilters.rating = presetFilters.rating.split('-').map(Number).join(',');
                filterManager.updateFilterCheckboxUI('crisil-rating', presetFilters.rating);
            }
            if (presetFilters.risk) {
                filterManager.updateFilterCheckboxUI('risk', presetFilters.risk);
            }
            if (presetFilters.plan_type) {
                filterManager.updateFilterCheckboxUI('plan', presetFilters.plan_type);
            }
            if (presetFilters.return3y_min) {
                additionalFilterControls.return3y = presetFilters.return3y_min;
            }
            if (presetFilters.return1y_min) {
                additionalFilterControls.return1y = presetFilters.return1y_min;
            }
            if (presetFilters.expense_ratio_range) {
                const expenseRatioRange = presetFilters.expense_ratio_range.split('-').map(Number);
                document.getElementById('expense-ratio-range-slider').noUiSlider.set(expenseRatioRange);
            }
            if (presetFilters.fund_size_range) {
                const fundSizeRange = presetFilters.fund_size_range.split('-').map(Number);
                document.getElementById('fund-size-range-slider').noUiSlider.set(fundSizeRange);
            }
            if (presetFilters.sort_key) {
                sortState.key = presetFilters.sort_key;
                sortState.direction = presetFilters.sort_direction;
                const sortKeyMap = {
                    rating: "rating-high-low",
                    returns_1m: "returns-1m-high-low",
                    returns_6m: "returns-6m-high-low",
                    returns_1y: "returns-1y-high-low",
                    returns_3y: "returns-3y-high-low",
                };
                ELEMENTS.sortFilter.value = sortKeyMap[presetFilters.sort_key] || "popularity";
            }
            filterManager.updateAppliedFilters();
            filterManager.updateCardSelection('preset-cards', '/mutual-funds/');
        }
    };

    // Debounce utility to prevent excessive API calls
    const debounce = (func, wait) => {
        let timeout;
        return (...args) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func(...args), wait);
        };
    };

    class PaginationManager {
        constructor({ filterManager, baseUrl = '/mutual-funds', perPage = 100, initialCurrentPage = 1, initialTotalFunds = 0, initialTotalPages = 1 }) {
            this.filterManager = filterManager;
            this.baseUrl = baseUrl;
            this.perPage = Math.max(1, Math.min(perPage, 100)); // Clamp to [1, 100]
            this.currentPage = Math.max(1, parseInt(initialCurrentPage) || 1);
            this.totalFunds = initialTotalFunds;
            this.totalPages = initialTotalPages;
            this.initEventListeners();
        }

        // Initialize pagination event listeners
        initEventListeners() {
            document.addEventListener('click', (e) => {
                if (e.target.id === 'prev-page' && this.currentPage > 1) {
                    this.setPage(this.currentPage - 1);
                } else if (e.target.id === 'next-page' && this.currentPage < this.totalPages) {
                    this.setPage(this.currentPage + 1);
                } else if (e.target.matches('nav a[data-page]')) {
                    e.preventDefault();
                    const page = parseInt(e.target.getAttribute('data-page'));
                    this.setPage(page);
                }
            });
        }

        // Set current page and fetch results
        setPage(page) {
            this.currentPage = page;
            this.filterManager.applyFilters({ page: this.currentPage, limit: this.perPage }, false);
        }

        // Update pagination state after fetching results
        updateState(totalFunds) {
            this.totalFunds = totalFunds;
            this.totalPages = Math.ceil(totalFunds / this.perPage);
            this.currentPage = Math.min(this.currentPage, this.totalPages || 1);
            this.render(this.totalPages);
            this.updateURL();
        }

        // Render pagination controls
        render(totalPages) {
            const nav = document.getElementById('pagination-nav');
            if (!nav) {
                console.error('Pagination nav element (#pagination-nav) not found');
                return;
            }

            // Generate page numbers
            let startPage = Math.max(1, this.currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);
            if (endPage < 4 && totalPages) startPage = Math.max(1, endPage - 4);

            let html = '';
            if (startPage > 1) {
                html += `<a href="${this.buildPageUrl(1)}" data-page="1" class="px-3 py-1 border rounded-md bg-white dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900">1</a>`;
                if (startPage > 2) html += `<span class="px-3 py-1">...</span>`;
            }
            for (let i = startPage; i <= endPage; i++) {
                html += `<a href="${this.buildPageUrl(i)}" data-page="${i}" class="px-3 py-1 border rounded-md ${i === this.currentPage ? 'bg-blue-100 text-blue-900 dark:bg-blue-800 dark:text-white' : 'bg-white dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900'}">${i}</a>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) html += `<span class="px-3 py-1">...</span>`;
                html += `<a href="${this.buildPageUrl(totalPages)}" data-page="${totalPages}" class="px-3 py-1 border rounded-md bg-white dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900">${totalPages}</a>`;
            }

            // Render pagination controls
            nav.innerHTML = `
                <button id="prev-page" class="px-3 py-1 border rounded-md ${this.currentPage === 1 ? 'bg-gray-100 dark:bg-gray-600 cursor-not-allowed' : 'bg-white dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900'}" ${this.currentPage === 1 ? 'disabled' : ''}>Previous</button>
                ${html}
                <button id="next-page" class="px-3 py-1 border rounded-md ${this.currentPage === totalPages ? 'bg-gray-100 dark:bg-gray-600 cursor-not-allowed' : 'bg-white dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900'}" ${this.currentPage === totalPages ? 'disabled' : ''}>Next</button>
            `;
        }

        // Update browser URL with pagination and filters
        updateURL() {
            let basePath = this.baseUrl;
            if (pathParam && pathParam !== 'mutual-funds') {
                basePath += `/${pathParam}`;
            }
            const url = this.currentPage > 1 ? `${basePath}/${this.currentPage}` : basePath;
            history.pushState({}, '', url);
        }

        buildPageUrl(page) {
            let basePath = this.baseUrl;
            if (pathParam && pathParam !== 'mutual-funds') {
                basePath += `/${pathParam}`;
                return page > 1 ? `${basePath}/${page}` : basePath;
            }
            return page > 1 ? `${basePath}/${page}` : basePath;
        }
    }
</script>