<!DOCTYPE html>
<html lang="en">

<head>
    <title>Compare Mutual Funds | AI Bull</title>
    <meta name="description"
        content="Compare up to 5 mutual funds side-by-side, including NAV, AUM, returns, risk, and more." />
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="{{ seo_meta_data.canonical_url | default('https://theaibull.com/mutual-funds/compare') }}" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/mutual-funds/compare" />
    <meta property="og:title" content="Compare Mutual Funds" />
    <meta property="og:description" content="Compare mutual funds to make informed investment decisions." />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Compare Mutual Funds" />
    <meta name="twitter:description" content="Compare mutual funds to make informed investment decisions." />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chartjs-plugin-zoom/2.0.1/chartjs-plugin-zoom.min.js"></script>
    {% include 'blocks/head.html' %}
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">
    {% include 'blocks/common.html' %}
    {% include 'blocks/info.html' %}
    {% include 'blocks/ai-feedback.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <main>
            <div class="py-3 px-10 mx-auto w-full">
                <!-- Header -->
                <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2 rounded-lg mb-3">
                    <!-- Title and Tabs Row -->
                    <div class="md:flex justify-between items-center">
                        <div class="flex items-center">
                            <h1 class="md:text-xl text-lg font-semibold tracking-tight">Compare Mutual Funds</h1>
                            <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">Compare up to 5 mutual
                                funds side-by-side to make informed investment decisions.</h2>

                        </div>
                        <a href="/mutual-funds"
                            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm flex items-center font-medium transition-colors duration-200">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Back to Screener
                        </a>
                    </div>
                </div>
                <div>
                    <!-- Comparison Section -->
                    <div class="bg-white relative rounded-2xl p-6 mb-6">
                        <div class="flex items-center gap-2 mb-3">
                            <h2 class="text-base font-semibold tracking-tight">Select Funds to Compare</h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                Add or remove up to 5 mutual funds to compare their performance and metrics.
                            </p>
                        </div>
                        <div id="slots-container" class="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
                            {% for i in range(5) %}
                            <div
                                class="comparison-slot bg-gray-100 dark:bg-gray-700 p-4 rounded-lg text-center relative transition-transform duration-200 hover:shadow-md">
                                <div class="fund-details hidden" data-slot="{{ i }}">
                                    <button
                                        class="absolute bg-red-100 text-red-600 hover:text-white flex h-4 hover:bg-red-600 items-center justify-center p-0.5 remove-fund right-[-5px] rounded-full text-white top-[-5px] w-4"
                                        title="Remove">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-x-icon lucide-x">
                                            <path d="M18 6 6 18" />
                                            <path d="m6 6 12 12" />
                                        </svg>
                                    </button>
                                    <p class="fund-name text-sm font-medium text-gray-900 dark:text-gray-100"></p>
                                    <p class="fund-house text-xs text-gray-500 dark:text-gray-400 mt-1"></p>
                                </div>
                                <div class="add-fund flex flex-col items-center justify-center h-full">
                                    <button
                                        class="add-btn flex mx-auto flex-col items-center text-gray-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200"
                                        title="Add Fund">
                                        <svg class="w-8 h-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 4v16m8-8H4">
                                            </path>
                                        </svg>
                                        <span class="text-sm font-medium">Add to Compare</span>
                                    </button>
                                    <input type="text"
                                        class="border border-gray-200 dark:bg-neutral-700 dark:border-neutral-600 focus:border-blue-500 focus:outline-0 mt-2 px-3 py-2.5 rounded-md search-input text-sm transition-all w-full hidden"
                                        placeholder="Search fund..." />
                                    <div
                                        class="autocomplete-results text-sm text-left hidden top-20 absolute z-10 w-full max-h-[200px] overflow-y-auto bg-white border border-gray-200 rounded-lg shadow-md">
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center">
                            <button id="compare-btn"
                                class="bg-blue-500 block focus:outline-0 font-medium hover:bg-blue-600 px-6 py-3 rounded-md text-white transition-colors w-full cursor-not-allowed"
                                disabled>
                                Compare Now
                            </button>
                        </div>
                    </div>

                    <main class="compare-section">
                        {% if error %}
                        <div
                            class="bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 p-4 rounded-lg mb-6">
                            <p class="font-semibold">{{ error }}</p>
                        </div>
                        {% else %}
                        <!-- NAV History Graph -->
                        <div class="bg-white relative rounded-2xl p-6 mb-6">
                            <div>
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-left">
                                        NAV History Comparison
                                    </h3>
                                    <button onclick="analyzeAllFunds();"
                                        class="animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-sparkles">
                                            <path
                                                d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                            <path d="M20 3v4" />
                                            <path d="M22 5h-4" />
                                            <path d="M4 17v2" />
                                            <path d="M5 18H3" />
                                        </svg> Get AI Feedback
                                    </button>
                                </div>
                                <div class="flex justify-center gap-2 mb-4" id="time-filter-buttons">
                                    <button id="time-1m"
                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200 active">1M</button>
                                    <button id="time-6m"
                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">6M</button>
                                    <button id="time-1y"
                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">1Y</button>
                                    <button id="time-3y"
                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">3Y</button>
                                    <button id="time-5y"
                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">5Y</button>
                                </div>
                                <div class="h-80">
                                    <canvas id="nav-chart-compare"></canvas>
                                </div>
                                <div id="zoom-options" class="flex justify-center gap-3 mt-4">
                                    <button id="reset-zoom-btn"
                                        class="text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-black hover:text-white dark:hover:bg-blue-600 px-3 py-1 rounded-md transition-all duration-200">Reset
                                        View</button>
                                    <button id="zoom-in-btn"
                                        class="text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-green-600 hover:text-white dark:hover:bg-blue-400 px-3 py-1  rounded-md transition-all duration-200">Zoom
                                        In</button>
                                    <button id="zoom-out-btn"
                                        class="text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-red-600 hover:text-white dark:hover:bg-blue-600 px-3 py-1  rounded-md transition-all duration-200">Zoom
                                        Out</button>
                                </div>
                            </div>
                        </div>

                        <!-- Sector Allocation Graph -->
                        <div class="bg-white relative rounded-2xl p-6 mb-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-left">
                                    Sector Allocation Comparison
                                </h3>
                                <div class=" flex justify-start gap-2 mb-4 flex-wrap" id="sector-tab-buttons">
                                    <button id="sector-overall"
                                        class="sector-tab text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-3 py-1 rounded-md transition-all duration-200 active text-xs font-medium">Overall</button>
                                    {% for fund in funds %}
                                    <button id="sector-{{ fund.scheme_code }}"
                                        class="sector-tab text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-3 py-1 rounded-md transition-all duration-200 text-xs font-medium"
                                        title="{{ fund.scheme_name }}">{{ fund.scheme_name
                                        }}</button>
                                    {% endfor %}
                                </div>
                                <div class="h-[30rem]">
                                    <canvas id="sector-chart"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="w-full overflow-auto  bg-white relative rounded-2xl mb-6">
                            <table class="w-full text-sm text-gray-700 dark:text-gray-300 table-fixed">
                                <thead>
                                    <tr class="bg-white">
                                        <th
                                            class="bg-white border-b font-medium font-semibold max-w-48 min-w-48 px-6 py-3 sticky text-left text-lg top-0 truncate w-48">
                                            Metric</th>
                                        {% for fund in funds %}
                                        <th
                                            class="w-48 min-w-48 max-w-48 border-l truncate sticky top-0  z-10 border-b bg-white">
                                            <div class="px-3 py-2.5 text-left truncate">
                                                <a href="/mutual-funds/{{ fund.scheme_code }}"
                                                    class="hover:text-blue-600 text-gray-800 hover:text-blue-800 dark:text-blue-400 truncate dark:hover:text-blue-300">{{
                                                    fund.scheme_name | default('N/A') }}</a>
                                                <p class="text-xs text-gray-400 dark:text-gray-400 mt-1">{{
                                                    fund.fund_house
                                                    | default('N/A') }}
                                                </p>
                                                <div class="flex flex-wrap justify-start gap-1 mt-2">
                                                    {% if fund.category %}
                                                    <span
                                                        class="px-2 py-0.5 text-[10px] rounded-md bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">{{
                                                        fund.category }}</span>
                                                    {% endif %}
                                                    {% if fund.sub_category %}
                                                    <span
                                                        class="px-2 py-0.5 text-[10px] rounded-md bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">{{
                                                        fund.sub_category }}</span>
                                                    {% endif %}
                                                    {% if fund.rating %}
                                                    <span
                                                        class="px-2 py-0.5 text-[12px] rounded-md bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 flex items-center">
                                                        <svg class="w-3 h-3 mr-1 text-yellow-500" fill="currentColor"
                                                            viewBox="0 0 20 20">
                                                            <path
                                                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                                            </path>
                                                        </svg>
                                                        {{ fund.rating }}/5
                                                    </span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </th>
                                        {% endfor %}
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Fund Details Category -->
                                    <tr class="bg-gray-100 dark:bg-gray-800 border-b dark:border-gray-700">
                                        <td class="px-6 py-3 font-bold text-left" colspan="{{ funds | length + 1 }}">
                                            Fund
                                            Details</td>
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">NAV
                                        </td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left break-words border-l">₹ {%
                                            if
                                            fund.nav
                                            %}{{ fund.nav | float
                                            | round(2) }} (<span class="date">{{ fund.nav_date }}</span>){% else %}N/A{%
                                            endif %}</td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Fund
                                            Size</td>
                                        {% for fund in funds %}
                                        <td
                                            class="w-48 min-w-48 max-w-48 px-6 py-3 text-left break-words crmoney border-l">
                                            {% if
                                            fund.aum %}{{ fund.aum |
                                            float | round(2) }}{% else %}N/A{% endif %}</td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">
                                            Expense
                                            Ratio (%)</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left truncate border-l">{% if
                                            fund.expense_ratio %}{{
                                            fund.expense_ratio | float | round(2) }}{% else %}N/A{% endif %}</td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Risk
                                        </td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left break-words border-l">{{
                                            fund.return_stats[0].risk |
                                            default('N/A') }}</td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Min
                                            Investment (₹)</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left truncate border-l">{{
                                            fund.min_investment_amount |
                                            default('N/A') }}</td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Min
                                            SIP
                                            (₹)</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left break-words border-l">{{
                                            fund.min_sip_investment |
                                            default('N/A') }}</td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Exit
                                            Load</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left break-words border-l">{%
                                            if
                                            fund.exit_load %}{{
                                            fund.exit_load }}{% else %}N/A{% endif %}</td>
                                        {% endfor %}
                                    </tr>
                                    <!-- Returns Category -->
                                    <tr class="bg-gray-100 dark:bg-gray-800 border-b dark:border-gray-700">
                                        <td class="px-6 py-3 font-bold text-left" colspan="{{ funds | length + 1 }}">
                                            Returns
                                        </td>
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">1Y
                                            Return (%)</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left break-words border-l">{%
                                            if
                                            fund.stats[0].stat_1y %}{{
                                            fund.stats[0].stat_1y | float | round(2) }}{% else %}N/A{% endif %}</td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">3Y
                                            Return (%)</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left break-words border-l">{%
                                            if
                                            fund.stats[0].stat_3y %}{{
                                            fund.stats[0].stat_3y | float | round(2) }}{% else %}N/A{% endif %}</td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">5Y
                                            Return (%)</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left break-words border-l">{%
                                            if
                                            fund.stats[0].stat_5y %}{{
                                            fund.stats[0].stat_5y | float | round(2) }}{% else %}N/A{% endif %}</td>
                                        {% endfor %}
                                    </tr>
                                    <!-- Top Holdings Category -->
                                    <tr class="bg-gray-100 dark:bg-gray-800 border-b dark:border-gray-700">
                                        <td class="px-6 py-3 font-bold text-left" colspan="{{ funds | length + 1 }}">Top
                                            Holdings</td>
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Top
                                            10
                                            Holdings</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left break-words border-l">
                                            <div class="font-medium text-gray-900 dark:text-white mb-2">{{ fund.name }}
                                            </div>
                                            {% if fund.holdings %}
                                            <div class="space-y-5">
                                                {% for holding in fund.holdings[:10] %}
                                                <div class="flex items-center justify-between gap-1">
                                                    <span
                                                        class="text-gray-600 dark:text-gray-400 break-words font-medium max-w-xs">{{
                                                        holding.company_name }}</span>
                                                    <div class="flex items-center">
                                                        <div class="relative w-12 h-12 mr-2">
                                                            <svg class="w-full h-full" viewBox="0 0 36 36">
                                                                <path class="text-gray-200 dark:text-gray-700" d="M18 2.0845
                                                                   a 15.9155 15.9155 0 0 1 0 31.831
                                                                   a 15.9155 15.9155 0 0 1 0 -31.831" fill="none"
                                                                    stroke="currentColor" stroke-width="4" />
                                                                <path class="text-green-600 dark:text-blue-500" d="M18 2.0845
                                                                   a 15.9155 15.9155 0 0 1 0 31.831
                                                                   a 15.9155 15.9155 0 0 1 0 -31.831" fill="none"
                                                                    stroke="currentColor" stroke-width="4"
                                                                    stroke-dasharray="{{ holding.corpus_per }}, 100" />
                                                            </svg>
                                                            <div
                                                                class="absolute inset-0 flex items-center justify-center">
                                                                <span
                                                                    class="text-[10px] font-medium text-gray-700 dark:text-gray-300">{{
                                                                    holding.corpus_per | float |
                                                                    round(2) }}%</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                            {% else %}
                                            <div class="text-gray-500 dark:text-gray-400 italic">No holdings data
                                                available
                                            </div>
                                            {% endif %}
                                        </td>
                                        {% endfor %}
                                    </tr>
                                    <!-- About Fund Category -->
                                    <tr class="bg-gray-100 dark:bg-gray-800 border-b dark:border-gray-700">
                                        <td class="px-6 py-3 font-bold text-left" colspan="{{ funds | length + 1 }}">
                                            About
                                            Fund</td>
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Fund
                                            Started Date</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left truncate border-l">{% if
                                            fund.launch_date %}<span class="date">{{ fund.launch_date }}</span>{% else
                                            %}N/A{% endif %}</td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left">Fund
                                            Manager</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-left border-l">
                                            {% if fund.fund_manager_details %}
                                            {{ fund.fund_manager_details | map(attribute='person_name') | join(', ') }}
                                            {% else %}
                                            N/A
                                            {% endif %}
                                        </td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left break-words">
                                            Custodian Name</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 border-l break-words">
                                            {% if fund.rta_details and fund.rta_details.custodian_name %}
                                            {{ fund.rta_details.custodian_name }}
                                            {% else %}
                                            N/A
                                            {% endif %}
                                        </td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">RTA
                                            Name
                                        </td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3  border-l break-words">
                                            {% if fund.rta_details and fund.rta_details.rta_name %}
                                            {{ fund.rta_details.rta_name }}
                                            {% else %}
                                            N/A
                                            {% endif %}
                                        </td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">RTA
                                            Address</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 border-l break-words">
                                            {% if fund.rta_details and fund.rta_details.address %}
                                            {{ fund.rta_details.address }}
                                            {% else %}
                                            N/A
                                            {% endif %}
                                        </td>
                                        {% endfor %}
                                    </tr>
                                    <tr class="border-b dark:border-gray-700">
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">RTA
                                            Email</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3  border-l">
                                            {% if fund.rta_details and fund.rta_details.email %}
                                            <a href="mailto:{{ fund.rta_details.email }}"
                                                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 break-words">{{
                                                fund.rta_details.email }}</a>
                                            {% else %}
                                            N/A
                                            {% endif %}
                                        </td>
                                        {% endfor %}
                                    </tr>
                                    <tr>
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">RTA
                                            Website</td>
                                        {% for fund in funds %}
                                        <td class="w-48 min-w-48 max-w-48 px-6 py-3  border-l">
                                            {% if fund.rta_details and fund.rta_details.website %}
                                            <a href="https://{{ fund.rta_details.website }}" target="_blank"
                                                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 break-words">{{
                                                fund.rta_details.website }}</a>
                                            {% else %}
                                            N/A
                                            {% endif %}
                                        </td>
                                        {% endfor %}
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        {% endif %}
                    </main>
                    <!-- You May Be Interested In Section -->
                    {% if recommended_funds %}
                    <div class="p-4 relative bg-white dark:bg-gray-800 rounded-lg mb-6 transition-all duration-300">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">You May Be Interested In</h3>
                            <div>
                                <button id="recommended-left-arrow"
                                    class="border z-30 h-10 w-10 bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                    aria-label="Scroll left">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M6 8L2 12L6 16"></path>
                                        <path d="M2 12H22"></path>
                                    </svg>
                                </button>
                                <button id="recommended-right-arrow"
                                    class="h-10 w-10 border bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                    aria-label="Scroll right">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M18 8L22 12L18 16"></path>
                                        <path d="M2 12H22"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div id="recommended-funds-cards" class="flex flex-col gap-3 md:flex-row overflow-hidden relative">
                            {% for fund in recommended_funds %}
                            <a href="/mutual-funds/{{ fund.scheme_code }}"
                                class="flex-shrink-0 p-3 rounded-lg cursor-pointer transition-colors w-72 hover:-translate-y-0.5 hover:shadow-md
                                {% if loop.index0 % 5 == 0 %} bg-blue-500/20
                                {% elif loop.index0 % 5 == 1 %} bg-emerald-500/20
                                {% elif loop.index0 % 5 == 2 %} bg-purple-500/20
                                {% elif loop.index0 % 5 == 3 %} bg-yellow-500/20
                                {% elif loop.index0 % 5 == 4 %} bg-red-500/20
                                {% endif %}">
                                <h3 class="font-semibold text-base mb-1">{{ fund.scheme_name }}</h3>
                                <p class="text-xs text-gray-700 dark:text-gray-300">{{ fund.fund_house }} • {{ fund.sub_category or
                                    fund.category }}</p>
                                {% if fund.rating %}
                                <div class="flex items-center mt-1">
                                    <svg class="w-3 h-3 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                        </path>
                                    </svg>
                                    <span class="text-xs text-gray-600 dark:text-gray-400">{{ fund.rating }}/5</span>
                                </div>
                                {% endif %}
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </main>
    </div>

    <script>
        // Initialize slot assignments (null for empty slots)
        let fundsData = []; // Will be populated via API
        const urlParts = window.location.pathname.split('/').filter(part => part);
        const slotAssignments = Array(5).fill(null); // One entry per slot
        const slots = document.querySelectorAll('.comparison-slot');
        const compareBtn = document.getElementById('compare-btn');

        // Fetch funds data from API
        async function fetchFundsData(schemeCodes) {
            if (!schemeCodes || schemeCodes.length === 0) {
                console.warn('No scheme codes provided for API call');
                return [];
            }
            try {
                const response = await fetch(`/mutual-funds/api/compare/${schemeCodes.map(encodeURIComponent).join('/')}`);
                const data = await response.json();
                if (!response.ok || data.error) {
                    console.error('API Error:', data.error || 'Failed to fetch funds');
                    showAlert(data.error || 'Unable to load fund data. Please try again.');
                    return [];
                }
                return data.funds; // Returns the funds array directly
            } catch (error) {
                console.error('Error fetching funds from API:', error);
                showAlert('Failed to connect to the server. Please check your connection.');
                return [];
            }
        }

        // Initialize NAV comparison chart
        function initializeNavCompareChart(timeRange = '1m') {
            const navCanvas = document.getElementById("nav-chart-compare")?.getContext("2d");
            if (navCanvas && fundsData.length > 0) {
                if (window.navCompareChart) {
                    window.navCompareChart.destroy();
                }
                window.navCompareChart = new Chart(navCanvas, getNavCompareChartConfig(fundsData, timeRange));
                setupZoomControls(window.navCompareChart);
            }
        }

        // Handle time filter buttons
        function setupTimeFilters() {
            const timeFilterButtons = document.querySelectorAll('.time-filter');
            timeFilterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const range = button.id.replace('time-', '');
                    updateFilterButtons(button);
                    initializeNavCompareChart(range);
                });
            });
            // Set default filter (1M)
            updateFilterButtons(document.getElementById('time-1m'));
        }

        // Update sector tab buttons
        function updateSectorTabButtons(activeButton) {
            const tabButtons = document.querySelectorAll('.sector-tab');
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-blue-500', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
            });
            activeButton.classList.add('active', 'bg-blue-500', 'text-white');
            activeButton.classList.remove('bg-gray-100', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
        }

        // Initialize sector pie charts with tabbed interface
        function initializeSectorCharts() {
            const sectorCanvas = document.getElementById('sector-chart')?.getContext('2d');
            if (!sectorCanvas || fundsData.length === 0) {
                console.warn('Sector chart canvas not found or no funds data available');
                return;
            }

            // Store current chart
            window.sectorChart = window.sectorChart || null;

            // Function to render chart based on tab
            const renderChart = (chartConfig) => {
                if (window.sectorChart) {
                    window.sectorChart.destroy();
                }
                window.sectorChart = new Chart(sectorCanvas, chartConfig);
            };

            // Tab button event listeners
            const tabButtons = document.querySelectorAll('.sector-tab');
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    updateSectorTabButtons(button);
                    const tabId = button.id;
                    try {
                        if (tabId === 'sector-overall') {
                            renderChart(getOverallSectorPieChartConfig(fundsData));
                        } else {
                            const schemeCode = tabId.replace('sector-', '');
                            const fund = fundsData.find(f => f.scheme_code === schemeCode);
                            if (fund) {
                                renderChart(getSectorPieChartConfig(fund));
                            } else {
                                console.warn(`Fund not found for scheme_code: ${schemeCode}`);
                            }
                        }
                    } catch (error) {
                        console.error(`Error rendering sector chart for ${tabId}:`, error);
                    }
                });
            });

            // Render overall chart by default
            try {
                renderChart(getOverallSectorPieChartConfig(fundsData));
                updateSectorTabButtons(document.getElementById('sector-overall'));
            } catch (error) {
                console.error('Error rendering initial overall sector chart:', error);
            }
        }

        // Pre-populate slots with selected funds from API data
        async function populateSlots() {
            // Extract scheme codes from the URL path
            const schemeCodes = urlParts.slice(urlParts.indexOf('compare') + 1);
            if (schemeCodes.length > 0) {
                fundsData = await fetchFundsData(schemeCodes);
                schemeCodes.forEach((symbol, index) => {
                    const schemeCode = decodeURIComponent(symbol);
                    if (schemeCode && index < slots.length) {
                        const fund = fundsData.find(f => f.scheme_code === schemeCode);
                        if (fund) {
                            const slot = slots[index];
                            const fundDetails = slot.querySelector('.fund-details');
                            const addFundDiv = slot.querySelector('.add-fund');
                            const fundName = fundDetails.querySelector('.fund-name');
                            const fundHouse = fundDetails.querySelector('.fund-house');

                            fundName.textContent = fund.scheme_name || 'N/A';
                            fundHouse.textContent = fund.fund_house || 'N/A';
                            fundDetails.dataset.schemeCode = schemeCode;
                            slotAssignments[index] = schemeCode;
                            fundDetails.style.display = 'block';
                            addFundDiv.style.display = 'none';
                        }
                    }
                });
            }
            updateCompareButton();
        }

        // Update Compare button state
        function updateCompareButton() {
            const assignedCount = slotAssignments.filter(code => code !== null).length;
            compareBtn.disabled = assignedCount < 2;
            compareBtn.classList.toggle('opacity-50', assignedCount < 2);
            compareBtn.classList.toggle('cursor-not-allowed', assignedCount < 2);
        }

        // Fetch funds for autocomplete
        async function fetchFunds(query) {
            try {
                const response = await fetch('/mutual-funds/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ scheme_name: query, columns: ['scheme_code', 'scheme_name', 'fund_house'] })
                });
                const data = await response.json();
                return data.funds || [];
            } catch (error) {
                console.error('Error fetching funds:', error);
                return [];
            }
        }

        // Show autocomplete results
        function showAutocomplete(input, resultsContainer, slotIndex) {
            input.addEventListener('input', async () => {
                const query = input.value.trim();
                if (query.length < 2) {
                    resultsContainer.innerHTML = '';
                    resultsContainer.classList.add('hidden');
                    return;
                }
                const funds = await fetchFunds(query);
                resultsContainer.innerHTML = funds.map(fund => `
                    <div class="autocomplete-item p-2 cursor-pointer hover:bg-gray-100" data-slot="${slotIndex}" data-scheme="${fund.scheme_code}" data-name="${fund.scheme_name}" data-house="${fund.fund_house}">
                        ${fund.scheme_name} (${fund.fund_house})
                    </div>
                `).join('');
                resultsContainer.classList.remove('hidden');
            });
        }

        // Handle slot interactions
        slots.forEach((slot, index) => {
            const addBtn = slot.querySelector('.add-btn');
            const searchInput = slot.querySelector('.search-input');
            const resultsContainer = slot.querySelector('.autocomplete-results');
            const fundDetails = slot.querySelector('.fund-details');
            const addFundDiv = slot.querySelector('.add-fund');
            const removeBtn = slot.querySelector('.remove-fund');

            addBtn.addEventListener('click', () => {
                // Check if max funds reached
                const assignedCount = slotAssignments.filter(code => code !== null).length;
                if (assignedCount >= 5) {
                    showAlert('Cannot add more than 5 funds for comparison.');
                    return;
                }
                // Show search input only for empty slots
                if (slotAssignments[index] === null) {
                    addBtn.classList.add('hidden');
                    searchInput.classList.remove('hidden');
                    searchInput.focus();
                    showAutocomplete(searchInput, resultsContainer, index);
                }
            });

            resultsContainer.addEventListener('click', (e) => {
                const item = e.target.closest('.autocomplete-item');
                if (item) {
                    const schemeCode = item.getAttribute('data-scheme');
                    const name = item.getAttribute('data-name');
                    const house = item.getAttribute('data-house');

                    // Check if fund is already selected
                    if (slotAssignments.includes(schemeCode)) {
                        showAlert('This fund is already selected.');
                        return;
                    }

                    // Find first empty slot
                    const emptySlotIndex = slotAssignments.indexOf(null);
                    if (emptySlotIndex === -1) {
                        showAlert('No empty slots available.');
                        return;
                    }

                    // Assign fund to first empty slot
                    slotAssignments[emptySlotIndex] = schemeCode;
                    const targetSlot = slots[emptySlotIndex];
                    const targetFundDetails = targetSlot.querySelector('.fund-details');
                    const targetAddFundDiv = targetSlot.querySelector('.add-fund');
                    targetFundDetails.dataset.schemeCode = schemeCode;
                    targetFundDetails.querySelector('.fund-name').textContent = name;
                    targetFundDetails.querySelector('.fund-house').textContent = house;
                    targetFundDetails.style.display = 'block';
                    targetAddFundDiv.style.display = 'none';

                    // Reset current slot’s input
                    searchInput.classList.add('hidden');
                    resultsContainer.classList.add('hidden');
                    if (slotAssignments[index] === null) {
                        addBtn.classList.remove('hidden');
                    }
                    updateCompareButton();
                }
            });

            removeBtn.addEventListener('click', () => {
                if (slotAssignments[index] !== null) {
                    slotAssignments[index] = null; // Clear slot
                    fundDetails.dataset.schemeCode = '';
                    fundDetails.style.display = 'none';
                    addFundDiv.style.display = 'block';
                    addBtn.classList.remove('hidden');
                    updateCompareButton();
                }
            });

            // Hide autocomplete on click outside
            document.addEventListener('click', (e) => {
                if (!slot.contains(e.target)) {
                    resultsContainer.classList.add('hidden');
                    searchInput.classList.add('hidden');
                    if (slotAssignments[index] === null) {
                        addBtn.classList.remove('hidden');
                    }
                }
            });
        });

        // Navigate to compare page
        compareBtn.addEventListener('click', () => {
            const assignedFunds = slotAssignments.map((code, index) => ({ code, index }))
                .filter(item => item.code !== null);
            if (assignedFunds.length >= 2) {
                const query = assignedFunds.map(item => `${encodeURIComponent(item.code).toLowerCase()}`).join('/');
                window.location.href = `/mutual-funds/compare/${query}`;
            }
            showCompareLoading('slots-container', 'Analyzing your selected mutual funds...');
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            await populateSlots(); // Fetch and populate funds
            initializeNavCompareChart();
            initializeSectorCharts();
            setupTimeFilters();
            updateCompareButton();
        });
    </script>

    <!-- Formatting in UI for jinja elements -->
    <script>
        // Function to format numbers in Indian style
        const parseNumber = (text) => {
            const match = text.match(/[\d,.]+/g);
            return match ? parseFloat(match.join('').replace(/,/g, '')) : NaN;
        };

        // Shared currency formatting function
        const formatCurrency = (value, isCrore = false) => {
            if (isNaN(value)) return null;
            return "₹ " + formatIndianNumber(Math.round(value)) + (isCrore ? " Cr" : "");
        };

        // Date formatting function
        const formatDate = (text) => {
            const parsedDate = new Date(text.trim());
            if (isNaN(parsedDate.getTime())) return null;
            return parsedDate.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        };

        // Process elements
        document.querySelectorAll('.crmoney').forEach(el => {
            const value = parseNumber(el.textContent);
            const formatted = formatCurrency(value, true);
            if (formatted) el.textContent = formatted;
        });
        document.querySelectorAll('.money').forEach(el => {
            const value = parseNumber(el.textContent);
            const formatted = formatCurrency(value);
            if (formatted) el.textContent = formatted;
        });
        document.querySelectorAll('.date').forEach(el => {
            const formatted = formatDate(el.textContent);
            if (formatted) el.textContent = formatted;
        });
    </script>

    {% with compare_charts="true" %}
    {% include 'mutual-funds/charts.html' %}
    {% endwith %}

    <script>
        function analyzeAllFunds() {
            if (!Array.isArray(fundsData) || fundsData.length === 0) {
                showAlert("No funds data available for analysis");
                return;
            }

            // Initialize an array to collect all payloads
            const payloads = [];

            fundsData.forEach(fundData => {
                if (!fundData) return;

                // Create a structured returns object similar to UI display
                const returns = {
                    performance: {
                        one_year: fundData.stats && fundData.stats[0] ? fundData.stats[0].stat_1y : null,
                        three_year: fundData.stats && fundData.stats[0] ? fundData.stats[0].stat_3y : null,
                        five_year: fundData.stats && fundData.stats[0] ? fundData.stats[0].stat_5y : null,
                        all_time: fundData.stats && fundData.stats[0] ? fundData.stats[0].stat_all : null
                    },
                    category_average: {
                        one_year: fundData.stats && fundData.stats[1] ? fundData.stats[1].stat_1y : null,
                        three_year: fundData.stats && fundData.stats[1] ? fundData.stats[1].stat_3y : null,
                        five_year: fundData.stats && fundData.stats[1] ? fundData.stats[1].stat_5y : null,
                        all_time: fundData.stats && fundData.stats[1] ? fundData.stats[1].stat_all : null
                    },
                    risk_metrics: {}
                };

                // Add risk metrics
                if (fundData.return_stats && fundData.return_stats.length > 0) {
                    const metrics = fundData.return_stats[0];
                    if (metrics.alpha !== undefined) returns.risk_metrics.alpha = metrics.alpha;
                    if (metrics.beta !== undefined) returns.risk_metrics.beta = metrics.beta;
                    if (metrics.sharpe_ratio !== undefined) returns.risk_metrics.sharpe_ratio = metrics.sharpe_ratio;
                    if (metrics.standard_deviation !== undefined) returns.risk_metrics.standard_deviation = metrics.standard_deviation;
                }

                // Prepare payload for AI analysis with the fund data
                const payload = {
                    scheme_name: fundData.scheme_name,
                    fund_house: fundData.fund_house,
                    category: fundData.category,
                    scheme_type: fundData.scheme_type,
                    scheme_plan: fundData.scheme_plan,
                    scheme_option: fundData.scheme_option,
                    aum: fundData.aum,
                    expense_ratio: fundData.expense_ratio,
                    nav: fundData.nav,
                    launch_date: fundData.launch_date,
                    min_investment: fundData.min_investment,
                    risk_level: fundData.risk_level,
                    fund_manager: fundData.fund_manager,
                    exit_load: fundData.exit_load,
                    holdings: fundData.holdings
                        ? fundData.holdings.slice(0, 10).map(holding => ({
                            company_name: holding.company_name,
                            sector_name: holding.sector_name,
                            instrument_name: holding.instrument_name,
                            corpus_per: holding.corpus_per
                        }))
                        : [], // Limit to top 10 holdings
                    returns: returns // Use structured returns object instead of arrays
                };

                // Add the payload to the payloads array
                payloads.push(payload);
            });

            // Call the AI feedback function with the array of payloads
            if (payloads.length > 0) {
                getAIFeedback({ funds: payloads }, { analysisType: 'compare_fund' });
            }
        }

        initializeCardsCarousel('recommended-funds-cards', 'recommended-left-arrow', 'recommended-right-arrow');
    </script>

    {% include 'blocks/footer.html' %}
</body>

</html>