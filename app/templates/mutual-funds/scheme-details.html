<!DOCTYPE html>
<html lang="en">

<head>
    <title>{{ fund_data.scheme_name | default('Fund Scheme') }} | {{ fund_data.fund_house | default('Fund House') }}
    </title>
    <meta name="description"
        content="View detailed information about {{ fund_data.scheme_name | default('this mutual fund') }}, including NAV history, performance metrics, holdings, sector allocation, expense ratio, and peer comparisons. Investments are subject to market risks; read the offer document for details on short term and long term capital gains." />
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://theaibull.com/mutual-funds/{{ scheme_code }}" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/mutual-funds/{{ scheme_code }}" />
    <meta property="og:title" content="{{ fund_data.scheme_name | default('Fund Details') }} Details" />
    <meta property="og:description"
        content="Detailed information about {{ fund_data.scheme_name | default('this mutual fund') }}, including NAV history, performance, and more. Investments are subject to market risks; read about short term and long term capital gains." />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="{{ fund_data.scheme_name | default('Fund Details') }} Details" />
    <meta name="twitter:description"
        content="Explore detailed information for {{ fund_data.scheme_name | default('this mutual fund') }}. Subject to market risks, with details on short term and long term capital gains." />
    {% include 'blocks/head.html' %}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chartjs-plugin-zoom/2.0.1/chartjs-plugin-zoom.min.js"></script>
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">
    {% include 'blocks/common.html' %}
    {% include 'blocks/info.html' %}
    {% include 'blocks/ai-feedback.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col justify-between min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb) relative">
        <div class="p-3 mx-auto max-w-7xl w-full">
            <div class="mx-auto p-1">
                <!-- Header -->
                <div class="bg-white rounded-2xl mb-6 transition-all duration-300 sticky top-[36px] z-10">
                    <div
                        class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6  px-8 py-5 ">
                        <div>
                            <div class="flex gap-3">
                                <img src="{{ fund_data.logo_url | default('https://via.placeholder.com/48') }}"
                                    alt="{{ fund_data.scheme_name | default('Fund') }} logo"
                                    class="dark:border-gray-700 h-[5rem] object-cover rounded-lg shadow-md w-[5rem]">
                                <div>
                                    <div class="flex items-center gap-3">
                                        <h1 id="details-title"
                                            class="font-semibold text-2xl leading-snug text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-pink-600 to-purple-600">
                                            {{ fund_data.scheme_name | default('Fund Details') }}
                                        </h1>
                                        <button id="fundAiAnalysisBtn" onclick="getFundAiAnalysis()"
                                            class="animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-sparkles">
                                                <path
                                                    d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z">
                                                </path>
                                                <path d="M20 3v4"></path>
                                                <path d="M22 5h-4"></path>
                                                <path d="M4 17v2"></path>
                                                <path d="M5 18H3"></path>
                                            </svg> Get AI Analysis
                                        </button>
                                    </div>
                                    <div id="fund-meta"
                                        class="flex flex-wrap mt-3 items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
                                        {% if fund_data.rating %}
                                        <span id="fund-rating"
                                            class="bg-yellow-50 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 px-3 py-1 rounded-md font-medium">
                                            <div class="flex items-center space-x-2">
                                                {% set rating = fund_data.rating | default(0) | round(0, 'floor') %}
                                                <!-- Star Count -->
                                                <span class="text-sm font-semibold text-gray-800 dark:text-gray-200">
                                                    Aggregate Rating: {{
                                                    "%.1f"|format(fund_data.rating)|replace('.0', '') }}/5
                                                </span>
                                                <div class="flex items-center">
                                                    {% for i in range(1, 6) %}
                                                    {% if i <= rating %} <!-- Filled Star -->
                                                        <svg class="w-4 h-4 text-yellow-400" fill="currentColor"
                                                            viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                                            </path>
                                                        </svg>
                                                        {% else %}
                                                        <!-- Empty Star -->
                                                        <svg class="w-4 h-4 text-gray-300 dark:text-gray-600"
                                                            fill="currentColor" viewBox="0 0 20 20"
                                                            xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                                            </path>
                                                        </svg>
                                                        {% endif %}
                                                        {% endfor %}
                                                </div>


                                            </div>
                                        </span>
                                        {% endif %}
                                        <span id="fund-category"
                                            class="bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-3 py-1 rounded-md font-medium">
                                            {{ fund_data.category | default('N/A') }}
                                        </span>
                                        <span id="fund-sub-category"
                                            class="bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-300 px-3 py-1 rounded-md font-medium ml-1">
                                            {{ fund_data.sub_category | default('N/A') }}
                                        </span>



                                    </div>

                                </div>
                            </div>

                        </div>

                        <a href="/mutual-funds"
                            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm flex items-center font-medium transition-colors duration-200 mb-16">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Back to Screener
                        </a>
                    </div>
                    <!-- Navigation Shortcuts -->
                    <div id="nav-shortcuts" class="border-t flex flex-wrap gap-2 py-4 px-8">
                        {% for link in [
                        ['#overview', 'Overview'],
                        ['#performance', 'Performance'],
                        ['#holdings-table', 'Holdings'],
                        ['#sip-stp-swp', 'SIP/STP/SWP'],
                        ['#peers', 'Peer Comparison'],
                        ['#fund-manager', 'Fund Manager'],
                        ['#compare-mutual-funds', 'Compare Funds'],
                        ['#faqs', 'FAQs']
                        ] %}
                        <a href="{{ link[0] }}"
                            class="text-xs text-blue-600 hover:text-white bg-blue-100 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-400 px-3 py-1.5 rounded-md transition-all duration-200 font-medium shadow-sm">
                            {{ link[1] }}
                        </a>
                        {% endfor %}
                    </div>
                </div>

                <main>
                    <div id="details-content">
                        <!-- Fund details -->
                        <div id="fund-details">
                            <!-- Overview -->
                            <div id="overview"
                                class="bg-white relative rounded-2xl p-8 mb-6 transition-all duration-300 scroll-mt-64">
                                <div class="mb-8">
                                    <h4
                                        class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b pb-2 border-gray-200 dark:border-gray-700">
                                        Overview</h4>
                                    <div class="flex items-center mb-6">

                                        <div>
                                            <h5 class="text-xl font-semibold text-gray-900 dark:text-gray-100">{{
                                                fund_data.scheme_name | default('N/A') }}
                                            </h5>
                                            <p class="text-sm text-gray-400 dark:text-gray-400 my-1">{{
                                                fund_data.fund_house | default('N/A') }}</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-300">{{ fund_data.description
                                                | default('No description
                                                available.') }}</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-300 mt-2">Investments in {{
                                                fund_data.scheme_name | default('this
                                                mutual fund') }} are subject to market risks. Please read the offer
                                                document carefully before investing to
                                                understand the potential market risks and rewards.</p>
                                        </div>
                                    </div>



                                    <div>
                                        <div
                                            class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900/30 dark:to-blue-900/30 p-4 border-b border-gray-100 dark:border-gray-700">
                                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                                <div
                                                    class="bg-white dark:bg-gray-700 p-4 rounded-lg  transition-shadow duration-200">
                                                    <p
                                                        class="text-xs uppercase font-semibold text-gray-500 dark:text-gray-400 mb-1">
                                                        Latest NAV</p>
                                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{%
                                                        if
                                                        fund_data.nav %}₹ {{ fund_data.nav |
                                                        float | round(4) }}{% else %}N/A{% endif %}</p>
                                                </div>

                                                <div
                                                    class="bg-white dark:bg-gray-700 p-4 rounded-lg  transition-shadow duration-200">
                                                    <p
                                                        class="text-xs uppercase font-semibold text-gray-500 dark:text-gray-400 mb-1">
                                                        Fund Size (AUM)</p>
                                                    <p
                                                        class="crmoney text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {% if
                                                        fund_data.aum %}₹{{ fund_data.aum |
                                                        float | round(2) }} Cr{% else %}N/A{% endif %}</p>
                                                </div>

                                                <div
                                                    class="bg-white dark:bg-gray-700 p-4 rounded-lg  transition-shadow duration-200">
                                                    <p
                                                        class="text-xs uppercase font-semibold text-gray-500 dark:text-gray-400 mb-1">
                                                        Risk</p>
                                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{
                                                        fund_data.return_stats[0].risk | default('N/A') }} <span
                                                            class="text-gray-500 dark:text-gray-400">(Risk rating: {{
                                                            fund_data.return_stats[0].risk_rating | default('N/A')
                                                            }}/10)</span></p>
                                                </div>

                                                <div
                                                    class="bg-white dark:bg-gray-700 p-4 rounded-lg transition-shadow duration-200">
                                                    <p
                                                        class="text-xs uppercase font-semibold text-gray-500 dark:text-gray-400 mb-1">
                                                        Minimum Investment</p>
                                                    <div class="grid grid-cols-1 md:grid-cols-2 align-right">
                                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                            <span
                                                                class="text-gray-500 dark:text-gray-400 text-xs">Lumpsum:</span>
                                                            ₹ {{
                                                            fund_data.min_investment_amount | default('N/A') }}
                                                        </p>
                                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                            <span
                                                                class="text-gray-500 dark:text-gray-400 text-xs">SIP:</span>
                                                            ₹{{
                                                            fund_data.min_sip_investment
                                                            | default('N/A') }}
                                                        </p>
                                                    </div>
                                                </div>

                                                <div
                                                    class="bg-white dark:bg-gray-700 p-4 rounded-lg transition-shadow duration-200">
                                                    <p
                                                        class="text-xs uppercase font-semibold text-gray-500 dark:text-gray-400 mb-1">
                                                        Launch Date</p>
                                                    <p
                                                        class="date text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {{
                                                        fund_data.launch_date | default('N/A') }}
                                                    </p>
                                                </div>

                                                <div
                                                    class="bg-white dark:bg-gray-700 p-4 rounded-lg transition-shadow duration-200">
                                                    <p
                                                        class="text-xs uppercase font-semibold text-gray-500 dark:text-gray-400 mb-1">
                                                        Expense Ratio</p>
                                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{%
                                                        if
                                                        fund_data.expense_ratio %}{{
                                                        fund_data.expense_ratio }}%{% else %}N/A{% endif %}</p>
                                                </div>

                                                <div
                                                    class="bg-white dark:bg-gray-700 p-4 rounded-lg transition-shadow duration-200">
                                                    <p
                                                        class="text-xs uppercase font-semibold text-gray-500 dark:text-gray-400 mb-1">
                                                        Exit Load</p>
                                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{
                                                        fund_data.exit_load | default('N/A') }}
                                                    </p>
                                                </div>

                                                <div
                                                    class="bg-white dark:bg-gray-700 p-4 rounded-lg transition-shadow duration-200">
                                                    <p
                                                        class="text-xs uppercase font-semibold text-gray-500 dark:text-gray-400 mb-1">
                                                        Benchmark</p>
                                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{
                                                        fund_data.benchmark_name | default('N/A')
                                                        }}</p>
                                                </div>

                                                <div
                                                    class="bg-white dark:bg-gray-700 p-4 rounded-lg transition-shadow duration-200">
                                                    <p
                                                        class="text-xs uppercase font-semibold text-gray-500 dark:text-gray-400 mb-1">
                                                        Standard Deviation</p>
                                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{%
                                                        if
                                                        fund_data.return_stats and
                                                        fund_data.return_stats[0].standard_deviation %}{{
                                                        fund_data.return_stats[0].standard_deviation
                                                        | float
                                                        | round(2) }}{% else
                                                        %}N/A{% endif %}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <!-- NAV Graph -->
                                    <div id="details-graph"
                                        class="{% if not fund_data.nav_history and not fund_data.historic_fund_expense %}hidden{% endif %} bg-white dark:bg-gray-800">
                                        <div class="flex items-center justify-between mb-2">
                                            <h4
                                                class="text-lg font-semibold text-gray-900 dark:text-gray-100 pr-4 mb-2">
                                                History
                                            </h4>
                                            <div class="flex space-x-4">
                                                <button id="nav-tab"
                                                    class="tab-button pb-2 text-sm font-medium text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 pb-2 transition-all duration-200">NAV
                                                </button>
                                                <button id="expense-tab"
                                                    class="tab-button pb-2 text-sm font-medium text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-blue-600 hover:border-blue-600 dark:hover:text-blue-400 dark:hover:border-blue-400 pb-2 transition-all duration-200">Expense</button>
                                            </div>
                                        </div>
                                        <div>
                                            <div
                                                class="rounded-xl bg-gradient-to-bl from-blue-50 via-gray-50 to-gray-50 dark:from-gray-900/30 dark:to-blue-900/30 p-6 border-b border-gray-100 dark:border-gray-700">
                                                <div class="flex justify-center gap-2 mb-4" id="time-filter-buttons">
                                                    <button id="time-1m"
                                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200 active">1M</button>
                                                    <button id="time-6m"
                                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">6M</button>
                                                    <button id="time-1y"
                                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">1Y</button>
                                                    <button id="time-3y"
                                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">3Y</button>
                                                    <button id="time-5y"
                                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">5Y</button>
                                                    <button id="time-all"
                                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">All</button>
                                                </div>
                                                <div class="h-80">
                                                    <canvas id="nav-chart"></canvas>
                                                </div>
                                                <div id="zoom-options" class="flex justify-center gap-1 mt-4">
                                                    <button id="reset-zoom-btn"
                                                        class="text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-black hover:text-white dark:hover:bg-blue-600 px-3 py-1 rounded-md transition-all duration-200">Reset
                                                        View</button>
                                                    <button id="zoom-in-btn"
                                                        class="text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-green-600 hover:text-white dark:hover:bg-blue-400 px-3 py-1  rounded-md transition-all duration-200">Zoom
                                                        In</button>
                                                    <button id="zoom-out-btn"
                                                        class="text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-red-600 hover:text-white dark:hover:bg-blue-600 px-3 py-1  rounded-md transition-all duration-200">Zoom
                                                        Out</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <!-- Pros and Cons -->
                                <div id="pros-cons" class="mb-8 hidden">
                                    {% if fund_data.analysis %}
                                    <h4
                                        class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b pb-2 border-gray-200 dark:border-gray-700">
                                        Pros and Cons
                                    </h4>
                                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        <!-- Pros -->
                                        <div
                                            class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg bg-gray-50 dark:bg-gray-700 p-4 rounded-lg hover:shadow-md transition-shadow duration-200">
                                            <h5 class="text-md font-semibold text-green-600 dark:text-green-400 mb-3">
                                                Pros
                                            </h5>
                                            <ul class="list-none space-y-2">
                                                {% for item in fund_data.analysis if item.analysis_type == 'PROS' %}
                                                <li class="flex items-start">
                                                    <span class="text-green-500 mr-2">✅</span>
                                                    <span class="text-sm text-gray-900 dark:text-gray-100">{{
                                                        item.analysis_desc }}</span>
                                                </li>
                                                {% else %}
                                                <li class="text-sm text-gray-600 dark:text-gray-400">No notable
                                                    advantages
                                                    available.</li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                        <!-- Cons -->
                                        <div
                                            class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg bg-gray-50 dark:bg-gray-700 p-4 rounded-lg hover:shadow-md transition-shadow duration-200">
                                            <h5 class="text-md font-semibold text-red-600 dark:text-red-400 mb-3">Cons
                                            </h5>
                                            <ul class="list-none space-y-2">
                                                {% for item in fund_data.analysis if item.analysis_type == 'CONS' %}
                                                <li class="flex items-start">
                                                    <span class="text-red-500 mr-2">❌</span>
                                                    <span class="text-sm text-gray-900 dark:text-gray-100">{{
                                                        item.analysis_desc }}</span>
                                                </li>
                                                {% else %}
                                                <li class="text-sm text-gray-600 dark:text-gray-400">No notable
                                                    disadvantages available.</li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div id="performance"
                                class="bg-white rounded-2xl p-8 mb-6 transition-all duration-300 relative scroll-mt-64">
                                <div
                                    class="grid grid-cols-1 md:grid-cols-2 gap-y-2 md:gap-x-4 items-start justify-between">
                                    <!-- Returns -->
                                    <div id="returns">
                                        <h4
                                            class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b pb-2 border-gray-200 dark:border-gray-700">
                                            Performance &amp; Ratios</h4>

                                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                                            {% set returns = [
                                            {'label': '1-Year SIP Return', 'value': fund_data.sip_return.return1y if
                                            fund_data.sip_return else None},
                                            {'label': '3-Year SIP Return', 'value': fund_data.sip_return.return3y if
                                            fund_data.sip_return else None},
                                            {'label': '5-Year SIP Return', 'value': fund_data.sip_return.return5y if
                                            fund_data.sip_return else None},
                                            {'label': 'Mean Return', 'value': fund_data.return_stats[0].mean_return if
                                            fund_data.return_stats else None},
                                            {'label': 'Alpha', 'value': fund_data.return_stats[0].alpha if
                                            fund_data.return_stats else None},
                                            {'label': 'Beta', 'value': fund_data.return_stats[0].beta if
                                            fund_data.return_stats else None},
                                            {'label': 'Sharpe Ratio', 'value': fund_data.return_stats[0].sharpe_ratio if
                                            fund_data.return_stats else None},
                                            {'label': 'Sortino Ratio', 'value': fund_data.return_stats[0].sortino_ratio
                                            if
                                            fund_data.return_stats else None},
                                            {'label': 'Information Ratio', 'value':
                                            fund_data.return_stats[0].information_ratio if fund_data.return_stats else
                                            None}
                                            ] %}
                                            {% for item in returns %}
                                            {% set color_class = 'bg-gray-100 dark:bg-gray-700' %}
                                            {% if item.value is not none and item.value|float > 0 %}
                                            {% set color_class = 'bg-green-100 dark:bg-green-800' %}
                                            {% elif item.value is not none and item.value|float < 0 %} {% set
                                                color_class='bg-red-100 dark:bg-red-800' %} {% elif item.value is not
                                                none and item.value|float==0 %} {% set
                                                color_class='bg-yellow-100 dark:bg-yellow-800' %} {% endif %} <div
                                                class="{{ color_class }} p-4 rounded-lg">
                                                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                                                    item.label }}
                                                </p>
                                                <p class="text-sm text-gray-900 dark:text-gray-100 font-semibold">
                                                    {% if item.value is not none %}
                                                    {% if item.value == 0 %}
                                                    -
                                                    {% elif 'Return' in item.label or 'Deviation' in item.label %}
                                                    {{ item.value | float | round(2) }}%
                                                    {% else %}
                                                    {{ item.value | float | round(2) }}
                                                    {% endif %}
                                                    {% else %}
                                                    N/A
                                                    {% endif %}
                                                </p>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% if fund_data.stats or fund_data.simple_return %}
                                <div>
                                    <div class="flex items-center justify-between mb-4">
                                        <h5 class="text-lg font-semibold text-gray-900 dark:text-gray-100 pb-2">Returns
                                        </h5>
                                        <div class="flex space-x-4">
                                            <button id="annualised-returns-tab"
                                                class="tab-button text-xs font-medium text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 pb-1 transition-all duration-200 ">
                                                Annualised
                                            </button>
                                            <button id="absolute-returns-tab"
                                                class="tab-button text-xs font-medium text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-blue-600 hover:border-blue-600 dark:hover:text-blue-400 dark:hover:border-blue-400 pb-2 transition-all duration-200 pb-1">
                                                Absolute
                                            </button>
                                        </div>
                                    </div>
                                    <div class="overflow-x-auto">
                                        <table class="w-full text-sm text-left text-gray-700 dark:text-gray-300"
                                            id="returns-table">
                                            <thead class="text-xs uppercase bg-gray-100 dark:bg-gray-700">
                                                <tr
                                                    class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900/30 dark:to-blue-900/30 p-6 border-b border-gray-100 dark:border-gray-700">
                                                    <th class="px-4 py-3"></th>
                                                    <th class="px-4 py-3">1Y</th>
                                                    <th class="px-4 py-3">3Y</th>
                                                    <th class="px-4 py-3">5Y</th>
                                                    <th class="px-4 py-3">All Time</th>
                                                </tr>
                                            </thead>
                                            <tbody id="returns-table-body">
                                                <!-- Annualised Returns (default) -->
                                                <tr class="border-b dark:border-gray-700">
                                                    <td class="px-4 py-3 font-medium">Fund Returns</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[0].stat_1y %}{{
                                                        fund_data.stats[0].stat_1y | float |
                                                        round(2) }}%{% else %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[0].stat_3y %}{{
                                                        fund_data.stats[0].stat_3y | float |
                                                        round(2) }}%{% else %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[0].stat_5y %}{{
                                                        fund_data.stats[0].stat_5y | float |
                                                        round(2) }}%{% else %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[0].stat_all %}{{
                                                        fund_data.stats[0].stat_all | float
                                                        | round(2) }}%{% else %}N/A{% endif %}</td>
                                                </tr>
                                                <tr class="border-b dark:border-gray-700">
                                                    <td class="px-4 py-3 font-medium">Category Average</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[1].stat_1y %}{{
                                                        fund_data.stats[1].stat_1y | float |
                                                        round(2) }}%{% else %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[1].stat_3y %}{{
                                                        fund_data.stats[1].stat_3y | float |
                                                        round(2) }}%{% else %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[1].stat_5y %}{{
                                                        fund_data.stats[1].stat_5y | float |
                                                        round(2) }}%{% else %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[1].stat_all %}{{
                                                        fund_data.stats[1].stat_all | float
                                                        | round(2) }}%{% else %}N/A{% endif %}</td>
                                                </tr>
                                                <tr class="border-b dark:border-gray-700">
                                                    <td class="px-4 py-3 font-medium">Rank Within Category</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[2].stat_1y %}{{
                                                        fund_data.stats[2].stat_1y }}{% else
                                                        %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[2].stat_3y %}{{
                                                        fund_data.stats[2].stat_3y }}{% else
                                                        %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[2].stat_5y %}{{
                                                        fund_data.stats[2].stat_5y }}{% else
                                                        %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{% if fund_data.stats[2].stat_all %}{{
                                                        fund_data.stats[2].stat_all }}{%
                                                        else %}N/A{% endif %}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="">
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <!-- Holdings Table -->
                        <div class="bg-white rounded-2xl p-8 transition-all duration-300">
                            <div id="holdings-table" class="mb-6 scroll-mt-64">
                                {% if fund_data.holdings %}
                                <h4
                                    class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b pb-2 border-gray-200 dark:border-gray-700">
                                    Holdings ({{ fund_data.holdings | length }})
                                </h4>
                                <div class="overflow-hidden border rounded-lg">
                                    <div class="overflow-x-auto mb-4">
                                        <table class="w-full text-sm text-left text-gray-700 dark:text-gray-300">
                                            <thead class="text-xs uppercase bg-gray-100 dark:bg-gray-700">
                                                <tr
                                                    class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900/30 dark:to-blue-900/30 p-6 border-b border-gray-100 dark:border-gray-700">
                                                    <th class="px-4 py-3">Company</th>
                                                    <th class="px-4 py-3">Sector</th>
                                                    <th class="px-4 py-3">Instrument</th>
                                                    <th class="px-4 py-3">Assets</th>
                                                </tr>
                                            </thead>
                                            <tbody id="holdings-body">
                                                {% for holding in fund_data.holdings[:10] %}
                                                <tr
                                                    class="border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                                                    <td class="px-4 py-3">{{ holding.company_name | default('N/A') }}
                                                    </td>
                                                    <td class="px-4 py-3">{{ holding.sector_name | default('N/A') }}
                                                    </td>
                                                    <td class="px-4 py-3">{{ holding.instrument_name | default('N/A') }}
                                                    </td>
                                                    <td class="px-4 py-3 text-left">
                                                        {% if holding.corpus_per %}{{ holding.corpus_per | float |
                                                        round(2)
                                                        }}%{% else %}N/A{% endif
                                                        %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="4">
                                                        <button id="show-more-btn"
                                                            class="px-4 py-2 text-blue-500 rounded hover:underline">
                                                            Show More
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                                {% if fund_data.holdings|length > 10 %}
                                {% endif %}
                                {% endif %}
                            </div>
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                <!-- Allocation Charts with Tabs -->
                                <div id="allocation-charts"
                                    class="{% if not fund_data.holdings %}hidden{% endif %} bg-gradient-to-r border-b border-gray-100 dark:border-gray-700 dark:from-gray-900/30 dark:to-blue-900/30 from-gray-50 p-6 rounded-xl to-blue-50">
                                    <div class="flex items-center justify-between mb-4">
                                        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 pr-4 pb-2">
                                            Holdings
                                            Analysis
                                        </h4>
                                        <div class="flex space-x-4">
                                            <button id="sector-tab"
                                                class="tab-button text-sm font-medium text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 pb-2 transition-all duration-200">Sector
                                            </button>
                                            <button id="instrument-tab"
                                                class="tab-button text-sm font-medium text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-blue-600 hover:border-blue-600 dark:hover:text-blue-400 dark:hover:border-blue-400 pb-2 transition-all duration-200">Instrument</button>
                                        </div>
                                    </div>
                                    <div class="overflow-hidden">
                                        <div>
                                            <div class="h-80">
                                                <canvas id="allocation-chart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Combined Comparison Chart with Tabs -->
                                <div id="comparison-charts"
                                    class="rounded-xl bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900/30 dark:to-blue-900/30 p-6 border-b border-gray-100 dark:border-gray-700">
                                    <div class="flex items-center justify-between mb-4">
                                        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                            Peer
                                            Comparison
                                        </h4>
                                        <div class="flex space-x-4">
                                            <button id="returns-tab"
                                                class="tab-button text-sm font-medium text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 pb-2 transition-all duration-200">Returns</button>
                                            <button id="expense-comparison-tab"
                                                class="tab-button text-sm font-medium text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-blue-600 hover:border-blue-600 dark:hover:text-blue-400 dark:hover:border-blue-400 pb-2 transition-all duration-200">Expense
                                                Ratio</button>
                                        </div>
                                    </div>
                                    <div>
                                        <div>
                                            <div class="h-80">
                                                <canvas id="peer-comparison-chart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Peer Comparison -->
                            <div id="peers" class="mt-6 scroll-mt-64">
                                {% if fund_data.peerComparison %}
                                <h4
                                    class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b pb-2 border-gray-200 dark:border-gray-700">
                                    Peer Comparison</h4>
                                <div class="border rounded-xl verflow-hidden">
                                    <div class="overflow-x-auto">
                                        <table class="w-full text-sm text-left text-gray-700 dark:text-gray-300">
                                            <thead class="text-xs uppercase bg-gray-100 dark:bg-gray-700">
                                                <tr
                                                    class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900/30 dark:to-blue-900/30 p-6 border-b border-gray-100 dark:border-gray-700">
                                                    <th class="px-4 py-3">Fund Name</th>
                                                    <th class="px-4 py-3">AUM (₹ Cr)</th>
                                                    <th class="px-4 py-3">1Y Return (%)</th>
                                                    <th class="px-4 py-3">3Y Return (%)</th>
                                                    <th class="px-4 py-3">Expense Ratio (%)</th>
                                                    <th class="px-4 py-3">Rating</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for peer in fund_data.peerComparison %}
                                                <tr
                                                    class="border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors {% if peer.scheme_code == scheme_code %}font-bold bg-gray-100 dark:bg-gray-600{% endif %}">
                                                    <td class="px-4 py-3"><a href="/mutual-funds/{{ peer.search_id }}"
                                                            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">{{
                                                            peer.scheme_name | default('N/A') }}</a></td>
                                                    <td class="money px-4 py-3">{% if peer.aum %}{{ peer.aum | float |
                                                        round(2)
                                                        }}{%
                                                        else %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{% if peer.return1y %}{{ peer.return1y | float
                                                        |
                                                        round(2) }}{% else %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{% if peer.return3y %}{{ peer.return3y | float
                                                        |
                                                        round(2) }}{% else %}N/A{% endif %}</td>
                                                    <td class="px-4 py-3">{{ peer.expense_ratio | default('N/A') }}</td>
                                                    <td class="px-4 py-3">{{ peer.rating | default('N/A') }}{% if
                                                        peer.rating %}/5{% endif %}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- SIP/STP/SWP -->
                        <div id="sip-stp-swp"
                            class="mb-6 mb-8 mt-8 p-6 p-8 rounded-2xl to-blue-50 transition-all scroll-mt-64" style="
    background-image: radial-gradient(circle at 100% 0, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #faf4f4, #f5f5f500, #f1f9ff);
">
                            <h4
                                class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b pb-2 border-gray-200 dark:border-gray-700">
                                SIP, STP & SWP
                                Details</h4>
                            <div>
                                <div>
                                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg">
                                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Systematic
                                                Investment Plan (SIP):</p>
                                            {% if fund_data.sip_allowed %}
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Frequency:
                                                {{ fund_data.frequency |
                                                default('Monthly') }}</p>
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Min Amount:
                                                <span class="font-semibold text-blue-600 dark:text-blue-400">₹ {{
                                                    fund_data.min_sip_investment | default('N/A')
                                                    }}</span>
                                            </p>
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Multiplier:
                                                {{
                                                fund_data.sip_multiplier | default('N/A') }}</p>

                                            <div class="mt-4 hidden">
                                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                                                    Investment Date Configurations:</p>
                                                <ul class="space-y-3">
                                                    {% for config in fund_data.investment_date_configs %}
                                                    {% if config.sip_start_date_time and config.sip_end_date_time and
                                                    config.lumpsum_start_date_time and config.lumpsum_end_date_time %}
                                                    <li
                                                        class="p-3 bg-white dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                                                        <p
                                                            class="text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 mb-1">
                                                            Scheme Type</p>
                                                        <p class="text-sm font-medium mb-2">{{ config.scheme_type |
                                                            default('N/A') }}</p>

                                                        <div class="grid grid-cols-2 gap-2">
                                                            <div>
                                                                <p
                                                                    class="text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400">
                                                                    SIP Start</p>
                                                                <p class="date text-sm">{{ config.sip_start_date_time |
                                                                    default('N/A') }}</p>
                                                            </div>
                                                            <div>
                                                                <p
                                                                    class="text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400">
                                                                    SIP End</p>
                                                                <p class="date text-sm">{{ config.sip_end_date_time |
                                                                    default('N/A') }}</p>
                                                            </div>
                                                            <div>
                                                                <p
                                                                    class="text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400">
                                                                    Lumpsum Start</p>
                                                                <p class="date text-sm">{{
                                                                    config.lumpsum_start_date_time |
                                                                    default('N/A') }}</p>
                                                            </div>
                                                            <div>
                                                                <p
                                                                    class="text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400">
                                                                    Lumpsum End</p>
                                                                <p class="date text-sm">{{ config.lumpsum_end_date_time
                                                                    |
                                                                    default('N/A') }}</p>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    {% endif %}
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                            {% else %}
                                            <p class="text-sm text-gray-900 dark:text-gray-100">SIP Not Available for
                                                investment</p>
                                            {% endif %}
                                        </div>
                                        <div class="bg-white  p-4 rounded-lg space-y-2">
                                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Systematic Transfer Plan (STP):</p>
                                            {% if fund_data.stp_details %}
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Frequency:
                                                {{ fund_data.stp_details.stp_frequency |
                                                default('N/A') }}</p>
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Dates:
                                                {% if fund_data.stp_details.stp_dates and
                                                fund_data.stp_details.stp_dates|length == 30 %}
                                                <span class="font-semibold text-blue-600 dark:text-blue-400">Any day of
                                                    the month</span>
                                                {% else %}
                                                <span class="font-semibold text-blue-600 dark:text-blue-400">{{
                                                    fund_data.stp_details.stp_dates | join(', ') |
                                                    default('N/A') }}</span>
                                                {% endif %}
                                            </p>
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Min Amount:
                                                <span class="font-semibold text-blue-600 dark:text-blue-400">₹ {{
                                                    fund_data.stp_details.stp_in_minimum_installment_amount |
                                                    default('N/A') }}</span>
                                            </p>
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Min
                                                Installments: {{
                                                fund_data.stp_details.stp_minimum_installment_numbers | default('N/A')
                                                }}</p>
                                            {% else %}
                                            <p class="text-sm italic text-gray-500 dark:text-gray-400">Not Available</p>
                                            {% endif %}
                                        </div>

                                        <div class="bg-white  p-4 rounded-lg space-y-2">
                                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Systematic Withdrawal Plan (SWP):</p>
                                            {% if fund_data.swp_details %}
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Frequency:
                                                {{ fund_data.swp_details.swp_frequency |
                                                default('N/A') }}</p>
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Dates:
                                                {% if fund_data.swp_details.swp_dates and
                                                fund_data.swp_details.swp_dates|length == 30 %}
                                                <span class="font-semibold text-blue-600 dark:text-blue-400">Any day of
                                                    the month</span>
                                                {% else %}
                                                <span class="font-semibold text-blue-600 dark:text-blue-400">{{
                                                    fund_data.swp_details.swp_dates | join(', ') |
                                                    default('N/A') }}</span>
                                                {% endif %}
                                            </p>
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Min Amount:
                                                <span class="font-semibold text-blue-600 dark:text-blue-400">₹ {{
                                                    fund_data.swp_details.swp_minimum_installment_amount |
                                                    default('N/A') }}</span>
                                            </p>
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Min
                                                Installments: {{
                                                fund_data.swp_details.swp_minimum_installment_numbers | default('N/A')
                                                }}</p>
                                            {% else %}
                                            <p class="text-sm italic text-gray-500 dark:text-gray-400">Not Available</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- AMC Details -->
                        <div class="mb-6 bg-white rounded-2xl p-8 transition-all duration-300">
                            <h4
                                class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b pb-2 border-gray-200 dark:border-gray-700">
                                AMC Details
                            </h4>

                            <div>
                                <div
                                    class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900/30 dark:to-blue-900/30 p-6 border-b border-gray-100 dark:border-gray-700">
                                    <div class="flex items-center">
                                        <div class="bg-gray-100 dark:bg-gray-800 p-3 rounded-full mr-4">
                                            <svg class="w-8 h-8 text-gray-600 dark:text-gray-300" fill="none"
                                                stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                                    d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                                </path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Asset
                                                Management Company</p>
                                            <p class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{
                                                fund_data.amc_info.name |
                                                default('N/A') }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="py-6">
                                    <div class="mb-6">
                                        <p class="text-sm text-gray-900 dark:text-gray-300">{{
                                            fund_data.amc_info.description | default('No description
                                            available.') }} {{ fund_data.amc_info.name | default('The AMC') }} employs
                                            robust strategies to manage market risks,
                                            but investments remain subject to market risks. Please read the fund’s
                                            documentation for detailed risk management
                                            policies.</p>
                                        <div
                                            class="text-sm text-gray-600 dark:text-gray-400 mt-4 prose dark:prose-invert">
                                            {{
                                            fund_data.amc_info.more_description | safe }}</div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg space-y-1">
                                            <p
                                                class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                Launch
                                                Date</p>
                                            <p class="date text-sm text-gray-900 mt-1 dark:text-gray-300">{{
                                                fund_data.amc_info.launch_date |
                                                default('N/A') }}</p>
                                        </div>

                                        <div class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg space-y-1">
                                            <p
                                                class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                Total
                                                AUM</p>
                                            <p class="crmoney text-sm text-gray-900 mt-1 dark:text-gray-300">
                                                {% if fund_data.amc_info.aum %}
                                                ₹ {{ fund_data.amc_info.aum | float | round(2) }} Cr
                                                {% else %}
                                                N/A
                                                {% endif %}
                                            </p>
                                        </div>

                                        <div class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg space-y-1">
                                            <p
                                                class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                Address
                                            </p>
                                            <p class="text-sm text-gray-900 mt-1 dark:text-gray-300">{{
                                                fund_data.amc_info.address | default('N/A')
                                                }}</p>
                                        </div>

                                        <div class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg space-y-1">
                                            <p
                                                class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                Phone
                                            </p>
                                            <p class="text-sm text-gray-900 mt-1 dark:text-gray-300">{{
                                                fund_data.amc_info.phone | default('N/A') }}
                                            </p>
                                        </div>

                                        <div class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg space-y-1">
                                            <p
                                                class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                Website
                                            </p>
                                            <p class="text-sm text-gray-900 mt-1 dark:text-gray-300">
                                                {% if fund_data.amc_info.vro_website %}
                                                <a href="{{ fund_data.amc_info.vro_website }}"
                                                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200 flex items-center"
                                                    target="_blank">
                                                    {{ fund_data.amc_info.vro_website }}
                                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14">
                                                        </path>
                                                    </svg>
                                                </a>
                                                {% else %}
                                                N/A
                                                {% endif %}
                                            </p>
                                        </div>

                                        <div class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg space-y-1">
                                            <p
                                                class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                Sub Type</p>
                                            <p class="text-sm text-gray-900 mt-1 dark:text-gray-300">{{
                                                fund_data.category_info.sub_type |
                                                default('N/A') }}</p>
                                        </div>

                                        <div class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg space-y-1">
                                            <p
                                                class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                Sub Type Info
                                            </p>
                                            <p class="text-sm text-gray-900 mt-1 dark:text-gray-300">
                                                {{ fund_data.category_info.description | default('No description
                                                available.') }}
                                            </p>
                                        </div>

                                        <div class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg space-y-1">
                                            <p
                                                class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                Tax Impact</p>
                                            <p class="text-sm text-gray-900 mt-1 dark:text-gray-300">{{
                                                fund_data.category_info.tax_impact |
                                                default('N/A') }}</p>
                                        </div>

                                        <div class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg space-y-1">
                                            <p
                                                class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                Stamp Duty
                                            </p>
                                            <p class="text-sm text-gray-900 mt-1 dark:text-gray-300">
                                                {{ fund_data.stamp_duty | default('N/A') }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Fund Manager Details -->
                        <div id="fund-manager"
                            class="mb-6 bg-white rounded-2xl p-8 transition-all duration-300 scroll-mt-64">
                            <h4
                                class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b pb-2 border-gray-200 dark:border-gray-700">
                                Fund Manager Details
                            </h4>

                            <div class="border rounded-lg">
                                {% for manager in fund_data.fund_manager_details %}
                                <div class="border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                                    <button
                                        class="w-full flex items-center justify-between p-6 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900/30 dark:to-blue-900/30 focus:outline-none accordion-toggle"
                                        data-target="manager-{{ loop.index }}">
                                        <div class="flex items-center">
                                            <div class="bg-gray-100 dark:bg-gray-800 p-3 rounded-full mr-4 hidden">
                                                <svg class="w-8 h-8 text-gray-600 dark:text-gray-300" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="1.5"
                                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                                                    </path>
                                                </svg>
                                            </div>
                                            <div class="text-left">
                                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Fund
                                                    Manager</p>
                                                <p class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{
                                                    manager.person_name |
                                                    default('N/A') }}</p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    <span class="date">{{ manager.date_from | default('N/A') }}</span> -
                                                    {{ manager.end_date | default('Present') }}
                                                </p>
                                            </div>
                                        </div>
                                        <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 transform transition-transform accordion-icon"
                                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="manager-{{ loop.index }}" class="accordion-content hidden p-6">
                                        <div class="grid grid-cols-1 gap-6">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div class="space-y-2">
                                                    <p
                                                        class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                        Experience</p>
                                                    <p class="text-sm text-gray-700 dark:text-gray-300">{{
                                                        manager.experience | default('N/A')
                                                        }}</p>
                                                </div>
                                                <div class="space-y-2">
                                                    <p
                                                        class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                        Education</p>
                                                    <p class="text-sm text-gray-700 dark:text-gray-300">{{
                                                        manager.education | default('N/A') }}
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="space-y-2">
                                                <p
                                                    class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                                                    Other
                                                    Funds Managed</p>
                                                <p class="text-sm text-gray-700 dark:text-gray-300">
                                                    {% if manager.funds_managed %}
                                                    {{ manager.funds_managed | map(attribute='scheme_name') | join(', ')
                                                    }}
                                                    {% else %}
                                                    N/A
                                                    {% endif %}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Compare Section -->
                        <div id="compare-mutual-funds"
                            class="mb-6 bg-white rounded-2xl p-8 transition-all duration-300 scroll-mt-64">
                            <h4
                                class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b pb-2 border-gray-200 dark:border-gray-700">
                                Compare with Another Fund</h4>
                            <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden">
                                <div
                                    class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900/30 dark:to-blue-900/30 p-6 border-b border-gray-100 dark:border-gray-700">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <!-- Current Fund -->
                                        <div
                                            class="comparison-slot bg-white dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600">
                                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
                                                fund_data.scheme_name |
                                                default('N/A') }}</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ fund_data.fund_house
                                                | default('N/A') }}</p>
                                        </div>
                                        <!-- Placeholder Slot -->
                                        <div
                                            class="comparison-slot bg-gray-50 dark:bg-gray-700 p-6 rounded-lg border border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center">
                                            <a href="/mutual-funds/compare?fund1={{ scheme_code }}"
                                                class="flex flex-col items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200">
                                                <svg class="w-8 h-8 mb-2" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M12 4v16m8-8H4">
                                                    </path>
                                                </svg>
                                                <span class="text-sm font-medium">Add to Compare</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- macro loop -->
                        {% macro render_card(item, index, url_field='url', title_field='title',
                        desc_field='description') %}
                        <a href="/mutual-funds/{{ item[url_field] }}" class="flex-shrink-0 p-3 rounded-lg cursor-pointer transition-transform w-72 hover:-translate-y-0.5 hover:shadow-md 
                            {% if index % 5 == 0 %} bg-blue-500/20
                            {% elif index % 5 == 1 %} bg-emerald-500/20
                            {% elif index % 5 == 2 %} bg-purple-500/20
                            {% elif index % 5 == 3 %} bg-yellow-500/20
                            {% elif index % 5 == 4 %} bg-red-500/20
                            {% endif %}">
                            <h3 class="font-semibold text-base mb-1">{{ item[title_field] }}</h3>
                            <p class="text-xs text-gray-700 dark:text-gray-300">{{ item[desc_field] }}</p>
                        </a>
                        {% endmacro %}

                        <!-- Interested Funds Section -->
                        <div id="interested-funds" class="p-4 bg-white dark:bg-gray-800 rounded-lg mb-6">
                            <div class="flex justify-between items-center mb-3">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">You May Be Interested
                                    In</h3>
                                <div>
                                    <button id="interested-left-arrow"
                                        class="border h-10 w-10 bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                        aria-label="Scroll left">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M6 8L2 12L6 16"></path>
                                            <path d="M2 12H22"></path>
                                        </svg>
                                    </button>
                                    <button id="interested-right-arrow"
                                        class="h-10 w-10 border bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                        aria-label="Scroll right">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M18 8L22 12L18 16"></path>
                                            <path d="M2 12H22"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div id="interested-funds-cards"
                                class="flex flex-col gap-3 md:flex-row overflow-hidden relative">
                                {% for fund in interested_funds %}
                                {{ render_card(fund, loop.index0) }}
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Prebuilt Screeners Section -->
                        <div class="p-4 relative bg-white dark:bg-gray-800 rounded-lg mb-6 transition-all duration-300">
                            <div class="flex justify-between items-center mb-3">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Explore More Mutual
                                    Funds</h3>
                                <div>
                                    <button id="screener-left-arrow"
                                        class="border h-10 w-10 bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                        aria-label="Scroll left">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M6 8L2 12L6 16"></path>
                                            <path d="M2 12H22"></path>
                                        </svg>
                                    </button>
                                    <button id="screener-right-arrow"
                                        class="h-10 w-10 border bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                        aria-label="Scroll right">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M18 8L22 12L18 16"></path>
                                            <path d="M2 12H22"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div id="preset-screener-cards"
                                class="flex flex-col gap-3 md:flex-row overflow-hidden relative">
                                {% for filter in random_filters %}
                                {{ render_card(filter, loop.index0) }}
                                {% endfor %}
                            </div>
                        </div>

                        <!-- FAQ Section -->
                        {% include 'mutual-funds/faqs.html' %}
                    </div>
            </div>
            </main>
        </div>
    </div>
    </div>
    <script>
        // Data from backend
        const fundData = {{ fund_data | tojson }};
        const navHistory = fundData.nav_history;
        const expenseHistory = fundData.historic_fund_expense;

        // Shared number parsing function
        const parseNumber = (text) => {
            const match = text.match(/[\d,.]+/g);
            return match ? parseFloat(match.join('').replace(/,/g, '')) : NaN;
        };

        // Shared currency formatting function
        const formatCurrency = (value, isCrore = false) => {
            if (isNaN(value)) return null;
            return "₹ " + formatIndianNumber(Math.round(value)) + (isCrore ? " Cr" : "");
        };

        // Date formatting function
        const formatDate = (text) => {
            const parsedDate = new Date(text.trim());
            if (isNaN(parsedDate.getTime())) return null;
            return parsedDate.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        };

        // Aggregate expense history by month (average expense ratio)
        const aggregateExpenseByMonth = (history) => {
            const monthlyData = {};
            history.forEach(item => {
                const date = new Date(item.as_on_date);
                const yearMonth = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                if (!monthlyData[yearMonth]) {
                    monthlyData[yearMonth] = { total: 0, count: 0, date: `${yearMonth}-01` };
                }
                monthlyData[yearMonth].total += parseFloat(item.expense_ratio) || 0;
                monthlyData[yearMonth].count += 1;
            });
            return Object.values(monthlyData).map(item => ({
                as_on_date: item.date,
                expense_ratio: item.total / item.count
            })).sort((a, b) => new Date(a.as_on_date) - new Date(b.as_on_date));
        };

        // Derive sector allocation from holdings
        const deriveSectorAllocation = (holdings) => {
            const sectorMap = holdings.reduce((acc, holding) => {
                const sector = holding.sector_name || "Unknown";
                const allocation = parseFloat(holding.corpus_per) || 0;
                if (allocation > 0) {
                    acc[sector] = (acc[sector] || 0) + allocation;
                }
                return acc;
            }, {});
            return Object.entries(sectorMap).map(([sector_name, allocation]) => ({
                name: sector_name,
                allocation
            }));
        };

        const deriveInstrumentAllocation = (holdings) => {
            const instrumentMap = holdings.reduce((acc, holding) => {
                const instrument = holding.instrument_name || "Unknown";
                const allocation = parseFloat(holding.corpus_per) || 0;
                acc[instrument] = (acc[instrument] || 0) + allocation;
                if (allocation > 0) {
                }
                return acc;
            }, {});
            return Object.entries(instrumentMap).map(([instrument_name, allocation]) => ({
                name: instrument_name,
                allocation
            }));
        };

    </script>
    {% include 'mutual-funds/charts.html' %}
    <script>
        // Initialize charts and handle tab switching
        document.addEventListener('DOMContentLoaded', () => {
            // Allocation chart (Sector and Instrument)
            if (fundData.holdings?.length) {
                const sectorAllocation = deriveSectorAllocation(fundData.holdings);
                const instrumentAllocation = deriveInstrumentAllocation(fundData.holdings);
                const allocationCanvas = document.getElementById("allocation-chart")?.getContext("2d");

                if (allocationCanvas && (sectorAllocation.length || instrumentAllocation.length)) {
                    // Initialize with Sector chart
                    window.allocationChart = new Chart(allocationCanvas, getSectorChartConfig(sectorAllocation));

                    // Tab switching logic
                    const sectorTab = document.getElementById("sector-tab");
                    const instrumentTab = document.getElementById("instrument-tab");

                    const updateTabStyles = (activeTab) => {
                        [sectorTab, instrumentTab].forEach(tab => {
                            tab.classList.remove('text-blue-600', 'border-blue-600', 'dark:text-blue-400', 'dark:border-blue-400');
                            tab.classList.add('text-gray-500', 'border-transparent', 'dark:text-gray-400');
                        });
                        activeTab.classList.remove('text-gray-500', 'border-transparent', 'dark:text-gray-400');
                        activeTab.classList.add('text-blue-600', 'border-blue-600', 'dark:text-blue-400', 'dark:border-blue-400');
                    };

                    sectorTab.addEventListener('click', () => {
                        updateTabStyles(sectorTab);
                        window.allocationChart.destroy();
                        window.allocationChart = new Chart(allocationCanvas, getSectorChartConfig(sectorAllocation));
                    });

                    instrumentTab.addEventListener('click', () => {
                        updateTabStyles(instrumentTab);
                        window.allocationChart.destroy();
                        window.allocationChart = new Chart(allocationCanvas, getSectorChartConfig(instrumentAllocation));
                    });

                    // Set initial active tab
                    updateTabStyles(sectorTab);
                }
            }

            // NAV and Expense chart
            if (navHistory?.length || expenseHistory?.length) {
                const navCanvas = document.getElementById("nav-chart").getContext("2d");
                window.navChart = new Chart(navCanvas, getNavChartConfig(filterHistory(navHistory || [], '1m')));
                setupZoomControls(window.navChart);

                const navTab = document.getElementById("nav-tab");
                const expenseTab = document.getElementById("expense-tab");

                const updateTabStyles = (activeTab) => {
                    [navTab, expenseTab].forEach(tab => {
                        tab.classList.remove('text-blue-600', 'border-blue-600', 'dark:text-blue-400', 'dark:border-blue-400');
                        tab.classList.add('text-gray-500', 'border-transparent', 'dark:text-gray-400');
                    });
                    activeTab.classList.remove('text-gray-500', 'border-transparent', 'dark:text-gray-400');
                    activeTab.classList.add('text-blue-600', 'border-blue-600', 'dark:text-blue-400', 'dark:border-blue-400');
                };

                let currentTab = 'nav';
                navTab.addEventListener('click', () => {
                    currentTab = 'nav';
                    updateTabStyles(navTab);
                    toggleTimeFilterButtons(true);
                    window.navChart.destroy();
                    window.navChart = new Chart(navCanvas, getNavChartConfig(filterHistory(navHistory || [], '1m')));
                    setupZoomControls(window.navChart);
                    updateFilterButtons(document.getElementById('time-1m'));
                });

                expenseTab.addEventListener('click', () => {
                    currentTab = 'expense';
                    updateTabStyles(expenseTab);
                    toggleTimeFilterButtons(false);
                    window.navChart.destroy();
                    const monthlyExpenseHistory = aggregateExpenseByMonth(expenseHistory || []);
                    window.navChart = new Chart(navCanvas, getExpenseChartConfig(monthlyExpenseHistory));
                    setupZoomControls(window.navChart);
                });

                // Set initial active tab
                updateTabStyles(navTab);
                toggleTimeFilterButtons(true);

                document.querySelectorAll('.time-filter').forEach(button => {
                    button.addEventListener('click', () => {
                        if (currentTab === 'nav') {
                            const range = button.id.replace('time-', '');
                            updateFilterButtons(button);
                            const filteredData = filterHistory(navHistory || [], range);
                            updateNavChart(filteredData, window.navChart, 'nav');
                        }
                    });
                });

                updateFilterButtons(document.getElementById('time-1m'));
            }

            // Comparison chart (Returns and Expense Ratio)
            if (fundData.peerComparison?.length || fundData.expense_ratio) {
                const peerCanvas = document.getElementById("peer-comparison-chart")?.getContext("2d");
                if (peerCanvas) {
                    // Initialize with Returns chart
                    window.comparisonChart = new Chart(peerCanvas, getPeerComparisonChartConfig(fundData, fundData.peerComparison || []));

                    // Tab switching logic
                    const returnsTab = document.getElementById("returns-tab");
                    const expenseTab = document.getElementById("expense-comparison-tab");

                    const updateTabStyles = (activeTab) => {
                        [returnsTab, expenseTab].forEach(tab => {
                            tab.classList.remove('text-blue-600', 'border-blue-600', 'dark:text-blue-400', 'dark:border-blue-400');
                            tab.classList.add('text-gray-500', 'border-transparent', 'dark:text-gray-400');
                        });
                        activeTab.classList.remove('text-gray-500', 'border-transparent', 'dark:text-gray-400');
                        activeTab.classList.add('text-blue-600', 'border-blue-600', 'dark:text-blue-400', 'dark:border-blue-400');
                    };

                    returnsTab.addEventListener('click', () => {
                        updateTabStyles(returnsTab);
                        window.comparisonChart.destroy();
                        window.comparisonChart = new Chart(peerCanvas, getPeerComparisonChartConfig(fundData, fundData.peerComparison || []));
                    });

                    expenseTab.addEventListener('click', () => {
                        updateTabStyles(expenseTab);
                        window.comparisonChart.destroy();
                        window.comparisonChart = new Chart(peerCanvas, getExpenseRatioChartConfig(fundData, fundData.peerComparison || []));
                    });

                    // Set initial active tab
                    updateTabStyles(returnsTab);
                }
            }

            // Holdings table logic
            const holdingsBody = document.getElementById('holdings-body');
            const showMoreBtn = document.getElementById('show-more-btn');
            const allHoldings = fundData.holdings;
            let displayedCount = 10;
            const increment = 10;

            if (showMoreBtn) {
                showMoreBtn.addEventListener('click', function () {
                    const nextHoldings = allHoldings.slice(displayedCount, displayedCount + increment);
                    nextHoldings.forEach(holding => {
                        const row = document.createElement('tr');
                        row.className = 'border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors';
                        row.innerHTML = `
                        <td class="px-4 py-3">${holding.company_name}</td>
                        <td class="px-4 py-3">${holding.sector_name}</td>
                        <td class="px-4 py-3">${holding.instrument_name}</td>
                        <td class="px-4 py-3 text-left">${holding.corpus_per !== 'N/A' ? `${parseFloat(holding.corpus_per).toFixed(2)}%` : 'N/A'}</td>
                    `;
                        holdingsBody.appendChild(row);
                    });
                    displayedCount += increment;
                    if (displayedCount >= allHoldings.length) {
                        showMoreBtn.style.display = 'none';
                    }
                });
            }

            // Process elements
            document.querySelectorAll('.crmoney').forEach(el => {
                const value = parseNumber(el.textContent);
                const formatted = formatCurrency(value, true);
                if (formatted) el.textContent = formatted;
            });
            document.querySelectorAll('.money').forEach(el => {
                const value = parseNumber(el.textContent);
                const formatted = formatCurrency(value);
                if (formatted) el.textContent = formatted;
            });
            document.querySelectorAll('.date').forEach(el => {
                const formatted = formatDate(el.textContent);
                if (formatted) el.textContent = formatted;
            });

            // Accordion logic
            document.querySelectorAll('.accordion-toggle').forEach(button => {
                button.addEventListener('click', () => {
                    const targetId = button.getAttribute('data-target');
                    const content = document.getElementById(targetId);
                    const icon = button.querySelector('.accordion-icon');
                    const isHidden = content.classList.contains('hidden');
                    content.classList.toggle('hidden', !isHidden);
                    icon.classList.toggle('rotate-180', isHidden);
                });
            });

            if (fundData.simple_return && fundData.stats) {
                const annualisedTab = document.getElementById('annualised-returns-tab');
                const absoluteTab = document.getElementById('absolute-returns-tab');
                const returnsTableBody = document.getElementById('returns-table-body');

                const simpleReturn = fundData.simple_return || {};

                // Function to update tab styles
                const updateTabStyles = (activeTab) => {
                    [annualisedTab, absoluteTab].forEach(tab => {
                        tab.classList.remove('text-blue-600', 'border-blue-600', 'dark:text-blue-400', 'dark:border-blue-400');
                        tab.classList.add('text-gray-500', 'border-transparent', 'dark:text-gray-400');
                    });
                    activeTab.classList.remove('text-gray-500', 'border-transparent', 'dark:text-gray-400');
                    activeTab.classList.add('text-blue-600', 'border-blue-600', 'dark:text-blue-400', 'dark:border-blue-400');
                };

                // Function to format value
                const formatValue = (value) => {
                    if (value == null || isNaN(value)) return 'N/A';
                    return `${parseFloat(value).toFixed(2)}%`;
                };

                // Annualised returns data (from existing fund_data.stats)
                const annualisedData = {
                    fundReturns: {
                        '1y': fundData.stats?.[0]?.stat_1y ? formatValue(fundData.stats[0].stat_1y) : 'N/A',
                        '3y': fundData.stats?.[0]?.stat_3y ? formatValue(fundData.stats[0].stat_3y) : 'N/A',
                        '5y': fundData.stats?.[0]?.stat_5y ? formatValue(fundData.stats[0].stat_5y) : 'N/A',
                        'all': fundData.stats?.[0]?.stat_all ? formatValue(fundData.stats[0].stat_all) : 'N/A'
                    },
                    categoryAverage: {
                        '1y': fundData.stats?.[1]?.stat_1y ? formatValue(fundData.stats[1].stat_1y) : 'N/A',
                        '3y': fundData.stats?.[1]?.stat_3y ? formatValue(fundData.stats[1].stat_3y) : 'N/A',
                        '5y': fundData.stats?.[1]?.stat_5y ? formatValue(fundData.stats[1].stat_5y) : 'N/A',
                        'all': fundData.stats?.[1]?.stat_all ? formatValue(fundData.stats[1].stat_all) : 'N/A'
                    },
                    rank: {
                        '1y': fundData.stats?.[2]?.stat_1y || 'N/A',
                        '3y': fundData.stats?.[2]?.stat_3y || 'N/A',
                        '5y': fundData.stats?.[2]?.stat_5y || 'N/A',
                        'all': fundData.stats?.[2]?.stat_all || 'N/A'
                    }
                };

                // Absolute returns data (from simple_return)
                const absoluteData = {
                    fundReturns: {
                        '1y': simpleReturn.return1y ? formatValue(simpleReturn.return1y) : 'N/A',
                        '3y': simpleReturn.return3y ? formatValue(simpleReturn.return3y) : 'N/A',
                        '5y': simpleReturn.return5y ? formatValue(simpleReturn.return5y) : 'N/A',
                        'all': simpleReturn.return_since_created ? formatValue(simpleReturn.return_since_created) : 'N/A'
                    },
                    categoryAverage: {
                        '1y': simpleReturn.cat_return1y ? formatValue(simpleReturn.cat_return1y) : 'N/A',
                        '3y': simpleReturn.cat_return3y ? formatValue(simpleReturn.cat_return3y) : 'N/A',
                        '5y': simpleReturn.cat_return5y ? formatValue(simpleReturn.cat_return5y) : 'N/A',
                        'all': simpleReturn.cat_return_since_launch ? formatValue(simpleReturn.cat_return_since_launch) : 'N/A'
                    },
                    rank: {
                        '1y': fundData.stats?.[2]?.stat_1y || 'N/A',
                        '3y': fundData.stats?.[2]?.stat_3y || 'N/A',
                        '5y': fundData.stats?.[2]?.stat_5y || 'N/A',
                        'all': fundData.stats?.[2]?.stat_all || 'N/A'
                    }
                };

                // Function to update table content
                const updateTable = (data) => {
                    returnsTableBody.innerHTML = `
                        <tr class="border-b dark:border-gray-700">
                            <td class="px-4 py-3 font-medium">Fund Returns</td>
                            <td class="px-4 py-3">${data.fundReturns['1y']}</td>
                            <td class="px-4 py-3">${data.fundReturns['3y']}</td>
                            <td class="px-4 py-3">${data.fundReturns['5y']}</td>
                            <td class="px-4 py-3">${data.fundReturns['all']}</td>
                        </tr>
                        <tr class="border-b dark:border-gray-700">
                            <td class="px-4 py-3 font-medium">Category Average</td>
                            <td class="px-4 py-3">${data.categoryAverage['1y']}</td>
                            <td class="px-4 py-3">${data.categoryAverage['3y']}</td>
                            <td class="px-4 py-3">${data.categoryAverage['5y']}</td>
                            <td class="px-4 py-3">${data.categoryAverage['all']}</td>
                        </tr>
                        <tr class="border-b dark:border-gray-700">
                            <td class="px-4 py-3 font-medium">Rank Within Category</td>
                            <td class="px-4 py-3">${data.rank['1y']}</td>
                            <td class="px-4 py-3">${data.rank['3y']}</td>
                            <td class="px-4 py-3">${data.rank['5y']}</td>
                            <td class="px-4 py-3">${data.rank['all']}</td>
                        </tr>
                    `;
                };

                // Event listeners for tab switching
                annualisedTab.addEventListener('click', () => {
                    updateTabStyles(annualisedTab);
                    updateTable(annualisedData);
                });

                absoluteTab.addEventListener('click', () => {
                    updateTabStyles(absoluteTab);
                    updateTable(absoluteData);
                });

                // Initialize with Annualised Returns
                updateTabStyles(annualisedTab);
                updateTable(annualisedData);
                updateManagerTenures();
                updateReturnFAQs();
                updateExpenseRatioFAQ();
            }
        });

        function getFundAiAnalysis() {
            if (!fundData) {
                showAlert("Fund data not available for analysis");
                return;
            }

            // Create a structured returns object similar to UI display
            const returns = {
                performance: {
                    one_year: fundData.stats && fundData.stats[0] ? fundData.stats[0].stat_1y : null,
                    three_year: fundData.stats && fundData.stats[0] ? fundData.stats[0].stat_3y : null,
                    five_year: fundData.stats && fundData.stats[0] ? fundData.stats[0].stat_5y : null,
                    all_time: fundData.stats && fundData.stats[0] ? fundData.stats[0].stat_all : null
                },
                category_average: {
                    one_year: fundData.stats && fundData.stats[1] ? fundData.stats[1].stat_1y : null,
                    three_year: fundData.stats && fundData.stats[1] ? fundData.stats[1].stat_3y : null,
                    five_year: fundData.stats && fundData.stats[1] ? fundData.stats[1].stat_5y : null,
                    all_time: fundData.stats && fundData.stats[1] ? fundData.stats[1].stat_all : null
                },
                risk_metrics: {}
            };

            // Add risk metrics
            if (fundData.return_stats && fundData.return_stats.length > 0) {
                const metrics = fundData.return_stats[0];
                // Only include metrics that exist
                if (metrics.alpha !== undefined) returns.risk_metrics.alpha = metrics.alpha;
                if (metrics.beta !== undefined) returns.risk_metrics.beta = metrics.beta;
                if (metrics.sharpe_ratio !== undefined) returns.risk_metrics.sharpe_ratio = metrics.sharpe_ratio;
                if (metrics.standard_deviation !== undefined) returns.risk_metrics.standard_deviation = metrics.standard_deviation;
            }

            // Prepare payload for AI analysis with the fund data
            const payload = {
                fund: {
                    scheme_name: fundData.scheme_name,
                    fund_house: fundData.fund_house,
                    category: fundData.category,
                    scheme_type: fundData.scheme_type,
                    scheme_plan: fundData.scheme_plan,
                    scheme_option: fundData.scheme_option,
                    aum: fundData.aum,
                    expense_ratio: fundData.expense_ratio,
                    nav: fundData.nav,
                    launch_date: fundData.launch_date,
                    min_investment: fundData.min_investment,
                    risk_level: fundData.risk_level,
                    fund_manager: fundData.fund_manager,
                    exit_load: fundData.exit_load,
                    holdings: fundData.holdings ? fundData.holdings.slice(0, 10) : [], // Limit to top 10 holdings
                    returns: returns, // Use structured returns object instead of arrays
                    analysis: fundData.analysis || []
                }
            };

            // Call the AI feedback function with the mutual fund analysis type
            getAIFeedback(payload, { analysisType: 'fund' });
        }

    </script>

    <script>
        let lastTargetElement = null;

        document.querySelectorAll('#nav-shortcuts a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                document.getElementById(targetId).scrollIntoView({ behavior: 'smooth' });
                document.querySelectorAll('#nav-shortcuts a').forEach(link => {
                    link.classList.remove('bg-blue-700', '!text-white');
                })
                link.classList.add('bg-blue-700', '!text-white');
            });
        });
    </script>
    <script>
        // Initialize carousels
        initializeCardsCarousel('interested-funds-cards', 'interested-left-arrow', 'interested-right-arrow');
        initializeCardsCarousel('preset-screener-cards', 'screener-left-arrow', 'screener-right-arrow');
    </script>

    {% include 'blocks/footer.html' %}
</body>

</html>