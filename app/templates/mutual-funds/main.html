<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="index, follow">

    <title>{{ seo_title | default("Mutual Funds - Discover Top Mutual Fund Opportunities | AI Bull") | e }}</title>
    <meta name="description"
        content="{{ seo_description | default('Screen mutual funds by category, AMC, NAV, fund size, and more to find top investment opportunities, including money market funds, index funds, and actively managed funds, tailored to your investment objectives.') | e }}" />
    <link rel="canonical" href="{{ canonical_url | default('https://theaibull.com/mutual-funds') | e }}" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="{{ canonical_url | default('https://theaibull.com/mutual-funds') | e }}" />
    <meta property="og:title"
        content="{{ seo_title | default('Mutual Funds - Discover Top Mutual Fund Opportunities') | e }}" />
    <meta property="og:description"
        content="{{ seo_description | default('Screen mutual funds by category, AMC, net asset value, fund size, and more to identify the best investment in securities, including index funds and money market funds, with low fees charged.') | e }}" />
    <meta property="og:image" content="{{ cdn('/static/images/og-mutual-funds.jpg') }}" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title"
        content="{{ seo_title | default('Mutual Funds - Discover Top Mutual Fund Opportunities') | e }}" />
    <meta name="twitter:description"
        content="{{ seo_description | default('Find the best mutual fund opportunities, including money market funds and index funds, by screening based on type of mutual funds, AMC, net asset value, and fund size in securities markets.') | e }}" />
    {% include 'blocks/head.html' %}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chartjs-plugin-zoom/2.0.1/chartjs-plugin-zoom.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.js"></script>
</head>

<body class="bg-gray-100 dark:bg-gray-900 dark:text-gray-100">
    {% include 'blocks/common.html' %}
    {% include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width relative pb-[62px] xl:pb-0">
        <div class="p-3 mx-auto">
            <div class="mx-auto p-1">
                <!-- Header -->
                <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2 rounded-lg mb-3">
                    <div class="flex justify-between items-center">
                        <div class="md:flex justify-between items-center">
                            <div class="flex items-center">
                                {% if not path_param or path_param == "mutual-funds" %}
                                    <h1 class="md:text-xl text-lg font-semibold tracking-tight">
                                        Mutual Funds Screener
                                    </h1>
                                {% else %}
                                    <h1 class="md:text-xl text-lg font-semibold tracking-tight">
                                        {{ (seo_title | default("Mutual Funds Screener")).replace("Mutual Funds", "Screener") }}
                                    </h1>
                                {% endif %}
                                <p class="text-gray-600 dark:text-gray-400 ml-2 mt-2">Explore top mutual funds,
                                    including
                                    money market funds, index funds, and actively managed funds, with our advanced
                                    screener to find the best investment opportunities.</p>
                                <!-- Button to trigger modal open -->
                                <button id="openModalBtn" onclick="openModal()" class="ml-2">
                                    <div class="flex group text-sm">
                                        <div
                                            class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                            <div
                                                class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                                <div>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-circle-help">
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                        <path d="M12 17h.01"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div
                                                class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                                <div
                                                    class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                                    How to Use This Page
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="flex flex-col xl:flex-row gap-3">
                    <!-- Sidebar (Filters + Fund Houses) -->
                    <aside
                        class="w-full w-[300px] bg-white dark:bg-gray-800  flex flex-col gap-4 sticky top-0 max-h-screen">
                        <!-- Filters and Applied Filters -->
                        <section class="min-h-0 flex flex-col overflow-hidden">
                            <!-- Sticky Filter Funds Title -->
                            <div
                                class="py-2 px-4 bg-blue-50 dark:bg-blue-900  text-gray-800 dark:text-gray-100 sticky top-0 z-10 flex justify-between items-center shrink-0">
                                <h2 class="text-lg font-semibold">Filter Mutual Funds</h2>
                                <button id="clear-all-filters"
                                    class="!font-normal dark:hover:text-blue-300 dark:text-blue-400 flex hover:text-blue-800 items-center text-neutral-700 text-xs">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    Reset
                                </button>
                            </div>

                            <!-- Scrollable Filter Content -->
                            <div class="flex-1 overflow-y-auto pb-4">
                                <form id="filter-form" class="flex flex-col gap-6 p-4">
                                    <!-- Fund House Filter -->
                                    <div>
                                        <label for="fund-house-filter"
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Fund
                                            House (AMC)</label>
                                        <div class="relative">
                                            <!-- Button to toggle dropdown -->
                                            <button type="button" id="fund-house-toggle"
                                                class="w-full p-2 border rounded-md bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-left text-sm flex justify-between items-center focus:ring-2 focus:ring-blue-500 focus:outline-none">
                                                <span id="fund-house-display">All Fund Houses</span>
                                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                </svg>
                                            </button>
                                            <!-- Dropdown for checkboxes -->
                                            <div id="fund-house-dropdown"
                                                class="absolute z-30 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-64 overflow-y-auto hidden">
                                                <ul class="p-2">
                                                    <!-- All Fund Houses checkbox -->
                                                    <li class="py-1">
                                                        <label
                                                            class="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                                            <input type="checkbox" id="fund-house-all" value=""
                                                                class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                                                                checked>
                                                            All Fund Houses
                                                        </label>
                                                    </li>
                                                    <!-- Fund house checkboxes (populated dynamically) -->
                                                    <div id="fund-house-checkboxes"></div>
                                                </ul>
                                            </div>
                                            <!-- Clear button -->
                                            <button type="button" id="clear-fund-house"
                                                class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hidden">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Category Filter -->
                                    <div>
                                        <label for="category-filter"
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
                                        <div class="relative">
                                            <!-- Button to toggle dropdown -->
                                            <button type="button" id="category-toggle"
                                                class="w-full p-2 border rounded-md bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-left text-sm flex justify-between items-center focus:ring-2 focus:ring-blue-500 focus:outline-none">
                                                <span id="category-display">All Categories</span>
                                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                </svg>
                                            </button>
                                            <!-- Dropdown for checkboxes -->
                                            <div id="category-dropdown"
                                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-64 overflow-y-auto hidden">
                                                <ul class="p-2">
                                                    <!-- All Categories checkbox -->
                                                    <li class="py-1">
                                                        <label
                                                            class="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                                            <input type="checkbox" id="category-all"
                                                                class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                                                                checked>
                                                            All Categories
                                                        </label>
                                                    </li>
                                                    <!-- Category checkboxes (populated dynamically) -->
                                                    <div id="category-checkboxes"></div>
                                                </ul>
                                            </div>
                                            <!-- Clear button -->
                                            <button type="button" id="clear-category"
                                                class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hidden">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <!-- Sub-Category Filter -->
                                    <div class="sub-category-container hidden">
                                        <label for="sub-category-filter"
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sub
                                            Category</label>
                                        <div class="relative">
                                            <!-- Button to toggle dropdown -->
                                            <button type="button" id="sub-category-toggle"
                                                class="w-full p-2 border rounded-md bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-left text-sm flex justify-between items-center focus:ring-2 focus:ring-blue-500 focus:outline-none">
                                                <span id="sub-category-display">All Sub Categories</span>
                                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                </svg>
                                            </button>
                                            <!-- Dropdown for checkboxes -->
                                            <div id="sub-category-dropdown"
                                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-64 overflow-y-auto hidden">
                                                <ul class="p-2">
                                                    <!-- All Sub Categories checkbox -->
                                                    <li class="py-1">
                                                        <label
                                                            class="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                                            <input type="checkbox" id="sub-category-all"
                                                                class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                                                                checked>
                                                            All Sub Categories
                                                        </label>
                                                    </li>
                                                    <!-- Sub-category checkboxes (populated dynamically) -->
                                                    <div id="sub-category-checkboxes"></div>
                                                </ul>
                                            </div>
                                            <!-- Clear button -->
                                            <button type="button" id="clear-sub-category"
                                                class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hidden">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <!-- Exit Load Filter -->
                                    <div>
                                        <label for="exit-load-filter"
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Exit
                                            Load</label>
                                        <div class="relative">
                                            <button type="button" id="exit-load-toggle"
                                                class="w-full p-2 border rounded-md bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-left text-sm flex justify-between items-center focus:ring-2 focus:ring-blue-500 focus:outline-none">
                                                <span id="exit-load-display">All Exit Loads</span>
                                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                </svg>
                                            </button>
                                            <div id="exit-load-dropdown"
                                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-64 overflow-y-auto hidden">
                                                <ul class="p-2">
                                                    <li class="py-1">
                                                        <label
                                                            class="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                                            <input type="checkbox" id="exit-load-all"
                                                                class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                                                                checked>
                                                            All Exit Loads
                                                        </label>
                                                    </li>
                                                    <div id="exit-load-checkboxes"></div>
                                                </ul>
                                            </div>
                                            <button type="button" id="clear-exit-load"
                                                class="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-gray-200 hidden">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <!-- CRISIL Rating Filter -->
                                    <div>
                                        <label for="crisil-rating-filter"
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">CRISIL
                                            Rating</label>
                                        <div class="relative">
                                            <button type="button" id="crisil-rating-toggle"
                                                class="w-full p-2 border rounded-md bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-left text-sm flex justify-between items-center focus:ring-2 focus:ring-blue-500 focus:outline-none">
                                                <span id="crisil-rating-display">All Ratings</span>
                                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                </svg>
                                            </button>
                                            <div id="crisil-rating-dropdown"
                                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-64 overflow-y-auto hidden">
                                                <ul class="p-2">
                                                    <li class="py-1">
                                                        <label
                                                            class="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                                            <input type="checkbox" id="crisil-rating-all"
                                                                class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                                                                checked>
                                                            All Ratings
                                                        </label>
                                                    </li>
                                                    <div id="crisil-rating-checkboxes"></div>
                                                </ul>
                                            </div>
                                            <button type="button" id="clear-crisil-rating"
                                                class="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hidden">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M6 18L18 6M6 6l12 12">
                                                    </path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <!-- Risk Filter -->
                                    <div>
                                        <label for="risk-filter"
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Risk</label>
                                        <div class="relative">
                                            <button type="button" id="risk-toggle"
                                                class="w-full p-2 border rounded-md bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-left text-sm flex justify-between items-center focus:ring-2 focus:ring-blue-500 focus:outline-none">
                                                <span id="risk-display">All Risk Levels</span>
                                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                </svg>
                                            </button>
                                            <div id="risk-dropdown"
                                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-64 overflow-y-auto hidden">
                                                <ul class="p-2">
                                                    <li class="py-1">
                                                        <label
                                                            class="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                                            <input type="checkbox" id="risk-all"
                                                                class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                                                                checked>
                                                            All Risk Levels
                                                        </label>
                                                    </li>
                                                    <div id="risk-checkboxes"></div>
                                                </ul>
                                            </div>
                                            <button type="button" id="clear-risk"
                                                class="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hidden">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <!-- Plan Filter -->
                                    <div>
                                        <label for="plan-filter"
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Plan</label>
                                        <div class="relative">
                                            <button type="button" id="plan-toggle"
                                                class="w-full p-2 border rounded-md bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-left text-sm flex justify-between items-center focus:ring-2 focus:ring-blue-500 focus:outline-none">
                                                <span id="plan-display">All Plans</span>
                                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                </svg>
                                            </button>
                                            <div id="plan-dropdown"
                                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-64 overflow-y-auto hidden">
                                                <ul class="p-2">
                                                    <li class="py-1">
                                                        <label
                                                            class="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                                            <input type="checkbox" id="plan-all"
                                                                class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                                                                checked>
                                                            All Plans
                                                        </label>
                                                    </li>
                                                    <div id="plan-checkboxes"></div>
                                                </ul>
                                            </div>
                                            <button type="button" id="clear-plan"
                                                class="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hidden">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <!-- Expense Ratio Range Slider -->
                                    <div>
                                        <div class="flex justify-between items-center mb-3">
                                            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Expense
                                                Ratio (%)</label>
                                            <span id="expense-ratio-range-indicator"
                                                class="text-xs text-gray-500 dark:text-gray-600">0% - 3%</span>
                                        </div>
                                        <div id="expense-ratio-range-slider" class="h-2 px-4"></div>
                                        <div class="mt-3 flex justify-between text-xs text-gray-800 dark:text-gray-400">
                                            <span>0%</span>
                                            <span>3%</span>
                                        </div>
                                    </div>
                                    <!-- NAV Range Slider -->
                                    <div>
                                        <div class="flex justify-between items-center mb-3">
                                            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">NAV
                                                Range</label>
                                            <span id="nav-range-indicator"
                                                class="text-xs text-gray-500 dark:text-gray-600">₹0 - ₹5000</span>
                                        </div>
                                        <div id="nav-range-slider" class="h-2 px-4"></div>
                                        <div class="mt-3 flex justify-between text-xs text-gray-800 dark:text-gray-400">
                                            <span>₹0</span>
                                            <span>₹5000</span>
                                        </div>
                                    </div>
                                    <!-- Fund Size Range Slider -->
                                    <div>
                                        <div class="flex justify-between items-center mb-3">
                                            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Fund
                                                Size (Cr)</label>
                                            <span id="fund-size-range-indicator"
                                                class="text-xs text-gray-500 dark:text-gray-600">₹0 - ₹50,000</span>
                                        </div>
                                        <div id="fund-size-range-slider" class="h-2 px-4"></div>
                                        <div class="mt-3 flex justify-between text-xs text-gray-800 dark:text-gray-400">
                                            <span>₹0</span>
                                            <span>₹50,000</span>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </section>
                    </aside>

                    <!-- Results -->
                    <section class="w-[calc(100%_-_300px)] bg-white dark:bg-gray-800 rounded-xl">
                        <div class="p-4 relative"
                            style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #ffffff, #f8f8f8, #ffffff, #f5f5f500, #ffffff)">
                            <div class="flex justify-between items-center mb-3">
                                <h3 class="text-lg font-semibold">Prebuilt Screeners</h3>
                                <div>
                                    <!-- Left Arrow -->
                                    <button id="left-arrow"
                                        class="border z-30 h-10 w-10 transform bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-move-left-icon lucide-move-left">
                                            <path d="M6 8L2 12L6 16"></path>
                                            <path d="M2 12H22"></path>
                                        </svg>
                                    </button>
                                    <!-- Right Arrow -->
                                    <button id="right-arrow"
                                        class="transform h-10 w-10 border bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-move-right-icon lucide-move-right">
                                            <path d="M18 8L22 12L18 16"></path>
                                            <path d="M2 12H22"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div id="preset-cards" class="flex flex-col gap-3 md:flex-row overflow-hidden relative">
                                <a href="/mutual-funds/high-growth-equity" data-url="high-growth-equity"
                                    class="flex-shrink-0 bg-blue-100/80 p-3 rounded-lg cursor-pointer hover:bg-blue-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">High Growth Equity</h3>
                                    <p class="text-xs text-gray-700">Top-rated equity funds with direct plans</p>
                                </a>
                                <a href="/mutual-funds/stable-debt-funds" data-url="stable-debt-funds"
                                    class="flex-shrink-0 bg-green-100/80 p-3 rounded-lg cursor-pointer hover:bg-green-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">Stable Debt Funds</h3>
                                    <p class="text-xs text-gray-700">Low-risk debt funds with low expense ratios</p>
                                </a>
                                <a href="/mutual-funds/balanced-hybrid-picks" data-url="balanced-hybrid-picks"
                                    class="flex-shrink-0 bg-purple-100/80 p-3 rounded-lg cursor-pointer hover:bg-purple-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">Balanced Hybrid Picks</h3>
                                    <p class="text-xs text-gray-700">Hybrid funds with strong ratings and medium fund
                                        size</p>
                                </a>
                                <a href="/mutual-funds/top-small-cap" data-url="top-small-cap"
                                    class="flex-shrink-0 bg-yellow-100/80 p-3 rounded-lg cursor-pointer hover:bg-yellow-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">Top Small Caps</h3>
                                    <p class="text-xs text-gray-700">High-rated Small Cap funds with strong 3-year
                                        returns</p>
                                </a>
                                <a href="/mutual-funds/top-large-cap" data-url="top-large-cap"
                                    class="flex-shrink-0 bg-blue-100/80 p-3 rounded-lg cursor-pointer hover:bg-blue-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">Top Large Cap Funds</h3>
                                    <p class="text-xs text-gray-700">High-rated Large Cap funds with stable returns</p>
                                </a>
                                <a href="/mutual-funds/best-3-year-returns" data-url="best-3-year-returns"
                                    class="flex-shrink-0 bg-green-100/80 p-3 rounded-lg cursor-pointer hover:bg-green-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">Best 3-Year Returns</h3>
                                    <p class="text-xs text-gray-700">Funds with top 3-year returns across categories</p>
                                </a>
                                <a href="/mutual-funds/best-1-year-returns" data-url="best-1-year-returns"
                                    class="flex-shrink-0 bg-red-100/80 p-3 rounded-lg cursor-pointer hover:bg-red-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">Best 1-Year Returns</h3>
                                    <p class="text-xs text-gray-700">Funds with top 1-year returns across categories</p>
                                </a>
                                <a href="/mutual-funds/top-mid-cap" data-url="top-mid-cap"
                                    class="flex-shrink-0 bg-teal-100/80 p-3 rounded-lg cursor-pointer hover:bg-teal-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">Top Mid Cap Funds</h3>
                                    <p class="text-xs text-gray-700">High-rated Mid Cap funds with strong performance
                                    </p>
                                </a>
                                <a href="/mutual-funds/low-volatility-debt" data-url="low-volatility-debt"
                                    class="flex-shrink-0 bg-indigo-100/80 p-3 rounded-lg cursor-pointer hover:bg-indigo-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">Low Volatility Debt</h3>
                                    <p class="text-xs text-gray-700">Low-risk debt funds with high ratings</p>
                                </a>
                                <a href="/mutual-funds/high-return-small-caps" data-url="high-return-small-caps"
                                    class="flex-shrink-0 bg-red-100/80 p-3 rounded-lg cursor-pointer hover:bg-red-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">High Return Small Caps</h3>
                                    <p class="text-xs text-gray-700">Small Cap funds with strong 3-year returns</p>
                                </a>
                                <a href="/mutual-funds/volatility-large-caps" data-url="volatility-large-caps"
                                    class="flex-shrink-0 bg-teal-100/80 p-3 rounded-lg cursor-pointer hover:bg-teal-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">Volatility Large Caps</h3>
                                    <p class="text-xs text-gray-700">Stable Large Cap funds with high ratings</p>
                                </a>
                                <a href="/mutual-funds/short-term-debt" data-url="short-term-debt"
                                    class="flex-shrink-0 bg-indigo-100/80 p-3 rounded-lg cursor-pointer hover:bg-indigo-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">Short-Term Debt</h3>
                                    <p class="text-xs text-gray-700">Low-duration debt funds with minimal expenses</p>
                                </a>
                                <a href="/mutual-funds/aggressive-hybrid-growth" data-url="aggressive-hybrid-growth"
                                    class="flex-shrink-0 bg-orange-100/80 p-3 rounded-lg cursor-pointer hover:bg-orange-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">Aggressive Hybrid Growth</h3>
                                    <p class="text-xs text-gray-700">High-return aggressive hybrid funds, Direct plans
                                    </p>
                                </a>
                                <a href="/mutual-funds/elss-funds" data-url="elss-funds"
                                    class="flex-shrink-0 bg-pink-100/80 p-3 rounded-lg cursor-pointer hover:bg-pink-200 transition-colors w-72">
                                    <h3 class="font-semibold text-base mb-1">ELSS Funds</h3>
                                    <p class="text-xs text-gray-700">Equity Linked Savings Schemes with tax benefits</p>
                                </a>
                            </div>

                        </div>

                        <div class="px-4 py-2 bg-blue-50 dark:bg-blue-900 flex justify-between items-center">
                            <h2 class="font-semibold text-gray-800 dark:text-gray-100">
                                Mutual Funds Results <span class="text-gray-600 text-xs italic">(Total: <span
                                        id="total-funds">{{ total_funds
                                        }}</span> funds)</span>
                            </h2>
                            <div class="flex gap-x-2">
                                <div class="flex justify-between items-center">
                                    <nav class="flex gap-2" id="pagination-nav">
                                        <!-- Pagination controls will be rendered by JavaScript -->
                                    </nav>
                                </div>
                                <!-- Sort Dropdown -->
                                <div class="relative inline-block">
                                    <label for="sort-filter" class="sr-only">Sort By</label>
                                    <select id="sort-filter"
                                        class="appearance-none w-full pl-3 pr-10 py-2 border rounded-md bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none text-sm font-medium cursor-pointer">
                                        <option value="popularity" selected>Popularity</option>
                                        <option value="rating-high-low">Ratings: High to Low</option>
                                        <option value="returns-1m-high-low">Returns: 1M High to Low</option>
                                        <option value="returns-6m-high-low">Returns: 6M High to Low</option>
                                        <option value="returns-1y-high-low">Returns: 1Y High to Low</option>
                                        <option value="returns-3y-high-low">Returns: 3Y High to Low</option>
                                    </select>
                                    <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                        <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Applied Filters Summary -->
                        <div id="applied-filters"
                            class="p-2 hidden max-h-48 overflow-hidden transition-all duration-300 ease-in-out flex flex-wrap gap-2 items-center">
                            <p class="text-xs font-medium text-gray-500 dark:text-gray-400">Applied
                                Filters:</p>
                            <div id="filter-badges" class="flex flex-wrap gap-2"></div>
                        </div>

                        <div class="relative max-h-[800px] overflow-y-auto" id="table-container">
                            <table class="w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700 sticky top-0 z-10">
                                    <tr>
                                        <th scope="col"
                                            class="p-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase w-16 min-w-16">
                                            <!-- Empty for logo column -->
                                        </th>
                                        <th scope="col"
                                            class="p-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase cursor-pointer select-none hover:text-blue-600 dark:hover:text-blue-400 w-2/5"
                                            data-sort-key="scheme_name">
                                            Scheme Name
                                            <span
                                                class="inline-block w-4 text-center text-transparent transition-colors duration-200"></span>
                                        </th>
                                        <th scope="col"
                                            class="p-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase cursor-pointer select-none hover:text-blue-600 dark:hover:text-blue-400 w-1/8"
                                            data-sort-key="nav">
                                            Latest NAV
                                            <span
                                                class="inline-block w-4 text-center text-transparent transition-colors duration-200"></span>
                                        </th>
                                        <th scope="col"
                                            class="p-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase cursor-pointer select-none hover:text-blue-600 dark:hover:text-blue-400 w-1/8"
                                            data-sort-key="aum">
                                            Fund Size (Cr)
                                            <span
                                                class="inline-block w-4 text-center text-transparent transition-colors duration-200"></span>
                                        </th>
                                        <th scope="col"
                                            class="p-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase cursor-pointer select-none w-1/4">
                                            Returns
                                            <span
                                                class="inline-block w-4 text-center text-transparent transition-colors duration-200"></span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="results-body" class="divide-y divide-gray-200 dark:divide-gray-700">
                                    {% if initial_funds %}
                                    {% for fund in initial_funds %}
                                    <tr
                                        class="{% if loop.index0 % 2 %}bg-gray-50 dark:bg-gray-700{% else %}bg-white dark:bg-gray-800{% endif %} hover:bg-blue-50 dark:hover:bg-blue-900 transition-colors cursor-pointer group">
                                        <td class="p-2">
                                            <img src="{{ fund.logo_url or 'https://via.placeholder.com/32' }}"
                                                alt="{{ fund.scheme_name | default('Mutual Funds') + ' Mutual Funds' }} logo"
                                                loading="lazy"
                                                class="w-12 h-12 rounded-md object-contain border border-gray-200 dark:border-gray-600">
                                        </td>
                                        <td class="p-3 text-sm text-gray-800 dark:text-gray-200">
                                            <a href="/mutual-funds/{{ fund.scheme_code }}"
                                                class="group-hover:underline font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-all duration-200">{{
                                                fund.scheme_name or 'N/A' }}</a>
                                            <div class="flex flex-wrap gap-x-2 gap-y-1 mt-2">
                                                {% if fund.rating %}
                                                <div class="flex gap-0.5 mt-1">
                                                    {% for i in range(1, 6) %}
                                                    <svg class="w-4 h-4 {% if i <= fund.rating %}text-yellow-400{% else %}text-gray-300 dark:text-gray-600{% endif %}"
                                                        fill="currentColor" viewBox="0 0 24 24"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                                                    </svg>
                                                    {% endfor %}
                                                </div>
                                                {% endif %}
                                                {% if fund.category %}
                                                <span
                                                    class="px-2 py-0.5 text-[10px] font-medium rounded-md {{ {'Equity': 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100', 'Large Cap Fund': 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100', 'Mid Cap Fund': 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100', 'Small Cap Fund': 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100', 'Multi Cap Fund': 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100', 'Flexi Cap Fund': 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100', 'Debt': 'bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100', 'Liquid Fund': 'bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100', 'Low Duration Fund': 'bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100', 'Corporate Bond Fund': 'bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100', 'Hybrid': 'bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-100', 'Aggressive Hybrid Fund': 'bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-100', 'Conservative Hybrid Fund': 'bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-100', 'Commodities': 'bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-100', 'ELSS': 'bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-100'}.get(fund.category, 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-100') }}">{{
                                                    fund.category }}</span>
                                                {% endif %}
                                                {% if fund.sub_category %}
                                                <span
                                                    class="px-2 py-0.5 font-medium text-[10px] rounded-md bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">{{
                                                    fund.sub_category }}</span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td class="p-3 text-sm font-medium text-gray-800 dark:text-gray-200">₹{{
                                            fund.nav | float | round(2) }}</td>
                                        <td class="p-3 text-sm text-gray-800 dark:text-gray-200">₹{{ fund.aum | float |
                                            round }}</td>
                                        <td class="p-3 text-sm text-gray-800 dark:text-gray-200 w-1/4">
                                            <div class="grid grid-cols-4 gap-2">
                                                <div class="text-center rounded bg-gray-50 dark:bg-gray-700">
                                                    <span class="text-xs py-1 px-2">{{ fund.return_stats[0].return1m |
                                                        float | round(2) | string + '%'
                                                        if fund.return_stats[0].return1m else 'N/A' }}</span>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">1M</div>
                                                </div>
                                                <div class="text-center rounded bg-gray-50 dark:bg-gray-700">
                                                    <span class="text-xs py-1 px-2">{{ fund.return_stats[0].return6m |
                                                        float | round(2) | string + '%'
                                                        if fund.return_stats[0].return6m else 'N/A' }}</span>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">6M</div>
                                                </div>
                                                <div class="text-center rounded bg-gray-50 dark:bg-gray-700">
                                                    <span class="text-xs py-1 px-2">{{ fund.return_stats[0].return1y |
                                                        float | round(2) | string + '%'
                                                        if fund.return_stats[0].return1y else 'N/A' }}</span>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">1Y</div>
                                                </div>
                                                <div class="text-center rounded bg-gray-50 dark:bg-gray-700">
                                                    <span class="text-xs py-1 px-2">{{ fund.return_stats[0].return3y |
                                                        float | round(2) | string + '%'
                                                        if fund.return_stats[0].return3y else 'N/A' }}</span>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">3Y</div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                    {% else %}
                                    <tr>
                                        <td colspan="6" class="p-6 text-center text-gray-500 dark:text-gray-400">
                                            <svg class="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none"
                                                stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                                </path>
                                            </svg>
                                            No funds match your criteria
                                        </td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>

                        <!-- SEO FAQ Snippets Section -->
                        {% if faqs %}
                        <div id="seo-faq-snippets" class="mt-8 text-sm rounded-2xl px-2 transition-all duration-300 scroll-mt-64">
                            <h4
                                class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b pb-2 border-gray-200 dark:border-gray-700">
                                Frequently Asked Questions
                            </h4>
                            <div class="space-y-4" id="seo-faq-content">
                                {% for faq in faqs %}
                                <details class="border rounded-lg bg-white dark:bg-gray-700">
                                    <summary class="cursor-pointer p-4 text-gray-900 dark:text-gray-100 font-medium">{{ faq.question }}
                                    </summary>
                                    <div class="p-4 border-t text-gray-700 dark:text-gray-300 leading-6">
                                        <p>{{ faq.answer }}</p>
                                    </div>
                                </details>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </section>
                </div>
            </div>
        </div>
    </div>

    <script>
        const BASE_URL = '/mutual-funds';
        const ELEMENTS = {
            fundHouseSelect: document.getElementById('fund-house-filter'),
            exitLoadFilter: document.getElementById('exit-load-filter'),
            crisilRatingFilter: document.getElementById('crisil-rating-filter'),
            riskFilter: document.getElementById('risk-filter'),
            planFilter: document.getElementById('plan-filter'),
            sortFilter: document.getElementById('sort-filter'),
            filterForm: document.getElementById('filter-form'),
            resultsBody: document.getElementById('results-body'),
            totalFunds: document.getElementById('total-funds'),
            clearAllFilters: document.getElementById('clear-all-filters'),
            clearFundHouse: document.getElementById('clear-fund-house'),
            clearCategory: document.getElementById('clear-category'),
            clearSubCategory: document.getElementById('clear-sub-category'),
            clearExitLoad: document.getElementById('clear-exit-load'),
            clearCrisilRating: document.getElementById('clear-crisil-rating'),
            clearRisk: document.getElementById('clear-risk'),
            clearPlan: document.getElementById('clear-plan'),
            appliedFilters: document.getElementById('applied-filters'),
            filterBadges: document.getElementById('filter-badges'),
            navRangeIndicator: document.getElementById('nav-range-indicator'),
            fundSizeRangeIndicator: document.getElementById('fund-size-range-indicator'),
            expenseRatioRangeIndicator: document.getElementById('expense-ratio-range-indicator'),
        };

        let currentPage = {{ current_page }};
        let totalPages = {{ total_pages }};
        let perPage = 100; // Explicitly set to 100
        let isLoading = false;
        let allFunds = [];
        let sortState = { key: null, direction: 'asc' };
        let additionalFilterControls = {};
        let fundHouseCheckbox;
        let categoryCheckbox;
        let subCategoryCheckbox;
        let totalFunds;
        let presetFilters = {};
        let categoriesData = [];
        let fundHousesData = {};
        const pathParam = '{{ path_param }}';
    </script>

    {% include 'mutual-funds/filters-sort.html' %}

    <script>
        // Fetch utility
        const fetchData = async (url, options = {}) => {
            try {
                const response = await fetch(url, options);
                if (!response.ok) throw new Error(`HTTP error ${response.status}`);
                return await response.json();
            } catch (error) {
                console.error(`Fetch error: ${error}`);
                throw error;
            }
        };

        // Fetch filter data
        const fetchFilterData = async () => {
            try {
                const url = pathParam ? `${BASE_URL}/filter-data?path_param=${encodeURIComponent(pathParam)}` : `${BASE_URL}/filter-data`;
                const data = await fetchData(url);
                presetFilters = data.preset_filters || {};
                categoriesData = data.categories || [];
                fundHousesData = data.fund_house_summary || {};
            } catch (error) {
                console.error('Error fetching filter data:', error);
                ELEMENTS.resultsBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="p-6 text-center text-gray-500 dark:text-gray-400">
                            <svg class="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Error loading filter data. Please try again.
                        </td>
                    </tr>
                `;
            }
        };

        // Generate star rating SVG markup
        const generateStarRating = (rating) => {
            const ratingValue = rating ? parseInt(rating) : 0;
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                const isFilled = i <= ratingValue;
                stars += `
                    <svg class="w-4 h-4 ${isFilled ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'}" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
                    </svg>
                `;
            }
            return `<div class="flex gap-0.5 mt-1">${stars}</div>`;
        };

        // Render results
        const renderResults = (funds, totalCount) => {
            ELEMENTS.totalFunds.textContent = totalCount;
            if (!funds.length) {
                ELEMENTS.resultsBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="p-6 text-center text-gray-500 dark:text-gray-400">
                            <svg class="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            No funds match your criteria
                        </td>
                    </tr>
                `;
                return;
            }

            ELEMENTS.resultsBody.innerHTML = funds.map((fund, index) => `
                <tr class="${index % 2 ? 'bg-gray-50 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'} hover:bg-blue-50 dark:hover:bg-blue-900 transition-colors cursor-pointer group" onclick="window.location.href='/mutual-funds/${fund.scheme_code}'">
                    <td class="p-2">
                        <img src="${fund.logo_url || 'https://via.placeholder.com/32'}" alt="${fund.scheme_name ? fund.scheme_name + ' Mutual Funds' : 'Mutual Funds'} logo" loading="lazy" class="w-12 h-12 rounded-md object-contain border border-gray-200 dark:border-gray-600">
                    </td>
                    <td class="p-3 text-sm text-gray-800 dark:text-gray-200">
                        <a href="/mutual-funds/${fund.scheme_code}" class="group-hover:underline font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-all duration-200">${fund.scheme_name || 'N/A'}</a>
                        <div class="flex flex-wrap gap-x-2 gap-y-1 mt-2">
                            ${fund.rating && fund.rating > 0 ? generateStarRating(fund.rating) : ''}
                            ${fund.category ? `<span class="px-2 py-0.5 text-[10px] font-medium  rounded-md ${getCategoryColor(fund.category)}">${fund.category}</span>` : ''}
                            ${fund.sub_category ? `<span class="px-2 py-0.5 font-medium text-[10px] rounded-md bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">${fund.sub_category}</span>` : ''}
                        </div>
                    </td>
                    <td class="p-3 text-sm font-medium text-gray-800 dark:text-gray-200">₹${fund.nav ? formatIndianNumber(Number(fund.nav).toFixed(2)) : 'N/A'}</td>
                    <td class="p-3 text-sm text-gray-800 dark:text-gray-200">₹${fund.aum ? formatIndianNumber(Math.round(Number(fund.aum))) : 'N/A'}</td>
                    <td class="p-3 text-sm text-gray-800 dark:text-gray-200 w-1/4">
                        <div class="grid grid-cols-4 gap-2">
                            <div class="text-center rounded bg-gray-50 dark:bg-gray-700">
                                <span class="text-xs py-1 px-2">${fund.return_stats[0].return1m ? Number(fund.return_stats[0].return1m).toFixed(2) + '%' : 'N/A'}</span>
                                <div class="text-xs text-gray-500 dark:text-gray-400">1M</div>
                            </div>
                            <div class="text-center rounded bg-gray-50 dark:bg-gray-700">
                                <span class="text-xs py-1 px-2">${fund.return_stats[0].return6m ? Number(fund.return_stats[0].return6m).toFixed(2) + '%' : 'N/A'}</span>
                                <div class="text-xs text-gray-500 dark:text-gray-400">6M</div>
                            </div>
                            <div class="text-center rounded bg-gray-50 dark:bg-gray-700">
                                <span class="text-xs py-1 px-2">${fund.return_stats[0].return1y ? Number(fund.return_stats[0].return1y).toFixed(2) + '%' : 'N/A'}</span>
                                <div class="text-xs text-gray-500 dark:text-gray-400">1Y</div>
                            </div>
                            <div class="text-center rounded bg-gray-50 dark:bg-gray-700">
                                <span class="text-xs py-1 px-2">${fund.return_stats[0].return3y ? Number(fund.return_stats[0].return3y).toFixed(2) + '%' : 'N/A'}</span>
                                <div class="text-xs text-gray-500 dark:text-gray-400">3Y</div>
                            </div>
                        </div>
                    </td>
                </tr>
            `).join('');
        };

        // Category colors
        const getCategoryColor = category => ({
            Equity: 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100',
            'Large Cap Fund': 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100',
            'Mid Cap Fund': 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100',
            'Small Cap Fund': 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100',
            'Multi Cap Fund': 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100',
            'Flexi Cap Fund': 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100',
            Debt: 'bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100',
            'Liquid Fund': 'bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100',
            'Low Duration Fund': 'bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100',
            'Corporate Bond Fund': 'bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100',
            Hybrid: 'bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-100',
            'Aggressive Hybrid Fund': 'bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-100',
            'Conservative Hybrid Fund': 'bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-100',
            Commodities: 'bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-100',
            ELSS: 'bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-100'
        }[category] || 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-100');

        // Initialize
        document.addEventListener('DOMContentLoaded', async () => {
            const urlParams = new URLSearchParams(window.location.search);
            const urlPage = parseInt(urlParams.get('page')) || 1;
            currentPage = Math.max(1, urlPage);

            await fetchFilterData();
            window.filterManager = new FilterManager(getFilterConfigs(fundHousesData, categoriesData));
            initRangeFilters();
            document.querySelector('.sub-category-container').classList.add('hidden');
            initPresetCards(presetFilters);
            initSortListeners();
            filterManager.paginationManager = new PaginationManager({ filterManager, baseUrl: '/mutual-funds', perPage: 100, initialCurrentPage: currentPage });
            filterManager.paginationManager.render(totalPages);
            ELEMENTS.clearAllFilters.addEventListener('click', () => filterManager.resetAllFilters());
            initializeCardsCarousel('preset-cards', 'left-arrow', 'right-arrow');
        });

    </script>
    {% include 'blocks/footer.html' %}
</body>

</html>