<div class="overflow-x-auto">
    <table id="holdings-table" class="min-w-full text-sm hidden">
        <thead class="bg-gray-50 dark:bg-neutral-700">
            <tr>
                <th id="header-symbol" onclick="sortHoldingsTable('symbol')"
                    class="cursor-pointer hover:underline px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Symbol <span id="caret-symbol" class="caret"></span>
                </th>
                <th id="header-quantity" onclick="sortHoldingsTable('quantity')"
                    class="cursor-pointer hover:underline px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Quantity <span id="caret-quantity" class="caret"></span>
                </th>
                <th id="header-avg_price" onclick="sortHoldingsTable('avg_price')"
                    class="cursor-pointer hover:underline px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Avg Price <span id="caret-avg_price" class="caret"></span>
                </th>
                <th id="header-current_price" onclick="sortHoldingsTable('current_price')"
                    class="cursor-pointer hover:underline px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Current Price <span id="caret-current_price" class="caret"></span>
                </th>
                <th id="header-invested_amount" onclick="sortHoldingsTable('invested_amount')"
                    class="cursor-pointer hover:underline px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Invested Amount <span id="caret-invested_amount" class="caret"></span>
                </th>
                <th id="header-market_value" onclick="sortHoldingsTable('market_value')"
                    class="cursor-pointer hover:underline px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Market Value <span id="caret-market_value" class="caret"></span>
                </th>
                <th id="header-pl" onclick="sortHoldingsTable('pl')"
                    class="cursor-pointer hover:underline px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    P & L<span id="caret-pl" class="caret"></span>
                </th>
                <th
                    class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                </th>
            </tr>
        </thead>
        <tbody id="holdings-table-body"
            class="bg-white dark:bg-neutral-800 divide-y divide-gray-200 dark:divide-gray-700">
        </tbody>
    </table>
    <div id="holdings-placeholder" class="text-center py-12">
        <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-gray-200">No holdings</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {% if portfolio_uid %}
            Get started by adding a new stock to your portfolio
            {% else %}
            No holdings found across all portfolios
            {% endif %}
        </p>
        {% if portfolio_uid and (not portfolio.type or portfolio.type == "manual") %}
        <div class="mt-6">
            <button onclick="showAddStockModal()"
                class="bg-blue-100 font-medium hover:bg-blue-500 hover:text-white px-4 py-3 w-64 rounded-md text-blue-500 text-sm transition-colors">
                Add Stock
            </button>
        </div>
        {% endif %}
    </div>
</div>

<script>
    // Assuming these global variables are needed for some operations
    let holdingsArr = [];

    // Set default sort direction to -1 (for descending order).
    let sortDirection = -1;


    // Apply Indian formatting to Invested Amount (column index 4) and Market Value (column index 5) cells.
    function applyIndianFormatting() {
        const cellIndices = {
            quantity: 1,
            avgPrice: 2,
            currentPrice: 3,
            totalValue: 4,
            invested: 4,
            marketValue: 5,
            pl: 6
        };

        function formatCell(row, index, isPL = false) {
            let cell = row.children[index];
            if (cell && cell.textContent.trim() !== "N/A") {
                let raw = cell.textContent.trim().replace(/,/g, "");
                let num = parseFloat(raw);
                if (!isNaN(num)) {
                    if (isPL) {
                        let formattedNumber = formatIndianNumber(num);
                        let percentage = cell.textContent.trim().match(/\(([^)]+)\)/);
                        let colorClass = cell.querySelector('span')?.className;
                        cell.innerHTML = `<span class="${colorClass}">${formattedNumber}${percentage ? ` ${percentage[0]}` : ""}</span>`;
                    } else {
                        cell.textContent = formatIndianNumber(num);
                    }
                }
            }
        }


        document.querySelectorAll("tbody tr.group").forEach((row) => {
            // Format common cells
            Object.keys(cellIndices).forEach((key) => {
                formatCell(row, cellIndices[key], key === 'pl');
            });
        });
    }

    function populateHoldingsTable(holdings, portfolioUid, portfolioType) {
        const holdingsTableBody = document.getElementById('holdings-table-body');
        holdingsTableBody.innerHTML = '';

        if (holdings.length === 0) {
            document.getElementById('holdings-table').classList.add('hidden');
            document.getElementById('holdings-placeholder').classList.remove('hidden');
            return;
        }

        document.getElementById('holdings-table').classList.remove('hidden');
        document.getElementById('holdings-placeholder').classList.add('hidden');

        holdings.forEach(holding => {
            const row = document.createElement('tr');
            row.className = 'group';
            row.setAttribute('data-uid', holding.uid);

            row.innerHTML = `
                <td class="px-4 py-2">
                    <div>
                        <div class="font-medium">${holding.symbol}</div>
                        ${holding.sector ? `
                            <div class="text-xs text-gray-600 inline-block">
                                ${holding.sector} (${holding.industry || ''})
                            </div>` : ''}
                        ${!portfolioUid ? `
                            <div class="text-xs text-gray-500">${holding.portfolio_name || ''}</div>` : ''}
                    </div>
                </td>
                <td class="px-4 py-2">${holding.quantity ? Number(holding.quantity).toLocaleString() : 'N/A'}</td>
                <td class="px-4 py-2">${holding.avg_price ? Number(holding.avg_price.toFixed(2)).toLocaleString() : 'N/A'}</td>
                <td class="px-4 py-2">
                    ${holding.current_price ? Number(holding.current_price.toFixed(2)).toLocaleString() : '<span class="text-red-600">N/A</span>'}
                </td>
                <td class="px-4 py-2">
                    ${holding.invested_amount ? Number(holding.invested_amount.toFixed(2)).toLocaleString() : '<span class="text-red-600">N/A</span>'}
                </td>
                <td class="px-4 py-2">
                    ${holding.market_value ? Number(holding.market_value.toFixed(2)).toLocaleString() : '<span class="text-red-600">N/A</span>'}
                </td>
                <td class="px-4 py-2">
                    ${holding.pl !== null ? `
                        <span class="${holding.pl >= 0 ? 'text-green-600' : 'text-red-600'}">
                            ${Number(Math.abs(holding.pl).toFixed(2)).toLocaleString()}
                            (${Number(Math.abs(holding.pl_percentage || 0).toFixed(2))}%)
                        </span>
                    ` : '<span class="text-red-600">N/A</span>'}
                </td>
                
                ${portfolioUid && (!portfolioType || portfolioType === 'manual') ? `
                    <td class="px-4 py-2">
                        <button onclick="deleteHolding('${holding.uid}')"
                        class="hover:text-red-600 text-neutral-600 invisible group-hover:visible">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.7" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-trash-2">
                            <path d="M3 6h18" />
                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                            <line x1="10" x2="10" y1="11" y2="17" />
                            <line x1="14" x2="14" y1="11" y2="17" />
                        </svg>
                        </button>
                    </td>` : ''}
                    
            `;
            holdingsTableBody.appendChild(row);

            // Add orders row if exists
            if (holding.orders && holding.orders.length > 0) {
                const ordersRow = document.createElement('tr');
                ordersRow.id = `orders-${holding.uid}`;
                ordersRow.className = 'hidden';
                ordersRow.innerHTML = `
                <td colspan="8" class="px-4 py-2">
                    <div>
                        ${holding.orders.map(order => `
                            <div class="flex justify-between">
                                <span>${order.quantity} @ ${order.price}</span>
                                <button onclick="deleteOrder('${holding.uid}', '${order.uid}')" class="text-red-500">Delete</button>
                            </div>
                        `).join('')}
                    </div>
                </td>
            `;
                holdingsTableBody.appendChild(ordersRow);
            }
        });

        applyIndianFormatting();
        sortHoldingsTable('invested_amount'); // Default sort
    }


    function sortHoldingsTable(key) {
        const tbody = document.querySelector("table tbody");
        const rows = Array.from(tbody.children);
        let groupedRows = [];

        // Group each holding row with its potential orders row
        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            if (row.id && row.id.startsWith("orders-")) continue;
            let group = { holding: row, orders: null };
            if (i + 1 < rows.length && rows[i + 1].id && rows[i + 1].id.startsWith("orders-")) {
                group.orders = rows[i + 1];
                i++; // Skip the orders row
            }
            groupedRows.push(group);
        }

        // Map keys to their respective column index (0-based)
        const keyMap = {
            symbol: 0,
            quantity: 1,
            avg_price: 2,
            current_price: 3,
            invested_amount: 4,
            market_value: 5,
            pl: 6
        };

        const colIndex = keyMap[key];

        // Sort the grouped rows based on the specified column
        groupedRows.sort((a, b) => {
            let valA, valB, isNumeric;

            if (key === "pl") {
                // Extract P&L value and determine sign from text color
                const getPLValue = (element) => {
                    const cell = element.children[colIndex];
                    const span = cell.querySelector('span');
                    if (!span) return 0; // Default to 0 if no span (e.g., "N/A")

                    // Get the absolute value from the text
                    const text = span.textContent.trim();
                    const valueMatch = text.match(/[\d,]+\.?\d*/); // Extract the number part
                    const value = valueMatch ? parseFloat(valueMatch[0].replace(/,/g, "")) : 0;

                    // Determine sign based on text color class
                    const isLoss = span.classList.contains('text-red-600');
                    return isLoss ? -value : value; // Negative for red (loss), positive for green (profit)
                };

                valA = getPLValue(a.holding);
                valB = getPLValue(b.holding);
                isNumeric = true;
            } else {
                valA = a.holding.children[colIndex].textContent.trim().replace(/[,%₹]/g, "");
                valB = b.holding.children[colIndex].textContent.trim().replace(/[,%₹]/g, "");
                isNumeric = !isNaN(parseFloat(valA)) && !isNaN(parseFloat(valB));
            }

            if (isNumeric) {
                return sortDirection * (valA - valB); // Compare signed values
            }
            return sortDirection * valA.localeCompare(valB);
        });

        // Toggle sort direction for next time
        sortDirection *= -1;

        // Clear carets from all headers
        document.querySelectorAll("th span.caret").forEach(span => span.innerHTML = "");

        // Update caret for the sorted column
        const caret = document.getElementById("caret-" + key);
        if (caret) {
            caret.innerHTML = sortDirection === -1 ? "▼" : "▲"; // Descending or Ascending
        }

        // Clear the tbody and re-append rows in sorted order
        tbody.innerHTML = "";
        groupedRows.forEach(group => {
            tbody.appendChild(group.holding);
            if (group.orders) tbody.appendChild(group.orders);
        });

        // Re-apply Indian formatting after sorting
        applyIndianFormatting();
    }

    function toggleOrders(holdingUid) {
        const ordersRow = document.getElementById(`orders-${holdingUid}`);
        const btnText = document.getElementById(`orders-btn-text-${holdingUid}`);
        if (ordersRow) {
            ordersRow.classList.toggle('hidden');
            if (btnText) {
                btnText.textContent = ordersRow.classList.contains('hidden') ? 'View Orders' : 'Hide Orders';
            }
        }
    }

    async function deleteHolding(holdingUid) {
        const confirmed = await showConfirmDialog({
            title: 'Delete Holding',
            message: 'Are you sure you want to delete this holding and all its orders?',
            confirmText: 'Delete',
            cancelText: 'Cancel',
            type: 'danger'
        });
        if (!confirmed) return;
        try {
            currentPortfolio.holdings = currentPortfolio.holdings.filter(h => h.uid !== holdingUid);
            await portfoliosCrud.update(currentPortfolio.uid, currentPortfolio);
            window.location.reload();
        } catch (error) {
            console.error('Error:', error);
            showAlert('Failed to delete holding');
        }
    }

    async function deleteOrder(holdingUid, orderId) {
        const confirmed = await showConfirmDialog({
            title: 'Delete Order',
            message: 'Are you sure you want to delete this order?',
            confirmText: 'Delete',
            cancelText: 'Cancel',
            type: 'danger'
        });
        if (!confirmed) return;
        try {
            const holding = currentPortfolio.holdings.find(h => h.uid === holdingUid);
            if (!holding) throw new Error('Holding not found');
            holding.orders = holding.orders.filter(o => o.uid !== orderId);
            let totalQuantity = 0, totalValue = 0;
            holding.orders.forEach(order => {
                totalQuantity += order.quantity;
                totalValue += order.quantity * order.price;
            });
            if (holding.orders.length === 0) {
                currentPortfolio.holdings = currentPortfolio.holdings.filter(h => h.uid !== holdingUid);
            } else {
                holding.quantity = totalQuantity;
                holding.avg_price = totalValue / totalQuantity;
            }
            await portfoliosCrud.update(currentPortfolio.uid, currentPortfolio);
            window.location.reload();
        } catch (error) {
            console.error('Error:', error);
            showAlert('Failed to delete order');
        }
    }
</script>