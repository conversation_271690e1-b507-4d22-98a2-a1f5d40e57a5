<div id="charts-container" class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4">
    <h2 class="text-lg font-semibold mb-4">Charts</h2>
    <!-- Tabs Navigation -->
    <ul class="flex border-b mb-3 gap-6">
        <li class="-mb-px mr-1">
            <a id="stocksTab" class="bg-white inline-block pb-2 font-medium cursor-pointer border-b-2 border-blue-500"
                onclick="showChartTab('stocks')">Stocks</a>
        </li>
        <li class="mr-1">
            <a id="sectorsTab" class="bg-white inline-block pb-2 font-medium cursor-pointer"
                onclick="showChartTab('sectors')">Sectors</a>
        </li>
    </ul>

    <!-- Tab Content -->
    <div id="stocksContent">
        <div class="grid grid-cols-1 xl:grid-cols-3 md:grid-cols-2 gap-4">
            <div class="w-80 h-80">
                <canvas id="marketValueChart"></canvas>
            </div>
            <div class="w-80 h-80">
                <canvas id="profitsChart"></canvas>
            </div>
            <div class="w-80 h-80">
                <canvas id="marketCapChart"></canvas>
            </div>
        </div>
    </div>
    <div id="sectorsContent">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="w-80 h-80">
                <canvas id="sectorsChart"></canvas>
            </div>
            <div class="w-80 h-80">
                <canvas id="industriesChart"></canvas>
            </div>
        </div>
    </div>
</div>

<script>
    // Global chart instances.
    window.chart_marketValue = null;
    window.chart_profits = null;
    window.chart_marketCap = null;
    window.chart_sectors = null;
    window.chart_industries = null;

    // Standard color scheme - fixed colors.
    const standardColors = [
        'rgba(65, 105, 225, 0.7)', // Royal Blue
        'rgba(54, 162, 235, 0.7)',   // Bright Blue
        'rgba(255, 206, 86, 0.7)',   // Bright Yellow
        'rgba(75, 192, 192, 0.7)',   // Bright Green
        'rgba(153, 102, 255, 0.7)',  // Bright Purple
        'rgba(255, 159, 64, 0.7)'    // Bright Orange
    ];
    function getStandardColor(index) {
        return standardColors[index % standardColors.length];
    }

    // Global mapping for detailed stock info.
    window.stockDetails = {};

    // Helper function to wrap an array of stock symbols into multiple lines (default 5 per line).
    function wrapStockList(stocks, perLine = 5) {
        const lines = [];
        for (let i = 0; i < stocks.length; i += perLine) {
            lines.push(stocks.slice(i, i + perLine).join(', '));
        }
        return lines;
    }

    // Aggregate stocks data by symbol.
    function aggregateStocksData(holdingsData, positionsData) {
        const stocks = {};
        [...holdingsData, ...positionsData].forEach(item => {
            const symbol = item["symbol"];
            const marketValue = parseFloat(item["market_cap"]);
            const profit = parseFloat(item["pl"]) || 0;
            if (!isNaN(marketValue) && symbol) {
                if (!stocks[symbol]) {
                    stocks[symbol] = {
                        marketValue: 0,
                        profit: 0,
                        details: {
                            quantity: item["quantity"],
                            avg_price: item["avg_price"],
                            current_price: item["current_price"],
                            invested_amount: item["invested_amount"],
                            market_value: item["market_value"]
                        }
                    };
                }
                stocks[symbol].marketValue += marketValue;
                stocks[symbol].profit += profit;
            }
        });
        return stocks;
    }

    // Group stocks data.
    // If total stocks > maxSlices, show top (maxSlices-1) and group rest into "Others".
    function groupStocksData(stocks, maxSlices = 20) {
        let arr = Object.entries(stocks).map(([symbol, data]) => {
            return { symbol, marketValue: data.marketValue, profit: data.profit, details: data.details };
        });
        // Sort descending by marketValue.
        arr.sort((a, b) => b.marketValue - a.marketValue);
        if (arr.length <= maxSlices) {
            // No grouping needed.
            arr.forEach(item => {
                window.stockDetails[item.symbol] = item.details;
            });
            return {
                labels: arr.map(item => item.symbol),
                marketValues: arr.map(item => item.marketValue),
                profits: arr.map(item => item.profit)
            };
        }
        // Otherwise, take top (maxSlices - 1) and group the rest as "Others".
        let top = arr.slice(0, maxSlices - 1);
        let others = arr.slice(maxSlices - 1);
        let othersMarketValue = others.reduce((sum, item) => sum + item.marketValue, 0);
        let othersProfit = others.reduce((sum, item) => sum + item.profit, 0);
        let othersSymbols = others.map(item => item.symbol);
        top.push({
            symbol: "Others",
            marketValue: othersMarketValue,
            profit: othersProfit,
            details: {
                quantity: "N/A",
                avg_price: "N/A",
                current_price: "N/A",
                invested_amount: "N/A",
                market_value: othersMarketValue,
                othersList: othersSymbols
            }
        });
        top.forEach(item => {
            window.stockDetails[item.symbol] = item.details;
        });
        return {
            labels: top.map(item => item.symbol),
            marketValues: top.map(item => item.marketValue),
            profits: top.map(item => item.profit)
        };
    }

    // Aggregation for sectors, market cap, and industries.
    function aggregateDataFromHoldings(holdingsData, positionsData) {
        const stockSymbols = [];
        const marketValues = [];
        const profits = [];
        const marketCapCategories = { "Large Cap": 0, "Mid Cap": 0, "Small Cap": 0 };
        const sectorAggregates = {};
        const industryAggregates = {};
        // New objects to store stock symbols per sector/industry/market cap.
        const sectorStocks = {};
        const industryStocks = {};
        const marketCapStocks = {
            "Large Cap": [],
            "Mid Cap": [],
            "Small Cap": []
        };

        [...holdingsData, ...positionsData].forEach(item => {
            const symbol = item["symbol"];
            let sector = item["sector"];
            let industry = item["industry"];
            const marketValue = parseFloat(item["market_cap"]);
            const profit = parseFloat(item["pl"]) || 0;

            if (!isNaN(marketValue)) {
                stockSymbols.push(symbol);
                marketValues.push(marketValue);
                profits.push(profit);
                if (marketValue >= 1000000) {
                    marketCapCategories["Large Cap"] += marketValue;
                    marketCapStocks["Large Cap"].push(symbol);
                } else if (marketValue >= 100000) {
                    marketCapCategories["Mid Cap"] += marketValue;
                    marketCapStocks["Mid Cap"].push(symbol);
                } else {
                    marketCapCategories["Small Cap"] += marketValue;
                    marketCapStocks["Small Cap"].push(symbol);
                }
                // Aggregate by sector.
                if (sector) {
                    if (!sectorAggregates[sector]) {
                        sectorAggregates[sector] = 0;
                        sectorStocks[sector] = [];
                    }
                    sectorAggregates[sector] += marketValue;
                    sectorStocks[sector].push(symbol);
                }
                // Aggregate by industry.
                if (industry) {
                    if (!industryAggregates[industry]) {
                        industryAggregates[industry] = 0;
                        industryStocks[industry] = [];
                    }
                    industryAggregates[industry] += marketValue;
                    industryStocks[industry].push(symbol);
                }
            }
        });
        // Store the mappings globally.
        window.sectorStocks = sectorStocks;
        window.industryStocks = industryStocks;
        window.marketCapStocks = marketCapStocks;

        return { stockSymbols, marketValues, profits, marketCapCategories, sectorAggregates, industryAggregates };
    }

    // Chart options with updated tooltip callback.
    const pieOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: '' // Set per chart below.
            },
            tooltip: {
                displayColors: false,
                callbacks: {
                    label: function (context) {
                        const chartId = context.chart.canvas.id;
                        const dataset = context.chart.data.datasets[0];
                        const total = dataset.data.reduce((acc, val) => acc + val, 0);
                        const currentValue = dataset.data[context.dataIndex];
                        const percentage = ((currentValue / total) * 100).toFixed(0);
                        const symbol = context.label;
                        const lines = [];
                        lines.push(symbol + " (" + percentage + "%)");
                        if (symbol === "Others") {
                            if (window.stockDetails["Others"] && window.stockDetails["Others"].othersList) {
                                const wrapped = wrapStockList(window.stockDetails["Others"].othersList, 5);
                                lines.push("Stocks:");
                                wrapped.forEach(line => lines.push("  " + line));
                            }
                        } else if (chartId === 'marketValueChart' || chartId === 'profitsChart') {
                            if (window.stockDetails[symbol]) {
                                const details = window.stockDetails[symbol];
                                lines.push("Qty: " + details.quantity);
                                lines.push("Avg: " + details.avg_price);
                                lines.push("Current: " + details.current_price);
                                lines.push("Invested: " + details.invested_amount);
                                lines.push("Market: " + details.market_value);
                            }
                        } else if (chartId === 'sectorsChart') {
                            if (window.sectorStocks && window.sectorStocks[symbol]) {
                                const wrapped = wrapStockList(window.sectorStocks[symbol], 5);
                                lines.push("Stocks:");
                                wrapped.forEach(line => lines.push("  " + line));
                            }
                        } else if (chartId === 'industriesChart') {
                            if (window.industryStocks && window.industryStocks[symbol]) {
                                const wrapped = wrapStockList(window.industryStocks[symbol], 5);
                                lines.push("Stocks:");
                                wrapped.forEach(line => lines.push("  " + line));
                            }
                        } else if (chartId === 'marketCapChart') {
                            if (window.marketCapStocks && window.marketCapStocks[symbol]) {
                                const wrapped = wrapStockList(window.marketCapStocks[symbol], 5);
                                lines.push("Stocks:");
                                wrapped.forEach(line => lines.push("  " + line));
                            }
                        }
                        return lines;
                    }
                }
            },
            legend: {
                position: 'bottom',
                labels: {
                    font: { size: 12 },
                    generateLabels: function (chart) {
                        const data = chart.data;
                        const dataset = data.datasets[0];
                        const total = dataset.data.reduce((acc, val) => acc + val, 0);
                        if (data.labels.length && dataset.data.length) {
                            let labelsArray = data.labels.map((label, i) => {
                                return {
                                    label: label,
                                    value: dataset.data[i],
                                    percentage: ((dataset.data[i] / total) * 100).toFixed(0),
                                    index: i
                                };
                            });
                            // Sorting is optional; if you want to keep original order, comment out the sort.
                            // labelsArray.sort((a, b) => b.value - a.value);
                            return labelsArray.map((item) => {
                                return {
                                    text: item.label + ' (' + item.percentage + '%)',
                                    fillStyle: getStandardColor(item.index),
                                    strokeStyle: getStandardColor(item.index),
                                    lineWidth: 1,
                                    hidden: false,
                                    index: item.index
                                };
                            });
                        }
                        return [];
                    }
                }
            }
        }
    };

    // Toggle between Stocks and Sectors tabs.
    function showChartTab(tab) {
        if (tab === 'stocks') {
            document.getElementById('stocksContent').classList.remove('hidden');
            document.getElementById('sectorsContent').classList.add('hidden');
            document.getElementById('stocksTab').classList.add('border-b-2', 'border-blue-500');
            document.getElementById('sectorsTab').classList.remove('border-b-2', 'border-blue-500');
            if (window.chart_marketValue) window.chart_marketValue.update();
            if (window.chart_profits) window.chart_profits.update();
            if (window.chart_marketCap) window.chart_marketCap.update();
        } else {
            document.getElementById('sectorsContent').classList.remove('hidden');
            document.getElementById('stocksContent').classList.add('hidden');
            document.getElementById('sectorsTab').classList.add('border-b-2', 'border-blue-500');
            document.getElementById('stocksTab').classList.remove('border-b-2', 'border-blue-500');
            if (window.chart_sectors) window.chart_sectors.update();
            if (window.chart_industries) window.chart_industries.update();
        }
    }

    function initializeCharts(holdings, positions) {
        // For stocks tab: aggregate and group stocks data.
        const stocksAggregated = aggregateStocksData(holdings, positions);
        const groupedStocks = groupStocksData(stocksAggregated, 10);

        // Sort groupedStocks by descending marketValue and update in-place
        (() => {
            const combined = groupedStocks.labels.map((label, i) => ({
                label,
                marketValue: groupedStocks.marketValues[i] ?? 0,
                profit: groupedStocks.profits[i] ?? 0
            })).sort((a, b) => b.marketValue - a.marketValue);

            Object.assign(groupedStocks, {
                labels: combined.map(item => item.label),
                marketValues: combined.map(item => item.marketValue),
                profits: combined.map(item => item.profit)
            });
        })();

        const marketValueData = {
            labels: groupedStocks.labels,
            datasets: [{
                data: groupedStocks.marketValues,
                backgroundColor: groupedStocks.labels.map((_, i) => getStandardColor(i))
            }]
        };

        const profitsData = {
            labels: groupedStocks.labels,
            datasets: [{
                data: groupedStocks.profits,
                backgroundColor: groupedStocks.labels.map((_, i) => getStandardColor(i))
            }]
        };

        // For sectors, market cap, and industries, use the original aggregation.
        const aggregated = aggregateDataFromHoldings(holdings, positions);
        const marketCapLabels = ["Large Cap", "Mid Cap", "Small Cap"];
        const marketCapValues = marketCapLabels.map(label => aggregated.marketCapCategories[label]);
        const marketCapData = {
            labels: marketCapLabels,
            datasets: [{
                data: marketCapValues,
                backgroundColor: marketCapLabels.map((_, i) => getStandardColor(i))
            }]
        };

        const sectorEntries = Object.entries(aggregated.sectorAggregates).sort((a, b) => b[1] - a[1]);
        const sectorLabels = sectorEntries.map(e => e[0]);
        const sectorValues = sectorEntries.map(e => e[1]);
        const sectorsData = {
            labels: sectorLabels,
            datasets: [{
                data: sectorValues,
                backgroundColor: sectorLabels.map((_, i) => getStandardColor(i))
            }]
        };

        const industryEntries = Object.entries(aggregated.industryAggregates).sort((a, b) => b[1] - a[1]);
        const industryLabels = industryEntries.map(e => e[0]);
        const industryValues = industryEntries.map(e => e[1]);
        const industriesData = {
            labels: industryLabels,
            datasets: [{
                data: industryValues,
                backgroundColor: industryLabels.map((_, i) => getStandardColor(i))
            }]
        };

        function getChartOptionsWithTitle(titleText) {
            return Object.assign({}, pieOptions, {
                plugins: Object.assign({}, pieOptions.plugins, {
                    title: Object.assign({}, pieOptions.plugins.title, {
                        text: titleText
                    })
                })
            });
        }

        // Helper to render placeholder if all data is zero
        function renderPlaceholderIfAllZero(canvasId, dataArray, message) {
            const canvas = document.getElementById(canvasId);
            if (dataArray.every(val => val === 0)) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = '16px Arial';
            ctx.fillStyle = '#000';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(message, canvas.width / 2, canvas.height / 2);
            return true;
            }
            return false;
        }

        // Market Value Chart
        if (!renderPlaceholderIfAllZero('marketValueChart', groupedStocks.marketValues, 'No Market Value Data Available')) {
            window.chart_marketValue = new Chart(document.getElementById('marketValueChart').getContext('2d'), {
            type: 'pie',
            data: marketValueData,
            options: getChartOptionsWithTitle('Market Value')
            });
        }

        // Profits Chart
        if (!renderPlaceholderIfAllZero('profitsChart', groupedStocks.profits, 'No Profit/Loss Data Available')) {
            window.chart_profits = new Chart(document.getElementById('profitsChart').getContext('2d'), {
            type: 'pie',
            data: profitsData,
            options: getChartOptionsWithTitle('Profit/Loss')
            });
        }

        // Market Cap Chart
        if (!renderPlaceholderIfAllZero('marketCapChart', marketCapData.datasets[0].data, 'No Market Cap Data Available')) {
            window.chart_marketCap = new Chart(document.getElementById('marketCapChart').getContext('2d'), {
            type: 'pie',
            data: marketCapData,
            options: getChartOptionsWithTitle('Market Cap (Large/Mid/Small)')
            });
        }

        // Sectors Chart
        if (!renderPlaceholderIfAllZero('sectorsChart', sectorsData.datasets[0].data, 'No Sector Data Available')) {
            window.chart_sectors = new Chart(document.getElementById('sectorsChart').getContext('2d'), {
            type: 'pie',
            data: sectorsData,
            options: getChartOptionsWithTitle('Sectors')
            });
        }

        // Industries Chart
        if (!renderPlaceholderIfAllZero('industriesChart', industriesData.datasets[0].data, 'No Industry Data Available')) {
            window.chart_industries = new Chart(document.getElementById('industriesChart').getContext('2d'), {
            type: 'pie',
            data: industriesData,
            options: getChartOptionsWithTitle('Industries')
            });
        }

        showChartTab('stocks');
    };
</script>