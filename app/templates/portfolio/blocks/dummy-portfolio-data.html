<script>
    // Define dummy portfolio data for non-authenticated users
    const dummyPortfolios = [
        {
            "uid": "95a654ac-e477-47b7-b646-b429909e0529",
            "name": "Growth Portfolio",
            "holdings": [
                {
                    "symbol": "RELIANCE",
                    "company_name": "Reliance Industries Ltd",
                    "sector": "Oil & Gas",
                    "industry": "Integrated Oil & Gas",
                    "quantity": 20,
                    "avg_price": 2050.50,
                    "current_price": 2456.75,
                    "invested_amount": 41010.00,
                    "market_value": 49135.00,
                    "pl": 8125.00,
                    "pl_percentage": 19.81,
                    "pe_ratio": 28.4,
                    "market_cap": *************,
                    "week_52_low": 2010.25,
                    "week_52_high": 2530.00,
                    "status": "active",
                },
                {
                    "symbol": "HDFC",
                    "company_name": "HDFC Bank Ltd",
                    "sector": "Financial Services",
                    "industry": "Banking",
                    "quantity": 25,
                    "avg_price": 1545.25,
                    "current_price": 1738.40,
                    "invested_amount": 38631.25,
                    "market_value": 43460.00,
                    "pl": 4828.75,
                    "pl_percentage": 12.50,
                    "pe_ratio": 19.7,
                    "market_cap": *************,
                    "week_52_low": 1520.45,
                    "week_52_high": 1840.10,
                    "status": "active",
                },
                {
                    "symbol": "HDFCLIFE",
                    "company_name": "HDFC Life Insurance Co Ltd",
                    "sector": "Financial Services",
                    "industry": "Life Insurance",
                    "quantity": 40,
                    "avg_price": 580.75,
                    "current_price": 642.80,
                    "invested_amount": 23230.00,
                    "market_value": 25712.00,
                    "pl": 2482.00,
                    "pl_percentage": 10.68,
                    "pe_ratio": 24.2,
                    "market_cap": 389620000000,
                    "week_52_low": 524.20,
                    "week_52_high": 681.35,
                    "status": "active",
                },
            ],
            "positions": [
                {
                    "symbol": "NIFTY",
                    "instrument": "option",
                    "type": "CE",
                    "strike": 21500,
                    "expiry": "2024-04-25",
                    "quantity": 1,
                    "entry_price": 155.25,
                    "current_price": 225.40,
                    "pl": 7015.00,
                    "pl_percentage": 45.18,
                    "status": "active",
                    "side": "buy",
                    "lot_size": 50,
                }
            ],
            "created_at": "2025-02-19T09:33:58.196000",
            "updated_at": "2025-02-21T04:43:12.068000",
            "domain_user_id": "67b43862ab6b1d4f96b0c6b8",
            "holdings_count": 3,
            "positions_count": 1,
            "total_pl": 22450.75,
            "total_pl_percentage": 21.85,
            "total_value": 125322.00,
        },
        {
            "uid": "405ec80f-59e2-4488-a7fa-a8749a923293",
            "name": "Income Portfolio",
            "holdings": [
                {
                    "symbol": "INFY",
                    "company_name": "Infosys Ltd",
                    "sector": "Information Technology",
                    "industry": "IT Consulting & Services",
                    "quantity": 50,
                    "avg_price": 1320.80,
                    "current_price": 1476.30,
                    "invested_amount": 66040.00,
                    "market_value": 73815.00,
                    "pl": 7775.00,
                    "pl_percentage": 11.77,
                    "pe_ratio": 25.3,
                    "market_cap": ************,
                    "week_52_low": 1230.75,
                    "week_52_high": 1670.80,
                    "status": "active",
                },
                {
                    "symbol": "TCS",
                    "company_name": "Tata Consultancy Services Ltd",
                    "sector": "Information Technology",
                    "industry": "IT Consulting & Services",
                    "quantity": 15,
                    "avg_price": 3145.50,
                    "current_price": 3450.75,
                    "invested_amount": 47182.50,
                    "market_value": 51761.25,
                    "pl": 4578.75,
                    "pl_percentage": 9.70,
                    "pe_ratio": 27.8,
                    "market_cap": 1264000000000,
                    "week_52_low": 3100.25,
                    "week_52_high": 3800.10,
                    "status": "active",
                },
                {
                    "symbol": "BAJFINANCE",
                    "company_name": "Bajaj Finance Ltd",
                    "sector": "Financial Services",
                    "industry": "NBFC",
                    "quantity": 12,
                    "avg_price": 6245.30,
                    "current_price": 6985.55,
                    "invested_amount": 74943.60,
                    "market_value": 83826.60,
                    "pl": 8883.00,
                    "pl_percentage": 11.85,
                    "pe_ratio": 32.4,
                    "market_cap": ************,
                    "week_52_low": 5748.90,
                    "week_52_high": 7970.25,
                    "status": "active",
                },
            ],
            "positions": [
                {
                    "symbol": "BANKNIFTY",
                    "instrument": "option",
                    "type": "CE",
                    "strike": 45000,
                    "expiry": "2024-04-25",
                    "quantity": 1,
                    "entry_price": 320.50,
                    "current_price": 425.30,
                    "pl": 1572.00,
                    "pl_percentage": 32.70,
                    "status": "active",
                    "side": "buy",
                    "lot_size": 15,
                },
                {
                    "symbol": "SBIN",
                    "instrument": "option",
                    "type": "CE",
                    "strike": 640,
                    "expiry": "2024-05-30",
                    "quantity": 2,
                    "entry_price": 18.75,
                    "current_price": 26.80,
                    "pl": 1610.00,
                    "pl_percentage": 42.93,
                    "status": "active",
                    "side": "buy",
                    "lot_size": 1000,
                }
            ],
            "total_value": 212584.85,
            "total_pl": 24418.75,
            "total_pl_percentage": 13.98,
            "holdings_count": 3,
            "domain_user_id": "67b43862ab6b1d4f96b0c6b8",
            "created_at": "2025-02-19T06:32:03.494000",
            "updated_at": "2025-02-21T06:32:10.665000",
            "positions_count": 2,
        },
        {
            "uid": "app-1f814942-ddef-4df4-bca3-0e99d70b019e",
            "app_id": "1f814942-ddef-4df4-bca3-0e99d70b019e",
            "type": "Dhan",
            "name": "Dhan",
            "svg": "{{ cdn('/static/images/apps/dhan.svg') }}",
            "holdings": [
                {
                    "symbol": "HDFC",
                    "company_name": "HDFC Bank Ltd",
                    "sector": "Financial Services",
                    "industry": "Banking",
                    "quantity": 15,
                    "avg_price": 1625.25,
                    "current_price": 1738.40,
                    "invested_amount": 24378.75,
                    "market_value": 26076.00,
                    "pl": 1697.25,
                    "pl_percentage": 6.96,
                    "pe_ratio": 19.7,
                    "market_cap": *************,
                    "week_52_low": 1520.45,
                    "week_52_high": 1840.10,
                    "status": "active",
                },
                {
                    "symbol": "TATAMOTORS",
                    "company_name": "Tata Motors Ltd",
                    "sector": "Automobile",
                    "industry": "Automobile Manufacturers",
                    "quantity": 40,
                    "avg_price": 780.50,
                    "current_price": 878.20,
                    "invested_amount": 31220.00,
                    "market_value": 35128.00,
                    "pl": 3908.00,
                    "pl_percentage": 12.52,
                    "pe_ratio": 14.6,
                    "market_cap": 294100000000,
                    "week_52_low": 623.30,
                    "week_52_high": 915.60,
                    "status": "active",
                }
            ],
            "positions": [
                {
                    "symbol": "NIFTY",
                    "instrument": "option",
                    "type": "PE",
                    "strike": 21000,
                    "expiry": "2024-05-30",
                    "quantity": 1,
                    "entry_price": 142.50,
                    "current_price": 165.30,
                    "pl": 1140.00,
                    "pl_percentage": 15.99,
                    "status": "active",
                    "side": "buy",
                    "lot_size": 50,
                }
            ],
            "status": "Active",
            "holdings_count": 2,
            "positions_count": 1,
            "total_value": 62344.00,
            "total_pl": 6745.25,
            "total_pl_percentage": 12.14,
        },
    ];

    // Make the dummy data accessible to the main page
    window.dummyPortfolioData = {
        is_demo: true,
        portfolios: dummyPortfolios,
        holdings: [],
        positions: [],
        total_value: 0,
        total_pl: 0,
        total_pl_percentage: 0,
        total_holdings: 0,
        combined_total_pl_percentage: {}
    };

    // Function to load dummy portfolio data
    function loadDummyData() {
        try {
            showLoading("Loading demo portfolio data...");

            // Combine all holdings and positions from portfolios
            let allHoldings = [];
            let allPositions = [];
            let totalValue = 0;
            let totalPL = 0;
            let totalCost = 0;

            // Process each portfolio
            dummyPortfolios.forEach(portfolio => {
                // Add to totals
                totalValue += portfolio.total_value || 0;
                totalPL += portfolio.total_pl || 0;

                // Track portfolio P/L percentages
                window.dummyPortfolioData.combined_total_pl_percentage[portfolio.uid] = portfolio.total_pl_percentage || 0;

                // Add holdings and positions, with portfolio reference
                portfolio.holdings.forEach(holding => {
                    const holdingCopy = { ...holding };
                    holdingCopy.portfolio_name = portfolio.name;
                    allHoldings.push(holdingCopy);
                    totalCost += holding.invested_amount || 0;
                });

                portfolio.positions.forEach(position => {
                    const positionCopy = { ...position };
                    positionCopy.portfolio_name = portfolio.name;
                    allPositions.push(positionCopy);
                });
            });

            // Update the portfolioData object
            window.dummyPortfolioData.holdings = allHoldings;
            window.dummyPortfolioData.positions = allPositions;
            window.dummyPortfolioData.total_value = totalValue;
            window.dummyPortfolioData.total_pl = totalPL;
            window.dummyPortfolioData.total_pl_percentage = totalCost > 0 ? (totalPL / totalCost * 100) : 0;
            window.dummyPortfolioData.total_holdings = allHoldings.length;

            // Update the UI
            document.getElementById('total-value').textContent = formatIndianNumber(Number(Math.abs(totalValue)));
            document.getElementById('total-holdings').textContent = allHoldings.length.toLocaleString('en-IN');

            // Set P/L color and value
            const totalPlElement = document.getElementById('total-pl');
            const isProfit = totalPL >= 0;

            totalPlElement.innerHTML = `${formatIndianNumber(Number(Math.abs(totalPL)))} 
                <span class="text-sm">(${formatIndianNumber(Number(Math.abs(window.dummyPortfolioData.total_pl_percentage)))}%)</span>`;
            totalPlElement.classList.toggle('text-green-600', isProfit);
            totalPlElement.classList.toggle('text-red-600', !isProfit);

            // Update portfolio list P/L percentages
            Object.keys(window.dummyPortfolioData.combined_total_pl_percentage).forEach(uid => {
                const totalPlSpan = document.querySelector(`span[data-uid="${uid}"].total-pl-percentage`);
                const totalPlPercentage = window.dummyPortfolioData.combined_total_pl_percentage[uid];
                if (totalPlSpan && totalPlPercentage !== undefined) {
                    const percentage = formatIndianNumber(Number(Math.abs(totalPlPercentage)));
                    totalPlSpan.textContent = `${percentage}%`;
                    totalPlSpan.classList.toggle('text-green-600', totalPlPercentage >= 0);
                    totalPlSpan.classList.toggle('text-red-600', totalPlPercentage < 0);
                }
            });

            // Set global variables for other functions to use
            window.holdings = allHoldings;
            window.positions = allPositions;

            // Initialize charts if the function exists
            if (typeof initializeCharts === 'function') {
                initializeCharts(allHoldings, allPositions);
            }

            // Split positions for display
            const equityPositions = allPositions.filter(position => position.instrument === 'equity');
            const foPositions = allPositions.filter(position => position.instrument !== 'equity');

            // Populate tables
            populateHoldingsTable(allHoldings);
            populateEquityPositions(equityPositions);
            populateFoPositions(foPositions);

            // Populate portfolio sidebar with dummy portfolios
            const portfolioSidebar = document.getElementById('portfolio-list-container');
            if (portfolioSidebar) {
                // Clear existing content
                portfolioSidebar.innerHTML = '';

                // Add each portfolio to the sidebar
                dummyPortfolios.forEach(portfolio => {
                    const portfolioLink = document.createElement('a');
                    portfolioLink.href = '#'; // Prevent navigation but keep link style
                    portfolioLink.className = 'group flex flex-wrap gap-3 items-center justify-between p-2 text-sm rounded-lg hover:bg-gray-50 dark:hover:bg-neutral-700 demo-enabled';
                    portfolioLink.addEventListener('click', (e) => {
                        e.preventDefault();
                        showModal("Login or sign up to create your own portfolio and access all features!");
                    });

                    // Create content for the portfolio link
                    let portfolioHTML = `
                        <div class="flex flex-wrap items-start gap-x-1 min-w-0">
                    `;

                    // Add SVG icon if available, otherwise use default
                    if (portfolio.svg) {
                        portfolioHTML += `<img src="${portfolio.svg}" class="w-5 h-5 shrink-0">`;
                    } else {
                        portfolioHTML += `
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 shrink-0" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 12h.01" />
                                <path d="M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2" />
                                <path d="M22 13a18.15 18.15 0 0 1-20 0" />
                                <rect width="20" height="14" x="2" y="6" rx="2" />
                            </svg>
                        `;
                    }

                    // Add portfolio name
                    portfolioHTML += `<span class="truncate">${portfolio.name}</span>
                        </div>
                        <div class="flex items-center space-x-2">`;

                    // Show delete button for manual portfolios
                    if (portfolio.uid && (!portfolio.type || portfolio.type === "manual")) {
                        portfolioHTML += `
                            <button 
                                class="hidden group-hover:block text-red-600 bg-red-50 rounded-md dark:hover:bg-neutral-700 transition-all">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 6h18" />
                                    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                                    <path d="M10 11v6" />
                                    <path d="M14 11v6" />
                                    <path d="M5 6h14l-1 14H6L5 6z" />
                                </svg>
                            </button>
                        `;
                    }

                    // Add P&L percentage with appropriate color
                    const plPercentage = portfolio.total_pl_percentage || 0;
                    const formattedPL = formatIndianNumber(Math.abs(plPercentage));
                    const plClass = plPercentage >= 0 ? 'text-green-600' : 'text-red-600';

                    portfolioHTML += `
                            <span data-uid="${portfolio.uid}" class="total-pl-percentage text-sm ${plClass}">
                                ${formattedPL}%
                            </span>
                        </div>
                    `;

                    portfolioLink.innerHTML = portfolioHTML;
                    portfolioSidebar.appendChild(portfolioLink);
                });
            }

        } catch (error) {
            console.error('Error loading dummy portfolio data:', error);
        } finally {
            hideLoading();
        }
    }
</script>