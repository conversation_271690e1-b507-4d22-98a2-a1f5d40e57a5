<!-- Equity Positions Table -->
<div id="equity-positions-container" class="hidden mb-4 mx-3">
    <h2 class="text-lg font-semibold">Equity Positions</h2>
    <table class="text-sm w-full mt-2 bg-white dark:bg-neutral-800 divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-neutral-700" data-table="equity">
            <tr>
                <th data-key="symbol"
                    class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Symbol</th>
                <th data-key="quantity"
                    class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Quantity</th>
                <th data-key="avg_price"
                    class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Avg Price</th>
                <th data-key="current_price"
                    class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Current Price</th>
                <th data-key="invested_amount"
                    class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Invested Amount</th>
                <th data-key="market_value"
                    class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Market Value</th>
                <th data-key="pl"
                    class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    P/L</th>
            </tr>
        </thead>
        <tbody id="equity-positions-table-body"
            class="bg-white dark:bg-neutral-800 divide-y divide-gray-200 dark:divide-gray-700">
            <!-- Data will be populated here by JavaScript -->
        </tbody>
    </table>
</div>

<!-- F&O Positions Table -->
<div class="mb-4 mx-3">
    <div id="fo-positions-heading" class="hidden p-3 flex justify-between items-center">
        <h2 class=" text-lg font-semibold">F&O Positions</h2>
        <button onclick="getPortfolioAIFeedback('portfolio_fno_positions')"
            class="animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-sparkles">
                <path
                    d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                <path d="M20 3v4" />
                <path d="M22 5h-4" />
                <path d="M4 17v2" />
                <path d="M5 18H3" />
            </svg> Get AI Feedback
        </button>
    </div>
    <div class="overflow-auto">
        <table id="fo-positions-table"
            class="hidden text-sm w-full mt-2 bg-white dark:bg-neutral-800 divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-neutral-700" data-table="fo">
                <tr>
                    <th data-key="symbol"
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Symbol</th>
                    <th data-key="type"
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Type</th>
                    <th data-key="side"
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Side</th>
                    <th data-key="strike"
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Strike</th>
                    <th data-key="expiry"
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Expiry</th>
                    <th data-key="quantity"
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Quantity</th>
                    <th data-key="entry"
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Entry</th>
                    <th data-key="current"
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Current</th>
                    <th data-key="pl"
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        P/L</th>
                </tr>
            </thead>
            <tbody id="fo-positions-table-body"
                class="bg-white dark:bg-neutral-800 divide-y divide-gray-200 dark:divide-gray-700">
                <!-- Data will be populated here by JavaScript -->
            </tbody>
        </table>
    </div>
    <div id="no-fo-positions" class="text-center py-12">
        <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-gray-200">No positions</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {% if portfolio_uid %}
            Get started by adding a new position to your portfolio
            {% else %}
            No positions found across all portfolios
            {% endif %}
        </p>
        {% if portfolio_uid and (not portfolio.type or portfolio.type == "manual") %}
        <div class="mt-6">
            <button onclick="showAddPositionModal()"
                class="bg-blue-100 font-medium hover:bg-blue-500 hover:text-white px-4 py-3 w-64 rounded-md text-blue-500 text-sm transition-colors">

                Add Position
            </button>
        </div>
        {% endif %}
    </div>
</div>

<script>
    function populateEquityPositions(equityPositions) {
        const equityPositionsTableBody = document.getElementById('equity-positions-table-body');  // Ensure this ID exists in the HTML

        // Clear existing rows
        equityPositionsTableBody.innerHTML = '';

        // If no equity positions, show the message and return
        if (equityPositions.length === 0) {
            document.getElementById('equity-positions-container').classList.add('hidden');
            document.getElementById('fo-positions-heading').classList.add('hidden');
            return;
        } else {
            document.getElementById('equity-positions-container').classList.remove('hidden');
            document.getElementById('fo-positions-heading').classList.remove('hidden');
        }

        // Loop through each equity position and add a row
        equityPositions.forEach(position => {
            const row = document.createElement('tr');
            row.classList.add('hover:bg-gray-50', 'dark:hover:bg-neutral-700', 'group');

            row.innerHTML = `
                <td class="px-4 py-2">
                    <div>
                        <div class="font-medium">${position.symbol}</div>
                        ${position.sector ? `
                            <div class="text-xs text-gray-600 inline-block">
                                ${position.sector} (${position.industry || ''})
                            </div>` : ''}
                    </div>
                </td>
                <td class="px-4 py-2">${Number(position.quantity).toLocaleString() || 'N/A'}</td>
                <td class="px-4 py-2">${Number((position.avg_price).toFixed(2)).toLocaleString() || 'N/A'}</td>
                <td class="px-4 py-2">${position.current_price ? Number(position.current_price.toFixed(2)).toLocaleString() : '<span class="text-red-600">N/A</span>'}</td>
                <td class="px-4 py-2">${position.invested_amount ? Number(position.invested_amount.toFixed(2)).toLocaleString() : '<span class="text-red-600">N/A</span>'}</td>
                <td class="px-4 py-2">${position.market_value ? Number(position.market_value.toFixed(2)).toLocaleString() : '<span class="text-red-600">N/A</span>'}</td>
                <td class="px-4 py-2">
                    ${position.pl !== null ? `
                        <span class="${position.pl >= 0 ? 'text-green-600' : 'text-red-600'}">
                            ${Number(Math.abs(position.pl).toFixed(2)).toLocaleString()}
                            (${Math.abs(position.pl_percentage || 0).toFixed(2)}%)
                        </span>
                    ` : '<span class="text-red-600">N/A</span>'}
                </td>
            `;
            equityPositionsTableBody.appendChild(row);
        });
    }

    function populateFoPositions(foPositions) {
        const foPositionsTableBody = document.getElementById('fo-positions-table-body');  // Ensure this ID exists in the HTML

        // Clear existing rows
        foPositionsTableBody.innerHTML = '';

        // If no F&O positions, show the message and return
        if (foPositions.length === 0) {
            document.getElementById('fo-positions-table').classList.add('hidden');
            document.getElementById('no-fo-positions').classList.remove('hidden');
            return;
        } else {
            document.getElementById('fo-positions-table').classList.remove('hidden');
            document.getElementById('no-fo-positions').classList.add('hidden');
        }

        // Loop through each F&O position and add a row
        foPositions.forEach(position => {
            const row = document.createElement('tr');
            row.classList.add('hover:bg-gray-50', 'dark:hover:bg-neutral-700', 'group');

            row.innerHTML = `
                <td class="px-4 py-2">${position.symbol}</td>
                <td class="px-4 py-2">
                    <span class="px-2 py-1 text-xs rounded-full 
                        ${position.instrument === 'future' ? 'bg-yellow-100 text-yellow-800' : position.type === 'CE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        ${position.instrument === 'future' ? 'FUT' : position.type}
                    </span>
                </td>
                <td class="px-4 py-2">
                    <span class="px-2 py-1 text-xs rounded-full 
                        ${position.side === 'sell' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}">
                        ${position.side === 'sell' ? 'Sell' : 'Buy'}
                    </span>
                </td>
                <td class="px-4 py-2">${position.instrument === 'option' ? Number(position.strike).toLocaleString() : '-'}</td>
                <td class="px-4 py-2">
                    <div class="text-sm">${position.expiry}</div>
                    <div class="text-xs text-gray-500 pt-1">Entry: ${position.entry_date}</div>
                </td>
                <td class="px-4 py-2">${Number(position.quantity).toLocaleString()}</td>
                <td class="px-4 py-2">${position.entry_price ? Number(position.entry_price.toFixed(2)).toLocaleString() : '-'}</td>
                <td class="px-4 py-2">${position.current_price ? Number(position.current_price.toFixed(2)).toLocaleString() : '<span class="text-red-600">N/A</span>'}</td>
                <td class="px-4 py-2">
                    ${position.pl !== null ? `
                        <span class="${position.pl >= 0 ? 'text-green-600' : 'text-red-600'}">
                            ${Number(Math.abs(position.pl).toFixed(2)).toLocaleString()}
                            (${Math.abs(position.pl_percentage || 0).toFixed(2)}%)
                        </span>
                    ` : '<span class="text-red-600">N/A</span>'}
                </td>
            `;
            foPositionsTableBody.appendChild(row);
        });
    }

    const tableConfigs = {
        equity: {
            tbodyId: 'equity-positions-table-body',
            columns: {
                symbol: 0, quantity: 1, avg_price: 2, current_price: 3,
                invested_amount: 4, market_value: 5, pl: 6
            }
        },
        fo: {
            tbodyId: 'fo-positions-table-body',
            columns: {
                symbol: 0, type: 1, side: 2, strike: 3, expiry: 4,
                quantity: 5, entry: 6, current: 7, pl: 8
            },
            specialColumns: ['type', 'side']
        }
    };

    // Sorting state
    const sortStates = { equity: { direction: -1 }, fo: { direction: -1 } };

    // Generic sorting function
    function sortTable(tableType, key) {
        const config = tableConfigs[tableType];
        const tbody = document.getElementById(config.tbodyId);
        const rows = Array.from(tbody.children);
        const colIndex = config.columns[key];
        const state = sortStates[tableType];

        const getValue = (row, index) => {
            const cell = row.children[index];
            return config.specialColumns?.includes(key)
                ? cell.querySelector('span')?.textContent || cell.textContent
                : cell.textContent;
        };

        rows.sort((a, b) => {
            const valA = getValue(a, colIndex).trim().replace(/[,%₹]/g, "");
            const valB = getValue(b, colIndex).trim().replace(/[,%₹]/g, "");
            const isNumeric = !isNaN(parseFloat(valA)) && !isNaN(parseFloat(valB));

            return isNumeric
                ? state.direction * (parseFloat(valA) - parseFloat(valB))
                : state.direction * valA.localeCompare(valB);
        });

        state.direction *= -1;

        // Update UI
        const thead = document.querySelector(`thead[data-table="${tableType}"]`);
        thead.querySelectorAll('.caret').forEach(span => span.remove());

        const th = thead.querySelector(`th[data-key="${key}"]`);
        th.insertAdjacentHTML('beforeend', `<span class="caret">${state.direction === -1 ? "▼" : "▲"}</span>`);

        tbody.replaceChildren(...rows);
    }

    document.querySelectorAll('thead[data-table]').forEach(thead => {
        thead.addEventListener('click', (e) => {
            const th = e.target.closest('th[data-key]');
            if (th) {
                const tableType = thead.dataset.table;
                sortTable(tableType, th.dataset.key);
            }
        });
    });

    const thStyles = 'cursor-pointer hover:underline px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider';
    document.querySelectorAll('th[data-key]').forEach(th => th.className = thStyles);
</script>