<div id="addPositionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white dark:bg-neutral-800 rounded-xl w-full max-w-xl">
        <div class="px-6 py-4 border-b">
            <h2 class="text-xl font-semibold">Add Position</h2>
        </div>
        <div class="bg-neutral-100  p-3 custom-scroll max-h-[calc(100vh_-_10rem)] overflow-auto rounded-b-xl">
            <form onsubmit="addPosition(event)" class="bg-white p-5 rounded-lg">
                <div class="space-y-4">
                    <!-- Symbol Search -->
                    <div>
                        <label class="block font-medium mb-2 text-sm">
                            Symbol
                        </label>
                        <div class="relative">
                            <input type="text" id="position-symbol" required
                                class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                            <div id="position-search-results"
                                class="absolute left-0 right-0 top-full mt-1 bg-white dark:bg-neutral-800 border dark:border-neutral-700 rounded-md shadow-lg hidden">
                            </div>
                        </div>
                    </div>

                    <!-- Position Type -->
                    <div>
                        <label class="block font-medium mb-2 text-sm">
                            Instrument Type
                        </label>
                        <div
                            class="border border-gray-300 dark:border-neutral-600 cursor-pointer dark:bg-neutral-700 focus:border focus:border-neutral-400 focus:outline-0  px-4 py-2.5 rounded-md shadow-sm">
                            <select id="position-instrument" required onchange="handleInstrumentChange()"
                                class="bg-transparent block cursor-pointer dark:bg-neutral-700 dark:text-neutral-100 focus:outline-0 text-sm w-full">
                                <option value="option">Option</option>
                                <option value="future">Future</option>
                            </select>
                        </div>
                    </div>

                    <!-- Option Type (Only for Options) -->
                    <div id="option-type-container">
                        <label class="block font-medium mb-2 text-sm">
                            Option Type
                        </label>
                        <div
                            class="border border-gray-300 dark:border-neutral-600 cursor-pointer dark:bg-neutral-700 focus:border focus:border-neutral-400 focus:outline-0  px-4 py-2.5 rounded-md shadow-sm">
                            <select id="position-type" required
                                class="bg-transparent block cursor-pointer dark:bg-neutral-700 dark:text-neutral-100 focus:outline-0 text-sm w-full">
                                <option value="CE">Call (CE)</option>
                                <option value="PE">Put (PE)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Strike Price (Only for Options) -->
                    <div id="strike-price-container">
                        <label class="block font-medium mb-2 text-sm">
                            Strike Price
                        </label>
                        <input type="number" id="position-strike" required step="0.05"
                            class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                    </div>

                    <!-- Expiry Date -->
                    <div>
                        <label class="block font-medium mb-2 text-sm">
                            Expiry Date
                        </label>
                        <input type="date" id="position-expiry" required
                            class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                    </div>

                    <!-- Quantity -->
                    <div>
                        <label class="block font-medium mb-2 text-sm">
                            Quantity
                        </label>
                        <input type="number" id="position-quantity" required min="1"
                            class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                    </div>

                    <!-- Lot Size -->
                    <div id="lot-size-container">
                        <label class="block font-medium mb-2 text-sm">
                            Lot Size
                        </label>
                        <input type="number" id="position-lot-size" required min="1" value="1"
                            class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                    </div>

                    <!-- Entry Price -->
                    <div>
                        <label id="price-label" class="block font-medium mb-2 text-sm">
                            Premium Per Lot
                        </label>
                        <input type="number" id="position-price" required min="0" step="0.05"
                            class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400" id="price-help-text">
                            For options, enter premium per lot. For futures, enter price per unit.
                        </p>
                    </div>

                    <!-- Entry Date -->
                    <div>
                        <label class="block font-medium mb-2 text-sm">
                            Entry Date
                        </label>
                        <input type="date" id="position-entry-date" required
                            class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all"
                            max="{{ today_date }}" value="{{ today_date }}">
                    </div>

                    <!-- Position Side -->
                    <div>
                        <label class="block font-medium mb-2 text-sm">
                            Position Side
                        </label>
                        <div class="border border-gray-300 dark:border-neutral-600 cursor-pointer dark:bg-neutral-700
                            focus:border focus:border-neutral-400 focus:outline-0 px-4 py-2.5 rounded-md shadow-sm">
                            <select id="position-side" required
                                class="bg-transparent block cursor-pointer dark:bg-neutral-700 dark:text-neutral-100 focus:outline-0 text-sm w-full">
                                <option value="sell">Sell (Write)</option>
                                <option value="buy">Buy (Long)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="hideAddPositionModal()"
                        class="bg-gray-200 font-medium hover:bg-gray-300 px-4 py-2 rounded-md text-neutral-900 text-sm">Cancel</button>
                    <button type="submit"
                        class="bg-blue-500 font-medium hover:bg-blue-600 px-4 py-2 rounded-md text-sm text-white">Add</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Position Modal Controls
    function showAddPositionModal() {
        document.getElementById('addPositionModal').classList.remove('hidden');
        document.getElementById('addPositionModal').classList.add('flex');
    }

    function hideAddPositionModal() {
        document.getElementById('addPositionModal').classList.add('hidden');
        document.getElementById('addPositionModal').classList.remove('flex');
        document.getElementById('position-search-results').classList.add('hidden');
    }

    // Position Symbol Search
    let positionSearchTimeout;
    const positionSearchInput = document.getElementById('position-symbol');
    const positionSearchResults = document.getElementById('position-search-results');

    positionSearchInput.addEventListener('input', function (e) {
        clearTimeout(positionSearchTimeout);
        const query = e.target.value;

        if (query.length < 1) {
            positionSearchResults.classList.add('hidden');
            return;
        }

        positionSearchTimeout = setTimeout(async () => {
            try {
                const response = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/search/find?query=${query}`);
                if (!response.ok) throw new Error('Search failed');

                const results = await response.json();
                displayPositionSearchResults(results.result);
            } catch (error) {
                console.error('Search error:', error);
            }
        }, 300);
    });

    function displayPositionSearchResults(results) {
        positionSearchResults.innerHTML = '';

        if (results && results.length > 0) {
            const ul = document.createElement('ul');
            ul.className = 'py-1';

            results.forEach(symbol => {
                const li = document.createElement('li');
                li.className = 'px-4 py-2 hover:bg-gray-100 dark:hover:bg-neutral-700 cursor-pointer';
                li.innerHTML = `<div class="font-medium">${symbol}</div>`;
                li.onclick = () => {
                    positionSearchInput.value = symbol;
                    positionSearchResults.classList.add('hidden');
                };
                ul.appendChild(li);
            });

            positionSearchResults.appendChild(ul);
            positionSearchResults.classList.remove('hidden');
        } else {
            positionSearchResults.classList.add('hidden');
        }
    }

    function handleInstrumentChange() {
        const instrumentType = document.getElementById('position-instrument').value;
        const optionTypeContainer = document.getElementById('option-type-container');
        const strikePriceContainer = document.getElementById('strike-price-container');
        const lotSizeContainer = document.getElementById('lot-size-container');
        const priceLabel = document.getElementById('price-label');
        const priceHelpText = document.getElementById('price-help-text');
        const strikeInput = document.getElementById('position-strike');
        const typeSelect = document.getElementById('position-type');
        const lotSizeInput = document.getElementById('position-lot-size');

        if (instrumentType === 'option') {
            optionTypeContainer.style.display = 'block';
            strikePriceContainer.style.display = 'block';
            lotSizeContainer.style.display = 'block';
            priceLabel.textContent = 'Premium Per Lot';
            priceHelpText.textContent = 'Enter the premium per lot (not per share)';
            strikeInput.setAttribute('required', 'required');
            typeSelect.setAttribute('required', 'required');
            lotSizeInput.setAttribute('required', 'required');
        } else {
            optionTypeContainer.style.display = 'none';
            strikePriceContainer.style.display = 'none';
            lotSizeContainer.style.display = 'block';
            priceLabel.textContent = 'Entry Price';
            priceHelpText.textContent = 'Enter price per unit';
            strikeInput.removeAttribute('required');
            typeSelect.removeAttribute('required');
            lotSizeInput.setAttribute('required', 'required');
        }
    }

    async function addPosition(event) {
        event.preventDefault();

        const instrumentType = document.getElementById('position-instrument').value;
        const quantity = parseInt(document.getElementById('position-quantity').value);
        const entryPrice = parseFloat(document.getElementById('position-price').value);
        const lotSize = parseInt(document.getElementById('position-lot-size').value);

        // Validate date
        const entryDate = document.getElementById('position-entry-date').value;
        if (!validateDate(entryDate)) {
            showAlert('Cannot add positions for future dates');
            return;
        }

        const position = {
            uid: generateUUID(),
            symbol: document.getElementById('position-symbol').value.toUpperCase(),
            instrument: instrumentType,
            type: instrumentType === 'option' ? document.getElementById('position-type').value : 'FUT',
            strike: instrumentType === 'option' ?
                parseFloat(document.getElementById('position-strike').value) :
                undefined,
            expiry: document.getElementById('position-expiry').value,
            quantity: quantity,
            entry_price: entryPrice,
            entry_date: entryDate,
            side: document.getElementById('position-side').value,
            lot_size: lotSize,
        };

        // Calculate total premium/value based on instrument type and side
        if (instrumentType === 'option') {
            const totalPremium = entryPrice * quantity * lotSize;
            position.premium_received = position.side === 'sell' ? totalPremium : -totalPremium;
        } else {
            position.premium_received = null; // Futures don't have premium
        }

        try {
            if (!currentPortfolio.positions) {
                currentPortfolio.positions = [];
            }

            currentPortfolio.positions.push(position);
            await portfoliosCrud.update(currentPortfolio.uid, currentPortfolio);

            hideAddPositionModal();
            window.location.reload();
        } catch (error) {
            console.error('Error:', error);
            showAlert('Failed to add position');
        }
    }

    async function deletePosition(positionId) {
        const confirmed = await showConfirmDialog({
            title: 'Delete Position',
            message: 'Are you sure you want to delete this position?',
            confirmText: 'Delete',
            cancelText: 'Cancel',
            type: 'danger'
        });

        if (!confirmed) return;

        try {
            currentPortfolio.positions = currentPortfolio.positions.filter(p => p.uid !== positionId);
            await portfoliosCrud.update(currentPortfolio.uid, currentPortfolio);
            window.location.reload();
        } catch (error) {
            console.error('Error:', error);
            showAlert('Failed to delete position');
        }
    }

</script>