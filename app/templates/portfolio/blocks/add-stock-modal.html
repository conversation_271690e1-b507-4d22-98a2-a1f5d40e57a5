<div id="addStockModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white dark:bg-neutral-800 rounded-xl w-full max-w-md">
        <div class="px-6 py-4 border-b">
            <h2 class="text-xl font-semibold">Add Stock</h2>
        </div>
        <div class="bg-neutral-100  p-3 custom-scroll max-h-[calc(100vh_-_10rem)] overflow-auto rounded-b-xl">
            <form onsubmit="addStock(event)" class="bg-white p-5 rounded-lg">
                <div class="space-y-4">
                    <!-- Symbol Search -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Symbol
                        </label>
                        <div class="relative">
                            <input type="text" id="search-input" required
                                class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                            <div id="search-results"
                                class="absolute left-0 right-0 top-full mt-1 bg-white dark:bg-neutral-800 border dark:border-neutral-700 rounded-md shadow-lg hidden">
                            </div>
                        </div>
                    </div>

                    <!-- Quantity -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Quantity
                        </label>
                        <input type="number" id="stockQuantity" required min="1"
                            class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                    </div>

                    <!-- Purchase Price -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Purchase Price
                        </label>
                        <input type="number" id="stockPrice" required min="0" step="0.01"
                            class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                    </div>

                    <!-- Date -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Date
                        </label>
                        <input type="date" id="stockDate" required
                            class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all"
                            max="{{ today_date }}" value="{{ today_date }}">
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="hideAddStockModal()"
                        class="bg-gray-200 font-medium hover:bg-gray-300 px-4 py-2 rounded-md text-neutral-900 text-sm">Cancel</button>
                    <button type="submit"
                        class="bg-blue-500 font-medium hover:bg-blue-600 px-4 py-2 rounded-md text-sm text-white">Add</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Stock Modal Controls
    function showAddStockModal() {
        document.getElementById('addStockModal').classList.remove('hidden');
        document.getElementById('addStockModal').classList.add('flex');
    }

    function hideAddStockModal() {
        document.getElementById('addStockModal').classList.add('hidden');
        document.getElementById('addStockModal').classList.remove('flex');
        document.getElementById('search-results').classList.add('hidden');
    }

    // Stock Symbol Search
    let stockSearchTimeout;
    const searchInput = document.getElementById('search-input');
    const searchResults = document.getElementById('search-results');

    searchInput.addEventListener('input', function (e) {
        clearTimeout(stockSearchTimeout);
        const query = e.target.value;

        if (query.length < 1) {
            searchResults.classList.add('hidden');
            return;
        }

        stockSearchTimeout = setTimeout(async () => {
            try {
                const response = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/search/find?query=${query}`);
                if (!response.ok) throw new Error('Search failed');

                const results = await response.json();
                displaySearchResults(results.result);
            } catch (error) {
                console.error('Search error:', error);
            }
        }, 300);
    });

    function displaySearchResults(results) {
        searchResults.innerHTML = '';

        if (results && results.length > 0) {
            const ul = document.createElement('ul');
            ul.className = 'py-1';

            results.forEach(symbol => {
                const li = document.createElement('li');
                li.className = 'px-4 py-2 hover:bg-gray-100 dark:hover:bg-neutral-700 cursor-pointer';
                li.innerHTML = `<div class="font-medium">${symbol}</div>`;
                li.onclick = () => {
                    searchInput.value = symbol;
                    searchResults.classList.add('hidden');
                };
                ul.appendChild(li);
            });

            searchResults.appendChild(ul);
            searchResults.classList.remove('hidden');
        } else {
            searchResults.classList.add('hidden');
        }
    }

    function validateDate(dateString) {
        const selectedDate = new Date(dateString);
        const today = new Date();

        // Reset time part for accurate date comparison
        selectedDate.setHours(0, 0, 0, 0);
        today.setHours(0, 0, 0, 0);

        return selectedDate <= today;
    }

    async function addStock(event) {
        event.preventDefault();
        const symbol = document.getElementById('search-input').value.toUpperCase();
        const quantity = parseInt(document.getElementById('stockQuantity').value);
        const price = parseFloat(document.getElementById('stockPrice').value);
        const date = document.getElementById('stockDate').value;

        // Validate date
        if (!validateDate(date)) {
            showAlert('Cannot add transactions for future dates');
            return;
        }

        try {
            // Find if stock already exists
            const existingHoldingIndex = currentPortfolio.holdings.findIndex(h => h.symbol === symbol);

            if (existingHoldingIndex !== -1) {
                // Update existing holding
                const existingHolding = currentPortfolio.holdings[existingHoldingIndex];
                const totalOldValue = existingHolding.quantity * existingHolding.avg_price;
                const totalNewValue = quantity * price;
                const totalQuantity = existingHolding.quantity + quantity;

                // Calculate new average price
                const newAvgPrice = (totalOldValue + totalNewValue) / totalQuantity;

                // Update the holding
                currentPortfolio.holdings[existingHoldingIndex] = {
                    ...existingHolding,
                    quantity: totalQuantity,
                    avg_price: newAvgPrice,
                    orders: [
                        ...(existingHolding.orders || []),
                        {
                            uid: generateUUID(),
                            quantity,
                            price,
                            date,
                            type: 'buy'
                        }
                    ]
                };
            } else {
                // Create new holding
                const newHolding = {
                    uid: generateUUID(),
                    symbol,
                    quantity,
                    avg_price: price,
                    orders: [{
                        uid: generateUUID(),
                        quantity,
                        price,
                        date,
                        type: 'buy'
                    }]
                };
                currentPortfolio.holdings.push(newHolding);
            }

            // Save to database
            await portfoliosCrud.update(currentPortfolio.uid, currentPortfolio);

            hideAddStockModal();
            window.location.reload();
        } catch (error) {
            console.error('Error:', error);
            showAlert('Failed to add stock');
        }
    }
</script>