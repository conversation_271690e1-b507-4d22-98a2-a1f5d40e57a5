<div id="share-portfolio-modal"
    class="modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-neutral-800 p-6 rounded-lg w-full max-w-md">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold">Share Portfolio</h2>
            <button id="close-share-modal" onclick="PortfolioShare.hideModal('share-portfolio-modal')"
                class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path
                        d="M12 10.586l4.95-4.95 1.414 1.414L13.414 12l4.95 4.95-1.414 1.414L12 13.414l-4.95 4.95-1.414-1.414L10.586 12 5.636 7.05l1.414-1.414z">
                    </path>
                </svg>
            </button>
        </div>
        <label class="block text-sm font-medium mb-2">Portfolio Name</label>
        <input id="share-portfolio-name" type="text"
            class="w-full px-2 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all" />
        <div class="my-4">
            <label class="flex items-center cursor-pointer">
                <div class="relative">
                    <input type="checkbox" id="share-type-toggle" class="sr-only peer" />
                    <div class="w-11 h-6 bg-gray-200 rounded-full peer-checked:bg-blue-500 transition-colors"></div>
                    <div
                        class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transition-transform peer-checked:translate-x-5">
                    </div>
                </div>
                <span class="ml-3 text-sm font-medium" id="toggle-label">Static Share</span>
            </label>
            <p class="text-xs text-gray-500 mt-2 " id="toggle-help">
                Static: Shares a fixed snapshot
            </p>
        </div>

        <!-- Initial buttons -->
        <div id="initial-buttons" class="flex gap-2 justify-end mt-4">
            <button onclick="PortfolioShare.hideModal('share-portfolio-modal')"
                class="bg-red-50 text-red-500 px-4 py-1.5 rounded-md hover:bg-red-100 text-sm">Cancel</button>
            <button id="share-button" onclick="PortfolioShare.sharePortfolio()"
                class="bg-blue-500 text-white px-4 py-1.5 rounded-md hover:bg-blue-600 text-sm">Share</button>
        </div>

        <!-- Share buttons (initially hidden) -->
        <div id="share-options" class="hidden mt-4">
            <p class="text-lg mb-3">URL copied to clipboard!</p>
            <div class="flex flex-wrap gap-2 justify-end items-center">
                <p class="text-sm">Share via:</p>
                <button onclick="PortfolioShare.shareVia('whatsapp')"
                    class="flex items-center gap-1 bg-green-500 text-white px-3 py-1.5 rounded-md hover:bg-green-600 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                        fill="currentColor">
                        <path
                            d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z" />
                    </svg>
                    WhatsApp
                </button>
                <button onclick="PortfolioShare.shareVia('twitter')"
                    class="flex items-center gap-1 bg-blue-400 text-white px-3 py-1.5 rounded-md hover:bg-blue-500 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                        fill="currentColor">
                        <path
                            d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                    </svg>
                    X
                </button>
                <button onclick="PortfolioShare.shareVia('reddit')"
                    class="flex items-center gap-1 bg-orange-600 text-white px-3 py-1.5 rounded-md hover:bg-orange-700 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                        fill="currentColor">
                        <path
                            d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.************* 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z" />
                    </svg>
                    Reddit
                </button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/dom-to-image/2.6.0/dom-to-image.min.js"></script>
<script>
    const PortfolioShare = {
        // DOM elements cached as constants
        ELEMENTS: {
            modal: document.getElementById('share-portfolio-modal'),
            input: document.getElementById('share-portfolio-name'),
            portfolioContainer: document.getElementById('portfolio-right-container'),
            toggle: document.getElementById('share-type-toggle'),
            toggleLabel: document.getElementById('toggle-label'),
            toggleHelp: document.getElementById('toggle-help'),
            shareButton: document.getElementById('share-button'),
            initialButtons: document.getElementById('initial-buttons'),
            shareOptions: document.getElementById('share-options'),
            closeButton: document.getElementById('close-share-modal')
        },

        // Store the generated share URL
        shareUrl: '',

        showSharePortfolioModal() {
            const defaultName = `${userData.getItem("name")}'s ${currentPortfolio.name ?? ""} Portfolio on ${new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
            this.ELEMENTS.input.value = defaultName;
            this.ELEMENTS.modal.classList.remove('hidden');
            this.updateToggleDisplay();
            this.ELEMENTS.toggle.addEventListener('change', this.updateToggleDisplay.bind(this));

            // Reset to initial state when opening modal
            this.ELEMENTS.initialButtons.classList.remove('hidden');
            this.ELEMENTS.shareOptions.classList.add('hidden');
            this.ELEMENTS.closeButton.classList.add('hidden');
            this.ELEMENTS.shareButton.textContent = 'Share';
            this.ELEMENTS.shareButton.disabled = false;
            this.ELEMENTS.shareButton.classList.remove('opacity-50', 'cursor-not-allowed');
        },

        hideModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        },

        updateToggleDisplay() {
            const isDynamic = this.ELEMENTS.toggle.checked;
            this.ELEMENTS.toggleLabel.textContent = isDynamic ? 'Dynamic Share' : 'Static Share';
            this.ELEMENTS.toggleHelp.innerHTML = isDynamic
                ? '<strong>Dynamic:</strong> Shares live portfolio updates including real-time changes to holdings and performance metrics.'
                : '<strong>Static:</strong> Shares a fixed snapshot of your portfolio that preserves current holdings and values at this moment.';
        },

        async sharePortfolio() {
            const portfolioUid = currentPortfolio.uid || "all";
            const customName = this.ELEMENTS.input.value;
            const shareType = this.ELEMENTS.toggle.checked ? 'dynamic' : 'static';
            this.ELEMENTS.shareButton.textContent = 'Sharing...';
            this.ELEMENTS.shareButton.disabled = true;
            this.ELEMENTS.shareButton.classList.add('opacity-50', 'cursor-not-allowed');

            try {
                const requestBody = {
                    portfolio_uid: portfolioUid,
                    share_type: shareType,
                    custom_name: customName
                };

                const response = await fetch('/portfolio/share', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`Server error: ${response.status}`);
                }

                const result = await response.json();
                if (result.share_url) {
                    this.shareUrl = result.share_url;
                    await this.copyToClipboard(result.share_url);

                    // Show sharing options instead of alert
                    this.ELEMENTS.initialButtons.classList.add('hidden');
                    this.ELEMENTS.shareOptions.classList.remove('hidden');
                    this.ELEMENTS.closeButton.classList.remove('hidden');
                }
            } catch (error) {
                console.error('Error sharing portfolio:', error);
                showAlert('Failed to share portfolio: ' + error.message);

                // Reset button state on error
                this.ELEMENTS.shareButton.textContent = 'Share';
                this.ELEMENTS.shareButton.disabled = false;
                this.ELEMENTS.shareButton.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        },

        async copyToClipboard(text) {
            try {
                await navigator.clipboard.writeText(text);
                console.log('Copied to clipboard successfully');
            } catch (error) {
                console.error('Failed to copy to clipboard:', error);
                throw error;
            }
        },

        shareVia(platform) {
            const text = `Check out my portfolio: ${this.ELEMENTS.input.value}`;
            const url = this.shareUrl;

            let shareUrl;
            switch (platform) {
                case 'whatsapp':
                    shareUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`;
                    break;
                case 'twitter':
                    shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
                    break;
                case 'reddit':
                    shareUrl = `https://www.reddit.com/submit?url=${encodeURIComponent(url)}&title=${encodeURIComponent(text)}`;
                    break;
                default:
                    return;
            }

            window.open(shareUrl, '_blank');
        }
    };
</script>