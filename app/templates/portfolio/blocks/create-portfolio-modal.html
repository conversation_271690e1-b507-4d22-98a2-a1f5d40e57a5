<div id="createPortfolioModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="relative bg-white dark:bg-neutral-800 rounded-xl p-6 w-full max-w-md">
        <!-- Close Button Positioned at the Top-Right -->
        <button onclick="hideCreatePortfolioModal()" class="absolute top-4 right-4 text-gray-600 hover:text-red-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-x">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
            </svg>
        </button>

        <h2 class="text-xl font-semibold mb-4">Create Portfolio</h2>
        <form onsubmit="createPortfolio(event)">
            <div class="space-y-4">
                <!-- Portfolio Name -->
                <div>
                    <label class="block text-sm font-medium text-neutral-900 dark:text-gray-300 mb-2">
                        Portfolio Name
                    </label>
                    <input type="text" id="portfolio-name" required
                        class="w-full px-3 text-sm py-2.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="hideCreatePortfolioModal()"
                    class="bg-gray-200 font-medium hover:bg-gray-300 px-4 py-2 rounded-md text-neutral-900 text-sm">Cancel</button>
                <button type="submit"
                    class="bg-blue-500 font-medium hover:bg-blue-600 px-4 py-2 rounded-md text-sm text-white">Create</button>
            </div>
        </form>
    </div>
</div>

<script>
    function showCreatePortfolioModal() {
        const guestUserData = checkLogin('');

        if (!guestUserData) {
            showAlert("You must be logged in to create a custom portfolio.");
            return;
        }

        document.getElementById('createPortfolioModal').classList.remove('hidden');
        document.getElementById('createPortfolioModal').classList.add('flex');
    }

    function hideCreatePortfolioModal() {
        document.getElementById('createPortfolioModal').classList.add('hidden');
        document.getElementById('createPortfolioModal').classList.remove('flex');
    }

    async function createPortfolio(event) {
        event.preventDefault();
        const name = document.getElementById('portfolio-name').value;

        try {
            const portfolio = {
                uid: generateUUID(),
                name: name,
                holdings: [],
                positions: [],
                created_at: new Date().toISOString()
            };

            await portfoliosCrud.create(portfolio);
            hideCreatePortfolioModal();
            window.location.reload();
        } catch (error) {
            console.error('Error:', error);
            showAlert('Failed to create portfolio');
        }
    }
</script>