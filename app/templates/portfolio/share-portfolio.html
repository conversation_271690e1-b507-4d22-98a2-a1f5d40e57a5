<!DOCTYPE html>
<html lang="en">

<head>
    <title>Optimize Trading Portfolio with AI-Powered Strategies | AI Bull</title>
    <meta name="description"
        content="Enhance your trading portfolio with AI-powered tools like Trade Signals, Strike Scanner, and AI Rebalancing. Implement strategies like Covered Calls, Cash Secured Puts, and Technical Analysis to manage risk and maximize returns." />
    <link rel="canonical" href="https://theaibull.com/portfolio" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/portfolio" />
    <meta property="og:title" content="Optimize Your Trading Portfolio with AI-Powered Tools and Strategies" />
    <meta property="og:description"
        content="Enhance your trading portfolio with AI-powered tools like Trade Signals, Strike Scanner, and AI Rebalancing. Implement strategies like Covered Calls, Cash Secured Puts, and Technical Analysis to manage risk and maximize returns." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@aibull" />
    <meta name="twitter:title" content="Optimize Your Trading Portfolio with AI-Powered Tools and Strategies" />
    <meta name="twitter:description"
        content="Maximize your trading portfolio's potential using AI-driven tools, strategies like Covered Calls, and risk management techniques." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
    <!-- Load Chart.js if needed -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .font-semibold {
            font-family: poppins-semibold;
        }
    </style>
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">
    {% include 'blocks/common.html' %}
    <div class="flex flex-col justify-between min-h-screen">
        <div
            class="bg-neutral-100 bg-neutral-50 dark:bg-neutral-900 duration-300  isolate min-h-screen overflow-hidden pb-[62px] relative rhs-block transition-width xl:pb-0">
            <div>
                <div
                    class="absolute inset-x-0 bottom-[calc(-702/16*1rem)] top-0 -z-10 bg-[radial-gradient(154.86%_76.83%_at_50%_22.26%,_rgba(243,244,246,0.4)_8.98%,_rgba(243,244,246,1)_45.99%)]">
                </div>
                <svg viewBox="-1000 0 3504 918"
                    class="absolute -top-6 left-1/2 -z-10 ml-[calc(-3504/2/16*1rem)] w-[calc(3504/16*1rem)] mix-blend-overlay">
                    <path fill="url(#hero-gradient)" d="M3504 918H-1000V0h3504v918Z"></path>
                    <defs>
                        <radialGradient id="hero-gradient" cx="0" cy="0" r="1"
                            gradientTransform="matrix(0 707.279 -1739.2 0 741 159.991)" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#6C47FF" stop-opacity="0.6"></stop>
                            <stop offset=".412" stop-color="#FFF963" stop-opacity=".8"></stop>
                            <stop offset=".623" stop-color="#38DAFD" stop-opacity=".6"></stop>
                            <stop offset=".919" stop-color="#6248F6" stop-opacity="0"></stop>
                        </radialGradient>
                    </defs>
                </svg>
                <svg class="absolute inset-x-0 top-0 -z-10 h-full w-full stroke-gray-300/80 [mask-image:radial-gradient(32rem_32rem_at_center,white,transparent)]"
                    aria-hidden="true">
                    <defs>
                        <pattern id="1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84" width="200" height="200" x="50%" y="-1"
                            patternUnits="userSpaceOnUse">
                            <path d="M.5 200V.5H200" fill="none"></path>
                        </pattern>
                    </defs>
                    <svg x="50%" y="-1" class="overflow-visible fill-gray-50">
                        <path
                            d="M-200 0h201v201h-201Z M600 0h201v201h-201Z M-400 600h201v201h-201Z M200 800h201v201h-201Z"
                            stroke-width="0"></path>
                    </svg>
                    <rect width="100%" height="100%" stroke-width="0" fill="url(#1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84)">
                    </rect>
                </svg>
            </div>
            <div id="share-portfolio-container" class="max-w-7xl mx-auto py-6">
                <div class="w-full">
                    <div class="w-full">
                        <h1 id="portfolio-name"
                            class="text-2xl mb-2 font-semibold bg-clip-text bg-gradient-to-r font-semibold from-blue-600 inline-block text-transparent to-indigo-600 via-pink-600">
                        </h1>
                        <div>
                            <div id="dynamic-portfolio-data" class="hidden">

                                <!-- Portfolio Header -->
                                <div class="md:flex justify-between items-start mb-4">
                                    <div>
                                        <div class="md:flex font-medium gap-3 items-center mt-1 text-sm">
                                            <div id="dynamic-total-value" class="text-gray-500"></div>
                                            <div class="text-gray-400 hidden md:block">|</div>
                                            <div id="dynamic-total-pl" class="text-gray-500"></div>
                                            <div class="text-gray-400 hidden md:block">|</div>
                                            <div id="dynamic-total-holdings" class="text-gray-500"></div>
                                        </div>
                                    </div>
                                </div>

                                {% if "share" in request.path and not User_Data %}
                                <div>
                                    <div class="mb-24 mt-10">
                                        <div id="signup-form-share"
                                            class="flex lg:px-20 lg:py-24 p-4 px-5 border border-white py-5 relative rounded-xl h-[330px] items-center"
                                            style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #ffeaea, #fffcf4, #fffff8, #ffe6fd00, #e3f3ff)">
                                            {% include 'blocks/signup.html' %}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}


                                <div>
                                    <!-- Charts Section -->
                                    {% include 'portfolio/blocks/charts.html' %}
                                </div>

                                <!-- Holdings Section -->
                                <div class="bg-white dark:bg-neutral-800 rounded-lg">
                                    <div class="p-2 px-4 flex justify-between items-center">
                                        <h2 class="text-lg font-semibold">Holdings</h2>
                                        <button id="aiFeedbackBtn" onclick="getPortfolioAiFeedback()"
                                            class="animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-sparkles">
                                                <path
                                                    d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                                <path d="M20 3v4" />
                                                <path d="M22 5h-4" />
                                                <path d="M4 17v2" />
                                                <path d="M5 18H3" />
                                            </svg> Get AI Feedback
                                        </button>
                                    </div>
                                    {% include 'portfolio/blocks/holdings-table.html' %}
                                </div>

                                <!-- Positions Section -->
                                <div class="bg-white dark:bg-neutral-800 rounded-lg mt-4">
                                    <div class="p-2 px-4">
                                        <h2 class="text-lg font-semibold">Positions</h2>
                                    </div>
                                    {% include 'portfolio/blocks/positions-table.html' %}
                                </div>
                            </div>
                            <div id="comments-section" class="bg-white rounded-lg p-4">
                                <!-- Header -->
                                <h2 class="text-lg font-semibold mb-2">Comments</h2>

                                <!-- Comment Input Area -->
                                <div class="flex flex-col gap-4">
                                    <div class="relative">
                                        <textarea id="comment-input" maxlength="500"
                                            class="w-full p-3 border rounded-md bg-gray-50 dark:bg-neutral-900 dark:border-neutral-600 text-gray-800 dark:text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-y min-h-[120px] transition-all duration-200"
                                            placeholder="Share your thoughts..." oninput="updateCharCount()"></textarea>
                                        <span id="char-count"
                                            class="absolute bottom-3 right-3 text-sm text-gray-500 dark:text-gray-400">0/500</span>
                                    </div>
                                    <button id="post-comment-btn" onclick="postComment()"
                                        class="self-end bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200">
                                        Post Comment
                                    </button>
                                </div>

                                <!-- Comments List -->
                                <div id="comments-list" class="mt-6 space-y-4">
                                    <!-- Dynamically loaded comments will go here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script>
        let portfolioHoldings = [];
        async function loadSharedPortfolio() {
            showLoading("Loading portfolio...");
            try {
                const shareUid = getShareUid();
                const response = await fetch(`/portfolio/api/share/${shareUid}`);
                const { portfolio, name, type } = await response.json();

                // Cache DOM elements
                const portfolioName = document.getElementById('portfolio-name');
                const staticData = document.getElementById('static-portfolio-data');
                const dynamicData = document.getElementById('dynamic-portfolio-data');
                const totalValue = document.getElementById('dynamic-total-value');
                const totalPL = document.getElementById('dynamic-total-pl');
                const totalHoldings = document.getElementById('dynamic-total-holdings');

                portfolioName.textContent = name;

                dynamicData.classList.remove('hidden');
                const { total_value, total_pl, total_pl_percentage, holdings = [], positions = [] } = portfolio;

                totalValue.innerHTML = `Total Value: <span class="text-black dark:text-white font-medium">${formatIndianNumber(total_value)}</span>`;
                totalPL.innerHTML = `P/L: <span class="${total_pl >= 0 ? 'text-green-600' : 'text-red-600'} font-medium">${formatIndianNumber(total_pl)} (${formatIndianNumber(total_pl_percentage)}%)</span>`;
                totalHoldings.innerHTML = `Total Holdings: <span class="text-black dark:text-white font-medium">${holdings.length + positions.length}</span>`;

                const [equityPositions, foPositions] = partitionPositions(positions);

                // Dynamically populate the holdings & positions tables
                populateTables(holdings, equityPositions, foPositions);

                if (typeof initializeCharts === 'function') {
                    initializeCharts(holdings, positions);
                }
            } catch (error) {
                console.error('Error loading portfolio:', error);
            } finally {
                const sharePortfolioContainer = document.getElementById('share-portfolio-container');
                if (!userDataExists) {
                    const signupForm = document.getElementById('signup-form-share')
                    sharePortfolioContainer.classList.add('flex', 'gap-x-2');
                    signupForm.classList.remove('hidden');
                } else {
                    const signupForm = document.getElementById('share-portfolio-container');
                    sharePortfolioContainer.classList.remove('flex', 'gap-x-2');
                }
                hideLoading();
            }
        }

        function getShareUid() {
            return window.location.pathname.split('/').pop();
        }

        function partitionPositions(positions) {
            return [
                positions.filter(p => p.instrument === 'equity'),
                positions.filter(p => p.instrument !== 'equity')
            ];
        }

        function populateTables(holdings, equityPositions, foPositions) {
            populateHoldingsTable(holdings);
            populateEquityPositions(equityPositions);
            populateFoPositions(foPositions);

            const rawHoldings = holdings;
            portfolioHoldings = rawHoldings.map(holding => ({
                symbol: holding.symbol,
                company_name: holding.company_name || holding.symbol,
                sector: holding.sector + " ", // added this to ensure that prompt is not caching
                industry: holding.industry,
                quantity: holding.quantity || 0,
                avg_price: holding.avg_price || 0,
                current_price: holding.current_price || 0,
                invested_amount: holding.invested_amount || 0,
                market_value: holding.market_value || 0,
                pl: holding.pl != null ? holding.pl : 0,
                pl_percentage: holding.pl_percentage != null ? holding.pl_percentage : 0,
                pe_ratio: holding.pe_ratio || null,
                market_cap: holding.market_cap || null,
                week_52_low: holding.week_52_low || null,
                week_52_high: holding.week_52_high || null,

                // Additional fields for AI feedback if needed in the future
                // dividend_yield: holding.dividend_yield || null,
                // beta: holding.beta || null,
                // eps: holding.eps || null,
                // roe: holding.roe || null
            }));
        }

        const renderComment = comment => `
                <div class="p-4 border border-gray-200 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 hover:bg-gray-50 dark:hover:bg-neutral-700 transition-colors duration-200">
                    <p class="text-gray-700 dark:text-gray-300">${comment.text}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                        Posted on ${new Date(comment.date).toLocaleString('en-US', {
            month: 'short', day: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit'
        })}
                    </p>
                </div>
                `;

        function updateCharCount(input = document.getElementById('comment-input')) {
            document.getElementById('char-count').textContent = `${input.value.length}/500`;
        }

        async function postComment() {
            const commentInput = document.getElementById('comment-input');
            const postButton = document.getElementById('post-comment-btn');
            const commentText = commentInput.value.trim();

            if (!commentText) {
                showAlert('Please enter a comment');
                return;
            }

            try {
                postButton.disabled = true;
                postButton.textContent = 'Posting...';

                await fetch(`/portfolio/share/${getShareUid()}/comments`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ comment: commentText })
                });

                commentInput.value = '';
                updateCharCount(commentInput);
                await loadComments();
            } catch (error) {
                console.error('Error posting comment:', error);
                showAlert('Failed to post comment');
            } finally {
                postButton.disabled = false;
                postButton.textContent = 'Post Comment';
            }
        }

        async function loadComments() {
            const commentsList = document.getElementById('comments-list');
            try {
                const response = await fetch(`/portfolio/share/${getShareUid()}/comments`);
                const comments = await response.json();
                commentsList.innerHTML = comments.length
                    ? comments.map(renderComment).join('')
                    : '<p class="text-gray-500 dark:text-gray-400 text-center">No comments yet. Be the first to comment!</p>';
            } catch (error) {
                console.error('Error loading comments:', error);
                commentsList.innerHTML = '<p class="text-red-500 dark:text-red-400 text-center">Failed to load comments.</p>';
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            loadSharedPortfolio();
            loadComments();
        });

        function getPortfolioAiFeedback() {
            getAIFeedback(
                { holdings: portfolioHoldings },
                { analysisType: 'portfolio' }
            );
        }
    </script>
    {% include 'blocks/ai-feedback.html' %}
</body>

</html>