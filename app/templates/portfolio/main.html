<!DOCTYPE html>
<html lang="en">

<head>
    <title>Optimize Trading Portfolio with AI-Powered Strategies | AI Bull</title>
    <meta name="description"
        content="Enhance your trading portfolio with AI-powered tools like Trade Signals, Strike Scanner, and AI Rebalancing. Implement strategies like Covered Calls, Cash Secured Puts, and Technical Analysis to manage risk and maximize returns." />
    <link rel="canonical" href="https://theaibull.com/portfolio" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/portfolio" />
    <meta property="og:title" content="Optimize Your Trading Portfolio with AI-Powered Tools and Strategies" />
    <meta property="og:description"
        content="Enhance your trading portfolio with AI-powered tools like Trade Signals, Strike Scanner, and AI Rebalancing. Implement strategies like Covered Calls, Cash Secured Puts, and Technical Analysis to manage risk and maximize returns." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@aibull" />
    <meta name="twitter:title" content="Optimize Your Trading Portfolio with AI-Powered Tools and Strategies" />
    <meta name="twitter:description"
        content="Maximize your trading portfolio's potential using AI-driven tools, strategies like Covered Calls, and risk management techniques." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
    <!-- Load Chart.js if needed -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">
    {% include 'blocks/common.html' %}
    {%include 'blocks/onboarding.html' %}
    <div class="flex flex-col justify-between min-h-screen">
        <div class="rhs-block duration-300 transition-width relative pb-[62px] xl:pb-0"
            style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
            <button
                class="mobile-toggle-btn font-medium text-base cursor-pointer xl:hidden w-full text-blue-800 p-2 text-left"
                onclick="togglePortSlideout()" style="background-image: linear-gradient(45deg, #d0f1ff, #eff1f3);">
                <div class="flex items-center justify-between">
                    <p>Portfolios</p>
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-menu-icon lucide-menu">
                            <line x1="4" x2="20" y1="12" y2="12" />
                            <line x1="4" x2="20" y1="6" y2="6" />
                            <line x1="4" x2="20" y1="18" y2="18" />
                        </svg>
                    </div>
                </div>
            </button>
            <div class="xl:flex">

                <!-- Left Column - Portfolio List -->
                <div
                    class="portfolio-container w-64 min-h-screen bg-white dark:bg-neutral-800 border-r dark:border-neutral-700 fixed">
                    <div class="flex items-center justify-between border-b border-gray-200 p-2.5">
                        <h2 class="text-lg font-semibold">Portfolios</h2>
                        <div class="flex items-center">
                            <button onclick="showCreatePortfolioModal()"
                                class="p-1 text-blue-600 bg-blue-50 rounded-md dark:hover:bg-neutral-700 hover:bg-blue-500 hover:text-white transition-all">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <path d="M12 5v14M5 12h14" />
                                </svg>
                            </button>
                            <button onclick="applyAndClose()"
                                class="p-1 text-red-600 ml-3 xl:hidden bg-red-50 rounded-md dark:hover:bg-neutral-700 hover:bg-red-500 hover:text-white transition-all">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-x-icon lucide-x">
                                    <path d="M18 6 6 18" />
                                    <path d="m6 6 12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div class="p-2">
                        <!-- All Portfolios Link without SVG -->
                        <a href="/portfolio"
                            class="flex items-center px-2 py-2 mb-2 text-sm rounded-lg {% if not portfolio_uid %}bg-blue-50 text-blue-600{% else %}hover:bg-gray-50 dark:hover:bg-neutral-700{% endif %}">
                            <!-- SVG removed -->
                            <span class="pl-2">All Portfolios</span>
                        </a>

                        <!-- Portfolio List -->
                        <div id="portfolio-list-container" class="space-y-1">
                            <!-- Portfolio list will be populated dynamically from the API response -->
                        </div>
                    </div>
                </div>

                <!-- Right Column - Content -->
                <div class="flex-1 min-h-screen p-4 xl:pl-64 xl:ml-3">
                    <div class="flex items-center mb-4">
                        <h1 id="page-header" class="md:text-xl text-lg font-semibold ">
                            Portfolios
                        </h1>
                        <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">Track, Optimize & Grow
                            with AI-Powered Investment Portfolios</h2>
                    </div>

                    <!-- Demo Mode Banner - Only shown for non-authenticated users -->
                    {% if not User_Data %}
                    <div id="demo-mode-banner"
                        class="animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2  mb-4">
                        <div class="container mx-auto flex flex-col md:flex-row justify-between items-center">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span class="font-medium">You're viewing demo data. <a href="javascript:void(0);"
                                        onclick="showLoginModal(false)" class="text-black-600 underline">Sign up</a> to
                                    create your own portfolio!</span>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    <div class="space-y-4">
                        <!-- Summary Cards -->
                        <div class="grid lg:grid-cols-3 md:grid-cols-2 grid-col-1 gap-4">
                            <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm text-gray-500 dark:text-gray-400 uppercase">Total Value</h3>
                                    <div id="total-value" class="text-2xl font-semibold mt-2"></div>
                                </div>
                            </div>
                            <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm text-gray-500 dark:text-gray-400 uppercase">Stock P&L</h3>
                                    <div id="stock-pl" class="text-2xl font-semibold mt-2 text-red-600"></div>
                                </div>
                                <div>
                                    <h3 class="text-sm text-gray-500 dark:text-gray-400 uppercase">F&O P&L</h3>
                                    <div id="fno-pl" class="text-2xl font-semibold mt-2 text-red-600"></div>
                                </div>
                            </div>
                            <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm text-gray-500 dark:text-gray-400 uppercase">Total Holdings</h3>
                                    <div id="total-holdings" class="text-2xl font-semibold mt-2"></div>
                                </div>
                                <button onclick="PortfolioShare.showSharePortfolioModal()"
                                    class="bg-green-100 font-medium hover:bg-green-500 hover:text-white px-4 py-2 rounded-md text-green-800 text-sm transition-colors">
                                    Share Portfolio
                                </button>
                            </div>
                        </div>

                        <!-- Charts Section -->
                        {% include 'portfolio/blocks/charts.html' %}

                        <!-- Holdings Section -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg shadow-sm">
                            <!-- Updated header with AI Feedback button -->
                            <div class="p-3 flex justify-between items-center">
                                <h2 class="text-lg font-semibold">All Holdings</h2>
                                <button onclick="getPortfolioAIFeedback('portfolio_holdings')"
                                    class="animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-sparkles">
                                        <path
                                            d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                        <path d="M20 3v4" />
                                        <path d="M22 5h-4" />
                                        <path d="M4 17v2" />
                                        <path d="M5 18H3" />
                                    </svg> Get AI Feedback
                                </button>
                            </div>
                            {% include 'portfolio/blocks/holdings-table.html' %}
                        </div>

                        <!-- Positions Section -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg shadow-sm">
                            <div class="p-3 flex justify-between items-center">
                                <h2 class="text-lg font-semibold">All Positions</h2>
                                <button onclick="getPortfolioAIFeedback('portfolio_positions')"
                                    class="animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-sparkles">
                                        <path
                                            d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                        <path d="M20 3v4" />
                                        <path d="M22 5h-4" />
                                        <path d="M4 17v2" />
                                        <path d="M5 18H3" />
                                    </svg> Get AI Feedback
                                </button>
                            </div>
                            {% include 'portfolio/blocks/positions-table.html' %}
                        </div>
                    </div>

                    <!-- FAQ Section -->
                    <div class="mt-8 text-sm">
                        <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
                        <div class="space-y-4">
                            <details class="border rounded-lg bg-white">
                                <summary class="cursor-pointer p-4">How can I diversify my trading portfolio?</summary>
                                <div class="p-4 border-t">
                                    <p class="text-gray-700 dark:text-gray-300 leading-6">
                                        Diversify by spreading investments across different sectors, asset classes, and
                                        strategies to reduce risk and increase potential returns.
                                    </p>
                                </div>
                            </details>
                            <details class="border rounded-lg bg-white">
                                <summary class="cursor-pointer p-4">What tools can help optimize my trades?</summary>
                                <div class="p-4 border-t">
                                    <p class="text-gray-700 dark:text-gray-300 leading-6">
                                        Tools like AI-Powered Trade Signals, Strike Scanner, and Spread Analyzer can
                                        help
                                        identify optimal trading opportunities and automate decision-making for better
                                        results.
                                    </p>
                                </div>
                            </details>
                            <details class="border rounded-lg bg-white">
                                <summary class="cursor-pointer p-4">How can I manage risk in my trading portfolio?
                                </summary>
                                <div class="p-4 border-t">
                                    <p class="text-gray-700 dark:text-gray-300 leading-6">
                                        Use stop-loss orders, regularly rebalance your portfolio using AI Rebalancing,
                                        and
                                        apply strategies like Covered Calls and Cash Secured Puts to limit losses.
                                    </p>
                                </div>
                            </details>
                            <details class="border rounded-lg bg-white">
                                <summary class="cursor-pointer p-4">What role does technical analysis play in trading?
                                </summary>
                                <div class="p-4 border-t">
                                    <p class="text-gray-700 dark:text-gray-300 leading-6">
                                        Technical analysis provides insights into price movements, trends, and market
                                        patterns, helping to guide entry and exit points for trades.
                                    </p>
                                </div>
                            </details>
                            <details class="border rounded-lg bg-white">
                                <summary class="cursor-pointer p-4">How can options strategies improve my trading
                                    portfolio?
                                </summary>
                                <div class="p-4 border-t">
                                    <p class="text-gray-700 dark:text-gray-300 leading-6">
                                        Strategies like Covered Calls, Cash Secured Puts, and Box Spread Arbitrage allow
                                        you
                                        to generate income, manage risk, and capitalize on market inefficiencies.
                                    </p>
                                </div>
                            </details>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-auto">
            {% include 'blocks/footer.html' %}
        </div>
    </div>
    {% include 'portfolio/blocks/create-portfolio-modal.html' %}
    {% include 'portfolio/blocks/add-stock-modal.html' %}
    {% include 'portfolio/blocks/add-position-modal.html' %}

    {% if not User_Data %}
    {% include 'portfolio/blocks/dummy-portfolio-data.html' %}
    {% endif %}

    <!-- Expose holdings data for AI call -->
    <script>
        const currentPortfolio = {};
        let holdings = []
        let positions = []
        let portfolioHoldings = []

        document.addEventListener('DOMContentLoaded', async function () {
            // Check if user is logged in
            if (isLoggedIn()) {
                loadHoldingsPositions();
            } else {
                loadDummyData();
            }
        });

        async function loadHoldingsPositions() {
            try {
                showLoading("Loading holdings & positions...");
                // Fetch portfolio data from the backend API    
                const response = await fetch('/portfolio/api/portfolios');
                const portfoliosData = await response.json();

                document.getElementById('total-value').textContent = formatIndianNumber(Number(Math.abs(portfoliosData.total_value)));
                const totalHoldings = portfoliosData.holdings.length + portfoliosData.positions.length || 0;
                document.getElementById('total-holdings').textContent = totalHoldings.toLocaleString('en-IN');

                // Stock P&L
                const stockPlElement = document.getElementById('stock-pl');
                const isStockProfit = portfoliosData.stock_pl >= 0;

                stockPlElement.innerHTML = `${formatIndianNumber(Number(Math.abs(portfoliosData.stock_pl)))} 
                    <span class="text-sm">(${formatIndianNumber(Number(Math.abs(portfoliosData.stock_pl_percentage)))}%)</span>`;
                stockPlElement.classList.toggle('text-green-600', isStockProfit);
                stockPlElement.classList.toggle('text-red-600', !isStockProfit);

                // F&O P&L
                const fnoPlElement = document.getElementById('fno-pl');
                const isFnoProfit = portfoliosData.fno_pl >= 0;
                fnoPlElement.innerHTML = `${formatIndianNumber(Number(Math.abs(portfoliosData.fno_pl)))} 
                    <span class="text-sm">(${formatIndianNumber(Number(Math.abs(portfoliosData.fno_pl_percentage)))}%)</span>`;
                fnoPlElement.classList.toggle('text-green-600', isFnoProfit);
                fnoPlElement.classList.toggle('text-red-600', !isFnoProfit);

                // Update portfolio list with separate P&L percentages
                if (portfoliosData.portfolios) {
                    updatePortfolioList(portfoliosData.portfolios, portfoliosData.combined_stock_pl_percentage, portfoliosData.combined_fno_pl_percentage);
                }

                // Update portfolio P&L percentages in sidebar
                Object.keys(portfoliosData?.combined_stock_pl_percentage || {}).forEach(uid => {
                    const stockPlSpan = document.querySelector(`span[data-uid="${uid}"].stock-pl-percentage`);
                    const stockPlPercentage = portfoliosData.combined_stock_pl_percentage[uid];
                    if (stockPlSpan && stockPlPercentage !== undefined) {
                        const percentage = formatIndianNumber(Number(Math.abs(stockPlPercentage)));
                        stockPlSpan.textContent = `Stocks: ${percentage}%`;
                        stockPlSpan.classList.toggle('text-green-600', stockPlPercentage >= 0);
                        stockPlSpan.classList.toggle('text-red-600', stockPlPercentage < 0);
                    }
                });

                Object.keys(portfoliosData?.combined_fno_pl_percentage || {}).forEach(uid => {
                    const fnoPlSpan = document.querySelector(`span[data-uid="${uid}"].fno-pl-percentage`);
                    const fnoPlPercentage = portfoliosData.combined_fno_pl_percentage[uid];
                    if (fnoPlSpan && fnoPlPercentage !== undefined) {
                        const percentage = formatIndianNumber(Number(Math.abs(fnoPlPercentage)));
                        fnoPlSpan.textContent = `F&O: ${percentage}%`;
                        fnoPlSpan.classList.toggle('text-green-600', fnoPlPercentage >= 0);
                        fnoPlSpan.classList.toggle('text-red-600', fnoPlPercentage < 0);
                    }
                });

                holdings = portfoliosData.holdings || [];
                positions = portfoliosData.positions || [];

                if (typeof initializeCharts === 'function') {
                    initializeCharts(holdings, positions);
                }
                // Separate equity positions and F&O positions
                const equityPositions = positions.filter(position => position.instrument === 'equity');
                const foPositions = positions.filter(position => position.instrument !== 'equity');

                // Dynamically populate the holdings & positions tables
                populateHoldingsTable(holdings);
                populateEquityPositions(equityPositions);
                populateFoPositions(foPositions);

                portfolioHoldings = holdings.map(holding => ({
                    symbol: holding.symbol,
                    company_name: holding.company_name || holding.symbol,
                    sector: holding.sector + " ", // added this to ensure that prompt is not caching
                    industry: holding.industry,
                    quantity: holding.quantity || 0,
                    avg_price: holding.avg_price || 0,
                    current_price: holding.current_price || 0,
                    invested_amount: holding.invested_amount || 0,
                    market_value: holding.market_value || 0,
                    pl: holding.pl != null ? holding.pl : 0,
                    pl_percentage: holding.pl_percentage != null ? holding.pl_percentage : 0,
                    pe_ratio: holding.pe_ratio || null,
                    market_cap: holding.market_cap || null,
                    week_52_low: holding.week_52_low || null,
                    week_52_high: holding.week_52_high || null,

                    // Additional fields for AI feedback if needed in the future
                    // dividend_yield: holding.dividend_yield || null,
                    // beta: holding.beta || null,
                    // eps: holding.eps || null,
                    // roe: holding.roe || null
                }));
            } catch (error) {
                console.error('Error fetching portfolio data:', error);
            } finally {
                hideLoading();
            }
        }

        // Function to update the portfolio list in the sidebar
        function updatePortfolioList(portfolios, stockPlPercentages, fnoPlPercentages) {
            const container = document.getElementById('portfolio-list-container');
            if (!container) return;

            let html = '';
            const currentUid = '{{ portfolio_uid }}'; // Get current portfolio UID if any

            portfolios.forEach(portfolio => {
                const isActive = currentUid === portfolio.uid;
                const stockPlPercentage = stockPlPercentages[portfolio.uid] || 0;
                const fnoPlPercentage = fnoPlPercentages[portfolio.uid] || 0;
                const isStockProfit = stockPlPercentage >= 0;
                const isFnoProfit = fnoPlPercentage >= 0;
                const formattedStockPercentage = formatIndianNumber(Number(Math.abs(stockPlPercentage)));
                const formattedFnoPercentage = formatIndianNumber(Number(Math.abs(fnoPlPercentage)));

                html += `
                <a href="/portfolio/${portfolio.uid}"
                   class="group flex flex-wrap gap-3 items-center justify-between p-2 text-sm rounded-lg ${isActive ? 'bg-blue-50 text-blue-600' : 'hover:bg-gray-50 dark:hover:bg-neutral-700'}">
                    <div class="flex flex-wrap items-start gap-x-1 min-w-0">
                        ${portfolio.svg ?
                        `<img src="${portfolio.svg}" class="w-5 h-5 shrink-0">` :
                        `<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 shrink-0" viewBox="0 0 24 24"
                               fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                               stroke-linejoin="round">
                               <path d="M12 12h.01" />
                               <path d="M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2" />
                               <path d="M22 13a18.15 18.15 0 0 1-20 0" />
                               <rect width="20" height="14" x="2" y="6" rx="2" />
                            </svg>`
                    }
                        <span class="truncate whitespace-nowrap overflow-hidden" style="max-width: 120px;">${portfolio.name}</span>
                    </div>
                    <div class="flex flex-col items-end space-y-1">
                        ${(portfolio.uid && (!portfolio.type || portfolio.type === "manual")) ?
                        `<button onclick="event.preventDefault(); deletePortfolio('${portfolio.uid}')"
                                class="hidden group-hover:block text-red-600 bg-red-50 rounded-md dark:hover:bg-neutral-700 transition-all">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 6h18" />
                                    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                                    <path d="M10 11v6" />
                                    <path d="M14 11v6" />
                                    <path d="M5 6h14l-1 14H6L5 6z" />
                                </svg>
                            </button>` : ''
                    }
                        <span data-uid="${portfolio.uid}" class="stock-pl-percentage text-xs ${isStockProfit ? 'text-green-600' : 'text-red-600'}">
                            Stocks: ${formattedStockPercentage}%
                        </span>
                        <span data-uid="${portfolio.uid}" class="fno-pl-percentage text-xs ${isFnoProfit ? 'text-green-600' : 'text-red-600'}">
                            F&O: ${formattedFnoPercentage}%
                        </span>
                    </div>
                </a>
                `;
            });

            container.innerHTML = html;
        }
    </script>
    {% include 'blocks/ai-feedback.html' %}
    {% include 'portfolio/blocks/share-portfolio-modal.html' %}

    <!-- Your existing scripts (e.g., formatting numbers) remain unchanged -->
    <script>
        const portfoliosCrud = new Crud('portfolios');

        async function deletePortfolio(portfolioUid) {
            if (!portfolioUid) return;

            const confirmed = await showConfirmDialog({
                title: 'Delete Portfolio',
                message: 'Are you sure you want to delete this portfolio?',
                confirmText: 'Delete',
                cancelText: 'Cancel',
                type: 'danger'
            });
            if (!confirmed) return;
            try {
                await portfoliosCrud.delete(portfolioUid);
                window.location.reload();
            } catch (error) {
                console.error('Error:', error);
                showAlert('Failed to delete portfolio. Please try again later');
            }
        }

        const guestUserData = localStorage.getItem('UserData');
        if (guestUserData) {
            document.cookie = `guestUserData=${encodeURIComponent(guestUserData)}; path=/portfolio`;
        }

        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('.rupee-format').forEach(function (el) {
                const value = parseFloat(el.getAttribute('data-value'));
                if (!isNaN(value)) {
                    el.textContent = new Intl.NumberFormat('en-IN', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }).format(value);
                }
            });
        });

        function getPortfolioAIFeedback(type) {
            const equityPositions = positions.filter(position => position.instrument === 'equity');
            const fnoPositions = positions.filter(position => position.instrument !== 'equity');
            if (type === 'portfolio_holdings') {
                getAIFeedback(
                    { holdings: portfolioHoldings },
                    { analysisType: type }
                );
            } else if (type === 'portfolio_positions') {
                getAIFeedback(
                    { positions: equityPositions },
                    { analysisType: type }
                );
            } else if (type === 'portfolio_fno_positions') {
                getAIFeedback(
                    { positions: fnoPositions },
                    { analysisType: type }
                );
            }
        }
    </script>

    <script>
        function togglePortSlideout() {
            const strategiesContainers = document.querySelectorAll('.portfolio-container');

            // Loop through all the selected containers and toggle the class
            strategiesContainers.forEach(container => {
                container.classList.toggle('!left-0');
            });
        }

        function applyAndClose() {
            // You can perform additional actions for the "Apply" button here.

            // Close the slideout menu after applying
            togglePortSlideout();
        }
    </script>
    <style>
        /* Default (Desktop view) */
        .portfolio-container {
            display: block;
        }

        /* Slide-out effect (Mobile view) */
        @media screen and (max-width: 1290px) {
            .portfolio-container {
                position: fixed;
                top: 0;
                left: -100%;
                z-index: 30;
                width: 100%;
                height: 100%;
                background-color: white;
                transition: left 0.3s ease-in-out;
            }

            .portfolio-container.open {
                left: 0;
            }
        }
    </style>
</body>

</html>