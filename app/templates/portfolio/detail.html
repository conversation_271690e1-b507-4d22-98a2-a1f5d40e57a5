<!DOCTYPE html>
<html lang="en">

{% include 'blocks/head.html' %}

<head>
    <!-- Load Chart.js for charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>

<body>
    {%include 'blocks/common.html' %}
    {% include 'blocks/timestamp.html' %}
    <div class="flex flex-col justify-between min-h-screen">
        <div class="rhs-block duration-300 transition-width bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0">
            <button
                class="mobile-toggle-btn font-medium text-base cursor-pointer xl:hidden w-full text-blue-800 p-2 text-left"
                onclick="togglePortSlideout()" style="background-image: linear-gradient(45deg, #d0f1ff, #eff1f3);">
                <div class="flex items-center justify-between">
                    <p>Portfolios</p>
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-menu-icon lucide-menu">
                            <line x1="4" x2="20" y1="12" y2="12" />
                            <line x1="4" x2="20" y1="6" y2="6" />
                            <line x1="4" x2="20" y1="18" y2="18" />
                        </svg>
                    </div>
                </div>
            </button>
            <div class="xl:flex">
                <!-- Left Column - Portfolio List -->
                <div
                    class="portfolio-container w-64 min-h-screen bg-white dark:bg-neutral-800 border-r dark:border-neutral-700 fixed">
                    <div class="flex items-center justify-between border-b border-gray-200 p-2.5">
                        <h2 class="text-lg font-semibold">Portfolios</h2>
                        <div class="flex items-center">
                            <button onclick="showCreatePortfolioModal()"
                                class="p-1 text-blue-600 bg-blue-50 rounded-md dark:hover:bg-neutral-700 hover:bg-blue-500 hover:text-white transition-all">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <path d="M12 5v14M5 12h14" />
                                </svg>
                            </button>
                            <button onclick="applyAndClose()"
                                class="p-1 text-red-600 ml-3 xl:hidden bg-red-50 rounded-md dark:hover:bg-neutral-700 hover:bg-red-500 hover:text-white transition-all">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-x-icon lucide-x">
                                    <path d="M18 6 6 18" />
                                    <path d="m6 6 12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div class="p-2">
                        <!-- All Portfolios Link -->
                        <a href="/portfolio"
                            class="flex items-center mb-2 p-2 text-sm rounded-lg {% if not portfolio_uid %}bg-blue-50 text-blue-600{% else %}hover:bg-gray-100 dark:hover:bg-neutral-700{% endif %}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-gallery-vertical-end">
                                <path d="M7 2h10" />
                                <path d="M5 6h14" />
                                <rect width="18" height="12" x="3" y="10" rx="2" />
                            </svg>
                            <span class="pl-2">All Portfolios</span>
                        </a>

                        <!-- Portfolio List -->
                        <div class="space-y-1">
                            {% for p in portfolios %}
                            <a href="/portfolio/{{ p.uid }}"
                                class="group flex flex-wrap gap-3 items-center justify-between p-2 text-sm rounded-lg {% if portfolio_uid == p.uid %}bg-blue-50 text-blue-600{% else %}hover:bg-gray-50 dark:hover:bg-neutral-700{% endif %}">
                                <div class="flex flex-wrap items-start gap-x-1 min-w-0">
                                    {% if p.svg %}
                                    <img src="{{ p.svg }}" class="w-5 h-5 shrink-0">
                                    {% else %}
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 shrink-0" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M12 12h.01" />
                                        <path d="M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2" />
                                        <path d="M22 13a18.15 18.15 0 0 1-20 0" />
                                        <rect width="20" height="14" x="2" y="6" rx="2" />
                                    </svg>
                                    {% endif %}
                                    <span class="truncate">{{ p.name }}</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    {% if p.uid and (not p.type or p.type == "manual") %}
                                    <button onclick="event.preventDefault(); deleteCurrentPortfolio('{{ p.uid }}')"
                                        class="hidden group-hover:block text-red-600 bg-red-50 rounded-md dark:hover:bg-neutral-700 transition-all">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 6h18" />
                                            <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                                            <path d="M10 11v6" />
                                            <path d="M14 11v6" />
                                            <path d="M5 6h14l-1 14H6L5 6z" />
                                        </svg>
                                    </button>
                                    {% endif %}
                                    <span id="position-total-pl-percentage" class="text-sm text-green-600"></span>
                                </div>
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Right Column - Content -->
                <div class="xl:flex-1 min-h-screen p-4 xl:pl-64 xl:ml-3">
                    <div id="portfolio-right-container" class="space-y-4">
                        <!-- Portfolio Header -->
                        <div id="portfolio-header" class="md:flex justify-between items-start">
                            <div>
                                <h1
                                    class="text-2xl font-semibold bg-clip-text bg-gradient-to-r font-semibold from-blue-600 inline-block text-transparent to-indigo-600 via-pink-600">
                                    {{ portfolio.name }}</h1>
                                <div class="md:flex font-medium gap-3 items-center mt-1 text-sm">
                                    <div id="detail-total-value" class="text-gray-500"></div>
                                    <div class="text-gray-400 hidden md:block">|</div>
                                    <div id="detail-stock-pl" class="text-gray-500"></div>
                                    <div class="text-gray-400 hidden md:block">|</div>
                                    <div id="detail-fno-pl" class="text-gray-500"></div>
                                    <div class="text-gray-400 hidden md:block">|</div>
                                    <div id="detail-total-holdings" class="text-gray-500"></div>
                                </div>
                            </div>
                            <div class="md:flex gap-2  mt-3 md:mt-0">
                                {% if portfolio.app_id and (not portfolio.type or portfolio.type != "manual") %}
                                <div class="md:flex flex-col justify-end gap-y-2">
                                    <div class="flex gap-x-2">
                                        <button onclick="PortfolioShare.showSharePortfolioModal()"
                                            class="bg-green-100 font-medium hover:bg-green-500 hover:text-white px-4 py-2 rounded-md text-green-800 text-sm transition-colors">
                                            Share Portfolio
                                        </button>
                                        <a href="/apps"
                                            class="ml-auto bg-blue-100 font-medium hover:bg-blue-500 hover:text-white px-4 py-2 rounded-md text-blue-500 text-sm transition-colors">
                                            Re-Authorize
                                        </a>
                                    </div>
                                    <div id="preview-timestamp" class="timestamp-component justify-end mt-3 md:mt-0">
                                    </div>
                                </div>
                                {% else %}
                                <button onclick="PortfolioShare.showSharePortfolioModal()"
                                    class="bg-green-100 font-medium hover:bg-green-500 hover:text-white px-4 py-2 rounded-md text-green-800 text-sm transition-colors">
                                    Share Portfolio
                                </button>
                                <button onclick="showAddPositionModal()"
                                    class="bg-blue-100 font-medium hover:bg-blue-500 hover:text-white px-4 py-2 rounded-md text-blue-500 text-sm transition-colors">Add
                                    Position</button>
                                <button onclick="showAddStockModal()"
                                    class="bg-blue-500 font-medium hover:bg-blue-600 hover:text-white px-4 py-2 rounded-md text-white text-sm transition-colors">Add
                                    Stock</button>
                                {% endif %}
                            </div>
                        </div>

                        <div id="failed-results" class="flex flex-col gap-y-2 items-center bg-red-50 p-4 rounded-lg hidden">
                            <p class="text-lg font-semibold text-red-600 mb-2">
                                There was an issue with your portfolio authorization.
                            </p>
                            <p class="text-sm text-gray-600 mb-4">
                                Please re-authorize your account to continue.
                            </p>
                        </div>
                        <div id="success-results" class="space-y-4">

                            <!-- Charts Section -->
                            {% include 'portfolio/blocks/charts.html' %}
    
                            <!-- Holdings Section -->
                            <div class="bg-white dark:bg-neutral-800 rounded-lg shadow-sm">
                                <div class="p-3 flex justify-between items-center">
                                    <h2 class="text-lg font-semibold">Holdings</h2>
                                    <button onclick="getPortfolioAIFeedback('portfolio_holdings')"
                                        class="aiFeedbackBtn animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-sparkles">
                                            <path
                                                d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                            <path d="M20 3v4" />
                                            <path d="M22 5h-4" />
                                            <path d="M4 17v2" />
                                            <path d="M5 18H3" />
                                        </svg> Get AI Feedback
                                    </button>
                                </div>
                                {% include 'portfolio/blocks/holdings-table.html' %}
                            </div>
    
                            <!-- Positions Section -->
                            <div class="bg-white dark:bg-neutral-800 rounded-lg shadow-sm">
                                <div class="p-3 flex justify-between items-center">
                                    <h2 class="text-lg font-semibold">Positions</h2>
                                    <button onclick="getPortfolioAIFeedback('portfolio_holdings')"
                                        class="aiFeedbackBtn animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                            stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles">
                                            <path
                                                d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                            <path d="M20 3v4" />
                                            <path d="M22 5h-4" />
                                            <path d="M4 17v2" />
                                            <path d="M5 18H3" />
                                        </svg> Get AI Feedback
                                    </button>
                                </div>
                                {% include 'portfolio/blocks/positions-table.html' %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div>{% include 'blocks/footer.html' %}</div>
    </div>

    {% include 'portfolio/blocks/create-portfolio-modal.html' %}
    {% include 'portfolio/blocks/add-stock-modal.html' %}
    {% include 'portfolio/blocks/add-position-modal.html' %}


    <script>
        const portfoliosCrud = new Crud('portfolios');
        const currentPortfolio = JSON.parse('{{ portfolio | tojson | safe }}');
        let holdings = [];
        let positions = [];
        let portfolioHoldings = [];

        async function deleteCurrentPortfolio(portfolioUid) {
            if (!portfolioUid) return;

            const confirmed = await showConfirmDialog({
                title: 'Delete Portfolio',
                message: 'Are you sure you want to delete this portfolio?',
                confirmText: 'Delete',
                cancelText: 'Cancel',
                type: 'danger'
            });
            if (!confirmed) return;
            try {
                await portfoliosCrud.delete(portfolioUid);
                window.location.href = '/portfolio';
            } catch (error) {
                console.error('Error:', error);
                showAlert('Failed to delete portfolio. Please try again later');
            }
        }

        async function refreshPortfolioData() {
            window.location.reload();
        }

        async function loadSingleHoldingsPositions(portfolioUid) {
            try {
                showLoading("Loading holdings & positions...");

                // Fetch portfolio data for the specific portfolio using its UID
                const response = await fetch(`/portfolio/api/portfolios/${portfolioUid}`);
                const portfolio = await response.json();

                if (portfolio.portfolio.status == "Failure") {
                    document.getElementById('failed-results').classList.remove('hidden');
                    document.getElementById('success-results').classList.add('hidden');
                }

                holdings = portfolio.holdings || []; // Extract holdings from the response
                positions = portfolio.positions || []; // Extract positions from the response

                // Remove charts section if holdings or positions are empty
                if (holdings.length === 0 && positions.length === 0) {
                    const chartsSection = document.querySelector('#charts-container');
                    const aiFeedbackBtns = document.querySelectorAll('.aiFeedbackBtn');
                    aiFeedbackBtns.forEach(btn => btn.remove());
                    if (chartsSection) chartsSection.remove();
                }

                // Update header stats
                document.getElementById('detail-total-holdings').innerHTML = `
                    Total Holdings: <span class="text-black dark:text-white font-medium">
                        ${portfolio.total_holdings ? portfolio.total_holdings.toLocaleString('en-IN') : holdings.length}
                    </span>
                `;
                document.getElementById('detail-total-value').innerHTML = `
                    Total Value: <span class="text-black dark:text-white font-medium">
                        ${formatIndianNumber(portfolio.total_value)}
                    </span>
                `;
                document.getElementById('detail-stock-pl').innerHTML = `
                    Stock P&L: <span class="${portfolio.stock_pl >= 0 ? 'text-green-600' : 'text-red-600'} font-medium">
                        ${formatIndianNumber(portfolio.stock_pl)} (${formatIndianNumber(portfolio.stock_pl_percentage)}%)
                    </span>
                `;
                document.getElementById('detail-fno-pl').innerHTML = `
                    F&O P&L: <span class="${portfolio.fno_pl >= 0 ? 'text-green-600' : 'text-red-600'} font-medium">
                        ${formatIndianNumber(portfolio.fno_pl)} (${formatIndianNumber(portfolio.fno_pl_percentage)}%)
                    </span>
                `;

                // Separate equity positions and F&O positions
                const equityPositions = positions.filter(position => position.instrument === 'equity');
                const foPositions = positions.filter(position => position.instrument !== 'equity');
                const portfolioType = portfolioUid.includes("app") ? "imported" : 'manual';

                // Dynamically populate the holdings & positions tables
                populateHoldingsTable(holdings, portfolioUid, portfolioType);
                populateEquityPositions(equityPositions);
                populateFoPositions(foPositions);

                // Initialize charts
                if (typeof initializeCharts === 'function' || holdings.length > 0 || positions.length > 0) {
                    initializeCharts(holdings, positions);
                }

                // Prepare portfolioHoldings for AI feedback
                portfolioHoldings = holdings.map(holding => ({
                    symbol: holding.symbol,
                    company_name: holding.company_name || holding.symbol,
                    sector: holding.sector + " ",
                    industry: holding.industry,
                    quantity: holding.quantity || 0,
                    avg_price: holding.avg_price || 0,
                    current_price: holding.current_price || 0,
                    invested_amount: holding.invested_amount || 0,
                    market_value: holding.market_value || 0,
                    pl: holding.pl != null ? holding.pl : 0,
                    pl_percentage: holding.pl_percentage != null ? holding.pl_percentage : 0,
                    pe_ratio: holding.pe_ratio || null,
                    market_cap: holding.market_cap || null,
                    week_52_low: holding.week_52_low || null,
                    week_52_high: holding.week_52_high || null,
                }));

            } catch (error) {
                console.error('Error fetching portfolio data:', error);
            } finally {
                hideLoading();
            }
        }

        // Call the function with portfolio UID when the page is loaded
        document.addEventListener('DOMContentLoaded', () => {
            const portfolioUid = window.location.pathname.split('/').pop();
            loadSingleHoldingsPositions(portfolioUid);
        });

        window.addEventListener('DOMContentLoaded', function () {
            const previewtimestamp = document.getElementById("preview-timestamp")
            if (previewtimestamp) {
                window.createTimestamp('preview-timestamp', {
                    showRefresh: true,
                    onRefresh: async () => {
                        await refreshPortfolioData();
                    }
                });
            }
        });

        function getPortfolioAIFeedback(type) {
            const equityPositions = positions.filter(position => position.instrument === 'equity');
            const fnoPositions = positions.filter(position => position.instrument !== 'equity');
            if (type === 'portfolio_holdings') {
                getAIFeedback(
                    { holdings: portfolioHoldings },
                    { analysisType: type }
                );
            } else if (type === 'portfolio_positions') {
                getAIFeedback(
                    { positions: equityPositions },
                    { analysisType: type }
                );
            } else if (type === 'portfolio_fno_positions') {
                getAIFeedback(
                    { positions: fnoPositions },
                    { analysisType: type }
                );
            }
        }
    </script>
    {% include 'blocks/ai-feedback.html' %}
    {% include 'portfolio/blocks/share-portfolio-modal.html' %}

    <script>
        function togglePortSlideout() {
            const strategiesContainers = document.querySelectorAll('.portfolio-container');

            // Loop through all the selected containers and toggle the class
            strategiesContainers.forEach(container => {
                container.classList.toggle('!left-0');
            });
        }

        function applyAndClose() {
            // You can perform additional actions for the "Apply" button here.

            // Close the slideout menu after applying
            togglePortSlideout();
        }
    </script>
    <style>
        /* Default (Desktop view) */
        .portfolio-container {
            display: block;
        }

        /* Slide-out effect (Mobile view) */
        @media screen and (max-width: 1290px) {
            .portfolio-container {
                position: fixed;
                top: 0;
                left: -100%;
                z-index: 30;
                width: 100%;
                height: 100%;
                background-color: white;
                transition: left 0.3s ease-in-out;
            }

            .portfolio-container.open {
                left: 0;
            }
        }
    </style>
</body>

</html>