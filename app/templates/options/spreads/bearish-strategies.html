<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('buy-put')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/buy-put.png') }}" alt='Buy Put' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Buy Put</span>
            </div>

            <p class="text-[10px] text-gray-500 mb-2">Long put for downside potential; limited risk if underlying rises.
            </p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Limited
                    Risk</span>
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">High
                    Cost</span>
            </div>
        </div>
    </div>
</div>

<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('bear-put-spread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/bear-put-spread.png') }}" alt='Bear Put Spread'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Bear Put Spread</span>
            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy higher-strike put, sell lower-strike put; risk-defined bearish
                play.</p>
            <div class="flex gap-1 flex-wrap">
                <span
                    class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Risk-Defined</span>
                <span class="text-[10px] font-medium bg-blue-50 text-blue-700 px-2 py-0.5 rounded-sm">Lower
                    Cost</span>
            </div>
        </div>
    </div>
</div>

<!-- Bear Call Spread -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('bear-call-spread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/bear-call-spread.png') }}" alt='Bear Call Spread'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Bear Call Spread</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Sell lower strike call & buy higher strike call. Also known as
                Call
                Credit Spread.</p>
            <div class="flex gap-1 flex-wrap">
                <span
                    class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Risk-Defined</span>
                <span class="text-[10px] font-medium bg-blue-50 text-blue-700 px-2 py-0.5 rounded-sm">Credit
                    Strategy</span>
            </div>
        </div>
    </div>
</div>

<!-- Diagonal Put Spread -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('diagonal-put-spread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/diagonal-put-spread.png') }}" alt='Diagonal Put Spread'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Diagonal Put Spread</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy ITM LEAPS put, sell shorter-term OTM put. Calendar spread
                variant.</p>

            <div class="flex gap-1 flex-wrap">
                <span
                    class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Risk-Defined</span>
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
            </div>
        </div>
    </div>
</div>

<!-- Short Call -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('short-call')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/short-call.png') }}" alt='Short Call'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Short Call</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Sell call for premium. Also known as Naked Call. Unlimited risk if
                stock
                rises.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-sm">High
                    Premium</span>
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">High Risk</span>
            </div>
        </div>
    </div>
</div>

<!-- Put Ratio Backspread -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('put-ratio-backspread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/put-ratio-backspread.png') }}" alt='Put Ratio Backspread'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Put Ratio Backspread</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Sell 1 higher strike put, buy 2 lower strike puts. Profits from
                big
                moves
                down.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Unlimited
                    Profit</span>
            </div>
        </div>
    </div>
</div>

<!-- Call Broken Wing -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('call-broken-wing-butterfly')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/call-broken-wing.png') }}"
                alt='Call Broken Wing Butterfly' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Call Broken Wing Butterfly</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Modified call butterfly with bearish bias. Also known as Skip
                Strike
                Butterfly.</p>

            <div class="flex gap-1 flex-wrap">
                <span
                    class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Risk-Defined</span>
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
            </div>
        </div>
    </div>
</div>

<!-- Inverse Put Broken Wing -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('inverse-put-broken-wing-butterfly')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src='/static/images/strategies/bearish-strategies/inverse-put-broken-wing.png'
                alt='Inverse Put Broken Wing Butterfly' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Inverse Put Broken Wing Butterfly</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Inverse of put broken wing. Also known as Inverse Skip Strike
                Butterfly.</p>

            <div class="flex gap-1 flex-wrap">
                <span
                    class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Risk-Defined</span>
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
            </div>
        </div>
    </div>
</div>

<!-- Reverse Jade Lizard -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('reverse-jade-lizard')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/reverse-jade-lizard.png') }}" alt='Reverse Jade Lizard'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Reverse Jade Lizard</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Short OTM call with OTM bear put spread. No downside risk when
                properly
                structured.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
                <span class="text-[10px] font-medium bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-sm">High
                    Premium</span>
            </div>
        </div>
    </div>
</div>

<!-- Strip -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('strip')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/strip.png') }}" alt='Strip' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Strip</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy 1 ATM call & 2 ATM puts. More profit potential on downside
                moves.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Unlimited
                    Profit</span>
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">High
                    Cost</span>
            </div>
        </div>
    </div>
</div>

<!-- Put Ratio Spread -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('put-ratio-spread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/put-ratio-spread.png') }}" alt='Put Ratio Spread'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Put Ratio Spread</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy fewer puts, sell more puts. Another ratio-based approach.</p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
                <span class="text-[10px] font-medium bg-lime-50 text-lime-700 px-2 py-0.5 rounded-sm">Medium
                    Margin</span>
            </div>
        </div>
    </div>
</div>

<!-- Short Synthetic Future -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('short-synthetic-future')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/short-synthetic-future.png') }}"
                alt='Short Synthetic Future' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Short Synthetic Future</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Sell ATM call & buy ATM put. Simulates short stock position with
                minimal
                cost.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Unlimited
                    Profit</span>
                <span class="text-[10px] font-medium bg-purple-50 text-purple-700 px-2 py-0.5 rounded-sm">Low
                    Cost</span>
            </div>
        </div>
    </div>
</div>

<!-- Synthetic Put -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('synthetic-put')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/synthetic-put.png') }}" alt='Synthetic Put'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Synthetic Put</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Short stock & buy ATM call. Also known as Protective Call.
                Simulates
                long
                put.</p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Unlimited
                    Profit</span>
                <span class="text-[10px] font-medium bg-blue-50 text-blue-700 px-2 py-0.5 rounded-sm">Stock-Based</span>
            </div>
        </div>
    </div>
</div>

<!-- Short Combo -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBearishStrategy('short-combo')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bearish-strategies/short-combo.png') }}" alt='Short Combo'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Short Combo</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Sell OTM call & buy OTM put. Neutral between strikes, profit
                beyond
                them.</p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Unlimited
                    Profit</span>
                <span class="text-[10px] font-medium bg-purple-50 text-purple-700 px-2 py-0.5 rounded-sm">Low
                    Cost</span>
            </div>
        </div>
    </div>
</div>


<script>
    function loadBearishStrategy(strategyType) {
        if (!window.currentSymbol) {
            showAlert("Please search for a symbol first");
            return;
        }
        selectedLegs = [];
        let atmStrike = null;
        let sellPutStrike = null;
        let buyPutPrice = null;
        let sellPutPrice = null;
        let buyPutStrike = null;
        let shortCallPrice = null;

        switch (strategyType) {
            case 'buy-put':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get option price for ATM strike
                const putPrice = getOptionPrice('PE', atmStrike);
                if (!putPrice) {
                    showAlert("Unable to get option price. Please load chain data first.");
                    return;
                }

                // Add the leg
                selectedLegs.push({
                    id: `PE-${atmStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: atmStrike,
                    price: putPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'bear-put-spread':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Find the next lower strike for the sell leg
                const bearPutStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const bearPutAtmIndex = bearPutStrikes.indexOf(atmStrike);
                if (bearPutAtmIndex === -1 || bearPutAtmIndex === 0) {
                    showAlert("Unable to find appropriate strikes for bear put spread.");
                    return;
                }
                const strikeDelta = bearPutStrikes[bearPutAtmIndex - 1] - bearPutStrikes[bearPutAtmIndex];
                const strikeDistance = calculateLegDistance(strikeDelta, window.currentSymbol, 'bear-put-spread');
                sellPutStrike = bearPutStrikes[bearPutAtmIndex - strikeDistance];
                // Get option prices
                buyPutPrice = getOptionPrice('PE', atmStrike);
                sellPutPrice = getOptionPrice('PE', sellPutStrike);
                if (!buyPutPrice || !sellPutPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add buy leg (higher strike)
                selectedLegs.push({
                    id: `PE-${atmStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: atmStrike,
                    price: buyPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add sell leg (lower strike)
                selectedLegs.push({
                    id: `PE-${sellPutStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: sellPutStrike,
                    price: sellPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'bear-call-spread':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const bearCallStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const bearCallAtmIndex = bearCallStrikes.indexOf(atmStrike);

                if (bearCallAtmIndex === -1 || bearCallAtmIndex >= bearCallStrikes.length - 1) {
                    showAlert("Unable to find appropriate strikes for bear call spread.");
                    return;
                }

                // For short call, use ATM strike
                const shortCallStrike = atmStrike;
                // Calculate the appropriate strike distance based on strategy
                const callStrikeDelta = bearCallStrikes[bearCallAtmIndex + 1] - bearCallStrikes[bearCallAtmIndex];
                const callStrikeDistance = calculateLegDistance(callStrikeDelta, window.currentSymbol, 'bear-call-spread');

                // Check if we have enough strikes available
                if (bearCallAtmIndex + callStrikeDistance >= bearCallStrikes.length) {
                    showAlert("Not enough strikes available for bear call spread with the calculated distance.");
                    return;
                }

                // For long call, use strike based on calculated distance
                const longCallStrike = bearCallStrikes[bearCallAtmIndex + callStrikeDistance];

                // Get option prices
                shortCallPrice = getOptionPrice('CE', shortCallStrike);
                const longCallPrice = getOptionPrice('CE', longCallStrike);

                if (!shortCallPrice || !longCallPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add short call leg (lower strike)
                selectedLegs.push({
                    id: `CE-${shortCallStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: shortCallStrike,
                    price: shortCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long call leg (higher strike)
                selectedLegs.push({
                    id: `CE-${longCallStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: longCallStrike,
                    price: longCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'diagonal-put-spread':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get expiry dates - we need two different expiries
                const availableExpiries = chainData.expiryDates;
                if (availableExpiries.length < 2) {
                    showAlert("Need at least two different expiry dates for diagonal spread.");
                    return;
                }

                // Use current expiry for short leg
                const shortExpiry = modalCurrentExpiry;
                // Use next available expiry for long leg (farther in future)
                const longExpiry = availableExpiries[availableExpiries.indexOf(modalCurrentExpiry) + 1];

                // Get available strikes for SHORT EXPIRY
                const shortExpiryStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, shortExpiry);
                const shortExpiryAtmIndex = shortExpiryStrikes.indexOf(atmStrike);

                if (shortExpiryAtmIndex === -1 || shortExpiryAtmIndex < 1) {
                    showAlert("Unable to find appropriate OTM strikes for short leg in diagonal put spread.");
                    return;
                }

                // Get available strikes for LONG EXPIRY
                const longExpiryStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, longExpiry);
                const longExpiryAtmIndex = longExpiryStrikes.indexOf(atmStrike);

                // Need at least 2 strikes above ATM for a properly deep ITM long put
                if (longExpiryAtmIndex === -1 || longExpiryAtmIndex >= longExpiryStrikes.length - 2) {
                    showAlert("Unable to find appropriate deep ITM strikes for long leg in diagonal put spread.");
                    return;
                }

                // Calculate the appropriate strike distance for short leg
                const shortStrikeDelta = shortExpiryStrikes[shortExpiryAtmIndex] - shortExpiryStrikes[shortExpiryAtmIndex - 1];
                const shortStrikeDistance = calculateLegDistance(shortStrikeDelta, window.currentSymbol, 'diagonal-put-spread');

                // Check if we have enough strikes available for short leg
                if (shortExpiryAtmIndex - shortStrikeDistance < 0) {
                    showAlert("Not enough strikes available for diagonal put spread with the calculated distance (short leg).");
                    return;
                }

                // For the short put, go OTM in the SHORT expiry (below ATM for puts) based on calculated distance
                const shortStrike = shortExpiryStrikes[shortExpiryAtmIndex - shortStrikeDistance];

                // Calculate the appropriate strike distance for long leg
                const longStrikeDelta = longExpiryStrikes[longExpiryAtmIndex + 1] - longExpiryStrikes[longExpiryAtmIndex];
                const longStrikeDistance = calculateLegDistance(longStrikeDelta, window.currentSymbol, 'diagonal-put-spread');

                // Check if we have enough strikes available for long leg
                if (longExpiryAtmIndex + longStrikeDistance >= longExpiryStrikes.length) {
                    showAlert("Not enough strikes available for diagonal put spread with the calculated distance (long leg).");
                    return;
                }

                // For the long LEAPS put, go deep ITM in the LONG expiry (above ATM for puts) based on calculated distance
                // This ensures a deeper ITM put with more delta, mimicking short stock ownership better
                const longStrike = longExpiryStrikes[longExpiryAtmIndex + longStrikeDistance];

                // Ensure the long strike is higher than short strike for proper diagonal
                if (longStrike <= shortStrike) {
                    showAlert("Cannot create proper diagonal spread with available strikes. Try a different expiry.");
                    return;
                }

                // Get option prices with proper expiry dates
                const diagLongPrice = getOptionPrice('PE', longStrike, longExpiry);
                const diagShortPrice = getOptionPrice('PE', shortStrike, shortExpiry);

                if (!diagLongPrice || !diagShortPrice) {
                    showAlert("Unable to get option prices for diagonal put spread. Please load chain data first.");
                    return;
                }

                // Verify debit spread - long option should cost more than premium received from short option
                if (diagLongPrice <= diagShortPrice) {
                    showAlert("Warning: Unusual price relationship in diagonal spread. Verify option prices.");
                }

                // Add long LEAPS put leg
                selectedLegs.push({
                    id: `PE-${longStrike}-buy-${longExpiry}`,
                    type: 'PE',
                    strike: longStrike,
                    price: diagLongPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: longExpiry
                });

                // Add short put leg
                selectedLegs.push({
                    id: `PE-${shortStrike}-sell-${shortExpiry}`,
                    type: 'PE',
                    strike: shortStrike,
                    price: diagShortPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: shortExpiry
                });

                updateLegsAndUrl();
                break;

            case 'short-call':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get option price for ATM strike
                shortCallPrice = getOptionPrice('CE', atmStrike);
                if (!shortCallPrice) {
                    showAlert("Unable to get option price. Please load chain data first.");
                    return;
                }

                // Add the short call leg
                selectedLegs.push({
                    id: `CE-${atmStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: shortCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'put-ratio-backspread':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const backspreadStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const backspreadAtmIndex = backspreadStrikes.indexOf(atmStrike);

                // Need at least one strike above ATM and 2+ strikes below ATM for proper setup
                if (backspreadAtmIndex === -1 || backspreadAtmIndex === 0 || backspreadAtmIndex >= backspreadStrikes.length - 2) {
                    showAlert("Unable to find appropriate strikes for put ratio backspread.");
                    return;
                }

                // For short put, use ITM strike (above ATM)
                const shortBackspreadStrike = backspreadStrikes[backspreadAtmIndex + 1];
                const longBackspreadDelta = backspreadStrikes[backspreadAtmIndex] - backspreadStrikes[backspreadAtmIndex - 1];
                const longBackspreadDistance = calculateLegDistance(longBackspreadDelta, window.currentSymbol, 'put-ratio-backspread');
                const longBackspreadStrike = backspreadStrikes[backspreadAtmIndex - longBackspreadDistance];

                // Get option prices
                const shortBackspreadPrice = getOptionPrice('PE', shortBackspreadStrike);
                const longBackspreadPrice = getOptionPrice('PE', longBackspreadStrike);

                if (!shortBackspreadPrice || !longBackspreadPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Verify this will likely be a credit spread
                if (shortBackspreadPrice < 2 * longBackspreadPrice) {
                    showAlert("Warning: This put ratio backspread may not be a credit spread. Check option prices.");
                }

                // Add short put leg (1x ITM - higher strike)
                selectedLegs.push({
                    id: `PE-${shortBackspreadStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: shortBackspreadStrike,
                    price: shortBackspreadPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long put leg (2x OTM - lower strike) - using lots parameter
                selectedLegs.push({
                    id: `PE-${longBackspreadStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: longBackspreadStrike,
                    price: longBackspreadPrice,
                    action: 'buy',
                    lots: 2,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'call-broken-wing-butterfly':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const bwStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const bwAtmIndex = bwStrikes.indexOf(atmStrike);

                // Need at least 3 strikes above ATM for proper setup
                if (bwAtmIndex === -1 || bwAtmIndex >= bwStrikes.length - 3) {
                    showAlert("Unable to find appropriate strikes for call broken wing butterfly.");
                    return;
                }

                // Lower strike is ATM
                const bwLowerStrike = atmStrike;

                // Calculate the appropriate strike distance for middle leg
                const bwMiddleDelta = bwStrikes[bwAtmIndex + 1] - bwStrikes[bwAtmIndex];
                const bwMiddleDistance = calculateLegDistance(bwMiddleDelta, window.currentSymbol, 'call-broken-wing-butterfly');

                // Check if we have enough strikes available for middle leg
                if (bwAtmIndex + bwMiddleDistance >= bwStrikes.length) {
                    showAlert("Not enough strikes available for call broken wing butterfly with the calculated distance (middle leg).");
                    return;
                }

                // Middle strike is higher than ATM based on calculated distance
                const bwMiddleStrike = bwStrikes[bwAtmIndex + bwMiddleDistance];

                // Calculate the appropriate strike distance for upper leg (from middle strike)
                const bwUpperDelta = bwStrikes[bwAtmIndex + bwMiddleDistance + 1] - bwStrikes[bwAtmIndex + bwMiddleDistance];
                const bwUpperDistance = calculateLegDistance(bwUpperDelta, window.currentSymbol, 'call-broken-wing-butterfly');

                // Check if we have enough strikes available for upper leg
                if (bwAtmIndex + bwMiddleDistance + bwUpperDistance >= bwStrikes.length) {
                    showAlert("Not enough strikes available for call broken wing butterfly with the calculated distance (upper leg).");
                    return;
                }

                // Upper strike is higher than middle strike based on calculated distance
                const bwUpperStrike = bwStrikes[bwAtmIndex + bwMiddleDistance + bwUpperDistance];

                // Get option prices
                const bwLowerPrice = getOptionPrice('CE', bwLowerStrike);
                const bwMiddlePrice = getOptionPrice('CE', bwMiddleStrike);
                const bwUpperPrice = getOptionPrice('CE', bwUpperStrike);

                if (!bwLowerPrice || !bwMiddlePrice || !bwUpperPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add long lower call
                selectedLegs.push({
                    id: `CE-${bwLowerStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: bwLowerStrike,
                    price: bwLowerPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short middle calls (2x) - using lots parameter
                selectedLegs.push({
                    id: `CE-${bwMiddleStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: bwMiddleStrike,
                    price: bwMiddlePrice,
                    action: 'sell',
                    lots: 2,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long upper call
                selectedLegs.push({
                    id: `CE-${bwUpperStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: bwUpperStrike,
                    price: bwUpperPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'inverse-put-broken-wing-butterfly':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const ipbwStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const ipbwAtmIndex = ipbwStrikes.indexOf(atmStrike);

                // Need at least 3 strikes below ATM for proper setup
                if (ipbwAtmIndex === -1 || ipbwAtmIndex < 3) {
                    showAlert("Unable to find appropriate strikes for inverse put broken wing butterfly.");
                    return;
                }

                // Upper strike is ATM
                const ipbwUpperStrike = atmStrike;

                // Calculate the appropriate strike distance for middle leg
                const ipbwMiddleDelta = ipbwStrikes[ipbwAtmIndex] - ipbwStrikes[ipbwAtmIndex - 1];
                const ipbwMiddleDistance = calculateLegDistance(ipbwMiddleDelta, window.currentSymbol, 'inverse-put-broken-wing-butterfly');

                // Check if we have enough strikes available for middle leg
                if (ipbwAtmIndex - ipbwMiddleDistance < 0) {
                    showAlert("Not enough strikes available for inverse put broken wing butterfly with the calculated distance (middle leg).");
                    return;
                }

                // Middle strike is lower than ATM based on calculated distance
                const ipbwMiddleStrike = ipbwStrikes[ipbwAtmIndex - ipbwMiddleDistance];

                // Calculate the appropriate strike distance for lower leg (from middle strike)
                const ipbwLowerDelta = ipbwStrikes[ipbwAtmIndex - ipbwMiddleDistance] - ipbwStrikes[ipbwAtmIndex - ipbwMiddleDistance - 1];
                const ipbwLowerDistance = calculateLegDistance(ipbwLowerDelta, window.currentSymbol, 'inverse-put-broken-wing-butterfly');

                // Check if we have enough strikes available for lower leg
                if (ipbwAtmIndex - ipbwMiddleDistance - ipbwLowerDistance < 0) {
                    showAlert("Not enough strikes available for inverse put broken wing butterfly with the calculated distance (lower leg).");
                    return;
                }

                // Lower strike is lower than middle strike based on calculated distance
                const ipbwLowerStrike = ipbwStrikes[ipbwAtmIndex - ipbwMiddleDistance - ipbwLowerDistance];

                // Get option prices
                const ipbwUpperPrice = getOptionPrice('PE', ipbwUpperStrike);
                const ipbwMiddlePrice = getOptionPrice('PE', ipbwMiddleStrike);
                const ipbwLowerPrice = getOptionPrice('PE', ipbwLowerStrike);

                if (!ipbwUpperPrice || !ipbwMiddlePrice || !ipbwLowerPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add long upper put
                selectedLegs.push({
                    id: `PE-${ipbwUpperStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: ipbwUpperStrike,
                    price: ipbwUpperPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short middle puts (2x) - using lots parameter
                selectedLegs.push({
                    id: `PE-${ipbwMiddleStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: ipbwMiddleStrike,
                    price: ipbwMiddlePrice,
                    action: 'sell',
                    lots: 2,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long lower put
                selectedLegs.push({
                    id: `PE-${ipbwLowerStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: ipbwLowerStrike,
                    price: ipbwLowerPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'reverse-jade-lizard':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const rjlStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const rjlAtmIndex = rjlStrikes.indexOf(atmStrike);

                // Need at least 2 strikes below ATM for the put spread and 1 strike above for the call
                if (rjlAtmIndex === -1 || rjlAtmIndex < 2 || rjlAtmIndex >= rjlStrikes.length - 1) {
                    showAlert("Unable to find appropriate strikes for reverse jade lizard.");
                    return;
                }

                // Calculate the appropriate strike distance for call leg
                const rjlCallDelta = rjlStrikes[rjlAtmIndex + 1] - rjlStrikes[rjlAtmIndex];
                const rjlCallDistance = calculateLegDistance(rjlCallDelta, window.currentSymbol, 'reverse-jade-lizard');

                // Check if we have enough strikes available for call leg
                if (rjlAtmIndex + rjlCallDistance >= rjlStrikes.length) {
                    showAlert("Not enough strikes available for reverse jade lizard with the calculated distance (call leg).");
                    return;
                }

                // For short OTM call, go above ATM based on calculated distance
                const rjlCallStrike = rjlStrikes[rjlAtmIndex + rjlCallDistance];

                // Calculate the appropriate strike distance for short put leg
                const rjlShortPutDelta = rjlStrikes[rjlAtmIndex] - rjlStrikes[rjlAtmIndex - 1];
                const rjlShortPutDistance = calculateLegDistance(rjlShortPutDelta, window.currentSymbol, 'reverse-jade-lizard');

                // Check if we have enough strikes available for short put leg
                if (rjlAtmIndex - rjlShortPutDistance < 0) {
                    showAlert("Not enough strikes available for reverse jade lizard with the calculated distance (short put leg).");
                    return;
                }

                // For bear put spread: short put below ATM based on calculated distance
                const rjlShortPutStrike = rjlStrikes[rjlAtmIndex - rjlShortPutDistance];

                // Calculate the appropriate strike distance for long put leg (from short put)
                const rjlLongPutDelta = rjlStrikes[rjlAtmIndex - rjlShortPutDistance] - rjlStrikes[rjlAtmIndex - rjlShortPutDistance - 1];
                const rjlLongPutDistance = calculateLegDistance(rjlLongPutDelta, window.currentSymbol, 'reverse-jade-lizard');

                // Check if we have enough strikes available for long put leg
                if (rjlAtmIndex - rjlShortPutDistance - rjlLongPutDistance < 0) {
                    showAlert("Not enough strikes available for reverse jade lizard with the calculated distance (long put leg).");
                    return;
                }

                // For bear put spread: long put further below short put based on calculated distance
                const rjlLongPutStrike = rjlStrikes[rjlAtmIndex - rjlShortPutDistance - rjlLongPutDistance];

                // A proper Reverse Jade Lizard should have credit exceeding the width of put spread to eliminate downside risk
                // (This can be verified but is not enforced here)

                // Get option prices
                const rjlCallPrice = getOptionPrice('CE', rjlCallStrike);
                const rjlShortPutPrice = getOptionPrice('PE', rjlShortPutStrike);
                const rjlLongPutPrice = getOptionPrice('PE', rjlLongPutStrike);

                if (!rjlCallPrice || !rjlShortPutPrice || !rjlLongPutPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Verify credit collected
                const creditCollected = rjlCallPrice + rjlShortPutPrice - rjlLongPutPrice;
                const putSpreadWidth = rjlShortPutStrike - rjlLongPutStrike;

                if (creditCollected < putSpreadWidth) {
                    showAlert("Warning: Reverse Jade Lizard may have downside risk as credit collected is less than put spread width.");
                }

                // Add short OTM call leg
                selectedLegs.push({
                    id: `CE-${rjlCallStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: rjlCallStrike,
                    price: rjlCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short OTM put leg
                selectedLegs.push({
                    id: `PE-${rjlShortPutStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: rjlShortPutStrike,
                    price: rjlShortPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long further OTM put leg
                selectedLegs.push({
                    id: `PE-${rjlLongPutStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: rjlLongPutStrike,
                    price: rjlLongPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'strip':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get option prices for ATM call and ATM puts
                const callPrice = getOptionPrice('CE', atmStrike);
                const stripPutPrice = getOptionPrice('PE', atmStrike);

                if (!callPrice || !stripPutPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add long call leg (1x)
                selectedLegs.push({
                    id: `CE-${atmStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: callPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long put legs (2x) - using lots parameter
                selectedLegs.push({
                    id: `PE-${atmStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: atmStrike,
                    price: stripPutPrice,
                    action: 'buy',
                    lots: 2,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'put-ratio-spread':
                // Find ATM strike for the buy leg
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get all available strikes
                const putRatioStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const atmPutRatioIndex = putRatioStrikes.indexOf(atmStrike);

                if (atmPutRatioIndex === -1 || atmPutRatioIndex === 0) {
                    showAlert("Not enough strikes available for put ratio spread.");
                    return;
                }

                // Buy leg at ATM
                const buyPutRatioStrike = atmStrike;

                // Calculate the appropriate strike distance for sell leg
                const sellPutRatioDelta = putRatioStrikes[atmPutRatioIndex] - putRatioStrikes[atmPutRatioIndex - 1];
                const sellPutRatioDistance = calculateLegDistance(sellPutRatioDelta, window.currentSymbol, 'put-ratio-spread');

                // Check if we have enough strikes available for sell leg
                if (atmPutRatioIndex - sellPutRatioDistance < 0) {
                    showAlert("Not enough strikes available for put ratio spread with the calculated distance.");
                    return;
                }

                // Sell leg at lower strike based on calculated distance
                const sellPutRatioStrike = putRatioStrikes[atmPutRatioIndex - sellPutRatioDistance];

                // Get option prices
                const buyPutRatioPrice = getOptionPrice('PE', buyPutRatioStrike);
                const sellPutRatioPrice = getOptionPrice('PE', sellPutRatioStrike);

                if (!buyPutRatioPrice || !sellPutRatioPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add buy leg (1x)
                selectedLegs.push({
                    id: `PE-${buyPutRatioStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: buyPutRatioStrike,
                    price: buyPutRatioPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add sell legs (2x) - using lots parameter
                selectedLegs.push({
                    id: `PE-${sellPutRatioStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: sellPutRatioStrike,
                    price: sellPutRatioPrice,
                    action: 'sell',
                    lots: 2,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'short-synthetic-future':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get option prices for ATM strike
                const synthCallPrice = getOptionPrice('CE', atmStrike);
                const synthPutPrice = getOptionPrice('PE', atmStrike);

                if (!synthCallPrice || !synthPutPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add short ATM call leg
                selectedLegs.push({
                    id: `CE-${atmStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: synthCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long ATM put leg
                selectedLegs.push({
                    id: `PE-${atmStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: atmStrike,
                    price: synthPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'synthetic-put':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Check if we have future prices for this expiry
                if (!futurePrices || !futurePrices[modalCurrentExpiry]) {
                    showAlert(`Future prices not available for ${modalCurrentExpiry}. Cannot create synthetic-put strategy.`);
                    return;
                }

                // Get option price for ATM call
                const synthPutCallPrice = getOptionPrice('CE', atmStrike);
                if (!synthPutCallPrice) {
                    showAlert("Unable to get option price. Please load chain data first.");
                    return;
                }

                // Add the futures leg (short)
                selectedLegs.push({
                    id: `FUT-sell-${modalCurrentExpiry}`,
                    type: 'FUT',
                    strike: null,
                    price: futurePrices[modalCurrentExpiry],
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long ATM call leg
                selectedLegs.push({
                    id: `CE-${atmStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: synthPutCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'short-combo':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const comboStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const comboAtmIndex = comboStrikes.indexOf(atmStrike);

                // Need at least 2 strikes on each side of ATM
                if (comboAtmIndex === -1 || comboAtmIndex < 2 || comboAtmIndex >= comboStrikes.length - 2) {
                    showAlert("Unable to find appropriate strikes for short combo strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for put leg
                const comboPutDelta = comboStrikes[comboAtmIndex] - comboStrikes[comboAtmIndex - 1];
                const comboPutDistance = calculateLegDistance(comboPutDelta, window.currentSymbol, 'short-combo');

                // Check if we have enough strikes available for put leg
                if (comboAtmIndex - comboPutDistance < 0) {
                    showAlert("Not enough strikes available for short combo with the calculated distance (put leg).");
                    return;
                }

                // For long put, go below ATM based on calculated distance (wider neutral zone)
                const comboPutStrike = comboStrikes[comboAtmIndex - comboPutDistance];

                // Calculate the appropriate strike distance for call leg
                const comboCallDelta = comboStrikes[comboAtmIndex + 1] - comboStrikes[comboAtmIndex];
                const comboCallDistance = calculateLegDistance(comboCallDelta, window.currentSymbol, 'short-combo');

                // Check if we have enough strikes available for call leg
                if (comboAtmIndex + comboCallDistance >= comboStrikes.length) {
                    showAlert("Not enough strikes available for short combo with the calculated distance (call leg).");
                    return;
                }

                // For short call, go above ATM based on calculated distance
                const comboCallStrike = comboStrikes[comboAtmIndex + comboCallDistance];

                // Get option prices
                const comboCallPrice = getOptionPrice('CE', comboCallStrike);
                const comboPutPrice = getOptionPrice('PE', comboPutStrike);

                if (!comboCallPrice || !comboPutPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add short OTM call leg
                selectedLegs.push({
                    id: `CE-${comboCallStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: comboCallStrike,
                    price: comboCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long OTM put leg
                selectedLegs.push({
                    id: `PE-${comboPutStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: comboPutStrike,
                    price: comboPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

        }
    }

</script>