<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('buy-call')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/buy-call.png') }}" alt='Buy Call'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Buy Call</span>
            </div>
            <p class="text-[10px] text-gray-500 mb-2">Long call for upside potential; limited downside risk.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">High
                    Cost</span>
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Limited
                    Risk</span>
            </div>
        </div>
    </div>
</div>

<!-- Buy Call -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('bull-call-spread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/bull-call-spread.png') }}" alt='Bull Call Spread'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Bull Call Spread</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy a lower call & sell a higher call. Reduced cost, capped
                upside.</p>
            <div class="flex gap-1 flex-wrap">
                <span
                    class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Risk-Defined</span>
                <span class="text-[10px] font-medium bg-blue-50 text-blue-700 px-2 py-0.5 rounded-sm">Lower
                    Cost</span>
            </div>
        </div>
    </div>
</div>

<!-- Short Put -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('short-put')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/short-put.png') }}" alt='Short Put'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Short Put</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Sell put for premium; obligation to buy shares if assigned.</p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-sm">High
                    Premium</span>
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">High
                    Risk</span>
            </div>
        </div>
    </div>
</div>

<!-- Bull Put Spread -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('bull-put-spread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/bull-put-spread.png') }}" alt='Bull Put Spread'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Bull Put Spread</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Sell higher strike put & buy lower strike put. Limited risk &
                reward.</p>
            <div class="flex gap-1 flex-wrap">
                <span
                    class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Risk-Defined</span>
                <span class="text-[10px] font-medium bg-blue-50 text-blue-700 px-2 py-0.5 rounded-sm">Credit
                    Strategy</span>
            </div>
        </div>
    </div>
</div>

<!-- Diagonal Call Spread -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('diagonal-call-spread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/diagonal-call-spread.png') }}" alt='Diagonal Call Spread'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Diagonal Call Spread</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy ITM LEAPS call, sell shorter-term OTM call. Also known as Poor
                Man's Covered Call.</p>
            <div class="flex gap-1 flex-wrap">
                <span
                    class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Risk-Defined</span>
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
            </div>
        </div>
    </div>
</div>

<!-- Collar -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('collar')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/collar.png') }}" alt='Collar' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Collar</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Own stock, buy protective put & sell covered call. Also known as
                Risk-reversal.</p>
            <div class="flex gap-1 flex-wrap">
                <span
                    class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Risk-Defined</span>
                <span class="text-[10px] font-medium bg-blue-50 text-blue-700 px-2 py-0.5 rounded-sm">Stock-Based</span>
            </div>
        </div>
    </div>
</div>

<!-- Call Ratio Backspread -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('call-ratio-backspread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/call-ratio-backspread.png') }}"
                alt='Call Ratio Backspread' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Call Ratio Backspread</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Sell 1 ITM call (below ATM) and buy 2 OTM calls (2 strikes higher
                than ATM for better spread).</p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Unlimited
                    Profit</span>
            </div>
        </div>
    </div>
</div>

<!-- Covered Short Straddle -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('covered-short-straddle')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/covered-short-straddle.png') }}"
                alt='Covered Short Straddle' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Covered Short Straddle</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Own stock, sell ATM call & put. High income but significant
                downside
                risk.</p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-sm">High
                    Income</span>
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">High
                    Risk</span>
            </div>
        </div>
    </div>
</div>

<!-- Covered Short Strangle -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('covered-short-strangle')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/covered-short-strangle.png') }}"
                alt='Covered Short Strangle' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Covered Short Strangle</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Own stock, sell OTM call & put. Less risky version of covered
                straddle.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-sm">High
                    Income</span>
                <span class="text-[10px] font-medium bg-orange-50 text-orange-700 px-2 py-0.5 rounded-sm">Medium
                    Risk</span>
            </div>
        </div>
    </div>
</div>

<!-- Put Broken Wing -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('put-broken-wing-butterfly')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/put-broken-wing.png') }}" alt='Put Broken Wing'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Put Broken Wing Butterfly</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Modified put butterfly with bullish bias. Also known as Skip
                Strike
                Butterfly.</p>

            <div class="flex gap-1 flex-wrap">
                <span
                    class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Risk-Defined</span>
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
            </div>
        </div>
    </div>
</div>

<!-- Inverse Call Broken Wing -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('inverse-call-broken-wing-butterfly')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/inverse-call-broken-wing.png') }}"
                alt='Inverse Call Broken Wing' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Inverse Call Broken Wing Butterfly</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Inverse of call broken wing. Also known as Inverse Skip Strike
                Butterfly.</p>

            <div class="flex gap-1 flex-wrap">
                <span
                    class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Risk-Defined</span>
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
            </div>
        </div>
    </div>
</div>

<!-- Jade Lizard -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('jade-lizard')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/jade-lizard.png') }}" alt='Jade Lizard'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Jade Lizard</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Short OTM put with OTM bear call spread. No upside risk when
                properly
                structured.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
                <span class="text-[10px] font-medium bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-sm">High
                    Premium</span>
            </div>
        </div>
    </div>
</div>

<!-- Strap -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('strap')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/strap.png') }}" alt='Strap' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Strap</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy 2 ATM calls & 1 ATM put. More profit potential on upside
                moves.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Unlimited
                    Profit</span>
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">High
                    Cost</span>
            </div>
        </div>
    </div>
</div>

<!-- Call Ratio Spread -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('call-ratio-spread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/call-ratio-spread.png') }}" alt='Call Ratio Spread'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Call Ratio Spread</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy fewer calls, sell more calls at higher strike. Ratio-based
                risk.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
                <span class="text-[10px] font-medium bg-lime-50 text-lime-700 px-2 py-0.5 rounded-sm">Medium
                    Margin</span>
            </div>
        </div>
    </div>
</div>

<!-- Synthetic Future -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('long-synthetic-future')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/long-synthetic-future.png') }}"
                alt='Long Synthetic Future' class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Long Synthetic Future</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy ATM call & sell ATM put. Simulates long stock position with
                minimal
                cost.</p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Unlimited
                    Profit</span>
                <span class="text-[10px] font-medium bg-purple-50 text-purple-700 px-2 py-0.5 rounded-sm">Low
                    Cost</span>
            </div>
        </div>
    </div>
</div>

<!-- Long Combo -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadBullishStrategy('long-combo')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/bullish-strategies/long-combo.png') }}" alt='Long Combo'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Long Combo</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy OTM call & sell OTM put. Neutral between strikes, profit
                beyond
                them.</p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Unlimited
                    Profit</span>
                <span class="text-[10px] font-medium bg-purple-50 text-purple-700 px-2 py-0.5 rounded-sm">Low
                    Cost</span>
            </div>
        </div>
    </div>
</div>

<script>
    function loadBullishStrategy(strategyType) {
        if (!window.currentSymbol) {
            showAlert("Please search for a symbol first");
            return;
        }
        selectedLegs = [];
        let atmStrike = null;
        let sellPutStrike = null;
        let buyPutPrice = null;
        let sellPutPrice = null;
        let buyPutStrike = null;

        switch (strategyType) {
            case 'buy-call':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get option price for ATM strike
                const optionPrice = getOptionPrice('CE', atmStrike);
                if (!optionPrice) {
                    showAlert("Unable to get option price. Please load chain data first.");
                    return;
                }

                // Add the leg
                selectedLegs.push({
                    id: `CE-${atmStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: optionPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'short-put':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get option price for ATM strike
                const putOptionPrice = getOptionPrice('PE', atmStrike);
                if (!putOptionPrice) {
                    showAlert("Unable to get option price. Please load chain data first.");
                    return;
                }

                // Add the leg
                selectedLegs.push({
                    id: `PE-${atmStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: atmStrike,
                    price: putOptionPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'bull-call-spread':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Find available strikes
                const callStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const callAtmIndex = callStrikes.indexOf(atmStrike);
                if (callAtmIndex === -1 || callAtmIndex === callStrikes.length - 1) {
                    showAlert("Unable to find appropriate strikes for bull call spread.");
                    return;
                }

                // Calculate the appropriate strike distance based on strategy
                const callStrikeDelta = callStrikes[callAtmIndex + 1] - callStrikes[callAtmIndex];
                const callStrikeDistance = calculateLegDistance(callStrikeDelta, window.currentSymbol, 'bull-call-spread');

                // Check if we have enough strikes available
                if (callAtmIndex + callStrikeDistance >= callStrikes.length) {
                    showAlert("Not enough strikes available for bull call spread with the calculated distance.");
                    return;
                }

                // For sell leg, use higher strike based on calculated distance
                const callSellStrike = callStrikes[callAtmIndex + callStrikeDistance];

                // Get option prices
                const callBuyPrice = getOptionPrice('CE', atmStrike);
                const callSellPrice = getOptionPrice('CE', callSellStrike);
                if (!callBuyPrice || !callSellPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add buy leg
                selectedLegs.push({
                    id: `CE-${atmStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: callBuyPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add sell leg
                selectedLegs.push({
                    id: `CE-${callSellStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: callSellStrike,
                    price: callSellPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'bull-put-spread':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const bullPutStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const bullPutAtmIndex = bullPutStrikes.indexOf(atmStrike);

                if (bullPutAtmIndex === -1 || bullPutAtmIndex < 1) {
                    showAlert("Unable to find appropriate strikes for bull put spread.");
                    return;
                }

                // Sell the higher strike put (ATM)
                const bullPutSellStrike = atmStrike;

                // Calculate the appropriate strike distance based on strategy
                const bullPutStrikeDelta = bullPutStrikes[bullPutAtmIndex] - bullPutStrikes[bullPutAtmIndex - 1];
                const bullPutStrikeDistance = calculateLegDistance(bullPutStrikeDelta, window.currentSymbol, 'bull-put-spread');

                // Check if we have enough strikes available
                if (bullPutAtmIndex - bullPutStrikeDistance < 0) {
                    showAlert("Not enough strikes available for bull put spread with the calculated distance.");
                    return;
                }

                // Buy the lower strike put based on calculated distance
                const bullPutBuyStrike = bullPutStrikes[bullPutAtmIndex - bullPutStrikeDistance];

                // Get option prices
                const bullPutSellPrice = getOptionPrice('PE', bullPutSellStrike);
                const bullPutBuyPrice = getOptionPrice('PE', bullPutBuyStrike);

                if (!bullPutSellPrice || !bullPutBuyPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add sell leg (higher strike)
                selectedLegs.push({
                    id: `PE-${bullPutSellStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: bullPutSellStrike,
                    price: bullPutSellPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add buy leg (lower strike)
                selectedLegs.push({
                    id: `PE-${bullPutBuyStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: bullPutBuyStrike,
                    price: bullPutBuyPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'diagonal-call-spread':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike for diagonal call spread. Please load chain data first.");
                    return;
                }

                // Get expiry dates - we need two different expiries
                const availableExpiries = chainData.expiryDates;
                if (availableExpiries.length < 2) {
                    showAlert("Need at least two different expiry dates for diagonal spread.");
                    return;
                }

                // Use current expiry for short leg
                const shortExpiry = modalCurrentExpiry;
                // Use next available expiry for long leg (farther in future)
                const longExpiry = availableExpiries[availableExpiries.indexOf(modalCurrentExpiry) + 1];

                // Get available strikes for SHORT EXPIRY
                const shortExpiryStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, shortExpiry);
                const shortExpiryAtmIndex = shortExpiryStrikes.indexOf(atmStrike);

                if (shortExpiryAtmIndex === -1 || shortExpiryAtmIndex >= shortExpiryStrikes.length - 1) {
                    showAlert("Unable to find appropriate OTM strikes for short leg in diagonal call spread.");
                    return;
                }

                // Get available strikes for LONG EXPIRY
                const longExpiryStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, longExpiry);
                const longExpiryAtmIndex = longExpiryStrikes.indexOf(atmStrike);

                // Need at least 2 strikes below ATM for a properly deep ITM long call
                if (longExpiryAtmIndex === -1 || longExpiryAtmIndex < 2) {
                    showAlert("Unable to find appropriate deep ITM strikes for long leg in diagonal call spread.");
                    return;
                }

                // Calculate the appropriate strike distance for short leg
                const shortStrikeDelta = shortExpiryStrikes[shortExpiryAtmIndex + 1] - shortExpiryStrikes[shortExpiryAtmIndex];
                const shortStrikeDistance = calculateLegDistance(shortStrikeDelta, window.currentSymbol, 'diagonal-call-spread');

                // Check if we have enough strikes available for short leg
                if (shortExpiryAtmIndex + shortStrikeDistance >= shortExpiryStrikes.length) {
                    showAlert("Not enough strikes available for diagonal call spread with the calculated distance (short leg).");
                    return;
                }

                // For the short call, go OTM in the SHORT expiry based on calculated distance
                const shortStrike = shortExpiryStrikes[shortExpiryAtmIndex + shortStrikeDistance];

                // Calculate the appropriate strike distance for long leg
                const longStrikeDelta = longExpiryStrikes[longExpiryAtmIndex] - longExpiryStrikes[longExpiryAtmIndex - 1];
                const longStrikeDistance = calculateLegDistance(longStrikeDelta, window.currentSymbol, 'diagonal-call-spread');

                // Check if we have enough strikes available for long leg
                if (longExpiryAtmIndex - longStrikeDistance < 0) {
                    showAlert("Not enough strikes available for diagonal call spread with the calculated distance (long leg).");
                    return;
                }

                // For the long LEAPS call, go deep ITM in the LONG expiry based on calculated distance
                // This ensures a deeper ITM call with more delta, mimicking stock ownership better
                const longStrike = longExpiryStrikes[longExpiryAtmIndex - longStrikeDistance];

                // Ensure the long strike is lower than short strike for proper diagonal
                if (longStrike >= shortStrike) {
                    showAlert("Cannot create proper diagonal spread with available strikes. Try a different expiry.");
                    return;
                }

                // Get option prices with proper expiry dates
                const diagLongPrice = getOptionPrice('CE', longStrike, longExpiry);
                const diagShortPrice = getOptionPrice('CE', shortStrike, shortExpiry);

                if (!diagLongPrice || !diagShortPrice) {
                    showAlert("Unable to get option prices for diagonal call spread. Please load chain data first.");
                    return;
                }

                // Verify debit spread - long option should cost more than premium received from short option
                if (diagLongPrice <= diagShortPrice) {
                    showAlert("Warning: Unusual price relationship in diagonal spread. Verify option prices.");
                }

                // Add long LEAPS call leg
                selectedLegs.push({
                    id: `CE-${longStrike}-buy-${longExpiry}`,
                    type: 'CE',
                    strike: longStrike,
                    price: diagLongPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: longExpiry
                });

                // Add short call leg
                selectedLegs.push({
                    id: `CE-${shortStrike}-sell-${shortExpiry}`,
                    type: 'CE',
                    strike: shortStrike,
                    price: diagShortPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: shortExpiry
                });

                updateLegsAndUrl();
                break;

            case 'collar':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Check if we have future prices for this expiry
                if (!futurePrices || !futurePrices[modalCurrentExpiry]) {
                    showAlert(`Future prices not available for ${modalCurrentExpiry}. Cannot create collar strategy.`);
                    return;
                }

                // Get available strikes
                const collarStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const collarAtmIndex = collarStrikes.indexOf(atmStrike);

                if (collarAtmIndex === -1 || collarAtmIndex < 1 || collarAtmIndex >= collarStrikes.length - 1) {
                    showAlert("Unable to find appropriate strikes for collar strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for put leg
                const collarPutDelta = collarStrikes[collarAtmIndex] - collarStrikes[collarAtmIndex - 1];
                const collarPutDistance = calculateLegDistance(collarPutDelta, window.currentSymbol, 'collar');

                // Check if we have enough strikes available for put leg
                if (collarAtmIndex - collarPutDistance < 0) {
                    showAlert("Not enough strikes available for collar with the calculated distance (put leg).");
                    return;
                }

                // For protective put, go OTM (lower) based on calculated distance
                const putStrike = collarStrikes[collarAtmIndex - collarPutDistance];

                // Calculate the appropriate strike distance for call leg
                const collarCallDelta = collarStrikes[collarAtmIndex + 1] - collarStrikes[collarAtmIndex];
                const collarCallDistance = calculateLegDistance(collarCallDelta, window.currentSymbol, 'collar');

                // Check if we have enough strikes available for call leg
                if (collarAtmIndex + collarCallDistance >= collarStrikes.length) {
                    showAlert("Not enough strikes available for collar with the calculated distance (call leg).");
                    return;
                }

                // For covered call, go OTM (higher) based on calculated distance
                const callStrike = collarStrikes[collarAtmIndex + collarCallDistance];

                // Get option prices
                const collarPutPrice = getOptionPrice('PE', putStrike);
                const collarCallPrice = getOptionPrice('CE', callStrike);

                if (!collarPutPrice || !collarCallPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add stock position
                selectedLegs.push({
                    id: `FUT-buy-${modalCurrentExpiry}`,
                    type: 'FUT',
                    strike: null,
                    price: futurePrices[modalCurrentExpiry],
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add protective put leg
                selectedLegs.push({
                    id: `PE-${putStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: putStrike,
                    price: collarPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add covered call leg
                selectedLegs.push({
                    id: `CE-${callStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: callStrike,
                    price: collarCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'call-ratio-backspread':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const backspreadStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const backspreadAtmIndex = backspreadStrikes.indexOf(atmStrike);

                // Need at least 1 strike below ATM and 2 strikes above ATM for proper setup
                if (backspreadAtmIndex === -1 || backspreadAtmIndex <= 0 || backspreadAtmIndex >= backspreadStrikes.length - 2) {
                    showAlert("Unable to find appropriate strikes for call ratio backspread.");
                    return;
                }

                // Calculate the appropriate strike distance for sell leg (ITM)
                const backspreadSellDelta = backspreadStrikes[backspreadAtmIndex] - backspreadStrikes[backspreadAtmIndex - 1];
                const backspreadSellDistance = calculateLegDistance(backspreadSellDelta, window.currentSymbol, 'call-ratio-backspread');

                // Check if we have enough strikes available for sell leg
                if (backspreadAtmIndex - backspreadSellDistance < 0) {
                    showAlert("Not enough strikes available for call ratio backspread with the calculated distance (sell leg).");
                    return;
                }

                // Sell 1 ITM call (below ATM) based on calculated distance
                const backspreadSellStrike = backspreadStrikes[backspreadAtmIndex - backspreadSellDistance];

                // Calculate the appropriate strike distance for buy leg (OTM)
                const backspreadBuyDelta = backspreadStrikes[backspreadAtmIndex + 1] - backspreadStrikes[backspreadAtmIndex];
                const backspreadBuyDistance = calculateLegDistance(backspreadBuyDelta, window.currentSymbol, 'call-ratio-backspread');

                // Check if we have enough strikes available for buy leg
                if (backspreadAtmIndex + backspreadBuyDistance >= backspreadStrikes.length) {
                    showAlert("Not enough strikes available for call ratio backspread with the calculated distance (buy leg).");
                    return;
                }

                // Buy 2 OTM calls based on calculated distance
                const backspreadBuyStrike = backspreadStrikes[backspreadAtmIndex + backspreadBuyDistance];

                // Get option prices
                const backspreadSellPrice = getOptionPrice('CE', backspreadSellStrike);
                const backspreadBuyPrice = getOptionPrice('CE', backspreadBuyStrike);

                if (!backspreadSellPrice || !backspreadBuyPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Verify this will likely be a credit spread
                if (backspreadSellPrice < 2 * backspreadBuyPrice) {
                    showAlert("Warning: This call ratio backspread may not be a credit spread. Check option prices.");
                }

                // Add sell leg (1x ITM - lower strike)
                selectedLegs.push({
                    id: `CE-${backspreadSellStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: backspreadSellStrike,
                    price: backspreadSellPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add buy leg (2x OTM - higher strike) - using lots parameter
                selectedLegs.push({
                    id: `CE-${backspreadBuyStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: backspreadBuyStrike,
                    price: backspreadBuyPrice,
                    action: 'buy',
                    lots: 2,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'covered-short-straddle':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Check if we have future prices for this expiry
                if (!futurePrices || !futurePrices[modalCurrentExpiry]) {
                    showAlert(`Future prices not available for ${modalCurrentExpiry}. Cannot create covered-short-straddle strategy.`);
                    return;
                }

                // Get option prices for ATM strike
                const coveredStraddleCallPrice = getOptionPrice('CE', atmStrike);
                const coveredStraddlePutPrice = getOptionPrice('PE', atmStrike);

                if (!coveredStraddleCallPrice || !coveredStraddlePutPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add stock position
                selectedLegs.push({
                    id: `FUT-buy-${modalCurrentExpiry}`,
                    type: 'FUT',
                    strike: null,
                    price: futurePrices[modalCurrentExpiry],
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short call leg (covered by stock)
                selectedLegs.push({
                    id: `CE-${atmStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: coveredStraddleCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short put leg (naked)
                selectedLegs.push({
                    id: `PE-${atmStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: atmStrike,
                    price: coveredStraddlePutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'covered-short-strangle':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Check if we have future prices for this expiry
                if (!futurePrices || !futurePrices[modalCurrentExpiry]) {
                    showAlert(`Future prices not available for ${modalCurrentExpiry}. Cannot create covered-short-strangle strategy.`);
                    return;
                }

                // Get available strikes
                const strangleStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const strangleAtmIndex = strangleStrikes.indexOf(atmStrike);

                if (strangleAtmIndex === -1 || strangleAtmIndex < 1 || strangleAtmIndex >= strangleStrikes.length - 1) {
                    showAlert("Unable to find appropriate strikes for covered short strangle.");
                    return;
                }

                // Calculate the appropriate strike distance for put leg
                const stranglePutDelta = strangleStrikes[strangleAtmIndex] - strangleStrikes[strangleAtmIndex - 1];
                const stranglePutDistance = calculateLegDistance(stranglePutDelta, window.currentSymbol, 'covered-short-strangle');

                // Check if we have enough strikes available for put leg
                if (strangleAtmIndex - stranglePutDistance < 0) {
                    showAlert("Not enough strikes available for covered short strangle with the calculated distance (put leg).");
                    return;
                }

                // For OTM put, go below ATM based on calculated distance
                const stranglePutStrike = strangleStrikes[strangleAtmIndex - stranglePutDistance];

                // Calculate the appropriate strike distance for call leg
                const strangleCallDelta = strangleStrikes[strangleAtmIndex + 1] - strangleStrikes[strangleAtmIndex];
                const strangleCallDistance = calculateLegDistance(strangleCallDelta, window.currentSymbol, 'covered-short-strangle');

                // Check if we have enough strikes available for call leg
                if (strangleAtmIndex + strangleCallDistance >= strangleStrikes.length) {
                    showAlert("Not enough strikes available for covered short strangle with the calculated distance (call leg).");
                    return;
                }

                // For OTM call, go above ATM based on calculated distance
                const strangleCallStrike = strangleStrikes[strangleAtmIndex + strangleCallDistance];

                // Get option prices
                const strangleCallPrice = getOptionPrice('CE', strangleCallStrike);
                const stranglePutPrice = getOptionPrice('PE', stranglePutStrike);

                if (!strangleCallPrice || !stranglePutPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add stock position
                selectedLegs.push({
                    id: `FUT-buy-${modalCurrentExpiry}`,
                    type: 'FUT',
                    strike: null,
                    price: futurePrices[modalCurrentExpiry],
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short OTM call leg (covered by stock)
                selectedLegs.push({
                    id: `CE-${strangleCallStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: strangleCallStrike,
                    price: strangleCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short OTM put leg
                selectedLegs.push({
                    id: `PE-${stranglePutStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: stranglePutStrike,
                    price: stranglePutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'put-broken-wing-butterfly':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const bwStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const bwAtmIndex = bwStrikes.indexOf(atmStrike);

                // Need at least 3 strikes below ATM for this strategy
                if (bwAtmIndex === -1 || bwAtmIndex < 3) {
                    showAlert("Unable to find appropriate strikes for put broken wing butterfly strategy.");
                    return;
                }

                // Buy highest strike put (ATM or slightly OTM)
                const bwHighStrike = atmStrike;

                // Calculate the appropriate strike distance for middle leg
                const bwMidDelta = bwStrikes[bwAtmIndex] - bwStrikes[bwAtmIndex - 1];
                const bwMidDistance = calculateLegDistance(bwMidDelta, window.currentSymbol, 'put-broken-wing-butterfly');

                // Check if we have enough strikes available for middle leg
                if (bwAtmIndex - bwMidDistance < 0) {
                    showAlert("Not enough strikes available for put broken wing butterfly with the calculated distance (middle leg).");
                    return;
                }

                // Sell 2 puts at middle strike based on calculated distance
                const bwMidStrike = bwStrikes[bwAtmIndex - bwMidDistance];

                // Calculate the appropriate strike distance for lower leg (from middle strike)
                const bwLowDelta = bwStrikes[bwAtmIndex - bwMidDistance] - bwStrikes[bwAtmIndex - bwMidDistance - 1];
                const bwLowDistance = calculateLegDistance(bwLowDelta, window.currentSymbol, 'put-broken-wing-butterfly');

                // Check if we have enough strikes available for lower leg
                if (bwAtmIndex - bwMidDistance - bwLowDistance < 0) {
                    showAlert("Not enough strikes available for put broken wing butterfly with the calculated distance (lower leg).");
                    return;
                }

                // Buy lowest strike put based on calculated distance
                const bwLowStrike = bwStrikes[bwAtmIndex - bwMidDistance - bwLowDistance];

                // Get option prices
                const bwHighPrice = getOptionPrice('PE', bwHighStrike);
                const bwMidPrice = getOptionPrice('PE', bwMidStrike);
                const bwLowPrice = getOptionPrice('PE', bwLowStrike);

                if (!bwHighPrice || !bwMidPrice || !bwLowPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add highest strike long put
                selectedLegs.push({
                    id: `PE-${bwHighStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: bwHighStrike,
                    price: bwHighPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add middle strike short puts (2x) - using lots parameter instead of creating duplicate legs
                selectedLegs.push({
                    id: `PE-${bwMidStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: bwMidStrike,
                    price: bwMidPrice,
                    action: 'sell',
                    lots: 2,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add lowest strike long put
                selectedLegs.push({
                    id: `PE-${bwLowStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: bwLowStrike,
                    price: bwLowPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'inverse-call-broken-wing-butterfly':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const ibwStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const ibwAtmIndex = ibwStrikes.indexOf(atmStrike);

                // Need at least 3 strikes above ATM for this strategy
                if (ibwAtmIndex === -1 || ibwAtmIndex >= ibwStrikes.length - 3) {
                    showAlert("Unable to find appropriate strikes for inverse call broken wing butterfly strategy.");
                    return;
                }

                // Buy lowest strike call (ATM or slightly OTM)
                const ibwLowStrike = atmStrike;

                // Calculate the appropriate strike distance for middle leg
                const ibwMidDelta = ibwStrikes[ibwAtmIndex + 1] - ibwStrikes[ibwAtmIndex];
                const ibwMidDistance = calculateLegDistance(ibwMidDelta, window.currentSymbol, 'inverse-call-broken-wing-butterfly');

                // Check if we have enough strikes available for middle leg
                if (ibwAtmIndex + ibwMidDistance >= ibwStrikes.length) {
                    showAlert("Not enough strikes available for inverse call broken wing butterfly with the calculated distance (middle leg).");
                    return;
                }

                // Sell 2 calls at middle strike based on calculated distance
                const ibwMidStrike = ibwStrikes[ibwAtmIndex + ibwMidDistance];

                // Calculate the appropriate strike distance for higher leg (from middle strike)
                const ibwHighDelta = ibwStrikes[ibwAtmIndex + ibwMidDistance + 1] - ibwStrikes[ibwAtmIndex + ibwMidDistance];
                const ibwHighDistance = calculateLegDistance(ibwHighDelta, window.currentSymbol, 'inverse-call-broken-wing-butterfly');

                // Check if we have enough strikes available for higher leg
                if (ibwAtmIndex + ibwMidDistance + ibwHighDistance >= ibwStrikes.length) {
                    showAlert("Not enough strikes available for inverse call broken wing butterfly with the calculated distance (higher leg).");
                    return;
                }

                // Buy highest strike call based on calculated distance
                const ibwHighStrike = ibwStrikes[ibwAtmIndex + ibwMidDistance + ibwHighDistance];

                // Get option prices
                const ibwLowPrice = getOptionPrice('CE', ibwLowStrike);
                const ibwMidPrice = getOptionPrice('CE', ibwMidStrike);
                const ibwHighPrice = getOptionPrice('CE', ibwHighStrike);

                if (!ibwLowPrice || !ibwMidPrice || !ibwHighPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add lowest strike long call
                selectedLegs.push({
                    id: `CE-${ibwLowStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: ibwLowStrike,
                    price: ibwLowPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add middle strike short calls (2x) - using lots parameter instead of creating duplicate legs
                selectedLegs.push({
                    id: `CE-${ibwMidStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: ibwMidStrike,
                    price: ibwMidPrice,
                    action: 'sell',
                    lots: 2,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add highest strike long call
                selectedLegs.push({
                    id: `CE-${ibwHighStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: ibwHighStrike,
                    price: ibwHighPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'jade-lizard':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const lizardStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const lizardAtmIndex = lizardStrikes.indexOf(atmStrike);

                // Need at least 2 strikes above ATM for the call spread and 1 strike below for the put
                if (lizardAtmIndex === -1 || lizardAtmIndex < 1 || lizardAtmIndex >= lizardStrikes.length - 2) {
                    showAlert("Unable to find appropriate strikes for jade lizard strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for put leg
                const lizardPutDelta = lizardStrikes[lizardAtmIndex] - lizardStrikes[lizardAtmIndex - 1];
                const lizardPutDistance = calculateLegDistance(lizardPutDelta, window.currentSymbol, 'jade-lizard');

                // Check if we have enough strikes available for put leg
                if (lizardAtmIndex - lizardPutDistance < 0) {
                    showAlert("Not enough strikes available for jade lizard with the calculated distance (put leg).");
                    return;
                }

                // For short OTM put, go below ATM based on calculated distance
                const lizardPutStrike = lizardStrikes[lizardAtmIndex - lizardPutDistance];

                // Calculate the appropriate strike distance for short call leg
                const lizardShortCallDelta = lizardStrikes[lizardAtmIndex + 1] - lizardStrikes[lizardAtmIndex];
                const lizardShortCallDistance = calculateLegDistance(lizardShortCallDelta, window.currentSymbol, 'jade-lizard');

                // Check if we have enough strikes available for short call leg
                if (lizardAtmIndex + lizardShortCallDistance >= lizardStrikes.length) {
                    showAlert("Not enough strikes available for jade lizard with the calculated distance (short call leg).");
                    return;
                }

                // For bear call spread: short call above ATM based on calculated distance
                const lizardShortCallStrike = lizardStrikes[lizardAtmIndex + lizardShortCallDistance];

                // Calculate the appropriate strike distance for long call leg (from short call)
                const lizardLongCallDelta = lizardStrikes[lizardAtmIndex + lizardShortCallDistance + 1] - lizardStrikes[lizardAtmIndex + lizardShortCallDistance];
                const lizardLongCallDistance = calculateLegDistance(lizardLongCallDelta, window.currentSymbol, 'jade-lizard');

                // Check if we have enough strikes available for long call leg
                if (lizardAtmIndex + lizardShortCallDistance + lizardLongCallDistance >= lizardStrikes.length) {
                    showAlert("Not enough strikes available for jade lizard with the calculated distance (long call leg).");
                    return;
                }

                // For bear call spread: long call further above short call based on calculated distance
                const lizardLongCallStrike = lizardStrikes[lizardAtmIndex + lizardShortCallDistance + lizardLongCallDistance];

                // A proper Jade Lizard should have credit exceeding the width of call spread to eliminate upside risk
                // (This can be verified but is not enforced here)

                // Get option prices
                const lizardPutPrice = getOptionPrice('PE', lizardPutStrike);
                const lizardShortCallPrice = getOptionPrice('CE', lizardShortCallStrike);
                const lizardLongCallPrice = getOptionPrice('CE', lizardLongCallStrike);

                if (!lizardPutPrice || !lizardShortCallPrice || !lizardLongCallPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add short OTM put leg
                selectedLegs.push({
                    id: `PE-${lizardPutStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: lizardPutStrike,
                    price: lizardPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short OTM call leg
                selectedLegs.push({
                    id: `CE-${lizardShortCallStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: lizardShortCallStrike,
                    price: lizardShortCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long further OTM call leg
                selectedLegs.push({
                    id: `CE-${lizardLongCallStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: lizardLongCallStrike,
                    price: lizardLongCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'strap':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get option prices for ATM strike
                const strapCallPrice = getOptionPrice('CE', atmStrike);
                const strapPutPrice = getOptionPrice('PE', atmStrike);

                if (!strapCallPrice || !strapPutPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add call legs (2x) - using lots parameter instead of creating duplicate legs
                selectedLegs.push({
                    id: `CE-${atmStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: strapCallPrice,
                    action: 'buy',
                    lots: 2,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add ATM put leg
                selectedLegs.push({
                    id: `PE-${atmStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: atmStrike,
                    price: strapPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'call-ratio-spread':
                // Find ATM strike for the buy leg
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get all available strikes
                const ratioStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const atmRatioIndex = ratioStrikes.indexOf(atmStrike);

                if (atmRatioIndex === -1 || atmRatioIndex >= ratioStrikes.length - 1) {
                    showAlert("Not enough strikes available for call ratio spread.");
                    return;
                }

                // Buy leg at ATM
                const buyRatioStrike = atmStrike;

                // Calculate the appropriate strike distance for sell leg
                const sellRatioDelta = ratioStrikes[atmRatioIndex + 1] - ratioStrikes[atmRatioIndex];
                const sellRatioDistance = calculateLegDistance(sellRatioDelta, window.currentSymbol, 'call-ratio-spread');

                // Check if we have enough strikes available for sell leg
                if (atmRatioIndex + sellRatioDistance >= ratioStrikes.length) {
                    showAlert("Not enough strikes available for call ratio spread with the calculated distance.");
                    return;
                }

                // Sell leg at higher strike based on calculated distance
                const sellRatioStrike = ratioStrikes[atmRatioIndex + sellRatioDistance];

                // Get option prices
                const buyRatioPrice = getOptionPrice('CE', buyRatioStrike);
                const sellRatioPrice = getOptionPrice('CE', sellRatioStrike);

                if (!buyRatioPrice || !sellRatioPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add buy leg (1x)
                selectedLegs.push({
                    id: `CE-${buyRatioStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: buyRatioStrike,
                    price: buyRatioPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add sell legs (2x) - using lots parameter instead of creating duplicate legs
                selectedLegs.push({
                    id: `CE-${sellRatioStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: sellRatioStrike,
                    price: sellRatioPrice,
                    action: 'sell',
                    lots: 2,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'long-synthetic-future':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get option prices for ATM strike
                const synthCallPrice = getOptionPrice('CE', atmStrike);
                const synthPutPrice = getOptionPrice('PE', atmStrike);

                if (!synthCallPrice || !synthPutPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add long ATM call leg
                selectedLegs.push({
                    id: `CE-${atmStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: synthCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short ATM put leg
                selectedLegs.push({
                    id: `PE-${atmStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: atmStrike,
                    price: synthPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'long-combo':
                // Find ATM strike
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const comboStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const comboAtmIndex = comboStrikes.indexOf(atmStrike);

                // Need at least 2 strikes on each side of ATM
                if (comboAtmIndex === -1 || comboAtmIndex < 2 || comboAtmIndex >= comboStrikes.length - 2) {
                    showAlert("Unable to find appropriate strikes for long combo strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for put leg
                const comboPutDelta = comboStrikes[comboAtmIndex] - comboStrikes[comboAtmIndex - 1];
                const comboPutDistance = calculateLegDistance(comboPutDelta, window.currentSymbol, 'long-combo');

                // Check if we have enough strikes available for put leg
                if (comboAtmIndex - comboPutDistance < 0) {
                    showAlert("Not enough strikes available for long combo with the calculated distance (put leg).");
                    return;
                }

                // For short put, go below ATM based on calculated distance (wider neutral zone)
                const comboPutStrike = comboStrikes[comboAtmIndex - comboPutDistance];

                // Calculate the appropriate strike distance for call leg
                const comboCallDelta = comboStrikes[comboAtmIndex + 1] - comboStrikes[comboAtmIndex];
                const comboCallDistance = calculateLegDistance(comboCallDelta, window.currentSymbol, 'long-combo');

                // Check if we have enough strikes available for call leg
                if (comboAtmIndex + comboCallDistance >= comboStrikes.length) {
                    showAlert("Not enough strikes available for long combo with the calculated distance (call leg).");
                    return;
                }

                // For long call, go above ATM based on calculated distance
                const comboCallStrike = comboStrikes[comboAtmIndex + comboCallDistance];

                // Get option prices
                const comboCallPrice = getOptionPrice('CE', comboCallStrike);
                const comboPutPrice = getOptionPrice('PE', comboPutStrike);

                if (!comboCallPrice || !comboPutPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add long OTM call leg
                selectedLegs.push({
                    id: `CE-${comboCallStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: comboCallStrike,
                    price: comboCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short OTM put leg
                selectedLegs.push({
                    id: `PE-${comboPutStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: comboPutStrike,
                    price: comboPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;
        }
    }
</script>