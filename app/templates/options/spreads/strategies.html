<div id="strategies-container-wrapper" class="relative">
    <button class="mobile-toggle-btn font-medium text-base cursor-pointer lg:hidden w-full text-blue-800 p-2 text-left"
        onclick="toggleSlideout()" style="background-image: linear-gradient(45deg, #d0f1ff, #eff1f3);">
        <div>
            <p>Strategies</p>
            <p class="text-xs mt-2">Please select a stock to choose any strategy</p>
        </div>
    </button>
    <div id="strategies-container" class="2xl:mb-0 bg-white mb-3 p-5 rounded-lg tabs-container">
        <div id="strategy-message-container" class="fixed left-[52%] h-full inset-0 z-30 hidden">
            <div class="h-screen  items-center justify-center hidden xl:flex">
                <div class="bg-white">
                    <div id="strategy-message"
                        class="bg-white border border-gray-200 p-3 rounded-md shadow-lg text-center border-blue-600">
                        <p class="font-medium text-base">Please select a stock to choose any strategy</p>
                        <p class="text-xs text-gray-500 mt-1">Once you select a stock, you can click on any strategy to
                            load
                            it
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex border-b gap-6 mb-4">
            <button class="tab-btn py-2 font-medium text-sm text-blue-600 border-b border-blue-600"
                onclick="showTab('bullish', this)">Bullish</button>
            <button class="tab-btn py-2 text-gray-500 text-sm font-medium border-b border-transparent"
                onclick="showTab('bearish', this)">Bearish</button>
            <button class="tab-btn py-2 text-gray-500 text-sm font-medium border-b border-transparent"
                onclick="showTab('neutral', this)">Neutral</button>
        </div>

        <div class="max-h-[calc(100vh_-_220px)] lg:max-h-max overflow-auto">
            <!-- Bullish Strategies -->
            <div id="bullish" class="tab-content">
                <div class="grid 2xl:grid-cols-3 xl:grid-cols-2 gap-2 md:grid-cols-3 grid-col-1">
                    {% include 'options/spreads/bullish-strategies.html' %}
                </div>
            </div>

            <!-- Bearish Strategies -->
            <div id="bearish" class="tab-content hidden">
                <div class="grid 2xl:grid-cols-3 xl:grid-cols-2 gap-2 md:grid-cols-3 grid-col-1">
                    {% include 'options/spreads/bearish-strategies.html' %}
                </div>
            </div>

            <!-- Neutral Strategies -->
            <div id="neutral" class="tab-content hidden">
                <div class="grid 2xl:grid-cols-3 xl:grid-cols-2 gap-2 md:grid-cols-3 grid-col-1">
                    {% include 'options/spreads/neutral-strategies.html' %}
                </div>
            </div>

        </div>
        <!-- Apply Button (Inside slide-out) -->
        <div>
            <button
                class="apply-btn lg:hidden block bg-blue-500 hover:bg-blue-600 p-2 text-center text-white w-full rounded-md mt-3 font-medium text-sm"
                onclick="applyAndClose()">Apply Strategie</button>
        </div>
    </div>
</div>


<style>
    /* Default (Desktop view) */
    #strategies-container-wrapper {
        position: relative;
    }

    #strategies-container {
        display: block;
    }

    /* Slide-out effect (Mobile view) */
    @media screen and (max-width: 768px) {
        #strategies-container {
            position: fixed;
            top: 0;
            left: -100%;
            z-index: 30;
            width: 100%;
            height: 100%;
            background-color: white;
            transition: left 0.3s ease-in-out;
        }

        #strategies-container.open {
            left: 0;
        }
    }
</style>
<script>
    /********************************************************************
     * STRATEGY MANAGEMENT
     ********************************************************************/

    function toggleSlideout() {
        const strategiesContainer = document.getElementById('strategies-container');
        strategiesContainer.classList.toggle('!left-0');
    }

    function applyAndClose() {
        // You can perform additional actions for the "Apply" button here.

        // Close the slideout menu after applying
        toggleSlideout();
    }

    function findAtmStrike() {
        if (!chainData.options.length || !currentSpotPrice) return null;

        // Filter options for current expiry
        const currentExpiryOptions = chainData.options.filter(opt =>
            opt.expiryDate === modalCurrentExpiry
        );

        if (!currentExpiryOptions.length) return null;

        // Find closest strike to current spot price
        return currentExpiryOptions.reduce((closest, curr) => {
            const currDiff = Math.abs(curr.strikePrice - currentSpotPrice);
            const closestDiff = Math.abs(closest.strikePrice - currentSpotPrice);
            return currDiff < closestDiff ? curr : closest;
        }).strikePrice;
    }

    function getOptionPrice(type, strike, expiry = modalCurrentExpiry) {
        if (!chainData.options.length) return null;

        const option = chainData.options.find(opt =>
            opt.expiryDate === expiry &&
            opt.strikePrice === strike
        );

        if (!option) return null;

        return type === 'CE' ? option.CE?.lastPrice : option.PE?.lastPrice;
    }

    function getAvailableExpiries() {
        if (!chainData.options.length) return [];

        // Get unique expiry dates
        const expiries = [...new Set(chainData.options.map(opt => opt.expiryDate))];

        // Sort expiries chronologically
        return expiries.sort((a, b) => new Date(a) - new Date(b));
    }

    function showTab(tabId, btn) {
        document.querySelectorAll('.tab-content').forEach(tab => tab.classList.add('hidden'));
        document.getElementById(tabId).classList.remove('hidden');
        document.querySelectorAll('.tab-btn').forEach(button => {
            button.classList.remove('text-blue-600', 'border-blue-600');
            button.classList.add('text-gray-500', 'border-transparent');
        });
        btn.classList.add('text-blue-600', 'border-blue-600');
        btn.classList.remove('text-gray-500', 'border-transparent');
    }


    // Handle strategy cards state
    function updateStrategyCards() {
        const strategyMessageContainer = document.getElementById('strategy-message-container');
        const strategyCards = document.querySelectorAll('.strategy-card');
        const messageDiv = document.getElementById('strategy-message');

        if (!window.currentSymbol) {
            strategyMessageContainer.classList.remove('hidden');
            messageDiv.classList.remove('bg-blue-50', 'border-blue-200');
            messageDiv.classList.add('bg-white', 'border-gray-200');
            messageDiv.classList.remove("hidden");

            // Disable all strategy cards
            strategyCards.forEach(card => {
                card.classList.add('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
                card.style.userSelect = 'none';
            });
        } else {
            strategyMessageContainer.classList.add('hidden');
            messageDiv.classList.remove('bg-white', 'border-gray-200');
            messageDiv.classList.add("hidden");


            // Enable all strategy cards
            strategyCards.forEach(card => {
                card.classList.remove('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
                card.style.userSelect = '';
            });
        }
    }

    // Function to calculate the difference between legs based on strategy
    function calculateLegDistance(strikeDelta, symbol, strategyType) {
        // This function determines the distance between option legs for different strategies
        // For now, we're keeping the distance at 1 as per the requirement
        // In the future, this will be updated with more sophisticated logic
        strikeDelta = Math.abs(strikeDelta);
        //Check if the symbol is a stock or index
        if (indexTickers.includes(symbol)) {
            if (strikeDelta < 50) {
                return 2;
            }
            else if (strikeDelta < 100) {
                return 4;
            }
            else {
                return 5;
            }
        }
        else {
            console.log('symbol is stock', symbol, 'and strikeDelta is', strikeDelta);
            return 2;
        }
    }
</script>