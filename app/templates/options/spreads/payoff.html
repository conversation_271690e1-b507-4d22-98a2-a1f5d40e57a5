<!-- options/spreads/payoff.html -->
<div id="payoff-container" class="hidden"></div>

<!-- Modal for Bulk Backtest -->
<div id="backtestModal" class="fixed inset-0 flex items-center justify-center z-50"
    style="display:none; background-color: rgba(0,0,0,0.5);">
    <!-- Fixed-height modal with internal scrolling -->
    <div
        class="bg-white rounded-lg shadow-lg p-6 max-w-4xl w-full min-h-[400px] max-h-[calc(100vh_-_6rem)] overflow-auto">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">Bulk Backtest</h2>
            <button id="modalCloseBtn"
                class="dark:hover:text-neutral-100 dark:text-neutral-300 flex hover:text-neutral-800 text-neutral-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-x">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                </svg>
            </button>
        </div>
        <!-- Top section: Compact row for date pickers and interval buttons -->
        <form id="backtestForm" class="bg-blue-50 flex flex-wrap gap-4 items-end p-3 rounded-lg">
            <div class="flex flex-col">
                <label for="startDate" class="block font-medium mb-2 text-sm">Start</label>
                <input type="date" id="startDate" name="startDate" required
                    class="w-full px-3 text-sm py-1.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
            </div>
            <div class="flex flex-col">
                <label for="expirationDate" class="block font-medium mb-2 text-sm">Expiration</label>
                <input type="date" id="expirationDate" name="expirationDate" readonly
                    class="w-full px-3 text-sm py-1.5 bg-red-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
            </div>
            <div>
                <span class="block font-medium mb-2 text-sm">Interval</span>
                <div class="flex items-center gap-2">
                    <button type="button" class="intervalBtn px-3 py-1 border border-gray-300 rounded"
                        data-interval="1m">1m</button>
                    <button type="button" class="intervalBtn px-3 py-1 border  border-gray-300 rounded"
                        data-interval="15m">15m</button>
                    <button type="button"
                        class="intervalBtn px-3 py-1 border  border-gray-300 rounded bg-blue-500 text-white"
                        data-interval="1d">1d</button>
                </div>
            </div>
            <div class="flex-grow text-right">
                <button type="button" id="goBacktest"
                    class="text-white bg-gradient-to-br from-purple-600 to-blue-500 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg px-5 py-2.5 text-center w-1/2">Go</button>
            </div>
        </form>

        <!-- Help text section -->
        <div id="helpText" class="mt-4 p-4 bg-lime-50/50 rounded">
            <p class="text-sm text-gray-700">
                Based on your selected start date, expiration date, and interval, AIBull will retrieve historical OHLC
                data and calculate the corresponding profit/loss (Spot P & L) for each timestamp. The results below
                will
                display the progression of Spot P & Lalong with the historical Spot values.
            </p>
            <p class="mt-5 text-gray-700 font-semibold mb-3">
                FAQs
            </p>
            <div class="list-disc list-inside text-sm text-gray-700">
                <p class="mb-3">
                    <strong class="text-red-500">Q :</strong> How accurate is the backtest?
                </p>
                <p class="mb-5">
                    <strong class="text-green-600">A :</strong>
                    The backtest uses
                    historical data and our proprietary calculations. Actual results may vary.
                </p>
                <p class="mb-3">
                    <strong class="text-red-500">Q :</strong> What does the interval mean?
                </p>
                <p>
                    <strong class="text-green-600">A :</strong> The interval
                    specifies the frequency of data points (e.g., 1d for daily data).
                </p>
            </div>
        </div>

        <!-- Loading indicator -->
        <div id="backtestLoading" class="mt-4 text-center text-gray-600" style="display:none;">
            Loading backtest data...
        </div>

        <!-- Bottom section: Bulk Backtest Results (stacked layout) -->
        <div id="bulkBacktestResults" class="mt-4"></div>
    </div>
</div>

<!-- Optional container for bulk backtest results outside modal -->
<div id="bulk-backtest-container" class="mt-4"></div>

<script>
    // Default initialization for the payoff container
    function initializePayoff() {
        const container = document.getElementById("payoff-container");
        window.PayoffUtils.renderPayoffUI(container);
    }

    // Update main payoff view
    function updatePayoff() {
        const container = document.getElementById("payoff-container");
        container.classList.remove('hidden');

        window.PayoffUtils.renderPayoffUI(container, {
            legs: window.selectedLegs,
            symbol: window.currentSymbol,
            spotPrice: window.currentSpotPrice,
            chainData: window.chainData?.options,
            multiplier: window.currentMultiplier,
            backtestDate: window.backtestDate,
            chartId: 'payoffChart',
            showShare: true,  // Enable share button for payoff.html
            onBacktest: handleBacktestClick,
            expiryDates: window.chainData?.expiryDates || []
        });
    }

    // Handle the "backtest" button click event
    function handleBacktestClick() {
        document.getElementById("startDate").value = window.backtestDate || "";
        document.getElementById("expirationDate").value = getMostRecentExpirationDate();
        document.getElementById("backtestModal").style.display = "flex";
    }

    // Get most recent expiration date from selected legs
    function getMostRecentExpirationDate() {
        if (!window.selectedLegs || !window.selectedLegs.length) return "";
        const expirations = window.selectedLegs.map(l => new Date(l.expiryDate));
        const mostRecent = new Date(Math.max.apply(null, expirations));
        const yyyy = mostRecent.getFullYear();
        const mm = String(mostRecent.getMonth() + 1).padStart(2, "0");
        const dd = String(mostRecent.getDate()).padStart(2, "0");
        return `${yyyy}-${mm}-${dd}`;
    }

    // Helper: Format timestamp
    // If the timestamp is already in "dd-MMM-yyyy" format, return it directly.
    function formatTimestamp(ts) {
        if (typeof ts === "string" && ts.includes("-")) {
            const parts = ts.split("-");
            if (parts.length === 3) return ts;
        }
        const epoch = parseInt(ts, 10);
        const date = epoch.toString().length <= 10 ? new Date(epoch * 1000) : new Date(epoch);
        const day = String(date.getDate()).padStart(2, "0");
        const monthNames = [
            "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        ];
        const month = monthNames[date.getMonth()].substring(0, 3);
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    }

    // Render bulk backtest results with a stacked layout.
    // The table now includes an extra "Details" column with a "+" button.
    // On click, the details row is toggled to display the legs information in the table.
    function renderBulkBacktestResults(bulkData) {
        const container = document.getElementById("bulkBacktestResults");
        if (!bulkData || !bulkData.results || !bulkData.results.length) {
            container.innerHTML = '<p class="text-center text-gray-500">No bulk data available.</p>';
            return;
        }

        let tableRows = '';
        const timestamps = [];
        const spotPLs = [];
        const spotValues = [];

        bulkData.results.forEach((result, index) => {
            const formattedTs = formatTimestamp(result.Timestamp);
            timestamps.push(formattedTs);
            spotPLs.push(result["Profit/Loss"]);
            spotValues.push(parseFloat(result["Spot"]).toFixed(2));

            // Build a details row with leg information
            let detailsHTML = `
                <table class="min-w-full text-xs">
                    <thead class="bg-gray-100 text-left">
                        <tr>
                            <th class="px-2 py-1 border font-medium">Strike</th>
                            <th class="px-2 py-1 border font-medium">Type</th>
                            <th class="px-2 py-1 border font-medium">Tr_Type</th>
                            <th class="px-2 py-1 border font-medium">Price</th>
                            <th class="px-2 py-1 border font-medium">LTP</th>
                            <th class="px-2 py-1 border font-medium">Lots</th>
                            <th class="px-2 py-1 border font-medium">P&L</th>
                        </tr>
                    </thead>
                    <tbody>`;

            result.legs.forEach(leg => {
                detailsHTML += `
                    <tr class="border-b text-left bg-white">
                        <td class="px-2 py-1 border">${leg.strike}</td>
                        <td class="px-2 py-1 border">${leg.type}</td>
                        <td class="px-2 py-1 border">${leg.tr_type}</td>
                        <td class="px-2 py-1 border">${leg.price}</td>
                        <td class="px-2 py-1 border">${leg.ltp}</td>
                        <td class="px-2 py-1 border">${leg.lots || 1}</td>
                        <td class="px-2 py-1 border">${leg.profit_loss * (leg.lots || 1)}</td>
                    </tr>`;
            });
            detailsHTML += '</tbody></table>';

            tableRows += `
                <tr class="hover:bg-gray-50">
                    <td class="px-4 py-2 border">
                        ${formattedTs} <button class="expand-btn px-2 py-0.5 border rounded text-sm" data-index="${index}">+</button>
                    </td>
                    <td class="px-4 py-2 border">${result["Profit/Loss"].toFixed(2)}</td>
                    <td class="px-4 py-2 border">${parseFloat(result["Spot"]).toFixed(2)}</td>
                </tr>
                <tr class="detail-row" id="detail-row-${index}" style="display:none;">
                    <td class="px-3 py-2 border bg-blue-50" colspan="3">
                        ${detailsHTML}
                    </td>
                </tr>`;
        });

        container.innerHTML = `
            <div class="bg-gray-50 rounded-md p-4 mt-4">
                <div class="mb-4">
                    <h3 class="font-semibold mb-2">Results</h3>
                    <div class="overflow-y-auto max-h-64 custom-scroll">
                        <table class="min-w-full text-xs">
                            <thead>
                                <tr class="bg-gray-100 text-left">
                                    <th class="px-4 py-2 border font-medium">Timestamp</th>
                                    <th class="px-4 py-2 border font-medium">Spot P&L</th>
                                    <th class="px-4 py-2 border font-medium">Spot</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div>
                    <canvas id="bulkChart" style="min-height:300px;"></canvas>
                </div>
            </div>`;

        // Add event listeners to toggle detail rows
        document.querySelectorAll(".expand-btn").forEach(btn => {
            btn.addEventListener("click", function () {
                const idx = this.getAttribute("data-index");
                const detailRow = document.getElementById("detail-row-" + idx);
                if (detailRow.style.display === "none") {
                    detailRow.style.display = "table-row";
                    this.textContent = "-";
                } else {
                    detailRow.style.display = "none";
                    this.textContent = "+";
                }
            });
        });

        const ctx = document.getElementById("bulkChart").getContext("2d");
        if (window.bulkChartInstance) {
            window.bulkChartInstance.destroy();
        }
        window.bulkChartInstance = new Chart(ctx, {
            type: "line",
            data: {
                labels: timestamps,
                datasets: [
                    {
                        label: "Spot P&L",
                        data: spotPLs,
                        pointRadius: 3,
                        borderWidth: 2,
                        segment: {
                            borderColor: ctx => ctx.p0.parsed.y >= 0 ? "rgb(34, 197, 94)" : "rgb(239, 68, 68)"
                        },
                        fill: false,
                        yAxisID: 'y'
                    },
                    {
                        label: "Spot Price",
                        data: spotValues,
                        pointRadius: 3,
                        borderWidth: 2,
                        borderColor: "rgb(59, 130, 246)",
                        fill: false,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                scales: {
                    x: {
                        title: { display: true, text: "Timestamp" },
                        grid: {
                            display: true,
                            color: "rgba(0, 0, 0, 0.1)"
                        }
                    },
                    y: {
                        type: "linear",
                        position: "left",
                        title: { display: true, text: "Profit/Loss" },
                        grid: {
                            display: true,
                            color: "rgba(0, 0, 0, 0.1)"
                        }
                    },
                    y1: {
                        type: "linear",
                        position: "right",
                        title: { display: true, text: "Spot Price" },
                        grid: { drawOnChartArea: false }
                    }
                },
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    },
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }

    // Update help text function
    function updateHelpText(message) {
        document.getElementById("helpText").innerHTML = `
            <p class="text-sm text-gray-700">${message}</p>`;
    }

    let currentInterval = "1d";

    document.addEventListener("DOMContentLoaded", function () {
        initializePayoff();

        // Close modal handler
        document.getElementById("modalCloseBtn").addEventListener("click", function () {
            document.getElementById("backtestModal").style.display = "none";
        });

        // Interval picker buttons: use gray for selection
        document.querySelectorAll(".intervalBtn").forEach(btn => {
            btn.addEventListener("click", function () {
                currentInterval = this.getAttribute("data-interval");
                document.querySelectorAll(".intervalBtn").forEach(b => {
                    b.classList.remove("bg-blue-500", "text-white");
                    b.classList.add("border");
                });
                this.classList.add("bg-blue-500", "text-white");
                this.classList.remove("border");
            });
        });

        // Handle "Go" button click
        document.getElementById("goBacktest").addEventListener("click", function () {
            const startDate = document.getElementById("startDate").value;
            const expirationDate = document.getElementById("expirationDate").value;
            const interval = currentInterval;

            updateHelpText('AIBull is calculating the profit/loss based on your selected parameters. Please wait...');
            document.getElementById("backtestLoading").style.display = "block";

            // Abort any ongoing calculation
            if (window.bulkPayoffAbortController) {
                window.bulkPayoffAbortController.abort();
            }

            // Create a new abort controller
            window.bulkPayoffAbortController = new AbortController();

            window.PayoffUtils.calculatePayoffDataBulk(
                window.selectedLegs,
                window.currentSymbol,
                startDate,
                expirationDate,
                interval,
                window.currentMultiplier,
                window.bulkPayoffAbortController
            ).then(bulkData => {
                document.getElementById("backtestLoading").style.display = "none";

                if (bulkData?.aborted) {
                    // Request was aborted, do nothing
                    return;
                }

                if (bulkData?.error) {
                    console.error("Bulk Backtest error:", bulkData.error);
                    updateHelpText('Error calculating backtest results. Please try again.');
                    return;
                }
                updateHelpText('Calculation complete. See the results below.');
                renderBulkBacktestResults(bulkData);
            }).catch(error => {
                document.getElementById("backtestLoading").style.display = "none";

                // Don't show error message if it was just aborted
                if (error.name === 'AbortError') {
                    console.log('Bulk payoff calculation aborted');
                    return;
                }

                console.error("Backtest calculation error:", error);
                updateHelpText('An error occurred while calculating backtest results. Please try again.');
            });
        });
    });
</script>