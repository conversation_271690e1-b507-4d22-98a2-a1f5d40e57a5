<!DOCTYPE html>
<html lang="en">

<head>
    <title>Spread Analysis - Optimize Trades with Bull & Bear Strategies | AI Bull</title>
    <meta name="description"
        content="Analyze Bull and Bear spreads to optimize your trades. Compare premiums, risks, and rewards across multiple strike prices to make informed decisions with AIBull's advanced spread analysis tools." />
    <meta name="keywords"
        content="bull spreads, bear spreads, spread trading, options trading, spread analysis, spread generally, trading strategies, AIBull, premiums, strike prices" />
    <link rel="canonical" href="https://theaibull.com/options/spreads" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/options/spreads" />
    <meta property="og:title" content="Spread Analysis - Optimize Trades with Bull & Bear Strategies" />
    <meta property="og:description"
        content="Analyze Bull and Bear spreads to optimize your trades. Compare premiums, risks, and rewards across multiple strike prices to make informed decisions with AIBull's advanced spread analysis tools." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@aibull" />
    <meta name="twitter:title" content="Spread Analysis - Optimize Trades with Bull & Bear Strategies" />
    <meta name="twitter:description"
        content="Maximize your trading strategy by analyzing Bull and Bear spreads with AIBull's advanced tools, comparing premiums, risks, and rewards." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />

    {% include 'blocks/head.html' %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.css">
</head>

<body class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 pb-[65px] xl:pb-0">
    {% include 'blocks/common.html' %}
    {% include 'blocks/onboarding.html' %}
    {% include 'blocks/lot-sizes.html' %}
    {% include 'blocks/stock-symbols.html' %}

    {% include 'blocks/timestamp.html' %}

    <script>
        window.clearSpreadState = function () {
            window.currentSpotPrice = 0;
            window.currentSymbol = "";
            window.chainData = { expiryDates: [], options: [] };
            window.futurePrices = {};
            window.selectedLegs = [];
            window.currentMultiplier = 1;
            window.modalCurrentExpiry = null;
        }

        window.clearSpreadState();

        document.addEventListener("DOMContentLoaded", function () {
            document.addEventListener("stockSearchTriggered", (e) => {
                const symbol = e.detail.symbol;
                if (!symbol) {
                    showAlert("Please enter a symbol");
                    return;
                }

                // Clear previous state
                window.clearSpreadState();
                window.currentSymbol = symbol;

                // Update UI to reflect cleared state
                updateLegsAndUrl();
                openChainModal();
                loadChain(window.currentSymbol);

            });

            // Add listener for the stockSearchCleared event
            document.addEventListener("stockSearchCleared", () => {
                // Clear previous state
                window.clearSpreadState();
                window.currentSymbol = "";

                window.resetBacktestingState();
                // Update UI to reflect cleared state
                updateMultiplier(1);
                updateLegsAndUrl();
            });
        });
    </script>

    <!-- MAIN CONTAINER -->
    <div class="rhs-block duration-300 transition-width min-h-screen bg-neutral-100 pb-[65px] xl:pb-0">
        <div class="flex items-center ml-5">
            <h1 id="page-header" class="md:text-xl text-lg font-semibold ">
                Spreads
            </h1>
            <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">Learn, Compare & Execute the
                Best Spread Strategies in One Place</h2>
        </div>
        <div class="p-3">
            <!-- Introductory Paragraph always hidden, added for SEO -->
            <div class="hidden bg-white rounded-lg md:p-5 p-3 mb-4">
                <p>Explore spread trading with AIBull's advanced tools, designed to help you analyze and execute Bull
                    and Bear spreads effectively. Whether you're new to options or an experienced trader, our platform
                    simplifies spread analysis, allowing you to compare strategies and understand risks and rewards.
                    Learn how a spread generally works and optimize your trading decisions with confidence.</p>
            </div>
            <div class="xl:flex gap-3">
                <!-- LEFT PANEL: Search & Legs -->
                <div class="xl:w-[45%] w-full">
                    <!-- Search & Most Active Block -->
                    <div class="bg-white rounded-lg md:p-5 p-3 mb-4">
                        {% with enable_backtesting=true, load_strategies="true" %}
                        {% include 'blocks/search-active-recent-stocks.html' %}
                        {% endwith %}
                    </div>

                    <!-- Legs & Multiplier Block -->
                    {% include 'options/spreads/legs.html' %}
                    <div id="legs-control-loader"></div>

                </div>

                <!-- RIGHT PANEL: Payoff and Chart -->
                <div class="xl:w-[55%] w-full">
                    <!-- Strategies Section  -->
                    <div>
                        {% include 'options/spreads/strategies.html' %}
                    </div>
                    <!-- Payoff Summary -->
                    <div>
                        {% include 'options/spreads/payoff.html' %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- MODAL: Option Chain -->
    {% include 'options/spreads/option-chain.html' %}

    <!-- URL Functions -->
    {% include 'options/spreads/url.html' %}


    {% include 'blocks/footer.html' %}

    <!-- Global Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@^2.0.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
    <!-- (Additional shared scripts can be included here if needed) -->
</body>

</html>