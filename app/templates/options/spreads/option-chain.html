{% include 'blocks/options.html' %}
{% include 'blocks/options-chain-ai-analysis.html' %}
<!-- options/spreads/option-chain.html -->
<div id="chain-modal"
    class="modal fixed inset-0 bg-black bg-opacity-50 h-full hidden overflow-hidden flex items-center justify-center z-50">
    <div
        class="flex flex-col bg-white p-6 rounded-xl shadow-lg w-[98%] h-[95vh] overflow-scroll custom-scroll lg:w-[80%]">
        <!-- Modal Header -->
        <div class="mb-3">
            <div class="flex justify-between items-center">
                <div class="lg:flex items-center lg:space-x-6">
                    <div class="md:flex items-center space-x-2">
                        <h2 class="text-lg font-semibold">Option Chain</h2>
                        <div id="modal-timestamp" class="timestamp-component text-sm text-gray-600"></div>
                    </div>

                    <!-- Display Current Stock Info -->
                    <div id="chain-stock-info"
                        class="text-sm text-neutral-600 flex flex-wrap items-center md:space-x-3 mt-2 lg:mt-0">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>

                <div class="flex items-center gap-2">
                    <!-- AI Analysis Button -->
                    <button id="chain-ai-analysis-btn" onclick="handleAiAnalysis()"
                        class="animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-sparkles">
                            <path
                                d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                            <path d="M20 3v4" />
                            <path d="M22 5h-4" />
                            <path d="M4 17v2" />
                            <path d="M5 18H3" />
                        </svg> Get AI Analysis
                    </button>

                    <button onclick="closeChainModal()" class="hover:text-red-600 text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x">
                            <path d="M18 6 6 18"></path>
                            <path d="m6 6 12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Expiry Tabs + Future Price + LTP Checkbox in a single row (or two) -->
        <div class="flex flex-wrap items-center gap-2 mb-2 text-xs relative">
            <!-- Expiry Tabs -->
            <div id="chain-expiry-tabs" class="flex flex-wrap gap-1"></div>

            <!-- Future Price + FUT Buttons -->
            <div id="chain-future-price"></div>

            <!-- Checkbox for using LTP instead of Bid/Ask Prices -->
            <div class="md:flex items-center gap-3">
                <div class="flex items-center gap-1">
                    <input type="checkbox" id="useLTPCheckbox" onchange="toggleLTPUsage()" />
                    <label for="useLTPCheckbox" class="text-xs text-gray-700 dark:text-gray-300">
                        Use Strike LTPs instead of Bid/Ask
                        <span class="italic text-gray-500 dark:text-gray-400">(may be delayed in fast markets)</span>
                    </label>
                </div>
                <div class="flex items-center gap-1">
                    <input type="checkbox" id="hideIlliquidCheckbox" onchange="toggleHideIlliquid()" />
                    <label for="hideIlliquidCheckbox" class="text-xs text-gray-700 dark:text-gray-300">
                        Hide Illiquid Options
                    </label>
                </div>
            </div>
        </div>

        <!-- Loading Indicator - Absolutely positioned in the center of the modal -->
        <div id="chain-loading"
            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white bg-opacity-90 p-6 rounded-lg z-20 hidden">
            <div class="flex flex-col items-center">
                <div class="w-16 h-16 border-4 border-blue-500 border-dashed rounded-full animate-spin"></div>
                <span class="mt-4 text-sm font-medium text-gray-700">Loading chain data...</span>
            </div>
        </div>

        <!-- Option Chain Table -->
        <div id="chain-table" class="overflow-x-auto overflow-auto bg-white custom-scroll rounded text-xs">
            <!-- Sticky header CSS for table -->
            <style>
                #chain-table table thead th {
                    position: sticky;
                    top: 0;
                    background: white;
                    z-index: 2;
                }
            </style>
        </div>
    </div>
</div>

<script>
    /********************************************************************
     * CHAIN MODAL & DATA FUNCTIONS
     ********************************************************************/

    // Initialize modal timestamp
    let modalTimestamp;

    function initModalTimestamp() {
        if (!modalTimestamp) {
            modalTimestamp = window.createTimestamp('modal-timestamp', {
                showRefresh: true,
                onRefresh: () => refreshChainData()
            });
        } else {
            modalTimestamp.updateTimestamp();
        }
    }

    // Global flags for options
    window.useLTPForPrices = false;
    window.hideIlliquid = false;

    function toggleLTPUsage() {
        window.useLTPForPrices = document.getElementById('useLTPCheckbox').checked;
        if (window.modalCurrentExpiry) {
            displayChainForExpiry(window.chainData.options, window.modalCurrentExpiry);
        }
    }

    function toggleHideIlliquid() {
        window.hideIlliquid = document.getElementById('hideIlliquidCheckbox').checked;
        if (window.modalCurrentExpiry) {
            displayChainForExpiry(window.chainData.options, window.modalCurrentExpiry);
        }
    }

    // Function to set LTP usage based on backtesting status
    function setUseLTPForBacktesting() {
        const useLTPCheckbox = document.getElementById('useLTPCheckbox');
        if (useLTPCheckbox && window.backtestDate) {
            useLTPCheckbox.checked = true;
            window.useLTPForPrices = true;
        } else {
            useLTPCheckbox.checked = false;
            window.useLTPForPrices = false;
        }
    }

    function showChainLoading() {
        document.getElementById("chain-loading").classList.remove("hidden");
        document.getElementById("chain-table").classList.add("hidden");
    }

    function hideChainLoading() {
        document.getElementById("chain-loading").classList.add("hidden");
        document.getElementById("chain-table").classList.remove("hidden");
    }

    async function loadChain(symbol) {
        showChainLoading();
        try {
            const result = await window.OptionsUtils.loadChainData(symbol);
            if (!result) {
                document.getElementById("chain-table").innerHTML =
                    "<p class='p-2 text-red-600'>No chain data found.</p>";
                throw new Error("No chain data found.");
            }
            // Update global state
            window.chainData.options = result.chainData;
            window.chainData.expiryDates = result.expiryDates;
            window.currentSpotPrice = result.spotPrice;
            window.futurePrices = result.futurePrices;
            // Update lot size
            const lotSize = LotSizes[symbol] || 1;
            updateMultiplier(1);
            // Update UI elements with new data
            document.getElementById("chain-stock-info").innerHTML =
                `<span class="font-medium">Stock: ${symbol}</span> |
                 <span class="font-medium">Spot: ₹${window.currentSpotPrice}</span> |
                 <span class="font-medium">Lot Size: ${lotSize * window.currentMultiplier} (${window.currentMultiplier} x ${lotSize})</span>`;
            // Build expiry tabs
            if (window.chainData.expiryDates.length) {
                buildExpiryTabs(window.chainData.expiryDates);
            } else {
                document.getElementById("chain-table").innerHTML = "<p class='p-2'>No expiries found.</p>";
            }
            legsControlHeaderVisibility();
            renderPopularStrikes();
        } catch (error) {
            console.error("Error loading chain data:", error);
            document.getElementById("chain-table").innerHTML =
                `<p class='p-2 text-red-600'>Error loading data: ${error.message}</p>`;
        } finally {
            hideChainLoading();
        }
    }

    async function refreshChainData() {
        try {
            await loadChain(window.currentSymbol);
            window.selectedLegs.forEach(leg => {
                window.OptionsUtils.getStrikePriceForLeg(
                    leg,
                    window.chainData.options,
                    window.futurePrices,
                    window.currentSpotPrice
                );
            });
            updateLegsAndUrl();
            if (window.modalCurrentExpiry) {
                displayChainForExpiry(window.chainData.options, window.modalCurrentExpiry);
            }
        } catch (error) {
            console.error('Error refreshing chain data:', error);
            showAlert('Error refreshing data');
        }
    }

    function openChainModal() {
        const lotSize = LotSizes[currentSymbol] || 1;
        const calculatedLotSize = lotSize * currentMultiplier;
        document.getElementById("chain-stock-info").innerHTML =
            `<span class="font-medium">Stock: ${currentSymbol}</span> |
             <span class="font-medium">Spot: ₹${currentSpotPrice}</span> |
             <span class="font-medium">Lot Size: ${calculatedLotSize} (${currentMultiplier} x ${lotSize})</span>`;
        document.getElementById("chain-table").innerHTML = "";
        document.getElementById("chain-expiry-tabs").innerHTML = "";
        document.getElementById("chain-future-price").innerHTML = "";
        document.getElementById("chain-modal").classList.remove("hidden");

        // Set LTP checkbox based on backtesting status
        setUseLTPForBacktesting();
    }

    function closeChainModal() {
        document.getElementById("chain-modal").classList.add("hidden");
    }

    function buildExpiryTabs(expiryDates) {
        const tabsDiv = document.getElementById("chain-expiry-tabs");
        tabsDiv.innerHTML = "";
        if (!expiryDates.length) {
            document.getElementById("chain-table").innerHTML = "<p class='p-2'>No expiries found.</p>";
            return;
        }
        expiryDates.forEach((date, idx) => {
            const btn = document.createElement("button");
            btn.dataset.expiry = date;
            btn.textContent = date;
            btn.className = `px-2 py-1 border text-xs rounded font-medium
                             ${idx === 0
                    ? "bg-blue-500 text-white"
                    : "bg-white dark:bg-neutral-800  dark:hover:bg-neutral-700"}`;
            btn.onclick = () => {
                handleExpiryClick(date);
            };
            tabsDiv.appendChild(btn);
        });
        window.modalCurrentExpiry = window.modalCurrentExpiry || expiryDates[0];
        handleExpiryClick(window.modalCurrentExpiry);
    }

    function handleExpiryClick(expiry) {
        const tabsDiv = document.getElementById("chain-expiry-tabs");
        const btn = tabsDiv.querySelector(`[data-expiry="${expiry}"]`);
        tabsDiv.querySelectorAll("button").forEach(b => {
            b.classList.remove('bg-blue-500', 'text-white');
            b.classList.add('bg-white', 'dark:bg-neutral-800');
        });
        btn.classList.add('bg-blue-500', 'text-white');
        btn.classList.remove('bg-white', 'dark:bg-neutral-800');
        window.modalCurrentExpiry = expiry;
        displayChainForExpiry(window.chainData.options, window.modalCurrentExpiry);
    }

    // Funtion to build future buttons
    function buildFutureButtonsInChainModal(expiry) {
        const futDiv = document.getElementById("chain-future-price");
        futDiv.innerHTML = "";

        if (futurePrices[expiry]) {
            const futBuySelected = window.selectedLegs.some(
                l => l.type === "FUT" && l.action === "buy" && l.expiry === expiry
            );
            const futSellSelected = window.selectedLegs.some(
                l => l.type === "FUT" && l.action === "sell" && l.expiry === expiry
            );
            futDiv.innerHTML = `
                <div>
                    <span class="bg-purple-100 font-semibold px-2 py-1 rounded">
                        FUT Price: ₹${futurePrices[expiry]}
                    </span>
                    <button class="ml-2 px-2 py-1
                        ${futBuySelected ? 'bg-green-500 text-white' : 'bg-green-100 hover:bg-green-200'}
                        rounded font-medium text-xs"
                        onclick="toggleFutureLeg('${expiry}','buy',${futurePrices[expiry]})">
                        Buy Future
                    </button>
                    <button class="px-2 py-1
                        ${futSellSelected ? 'bg-red-500 text-white' : 'bg-red-100 hover:bg-red-200'}
                        rounded font-medium text-xs"
                        onclick="toggleFutureLeg('${expiry}','sell',${futurePrices[expiry]})">
                        Sell Future
                    </button>
                </div>
            `;
        } else {
            // Show message that future prices are not available for this expiry
            futDiv.innerHTML = `
                <div class="text-center text-gray-500">
                    No future prices available for this expiry
                </div>
            `;
        }
    }

    // Function to display chain for expiry
    function displayChainForExpiry(allOptions, expiry) {
        let futureInfo = "";
        if (futurePrices[expiry]) {
            futureInfo = ` |
                <span>FUT: <span class="font-medium text-neutral-900">₹${futurePrices[expiry]}</span></span>`;
        }
        const lotSize = LotSizes[currentSymbol] || 1;
        const calculatedLotSize = lotSize * currentMultiplier;
        document.getElementById("chain-stock-info").innerHTML =
            `<span class="font-medium">Stock: ${currentSymbol}</span> |
             <span class="font-medium">Spot: ₹${currentSpotPrice}${futureInfo}</span> |
             <span class="font-medium">Lot Size: ${calculatedLotSize} (${currentMultiplier} x ${lotSize})</span>`;

        // Build future buttons
        buildFutureButtonsInChainModal(expiry);

        // Filter by expiry
        let filtered = allOptions.filter(opt => opt.expiryDate === expiry);

        // Filter out illiquid options if the checkbox is checked
        if (window.hideIlliquid) {
            filtered = filtered.filter(opt => {
                const ceVol = (opt.CE && opt.CE.totalTradedVolume) || 0;
                const peVol = (opt.PE && opt.PE.totalTradedVolume) || 0;
                return ceVol > 0 && peVol > 0;
            });
        }

        // Show message if no options after filtering
        if (!filtered.length) {
            document.getElementById("chain-table").innerHTML = `
                <table class="w-full border-collapse text-xs">
                    ${buildTableHeader()}
                    <tr>
                        <td colspan="21" class="p-4 text-center text-gray-600">
                            No options available for this expiry with current filters
                        </td>
                    </tr>
                </table>
            `;
            return;
        }

        // Sort by strike ascending
        filtered.sort((a, b) => a.strikePrice - b.strikePrice);

        // Find spot price index or closest index
        const spotPrice = window.currentSpotPrice;
        let spotIndex = filtered.findIndex(opt => opt.strikePrice >= spotPrice);
        if (spotIndex === -1) spotIndex = filtered.length - 1;

        // Calculate visible range (15 strikes above and below spot)
        const lowerBound = Math.max(0, spotIndex - 15);
        const upperBound = Math.min(filtered.length - 1, spotIndex + 15);

        // Store the full filtered data and current bounds for "Show More" functionality
        window._fullFilteredOptions = filtered;
        window._currentLowerBound = lowerBound;
        window._currentUpperBound = upperBound;

        // Get visible options
        let visibleOptions = filtered.slice(lowerBound, upperBound + 1);

        // Build table HTML
        let tableHtml = buildTableHeader();
        let tableBody = document.createElement("tbody");

        // Add "Show More Lower Strikes" button if needed
        if (lowerBound > 0) {
            const remainingBelow = lowerBound;
            const toLoadBelow = Math.min(15, remainingBelow);
            tableBody.innerHTML += `
                <tr class="bg-gray-100 hover:bg-gray-200 cursor-pointer text-center"
                    onclick="renderChainTableWithBounds('below')">
                    <td colspan="21" class="p-2 text-sm font-medium text-blue-600">
                        Show ${toLoadBelow} More Lower Strikes
                    </td>
                </tr>
            `;
        }

        // Add option rows
        tableBody.innerHTML += buildOptionRows(visibleOptions);

        // Add "Show More Higher Strikes" button if needed
        if (upperBound < filtered.length - 1) {
            const remainingAbove = filtered.length - 1 - upperBound;
            const toLoadAbove = Math.min(15, remainingAbove);
            tableBody.innerHTML += `
                <tr class="bg-gray-100 hover:bg-gray-200 cursor-pointer text-center"
                    onclick="renderChainTableWithBounds('above')">
                    <td colspan="21" class="p-2 text-sm font-medium text-blue-600">
                        Show ${toLoadAbove} More Higher Strikes
                    </td>
                </tr>
            `;
        }

        tableHtml += tableBody.innerHTML;
        document.getElementById("chain-table").innerHTML = tableHtml;
        addHoverListeners();

        // Scroll to the spot price row
        setTimeout(() => {
            const spotRow = document.querySelector('.spot-row');
            if (spotRow) {
                spotRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }, 100);
    }

    // Funtion to render the chain table with bounds
    function renderChainTableWithBounds(direction = null) {
        const allOptions = window._fullFilteredOptions;
        if (!allOptions) return;

        // If direction is null, we will use the prev bounds
        let newLowerBound = window._currentLowerBound;
        let newUpperBound = window._currentUpperBound;

        if (direction === 'above') {
            // Load 15 more strikes above
            newUpperBound = Math.min(allOptions.length - 1, newUpperBound + 15);
        } else if (direction === 'below') {
            // Load 15 more strikes below
            newLowerBound = Math.max(0, newLowerBound - 15);
        }

        // Update bounds
        window._currentLowerBound = newLowerBound;
        window._currentUpperBound = newUpperBound;

        // Get visible options
        const visibleOptions = allOptions.slice(newLowerBound, newUpperBound + 1);

        // Rebuild table
        let tableHtml = buildTableHeader();

        // Add "Show More Lower Strikes" button if needed
        if (newLowerBound > 0) {
            const remainingBelow = newLowerBound;
            const toLoadBelow = Math.min(15, remainingBelow);
            tableHtml += `
                <tr class="bg-gray-100 hover:bg-gray-200 cursor-pointer text-center"
                    onclick="renderChainTableWithBounds('below')">
                    <td colspan="21" class="p-2 text-sm font-medium text-blue-600">
                        Show ${toLoadBelow} More Lower Strikes
                    </td>
                </tr>
            `;
        }

        // Add option rows
        tableHtml += buildOptionRows(visibleOptions);

        // Add "Show More Higher Strikes" button if needed
        if (newUpperBound < allOptions.length - 1) {
            const remainingAbove = allOptions.length - 1 - newUpperBound;
            const toLoadAbove = Math.min(15, remainingAbove);
            tableHtml += `
                <tr class="bg-gray-100 hover:bg-gray-200 cursor-pointer text-center"
                    onclick="renderChainTableWithBounds('above')">
                    <td colspan="21" class="p-2 text-sm font-medium text-blue-600">
                        Show ${toLoadAbove} More Higher Strikes
                    </td>
                </tr>
            `;
        }

        document.getElementById("chain-table").innerHTML = tableHtml;
        addHoverListeners();
    }

    // Helper function to build table header
    function buildTableHeader() {
        return `
            <table class="w-full border-collapse text-xs">
                <thead class="sticky top-0 z-10">
                    <tr>
                        <th colspan="10" class="border p-1 text-center bg-green-50">CALLS</th>
                        <th class="border p-1 text-center bg-purple-100"></th>
                        <th colspan="10" class="border p-1 text-center bg-red-50">PUTS</th>
                    </tr>
                    <tr class="bg-gray-100">
                        <th class="border p-1 text-center">IV</th>
                        <th class="border p-1 text-center">OI</th>
                        <th class="border p-1 text-center">Delta</th>
                        <th class="border p-1 text-center">Ask</th>
                        <th class="border p-1 text-center">Ask Qty</th>
                        <th class="border p-1 text-center">Bid</th>
                        <th class="border p-1 text-center">Bid Qty</th>
                        <th class="border p-1 text-center">LTP</th>
                        <th class="border p-1 text-center">Action</th>
                        <th class="border p-1 text-center bg-purple-50">STRIKE</th>
                        <th class="border p-1 text-center">Action</th>
                        <th class="border p-1 text-center">LTP</th>
                        <th class="border p-1 text-center">Bid</th>
                        <th class="border p-1 text-center">Bid Qty</th>
                        <th class="border p-1 text-center">Ask</th>
                        <th class="border p-1 text-center">Ask Qty</th>
                        <th class="border p-1 text-center">Delta</th>
                        <th class="border p-1 text-center">OI</th>
                        <th class="border p-1 text-center">IV</th>
                    </tr>
                </thead>
        `;
    }

    // Helper function to build option rows
    function buildOptionRows(options) {
        let html = '';
        let spotInserted = false;

        options.forEach((opt, index) => {
            // Insert spot price row if needed
            if (!spotInserted && opt.strikePrice > window.currentSpotPrice) {
                html += `
                    <tr class="spot-row bg-blue-100">
                        <td colspan="21" class="p-2 text-center font-medium">
                            Spot Price: ₹${window.currentSpotPrice.toFixed(2)}
                        </td>
                    </tr>
                `;
                spotInserted = true;
            }

            // Add the option row
            html += buildSingleRow(opt);
        });

        return html;
    }

    function buildSingleRow(opt) {
        const ce = opt.CE || {};
        const pe = opt.PE || {};
        const underlying = ce.underlyingValue || pe.underlyingValue || window.currentSpotPrice;
        const callITM = (opt.strikePrice < underlying);
        const putITM = (opt.strikePrice > underlying);
        const callHighlight = callITM ? "bg-yellow-100" : "";
        const putHighlight = putITM ? "bg-yellow-100" : "";

        // Helper function to format numbers
        const formatNumber = (num, decimals = 2) => {
            if (!num && num !== 0) return '-';
            return Number(num).toFixed(decimals);
        };

        // Check if leg is selected
        const isLegSelected = (type, strike, action) => {
            const legId = `${type}-${strike}-${action}-${opt.expiryDate}`;
            return window.selectedLegs.some(l => l.id === legId);
        };

        // Get prices based on LTP usage setting
        const callBuyPrice = window.useLTPForPrices ? ce?.lastPrice : ce?.askPrice;
        const callSellPrice = window.useLTPForPrices ? ce?.lastPrice : ce?.bidprice;
        const putBuyPrice = window.useLTPForPrices ? pe?.lastPrice : pe?.askPrice;
        const putSellPrice = window.useLTPForPrices ? pe?.lastPrice : pe?.bidprice;

        return `
            <tr class="text-center hover:bg-gray-50">
                <!-- Call Side -->
                <td class="border p-1 text-center ${callHighlight}">${ce?.impliedVolatility ? ce.impliedVolatility.toFixed(2) : '-'}</td>
                <td class="border p-1 text-center ${callHighlight}">${formatNumber(ce?.openInterest)}</td>
                <td class="border p-1 text-center ${callHighlight}">${ce?.delta ? ce.delta.toFixed(2) : '-'}</td>
                <td class="border p-1 text-center ${callHighlight}">${ce?.askPrice || '-'}</td>
                <td class="border p-1 text-center ${callHighlight}">${ce?.askQty || '-'}</td>
                <td class="border p-1 text-center ${callHighlight}">${ce?.bidprice || '-'}</td>
                <td class="border p-1 text-center ${callHighlight}">${ce?.bidQty || '-'}</td>
                <td class="border p-1 text-center ${callHighlight}">${ce?.lastPrice || '-'}</td>
                <td class="border p-1 text-center ${callHighlight}">
                    <div class="flex gap-1 justify-center">
                        <button onclick="toggleOptionLeg('CE', 'buy', ${opt.strikePrice}, ${callBuyPrice}, ${ce?.impliedVolatility ? ce.impliedVolatility.toFixed(2) : 0})"
                            class="px-2 py-0.5 rounded text-xs ${isLegSelected('CE', opt.strikePrice, 'buy') ? 'bg-green-500 text-white' : 'bg-green-100 hover:bg-green-200'}"
                            data-type="CE" data-action="buy">
                            B
                        </button>
                        <button onclick="toggleOptionLeg('CE', 'sell', ${opt.strikePrice}, ${callSellPrice}, ${ce?.impliedVolatility ? ce.impliedVolatility.toFixed(2) : 0})"
                            class="px-2 py-0.5 rounded text-xs ${isLegSelected('CE', opt.strikePrice, 'sell') ? 'bg-red-500 text-white' : 'bg-red-100 hover:bg-red-200'}"
                            data-type="CE" data-action="sell">
                            S
                        </button>
                    </div>
                </td>

                <!-- Strike Column -->
                <td class="border p-1 text-center font-semibold bg-purple-50">${opt.strikePrice}</td>

                <!-- Put Side -->
                <td class="border p-1 text-center ${putHighlight}">
                    <div class="flex gap-1 justify-center">
                        <button onclick="toggleOptionLeg('PE', 'buy', ${opt.strikePrice}, ${putBuyPrice}, ${pe?.impliedVolatility ? pe.impliedVolatility.toFixed(2) : 0})"
                            class="px-2 py-0.5 rounded text-xs ${isLegSelected('PE', opt.strikePrice, 'buy') ? 'bg-green-500 text-white' : 'bg-green-100 hover:bg-green-200'}"
                            data-type="PE" data-action="buy">
                            B
                        </button>
                        <button onclick="toggleOptionLeg('PE', 'sell', ${opt.strikePrice}, ${putSellPrice}, ${pe?.impliedVolatility ? pe.impliedVolatility.toFixed(2) : 0})"
                            class="px-2 py-0.5 rounded text-xs ${isLegSelected('PE', opt.strikePrice, 'sell') ? 'bg-red-500 text-white' : 'bg-red-100 hover:bg-red-200'}"
                            data-type="PE" data-action="sell">
                            S
                        </button>
                    </div>
                </td>
                <td class="border p-1 text-center ${putHighlight}">${pe?.lastPrice || '-'}</td>
                <td class="border p-1 text-center ${putHighlight}">${pe?.bidprice || '-'}</td>
                <td class="border p-1 text-center ${putHighlight}">${pe?.bidQty || '-'}</td>
                <td class="border p-1 text-center ${putHighlight}">${pe?.askPrice || '-'}</td>
                <td class="border p-1 text-center ${putHighlight}">${pe?.askQty || '-'}</td>
                <td class="border p-1 text-center ${putHighlight}">${pe?.delta ? pe.delta.toFixed(2) : '-'}</td>
                <td class="border p-1 text-center ${putHighlight}">${formatNumber(pe?.openInterest)}</td>
                <td class="border p-1 text-center ${putHighlight}">${pe?.impliedVolatility ? pe.impliedVolatility.toFixed(2) : '-'}</td>
            </tr>
        `;
    }

    // Function to add hover event listeners on option action buttons using Tailwind classes
    function addHoverListeners() {
        // Remove existing listeners by cloning and replacing buttons
        const buttons = document.querySelectorAll('button[data-type][data-action]');
        buttons.forEach(button => {
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
        });

        // Add new listeners
        document.querySelectorAll('button[data-type][data-action]').forEach(button => {
            button.addEventListener('mouseover', function () {
                const type = button.getAttribute('data-type');
                const action = button.getAttribute('data-action');
                const row = button.closest('tr');
                if (!row) return;
                let cellIndex;
                // For call options
                if (type === 'CE') {
                    if (action === 'buy') {
                        cellIndex = window.useLTPForPrices ? 7 : 3;
                    } else if (action === 'sell') {
                        cellIndex = window.useLTPForPrices ? 7 : 5;
                    }
                }
                // For put options
                else if (type === 'PE') {
                    if (action === 'buy') {
                        cellIndex = window.useLTPForPrices ? 11 : 14;
                    } else if (action === 'sell') {
                        cellIndex = window.useLTPForPrices ? 11 : 12;
                    }
                }
                const cells = row.querySelectorAll('td');
                if (cells[cellIndex]) {
                    cells[cellIndex].classList.add("bg-yellow-200");
                }
            });

            button.addEventListener('mouseout', function () {
                const row = button.closest('tr');
                if (!row) return;
                const cells = row.querySelectorAll('td');
                // Remove highlight from all candidate cells
                [3, 5, 7, 11, 12, 14].forEach(idx => {
                    if (cells[idx]) cells[idx].classList.remove("bg-yellow-200");
                });
            });
        });
    }

    // Function to handle AI Analysis button click
    window.handleAiAnalysis = function () {
        // Check if we have the necessary chain data 
        if (!window.chainData || !window.chainData.options || !window.modalCurrentExpiry) {
            showAlert("Please load option chain data first");
            return;
        }

        if (!window.currentSpotPrice) {
            showAlert("Stock price data is not available");
            return;
        }

        // Create a data object to pass to the getChainAiAnalysis function
        const customData = {
            symbol: window.currentSymbol || 'Unknown',
            chainData: {
                options: window.chainData.options || []
            },
            currentExpiry: window.modalCurrentExpiry,
            spotPrice: window.currentSpotPrice,
            futurePrices: window.futurePrices || {}
        };

        // Call the getChainAiAnalysis function with custom data
        getChainAiAnalysis(customData);
    };

    initModalTimestamp();
    initLegsTimestamp();
</script>