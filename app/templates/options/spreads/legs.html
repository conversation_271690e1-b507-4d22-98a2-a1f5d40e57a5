<div id="legs-control-header" class="flex flex-wrap bg-white rounded-lg md:p-5 p-3 hidden w-full">
    <div class="w-full bg-gray-50 p-2 rounded-lg 2xl:flex flex-wrap justify-between items-start">
        <div class="flex flex-col flex-wrap gap-3 items-start justify-between md:flex-row">
            <div class="flex items-center hidden w-full md:w-auto gap-2 text-xs" id="multiplier-input-container">
                <label for="multiplier-input" class="block font-medium  text-neutral-600 flex">
                    <span class="md:flex hidden">Multiplier (No. of Lots): </span>
                </label>
                <input id="multiplier-input" type="number" value=1 min="1"
                    class="w-12 border rounded px-2 py-0.5 font-medium focus:outline-0 focus:border focus:border-blue-400"
                    onchange="updateMultiplier(this.value)" />
                Lot Size: <span id="lot-size-display" class="text-neutral-950 font-medium"></span>
            </div>

            <!-- Timestamp and Refresh Section -->
            <div class="flex items-center gap-2 hidden  mb-3" id="timestamp-content">
                <div id="legs-timestamp" class="timestamp-component"></div>
            </div>
        </div>
        <!-- Action Buttons -->
        <div class="flex items-center justify-start gap-1 w-full md:w-auto">
            <button id="open-chain-modal-btn"
                class="text-blue-600 border transition-colors bg-blue-100 hover:text-white font-medium hover:bg-blue-600 px-2 py-1 rounded-md text-xs disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent disabled:hover:text-blue-600">
                Add Leg
            </button>
            <button onclick="openSaveStrategyModal(event)" id="saveStrategyBtn"
                class="relative group flex items-center px-2 py-1 text-xs font-medium text-white bg-green-600 hover:bg-green-800 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                Save
                <span
                    class="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 whitespace-nowrap bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                    Save Strategy
                </span>
            </button>
        </div>
    </div>

    <div id="legs-container" class="w-full"></div>

    <!-- Add this after the legs-container div and before the strategies section -->
    <div id="popular-strikes-section" class="hidden mt-3 border-t pt-3 border-dashed w-full">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-2">
                <h3 class="text-sm font-semibold text-neutral-800">Popular Strikes</h3>
                <span class="bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-lg">Based on OI</span>
            </div>
            <div id="popular-strikes-expiry" class="text-xs text-neutral-800"></div>
        </div>
        <div id="popular-strikes-content" class="text-sm"></div>
    </div>
</div>
<script>
    // Initialize timestamp component for legs
    let legsTimestamp;

    function initLegsTimestamp() {
        if (!legsTimestamp) {
            legsTimestamp = window.createTimestamp('legs-timestamp', {
                showRefresh: true,
                onRefresh: () => refreshChainData() // Function is in option-chain.html 
            });
        } else {
            legsTimestamp.updateTimestamp();
        }
    }

    // Function to handle the visibility to control the multiplier, timestamp and add leg button
    function legsControlHeaderVisibility() {
        const legsControlHeader = document.getElementById("legs-control-header");

        if (window.chainData.options.length) {
            legsControlHeader.classList.remove("hidden");
            // Visibility of multiplier, timestamp and add leg button
            if (window.selectedLegs.length > 0) {
                document.getElementById("multiplier-input-container").classList.remove("hidden");
                document.getElementById("timestamp-content").classList.remove("hidden");
                document.getElementById("lot-size-display").classList.remove("hidden");
            } else {
                document.getElementById("multiplier-input-container").classList.add("hidden");
                document.getElementById("timestamp-content").classList.add("hidden");
                document.getElementById("lot-size-display").classList.add("hidden");
            }
        } else {
            legsControlHeader.classList.add("hidden");
        }
    }

    // New function to remove all legs
    function removeAllLegs() {
        window.selectedLegs = [];
        updateLegsAndUrl();
    }

    // Toggle action using a button instead of a dropdown
    function toggleLegAction(id) {
        const leg = window.selectedLegs.find(l => l.id === id);
        if (!leg) return;
        leg.action = (leg.action === 'buy') ? 'sell' : 'buy';
        // Recalculate id after toggle
        leg.id = leg.type === 'FUT'
            ? `FUT-${leg.action}-${leg.expiryDate}`
            : `${leg.type}-${leg.strike}-${leg.action}-${leg.expiryDate}`;

        // Need to update the price too
        window.OptionsUtils.getStrikePriceForLeg(leg, window.chainData.options, window.futurePrices, window.currentSpotPrice);
        updateLegsAndUrl();
    }

    /********************************************************************
     * STRIKE INCREMENT/DECREMENT FUNCTIONS
     *******************************************************************/

    function updateStrike(legId, newStrike, mode = 'direct') {
        const leg = window.selectedLegs.find(l => l.id === legId);
        window.OptionsUtils.updateStrike(
            leg,
            newStrike,
            mode,
            window.chainData.options,
            window.futurePrices,
            window.currentSpotPrice
        );

        updateLegsAndUrl();
        if (window.modalCurrentExpiry) {
            displayChainForExpiry(window.chainData.options, window.modalCurrentExpiry);
        }
    }

    function updateStrategiesAndPayoffContainerVisibility() {
        const payoffContainer = document.getElementById("payoff-container");
        updateStrategyCards();
        const strategiesContainerWrapper = document.getElementById("strategies-container-wrapper");
        if (!window.selectedLegs || window.selectedLegs.length === 0) {
            payoffContainer.classList.add("hidden");
            strategiesContainerWrapper.classList.remove("hidden");
        } else {
            payoffContainer.classList.remove("hidden");
            strategiesContainerWrapper.classList.add("hidden");
        }
    }

    // Render the selected legs table.
    function renderLegs() {
        const container = document.getElementById("legs-container");

        // Update multiplier visibility
        legsControlHeaderVisibility();

        // If no legs are selected, clear the legs container.
        if (!window.selectedLegs || window.selectedLegs.length === 0) {
            container.innerHTML = "";
            return;
        }

        let html = `<div class="mt-4 overflow-x-auto custom-scroll">
      <table class="w-full text-xs border-collapse">
        <thead>
          <tr class="bg-gray-50 text-neutral-800">
            <th class="border p-1 text-left w-[1rem]"></th>
            <th class="border p-1 text-left w-[2rem]">Type</th>
            <th class="border p-1 text-left w-[4rem]">Option</th>
            <th class="border p-1 text-left w-[7rem]">Strike</th>
            <th class="border p-1 text-left w-[8rem]">Expiry</th>
            <th class="border p-1 text-left">Price</th>
            <th class="border p-1 text-left">Lots</th>
            <th class="border p-1 xl:w-8">
                <!-- Global remove all legs button -->
                <button onclick="removeAllLegs()" class="text-red-600 hover:text-red-800 text-sm" title="Remove All Legs">✕</button>
            </th>
          </tr>
        </thead>
        <tbody>`;

        window.selectedLegs.forEach(leg => {
            html += `
        <tr class="hover:bg-gray-50">
          <td class="border p-1">
            <input type="checkbox" onchange="toggleLeg('${leg.id}')" ${leg.enabled ? "checked" : ""} class="cursor-pointer" />
          </td>
          <td class="border p-1">
            <!-- Toggling button for Buy/Sell using FUT colors -->
            <button onclick="toggleLegAction('${leg.id}')" class="border px-2 py-0.5 rounded focus:outline-0 ${leg.action === 'buy' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}">
              ${leg.action === 'buy' ? 'B' : 'S'}
            </button>
          </td>
          <td class="border p-1">
            <select onchange="updateLegType('${leg.id}', this.value)" class="bg-gray-100 w-[50px] border focus:outline-0 px-1 py-0.5 rounded">
              <option value="CE" ${leg.type === 'CE' ? 'selected' : ''}>CE</option>
              <option value="PE" ${leg.type === 'PE' ? 'selected' : ''}>PE</option>
              <option value="FUT" ${leg.type === 'FUT' ? 'selected' : ''}>FUT</option>
            </select>
          </td>
          <td class="border p-1">
            ${leg.type === 'FUT' ?
                    `<div class="text-center text-gray-500">N/A</div>` :
                    `<div class="flex items-center">
                    <button class="bg-gray-200 border hover:bg-gray-300 px-1" 
                            onclick="updateStrike('${leg.id}', null, 'decrement')" 
                            ${leg.type === 'FUT' ? 'disabled' : ''}>-</button>
                    <input type="number" 
                           value="${leg.strike || ''}" 
                           class="border px-1 text-center w-20 focus:outline-0 focus:border-blue-400 ${leg.type === 'FUT' ? 'bg-gray-100' : ''}" 
                           onchange="updateStrike('${leg.id}', this.value, 'direct')" 
                           ${leg.type === 'FUT' ? 'disabled' : ''} />
                    <button class="bg-gray-200 border hover:bg-gray-300 px-1" 
                            onclick="updateStrike('${leg.id}', null, 'increment')" 
                            ${leg.type === 'FUT' ? 'disabled' : ''}>+</button>
                </div>`
                }
          </td>
          <td class="border p-1">
            <select onchange="updateLegExpiry('${leg.id}', this.value)" class="w-full border rounded px-1 py-0.5 bg-gray-50 w-[120px] ">
              ${window.chainData.expiryDates.map(exp => `<option value="${exp}" ${leg.expiryDate === exp ? 'selected' : ''}>${exp}</option>`).join('')}
            </select>
          </td>
          <td class="border p-1">
            <input type="number" value="${leg.price}" class="w-full focus:outline-0 w-[100px] focus:border-blue-400 rounded px-1" onchange="updateLegPrice('${leg.id}', this.value)" />
          </td>
          <td class="border p-1">
            <input type="number" value="${leg.lots || 1}" min="1" class="w-full focus:outline-0 w-[100px] focus:border-blue-400 rounded px-1" onchange="updateLegLots('${leg.id}', this.value)" />
          </td>
          <td class="border p-1 text-center">
            <button onclick="removeLeg('${leg.id}')" class="text-red-600 hover:text-red-800">✕</button>
          </td>
        </tr>`;
        });
        html += `</tbody></table></div>`;
        container.innerHTML = html;
    }

    // Functions for toggling, updating, and removing legs.
    function toggleLeg(id) {
        const leg = window.selectedLegs.find(l => l.id === id);
        if (leg) {
            leg.enabled = !leg.enabled;
            updateLegsAndUrl();
        }
    }
    function removeLeg(id) {
        window.selectedLegs = window.selectedLegs.filter(l => l.id !== id);
        updateLegsAndUrl();
    }
    function updateLegType(id, newType) {
        const leg = window.selectedLegs.find(l => l.id === id);
        if (!leg) return;

        leg.type = newType;

        // Clear strike for futures
        if (newType === 'FUT') {
            leg.strike = null;
        } else if (!leg.strike) {
            // For options, set initial strike near spot price
            leg.strike = window.OptionsUtils.findNearestStrike(window.currentSpotPrice, window.chainData.options, leg.expiryDate);
        }

        // Update leg ID
        leg.id = leg.type === 'FUT'
            ? `FUT-${leg.action}-${leg.expiryDate}`
            : `${leg.type}-${leg.strike}-${leg.action}-${leg.expiryDate}`;

        // Update price based on new type
        window.OptionsUtils.getStrikePriceForLeg(leg, window.chainData.options, window.futurePrices, window.currentSpotPrice);

        updateLegsAndUrl();
    }
    function updateLegPrice(id, newPrice) {
        const leg = window.selectedLegs.find(l => l.id === id);
        if (!leg) return;
        leg.price = parseFloat(newPrice).toFixed(2); // Ensure 2 decimals
        updateLegsAndUrl();
    }
    function updateLegLots(id, newLots) {
        const leg = window.selectedLegs.find(l => l.id === id);
        if (!leg) return;
        leg.lots = parseInt(newLots) < 1 ? 1 : parseInt(newLots);
        updateLegsAndUrl();
    }
    function updateLegExpiry(id, newExpiry) {
        const leg = window.selectedLegs.find(l => l.id === id);
        if (!leg) return;

        leg.expiryDate = newExpiry;

        // Update leg ID
        leg.id = leg.type === 'FUT'
            ? `FUT-${leg.action}-${newExpiry}`
            : `${leg.type}-${leg.strike}-${leg.action}-${newExpiry}`;

        // Update price based on new expiry
        window.OptionsUtils.getStrikePriceForLeg(leg, window.chainData.options, window.futurePrices, window.currentSpotPrice);

        updateLegsAndUrl();
    }

    function updateLegsAndUrl() {
        renderLegs();
        updatePayoff();
        updateUrlParams();
        updateStrategiesAndPayoffContainerVisibility();
    }

    // Funcction to add or remove a leg when clicked on the chain table
    function toggleOptionLeg(optType, action, strike, ltp, iv) {
        if (!ltp || ltp <= 0) {
            showAlert("Cannot add leg with invalid price (0 or undefined)");
            return;
        }

        const legId = `${optType}-${strike}-${action}-${modalCurrentExpiry}`;

        // Check for existing legs at the same strike price and option type
        const existingLegsAtStrike = window.selectedLegs.filter(l =>
            l.strike === strike &&
            l.expiryDate === modalCurrentExpiry &&
            l.type === optType
        );

        const existingLeg = window.selectedLegs.find(l => l.id === legId);

        if (existingLeg) {
            // If the exact leg exists, remove it
            removeLeg(legId);
        } else {
            // Remove any existing leg at this strike price for this option type
            existingLegsAtStrike.forEach(leg => removeLeg(leg.id));
            if (existingLegsAtStrike.length > 0) {
                showAlert(`Removing existing ${optType} leg at strike ${strike} and adding new leg`);
            }

            // Add the new leg
            window.selectedLegs.push({
                id: legId,
                type: optType,
                strike: strike,
                price: ltp,
                action: action,
                lots: 1,
                enabled: true,
                expiryDate: modalCurrentExpiry,
                iv: iv || 20
            });
        }

        updateLegsAndUrl();
        renderChainTableWithBounds(null);
    }

    // Update the future leg toggle function with similar behavior
    window.toggleFutureLeg = function (expiry, action, price) {
        const legId = `FUT-${action}-${expiry}`;

        // Check for existing future legs for this expiry
        const existingFutureLegs = window.selectedLegs.filter(l =>
            l.type === "FUT" &&
            l.expiryDate === expiry
        );

        const existingLeg = window.selectedLegs.find(l => l.id === legId);

        if (existingLeg) {
            // If the exact leg exists, remove it
            removeLeg(legId);
        } else {
            // Remove any existing future legs for this expiry
            existingFutureLegs.forEach(leg => removeLeg(leg.id));
            if (existingFutureLegs.length > 0) {
                showAlert(`Removing existing future leg at expiry ${expiry} and adding new leg`);
            }

            // Add the new future leg
            window.selectedLegs.push({
                id: legId,
                type: "FUT",
                strike: null,
                price: price,
                action: action,
                lots: 1,
                enabled: true,
                expiryDate: expiry,
                iv: 0
            });
        }

        updateLegsAndUrl();
        // To render the future buttons in chain modal
        buildFutureButtonsInChainModal(expiry);
    };


    function addPopularStrikeLeg(optType, action, strike) {
        const expiry = window.modalCurrentExpiry;

        // Get the proper strike using our utility (similar to increment/decrement logic)
        const properStrike = window.OptionsUtils.findNearestStrike(strike, window.chainData.options, expiry);
        if (!properStrike || properStrike <= 0) {
            showAlert("No valid strike found.");
            return;
        }

        // Construct leg id similar to our existing logic
        const legId = `${optType}-${properStrike}-${action}-${expiry}`;
        const existingLeg = window.selectedLegs.find(l => l.id === legId);
        if (existingLeg) {
            removeLeg(legId);
            return;
        }

        // Look up the option data for the given expiry and proper strike
        const option = window.chainData.options.find(opt => opt.expiryDate === expiry && opt.strikePrice == properStrike);
        if (!option) {
            showAlert("Option not found for the selected strike.");
            return;
        }

        // Extract last traded price and IV from the option data
        let ltp, iv;
        if (optType === 'CE') {
            ltp = option.CE.lastPrice;
            iv = option.CE.impliedVolatility || 20;
        } else {
            ltp = option.PE.lastPrice;
            iv = option.PE.impliedVolatility || 20;
        }

        if (!ltp || ltp <= 0) {
            showAlert("Cannot add leg with invalid price (0 or undefined)");
            return;
        }

        // Create the new leg object
        const newLeg = {
            id: legId,
            type: optType,
            strike: properStrike,
            price: ltp,
            action: action,
            lots: 1,
            enabled: true,
            expiryDate: expiry,
            iv: iv
        };

        window.selectedLegs.push(newLeg);

        // Use OptionsUtils to update the leg price (if additional calculation is needed)
        window.OptionsUtils.getStrikePriceForLeg(newLeg, window.chainData.options, window.futurePrices, window.currentSpotPrice);

        updateLegsAndUrl();
        renderChainTableWithBounds(null);
    }

    // Open chain modal when clicking "Add Leg" if chain data is loaded
    document.getElementById("open-chain-modal-btn").addEventListener("click", () => {
        if (chainData && chainData.options.length && chainData.expiryDates.length) {
            openChainModal();
            buildExpiryTabs(chainData.expiryDates, chainData.options);
        } else {
            showAlert("Please search for a symbol first.");
        }
    });

    function updateMultiplier(value) {
        // Parse the value and ensure it's at least 1
        const newValue = parseInt(value) || 1;
        window.currentMultiplier = Math.max(1, newValue);

        // Update the input value to reflect any corrections
        document.getElementById('multiplier-input').value = window.currentMultiplier;
        // Update the lot size display
        const lotSize = LotSizes[window.currentSymbol] || 1;
        document.getElementById('lot-size-display').textContent = lotSize * window.currentMultiplier;

        // Update the display and recalculate payoff
        updateLegsAndUrl();
    }

    function calculatePopularStrikes(options, expiry) {
        if (!options || !options.length) return [];

        // Filter options for the given expiry
        const expiryOptions = options.filter(opt => opt.expiryDate === expiry);

        // Combine CE and PE data for each strike
        const strikeData = expiryOptions.map(opt => ({
            strike: opt.strikePrice,
            totalOI: (opt.CE?.openInterest || 0) + (opt.PE?.openInterest || 0),
            ceOI: opt.CE?.openInterest || 0,
            peOI: opt.PE?.openInterest || 0
        }));

        // Sort by total OI and get top 5
        return strikeData
            .sort((a, b) => b.totalOI - a.totalOI)
            .slice(0, 5);
    }

    function renderPopularStrikes() {
        const section = document.getElementById("popular-strikes-section");
        const content = document.getElementById("popular-strikes-content");
        const expiryDisplay = document.getElementById("popular-strikes-expiry");

        // If no chain data or options, hide the section
        if (!window.chainData?.options?.length || !window.modalCurrentExpiry) {
            section.classList.add("hidden");
            return;
        }

        const popularStrikes = calculatePopularStrikes(window.chainData.options, window.modalCurrentExpiry);

        if (!popularStrikes.length) {
            section.classList.add("hidden");
            return;
        }

        section.classList.remove("hidden");
        expiryDisplay.textContent = `Expiry: ${window.modalCurrentExpiry}`;

        // Helper function to format large numbers
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num;
        }

        // Create the HTML for popular strikes with enhanced styling and hover action buttons.
        // Sort CE values in descending order
        let sortedCeStrikes = [...popularStrikes].sort((a, b) => b.ceOI - a.ceOI);

        // Sort PE values in descending order
        let sortedPeStrikes = [...popularStrikes].sort((a, b) => b.peOI - a.peOI);

        let html = `
    <div class="grid grid-cols-3 md:grid-cols-${sortedCeStrikes.length} gap-3">
        <!-- CE Blocks Row -->
        ${sortedCeStrikes.map((strike, index) => `
            <div class="relative group cursor-pointer">
                <div class="absolute inset-0 bg-gradient-to-r from-green-500/5 to-green-500/10 rounded-lg transition-all duration-300 group-hover:scale-105 group-hover:from-green-500/10"></div>
                <div class="relative border border-neutral-200 rounded-lg p-3 transition-all duration-300 group-hover:border-green-200 group-hover:shadow-sm">
                    <div class="font-medium text-neutral-800 mb-2">${strike.strike}</div>
                    <div class="flex items-center justify-between gap-2 text-xs">
                        <span class="text-neutral-500">CE</span>
                        <span class="font-medium text-green-600">${formatNumber(strike.ceOI)}</span>
                    </div>
                    <div class="absolute -top-1.5 -left-1.5 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center text-[10px] font-medium text-neutral-600">
                        ${index + 1}
                    </div>
                    <!-- Hover overlay with Buy and Sell buttons -->
                    <div class="absolute inset-0 flex items-center justify-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button onclick="addPopularStrikeLeg('CE','buy',${strike.strike})" class="bg-green-500 text-white px-1 py-0.5 text-xs rounded">B</button>
                        <button onclick="addPopularStrikeLeg('CE','sell',${strike.strike})" class="bg-red-500 text-white px-1 py-0.5 text-xs rounded">S</button>
                    </div>
                </div>
            </div>
        `).join('')}

        <!-- PE Blocks Row -->
        ${sortedPeStrikes.map((strike, index) => `
            <div class="relative group cursor-pointer">
                <div class="absolute inset-0 bg-gradient-to-r from-red-500/5 to-red-500/10 rounded-lg transition-all duration-300 group-hover:scale-105 group-hover:from-red-500/10"></div>
                <div class="relative border border-neutral-200 rounded-lg p-3 transition-all duration-300 group-hover:border-red-200 group-hover:shadow-sm">
                    <div class="font-medium text-neutral-800 mb-2">${strike.strike}</div>
                    <div class="flex items-center justify-between gap-2 text-xs">
                        <span class="text-neutral-500">PE</span>
                        <span class="font-medium text-red-600">${formatNumber(strike.peOI)}</span>
                    </div>
                    <div class="absolute -top-1.5 -left-1.5 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center text-[10px] font-medium text-neutral-600">
                        ${index + 1}
                    </div>
                    <!-- Hover overlay with Buy and Sell buttons -->
                    <div class="absolute inset-0 flex items-center justify-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button onclick="addPopularStrikeLeg('PE','buy',${strike.strike})" class="bg-green-500 text-white px-1 py-0.5 text-xs rounded">B</button>
                        <button onclick="addPopularStrikeLeg('PE','sell',${strike.strike})" class="bg-red-500 text-white px-1 py-0.5 text-xs rounded">S</button>
                    </div>
                </div>
            </div>
        `).join('')}
    </div>
`;
        content.innerHTML = html;
    }
</script>