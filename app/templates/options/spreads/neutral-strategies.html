<!-- Calendar Call Spread - Simpler strategy first -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('calendar-call-spread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/calendar-call-spread.png') }}" alt='Calendar Call Spread'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Calendar Call Spread</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy and sell calls at same strike but different expiries. Profit
                from
                time decay.</p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Limited
                    Loss</span>
                <span class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Limited
                    Profit</span>
            </div>
        </div>
    </div>
</div>

<!-- Calendar Put Spread -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('calendar-put-spread')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/calendar-put-spread.png') }}" alt='Calendar Put Spread'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Calendar Put Spread</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy and sell puts at same strike but different expiries. Profit
                from
                time decay.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Limited
                    Loss</span>
                <span class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Limited
                    Profit</span>
            </div>
        </div>
    </div>
</div>

<!-- Long Call Butterfly -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('long-call-butterfly')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/long-call-butterfly.png') }}" alt='Long Call Butterfly'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Long Call Butterfly</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy 1 ITM call, sell 2 ATM calls, buy 1 OTM call. Profit when
                price
                stays near middle strike.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Limited
                    Loss</span>
                <span class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Limited
                    Profit</span>
                <span class="text-[10px] font-medium bg-blue-50 text-blue-700 px-2 py-0.5 rounded-sm">Low
                    Cost</span>
            </div>
        </div>
    </div>
</div>

<!-- Long Put Butterfly -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('long-put-butterfly')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/long-put-butterfly.png') }}" alt='Long Put Butterfly'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Long Put Butterfly</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy 1 ITM put, sell 2 ATM puts, buy 1 OTM put. Profit when price
                stays near middle strike.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Limited
                    Loss</span>
                <span class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Limited
                    Profit</span>
                <span class="text-[10px] font-medium bg-blue-50 text-blue-700 px-2 py-0.5 rounded-sm">Low
                    Cost</span>
            </div>
        </div>
    </div>
</div>

<!-- Iron Butterfly -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('iron-butterfly')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/iron-butterfly.png') }}" alt='Iron Butterfly'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Iron Butterfly</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Sell ATM call & put, buy OTM call & ITM put. Profit from time
                decay
                and low volatility.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Limited
                    Loss</span>
                <span class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Limited
                    Profit</span>
                <span class="text-[10px] font-medium bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-sm">Net
                    Credit</span>
            </div>
        </div>
    </div>
</div>

<!-- Iron Condor -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('iron-condor')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/iron-condor.png') }}" alt='Iron Condor'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Iron Condor</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Short OTM call & put spreads. Profit if underlying stays in range.
            </p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-pink-50 text-pink-700 px-2 py-0.5 rounded-sm">Complex</span>
                <span class="text-[10px] font-medium bg-purple-50 text-purple-700 px-2 py-0.5 rounded-sm">Large
                    Margin</span>
            </div>
        </div>
    </div>
</div>

<!-- Bull Call Ladder -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('bull-call-ladder')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/bull-call-ladder.png') }}" alt='Bull Call Ladder'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Bull Call Ladder</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy lower strike call, sell middle & upper strike calls. Profit in
                moderate upward move.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Limited
                    Profit</span>
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">Unlimited
                    Loss</span>
                <span class="text-[10px] font-medium bg-orange-50 text-orange-700 px-2 py-0.5 rounded-sm">Complex</span>
            </div>
        </div>
    </div>
</div>

<!-- Bear Put Ladder -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('bear-put-ladder')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/bear-put-ladder.png') }}" alt='Bear Put Ladder'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Bear Put Ladder</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy higher strike put, sell middle & lower strike puts. Profit in
                moderate downward move.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Limited
                    Profit</span>
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">Nearly Unlimited
                    Loss</span>
                <span class="text-[10px] font-medium bg-orange-50 text-orange-700 px-2 py-0.5 rounded-sm">Complex</span>
            </div>
        </div>
    </div>
</div>

<!-- Long Call Condor -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('long-call-condor')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/long-call-condor.png') }}" alt='Long Call Condor'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Long Call Condor</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy lowest & highest strikes, sell middle strikes. Wider profit
                range
                than butterfly.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Limited
                    Loss</span>
                <span class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Limited
                    Profit</span>
                <span class="text-[10px] font-medium bg-blue-50 text-blue-700 px-2 py-0.5 rounded-sm">Wide
                    Range</span>
            </div>
        </div>
    </div>
</div>

<!-- Long Put Condor -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('long-put-condor')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/long-put-condor.png') }}" alt='Long Put Condor'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Long Put Condor</span>
            </div>
            <p class="text-[10px] text-gray-500 mb-2">Buy lowest & highest strikes, sell middle strikes. Wider profit
                range
                than butterfly.</p>

            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Limited
                    Loss</span>
                <span class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Limited
                    Profit</span>
                <span class="text-[10px] font-medium bg-blue-50 text-blue-700 px-2 py-0.5 rounded-sm">Wide
                    Range</span>
            </div>
        </div>
    </div>
</div>

<!-- Short Straddle -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('short-straddle')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/short-straddle.png') }}" alt='Short Straddle'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Short Straddle</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Write call & put at same strike. High premium, large risk if big
                move.</p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-sm">High
                    Premium</span>
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">High
                    Risk</span>
            </div>
        </div>
    </div>
</div>

<!-- Short Strangle -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('short-strangle')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/short-strangle.png') }}" alt='Short Strangle'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Short Strangle</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Sell OTM call & put at different strikes. Lower risk than straddle
                but
                less premium.</p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-sm">Premium
                    Income</span>
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">Unlimited
                    Loss</span>
                <span class="text-[10px] font-medium bg-blue-50 text-blue-700 px-2 py-0.5 rounded-sm">Wide
                    Range</span>
            </div>
        </div>
    </div>
</div>

<!-- Double Diagonal -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('double-diagonal')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/double-diagonal.png') }}" alt='Double Diagonal'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Double Diagonal</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Combines diagonal call & put spreads. Profit zone expands with
                time.
            </p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-green-50 text-green-700 px-2 py-0.5 rounded-sm">Limited
                    Loss</span>
                <span class="text-[10px] font-medium bg-yellow-50 text-yellow-700 px-2 py-0.5 rounded-sm">Limited
                    Profit</span>
                <span class="text-[10px] font-medium bg-purple-50 text-purple-700 px-2 py-0.5 rounded-sm">Expert</span>
            </div>
        </div>
    </div>
</div>

<!-- Short Guts -->
<div class="strategy-card border hover:bg-blue-50 hover:shadow-lg transition-all border-gray-200 rounded"
    onclick="loadNeutralStrategy('short-guts')">
    <div class="cursor-pointer">
        <div class="bg-gray-50 py-1">
            <img src="{{ cdn('/static/images/strategies/neutral-strategies/short-guts.png') }}" alt='Short Guts'
                class='h-[100px] mx-auto'>
        </div>
        <div class="p-3">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-sm">Short Guts</span>

            </div>
            <p class="text-[10px] text-gray-500 mb-2">Sell ITM call & put at different strikes. Similar to short
                straddle.
            </p>
            <div class="flex gap-1 flex-wrap">
                <span class="text-[10px] font-medium bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-sm">Premium
                    Income</span>
                <span class="text-[10px] font-medium bg-red-50 text-red-700 px-2 py-0.5 rounded-sm">Unlimited
                    Loss</span>
                <span class="text-[10px] font-medium bg-orange-50 text-orange-700 px-2 py-0.5 rounded-sm">High
                    Risk</span>
            </div>
        </div>
    </div>
</div>

<script>
    function loadNeutralStrategy(strategyType) {
        if (!window.currentSymbol) {
            showAlert("Please search for a symbol first");
            return;
        }
        selectedLegs = [];

        let atmStrike = null;
        let middleStrike = null;
        let upperStrike = null;
        let lowerStrike = null;
        let atmIndex = null;
        let longPutPrice = null;
        let shortPutPrice = null;
        let shortCallPrice = null;
        let longCallPrice = null;
        let condorStrikes = null;
        let buyLowerCallPrice = null;
        let lowerMidStrike = null;
        let upperMidStrike = null;
        let buyLowerPutPrice = null;
        let buyUpperPutPrice = null;

        switch (strategyType) {
            case 'calendar-call-spread':
                // Find ATM strike for both legs
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available expiries
                const expiries = getAvailableExpiries();
                if (expiries.length < 2) {
                    showAlert("Need at least two expiry dates for calendar spread.");
                    return;
                }

                // Use current expiry for short leg and next expiry for long leg
                const currentExpiryIndex = expiries.indexOf(modalCurrentExpiry);
                if (currentExpiryIndex === -1 || currentExpiryIndex === expiries.length - 1) {
                    showAlert("Please select an expiry that has a later expiry date available.");
                    return;
                }

                const nextExpiry = expiries[currentExpiryIndex + 1];

                if (!nextExpiry) {
                    showAlert("Unable to determine next expiry. Please load chain data first.");
                    return;
                }

                // Get option prices for both legs
                shortCallPrice = getOptionPrice('CE', atmStrike, modalCurrentExpiry);
                longCallPrice = getOptionPrice('CE', atmStrike, nextExpiry);

                if (!shortCallPrice || !longCallPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add short call leg (near expiry)
                selectedLegs.push({
                    id: `CE-${atmStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: shortCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long call leg (far expiry)
                selectedLegs.push({
                    id: `CE-${atmStrike}-buy-${nextExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: longCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: nextExpiry
                });

                updateLegsAndUrl();
                break;

            case 'calendar-put-spread':
                // Find ATM strike for both legs
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available expiries
                const putExpiries = getAvailableExpiries();
                if (putExpiries.length < 2) {
                    showAlert("Need at least two expiry dates for calendar spread.");
                    return;
                }

                // Use current expiry for short leg and next expiry for long leg
                const currentPutExpiryIndex = putExpiries.indexOf(modalCurrentExpiry);
                if (currentPutExpiryIndex === -1 || currentPutExpiryIndex === putExpiries.length - 1) {
                    showAlert("Please select an expiry that has a later expiry date available.");
                    return;
                }

                const nextPutExpiry = putExpiries[currentPutExpiryIndex + 1];

                // Get option prices for both legs
                shortPutPrice = getOptionPrice('PE', atmStrike, modalCurrentExpiry);
                longPutPrice = getOptionPrice('PE', atmStrike, nextPutExpiry);

                if (!shortPutPrice || !longPutPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add short put leg (near expiry)
                selectedLegs.push({
                    id: `PE-${atmStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: atmStrike,
                    price: shortPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long put leg (far expiry)
                selectedLegs.push({
                    id: `PE-${atmStrike}-buy-${nextPutExpiry}`,
                    type: 'PE',
                    strike: atmStrike,
                    price: longPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: nextPutExpiry
                });

                updateLegsAndUrl();
                break;

            case 'long-call-butterfly':
                // Find ATM strike as reference point
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const butterflyStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                atmIndex = butterflyStrikes.indexOf(atmStrike);

                if (atmIndex === -1 || atmIndex < 1 || atmIndex > butterflyStrikes.length - 2) {
                    showAlert("Not enough strikes available for butterfly strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for lower leg
                const lowerStrikeDelta = butterflyStrikes[atmIndex] - butterflyStrikes[atmIndex - 1];
                const lowerStrikeDistance = calculateLegDistance(lowerStrikeDelta, window.currentSymbol, 'long-call-butterfly');

                // Check if we have enough strikes available for lower leg
                if (atmIndex - lowerStrikeDistance < 0) {
                    showAlert("Not enough strikes available for long call butterfly with the calculated distance (lower leg).");
                    return;
                }

                // Lower strike is below ATM based on calculated distance
                lowerStrike = butterflyStrikes[atmIndex - lowerStrikeDistance];  // ITM

                // Middle strike is ATM
                middleStrike = atmStrike;

                // Calculate the appropriate strike distance for upper leg
                const upperStrikeDelta = butterflyStrikes[atmIndex + 1] - butterflyStrikes[atmIndex];
                const upperStrikeDistance = calculateLegDistance(upperStrikeDelta, window.currentSymbol, 'long-call-butterfly');

                // Check if we have enough strikes available for upper leg
                if (atmIndex + upperStrikeDistance >= butterflyStrikes.length) {
                    showAlert("Not enough strikes available for long call butterfly with the calculated distance (upper leg).");
                    return;
                }

                // Upper strike is above ATM based on calculated distance
                upperStrike = butterflyStrikes[atmIndex + upperStrikeDistance];  // OTM

                // Get option prices for all legs
                const lowerCallPrice = getOptionPrice('CE', lowerStrike);
                const middleCallPrice = getOptionPrice('CE', middleStrike);
                const upperCallPrice = getOptionPrice('CE', upperStrike);

                if (!lowerCallPrice || !middleCallPrice || !upperCallPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add lower strike long call
                selectedLegs.push({
                    id: `CE-${lowerStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: lowerStrike,
                    price: lowerCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add middle strike short calls (2 contracts)
                selectedLegs.push({
                    id: `CE-${middleStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: middleStrike,
                    price: middleCallPrice,
                    action: 'sell',
                    lots: 2,  // Selling 2 contracts
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add upper strike long call
                selectedLegs.push({
                    id: `CE-${upperStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: upperStrike,
                    price: upperCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'long-put-butterfly':
                // Find ATM strike as reference point
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const putButterflyStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const putAtmIndex = putButterflyStrikes.indexOf(atmStrike);

                if (putAtmIndex === -1 || putAtmIndex < 1 || putAtmIndex > putButterflyStrikes.length - 2) {
                    showAlert("Not enough strikes available for butterfly strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for lower leg
                const lowerPutDelta = putButterflyStrikes[putAtmIndex] - putButterflyStrikes[putAtmIndex - 1];
                const lowerPutDistance = calculateLegDistance(lowerPutDelta, window.currentSymbol, 'long-put-butterfly');

                // Check if we have enough strikes available for lower leg
                if (putAtmIndex - lowerPutDistance < 0) {
                    showAlert("Not enough strikes available for long put butterfly with the calculated distance (lower leg).");
                    return;
                }

                // Lower strike is below ATM based on calculated distance
                const lowerPutStrike = putButterflyStrikes[putAtmIndex - lowerPutDistance];  // ITM

                // Middle strike is ATM
                const middlePutStrike = atmStrike;

                // Calculate the appropriate strike distance for upper leg
                const upperPutDelta = putButterflyStrikes[putAtmIndex + 1] - putButterflyStrikes[putAtmIndex];
                const upperPutDistance = calculateLegDistance(upperPutDelta, window.currentSymbol, 'long-put-butterfly');

                // Check if we have enough strikes available for upper leg
                if (putAtmIndex + upperPutDistance >= putButterflyStrikes.length) {
                    showAlert("Not enough strikes available for long put butterfly with the calculated distance (upper leg).");
                    return;
                }

                // Upper strike is above ATM based on calculated distance
                const upperPutStrike = putButterflyStrikes[putAtmIndex + upperPutDistance];  // OTM

                // Get option prices for all legs
                const lowerPutPrice = getOptionPrice('PE', lowerPutStrike);
                const middlePutPrice = getOptionPrice('PE', middlePutStrike);
                const upperPutPrice = getOptionPrice('PE', upperPutStrike);

                if (!lowerPutPrice || !middlePutPrice || !upperPutPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add lower strike long put
                selectedLegs.push({
                    id: `PE-${lowerPutStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: lowerPutStrike,
                    price: lowerPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add middle strike short puts (2 contracts)
                selectedLegs.push({
                    id: `PE-${middlePutStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: middlePutStrike,
                    price: middlePutPrice,
                    action: 'sell',
                    lots: 2,  // Selling 2 contracts
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add upper strike long put
                selectedLegs.push({
                    id: `PE-${upperPutStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: upperPutStrike,
                    price: upperPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'iron-butterfly':
                // Find ATM strike as reference point
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const ironButterflyStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                atmIndex = ironButterflyStrikes.indexOf(atmStrike);

                if (atmIndex === -1 || atmIndex < 1 || atmIndex > ironButterflyStrikes.length - 2) {
                    showAlert("Not enough strikes available for iron butterfly strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for lower leg
                const ironButterflyLowerDelta = ironButterflyStrikes[atmIndex] - ironButterflyStrikes[atmIndex - 1];
                const ironButterflyLowerDistance = calculateLegDistance(ironButterflyLowerDelta, window.currentSymbol, 'iron-butterfly');

                // Check if we have enough strikes available for lower leg
                if (atmIndex - ironButterflyLowerDistance < 0) {
                    showAlert("Not enough strikes available for iron butterfly with the calculated distance (lower leg).");
                    return;
                }

                // Lower strike is below ATM based on calculated distance
                lowerStrike = ironButterflyStrikes[atmIndex - ironButterflyLowerDistance];  // Lower strike - long put

                // Middle strike is ATM
                middleStrike = atmStrike;  // ATM - short put and short call

                // Calculate the appropriate strike distance for upper leg
                const ironButterflyUpperDelta = ironButterflyStrikes[atmIndex + 1] - ironButterflyStrikes[atmIndex];
                const ironButterflyUpperDistance = calculateLegDistance(ironButterflyUpperDelta, window.currentSymbol, 'iron-butterfly');

                // Check if we have enough strikes available for upper leg
                if (atmIndex + ironButterflyUpperDistance >= ironButterflyStrikes.length) {
                    showAlert("Not enough strikes available for iron butterfly with the calculated distance (upper leg).");
                    return;
                }

                // Upper strike is above ATM based on calculated distance
                upperStrike = ironButterflyStrikes[atmIndex + ironButterflyUpperDistance];  // Upper strike - long call

                // Get option prices for all legs
                longPutPrice = getOptionPrice('PE', lowerStrike);
                shortPutPrice = getOptionPrice('PE', middleStrike);
                shortCallPrice = getOptionPrice('CE', middleStrike);
                longCallPrice = getOptionPrice('CE', upperStrike);

                if (!longPutPrice || !shortPutPrice || !shortCallPrice || !longCallPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add long put leg (lowest strike)
                selectedLegs.push({
                    id: `PE-${lowerStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: lowerStrike,
                    price: longPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short put leg (ATM)
                selectedLegs.push({
                    id: `PE-${middleStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: middleStrike,
                    price: shortPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short call leg (ATM)
                selectedLegs.push({
                    id: `CE-${middleStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: middleStrike,
                    price: shortCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add long call leg (highest strike)
                selectedLegs.push({
                    id: `CE-${upperStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: upperStrike,
                    price: longCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'iron-condor':
                // Find ATM strike as reference point
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get all available strikes
                const ironCondorStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const atmIronCondorIndex = ironCondorStrikes.indexOf(atmStrike);

                if (atmIronCondorIndex === -1 || atmIronCondorIndex < 2 || atmIronCondorIndex > ironCondorStrikes.length - 3) {
                    showAlert("Not enough strikes available for iron condor strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for put spread legs
                const ironCondorPutDelta = ironCondorStrikes[atmIronCondorIndex] - ironCondorStrikes[atmIronCondorIndex - 1];
                const ironCondorPutDistance = calculateLegDistance(ironCondorPutDelta, window.currentSymbol, 'iron-condor');

                // Check if we have enough strikes available for put spread legs
                if (atmIronCondorIndex - ironCondorPutDistance < 0 || atmIronCondorIndex - 2 * ironCondorPutDistance < 0) {
                    showAlert("Not enough strikes available for iron condor with the calculated distance (put spread legs).");
                    return;
                }

                // Put spread legs (below ATM)
                const shortPutStrike = ironCondorStrikes[atmIronCondorIndex - ironCondorPutDistance];  // Lower middle - short put
                const longPutStrike = ironCondorStrikes[atmIronCondorIndex - 2 * ironCondorPutDistance];  // Lowest strike - long put

                // Calculate the appropriate strike distance for call spread legs
                const ironCondorCallDelta = ironCondorStrikes[atmIronCondorIndex + 1] - ironCondorStrikes[atmIronCondorIndex];
                const ironCondorCallDistance = calculateLegDistance(ironCondorCallDelta, window.currentSymbol, 'iron-condor');

                // Check if we have enough strikes available for call spread legs
                if (atmIronCondorIndex + ironCondorCallDistance >= ironCondorStrikes.length ||
                    atmIronCondorIndex + 2 * ironCondorCallDistance >= ironCondorStrikes.length) {
                    showAlert("Not enough strikes available for iron condor with the calculated distance (call spread legs).");
                    return;
                }

                // Call spread legs (above ATM)
                const shortCallStrike = ironCondorStrikes[atmIronCondorIndex + ironCondorCallDistance];  // Upper middle - short call
                const longCallStrike = ironCondorStrikes[atmIronCondorIndex + 2 * ironCondorCallDistance];   // Highest strike - long call

                // Get option prices for all legs
                const longPutPrice = getOptionPrice('PE', longPutStrike);
                const shortPutPrice = getOptionPrice('PE', shortPutStrike);
                const shortCallPrice = getOptionPrice('CE', shortCallStrike);
                const longCallPrice = getOptionPrice('CE', longCallStrike);

                if (!longPutPrice || !shortPutPrice || !shortCallPrice || !longCallPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add all four legs
                // 1. Long Put (lowest strike)
                selectedLegs.push({
                    id: `PE-${longPutStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: longPutStrike,
                    price: longPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // 2. Short Put
                selectedLegs.push({
                    id: `PE-${shortPutStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: shortPutStrike,
                    price: shortPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // 3. Short Call
                selectedLegs.push({
                    id: `CE-${shortCallStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: shortCallStrike,
                    price: shortCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // 4. Long Call (highest strike)
                selectedLegs.push({
                    id: `CE-${longCallStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: longCallStrike,
                    price: longCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'bull-call-ladder':
                // Find ATM strike as reference point
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const ladderStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                atmIndex = ladderStrikes.indexOf(atmStrike);

                if (atmIndex === -1 || atmIndex < 1 || atmIndex > ladderStrikes.length - 2) {
                    showAlert("Not enough strikes available for ladder strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for lower leg
                const ladderLowerDelta = ladderStrikes[atmIndex] - ladderStrikes[atmIndex - 1];
                const ladderLowerDistance = calculateLegDistance(ladderLowerDelta, window.currentSymbol, 'bull-call-ladder');

                // Check if we have enough strikes available for lower leg
                if (atmIndex - ladderLowerDistance < 0) {
                    showAlert("Not enough strikes available for bull call ladder with the calculated distance (lower leg).");
                    return;
                }

                // Lower strike is below ATM based on calculated distance
                lowerStrike = ladderStrikes[atmIndex - ladderLowerDistance];  // Lower strike - Buy

                // Middle strike is ATM
                middleStrike = atmStrike;  // ATM - Sell

                // Calculate the appropriate strike distance for upper leg
                const ladderUpperDelta = ladderStrikes[atmIndex + 1] - ladderStrikes[atmIndex];
                const ladderUpperDistance = calculateLegDistance(ladderUpperDelta, window.currentSymbol, 'bull-call-ladder');

                // Check if we have enough strikes available for upper leg
                if (atmIndex + ladderUpperDistance >= ladderStrikes.length) {
                    showAlert("Not enough strikes available for bull call ladder with the calculated distance (upper leg).");
                    return;
                }

                // Upper strike is above ATM based on calculated distance
                upperStrike = ladderStrikes[atmIndex + ladderUpperDistance];  // Upper strike - Sell

                // Get option prices for all legs
                buyLowerCallPrice = getOptionPrice('CE', lowerStrike);
                const sellMiddleCallPrice = getOptionPrice('CE', middleStrike);
                const sellUpperCallPrice = getOptionPrice('CE', upperStrike);

                if (!buyLowerCallPrice || !sellMiddleCallPrice || !sellUpperCallPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add long call leg (lowest strike)
                selectedLegs.push({
                    id: `CE-${lowerStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: lowerStrike,
                    price: buyLowerCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add middle short call leg
                selectedLegs.push({
                    id: `CE-${middleStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: middleStrike,
                    price: sellMiddleCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add upper short call leg
                selectedLegs.push({
                    id: `CE-${upperStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: upperStrike,
                    price: sellUpperCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'bear-put-ladder':
                // Find ATM strike as reference point
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const putLadderStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                atmIndex = putLadderStrikes.indexOf(atmStrike);

                if (atmIndex === -1 || atmIndex < 1 || atmIndex > putLadderStrikes.length - 2) {
                    showAlert("Not enough strikes available for ladder strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for lower leg
                const putLadderLowerDelta = putLadderStrikes[atmIndex] - putLadderStrikes[atmIndex - 1];
                const putLadderLowerDistance = calculateLegDistance(putLadderLowerDelta, window.currentSymbol, 'bear-put-ladder');

                // Check if we have enough strikes available for lower leg
                if (atmIndex - putLadderLowerDistance < 0) {
                    showAlert("Not enough strikes available for bear put ladder with the calculated distance (lower leg).");
                    return;
                }

                // Lower strike is below ATM based on calculated distance
                lowerStrike = putLadderStrikes[atmIndex - putLadderLowerDistance];  // Lower strike - Sell

                // Middle strike is ATM
                middleStrike = atmStrike;  // ATM - Sell

                // Calculate the appropriate strike distance for upper leg
                const putLadderUpperDelta = putLadderStrikes[atmIndex + 1] - putLadderStrikes[atmIndex];
                const putLadderUpperDistance = calculateLegDistance(putLadderUpperDelta, window.currentSymbol, 'bear-put-ladder');

                // Check if we have enough strikes available for upper leg
                if (atmIndex + putLadderUpperDistance >= putLadderStrikes.length) {
                    showAlert("Not enough strikes available for bear put ladder with the calculated distance (upper leg).");
                    return;
                }

                // Upper strike is above ATM based on calculated distance
                upperStrike = putLadderStrikes[atmIndex + putLadderUpperDistance];  // Upper strike - Buy

                // Get option prices for all legs
                const sellLowerPutPrice = getOptionPrice('PE', lowerStrike);
                const sellMiddlePutPrice = getOptionPrice('PE', middleStrike);
                buyUpperPutPrice = getOptionPrice('PE', upperStrike);

                if (!sellLowerPutPrice || !sellMiddlePutPrice || !buyUpperPutPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add lower short put leg
                selectedLegs.push({
                    id: `PE-${lowerStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: lowerStrike,
                    price: sellLowerPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add middle short put leg
                selectedLegs.push({
                    id: `PE-${middleStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: middleStrike,
                    price: sellMiddlePutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add upper long put leg
                selectedLegs.push({
                    id: `PE-${upperStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: upperStrike,
                    price: buyUpperPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'long-call-condor':
                // Find ATM strike as reference point
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                condorStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                atmIndex = condorStrikes.indexOf(atmStrike);

                if (atmIndex === -1 || atmIndex < 2 || atmIndex > condorStrikes.length - 3) {
                    showAlert("Not enough strikes available for condor strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for condor legs
                const condorDelta = condorStrikes[atmIndex] - condorStrikes[atmIndex - 1];
                const condorDistance = calculateLegDistance(condorDelta, window.currentSymbol, 'long-call-condor');

                // Check if we have enough strikes available for all legs
                if (atmIndex - condorDistance < 0 || atmIndex - 2 * condorDistance < 0 ||
                    atmIndex + condorDistance >= condorStrikes.length || atmIndex + 2 * condorDistance >= condorStrikes.length) {
                    showAlert("Not enough strikes available for long call condor with the calculated distance.");
                    return;
                }

                // Select strikes for all 4 legs based on calculated distance
                lowerStrike = condorStrikes[atmIndex - 2 * condorDistance];     // Lowest strike - Buy
                lowerMidStrike = condorStrikes[atmIndex - condorDistance];      // Lower middle - Sell
                upperMidStrike = condorStrikes[atmIndex + condorDistance];      // Upper middle - Sell
                upperStrike = condorStrikes[atmIndex + 2 * condorDistance];     // Highest strike - Buy

                // Get option prices for all legs
                buyLowerCallPrice = getOptionPrice('CE', lowerStrike);
                const sellLowerMidCallPrice = getOptionPrice('CE', lowerMidStrike);
                const sellUpperMidCallPrice = getOptionPrice('CE', upperMidStrike);
                const buyUpperCallPrice = getOptionPrice('CE', upperStrike);

                if (!buyLowerCallPrice || !sellLowerMidCallPrice || !sellUpperMidCallPrice || !buyUpperCallPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add lowest strike long call
                selectedLegs.push({
                    id: `CE-${lowerStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: lowerStrike,
                    price: buyLowerCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add lower middle short call
                selectedLegs.push({
                    id: `CE-${lowerMidStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: lowerMidStrike,
                    price: sellLowerMidCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add upper middle short call
                selectedLegs.push({
                    id: `CE-${upperMidStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: upperMidStrike,
                    price: sellUpperMidCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add highest strike long call
                selectedLegs.push({
                    id: `CE-${upperStrike}-buy-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: upperStrike,
                    price: buyUpperCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'long-put-condor':
                // Find ATM strike as reference point
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const putCondorStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                atmIndex = putCondorStrikes.indexOf(atmStrike);

                if (atmIndex === -1 || atmIndex < 2 || atmIndex > putCondorStrikes.length - 3) {
                    showAlert("Not enough strikes available for condor strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for condor legs
                const putCondorDelta = putCondorStrikes[atmIndex] - putCondorStrikes[atmIndex - 1];
                const putCondorDistance = calculateLegDistance(putCondorDelta, window.currentSymbol, 'long-put-condor');

                // Check if we have enough strikes available for all legs
                if (atmIndex - putCondorDistance < 0 || atmIndex - 2 * putCondorDistance < 0 ||
                    atmIndex + putCondorDistance >= putCondorStrikes.length || atmIndex + 2 * putCondorDistance >= putCondorStrikes.length) {
                    showAlert("Not enough strikes available for long put condor with the calculated distance.");
                    return;
                }

                // Select strikes for all 4 legs based on calculated distance
                lowerStrike = putCondorStrikes[atmIndex - 2 * putCondorDistance];     // Lowest strike - Buy
                lowerMidStrike = putCondorStrikes[atmIndex - putCondorDistance];      // Lower middle - Sell
                upperMidStrike = putCondorStrikes[atmIndex + putCondorDistance];      // Upper middle - Sell
                upperStrike = putCondorStrikes[atmIndex + 2 * putCondorDistance];     // Highest strike - Buy

                // Get option prices for all legs
                buyLowerPutPrice = getOptionPrice('PE', lowerStrike);
                const sellLowerMidPutPrice = getOptionPrice('PE', lowerMidStrike);
                const sellUpperMidPutPrice = getOptionPrice('PE', upperMidStrike);
                buyUpperPutPrice = getOptionPrice('PE', upperStrike);

                if (!buyLowerPutPrice || !sellLowerMidPutPrice || !sellUpperMidPutPrice || !buyUpperPutPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add lowest strike long put
                selectedLegs.push({
                    id: `PE-${lowerStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: lowerStrike,
                    price: buyLowerPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add lower middle short put
                selectedLegs.push({
                    id: `PE-${lowerMidStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: lowerMidStrike,
                    price: sellLowerMidPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add upper middle short put
                selectedLegs.push({
                    id: `PE-${upperMidStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: upperMidStrike,
                    price: sellUpperMidPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add highest strike long put
                selectedLegs.push({
                    id: `PE-${upperStrike}-buy-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: upperStrike,
                    price: buyUpperPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'short-straddle':
                // Find ATM strike for both legs
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get option prices for both call and put
                const straddleCallPrice = getOptionPrice('CE', atmStrike);
                const straddlePutPrice = getOptionPrice('PE', atmStrike);

                if (!straddleCallPrice || !straddlePutPrice) {
                    showAlert("Unable to get option prices. Please load chain data first.");
                    return;
                }

                // Add short call leg
                selectedLegs.push({
                    id: `CE-${atmStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: atmStrike,
                    price: straddleCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short put leg
                selectedLegs.push({
                    id: `PE-${atmStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: atmStrike,
                    price: straddlePutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'short-strangle':
                // Find ATM strike as reference point
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const strangleStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                atmIndex = strangleStrikes.indexOf(atmStrike);

                if (atmIndex === -1 || atmIndex < 1 || atmIndex > strangleStrikes.length - 2) {
                    showAlert("Not enough strikes available for strangle strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for put leg
                const stranglePutDelta = strangleStrikes[atmIndex] - strangleStrikes[atmIndex - 1];
                const stranglePutDistance = calculateLegDistance(stranglePutDelta, window.currentSymbol, 'short-strangle');

                // Check if we have enough strikes available for put leg
                if (atmIndex - stranglePutDistance < 0) {
                    showAlert("Not enough strikes available for short strangle with the calculated distance (put leg).");
                    return;
                }

                // OTM Put strike based on calculated distance
                const putStrike = strangleStrikes[atmIndex - stranglePutDistance];

                // Calculate the appropriate strike distance for call leg
                const strangleCallDelta = strangleStrikes[atmIndex + 1] - strangleStrikes[atmIndex];
                const strangleCallDistance = calculateLegDistance(strangleCallDelta, window.currentSymbol, 'short-strangle');

                // Check if we have enough strikes available for call leg
                if (atmIndex + strangleCallDistance >= strangleStrikes.length) {
                    showAlert("Not enough strikes available for short strangle with the calculated distance (call leg).");
                    return;
                }

                // OTM Call strike based on calculated distance
                const callStrike = strangleStrikes[atmIndex + strangleCallDistance];

                // Get option prices for both legs
                const strangShortPutPrice = getOptionPrice('PE', putStrike);
                const strangShortCallPrice = getOptionPrice('CE', callStrike);

                if (!strangShortPutPrice || !strangShortCallPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add short put leg
                selectedLegs.push({
                    id: `PE-${putStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: putStrike,
                    price: strangShortPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short call leg
                selectedLegs.push({
                    id: `CE-${callStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: callStrike,
                    price: strangShortCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;

            case 'double-diagonal':
                // Find ATM strike as reference point
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes and expiries
                const diagonalStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                const diagonalExpiries = getAvailableExpiries();
                atmIndex = diagonalStrikes.indexOf(atmStrike);
                const diagonalCurrentExpiryIndex = diagonalExpiries.indexOf(modalCurrentExpiry);

                if (atmIndex === -1 || atmIndex < 1 || atmIndex > diagonalStrikes.length - 2 ||
                    diagonalCurrentExpiryIndex === -1 || diagonalCurrentExpiryIndex === diagonalExpiries.length - 1) {
                    showAlert("Not enough strikes/expiries available for double diagonal strategy.");
                    return;
                }

                // Select strikes and expiries
                const nearExpiry = modalCurrentExpiry;
                const farExpiry = diagonalExpiries[diagonalCurrentExpiryIndex + 1];

                // Calculate the appropriate strike distance for put side
                const diagonalPutDelta = diagonalStrikes[atmIndex] - diagonalStrikes[atmIndex - 1];
                const diagonalPutDistance = calculateLegDistance(diagonalPutDelta, window.currentSymbol, 'double-diagonal');

                // Check if we have enough strikes available for put side
                if (atmIndex - diagonalPutDistance < 0 || atmIndex - 2 * diagonalPutDistance < 0) {
                    showAlert("Not enough strikes available for double diagonal with the calculated distance (put side).");
                    return;
                }

                // Lower strikes for put side based on calculated distance
                const nearPutStrike = diagonalStrikes[atmIndex - diagonalPutDistance];  // OTM Put - Near term
                const farPutStrike = diagonalStrikes[atmIndex - 2 * diagonalPutDistance];   // Further OTM Put - Far term

                // Calculate the appropriate strike distance for call side
                const diagonalCallDelta = diagonalStrikes[atmIndex + 1] - diagonalStrikes[atmIndex];
                const diagonalCallDistance = calculateLegDistance(diagonalCallDelta, window.currentSymbol, 'double-diagonal');

                // Check if we have enough strikes available for call side
                if (atmIndex + diagonalCallDistance >= diagonalStrikes.length ||
                    atmIndex + 2 * diagonalCallDistance >= diagonalStrikes.length) {
                    showAlert("Not enough strikes available for double diagonal with the calculated distance (call side).");
                    return;
                }

                // Higher strikes for call side based on calculated distance
                const nearCallStrike = diagonalStrikes[atmIndex + diagonalCallDistance]; // OTM Call - Near term
                const farCallStrike = diagonalStrikes[atmIndex + 2 * diagonalCallDistance];  // Further OTM Call - Far term

                // Get option prices
                const shortNearPutPrice = getOptionPrice('PE', nearPutStrike, nearExpiry);
                const longFarPutPrice = getOptionPrice('PE', farPutStrike, farExpiry);
                const shortNearCallPrice = getOptionPrice('CE', nearCallStrike, nearExpiry);
                const longFarCallPrice = getOptionPrice('CE', farCallStrike, farExpiry);

                if (!shortNearPutPrice || !longFarPutPrice || !shortNearCallPrice || !longFarCallPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add short near-term put
                selectedLegs.push({
                    id: `PE-${nearPutStrike}-sell-${nearExpiry}`,
                    type: 'PE',
                    strike: nearPutStrike,
                    price: shortNearPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: nearExpiry
                });

                // Add long far-term put
                selectedLegs.push({
                    id: `PE-${farPutStrike}-buy-${farExpiry}`,
                    type: 'PE',
                    strike: farPutStrike,
                    price: longFarPutPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: farExpiry
                });

                // Add short near-term call
                selectedLegs.push({
                    id: `CE-${nearCallStrike}-sell-${nearExpiry}`,
                    type: 'CE',
                    strike: nearCallStrike,
                    price: shortNearCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: nearExpiry
                });

                // Add long far-term call
                selectedLegs.push({
                    id: `CE-${farCallStrike}-buy-${farExpiry}`,
                    type: 'CE',
                    strike: farCallStrike,
                    price: longFarCallPrice,
                    action: 'buy',
                    lots: 1,
                    enabled: true,
                    expiryDate: farExpiry
                });

                updateLegsAndUrl();
                break;

            case 'short-guts':
                // Find ATM strike as reference point
                atmStrike = findAtmStrike();
                if (!atmStrike) {
                    showAlert("Unable to determine ATM strike. Please load chain data first.");
                    return;
                }

                // Get available strikes
                const gutsStrikes = window.OptionsUtils.getAvailableStrikes(window.chainData.options, modalCurrentExpiry);
                atmIndex = gutsStrikes.indexOf(atmStrike);

                if (atmIndex === -1 || atmIndex < 1 || atmIndex > gutsStrikes.length - 2) {
                    showAlert("Not enough strikes available for guts strategy.");
                    return;
                }

                // Calculate the appropriate strike distance for lower leg (ITM Call)
                const gutsLowerDelta = gutsStrikes[atmIndex] - gutsStrikes[atmIndex - 1];
                const gutsLowerDistance = calculateLegDistance(gutsLowerDelta, window.currentSymbol, 'short-guts');

                // Check if we have enough strikes available for lower leg
                if (atmIndex - gutsLowerDistance < 0) {
                    showAlert("Not enough strikes available for short guts with the calculated distance (lower leg).");
                    return;
                }

                // For a Short Guts, the call should be ITM (below ATM) based on calculated distance
                const lowerGutsStrike = gutsStrikes[atmIndex - gutsLowerDistance];  // ITM Call (below current price)

                // Calculate the appropriate strike distance for upper leg (ITM Put)
                const gutsUpperDelta = gutsStrikes[atmIndex + 1] - gutsStrikes[atmIndex];
                const gutsUpperDistance = calculateLegDistance(gutsUpperDelta, window.currentSymbol, 'short-guts');

                // Check if we have enough strikes available for upper leg
                if (atmIndex + gutsUpperDistance >= gutsStrikes.length) {
                    showAlert("Not enough strikes available for short guts with the calculated distance (upper leg).");
                    return;
                }

                // The put should be ITM (above ATM) based on calculated distance
                const upperGutsStrike = gutsStrikes[atmIndex + gutsUpperDistance];  // ITM Put (above current price)

                // Get option prices for both legs
                const gutsShortCallPrice = getOptionPrice('CE', lowerGutsStrike);
                const gutsShortPutPrice = getOptionPrice('PE', upperGutsStrike);

                if (!gutsShortCallPrice || !gutsShortPutPrice) {
                    showAlert("Unable to get option prices. Please check data availability.");
                    return;
                }

                // Add short call leg (ITM - lower strike)
                selectedLegs.push({
                    id: `CE-${lowerGutsStrike}-sell-${modalCurrentExpiry}`,
                    type: 'CE',
                    strike: lowerGutsStrike,
                    price: gutsShortCallPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                // Add short put leg (ITM - higher strike)
                selectedLegs.push({
                    id: `PE-${upperGutsStrike}-sell-${modalCurrentExpiry}`,
                    type: 'PE',
                    strike: upperGutsStrike,
                    price: gutsShortPutPrice,
                    action: 'sell',
                    lots: 1,
                    enabled: true,
                    expiryDate: modalCurrentExpiry
                });

                updateLegsAndUrl();
                break;
        }
    }

</script>