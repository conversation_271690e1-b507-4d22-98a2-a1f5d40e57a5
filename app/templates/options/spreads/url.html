<script>
    window.updateUrlParams = function () {
        const newUrl = window.OptionsUtils.legsToUrlParams(
            window.selectedLegs,
            window.currentSymbol,
            window.currentMultiplier,
            window.backtestDate
        );
        window.history.pushState({}, '', newUrl);
    }

    function getUrlParams() {
        const params = new URLSearchParams(window.location.search);
        const legsParam = params.get('l');
        let legs = [];

        // Load multiplier
        const multiplier = params.get('multiplier');

        window.currentMultiplier = parseInt(multiplier) || 1;
        const multiplierInput = document.getElementById('multiplier-input');
        if (multiplierInput) {
            multiplierInput.value = window.currentMultiplier;
        }

        // Load backtesting date if present
        const backtestDate = params.get('backtest');
        console.log("Backtesting date from URL:", backtestDate);
        document.addEventListener("DOMContentLoaded", () => {
            if (backtestDate) {
                window.setBacktestingDate(backtestDate);
            }
        });

        if (legsParam) {
            try {
                legs = JSON.parse(decodeURIComponent(legsParam));
                // Validate each leg has required fields
                legs = legs.filter(leg =>
                    leg.type &&
                    leg.action &&
                    (leg.type === 'FUT' || leg.strike) &&
                    typeof leg.price === 'number' &&
                    leg.expiryDate  // Make sure expiry exists
                );
            } catch (e) {
                console.error('Error parsing legs from URL:', e);
                legs = [];
            }
        }

        return {
            symbol: params.get('s'),
            legs: legs,
            backtestDate: backtestDate
        };
    }

    function initURLParams() {
        // Load initial state from URL params
        const urlParams = getUrlParams();

        // Set initial symbol
        window.currentSymbol = urlParams.symbol;
        updateStrategiesAndPayoffContainerVisibility();
        if (!window.currentSymbol) {
            return;
        }
        const searchInput = document.getElementById("stock-input");
        searchInput.value = currentSymbol;

        document.addEventListener('DOMContentLoaded', () => {
            window.handleSearchInputChange(currentSymbol);

            // Set up backtesting UI if a date was provided
            if (urlParams.backtestDate) {
                document.addEventListener('DOMContentLoaded', () => {
                    // Wait for backtesting elements to be available
                    setTimeout(() => {
                        const backtestingCheckbox = document.getElementById('enable-backtesting');
                        const backtestingLabel = document.getElementById('backtesting-label');
                        const selectDateBtn = document.getElementById('select-date-btn');
                        const selectedDateSpan = document.getElementById('selected-date');

                        if (backtestingCheckbox && backtestingLabel && selectDateBtn && selectedDateSpan) {
                            // Format date for display (YYYY-MM-DD to DD-MM-YYYY)
                            const dateObj = new Date(urlParams.backtestDate);
                            const dd = String(dateObj.getDate()).padStart(2, '0');
                            const mm = String(dateObj.getMonth() + 1).padStart(2, '0');
                            const yyyy = dateObj.getFullYear();
                            const formattedDate = `${dd}-${mm}-${yyyy}`;

                            // Update UI
                            selectedDateSpan.textContent = formattedDate;
                            backtestingCheckbox.classList.remove('hidden');
                            backtestingLabel.classList.remove('hidden');
                            backtestingCheckbox.checked = true;
                            selectDateBtn.textContent = 'Change Date';
                        }
                    }, 100);
                });
            }
        });

        // Display a loader while loading chain data
        showLoadingSpinner('legs-control-loader', 'Loading data for Your Strategy...');

        // Load chain data
        loadChain(currentSymbol).then(() => {
            // Hide loader when data is loaded
            hideLoadingSpinner('legs-control-loader');

            // Generate proper id for each leg when loading from URL
            window.selectedLegs = urlParams.legs?.map(leg => ({
                ...leg,
                id: leg.type === 'FUT'
                    ? `FUT-${leg.action}-${leg.expiryDate}`
                    : `${leg.type}-${leg.strike}-${leg.action}-${leg.expiryDate}`,
                expiryDate: leg.expiryDate
            }));

            // Update prices for all legs
            window.selectedLegs.forEach(leg => {
                window.OptionsUtils.getStrikePriceForLeg(leg, window.chainData.options, window.futurePrices, window.currentSpotPrice);
            });

            updateLegsAndUrl();
        }).catch(error => {
            // Hide loader and show error if loading fails
            hideLoadingSpinner('legs-control-loader');
            console.error('Error loading chain data:', error);
        });
    }

    initURLParams()
</script>