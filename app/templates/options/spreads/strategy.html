<!-- Strategy buttons container -->
<div id="strategy-load-save-container" class="flex flex-col gap-2 relative">
    <!-- Load the strategies in scrollable rows -->
    <div id="strategies-tab-container" class="border border-white mt-3 p-2 rounded-md"
        style="background-image: linear-gradient(45deg, #f0faff, #ffffff);">
        <div class="flex justify-between items-center mb-2">
            <h3 class="font-medium text-neutral-600 uppercase text-xs">Saved Strategies</h3>
        </div>
        <div id="strategies-tab-list"
            class="flex flex-col gap-1 max-h-[108px] overflow-y-auto custom-scroll overflow-x-hidden">
            <div class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-1">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Save Strategy Dropdown -->
    <div id="saveStrategyModal"
        class="hidden absolute right-0 top-0 bg-white rounded-xl shadow-xl p-4 w-[28rem] z-50 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-medium">Save Strategy</h2>
            <button onclick="closeSaveStrategyModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <div class="mb-4">
            <label for="strategyName" class="block text-sm font-medium mb-2">Strategy Name</label>
            <input type="text" id="strategyName" placeholder="Enter strategy name"
                class="w-full border border-gray-200 rounded-md text-sm px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
        </div>
        <div class="flex justify-end gap-1">
            <button onclick="closeSaveStrategyModal()"
                class="px-4 py-1.5 bg-red-100 text-red-600 hover:bg-red-500 hover:text-white rounded-md text-xs hover:bg-red-700 rounded-md transition-colors">
                Cancel
            </button>
            <button onclick="saveStrategy()"
                class="px-4 py-1.5 text-sm font-medium text-white bg-green-600 hover:bg-green-800 text-xs rounded-md transition-colors">
                Save Strategy
            </button>
        </div>
    </div>
</div>

<script>
    // Close dropdowns when clicking outside
    document.addEventListener('click', function (event) {
        const saveModal = document.getElementById('saveStrategyModal');
        if (!event.target.closest('#strategy-load-save-container')) {
            saveModal.classList.add('hidden');
        }
    });

    function openSaveStrategyModal(event) {
        event.stopPropagation();
        // If there are no legs, symbol show alert
        if (!window.selectedLegs.length || !window.currentSymbol) {
            showAlert('Please add legs and select a symbol before saving a strategy.');
            return;
        }
        const modal = document.getElementById('saveStrategyModal');
        modal.classList.remove('hidden');
        document.getElementById('strategyName').focus();
    }

    function closeSaveStrategyModal() {
        document.getElementById('saveStrategyModal').classList.add('hidden');
    }

    // Initialize CRUD instance for strategies
    const strategiesCrud = new Crud('strategies');

    async function saveStrategy() {
        const nameInput = document.getElementById('strategyName');
        const name = nameInput.value.trim();

        if (!name) {
            showAlert('Please enter a strategy name');
            return;
        }

        if (name.length > 30) {
            showAlert('Strategy name cannot exceed 30 characters');
            nameInput.value = name.substring(0, 30);
            return;
        }

        try {
            const strategyData = {
                name: name,
                symbol: window.currentSymbol,
                url: window.location.href,
                legs: window.selectedLegs,
                multiplier: window.currentMultiplier,
                uid: generateUUID()
            };

            await strategiesCrud.create(strategyData);
            closeSaveStrategyModal();
            document.getElementById('strategyName').value = '';
            showAlert('Strategy saved successfully!');
            loadStrategiesTab(); // Refresh the strategies view
        } catch (error) {
            console.error('Error saving strategy:', error);
            showAlert('Error saving strategy', 'error');
        }
    }

    function loadStrategy(url) {
        window.location.href = url;
    }

    async function deleteStrategy(id) {
        const confirmed = await showConfirmDialog({
            title: 'Delete Strategy',
            message: 'Are you sure you want to delete this strategy?',
            confirmText: 'Delete',
            cancelText: 'Cancel',
            type: 'danger'
        });

        if (!confirmed) {
            return;
        }

        try {
            await strategiesCrud.delete(id);
            loadStrategiesTab(); // Refresh the strategies view
            showAlert('Strategy deleted successfully!');
        } catch (error) {
            console.error('Error deleting strategy:', error);
            showAlert('Error deleting strategy', 'error');
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        // Function to load and render strategies in tab
        const strategiesTabContainer = document.getElementById('strategies-tab-container');
        window.loadStrategiesTab = async function () {
            try {
                const response = await strategiesCrud.get({
                    limit: 100,
                    sort: 'desc'
                });

                const strategiesTabList = strategiesTabContainer.querySelector('.grid');

                if (!response.results?.length) {
                    strategiesTabContainer.style.display = 'none';
                    return;
                }

                strategiesTabContainer.style.display = 'block';
                strategiesTabList.innerHTML = response.results.map(strategy => `
                    <div class="relative px-2 py-1.5 text-neutral-700 text-sm border bg-white dark:border-neutral-600 
                                rounded cursor-pointer hover:bg-blue-100 dark:hover:bg-neutral-700 transition-colors group"
                        data-strategy-id="${strategy.uid}">
                        <button onclick="event.stopPropagation(); deleteStrategy('${strategy.uid}')" 
                            class="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity 
                                   bg-white hover:bg-gray-100 rounded-full p-1 border shadow-sm z-10">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" 
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" 
                                stroke-linejoin="round" class="text-gray-500 hover:text-red-600">
                                <path d="M18 6 6 18"/>
                                <path d="m6 6 12 12"/>
                            </svg>
                        </button>
                        <div class="whitespace-nowrap overflow-hidden" onclick="loadStrategy('${strategy.url}')" 
                             title="${strategy.name} (${strategy.symbol})">
                            <span class="font-medium text-xs block truncate">
                                ${strategy.name} (${strategy.symbol})
                            </span>
                        </div>
                    </div>
                `).join('');

            } catch (error) {
                console.error('Error loading strategies tab:', error);
                strategiesTabContainer.style.display = 'none';
            }
        }

        // Load strategies tab initially
        loadStrategiesTab();
    });
</script>