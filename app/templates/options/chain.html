<!DOCTYPE html>
<html lang="en">

<head>
    <title>
        {% if symbol %}
        {{ symbol | upper }} Greeks: Delta, Gamma, Theta, Vega | AI Bull
        {% else %}
        Option Greeks: Delta, Gamma, Theta, Vega Analysis | AI Bull
        {% endif %}
    </title>
    <meta name="description"
        content="{% if symbol %}Explore the {{ symbol | upper }} option chain with in-depth analysis of Option Greeks—Delta (Δ), Gamma (Γ), Theta (Θ), and Vega (ν). Make informed trading decisions with real-time securities market data on movements, volatility, and prices, guided by trusted stock brokers.{% else %}Analyze option chains with key Option Greeks—Delta (Δ), Gamma (Γ), Theta (Θ), and Vega (ν)—to optimize trading decisions based on securities market dynamics and volatility. Consult stock brokers and review documents carefully before investing.{% endif %}" />
    <meta name="keywords"
        content="option chain, {{ symbol | lower }} option chain, option greeks, delta, gamma, theta, vega, options trading, AI Bull, securities market, stock broker, exchange traded products, market risks" />
    <link rel="canonical" href="https://theaibull.com/options/chain{% if symbol %}/{{ symbol | lower }}{% endif %}"
        id="canonical-link" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/options/chain{% if symbol %}/{{ symbol | lower }}{% endif %}"
        id="og-url" />
    <meta property="og:title"
        content="{% if symbol %}{{ symbol | upper }} Option Chain - Analyze Delta, Gamma, Theta, Vega{% else %}Option Chain Analysis - Delta, Gamma, Theta, Vega for Smart Trading{% endif %}"
        id="og-title" />
    <meta property="og:description"
        content="{% if symbol %}Explore the {{ symbol | upper }} option chain with Delta, Gamma, Theta, and Vega for real-time trading insights.{% else %}Understand option chains and analyze Delta, Gamma, Theta, and Vega to enhance your trading strategy.{% endif %}"
        id="og-description" />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title"
        content="{% if symbol %}Option Chain for {{ symbol | upper }} - Analyze Delta, Gamma, Theta, and Vega{% else %}Option Chain - Analyze Delta, Gamma, Theta, and Vega for Smarter Trading Decisions{% endif %}"
        id="twitter-title" />
    <meta name="twitter:description"
        content="{% if symbol %}Analyze the Option Chain for {{ symbol | upper }} with Delta, Gamma, Theta, and Vega to optimize your trading strategy.{% else %}Analyze the key Option Greeks—Delta, Gamma, Theta, and Vega—to better understand market dynamics and optimize your options trading strategy.{% endif %}"
        id="twitter-description" />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}

    <style>
        /* ITM Highlighting: Light yellow for ITM calls/puts */
        .call-itm,
        .put-itm {
            background-color: #fff8dc;
        }

        /* Spot row styling */
        .spot-row {
            background-color: #dbeeff !important;
            font-weight: bold;
            text-align: center;
        }

        /* Strike column styling with a light gray gradient */
        .strike-cell {
            background: linear-gradient(90deg, #f0f0f0, #e0e0e0);
            padding: 0.5rem;
        }

        /* Initially hide containers and set transparent background */
        #stock-price,
        #future-price,
        #expiry-tabs,
        #toggles-container,
        #option-chain-table,
        #options-header,
        #option-chain-table,
        #option-chain-graph {
            display: none;
        }
    </style>
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">
    {% include 'blocks/common.html' %}
    {% include 'blocks/stock-symbols.html' %}
    {% include 'blocks/loader.html' %}
    {%include 'blocks/info.html' %}
    {% include 'blocks/ai-feedback.html' %}
    {% include 'blocks/options-chain-ai-analysis.html' %}

    <div class="rhs-block duration-300 transition-width flex flex-col justify-between min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb) relative">
        <main class="p-3 mx-auto max-w-7xl w-full">
            <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2  rounded-lg mb-3">
                <!-- Title and Tabs Row -->
                <div class="md:flex justify-between items-center">
                    <div class="flex items-center">
                        <h1 id="page-header" class="md:text-xl text-lg font-semibold tracking-tight">
                            {% if symbol %}
                            {{ symbol | upper }} Option Chain Analysis
                            {% else %}
                            Option Chain Analysis
                            {% endif %}
                        </h1>
                        <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">Explore option chains with
                            advanced tools</h2>
                        <!-- Button to trigger modal open -->
                        <button id="openModalBtn" onclick="openModal()" class="ml-2">

                            <div class="flex group text-sm">
                                <div
                                    class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                    <div
                                        class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                        <div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-circle-help">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                <path d="M12 17h.01"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div
                                        class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                        <div
                                            class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                            How to Use This Page</div>
                                    </div>
                                </div>
                            </div>
                        </button>
                    </div>

                </div>
            </div>



            <div>
                <div class="mx-auto container">
                    <div class="flex gap-3">
                        <div class="w-full rounded-lg md:p-8 p-4 border border-white"
                            style="background-image: linear-gradient(45deg, #f0faff, #ffffff);">
                            {% with enable_backtesting=true %}
                            {% include 'blocks/search-active-recent-stocks.html' %}
                            {% endwith %}
                        </div>
                    </div>
                    <!-- Expiry tabs, toggles, data table, OI chart -->
                    <div>
                        <div id="options-header" class="bg-white rounded-lg p-4 my-3">
                            <div>
                                <div class="flex md:flex-row flex-col gap-3 md:items-center mb-5">
                                    <div class="flex md:flex-row flex-col gap-3">
                                        <!-- Stock Price Display -->
                                        <div id="stock-price"></div>
                                        <!-- Future Price Display -->
                                        <div id="future-price"></div>
                                    </div>
                                    <!-- Toggles -->
                                    <div id="toggles-container" class="flex items-center md:gap-4 gap-3 flex-wrap">
                                        <div class="flex items-center gap-1">
                                            <input type="checkbox" id="showGreeks" class="h-4 w-4 cursor-pointer" />
                                            <label for="showGreeks" class="text-sm font-medium cursor-pointer">Show
                                                Greeks</label>
                                        </div>
                                        <div class="flex items-center gap-1 cursor-pointer">
                                            <input type="checkbox" id="hideIlliquid" class="h-4 w-4 cursor-pointer" />
                                            <label for="hideIlliquid" class="text-sm font-medium cursor-pointer">Hide
                                                Illiquid</label>
                                        </div>
                                        <div class="flex items-center gap-1">
                                            <input type="checkbox" id="showBuildups" class="h-4 w-4 cursor-pointer" />
                                            <label for="showBuildups" class="text-sm font-medium cursor-pointer">Show
                                                Buildups</label>
                                        </div>
                                        <!-- AI Analysis Button -->
                                        <button id="chain-ai-analysis-btn" onclick="handleAiAnalysis()"
                                            class="animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-3 py-1 relative rounded-md text-xs to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-1 ml-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-sparkles">
                                                <path
                                                    d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                                <path d="M20 3v4" />
                                                <path d="M22 5h-4" />
                                                <path d="M4 17v2" />
                                                <path d="M5 18H3" />
                                            </svg> Get AI Analysis
                                        </button>
                                    </div>
                                </div>
                                <div class="flex md:flex-row flex-col gap-3">
                                    <!-- Expiry Tabs -->
                                    <div id="expiry-tabs"
                                        class="flex flex-wrap items-center justify-start gap-2 lg:text-sm text-xs">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Option Chain Table -->
                        <div id="option-chain-table"></div>

                        <!-- OI Chart -->
                        <div class="container mx-auto my-3 bg-white rounded-lg p-4" id="option-chain-graph">
                            <canvas id="oiChart" class="w-[320px] h-[300px]" width="600" height="300"></canvas>
                        </div>

                        <!-- Spinner Animation (hidden by default) -->
                        <div id="loadingSpinner" class="flex flex-col items-center justify-center min-h-[40vh] hidden">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 border-4 border-blue-500 border-dashed rounded-full animate-spin">
                                </div>
                                <span class="mt-4 text-sm font-medium text-gray-700">Loading option chain data...</span>
                            </div>
                        </div>

                        <!-- FAQ Section in Accordion Format -->
                        <section class="mt-8 text-sm">
                            <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
                            <div class="space-y-4">
                                <details class="border rounded-lg bg-white">
                                    <summary class="cursor-pointer p-4">
                                        What is the Option Chain with Toggles tool?
                                    </summary>
                                    <div class="p-4 border-t">
                                        <p class="text-gray-700 dark:text-gray-300 leading-6">
                                            An option chain is a detailed table listing all available options contracts
                                            for a stock or index,
                                            such as {{ symbol | upper }}, within the securities market. It includes
                                            strike prices, expiration
                                            dates, and option types (calls and puts). By analyzing the option chain,
                                            traders, with guidance from
                                            stock brokers or depository participants, gain insights into market
                                            sentiment, volatility, and
                                            potential price movements, aiding strategic decision-making. Always read
                                            documents carefully before
                                            investing, as options are subject to market risks.
                                        </p>
                                    </div>
                                </details>
                                <details class="border rounded-lg bg-white">
                                    <summary class="cursor-pointer p-4">What are Option Greeks?</summary>
                                    <div class="p-4 border-t">
                                        <p class="text-gray-700 dark:text-gray-300 leading-6">
                                            Option Greeks are metrics that measure how an option's price responds to
                                            changes in market factors
                                            within the securities market. They are essential for understanding risks and
                                            returns in options
                                            trading, especially for exchange-traded products:
                                        </p>
                                        <ul class="list-disc pl-6">
                                            <li><strong>Delta (Δ):</strong> Shows how much an option's price changes for
                                                a $1 move in the
                                                underlying asset (e.g., a Delta of 0.5 means a $0.50 move per $1).</li>
                                            <li><strong>Gamma (Γ):</strong> Measures the rate of change in Delta,
                                                indicating Delta's stability.
                                            </li>
                                            <li><strong>Theta (Θ):</strong> Reflects time decay, showing how an option's
                                                value decreases as
                                                expiration nears.</li>
                                            <li><strong>Vega (ν):</strong> Indicates sensitivity to a 1% change in
                                                implied volatility.</li>
                                        </ul>
                                        <p class="text-gray-700 dark:text-gray-300 leading-6 mt-2">
                                            Consult a stock broker to understand how these metrics impact your trading
                                            strategy, and always
                                            review documents carefully before investing.
                                        </p>
                                    </div>
                                </details>
                                <details class="border rounded-lg bg-white">
                                    <summary class="cursor-pointer p-4">How do I use the option chain tool?</summary>
                                    <div class="p-4 border-t">
                                        <p class="text-gray-700 dark:text-gray-300 leading-6">
                                            To use our option chain tool, search for a stock (e.g., {{ symbol | upper
                                            }}), toggle between call
                                            and put options, filter by expiration dates, and adjust strike prices. The
                                            tool displays real-time
                                            securities market data, including Option Greeks and open interest, with
                                            AI-driven insights to
                                            optimize your trading strategy. Stock brokers can provide additional
                                            guidance, and you can verify
                                            your holdings in the consolidated account statement issued by NSDL/CDSL
                                            every month.
                                        </p>
                                    </div>
                                </details>
                                <details class="border rounded-lg bg-white">
                                    <summary class="cursor-pointer p-4">Why are Option Greeks important for trading?
                                    </summary>
                                    <div class="p-4 border-t">
                                        <p class="text-gray-700 dark:text-gray-300 leading-6">
                                            Option Greeks help traders understand how options prices react to changes in
                                            the securities market.
                                            For example, Delta predicts price movement, Theta highlights time decay
                                            risks, and Vega shows
                                            volatility impacts. By analyzing these metrics in an option chain, you can
                                            develop effective trading
                                            strategies for exchange-traded products. Always consult a stock broker and
                                            review documents
                                            carefully before investing, as options are subject to market risks.
                                        </p>
                                    </div>
                                </details>
                                <details class="border rounded-lg bg-white">
                                    <summary class="cursor-pointer p-4">What does the AI Analysis feature do?</summary>
                                    <div class="p-4 border-t">
                                        <p class="text-gray-700 dark:text-gray-300 leading-6">
                                            The AI Analysis feature processes option chain data, including open
                                            interest, implied volatility,
                                            and Option Greeks, to provide actionable insights for smarter trading
                                            decisions in the securities
                                            market. It helps traders, in collaboration with stock brokers, make informed
                                            decisions while
                                            navigating market risks.
                                        </p>
                                    </div>
                                </details>
                                <details class="border rounded-lg bg-white">
                                    <summary class="cursor-pointer p-4">How often is the option chain data updated?
                                    </summary>
                                    <div class="p-4 border-t">
                                        <p class="text-gray-700 dark:text-gray-300 leading-6">
                                            Our option chain data is updated in real-time, ensuring you have the latest
                                            securities market
                                            information for accurate analysis and timely trading. Check your securities,
                                            mutual funds, and bonds
                                            in the consolidated account statement issued by NSDL/CDSL every month for a
                                            comprehensive view of
                                            your investments.
                                        </p>
                                    </div>
                                </details>
                                <details class="border rounded-lg bg-white">
                                    <summary class="cursor-pointer p-4">How can I ensure safe trading practices?
                                    </summary>
                                    <div class="p-4 border-t">
                                        <p class="text-gray-700 dark:text-gray-300 leading-6">
                                            To trade safely, work with registered stock brokers or depository
                                            participants who can guide you
                                            through the securities market. Stock brokers can accept securities as margin
                                            from clients only,
                                            ensuring compliance with exchange regulations. Always read documents
                                            carefully before investing, as
                                            all investments are subject to market risks. For any issues, utilize the
                                            exchange investor redressal
                                            mechanisms provided by the stock exchange.
                                        </p>
                                    </div>
                                </details>
                                <details class="border rounded-lg bg-white">
                                    <summary class="cursor-pointer p-4">How do I manage my securities and pledges?
                                    </summary>
                                    <div class="p-4 border-t">
                                        <p class="text-gray-700 dark:text-gray-300 leading-6">
                                            Stock brokers can accept securities as margin from clients only after you
                                            create a pledge using your
                                            registered mobile number to receive an OTP from the depository participant.
                                            Verify your holdings,
                                            including securities, mutual funds, and bonds, in the consolidated account
                                            statement issued by
                                            NSDL/CDSL every month. Contact your depository participant for assistance
                                            with pledges or account
                                            management.
                                        </p>
                                    </div>
                                </details>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </main>

        <!-- Dependencies -->
        <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@1.0.2"></script>

        <script>
            // Global variables for data storage
            window._lastOptionData = {};
            window.currentStockPrice = null;
            window.futurePrices = null;
            const serverSymbol = "{{ symbol | default ('') | escape }}";

            document.addEventListener("DOMContentLoaded", async () => {
                const expiryTabsContainer = document.getElementById("expiry-tabs");
                const tableContainer = document.getElementById("option-chain-table");
                const togglesContainer = document.getElementById("toggles-container");
                const stockPriceContainer = document.getElementById("stock-price");
                const futurePriceContainer = document.getElementById("future-price");
                const spinner = document.getElementById("loadingSpinner");

                // Unhide the containers after data loads
                function unhideContainers() {
                    stockPriceContainer.style.display = "block";
                    futurePriceContainer.style.display = "block";
                    expiryTabsContainer.style.display = "flex";
                    togglesContainer.style.display = "flex";
                    tableContainer.style.display = "block";
                    document.getElementById("options-header").style.display = "block";
                    document.getElementById("option-chain-table").style.display = "block";
                    document.getElementById("option-chain-graph").style.display = "block";

                }

                // Toggles: "Show Greeks" ON by default, "Hide Illiquid" OFF by default
                let showGreeks = true;
                let hideIlliquid = false;
                let showBuildups = false;
                const showGreeksCheckbox = document.getElementById("showGreeks");
                const hideIlliquidCheckbox = document.getElementById("hideIlliquid");
                const showBuildupsCheckbox = document.getElementById("showBuildups");
                showGreeksCheckbox.checked = true;
                hideIlliquidCheckbox.checked = false;
                showBuildupsCheckbox.checked = false;

                showGreeksCheckbox.addEventListener("change", function () {
                    showGreeks = this.checked;
                    if (window._lastOptionData) {
                        const { allOptions, currentExpiry } = window._lastOptionData;
                        displayOptionsForExpiry(allOptions, currentExpiry);
                    }
                });

                hideIlliquidCheckbox.addEventListener("change", function () {
                    hideIlliquid = this.checked;
                    if (window._lastOptionData) {
                        const { allOptions, currentExpiry } = window._lastOptionData;
                        displayOptionsForExpiry(allOptions, currentExpiry);
                    }
                });

                showBuildupsCheckbox.addEventListener("change", function () {
                    showBuildups = this.checked;
                    if (window._lastOptionData) {
                        const { allOptions, currentExpiry } = window._lastOptionData;
                        displayOptionsForExpiry(allOptions, currentExpiry);
                    }
                });

                // Handle search: clear UI, then fetch data
                async function handleSearch(symbol) {
                    try {
                        // Clear previous data
                        expiryTabsContainer.innerHTML = "";
                        tableContainer.innerHTML = "";
                        stockPriceContainer.innerHTML = "";
                        futurePriceContainer.innerHTML = "";
                        // Hide containers during loading
                        stockPriceContainer.style.display = "none";
                        futurePriceContainer.style.display = "none";
                        expiryTabsContainer.style.display = "none";
                        togglesContainer.style.display = "none";
                        tableContainer.style.display = "none";

                        if (window._oiChart) {
                            window._oiChart.destroy();
                        }

                        // Show spinner
                        spinner.classList.remove("hidden");

                        updateSeoMetadata(symbol);

                        // Fetch data
                        await fetchFutures(symbol);
                        await fetchOptionChainAndPopulateSpotPrice(symbol);
                        unhideContainers();
                    } catch (error) {
                        console.error("Error during search:", error);
                        tableContainer.innerHTML = '<p class="text-red-500">Failed to fetch data</p>';
                    } finally {
                        // Hide spinner
                        spinner.classList.add("hidden");
                    }
                }

                // Listen for custom event from search component
                document.addEventListener('stockSearchTriggered', (e) => {
                    const symbol = e.detail.symbol;
                    if (symbol) {
                        handleSearch(symbol);
                    }
                });

                // Initialize with server-provided symbol
                if (serverSymbol) {
                    window.handleStockSymbolClick(serverSymbol.toUpperCase());
                    await handleSearch(serverSymbol.toUpperCase());
                }

                // Fetch option chain data
                async function fetchOptionChainAndPopulateSpotPrice(symbol) {
                    try {
                        let dateQuery = "";
                        if (window.backtestDate) {
                            // Convert the date from yyyy-mm-dd (input default) to dd-mm-yyyy format
                            const d = new Date(window.backtestDate);
                            const dd = String(d.getDate()).padStart(2, "0");
                            const mm = String(d.getMonth() + 1).padStart(2, "0");
                            const yyyy = d.getFullYear();
                            dateQuery = `?date=${dd}-${mm}-${yyyy}`;
                        }
                        const res = await axios.get(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${symbol}${dateQuery}`);
                        const records = res.data.records;

                        if (!records || !records.data || records.data.length === 0) {
                            throw new Error('No data available');
                        }

                        window.currentStockPrice = null;
                        if (records && records.data) {
                            for (const record of records.data) {
                                if (record.CE && record.CE.underlyingValue) {
                                    window.currentStockPrice = record.CE.underlyingValue;
                                    break;
                                }
                                if (record.PE && record.PE.underlyingValue) {
                                    window.currentStockPrice = record.PE.underlyingValue;
                                    break;
                                }
                            }
                        }

                        if (!window.currentStockPrice) {
                            stockPriceContainer.innerHTML = `<span class="bg-sky-100 font-semibold px-3 py-1 rounded text-center">Stock Price: N/A</span>`;
                        } else {
                            stockPriceContainer.innerHTML = `<span class="bg-sky-100 font-semibold px-3 py-1 rounded text-center">Stock Price: ${window.currentStockPrice}</span>`;
                        }

                        renderExpiryTabs(records);
                    } catch (error) {
                        console.error("Error fetching option chain:", error);
                        tableContainer.innerHTML = `
                            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mt-4">
                                <p class="text-red-600 dark:text-red-400">
                                    Failed to fetch options data. Please try again later.
                                </p>
                            </div>`;
                        throw error;
                    }
                }

                // Fetch futures data
                async function fetchFutures(symbol) {
                    try {
                        const resFutures = await axios.get(`https://iam.theaibull.com/v1/wg7ttpouv7/futures/${symbol}`);
                        window.futurePrices = resFutures.data;
                    } catch (error) {
                        console.error("Error fetching futures data", error);
                    }
                }

                function renderExpiryTabs(data) {
                    if (!data.expiryDates || !data.expiryDates.length) {
                        tableContainer.innerHTML = "<p>No expiry data found.</p>";
                        return;
                    }

                    expiryTabsContainer.innerHTML = data.expiryDates
                        .map((date, idx) => `
            <button class="tab-btn px-3 py-1 border rounded bg-gray-100 text-sm hover:bg-blue-500 hover:text-white ${idx === 0 ? "!bg-blue-500 text-white" : ""}" 
                data-expiry="${date}">
                ${date}
            </button>
        `)
                        .join("");

                    // Show the first expiry by default
                    displayOptionsForExpiry(data.data, data.expiryDates[0]);
                    window._lastOptionData = {
                        allOptions: data.data,
                        currentExpiry: data.expiryDates[0],
                    };

                    // Bind click events to the tab buttons
                    document.querySelectorAll(".tab-btn").forEach((btn) => {
                        btn.addEventListener("click", function () {
                            // Remove active classes from all tabs
                            document.querySelectorAll(".tab-btn").forEach((b) => b.classList.remove("!bg-blue-500", "text-white"));
                            // Add active classes to the clicked tab
                            this.classList.add("!bg-blue-500", "text-white");
                            // Update display for the selected expiry
                            displayOptionsForExpiry(data.data, this.dataset.expiry);
                            window._lastOptionData.currentExpiry = this.dataset.expiry;
                        });
                    });
                }

                // Display options for a given expiry
                function displayOptionsForExpiry(allOptions, expiryDate) {
                    window._lastOptionData.allOptions = allOptions;
                    window._lastOptionData.currentExpiry = expiryDate;

                    // Filter for the chosen expiry
                    let filtered = allOptions.filter(item => item.expiryDate === expiryDate);

                    // Hide illiquid if toggle is on
                    if (hideIlliquid) {
                        filtered = filtered.filter(opt => {
                            const ceVol = (opt.CE && opt.CE.totalTradedVolume) || 0;
                            const peVol = (opt.PE && opt.PE.totalTradedVolume) || 0;
                            return ceVol > 0 && peVol > 0;
                        });
                    }

                    if (!filtered.length) {
                        tableContainer.innerHTML = `<p>No options match your filter criteria.</p>`;
                        if (window._oiChart) window._oiChart.destroy();
                        futurePriceContainer.innerHTML = "";
                        return;
                    }

                    // Update future price display if available
                    if (window.futurePrices && window.futurePrices[expiryDate]) {
                        futurePriceContainer.innerHTML = `<span class="bg-purple-100 font-semibold px-3 py-1 rounded text-center">Future Price: ${window.futurePrices[expiryDate]}</span>`;
                    } else {
                        futurePriceContainer.innerHTML = `<span class="bg-purple-100 font-semibold px-3 py-1 rounded text-center">Future Price: N/A</span>`;
                    }

                    // Sort by strike ascending
                    filtered.sort((a, b) => a.strikePrice - b.strikePrice);

                    // Find spot price index or closest index
                    const spotPrice = window.currentStockPrice;
                    // Find the first strike price that's >= spot price
                    let spotIndex = filtered.findIndex(opt => opt.strikePrice >= spotPrice);
                    if (spotIndex === -1) spotIndex = filtered.length - 1;

                    // Calculate visible range (15 strikes above and below spot)
                    // lowerBound = lower array indices = lower strike prices
                    const lowerBound = Math.max(0, spotIndex - 15);
                    // upperBound = higher array indices = higher strike prices
                    const upperBound = Math.min(filtered.length - 1, spotIndex + 15);

                    // Slice the visible options
                    let visibleOptions = filtered.slice(lowerBound, upperBound + 1);

                    // Store the full filtered data for "Show More" functionality
                    window._fullFilteredOptions = filtered;
                    window._currentLowerBound = lowerBound;
                    window._currentUpperBound = upperBound;

                    // Build rows HTML
                    const rowsHtml = buildTableRowsWithSpot(visibleOptions);

                    // Prepare "Show More" buttons HTML
                    let showMoreAboveHtml = '';
                    let showMoreBelowHtml = '';

                    // "Show More Higher Strikes" button loads higher strike prices (appears at bottom of table)
                    // These are at higher array indices (upperBound+1 to end of array)
                    if (upperBound < filtered.length - 1) {
                        const remainingAbove = filtered.length - 1 - upperBound;
                        const toLoadAbove = Math.min(25, remainingAbove);
                        showMoreAboveHtml = `
                            <tr class="bg-gray-100 hover:bg-gray-200 cursor-pointer text-center" id="show-more-above" onclick="loadMoreStrikes('above')">
                                <td colspan="21" class="p-2 font-medium text-blue-600">
                                    Show ${toLoadAbove} More Higher Strikes
                                </td>
                            </tr>
                        `;
                    }

                    // "Show More Lower Strikes" button loads lower strike prices (appears at top of table)
                    // These are at lower array indices (0 to lowerBound-1)
                    if (lowerBound > 0) {
                        const remainingBelow = lowerBound;
                        const toLoadBelow = Math.min(25, remainingBelow);
                        showMoreBelowHtml = `
                            <tr class="bg-gray-100 hover:bg-gray-200 cursor-pointer text-center" id="show-more-below" onclick="loadMoreStrikes('below')">
                                <td colspan="21" class="p-2 font-medium text-blue-600">
                                    Show ${toLoadBelow} More Lower Strikes
                                </td>
                            </tr>
                        `;
                    }

                    // Adjust header colspans based on "Show Buildups"
                    const callsColspan = showBuildups ? 11 : 10;
                    const putsColspan = showBuildups ? 11 : 10;
                    tableContainer.innerHTML = `<div class="bg-white my-3 p-4 rounded-lg">
                        <div class="mb-2">Expiry: <span class="font-semibold">${expiryDate}</span></div>
                        <div class="overflow-x-auto max-h-[70vh] relative" id="option-table-container">
                            <table class="w-full border-collapse border dark:border-neutral-600 text-xs" id="data-table">
                                <thead class="sticky top-0 z-10">
                                    <tr class="bg-gray-50 dark:bg-neutral-800">
                                        <th colspan="${callsColspan}" class="border p-1 text-center font-semibold bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400">
                                            CALLS
                                        </th>
                                        <th rowspan="2" class="border p-1 text-center font-semibold bg-gray-50 dark:bg-neutral-800">
                                            Strike
                                        </th>
                                        <th colspan="${putsColspan}" class="border p-1 text-center font-semibold bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400">
                                            PUTS
                                        </th>
                                    </tr>
                                    <tr class="bg-gray-100 dark:bg-neutral-700">
                                        <!-- Calls columns -->
                                        <th class="border p-1 text-center" style="${showBuildups ? "" : "display: none;"}">Build-up</th>
                                        <th class="border p-1 text-center">IV</th>
                                        <th class="border p-1 text-center">OI</th>
                                        <th class="border p-1 text-center">Rho</th>
                                        <th class="border p-1 text-center">Vega</th>
                                        <th class="border p-1 text-center">Θ</th>
                                        <th class="border p-1 text-center">Γ</th>
                                        <th class="border p-1 text-center">Δ</th>
                                        <th class="border p-1 text-center">Ask</th>
                                        <th class="border p-1 text-center">Bid</th>
                                        <th class="border p-1 text-center">LTP</th>

                                        <!-- Puts columns -->
                                        <th class="border p-1 text-center">LTP</th>
                                        <th class="border p-1 text-center">Bid</th>
                                        <th class="border p-1 text-center">Ask</th>
                                        <th class="border p-1 text-center">Δ</th>
                                        <th class="border p-1 text-center">Γ</th>
                                        <th class="border p-1 text-center">Θ</th>
                                        <th class="border p-1 text-center">Vega</th>
                                        <th class="border p-1 text-center">Rho</th>
                                        <th class="border p-1 text-center">OI</th>
                                        <th class="border p-1 text-center">IV</th>
                                        <th class="border p-1 text-center" style="${showBuildups ? "" : "display: none;"}">Build-up</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${showMoreBelowHtml}
                                    ${rowsHtml}
                                    ${showMoreAboveHtml}
                                </tbody>
                            </table>
                        </div>
                    </div>`;

                    // Scroll to the middle of the table to show spot price
                    setTimeout(() => {
                        const tableContainer = document.getElementById('option-table-container');
                        if (tableContainer) {
                            // Find the spot row or calculate middle position
                            const spotRow = document.querySelector('.spot-row');
                            if (spotRow) {
                                // If spot row exists, scroll to it
                                spotRow.scrollIntoView({ block: 'center', behavior: 'smooth' });
                            }
                        }
                    }, 100); // Small delay to ensure the table is rendered

                    // Update OI chart
                    buildOIChart(filtered);
                }

                // Function to load more strikes
                window.loadMoreStrikes = function (direction) {
                    const allOptions = window._fullFilteredOptions;
                    if (!allOptions) return;

                    let newLowerBound = window._currentLowerBound;
                    let newUpperBound = window._currentUpperBound;

                    if (direction === 'above') {
                        // Load 25 more strikes above
                        newUpperBound = Math.min(allOptions.length - 1, newUpperBound + 25);
                    } else if (direction === 'below') {
                        // Load 25 more strikes below
                        newLowerBound = Math.max(0, newLowerBound - 25);
                    }

                    // Update bounds
                    window._currentLowerBound = newLowerBound;
                    window._currentUpperBound = newUpperBound;

                    // Get visible options
                    const visibleOptions = allOptions.slice(newLowerBound, newUpperBound + 1);

                    // Rebuild table with new visible options
                    const rowsHtml = buildTableRowsWithSpot(visibleOptions);

                    // Prepare "Show More" buttons HTML
                    let showMoreAboveHtml = '';
                    let showMoreBelowHtml = '';

                    if (newUpperBound < allOptions.length - 1) {
                        const remainingAbove = allOptions.length - 1 - newUpperBound;
                        const toLoadAbove = Math.min(25, remainingAbove);
                        showMoreAboveHtml = `
                            <tr class="bg-gray-100 hover:bg-gray-200 cursor-pointer text-center" id="show-more-above" onclick="loadMoreStrikes('above')">
                                <td colspan="21" class="p-2 font-medium text-blue-600">
                                    Show ${toLoadAbove} More Higher Strikes
                                </td>
                            </tr>
                        `;
                    }

                    if (newLowerBound > 0) {
                        const remainingBelow = newLowerBound;
                        const toLoadBelow = Math.min(25, remainingBelow);
                        showMoreBelowHtml = `
                            <tr class="bg-gray-100 hover:bg-gray-200 cursor-pointer text-center" id="show-more-below" onclick="loadMoreStrikes('below')">
                                <td colspan="21" class="p-2 font-medium text-blue-600">
                                    Show ${toLoadBelow} More Lower Strikes
                                </td>
                            </tr>
                        `;
                    }

                    // Update table body
                    const tableBody = document.querySelector("#data-table tbody");
                    if (tableBody) {
                        tableBody.innerHTML = `
                        ${showMoreBelowHtml}
                        ${rowsHtml}
                        ${showMoreAboveHtml}
                        `;
                    }
                }

                // Build table rows and insert a dedicated spot row where needed
                function buildTableRowsWithSpot(optionsArray) {
                    const rows = [];
                    let spotInserted = false;
                    // Insert a spot row at the beginning if current stock price is less than the first strike
                    if (window.currentStockPrice < optionsArray[0].strikePrice) {
                        rows.push(`
                          <tr class="spot-row">
                            <td colspan="21">Spot Price: ${window.currentStockPrice.toFixed(2)}</td>
                          </tr>
                        `);
                        spotInserted = true;
                    }
                    for (let i = 0; i < optionsArray.length; i++) {
                        const rowHtml = buildSingleRow(optionsArray[i]);
                        rows.push(rowHtml);
                        if (i < optionsArray.length - 1) {
                            const currentStrike = optionsArray[i].strikePrice;
                            const nextStrike = optionsArray[i + 1].strikePrice;
                            if (window.currentStockPrice > currentStrike && window.currentStockPrice < nextStrike) {
                                rows.push(`
                          <tr class="spot-row">
                            <td colspan="21">Spot Price: ${window.currentStockPrice.toFixed(2)}</td>
                          </tr>
                        `);
                                spotInserted = true;
                            }
                        }
                    }
                    // Insert at the end if not yet inserted and stock price is above the last strike
                    if (!spotInserted && window.currentStockPrice > optionsArray[optionsArray.length - 1].strikePrice) {
                        rows.push(`
                          <tr class="spot-row">
                            <td colspan="21">Spot Price: ${window.currentStockPrice.toFixed(2)}</td>
                          </tr>
                        `);
                    }
                    return rows.join("");
                }

                // Helper function to determine build-up status
                function determineBuildup(option) {
                    if (!option || !option.openInterest) return "-";
                    const priceChange = option.change || 0;
                    const oiChange = option.changeinOpenInterest || 0;
                    if (priceChange > 0 && oiChange > 0) {
                        return 'Long Buildup';
                    } else if (priceChange < 0 && oiChange > 0) {
                        return 'Short Buildup';
                    } else if (priceChange > 0 && oiChange < 0) {
                        return 'Short Covering';
                    } else if (priceChange < 0 && oiChange < 0) {
                        return 'Long Unwinding';
                    }
                    return "-";
                }

                // Build a single row for a given option
                function buildSingleRow(opt) {
                    const ce = opt.CE || {};
                    const pe = opt.PE || {};
                    const underlying = ce.underlyingValue || pe.underlyingValue || window.currentStockPrice;
                    const callITM = (opt.strikePrice < underlying);
                    const putITM = (opt.strikePrice > underlying);
                    const callHighlight = callITM ? "call-itm" : "";
                    const putHighlight = putITM ? "put-itm" : "";
                    const callBuildup = determineBuildup(ce);
                    const putBuildup = determineBuildup(pe);
                    const buildupDisplay = showBuildups ? "" : "display: none;";
                    const callIV = ce.impliedVolatility != null ? ce.impliedVolatility.toFixed(2) : "-";
                    const callOI = ce.openInterest != null ? ce.openInterest.toFixed(0) : "-";
                    const callRho = (ce.rho != null && showGreeks) ? ce.rho.toFixed(3) : "-";
                    const callVega = (ce.vega != null && showGreeks) ? ce.vega.toFixed(3) : "-";
                    const callTheta = (ce.theta != null && showGreeks) ? ce.theta.toFixed(3) : "-";
                    const callGamma = (ce.gamma != null && showGreeks) ? ce.gamma.toFixed(4) : "-";
                    const callDelta = (ce.delta != null && showGreeks) ? ce.delta.toFixed(3) : "-";
                    const callAsk = ce.askPrice != null ? ce.askPrice.toFixed(2) : "-";
                    const callBid = ce.bidprice != null ? ce.bidprice.toFixed(2) : "-";
                    const callLtp = ce.lastPrice != null ? ce.lastPrice.toFixed(2) : "-";
                    const putLtp = pe.lastPrice != null ? pe.lastPrice.toFixed(2) : "-";
                    const putBid = pe.bidprice != null ? pe.bidprice.toFixed(2) : "-";
                    const putAsk = pe.askPrice != null ? pe.askPrice.toFixed(2) : "-";
                    const putDelta = (pe.delta != null && showGreeks) ? pe.delta.toFixed(3) : "-";
                    const putGamma = (pe.gamma != null && showGreeks) ? pe.gamma.toFixed(4) : "-";
                    const putTheta = (pe.theta != null && showGreeks) ? pe.theta.toFixed(3) : "-";
                    const putVega = (pe.vega != null && showGreeks) ? pe.vega.toFixed(3) : "-";
                    const putRho = (pe.rho != null && showGreeks) ? pe.rho.toFixed(3) : "-";
                    const putOI = pe.openInterest != null ? pe.openInterest.toFixed(0) : "-";
                    const putIV = pe.impliedVolatility != null ? pe.impliedVolatility.toFixed(2) : "-";

                    return `
                        <tr class="hover:bg-gray-50 dark:hover:bg-neutral-800">
                            <!-- Calls (reversed order) -->
                            <td class="border p-1 text-center ${callHighlight}" style="${buildupDisplay}">${callBuildup}</td>
                            <td class="border p-1 text-center ${callHighlight}">${callIV}</td>
                            <td class="border p-1 text-center ${callHighlight}">${callOI}</td>
                            <td class="border p-1 text-center ${callHighlight}">${callRho}</td>
                            <td class="border p-1 text-center ${callHighlight}">${callVega}</td>
                            <td class="border p-1 text-center ${callHighlight}">${callTheta}</td>
                            <td class="border p-1 text-center ${callHighlight}">${callGamma}</td>
                            <td class="border p-1 text-center ${callHighlight}">${callDelta}</td>
                            <td class="border p-1 text-center ${callHighlight}">${callAsk}</td>
                            <td class="border p-1 text-center ${callHighlight}">${callBid}</td>
                            <td class="border p-1 text-center ${callHighlight}">${callLtp}</td>

                            <!-- Strike -->
                            <td class="border p-1 text-center font-semibold strike-cell">
                                ${opt.strikePrice}
                            </td>

                            <!-- Puts (original order) -->
                            <td class="border p-1 text-center ${putHighlight}">${putLtp}</td>
                            <td class="border p-1 text-center ${putHighlight}">${putBid}</td>
                            <td class="border p-1 text-center ${putHighlight}">${putAsk}</td>
                            <td class="border p-1 text-center ${putHighlight}">${putDelta}</td>
                            <td class="border p-1 text-center ${putHighlight}">${putGamma}</td>
                            <td class="border p-1 text-center ${putHighlight}">${putTheta}</td>
                            <td class="border p-1 text-center ${putHighlight}">${putVega}</td>
                            <td class="border p-1 text-center ${putHighlight}">${putRho}</td>
                            <td class="border p-1 text-center ${putHighlight}">${putOI}</td>
                            <td class="border p-1 text-center ${putHighlight}">${putIV}</td>
                            <td class="border p-1 text-center ${putHighlight}" style="${buildupDisplay}">${putBuildup}</td>
                        </tr>
                    `;
                }

                // Build OI Chart
                function buildOIChart(filteredOptions) {
                    const ctx = document.getElementById("oiChart").getContext("2d");
                    if (window._oiChart) {
                        window._oiChart.destroy();
                    }
                    const chartData = prepareChartData(filteredOptions);
                    window._oiChart = new Chart(ctx, {
                        type: "bar",
                        data: chartData,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                annotation: {
                                    annotations: getChartAnnotations(),
                                },
                                legend: {
                                    display: true,
                                    position: 'top',
                                    labels: {
                                        filter: (legendItem) => legendItem.text !== "",
                                    },
                                },
                                tooltip: {
                                    mode: "index",
                                    intersect: false,
                                    callbacks: {
                                        title: function (tooltipItems) {
                                            return `Strike: ${tooltipItems[0].label}`;
                                        },
                                        label: function (context) {
                                            const value = context.parsed.y;

                                            // Determine if this is an OI increase or decrease
                                            const isChange = context.datasetIndex % 2 === 1;
                                            const isCall = context.datasetIndex < 2;
                                            const type = isCall ? 'Call' : 'Put';

                                            if (isChange) {
                                                const color = context.raw > 0 ? 'Increase' : 'Decrease';
                                                return `${type} OI ${color}: ${Math.abs(value)}`;
                                            } else {
                                                return `${type} Base OI: ${value}`;
                                            }
                                        }
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    stacked: true,
                                    title: {
                                        display: true,
                                        text: "Strike Price",
                                    },
                                },
                                y: {
                                    stacked: true,
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: "Open Interest",
                                    },
                                },
                            },
                        },
                    });
                }

                // Prepare data for OI chart
                function prepareChartData(filteredOptions) {
                    const strikes = [];
                    const callData = { base: [], diff: [], baseColors: [], diffColors: [], diffBorders: [] };
                    const putData = { base: [], diff: [], baseColors: [], diffColors: [], diffBorders: [] };

                    filteredOptions.forEach((opt) => {
                        strikes.push(opt.strikePrice);
                        processOptionSide(opt.CE, callData, true);
                        processOptionSide(opt.PE, putData, false);
                    });

                    return {
                        labels: strikes,
                        datasets: [
                            createDataset("Calls Base OI", callData.base, callData.baseColors, "calls"),
                            createDataset("Calls OI Change", callData.diff, callData.diffColors, "calls", callData.diffBorders),
                            createDataset("Puts Base OI", putData.base, putData.baseColors, "puts"),
                            createDataset("Puts OI Change", putData.diff, putData.diffColors, "puts", putData.diffBorders),
                        ],
                    };
                }

                // Process each side for OI chart
                function processOptionSide(option, data, isCall) {
                    const oi = option?.openInterest || 0;
                    const change = option?.pchangeinOpenInterest || 0;
                    const prevOI = oi / (1 + change / 100);

                    if (oi >= prevOI) {
                        data.base.push(prevOI);
                        data.diff.push(oi - prevOI);
                        data.baseColors.push(isCall ? "rgba(0,128,0,0.3)" : "rgba(255,0,0,0.3)");
                        data.diffColors.push(isCall ? "rgba(0,128,0,0.6)" : "rgba(255,0,0,0.6)");
                        data.diffBorders.push(isCall ? "rgba(0,128,0,1)" : "rgba(255,0,0,1)");
                    } else {
                        data.base.push(oi);
                        data.diff.push(prevOI - oi);
                        data.baseColors.push(isCall ? "rgba(0,128,0,0.3)" : "rgba(255,0,0,0.3)");
                        data.diffColors.push("rgba(255,255,255,1)");
                        data.diffBorders.push(isCall ? "rgba(0,128,0,0.6)" : "rgba(255,0,0,0.6)");
                    }
                }

                // Create chart dataset
                function createDataset(label, data, backgroundColor, stack, borderColor = undefined) {
                    return {
                        label,
                        data,
                        backgroundColor,
                        ...(borderColor && { borderColor, borderWidth: 1 }),
                        stack,
                    };
                }

                // Annotation for chart (vertical spot line)
                function getChartAnnotations() {
                    if (!window.currentStockPrice) return {};
                    return {
                        stockLine: {
                            type: "line",
                            xMin: window.currentStockPrice,
                            xMax: window.currentStockPrice,
                            borderColor: "blue",
                            borderWidth: 2,
                            label: {
                                enabled: true,
                                content: "Spot: " + window.currentStockPrice,
                                position: "start",
                                backgroundColor: "blue",
                                color: "white",
                            },
                        },
                    };
                }

                // Function to handle AI Analysis button click
                window.handleAiAnalysis = function () {
                    if (!window._lastOptionData || !window.currentStockPrice) {
                        showAlert("Please load option chain data first");
                        return;
                    }

                    // Create a data object to pass to the getChainAiAnalysis function
                    const customData = {
                        symbol: document.getElementById('stock-input')?.value.trim() || 'Unknown',
                        chainData: {
                            options: window._lastOptionData.allOptions || []
                        },
                        currentExpiry: window._lastOptionData.currentExpiry,
                        spotPrice: window.currentStockPrice,
                        futurePrices: window.futurePrices || {}
                    };

                    // Call the getChainAiAnalysis function with custom data
                    getChainAiAnalysis(customData);
                };
            });
        </script>

        <!-- SEO -->
        <script>
            // Function to update SEO metadata and URL
            function updateSeoMetadata(symbol) {
                const formattedSymbol = symbol.toUpperCase(); // Ensure consistent casing
                const baseUrl = '/options/chain';
                const newUrl = `${baseUrl}/${formattedSymbol.toLowerCase()}`;
                const newTitle = `${formattedSymbol} Option Chain Analysis: Delta, Gamma, Theta, Vega`;
                const newDescription = `Explore the ${formattedSymbol} option chain with Delta, Gamma, Theta, and Vega for informed trading decisions.`;

                // Update page title
                document.title = newTitle;

                // Update canonical URL
                const canonicalLink = document.getElementById('canonical-link');
                if (canonicalLink) {
                    canonicalLink.href = newUrl;
                }

                // Update Open Graph tags
                document.getElementById('og-url').content = newUrl;
                document.getElementById('og-title').content = newTitle;
                document.getElementById('og-description').content = newDescription;

                // Update Twitter tags
                document.getElementById('twitter-title').content = newTitle;
                document.getElementById('twitter-description').content = `Analyze the ${formattedSymbol} option chain with Delta, Gamma, Theta, and Vega.`;

                // Update browser URL without reloading
                window.history.pushState({ symbol: formattedSymbol }, '', newUrl);

                // Update the page header
                const header = document.getElementById('page-header');
                if (header) {
                    header.textContent = `${formattedSymbol} Option Chain Analysis`;
                }
            }
        </script>

        {% include 'blocks/footer.html' %}
    </div>
</body>

</html>