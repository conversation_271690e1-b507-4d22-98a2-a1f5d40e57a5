<!DOCTYPE html>
<html lang="en">

<head>
    <title>Live Agent - Get Real-Time AI-Powered Support | AI Bull</title>
    <meta name="description"
        content="Chat with our AI-powered live agent for instant support and guidance on your options trading strategy. Get personalized recommendations with AI-driven insights." />
    <link rel="canonical" href="https://theaibull.com/live-agent" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/live-agent" />
    <meta property="og:title" content="Live Agent - Get Real-Time AI-Powered Support" />
    <meta property="og:description"
        content="Chat with our AI-powered live agent for instant support and personalized recommendations to enhance your trading experience." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@TheAIBull" />
    <meta name="twitter:title" content="Live Agent - Get Real-Time AI-Powered Support" />
    <meta name="twitter:description"
        content="Chat with our AI-powered live agent for real-time insights and support to optimize your trading strategy." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    {%include 'blocks/head.html' %}
    <style>
        #transcript-container.open {
            display: flex !important;
        }

        .splash-screen {

            opacity: 1;
            transition: opacity 0.5s ease;
        }

        .splash-logo {
            font-size: 48px;
            color: #fff;
        }

        @media (min-width: 320px) and (max-width: 767px) {
            .splash-logo {
                font-size: 24px;
            }
        }

        #visualizer {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100% !important;
            height: 200px;
            z-index: 99;
        }

        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        #transcript-container.open {
            right: 0px;
        }

        .transcript-item {
            background-color: #404040;
            padding: 13px;
            margin-bottom: 13px;
            border-radius: 12px;
            font-size: 14px;
            color: #FFF;
            margin-bottom: 10px;
        }

        .beta-label {
            margin-left: 10px;
            vertical-align: super;
            text-transform: uppercase;
        }

        .progress-bar {
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #1b1b1b;
            padding: 30px;
            border-radius: 10px;
            z-index: 1000;
        }

        .progress {
            width: 100%;
            background-color: #333;
            border-radius: 5px;
            margin-top: 10px;
        }

        .progress-fill {
            height: 20px;
            width: 0%;
            background-color: #318bd9;
            border-radius: 5px;
            transition: width 1s ease;
        }

        .icon-box:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: #1feaff;
            transition: .5s;
            transform: scale(.9);
            z-index: -1;
        }

        .icon-box:hover:before {
            transform: scale(1);
            box-shadow: 0 0 7px #ff0000;
        }

        .icon-box:hover {
            color: #fff;
            box-shadow: 0 0 20px #ff0000;
        }
    </style>
</head>

<body
    class="bg-neutral-950 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {% include 'blocks/common.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col pb-[62px] xl:pb-0 relative">
        <div>
            <div class="splash-screen bg-neutral-900 absolute flex inset-0 items-center justify-center splash-screen z-20 z-50"
                id="splash">
                <div class="splash-logo text-center">Welcome to AI Live Trading Voice Agent</div>
            </div>
        </div>
        <div class="content" id="content">
            <div class="bg-neutral-800 md:flex gap-4 items-center justify-between px-4 py-2">
                <div class="flex items-cneter">
                    <div class="flex items-center gap-2">
                        <h1 class="text-white text-base md:text-xl font-semibold">
                            AI Live Trading Voice Agent
                        </h1>
                        <span
                            class="bg-blue-800 text-white bg-gradient-to-tr bg-white flex flex-shrink-0 from-pink-700 items-center justify-center px-2 py-0.5 relative rounded-md to-blue-800">Beta</span>
                    </div>
                </div>

                <button id="toggle-transcript"
                    class="bg-neutral-800 transition-all mt-4 w-full md:w-fit md:mt-0 hover:shwdow-2xl font-medium hover:bg-black border border-neutral-700 cursor-pointer flex gap-1 justify-center items-center px-4 py-2 rounded-md text-white">Show
                    Transcript</button>
            </div>
            <div class="flex flex-col xl:h-[calc(100vh_-_94px)] justify-between md:h-[calc(100vh_-_150px)] relative">
                <div
                    class="relative flex items-center justify-center md:h-[calc(100vh_-_95px)] h-[calc(100vh_-_194px)]">
                    <div
                        class="bg-neutral-800 absolute icon-box text-white flex flex-col h-[185px] w-[185px] md:h-[220px] md:w-[220px] items-center justify-center rounded-full text-center cursor-pointer mb-32 duration-300 transistion-all">
                        <div class="absolute flex flex-col items-center justify-center h-full w-full">
                            <span id="start"
                                class="material-icons recording-icon material-icons recording-icon text-[65px] md:text-[100px] cursor-pointer">mic</span>
                            <div class="recording-text mt-3 md:text-base text-sm" id="recording-text">Click to start
                            </div>
                        </div>
                    </div>
                </div>

                <canvas id="visualizer" width="600" height="200"></canvas>
            </div>
        </div>

        <div id="transcript-container" style="display: none;"
            class="absolute md:rounded-l-xl shadow-md top-0 z-[99]  w-full  lg:w-[30%] h-[100vh] p-2.5 bg-[#2b2b2b] overflow-auto flex flex-col transition-[right] duration-500 ease">

            <div class="flex items-center justify-between pb-2.5 mb-2.5 border-b border-neutral-700 w-full">
                <h2 class="text-white text-lg font-semibold text-left ">Transcripts</h2>
                <button id="close-transcript" class="md:text-gray-400 text-white hover:text-red-400 cursor-pointer">

                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-x">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="max-h-[calc(100vh_-_30px)] overflow-auto">
                <div id="transcript-content">
                </div>
            </div>
        </div>
    </div>

    <div id="progress-bar" class="progress-bar text-white">
        <p>Connecting to trading server...</p>
        <div class="progress">
            <div id="progress-fill" class="progress-fill"></div>
        </div>
    </div>
    </div>
    <script>
        window.wsScheme = window.location.protocol === "https:" ? "wss" : "ws";

        window.addEventListener("load", () => {
            const splashScreen = document.getElementById("splash");
            const content = document.getElementById("content");
            const micIcon = document.getElementById("start");
            const transcriptContainer = document.getElementById(
                "transcript-container"
            );
            const toggleButton = document.getElementById("toggle-transcript");
            const closeTranscriptBtn = document.getElementById("close-transcript");
            const recordingText = document.getElementById("recording-text");
            let isRecording = false;
            let isMuted = false;

            setTimeout(() => {
                splashScreen.style.opacity = "0";
                splashScreen.addEventListener("transitionend", () => {
                    splashScreen.style.display = "none";
                    content.style.display = "block";
                });
            }, 2000);

            toggleButton.addEventListener("click", () => {
                if (transcriptContainer.classList.contains("open")) {
                    transcriptContainer.classList.remove("open");
                    toggleButton.textContent = "Show Transcript";
                } else {
                    transcriptContainer.classList.add("open");
                    toggleButton.textContent = "Hide Transcript";
                }
            });


            closeTranscriptBtn.addEventListener("click", () => {
                transcriptContainer.classList.remove("open");
                toggleButton.textContent = "Show Transcript";
            });

            micIcon.addEventListener("click", () => {
                if (!isRecording) {
                    isRecording = true;
                    isMuted = false;
                    recordingText.textContent = "Click to mute";
                    micIcon.style.color = "#1e88e5";
                } else if (isRecording && !isMuted) {
                    isMuted = true;
                    recordingText.textContent = "Click to resume";
                    micIcon.style.color = "#888";
                } else {
                    isMuted = false;
                    recordingText.textContent = "Click to mute";
                    micIcon.style.color = "#1e88e5";
                }
            });
        });


    </script>
    <script type="module" src="/static/js/oai-voice.js?v=3feab753-3fd0-4162-9709-265f5422id7b"></script>
    {% include 'blocks/microsoft/clarity.html' %}
</body>

</html>