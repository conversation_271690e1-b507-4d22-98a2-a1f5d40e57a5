<!DOCTYPE html>
<html lang="en">

{% include 'blocks/head.html' %}

<body class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">
    {%include 'blocks/common.html' %}

    <div class="rhs-block duration-300 transition-width flex flex-col justify-between min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
    style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb) relative">
      <div class="p-3 mx-auto w-full [&_button.bg-neutral-900]:bg-blue-500 [&_button.bg-neutral-900]:text-white [&_button.bg-neutral-900]:hover:bg-blue-600 [&_button.bg-neutral-900]:active:bg-blue-700">
        {{ get_block("apps", { "apps" : apps | tojson }) }}
        </div>
    </div>

    <script>
        // Override the default fetchApps function to use the apps passed in from the server
        async function fetchApps() {
            if (allApps.length === 0) {
                // No need to call JSON.parse, since it's already serialized as JSON in Jinja
                allApps = {{ apps | tojson }};
            }

            console.log("fetchApps ~ allApps:", allApps);
            
            return allApps;
        }

        // Override function to increase the width of the widow for upstox
        function showPopup(url, title, width = 600, height = 700) {

            if (url && url.includes('upstox')) width = 1040;

            // Show app modal popup
            const left = screen.width / 2 - width / 2;
            const top = screen.height / 2 - height / 2;
            window.open(
                url,
                title,
                "toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=" +
                width +
                ", height=" +
                height +
                ", top=" +
                top
            );
        }
    </script>

</body>

</html>