{# Derivatives market section template #}
<div id="derivatives" class="tab-content hidden">
    <div class="bg-white border border-t-0 pb-2 px-3 py-2.5">
        <!-- Category Tabs -->
        <div id="derivatives-category-tabs" class="category-tabs flex flex-wrap gap-2 justify-between">
        </div>
    </div>

    <div class="bg-white rounded-md border mt-3">
        <!-- Legend Tabs -->
        <div id="derivatives-legend-tabs" class="flex flex-wrap gap-2 p-3">
        </div>

        <div class="overflow-x-auto text-sm">
            <table class="w-full table-auto border-collapse">
                <thead class="bg-neutral-100 dark:bg-neutral-800">
                    <tr>
                        <th class="p-2 text-left border">SYMBOL</th>
                        <th class="p-2 text-left border">INSTRUMENT</th>
                        <th class="p-2 text-left">EXPIRY</th>
                        <th class="p-2 text-left border">STRIKE</th>
                        <th class="p-2 text-left border">OPTION TYPE</th>
                        <th class="p-2 text-left border">LTP</th>
                        <th class="p-2 text-left border">%CHNG</th>
                        <th class="p-2 text-left border">VOLUME<span
                                class="block text-xs text-gray-500">(Contracts)</span>
                        </th>
                        <th class="p-2 text-left border">TURNOVER<span class="block text-xs text-gray-500">(₹
                                Lakhs)</span>
                        </th>
                        <th class="p-2 text-left border">OI</th>
                    </tr>
                </thead>
                <tbody id="derivatives-table-body">
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    let currentDerivativesCategory = "{{ selected_derivatives_category | default('most_active') }}";
    let currentDerivativesLegend = "{{ selected_derivatives_legend | default('') }}";
    let currentDerivativesSortBy = 'volume';

    function updateDerivativesTabs() {
        const tabsContainer = document.getElementById('derivatives-category-tabs');
        tabsContainer.innerHTML = '';

        if (!stockData.derivatives) return;

        // Get all available categories from the derivatives data
        const categories = Object.keys(stockData.derivatives);

        // Set initial category if not set
        if (!currentDerivativesCategory) {
            currentDerivativesCategory = 'most_active';
        }

        categories.forEach(category => {
            const tab = document.createElement('a');
            tab.setAttribute('data-category', category);
            tab.className = `category-tab px-3 py-1 rounded-md text-sm font-medium border ${category === currentDerivativesCategory ? 'tab-active bg-blue-500 text-white' :
                'hover:bg-blue-100 dark:hover:bg-blue-900'
                }`;

            // Format the category name for display
            const displayName = category
                .split('_')
                .map(word => {
                    // Handle special cases
                    switch (word.toLowerCase()) {
                        case 'oi': return 'OI';
                        case 'idx': return 'Index';
                        case 'stk': return 'Stock';
                        default: return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                    }
                })
                .join(' ');

            tab.textContent = displayName;

            // Set the href attribute for navigation
            // Convert category from underscore to hyphen format for URLs
            const categoryForUrl = category === 'most_active' ? '' : category;
            tab.href = `/stocks/live/derivatives/${categoryForUrl}`;
            tabsContainer.appendChild(tab);
        });

        // Set initial legend and display data
        updateDerivativesLegendTabs(currentDerivativesCategory);
        displayDerivativesData(currentDerivativesCategory, currentDerivativesLegend);
    }

    function updateDerivativesLegendTabs(category) {
        const legendTabsContainer = document.getElementById('derivatives-legend-tabs');
        legendTabsContainer.innerHTML = '';

        // Get sub-categories based on the selected category
        let legends = [];
        if (category === 'most_active') {
            legends = ['contracts', 'futures', 'options', 'index_calls', 'index_puts', 'stock_calls', 'stock_puts'];
        }

        if (legends.length > 0) {
            // Create legend tabs instead of dropdown
            legends.forEach(legend => {
                const tab = document.createElement('a');
                tab.setAttribute('data-legend', legend);
                tab.className = `legend-tab px-3 py-1 rounded-md text-sm font-medium border ${legend === currentDerivativesLegend ? 'tab-active bg-blue-500 text-white' :
                    'hover:bg-blue-100 dark:hover:bg-blue-900'
                    }`;

                // Format display name
                const displayName = legend
                    .split('_')
                    .map(word => {
                        switch (word.toLowerCase()) {
                            case 'idx': return 'Index';
                            case 'stk': return 'Stock';
                            default: return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                        }
                    })
                    .join(' ');

                tab.textContent = displayName;

                // Convert from underscore to hyphen format for URLs
                const legendForUrl = legend.replace(/_/g, '-');

                // Set href attribute for navigation
                tab.href = `/stocks/live/derivatives/${legendForUrl}`;

                legendTabsContainer.appendChild(tab);
            });

            // Add Sort By dropdown if needed
            if (stockData.derivatives[category]?.contracts) {
                const sortDiv = document.createElement('div');
                sortDiv.className = 'flex items-center gap-2 ml-auto';
                sortDiv.innerHTML = `
                    <span>Sort By</span>
                    <select class="sort-by-select px-3 py-1 rounded border bg-transparent dark:bg-neutral-800 cursor-pointer">
                        <option value="volume" ${currentDerivativesSortBy === 'volume' ? 'selected' : ''}>Volume</option>
                        <option value="value" ${currentDerivativesSortBy === 'value' ? 'selected' : ''}>Value</option>
                    </select>
                `;
                legendTabsContainer.appendChild(sortDiv);

                const sortSelect = sortDiv.querySelector('.sort-by-select');
                sortSelect.addEventListener('change', (e) => {
                    currentDerivativesSortBy = e.target.value;
                    displayDerivativesData(currentDerivativesCategory);
                });
            }
        }
    }

    function displayDerivativesData(category, legend) {
        const tableBody = document.getElementById('derivatives-table-body');
        let data = [];

        if (stockData.derivatives?.[category]) {
            const categoryData = stockData.derivatives[category];
            const legendToUse = legend || currentDerivativesLegend;
            if (legendToUse) {
                switch (legendToUse) {
                    case 'contracts':
                        data = categoryData.contracts?.[currentDerivativesSortBy]?.data || [];
                        break;
                    case 'futures':
                        data = categoryData.futures?.[currentDerivativesSortBy]?.data || [];
                        break;
                    case 'options':
                        data = categoryData.options?.[currentDerivativesSortBy]?.data || [];
                        break;
                    case 'index_calls':
                        data = categoryData.index_calls?.OPTIDX?.data || [];
                        break;
                    case 'index_puts':
                        data = categoryData.index_puts?.OPTIDX?.data || [];
                        break;
                    case 'stock_calls':
                        data = categoryData.stock_calls?.OPTSTK?.data || [];
                        break;
                    case 'stock_puts':
                        data = categoryData.stock_puts?.OPTSTK?.data || [];
                        break;
                }
            } else {
                // Default to contracts if no legend selected
                data = categoryData.contracts?.[currentDerivativesSortBy]?.data || [];
            }
        }

        // Generate table rows with the new format
        tableBody.innerHTML = data.map(stock => `
                <tr class="border-b dark:border-neutral-700">
                    <td class="p-2 border text-left">${stock.underlying || '-'}</td>
                    <td class="p-2 border text-left">${stock.instrument || '-'}</td>
                    <td class="p-2 border text-left">${stock.expiryDate || '-'}</td>
                    <td class="p-2 border text-left">${stock.strikePrice?.toLocaleString() || '-'}</td>
                    <td class="p-2 border text-left">${stock.optionType || '-'}</td>
                    <td class="p-2 border text-left">₹${stock.lastPrice?.toFixed(2) || '-'}</td>
                    <td class="p-2 border text-left ${parseFloat(stock.pChange) > 0 ? 'text-green-500' : 'text-red-500'}">
                        ${stock.pChange?.toFixed(2) || '-'}%
                    </td>
                    <td class="p-2 border text-left">${stock.numberOfContractsTraded?.toLocaleString() || '-'}</td>
                    <td class="p-2 border text-left">${(stock.totalTurnover)?.toFixed(2) || '-'}</td>
                    <td class="p-2 border text-left">${stock.openInterest?.toLocaleString() || '-'}</td>
                </tr>
            `).join('');
    }
</script>