{# Capital market section template #}
<div id="capital" class="tab-content">
    <div class="bg-white border border-t-0 pb-2 px-3 py-2.5">
        <!-- Category Tabs -->
        <div id="capital-category-tabs" class="category-tabs flex flex-wrap gap-2">
        </div>
    </div>

    <div class="bg-white rounded-md border mt-3">
        <!-- Legend Tabs -->
        <div id="legend-tabs" class="flex flex-wrap gap-2 p-3">
        </div>

        <!-- Table Container -->
        <div id="capital-table-container" class="overflow-x-auto text-sm">
            <table class="w-full table-auto border-collapse">
                <thead id="capital-table-head" class="bg-neutral-100 dark:bg-neutral-800">
                </thead>
                <tbody id="capital-table-body">
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    let currentCategory = 'gainers';
    {% if selected_category %}
    currentCategory = '{{ selected_category }}';
    {% endif %}
    let currentLegend = null;
    let currentSortBy = 'volume';

    function updateCapitalTabs() {
        const tabsContainer = document.getElementById('capital-category-tabs');
        tabsContainer.innerHTML = '';

        if (!stockData.capital) return;

        // Get all available categories from the capital data
        const categories = Object.keys(stockData.capital).filter(key => key !== 'legends');

        categories.forEach(category => {
            const tab = document.createElement('a');
            tab.setAttribute('data-category', category);
            tab.className = `category-tab px-3 py-1 rounded-md border font-medium text-sm ${currentCategory === category ?
                'tab-active bg-blue-500 text-white' :
                'hover:bg-blue-100 dark:hover:bg-blue-900'
                }`;

            // Format the category name for display
            const displayName = category
                .split('_')
                .map(word => {
                    switch (word.toLowerCase()) {
                        case '52week': return '52 Week';
                        default: return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                    }
                })
                .join(' ');

            tab.textContent = displayName;

            // Set the href attribute for navigation
            // Convert category from underscore to hyphen format for URLs
            const categoryForUrl = category === 'active' ? '' :
                category === 'advances_declines' ? 'advances-declines' :
                    category === 'new_52week' ? 'new-52week' :
                        category === 'price_band_hitters' ? 'price-band-hitters' :
                            category === 'price_spurts' ? 'price-spurts' :
                                category === 'volume_gainers' ? 'volume-gainers' :
                                    category === 'loosers' ? 'losers' : category;

            tab.href = `/stocks/live/${categoryForUrl}`;

            tabsContainer.appendChild(tab);
        });
    }

    function updateLegendTabs(category) {
        const legendTabsContainer = document.getElementById('legend-tabs');
        legendTabsContainer.innerHTML = '';

        if (!stockData.capital?.[category]) return;

        if (category === 'price_band_hitters') {
            // Add tabs for upper, lower, and both
            const bandTypes = ['upper', 'lower', 'both'];
            currentLegend = currentLegend || bandTypes[0];
            bandTypes.forEach(bandType => {
                const tab = document.createElement('button');
                tab.className = `legend-tab px-3 py-1 rounded-md text-sm font-medium border ${currentLegend === bandType ? 'tab-active bg-blue-500 text-white' :
                    'hover:bg-blue-100 dark:hover:bg-blue-900'
                    }`;
                tab.textContent = bandType.charAt(0).toUpperCase() + bandType.slice(1);
                // For now, we'll keep the legend tabs as client-side filtering
                // since they don't have dedicated URLs in the backend
                tab.addEventListener('click', (e) => {
                    e.preventDefault();
                    document.querySelectorAll('.legend-tab').forEach(t => {
                        t.classList.remove('tab-active', 'bg-blue-500', 'text-white');
                    });
                    tab.classList.add('tab-active', 'bg-blue-500', 'text-white');
                    currentLegend = bandType;
                    displayCapitalData(currentCategory);
                });
                legendTabsContainer.appendChild(tab);
            });

            // Add count information
            const counts = stockData.capital.price_band_hitters.count;
            if (counts) {
                const countInfo = document.createElement('div');
                countInfo.className = 'ml-auto text-sm';
                countInfo.innerHTML = `
                        Total: ${counts.TOTAL || 0} |
                        Upper: ${counts.UPPER || 0} |
                        Lower: ${counts.LOWER || 0} |
                        Both: ${counts.BOTH || 0}
                    `;
                legendTabsContainer.appendChild(countInfo);
            }
        } else if (category === 'new_52week') {
            // Add tabs for high and low
            const types = ['high', 'low'];
            currentLegend = currentLegend || types[0];
            types.forEach(type => {
                const tab = document.createElement('button');
                tab.className = `legend-tab px-3 py-1 rounded-md text-sm font-medium border ${currentLegend === type ? 'tab-active bg-blue-500 text-white' :
                    'hover:bg-blue-100 dark:hover:bg-blue-900'
                    }`;
                tab.textContent = `52 Week ${type.charAt(0).toUpperCase() + type.slice(1)}`;
                tab.addEventListener('click', (e) => {
                    e.preventDefault();
                    document.querySelectorAll('.legend-tab').forEach(t => {
                        t.classList.remove('tab-active', 'bg-blue-500', 'text-white');
                    });
                    tab.classList.add('tab-active', 'bg-blue-500', 'text-white');
                    currentLegend = type;

                    displayCapitalData(currentCategory);
                });
                legendTabsContainer.appendChild(tab);
            });
        } else if (category === 'advances_declines') {
            // Get available types from the data structure
            const types = Object.keys(stockData.capital.advances_declines)
                .filter(key => key !== 'count' && key !== 'timestamp'); // Filter out non-legend keys

            currentLegend = currentLegend || types[0];

            types.forEach(type => {
                const tab = document.createElement('button');
                tab.className = `legend-tab px-3 py-1 rounded-md text-sm font-medium border ${currentLegend === type ? 'tab-active bg-blue-500 text-white' :
                    'hover:bg-blue-100 dark:hover:bg-blue-900'
                    }`;

                // Get the display name from the identifier in the data
                const displayName = stockData.capital.advances_declines[type]?.identifier ||
                    type.charAt(0).toUpperCase() + type.slice(1);
                tab.textContent = displayName;

                tab.addEventListener('click', (e) => {
                    e.preventDefault();
                    document.querySelectorAll('.legend-tab').forEach(t => {
                        t.classList.remove('tab-active', 'bg-blue-500', 'text-white');
                    });
                    tab.classList.add('tab-active', 'bg-blue-500', 'text-white');
                    currentLegend = type;
                    displayCapitalData(currentCategory);
                });
                legendTabsContainer.appendChild(tab);
            });

            // Add count information if available
            const counts = stockData.capital.advances_declines.count;
            if (counts) {
                const countInfo = document.createElement('div');
                countInfo.className = 'ml-4 text-sm';
                countInfo.innerHTML = `
                        <span class="text-green-500">Advances - ${counts.Advances || 0}</span> |
                        <span class="text-red-500">Declines - ${counts.Declines || 0}</span>
                    `;
                legendTabsContainer.appendChild(countInfo);
            }
        } else if (category === 'active') {
            // Add main legend tabs (Main Board, SME, ETFs, etc.)
            const legends = Object.keys(stockData.capital.active).filter(key => key !== 'timestamp');
            currentLegend = currentLegend || legends[0];
            legends.forEach(legend => {
                const tab = document.createElement('button');
                tab.className = `legend-tab px-3 py-1 rounded-md text-sm font-medium border ${currentLegend === legend ? 'tab-active bg-blue-500 text-white' :
                    'hover:bg-blue-100 dark:hover:bg-blue-900'
                    }`;

                // Format the display name
                const displayName = legend
                    .split('_')
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                    .join(' ');
                tab.textContent = displayName;

                tab.addEventListener('click', (e) => {
                    e.preventDefault();
                    document.querySelectorAll('.legend-tab').forEach(t => {
                        t.classList.remove('tab-active', 'bg-blue-500', 'text-white');
                    });
                    tab.classList.add('tab-active', 'bg-blue-500', 'text-white');
                    currentLegend = legend;
                    displayCapitalData(currentCategory);
                });
                legendTabsContainer.appendChild(tab);
            });

            // Add Sort By dropdown
            const sortDiv = document.createElement('div');
            sortDiv.className = 'flex items-center gap-4 ml-auto';
            sortDiv.innerHTML = `
                    <span>Sort By</span>
                    <select class="sort-by-select px-3 py-1 rounded border bg-transparent dark:bg-neutral-800 cursor-pointer">
                        <option value="volume" ${currentSortBy === 'volume' ? 'selected' : ''}>Volume</option>
                        <option value="value" ${currentSortBy === 'value' ? 'selected' : ''}>Value</option>
                    </select>
                `;
            legendTabsContainer.appendChild(sortDiv);

            // Add event listener for sort dropdown
            const sortSelect = sortDiv.querySelector('.sort-by-select');
            sortSelect.addEventListener('change', (e) => {
                currentSortBy = e.target.value;
                displayCapitalData(currentCategory);
            });
        } else {
            // Create legend name mappings dynamically from the legends array
            const legendNames = {};
            if (stockData.capital[category].legends) {
                stockData.capital[category].legends.forEach(legendArr => {
                    if (Array.isArray(legendArr) && legendArr.length >= 2) {
                        legendNames[legendArr[0]] = legendArr[1];
                    }
                });
            }

            // Add "All" tab
            const allTab = document.createElement('button');
            allTab.className = `legend-tab px-3 py-1 rounded-md text-sm font-medium border ${!currentLegend ? 'tab-active bg-blue-500 text-white' : ''}`;
            allTab.textContent = 'All';
            allTab.addEventListener('click', (e) => {
                e.preventDefault();
                document.querySelectorAll('.legend-tab').forEach(t => t.classList.remove('tab-active', 'bg-blue-500', 'text-white'));
                allTab.classList.add('tab-active', 'bg-blue-500', 'text-white');
                currentLegend = null;
                displayCapitalData(currentCategory);
            });
            legendTabsContainer.appendChild(allTab);

            // Add legend-specific tabs
            Object.keys(stockData.capital[category])
                .filter(key => key !== 'legends' && stockData.capital[category][key].data) // Filter out 'legends' key and ensure data exists
                .forEach(legend => {
                    const tab = document.createElement('button');
                    tab.className = `legend-tab px-3 py-1 rounded-md text-sm font-medium border ${currentLegend === legend ? 'tab-active bg-blue-500 text-white' : ''}`;
                    // Use the friendly name from the legends array if available
                    tab.textContent = legendNames[legend] || legend;
                    tab.addEventListener('click', (e) => {
                        e.preventDefault();
                        document.querySelectorAll('.legend-tab').forEach(t => t.classList.remove('tab-active', 'bg-blue-500', 'text-white'));
                        tab.classList.add('tab-active', 'bg-blue-500', 'text-white');
                        currentLegend = legend;
                        displayCapitalData(currentCategory);
                    });
                    legendTabsContainer.appendChild(tab);
                });
        }
    }

    function displayCapitalData(category) {
        const tableHead = document.getElementById('capital-table-head');
        const tableBody = document.getElementById('capital-table-body');

        // Update legend tabs
        updateLegendTabs(category);

        // Define columns based on category
        let columns;
        if (category === 'volume_gainers') {
            columns = [
                'SYMBOL', 'SECURITY', 'VOLUME',
                'AVG. VOLUME', 'CHANGE', 'AVG. VOLUME', 'CHANGE',
                'LTP', '% CHNG', 'TURNOVER'
            ];
        } else if (category === 'price_band_hitters') {
            columns = [
                'SYMBOL', 'SERIES', 'LTP', '%CHNG', 'PRICE BAND %',
                'VOLUME (Lakhs)', 'VALUE (Crores)'
            ];
        } else if (category === 'new_52week') {
            columns = [
                'SYMBOL', 'SERIES', 'LTP', '%CHNG', 'NEW 52W/H PRICE',
                'PREV.HIGH', 'PREV. HIGH DATE'
            ];
        } else if (category === 'advances_declines') {
            columns = [
                'SYMBOL', 'SERIES', 'LTP', '%CHNG', 'MKT CAP',
                'VOLUME', 'VALUE'
            ];
        } else {
            columns = [
                'SYMBOL', 'OPEN', 'HIGH', 'LOW', 'PREV CLOSE', 'LTP', '%CHNG',
                'VOLUME (Shares)', 'VALUE (Lakhs)', 'CA'
            ];
        }

        // Set table headers
        tableHead.innerHTML = `
                ${category === 'volume_gainers' ? `
                <tr>
                    <th class="p-2 text-left border">SYMBOL</th>
                    <th class="p-2 text-left border">SECURITY</th>
                    <th class="text-left border py-1 px-2">VOLUME</th>
                    <th class="p-2 text-center" colspan="2">1 WEEK</th>
                    <th class="p-2 text-center" colspan="2">2 WEEK</th>
                    <th class="text-left border py-1 px-2">LTP</th>
                    <th class="text-left border py-1 px-2">% CHNG</th>
                    <th class="text-left border py-1 px-2">TURNOVER (Lakhs)</th>
                </tr>
                <tr>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th class="text-left border py-1 px-2">AVG. VOLUME</th>
                    <th class="text-left border py-1 px-2">CHANGE<br>(No. of times)</th>
                    <th class="text-left border py-1 px-2">AVG. VOLUME</th>
                    <th class="text-left border py-1 px-2">CHANGE<br>(No. of times)</th>
                    <th></th>
                    <th></th>
                    <th></th>
                </tr>
                ` : category === 'price_band_hitters' ? `
                <tr>
                    <th class="p-2 border text-left">SYMBOL</th>
                    <th class="p-2 border text-left">SERIES</th>
                    <th class="text-left border py-1 px-2">LTP</th>
                    <th class="text-left border py-1 px-2">%CHNG</th>
                    <th class="text-left border py-1 px-2">PRICE BAND %</th>
                    <th class="text-left border py-1 px-2">VOLUME</th>
                    <th class="text-left border py-1 px-2">VALUE</th>
                </tr>
                ` : category === 'advances_declines' ? `
                <tr>
                    <th class="p-2 text-left border">SYMBOL</th>
                    <th class="p-2 text-left border">SERIES</th>
                    <th class="text-left border py-1 px-2">LTP</th>
                    <th class="text-left border py-1 px-2">%CHNG</th>
                    <th class="text-left border py-1 px-2">MKT CAP<br>(Crores)</th>
                    <th class="text-left border py-1 px-2">VOLUME<br>(Lakhs)</th>
                    <th class="text-left border py-1 px-2">VALUE<br>(Crores)</th>
                </tr>
                ` : columns.map(col => `<th class="p-2 text-left border">${col}</th>`).join('')}
            `;

        // Get and display data based on category
        let data = [];
        switch (category) {
            case 'gainers':
            case 'loosers':
                if (stockData.capital?.[category]) {
                    if (currentLegend) {
                        data = stockData.capital[category][currentLegend]?.data || [];
                    } else {
                        Object.values(stockData.capital[category]).forEach(group => {
                            if (group.data) data = data.concat(group.data);
                        });
                    }
                }
                break;
            case 'active':
                if (stockData.capital?.active) {
                    const section = currentLegend || 'securities';
                    data = stockData.capital.active[section]?.[currentSortBy]?.data || [];
                }
                break;
            case 'volume_gainers':
                data = stockData.capital?.volume_gainers?.data || [];
                break;
            case 'price_spurts':
                data = stockData.capital?.price_spurts?.data || [];
                break;
            case 'price_band_hitters':
                if (stockData.capital?.price_band_hitters) {
                    const bandType = currentLegend || 'upper'; // default to upper
                    const section = 'AllSec'; // default to AllSec
                    data = stockData.capital.price_band_hitters[bandType]?.[section]?.data || [];
                }
                break;
            case 'new_52week':
                if (currentLegend === 'low') {
                    data = stockData.capital?.new_52week?.low?.data || [];
                } else {
                    data = stockData.capital?.new_52week?.high?.data || [];
                }
                break;
            case 'advances_declines':
                if (stockData.capital?.advances_declines) {
                    const type = currentLegend || 'advance'; // default to advance
                    data = stockData.capital.advances_declines[type]?.[type]?.data || [];
                }
                break;
        }

        // Generate table rows
        if (category === 'volume_gainers') {
            tableBody.innerHTML = data.map(stock => `
                    <tr class="border-b dark:border-neutral-700">
                        <td class="text-left border py-1 px-2">${stock.symbol || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.companyName || '-'}</td>
                        <td class=text-left border py-1 px-2">${stock.volume?.toLocaleString() || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.week1AvgVolume?.toLocaleString() || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.week1volChange?.toFixed(2) || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.week2AvgVolume?.toLocaleString() || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.week2volChange?.toFixed(2) || '-'}</td>
                        <td class="text-left border py-1 px-2 ">${stock.ltp || '-'}</td>
                        <td class="text-left border py-1 px-2 ${parseFloat(stock.pChange) > 0 ? 'text-green-500' : 'text-red-500'}">
                            ${stock.pChange || '-'}%
                        </td>
                        <td class="text-left border py-1 px-2">${stock.turnover?.toLocaleString() || '-'}</td>
                    </tr>
                `).join('');
        } else if (category === 'price_band_hitters') {
            tableBody.innerHTML = data.map(stock => `
                    <tr class="border-b dark:border-neutral-700">
                        <td class="p-2 text-left border">${stock.symbol || '-'}</td>
                        <td class="p-2 text-left border">${stock.series || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.ltp || '-'}</td>
                        <td class="text-left border py-1 px-2 ${parseFloat(stock.pChange) > 0 ? 'text-green-500' : 'text-red-500'}">
                            ${parseFloat(stock.pChange).toFixed(2) || '-'}%
                        </td>
                        <td class="text-left border py-1 px-2">${stock.priceBand || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.totalTradedVol?.toFixed(2) || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.turnover?.toFixed(2) || '-'}</td>
                    </tr>
                `).join('');
        } else if (category === 'new_52week') {
            tableBody.innerHTML = data.map(stock => `
                    <tr class="border-b dark:border-neutral-700">
                        <td class="p-2 text-left border">${stock.symbol || '-'}</td>
                        <td class="p-2 text-left border">${stock.series || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.ltp || '-'}</td>
                        <td class="text-left border py-1 px-2 ${parseFloat(stock.pChange) > 0 ? 'text-green-500' : 'text-red-500'}">
                            ${parseFloat(stock.pChange).toFixed(2) || '-'}%
                        </td>
                        <td class="text-left border py-1 px-2">${stock.new52WHL || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.prev52WHL || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.prevHLDate || '-'}</td>
                    </tr>
                `).join('');
        } else if (category === 'advances_declines') {
            tableBody.innerHTML = data.map(stock => `
                    <tr class="border-b dark:border-neutral-700">
                        <td class="p-2 text-left border">${stock.symbol || '-'}</td>
                        <td class="p-2 text-left border">${stock.series || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.lastPrice || '-'}</td>
                        <td class="text-left border py-1 px-2 ${parseFloat(stock.pchange) > 0 ? 'text-green-500' : 'text-red-500'}">
                            ${stock.pchange?.toFixed(2) || '-'}%
                        </td>
                        <td class="text-left border py-1 px-2">${stock.totalMarketCap?.toFixed(2) || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.totalTradedVolume?.toFixed(2) || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.totalTradedValue?.toFixed(2) || '-'}</td>
                    </tr>
                `).join('');
        } else if (category === 'active') {
            // Set table headers for most active
            tableHead.innerHTML = `
                    <tr>
                        <th class="p-2 text-left border">SYMBOL</th>
                        <th class="p-2 text-left border">SERIES</th>
                        <th class="text-left border py-1 px-2">OPEN</th>
                        <th class="text-left border py-1 px-2">HIGH</th>
                        <th class="text-left border py-1 px-2">LOW</th>
                        <th class="text-left border py-1 px-2">PREV.<br>CLOSE</th>
                        <th class="text-left border py-1 px-2">LTP</th>
                        <th class="text-left border py-1 px-2">%CHNG</th>
                        <th class="text-left border py-1 px-2">VOLUME<br>(Shares)</th>
                        <th class="text-left border py-1 px-2">VALUE<br>(Lakhs)</th>
                        <th class="p-2 text-left border">CA</th>
                    </tr>
                `;

            tableBody.innerHTML = data.map(stock => `
                    <tr class="border-b dark:border-neutral-700">
                        <td class="p-2 text-left border">${stock.symbol || '-'}</td>
                        <td class="p-2 text-left border">${stock.identifier?.replace(stock.symbol, '') || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.open || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.dayHigh || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.dayLow || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.previousClose || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.lastPrice || '-'}</td>
                        <td class="text-left border py-1 px-2 ${parseFloat(stock.pChange) > 0 ? 'text-green-500' : 'text-red-500'}">
                            ${stock.pChange?.toFixed(2) || '-'}%
                        </td>
                        <td class="text-left border py-1 px-2">${stock.totalTradedVolume?.toLocaleString() || '-'}</td>
                        <td class="text-left border py-1 px-2">${(stock.totalTradedValue / 100000)?.toFixed(2) || '-'}</td>
                        <td class="p-2 text-left border">${stock.purpose || '-'}</td>
                    </tr>
                `).join('');
        } else {
            tableBody.innerHTML = data.map(stock => `
                    <tr class="border-b dark:border-neutral-700">
                        <td class="p-2 text-left border">${stock.symbol || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.open_price || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.high_price || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.low_price || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.prev_price || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.ltp || '-'}</td>
                        <td class="text-left border py-1 px-2 ${parseFloat(stock.net_price || stock.pChange) > 0 ? 'text-green-500' : 'text-red-500'}">
                            ${stock.net_price || stock.pChange || '-'}%
                        </td>
                        <td class="text-left border py-1 px-2">${stock.trade_quantity?.toLocaleString() || '-'}</td>
                        <td class="text-left border py-1 px-2">${stock.turnover?.toLocaleString() || '-'}</td>
                        <td class="p-2 text-left border">${stock.ca_purpose || '-'}</td>
                    </tr>
                `).join('');
        }
    }

</script>