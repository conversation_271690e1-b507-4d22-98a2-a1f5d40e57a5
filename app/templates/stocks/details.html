<!DOCTYPE html>
<html lang="en">

<head>
    <title>
        {% if stock_data %}
        {{ stock_data.info.longName or stock_data.info.shortName }} ({{ symbol }}) | Stock Details | AI Bull
        {% else %}
        Stock Details | AI Bull
        {% endif %}
    </title>
    <meta name="description"
        content="{% if stock_data %}View detailed stock price and share price information for {{ stock_data.info.longName or stock_data.info.shortName }} ({{ symbol }}), including price history, market cap, P/E ratio, and more.{% else %}Explore stock price and share price details for informed investment decisions.{% endif %}" />
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical"
        href="https://theaibull.com/stocks/{% if symbol %}{{ symbol|lower }}{% else %}screener2{% endif %}" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:type" content="website" />
    <meta property="og:url"
        content="https://theaibull.com/stocks/{% if symbol %}{{ symbol|lower }}{% else %}screener2{% endif %}" />
    <meta property="og:title"
        content="{% if stock_data %}{{ stock_data.info.longName or stock_data.info.shortName }} ({{ symbol }}) | Stock Price & Details{% else %}Stock Details{% endif %}" />
    <meta property="og:description"
        content="{% if stock_data %}View detailed stock price and share price information for {{ stock_data.info.longName or stock_data.info.shortName }} ({{ symbol }}) to make informed investment decisions.{% else %}Explore stock price and share price details to make informed investment decisions.{% endif %}" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title"
        content="{% if stock_data %}{{ stock_data.info.longName or stock_data.info.shortName }} ({{ symbol }}) | Stock Price & Details{% else %}Stock Details{% endif %}" />
    <meta name="twitter:description"
        content="{% if stock_data %}View detailed stock price and share price information for {{ stock_data.info.longName or stock_data.info.shortName }} ({{ symbol }}) to make informed investment decisions.{% else %}Explore stock price and share price details to make informed investment decisions.{% endif %}" />
    {% include 'blocks/head.html' %}
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {% include 'blocks/common.html' %}
    {% include 'blocks/stock-symbols.html' %}
    {% include 'blocks/ai-feedback.html' %}

    <div class="rhs-block duration-300 transition-width relative pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <div class="p-3 mx-auto max-w-7xl">
            <main>
                <div>
                    <!-- Search Tab Content -->
                    <div id="search-content" class="tab-content">
                        <div class="flex flex-col gap-3 mb-3">
                            <!-- Search Input Area -->
                            <div class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                                <!-- Stock Input Form -->
                                <div>
                                    <div class="flex flex-col gap-6">
                                        <div class="w-full">
                                            {% with show_submit_button=true, show_indices=false %}
                                            {% include 'blocks/search-active-recent-stocks.html' %}
                                            {% endwith %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="error-state">

                    </div>

                </div>
            </main>

            {% if stock_data %}
            <div>
                <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4">
                    <!-- Header Section -->
                    <div class="w-full mb-6">
                        <div class="flex justify-between items-start">
                            <div>
                                <h1
                                    class="bg-clip-text bg-gradient-to-r font-semibold from-blue-600 inline-block mb-2 text-3xl text-transparent to-indigo-600 via-pink-600">
                                    {{ stock_data.info.longName or stock_data.info.shortName }}
                                </h1>
                                <p class="text-gray-600 dark:text-gray-300">
                                    {{ stock_data.info.industryDisp }} | {{ stock_data.info.sectorDisp }}
                                </p>
                            </div>
                            <button id="stockAiAnalysisBtn" onclick="getStockAiAnalysis()"
                                class="animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-sparkles">
                                    <path
                                        d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                    <path d="M20 3v4" />
                                    <path d="M22 5h-4" />
                                    <path d="M4 17v2" />
                                    <path d="M5 18H3" />
                                </svg> Get AI Analysis
                            </button>
                        </div>
                    </div>

                    <!-- Overview Section -->
                    <section>
                        <h2 class="text-lg font-semibold mb-2">Company Overview</h2>
                        <p class="dark:text-gray-200 leading-6 text-gray-600 text-sm">
                            {{ stock_data.info.longBusinessSummary }}
                            {% if stock_data.info.installedCapacity %}
                            The company has an installed capacity of {{ stock_data.info.installedCapacity }} MW/units,
                            showcasing its operational strength in the {{ stock_data.info.sector }} sector.
                            {% endif %}
                        </p>
                    </section>
                </div>

                <!-- Company Details Section -->
                <section class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4">
                    <div>
                        <h3 class="text-lg font-semibold mb-2">Key Information</h3>
                        <div>
                            <div class="grid lg:grid-cols-4 md:grid-cols-3 grid-cols-1 ">
                                {% if stock_data.info.marketCap is not none %}
                                <div class="p-3 rounded-lg">
                                    <p class="text-neutral-700 text-base mb-1">Market Cap</p>
                                    <p class="font-medium text-lg money">{{ stock_data.info.marketCap }}</p>
                                </div>
                                {% endif %}

                                {% if stock_data.info.currentPrice is not none %}
                                <div class="p-3  rounded-lg">
                                    <p class="text-neutral-700 text-base mb-1">Current Price</p>
                                    <p class="font-medium text-lg money">{{ stock_data.info.currentPrice }}</p>
                                </div>
                                {% endif %}

                                {% if stock_data.info.fiftyTwoWeekRange %}
                                <div class="p-3 rounded-lg">
                                    <p class="text-neutral-700 text-base mb-1">52 Week Range</p>
                                    <p class="font-medium text-lg">{{ stock_data.info.fiftyTwoWeekRange }}</p>
                                </div>
                                {% endif %}

                                {% if stock_data.info.previousClose is not none %}
                                <div class="p-3 rounded-lg">
                                    <p class="text-neutral-700 text-base mb-1">Previous Close</p>
                                    <p class="font-medium text-lg money">{{ stock_data.info.previousClose }}</p>
                                </div>
                                {% endif %}

                                {% if stock_data.info.open is not none %}
                                <div class="p-3  rounded-lg">
                                    <p class="text-neutral-700 text-base mb-1">Open</p>
                                    <p class="font-medium text-lg money">{{ stock_data.info.open }}</p>
                                </div>
                                {% endif %}
                                
                                {% if stock_data.info.industry %}
                                <div class="p-3 rounded-lg">
                                    <p class="text-neutral-700 text-base mb-1">Industry</p>
                                    <a href="/stocks/{{ format_text_to_url(stock_data.info.industry) }}-industry" class="text-blue-500 hover:underline">
                                        <p class="font-medium text-lg">{{ stock_data.info.industry }}</p>
                                    </a>
                                </div>
                                {% endif %}

                                {% if stock_data.info.sector %}
                                <div class="p-3 rounded-lg">
                                    <p class="text-neutral-700 text-base mb-1">Sector</p>
                                    <a href="/stocks/{{ format_text_to_url(stock_data.info.sector) }}-sector" class="text-blue-500 hover:underline">
                                        <p class="font-medium text-lg">{{ stock_data.info.sector }}</p>
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>


                    </div>

                </section>

                <section class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4">
                    <h3 class="text-lg font-semibold mb-2">Contact Details</h3>
                    <div class="">
                        <div class="grid lg:grid-cols-4 md:grid-cols-3 grid-cols-1 ">
                            <div class="p-3 cols-span-1  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">Address</p>
                                <p class="font-medium">{{ stock_data.info.address1 }},
                                    {{ stock_data.info.address2 }},
                                    {{ stock_data.info.city }},
                                    {{ stock_data.info.zip }},
                                    {{ stock_data.info.country }}
                                </p>
                            </div>
                            {% if stock_data.info.phone %}
                            <div class="p-3  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">Phone</p>
                                <p class="font-medium">{{ format_phone_number(stock_data.info.phone) }}</p>
                            </div>
                            {% endif %}

                            {% if stock_data.info.fax %}
                            <div class="p-3  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">Fax</p>
                                <p class="font-medium">{{ format_phone_number(stock_data.info.fax) }}</p>
                            </div>
                            {% endif %}

                            {% if stock_data.info.website %}
                            <div class="p-3 rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">Website</p>
                                <a href="{{ stock_data.info.website }}" target="_blank"
                                    class="text-blue-500 hover:underline">
                                    <p class="font-medium break-all">{{ stock_data.info.website }}</p>
                                </a>
                            </div>
                            {% endif %}

                        </div>
                    </div>
                </section>

                <!-- Financial Metrics Section -->
                <section class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4">
                    <h3 class="text-lg font-semibold mb-2">Financials</h3>
                    <div class="">
                        <div class="grid lg:grid-cols-4 md:grid-cols-3 grid-cols-1 ">
                            {% if stock_data.info.dayLow is not none %}
                            <div class="p-3  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">Day Low</p>
                                <p class="font-medium text-lg money">{{ stock_data.info.dayLow }}</p>
                            </div>
                            {% endif %}
                            {% if stock_data.info.dayHigh is not none %}
                            <div class="p-3  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">Day High</p>
                                <p class="font-medium text-lg money">{{ stock_data.info.dayHigh }}</p>
                            </div>
                            {% endif %}
                            {% if stock_data.info.dividendYield is not none %}
                            <div class="p-3  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">Dividend Yield</p>
                                <p class="font-medium text-lg">{{ stock_data.info.dividendYield }}</p>
                            </div>
                            {% endif %}
                            {% if stock_data.info.trailingPE is not none %}
                            <div class="p-3  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">PE Ratio</p>
                                <p class="font-medium text-lg">{{ stock_data.info.trailingPE }}</p>
                            </div>
                            {% endif %}
                            {% if stock_data.info.trailingEps is not none %}
                            <div class="p-3  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">Earnings Per Share</p>
                                <p class="font-medium text-lg money">{{ stock_data.info.trailingEps }}</p>
                            </div>
                            {% endif %}
                            {% if stock_data.info.beta is not none %}
                            <div class="p-3  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">Beta</p>
                                <p class="font-medium text-lg">{{ stock_data.info.beta }}</p>
                            </div>
                            {% endif %}
                            {% if stock_data.info.ebitda is not none %}
                            <div class="p-3  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">EBITDA</p>
                                <p class="font-medium text-lg money">{{ stock_data.info.ebitda }}</p>
                            </div>
                            {% endif %}
                            {% if stock_data.info.bookValue is not none %}
                            <div class="p-3  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">Book Value</p>
                                <p class="font-medium text-lg money">{{ stock_data.info.bookValue }}</p>
                            </div>
                            {% endif %}
                            {% if stock_data.info.epsCurrentYear is not none %}
                            <div class="p-3  rounded-lg">
                                <p class="text-neutral-700 text-base mb-1">EPS</p>
                                <p class="font-medium text-lg money">{{ stock_data.info.epsCurrentYear }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </section>

                <!-- Company Officers Section -->
                <section class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4">
                    <h3 class="text-lg font-semibold mb-2">Company Officers</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full table-auto border-collapse text-sm">
                            <thead class="bg-neutral-100 dark:bg-neutral-800">
                                <tr>
                                    <th class="p-2 border text-left">Name</th>
                                    <th class="p-2 border text-left">Title</th>
                                    <th class="p-2 border text-left">Age</th>
                                    <th class="p-2 border text-left">Year Born</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for officer in stock_data.info.companyOfficers %}
                                <tr class="border-b dark:border-neutral-700">
                                    <td class="p-2 border">{{ officer.name }}</td>
                                    <td class="p-2 border">{{ officer.title }}</td>
                                    <td class="p-2 border">{{ officer.age or '-' }}</td>
                                    <td class="p-2 border">{{ officer.yearBorn or '-' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </section>
                {% include 'stocks/blocks/in-depth.html' %}

                <!-- History -->
                {% with unique_id="price_history"%}
                <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4">
                    {% include 'stocks/blocks/price-history-chart.html' %}
                </div>
                {% endwith %}

                <!-- Macro for rendering cards -->
                {% macro render_card(item, index, url_field='url', title_field='title', desc_field='description') %}
                <a href="/stocks/{{ item[url_field] }}" class="flex-shrink-0 p-3 rounded-lg cursor-pointer transition-transform w-72 hover:-translate-y-0.5 hover:shadow-md 
                    {% if index % 5 == 0 %} bg-blue-500/20
                    {% elif index % 5 == 1 %} bg-emerald-500/20
                    {% elif index % 5 == 2 %} bg-purple-500/20
                    {% elif index % 5 == 3 %} bg-yellow-500/20
                    {% elif index % 5 == 4 %} bg-red-500/20
                    {% endif %}">
                    <h3 class="font-semibold text-base mb-1">{{ item[title_field] }}</h3>
                    <p class="text-xs text-gray-700 dark:text-gray-300">{{ item[desc_field] }}</p>
                </a>
                {% endmacro %}

                <!-- Interested Stocks Section -->
                <div id="interested-stocks" class="p-4 bg-white dark:bg-gray-800 rounded-lg mb-6">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">You May Be Interested In</h3>
                        <div>
                            <button id="interested-left-arrow"
                                class="border h-10 w-10 bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                aria-label="Scroll left">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M6 8L2 12L6 16"></path>
                                    <path d="M2 12H22"></path>
                                </svg>
                            </button>
                            <button id="interested-right-arrow"
                                class="h-10 w-10 border bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                aria-label="Scroll right">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M18 8L22 12L18 16"></path>
                                    <path d="M2 12H22"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div id="interested-stocks-cards" class="flex flex-col gap-3 md:flex-row overflow-hidden relative">
                        {% for stock in interested_stocks %}
                        <a href="/stocks/{{ stock.symbol | replace('.NS', '') | lower }}" class="flex-shrink-0 p-3 rounded-lg cursor-pointer transition-colors w-72 hover:-translate-y-0.5 hover:shadow-md
                                {% if loop.index0 % 5 == 0 %} bg-blue-500/20
                                {% elif loop.index0 % 5 == 1 %} bg-emerald-500/20
                                {% elif loop.index0 % 5 == 2 %} bg-purple-500/20
                                {% elif loop.index0 % 5 == 3 %} bg-yellow-500/20
                                {% elif loop.index0 % 5 == 4 %} bg-red-500/20
                                {% endif %}">
                            <h3 class="font-semibold text-base mb-1">{{ stock.info.longName | default(stock.symbol) }}</h3>
                            <p class="text-xs text-gray-700 dark:text-gray-300">{{ stock.info.sector }} • {{ stock.info.industry }}</p>
                        </a>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Explore More Stocks Section -->
                <div class="p-4 bg-white dark:bg-gray-800 rounded-lg mb-6 transition-all duration-300">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Explore More Stocks</h3>
                        <div>
                            <button id="screener-left-arrow"
                                class="h-10 w-10 border bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                aria-label="Scroll left">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M6 8L2 12L6 16"></path>
                                    <path d="M2 12H22"></path>
                                </svg>
                            </button>
                            <button id="screener-right-arrow"
                                class="h-10 w-10 border bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                aria-label="Scroll right">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M18 8L22 12L18 16"></path>
                                    <path d="M2 12H22"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div id="preset-screener-cards" class="flex flex-col gap-3 md:flex-row overflow-hidden relative">
                        {% for filter in random_filters %}
                        {{ render_card(filter, loop.index0) }}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Formatters -->
    <script>
        // Shared number parsing function
        const parseNumber = (text) => {
            const match = text.match(/[\d,.]+/g);
            return match ? parseFloat(match.join('').replace(/,/g, '')) : NaN;
        };

        // Shared currency formatting function
        const formatCurrency = (value, isCrore = false) => {
            if (isNaN(value)) return null;
            return "₹ " + formatIndianNumber(value) + (isCrore ? " Cr" : "");
        };

        // Process elements
        document.querySelectorAll('.crmoney').forEach(el => {
            const value = parseNumber(el.textContent);
            const formatted = formatCurrency(value, true);
            if (formatted) el.textContent = formatted;
        });
        document.querySelectorAll('.money').forEach(el => {
            const value = parseNumber(el.textContent);
            const formatted = formatCurrency(value);
            if (formatted) el.textContent = formatted;
        });
    </script>
    <script>
        {% if symbol %}
        window.searchedSymbol = {{ symbol | tojson }};
        {% endif %}

        async function fetchStockData(symbol) {
            try {

                // Fetch stock data from the API
                const response = await fetch(`/screener/api/${symbol}`);
                if (!response.ok) throw new Error('Invalid stock symbol or API error');
                const stockData = await response.json();

                // Validate response
                if (!stockData || Object.keys(stockData).length === 0) throw new Error('No data returned for the symbol');

                window.stockData = stockData;
                historyData = stockData.history || {};
                filterData("1M");
            } catch (error) {
                console.error('Error fetching stock data:', error);
            }
        }


        // Listen for custom event from search component
        document.addEventListener('stockSearchTriggered', (e) => {
            const symbol = e.detail.symbol;
            if (symbol) {
                handleSearch(symbol);
            }
        });
        function handleSearch(symbol) {
            // Update the route without reloading the page
            window.location.href = `/stocks/${symbol.toLowerCase()}`;
        }

        // Function to prepare and send stock data to AI Analysis
        function getStockAiAnalysis() {
            if (!window.stockData) {
                showAlert("No stock data available for analysis");
                return;
            }

            // Prepare the payload with the stock data
            const stockInfo = window.stockData.info;
            const payload = {
                stock: {
                    symbol: window.searchedSymbol,
                    name: stockInfo.longName || stockInfo.shortName,
                    sector: stockInfo.sector,
                    industry: stockInfo.industry,
                    price: {
                        current: stockInfo.currentPrice,
                        previousClose: stockInfo.previousClose,
                        open: stockInfo.open,
                        dayLow: stockInfo.dayLow,
                        dayHigh: stockInfo.dayHigh,
                        fiftyTwoWeekRange: stockInfo.fiftyTwoWeekRange
                    },
                    fundamentals: {
                        marketCap: stockInfo.marketCap,
                        beta: stockInfo.beta,
                        pe: stockInfo.trailingPE,
                        eps: stockInfo.trailingEps,
                        dividendYield: stockInfo.dividendYield,
                        ebitda: stockInfo.ebitda,
                        bookValue: stockInfo.bookValue
                    },
                    company: {
                        description: stockInfo.longBusinessSummary,
                        employees: stockInfo.fullTimeEmployees,
                        website: stockInfo.website
                    }
                }
            };

            // Call the AI analysis function with stock analysis type
            getAIFeedback(payload, { analysisType: 'stock' });
        }

        document.addEventListener("DOMContentLoaded", async function () {

            if (window.searchedSymbol) {
                const searchInput = document.getElementById("stock-input");
                searchInput.value = window.searchedSymbol;
                window.handleSearchInputChange(window.searchedSymbol);
            }

            await fetchStockData(window.searchedSymbol);

            if (window.searchedSymbol && !window.stockData) {
                window.removeFromRecentSearches(window.searchedSymbol)
                // Create the container for the error message
                const errorContainer = document.createElement("div");
                errorContainer.classList.add("text-center", "bg-white", "mt-8", "p-8", "rounded-lg",);

                // Create the heading
                const errorHeading = document.createElement("h1");
                errorHeading.classList.add("text-2xl", "font-semibold", "text-gray-800", "mb-4");
                errorHeading.textContent = "Stock Not Found";

                // Create the paragraph
                const errorMessage = document.createElement("p");
                errorMessage.classList.add("text-lg", "text-gray-500", "mb-6", "max-w-xl", "mx-auto");
                errorMessage.textContent = "The stock symbol you entered is invalid or not recognized. Please check the symbol and try again.";

                // Create the "Go Back" button
                const goBackButton = document.createElement("button");
                goBackButton.classList.add("border-blue-500", "text-blue-500", "border", "hover:text-white", "px-8", "py-2", "rounded-lg", "hover:bg-blue-600", "focus:outline-none", "focus:ring-2", "focus:ring-blue-500", "focus:ring-opacity-50");
                goBackButton.textContent = "Go Back";

                // Add the event listener for the "click" event
                goBackButton.addEventListener("click", function () {
                    window.location.href = "/stocks"; // Redirect to "/stocks"
                });


                // Append all elements to the error container
                errorContainer.appendChild(errorHeading);
                errorContainer.appendChild(errorMessage);
                errorContainer.appendChild(goBackButton);

                // Find the parent element of the stock input and append the error container to it
                const inputContainer = document.getElementById("error-state");
                inputContainer.appendChild(errorContainer);
            }
        });

        // Initialize carousels
        initializeCardsCarousel('interested-stocks-cards', 'interested-left-arrow', 'interested-right-arrow');
        initializeCardsCarousel('preset-screener-cards', 'screener-left-arrow', 'screener-right-arrow');
    </script>
</body>

</html>