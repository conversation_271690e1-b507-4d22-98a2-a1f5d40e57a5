<!DOCTYPE html>
<html lang="en">

<head>
    <title>
        {{ seo_title | default("Stock Screener with AI Insights in India | AI Bull Stock Market") }}
    </title>
    <meta name="description"
        content="{{ seo_description | default('Screen stocks in India based on sector, market cap, debt-to-equity, beta, and more to find the best stock market investment opportunities with real-time stock prices, share prices and advanced filters.') }}" />
    <meta name="keywords"
        content="stock screener, stock market, stock prices, share price, in India, investment, financial ratios, market cap, AI Bull, stock screening" />
    <link rel="canonical" href="{{ canonical_url | default('https://theaibull.com/stocks') }}" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="{{ canonical_url | default('https://theaibull.com/stocks') }}" />
    <meta property="og:title"
        content="{{ seo_title | default('Stock Screener - Discover Top Stock Market Opportunities in India with Advanced Screening') }}" />
    <meta property="og:description"
        content="{{ seo_description | default('Screen stocks in India by sector, market cap, debt-to-equity, beta, and more to identify top stock market opportunities with real-time stock prices.') }}" />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title"
        content="{{ seo_title | default('Stock Screener - Discover Top Stock Market Opportunities in India with Advanced Screening') }}" />
    <meta name="twitter:description"
        content="{{ seo_description | default('Find top stock market opportunities in India by screening stocks based on sector, market cap, share price, and financial ratios with precision.') }}" />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.js"></script>
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">
    {% include 'blocks/common.html' %}
    {% include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width relative pb-[62px] xl:pb-0">
        <div class="p-3 mx-auto">

            <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2  rounded-lg mb-3">
                <div class="flex justify-between items-center">
                    <!-- Title and Tabs Row -->
                    <div class="md:flex justify-between items-center">
                        <div class="flex items-center">
                            <h1 class="md:text-xl text-lg font-semibold tracking-tight">Stock Screener in India</h1>
                            <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">Customize Stock Filters
                                for Precise Stock Market Investment Decisions</h2>
                            <!-- Button to trigger modal open -->
                            <button id="openModalBtn" onclick="openModal()" class="ml-2">

                                <div class="flex group text-sm">
                                    <div
                                        class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                        <div
                                            class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-circle-help">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                    <path d="M12 17h.01"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div
                                            class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                            <div
                                                class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                                How to Use This Stock Screener</div>
                                        </div>
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Button to open the filter sidebar on mobile -->
                    <button
                        class="open-btn lg:hidden block border bg-blue-50 hover:bg-blue-500 hover:text-white px-2 py-1 rounded-md flex gap-1 text-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-filter">
                            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3" />
                        </svg>
                        Filters</button>
                </div>
            </div>

            <div class="lg:flex gap-3">
                <!-- Left Panel: Filter Sidebar -->
                <aside
                    class="bg-white filter-slider  rounded-lg lg:w-72 flex-shrink-0  pb-[65px] lg:pb-4 z-[38] fixed inset-0 hidden lg:h-[calc(100vh_-_117px)]   lg:block lg:sticky lg:top-[60px]">
                    <div class="flex items-center justify-between p-4 pb-0">
                        <h2 class="text-lg font-semibold">Stock Market Filters</h2>
                    </div>
                    <form id="filter-form" class="space-y-4 h-[calc(100vh_-_180px)] overflow-y-auto custom-scroll p-4">
                        <!-- Sector Filter -->
                        <div>
                            <label for="sector" class="block font-medium text-sm mb-2">Sector</label>
                            <div
                                class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-neutral-400 focus:outline-0 px-2 py-2.5 rounded-md shadow-sm">
                                <select id="sector" name="sector"
                                    class="bg-transparent block cursor-pointer dark:bg-neutral-700 dark:text-neutral-100 focus:outline-0 text-sm w-full">
                                    <option value="">All</option>
                                    {% for sector in filters.sectors %}
                                    <option value="{{ sector }}">{{ sector }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <!-- Industry Filter -->
                        <div>
                            <label for="industry" class="block font-medium text-sm mb-2">Industry</label>
                            <div
                                class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-neutral-400 focus:outline-0 px-2 py-2.5 rounded-md shadow-sm">
                                <select id="industry" name="industry"
                                    class="bg-transparent block cursor-pointer dark:bg-neutral-700 dark:text-neutral-100 focus:outline-0 text-sm w-full">
                                    <option value="">All</option>
                                    <!-- Options will be populated dynamically based on sector -->
                                </select>
                            </div>
                        </div>
                        <!-- Market Cap Filters -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Market Cap
                                    (Cr)</label>
                                <span id="market-cap-range-indicator" class="text-xs text-gray-500 dark:text-gray-600">0
                                    Cr - 10,00,000
                                    Cr</span>
                            </div>
                            <div id="market-cap-range-slider" class="h-2 px-4"></div>
                            <div class="mt-3 flex justify-between text-xs text-gray-800 dark:text-gray-400">
                                <span>0 Cr</span>
                                <span>10,00,000 Cr</span>
                            </div>
                        </div>
                        <!-- Close Price Filters -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Close Price
                                    (₹)</label>
                                <span id="close-price-range-indicator"
                                    class="text-xs text-gray-500 dark:text-gray-600">₹0 -
                                    ₹10,000</span>
                            </div>
                            <div id="close-price-range-slider" class="h-2 px-4"></div>
                            <div class="mt-3 flex justify-between text-xs text-gray-800 dark:text-gray-400">
                                <span>₹0</span>
                                <span>₹10,000</span>
                            </div>
                        </div>
                        <!-- Debt/Equity -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Debt/Equity
                                    (%)</label>
                                <span id="debt-equity-range-indicator"
                                    class="text-xs text-gray-500 dark:text-gray-600">0% - 500%</span>
                            </div>
                            <div id="debt-equity-range-slider" class="h-2 px-4"></div>
                            <div class="mt-3 flex justify-between text-xs text-gray-800 dark:text-gray-400">
                                <span>0%</span>
                                <span>500%</span>
                            </div>
                        </div>
                        <!-- Beta-->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Beta</label>
                                <span id="beta-range-indicator" class="text-xs text-gray-500 dark:text-gray-600">0 -
                                    2</span>
                            </div>
                            <div id="beta-range-slider" class="h-2 px-4"></div>
                            <div class="mt-3 flex justify-between text-xs text-gray-800 dark:text-gray-400">
                                <span>0</span>
                                <span>2</span>
                            </div>
                        </div>
                        <!-- Forward PE -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Forward PE</label>
                                <span id="forward-pe-range-indicator" class="text-xs text-gray-500 dark:text-gray-600">0
                                    - 100</span>
                            </div>
                            <div id="forward-pe-range-slider" class="h-2 px-4"></div>
                            <div class="mt-3 flex justify-between text-xs text-gray-800 dark:text-gray-400">
                                <span>0</span>
                                <span>100</span>
                            </div>
                        </div>
                        <!-- Dividend Yield -->
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Dividend Yield
                                    (%)</label>
                                <span id="dividend-yield-range-indicator"
                                    class="text-xs text-gray-500 dark:text-gray-600">0% -
                                    10%</span>
                            </div>
                            <div id="dividend-yield-range-slider" class="h-2 px-4"></div>
                            <div class="mt-3 flex justify-between text-xs text-gray-800 dark:text-gray-400">
                                <span>0%</span>
                                <span>10%</span>
                            </div>
                        </div>
                    </form>
                    <div class="p-4">
                        <button
                            class="close-btn lg:hidden block bg-blue-500 hover:bg-blue-600 p-2 text-center text-white w-full rounded-md mt-3 font-medium text-sm">
                            Apply Filters
                        </button>
                    </div>
                </aside>

                <!-- Right Panel: Content -->
                <section class="flex-1 w-[calc(100%_-_300px)]">
                    <!-- Prebuilt Screeners Section -->
                    <div class="bg-white p-4 rounded-lg mb-3"
                        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #ffffff, #f8f8f8, #ffffff, #f5f5f500, #ffffff)">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-lg font-semibold">Prebuilt Stock Market Screeners</h3>
                            <div>
                                <!-- Left Arrow -->
                                <button id="left-arrow"
                                    class="border z-30 h-10 w-10 transform bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-move-left-icon lucide-move-left">
                                        <path d="M6 8L2 12L6 16"></path>
                                        <path d="M2 12H22"></path>
                                    </svg>
                                </button>
                                <!-- Right Arrow -->
                                <button id="right-arrow"
                                    class="transform h-10 w-10 border bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-move-right-icon lucide-move-right">
                                        <path d="M18 8L22 12L18 16"></path>
                                        <path d="M2 12H22"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div id="preset-cards" class="flex md:flex-row flex-col gap-4 overflow-x-auto">
                            <a href="/stocks/long-term-gems"
                                class="flex-shrink-0 bg-blue-100/80 p-4 rounded-lg shadow cursor-pointer hover:bg-blue-200 transition-colors w-72"
                                data-url="long-term-gems"
                                data-preset='{"max_close_price": "1300", "min_market_cap": "1000"}'>
                                <h3 class="font-semibold text-lg mb-1">Long Term Gems</h3>
                                <p class="text-sm text-gray-700">5Y revenue growth near 52W lows</p>
                            </a>
                            <a href="/stocks/momentum-monsters"
                                class="flex-shrink-0 bg-green-100/80 p-4 rounded-lg shadow cursor-pointer hover:bg-green-200 transition-colors w-72"
                                data-url="momentum-monsters"
                                data-preset='{"min_forward_pe": "15", "max_forward_pe": "25", "min_beta": "0.5", "max_beta": "1.0"}'>
                                <h3 class="font-semibold text-lg mb-1">Momentum Monsters</h3>
                                <p class="text-sm text-gray-700">High momentum with strong forward PE range</p>
                            </a>
                            <a href="/stocks/dividend-darlings"
                                class="flex-shrink-0 bg-purple-100/80 p-4 rounded-lg shadow cursor-pointer hover:bg-purple-200 transition-colors w-72"
                                data-url="dividend-darlings"
                                data-preset='{"min_dividend_yield": "2", "max_debt_to_equity": "100"}'>
                                <h3 class="font-semibold text-lg mb-1">Dividend Darlings</h3>
                                <p class="text-sm text-gray-700">High dividend yield with low debt-to-equity ratios</p>
                            </a>
                            <a href="/stocks/blue-chip-bargains"
                                class="flex-shrink-0 bg-red-100/80 p-4 rounded-lg shadow cursor-pointer hover:bg-red-200 transition-colors w-72"
                                data-url="blue-chip-bargains"
                                data-preset='{"min_market_cap": "50000", "max_beta": "1.2"}'>
                                <h3 class="font-semibold text-lg mb-1">Blue Chip Bargains</h3>
                                <p class="text-sm text-gray-700">Large-cap stocks with stable beta for lower volatility
                                </p>
                            </a>
                            <a href="/stocks/value-picks"
                                class="flex-shrink-0 bg-yellow-100/80 p-4 rounded-lg shadow cursor-pointer hover:bg-yellow-200 transition-colors w-72"
                                data-url="value-picks"
                                data-preset='{"min_forward_pe": "10", "max_forward_pe": "20", "min_market_cap": "1000"}'>
                                <h3 class="font-semibold text-lg mb-1">Value Picks</h3>
                                <p class="text-sm text-gray-700">Undervalued stocks with reasonable forward PE and
                                    market cap</p>
                            </a>
                            <a href="/stocks/high-volatility-ventures"
                                class="flex-shrink-0 bg-teal-100/80 p-4 rounded-lg shadow cursor-pointer hover:bg-teal-200 transition-colors w-72"
                                data-url="high-volatility-ventures"
                                data-preset='{"min_beta": "1.2", "min_close_price": "500"}'>
                                <h3 class="font-semibold text-lg mb-1">High Volatility Ventures</h3>
                                <p class="text-sm text-gray-700">High-beta stocks with significant share price movement
                                    potential</p>
                            </a>
                            <a href="/stocks/growth-giants"
                                class="flex-shrink-0 bg-orange-100/80 p-4 rounded-lg shadow cursor-pointer hover:bg-orange-200 transition-colors w-72"
                                data-url="growth-giants"
                                data-preset='{"min_market_cap": "10000", "max_forward_pe": "30"}'>
                                <h3 class="font-semibold text-lg mb-1">Growth Giants</h3>
                                <p class="text-sm text-gray-700">Mid to large-cap stocks with strong growth potential in
                                    the stock market</p>
                            </a>
                            <a href="/stocks/income-innovators"
                                class="flex-shrink-0 bg-indigo-100/80 p-4 rounded-lg shadow cursor-pointer hover:bg-indigo-200 transition-colors w-72"
                                data-url="income-innovators"
                                data-preset='{"max_close_price": "1000", "min_dividend_yield": "1"}'>
                                <h3 class="font-semibold text-lg mb-1">Income Innovators</h3>
                                <p class="text-sm text-gray-700">Affordable stocks with consistent dividend payouts</p>
                            </a>
                            <a href="/stocks/small-cap-stars"
                                class="flex-shrink-0 bg-pink-100/80 p-4 rounded-lg shadow cursor-pointer hover:bg-pink-200 transition-colors w-72"
                                data-url="small-cap-stars"
                                data-preset='{"min_market_cap": "500", "max_debt_to_equity": "50"}'>
                                <h3 class="font-semibold text-lg mb-1">Small Cap Stars</h3>
                                <p class="text-sm text-gray-700">Small-cap companies with low debt levels in the stock
                                    market</p>
                            </a>
                            <a href="/stocks/stable-value-bets"
                                class="flex-shrink-0 bg-cyan-100/80 p-4 rounded-lg shadow cursor-pointer hover:bg-cyan-200 transition-colors w-72"
                                data-url="stable-value-bets" data-preset='{"min_forward_pe": "5", "max_beta": "0.8"}'>
                                <h3 class="font-semibold text-lg mb-1">Stable Value Bets</h3>
                                <p class="text-sm text-gray-700">Low PE and low beta for conservative investors</p>
                            </a>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div class="bg-white p-4 rounded-lg">
                        <div class="px-4 py-2 bg-blue-50 dark:bg-blue-900 flex justify-between items-center">
                            <div class="flex items-center gap-2">
                                <h2 class="font-semibold text-gray-800 dark:text-gray-100">Stock Market Results</h2>
                                <span id="result-count" class="text-gray-600 text-xs italic">
                                    {% if total %}
                                    (Total: {{ total }})
                                    {% else %}
                                    (Total: 0)
                                    {% endif %}
                                </span>
                            </div>
                            <div class="flex gap-x-2">
                                <div id="pagination-container"
                                    class="pagination flex flex-col sm:flex-row justify-between items-center hidden">
                                    <div id="pagination-controls" class="flex gap-1 flex-wrap justify-center"></div>
                                </div>
                                <!-- Column Picker Dropdown -->
                                <div class="relative inline-block text-left">
                                    <button id="columnPickerBtn"
                                        class="border border-gray-300 focus:outline-none font-medium hover:bg-blue-600 hover:text-white inline-flex justify-center p-1 rounded-md shadow-sm text-sm">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-columns-2">
                                            <rect width="18" height="18" x="3" y="3" rx="2" />
                                            <path d="M12 3v18" />
                                        </svg>
                                    </button>
                                    <div id="columnPickerDropdown"
                                        class="origin-top-right absolute right-0 mt-2 w-64 rounded-md shadow-lg bg-white dark:bg-neutral-800 ring-1 ring-black ring-opacity-5 hidden z-50">
                                        <div class="py-2">
                                            <label class="block px-4 py-1">
                                                <input type="checkbox" class="col-picker" data-col="symbol" checked
                                                    disabled>
                                                Symbol
                                            </label>
                                            <label class="block px-4 py-1">
                                                <input type="checkbox" class="col-picker" data-col="name" checked> Name
                                            </label>
                                            <label class="block px-4 py-1">
                                                <input type="checkbox" class="col-picker" data-col="sector" checked>
                                                Sector
                                            </label>
                                            <label class="block px-4 py-1">
                                                <input type="checkbox" class="col-picker" data-col="industry" checked>
                                                Industry
                                            </label>
                                            <label class="block px-4 py-1">
                                                <input type="checkbox" class="col-picker" data-col="marketCap" checked>
                                                Market
                                                Cap
                                            </label>
                                            <label class="block px-4 py-1">
                                                <input type="checkbox" class="col-picker" data-col="regularMarketPrice"
                                                    checked>
                                                Close Price
                                            </label>
                                            <label class="block px-4 py-1">
                                                <input type="checkbox" class="col-picker" data-col="debtToEquity"
                                                    checked>
                                                Debt/Equity
                                            </label>
                                            <label class="block px-4 py-1">
                                                <input type="checkbox" class="col-picker" data-col="beta" checked> Beta
                                            </label>
                                            <label class="block px-4 py-1">
                                                <input type="checkbox" class="col-picker" data-col="forwardPE" checked>
                                                Forward
                                                PE
                                            </label>
                                            <label class="block px-4 py-1">
                                                <input type="checkbox" class="col-picker" data-col="dividendYield"
                                                    checked>
                                                Dividend Yield
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Applied Filters Summary -->
                        <div id="applied-filters"
                            class="p-2 hidden max-h-48 overflow-hidden transition-all duration-300 ease-in-out flex flex-wrap gap-2 items-center">
                            <p class="text-xs font-medium text-gray-500 dark:text-gray-400">Applied Stock Filters:</p>
                            <div id="filter-badges" class="flex flex-wrap gap-2"></div>
                        </div>

                        <!-- Loading Spinner -->
                        <div id="spinner" class="flex justify-center items-center py-4 hidden">
                            <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500">
                            </div>
                        </div>

                        <!-- Results Table with Fixed Height -->
                        <div id="results-container"
                            class="table-scroll overflow-x-auto max-h-[90vh] overflow-y-auto relative">
                            {% if initial_stocks %}
                            <table class="min-w-full w-full table-auto text-xs text-left">
                                <thead class="sticky top-0 z-10">
                                    <tr>
                                        {% for header in selected_columns %}
                                        <th class="border p-2 sortable bg-gray-50" data-col="{{ header }}">{{ header |
                                            replace("_", " ") |
                                            title }}<span class="sort-indicator"></span></th>
                                        {% endfor %}
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stock in initial_stocks %}
                                    <tr>
                                        {% for col in selected_columns %}
                                        <td class="border p-2">
                                            {% if col == "symbol" %}
                                            {% set sym = ( stock.symbol or "") | replace(".NS", "")
                                            %}
                                            {% if sym %}
                                            <a href="/stocks/{{ sym | lower }}" class="text-blue-500 hover:underline">{{
                                                sym | upper }}</a>
                                            {% else %}
                                            -
                                            {% endif %}
                                            {% elif col == "name" %}
                                            {{ stock.name or "-" }}
                                            {% elif col == "marketCap" and (stock[col]) %}
                                            {% set value = (stock[col]) | float %}
                                            {% if value >= 10000000 %}
                                            {{ (value / 10000000) | round(2) }} Cr
                                            {% elif value >= 100000 %}
                                            {{ (value / 100000) | round(2) }} L
                                            {% else %}
                                            {{ value | round(2) }}
                                            {% endif %}
                                            {% elif col in ["regularMarketPrice", "debtToEquity", "beta", "forwardPE",
                                            "dividendYield"] and
                                            (stock[col]) %}
                                            {{ (stock[col]) | float | round(2) }}
                                            {% else %}
                                            {{ stock[col] or "-" }}
                                            {% endif %}
                                        </td>
                                        {% endfor %}
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                            {% else %}
                            <p class="p-4 text-gray-600">No stocks match the filter criteria.</p>
                            {% endif %}
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
    {% include 'blocks/footer.html' %}
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        // Slider configurations
        const SLIDER_CONFIGS = {
            "market-cap": {
                start: [0, 1000000],
                range: { min: 0, max: 1000000 },
                step: 1000,
                format: { to: value => Math.round(value), from: value => Number(value) }
            },
            "close-price": {
                start: [0, 10000],
                range: { min: 0, max: 10000 },
                step: 10,
                format: { to: value => Math.round(value), from: value => Number(value) }
            },
            "debt-equity": {
                start: [0, 500],
                range: { min: 0, max: 500 },
                step: 1,
                format: { to: value => Math.round(value), from: value => Number(value) }
            },
            "beta": {
                start: [0, 2],
                range: { min: 0, max: 2 },
                step: 0.1,
                format: { to: value => Number(value).toFixed(1), from: value => Number(value) }
            },
            "forward-pe": {
                start: [0, 100],
                range: { min: 0, max: 100 },
                step: 1,
                format: { to: value => Math.round(value), from: value => Number(value) }
            },
            "dividend-yield": {
                start: [0, 10],
                range: { min: 0, max: 10 },
                step: 0.01,
                format: { to: value => Number(value).toFixed(2), from: value => Number(value) }
            }
        };

        // Current page state
        let currentPage = {{ current_page }} || 1;
        let totalCount = {{ total }} || 0;
        const defaultLimit = 100;
        let sectorIndustryMapping = {};
        let presetFilters = {};
    </script>

    <script>
        // Fetch filter data from endpoint
        async function fetchFilterData(pathParam = null) {
            try {
                const url = pathParam ? `/screener/filter-data?path_param=${encodeURIComponent(pathParam)}` : '/screener/filter-data';
                const response = await axios.get(url);
                sectorIndustryMapping = response.data.sectorIndustryMapping;
                presetFilters = response.data.preset_filters;
                const sectors = response.data.sectors;

                // Populate sector dropdown
                const sectorSelect = document.getElementById('sector');
                sectorSelect.innerHTML = '<option value="">All</option>';
                sectors.forEach(sector => {
                    const option = document.createElement('option');
                    option.value = sector;
                    option.textContent = sector;
                    sectorSelect.appendChild(option);
                });

                // Extract page number from URL
                const pathSegments = window.location.pathname.split('/').filter(segment => segment);
                const pageFromUrl = parseInt(pathSegments[pathSegments.length - 1]) || 1;
                paginationManager.setPage(pageFromUrl);

                // Apply preset filters if present
                if (Object.keys(presetFilters).length > 0) {
                    applyPresetFilters();
                } else {
                    applyFilters();
                }
            } catch (error) {
                console.error('Error fetching filter data:', error);
            }
        }

        // Apply preset filters to form
        function applyPresetFilters() {
            resetAllFilters();
            const filters = presetFilters;
            if (filters.sector) {
                document.getElementById('sector').value = filters.sector;
                updateIndustryOptions(filters.sector);
            }
            if (filters.industry) {
                const sector = Object.keys(sectorIndustryMapping).find(s => sectorIndustryMapping[s]?.includes(filters.industry));
                if (sector) {
                    document.getElementById('sector').value = sector;
                    updateIndustryOptions(sector);
                }
                document.getElementById('industry').value = filters.industry;
            }
            const sliderMap = {
                min_market_cap: ['market-cap-range-slider', 0],
                max_market_cap: ['market-cap-range-slider', 1],
                min_close_price: ['close-price-range-slider', 0],
                max_close_price: ['close-price-range-slider', 1],
                min_debt_to_equity: ['debt-equity-range-slider', 0],
                max_debt_to_equity: ['debt-equity-range-slider', 1],
                min_beta: ['beta-range-slider', 0],
                max_beta: ['beta-range-slider', 1],
                min_forward_pe: ['forward-pe-range-slider', 0],
                max_forward_pe: ['forward-pe-range-slider', 1],
                min_dividend_yield: ['dividend-yield-range-slider', 0],
                max_dividend_yield: ['dividend-yield-range-slider', 1]
            };
            for (const [key, [sliderId, index]] of Object.entries(sliderMap)) {
                if (filters[key] !== null && filters[key] !== undefined) {
                    const slider = document.getElementById(sliderId);
                    if (slider?.noUiSlider) {
                        const values = slider.noUiSlider.get().map(Number);
                        let value = Number(filters[key]);
                        if (sliderId === 'market-cap-range-slider') {
                            value /= 10000000; // Convert to Cr
                        }
                        values[index] = value;
                        slider.noUiSlider.set(values);
                    }
                }
            }
            updateAppliedFilters();
            applyFilters();
        }

        // Update card selection based on URL
        function updateCardSelection(containerId = 'preset-cards', basePath = '/stocks/') {
            const presetCardsContainer = document.getElementById(containerId);
            if (!presetCardsContainer) return;

            const cards = presetCardsContainer.querySelectorAll('a[data-url]');
            const pathSegments = window.location.pathname.split(basePath)[1]?.split('/') || [''];
            const activePresetId = pathSegments[0] || '';

            // Skip card selection for sector or industry routes
            if (activePresetId.endsWith('-sector') || activePresetId.endsWith('-industry')) {
                cards.forEach(card => {
                    const classesToRemove = ['selected', 'border-2', 'shadow-lg', 'shadow-gray-400', 'border-blue-700', 'border-green-700', 'border-purple-700', 'border-red-700', 'border-yellow-700', 'border-teal-700', 'border-orange-700', 'border-indigo-700', 'border-pink-700', 'border-cyan-700'];
                    card.classList.remove(...classesToRemove);
                });
                return;
            }

            if (!activePresetId || activePresetId === 'screener') return;

            const ACTIVE_CLASSES = ['selected', 'border-2', 'shadow-lg', 'shadow-gray-400'];
            const DEFAULT_COLOR = 'blue';

            cards.forEach(card => {
                const cardUrl = card.getAttribute('data-url') || '';
                const isActive = activePresetId === cardUrl;

                // Extract border color efficiently
                const bgClass = [...card.classList].find(cls => cls.match(/^(bg|from)-/)) || `bg-${DEFAULT_COLOR}-50`;
                const colorKey = bgClass.match(/-(\w+)-/)?.[1] || DEFAULT_COLOR;
                const borderClass = `border-${colorKey}-700`;

                const classesToToggle = [...ACTIVE_CLASSES, borderClass];
                classesToToggle.forEach(cls => card.classList.toggle(cls, isActive));

                // Scroll to active card (only one should be active)
                if (isActive) {
                    card.scrollIntoView({ behavior: 'smooth', inline: 'center' });
                }
            });
        }

        // Initialize sliders
        function initRangeFilters() {
            const sliders = [
                {
                    id: 'market-cap-range-slider',
                    config: SLIDER_CONFIGS['market-cap'],
                    indicator: document.getElementById('market-cap-range-indicator'),
                    format: ([min, max]) => `${min.toLocaleString('en-IN')} Cr - ${max.toLocaleString('en-IN')} Cr`
                },
                {
                    id: 'close-price-range-slider',
                    config: SLIDER_CONFIGS['close-price'],
                    indicator: document.getElementById('close-price-range-indicator'),
                    format: ([min, max]) => `₹${min} - ₹${max}`
                },
                {
                    id: 'debt-equity-range-slider',
                    config: SLIDER_CONFIGS['debt-equity'],
                    indicator: document.getElementById('debt-equity-range-indicator'),
                    format: ([min, max]) => `${min}% - ${max}%`
                },
                {
                    id: 'beta-range-slider',
                    config: SLIDER_CONFIGS['beta'],
                    indicator: document.getElementById('beta-range-indicator'),
                    format: ([min, max]) => `${min} - ${max}`
                },
                {
                    id: 'forward-pe-range-slider',
                    config: SLIDER_CONFIGS['forward-pe'],
                    indicator: document.getElementById('forward-pe-range-indicator'),
                    format: ([min, max]) => `${min} - ${max}`
                },
                {
                    id: 'dividend-yield-range-slider',
                    config: SLIDER_CONFIGS['dividend-yield'],
                    indicator: document.getElementById('dividend-yield-range-indicator'),
                    format: ([min, max]) => `${min}% - ${max}%`
                }
            ];

            sliders.forEach(({ id, config, indicator, format }) => {
                const slider = document.getElementById(id);
                if (!slider) {
                    console.error(`Slider element with ID ${id} not found`);
                    return;
                }
                try {
                    noUiSlider.create(slider, {
                        ...config,
                        connect: true,
                        behaviour: 'drag'
                    });
                    slider.noUiSlider.on('update', (values) => {
                        indicator.textContent = format(values.map(Number));
                    });
                    slider.noUiSlider.on('end', () => {
                        paginationManager.setPage(1);
                        updateAppliedFilters();
                        applyFilters();
                    });
                } catch (error) {
                    console.error(`Error initializing slider ${id}:`, error);
                }
            });
        }

        // Update applied filters UI
        function updateAppliedFilters() {
            const activeFilters = [];
            const addFilter = (type, text, clearAction) => activeFilters.push({ type, text, clearAction });

            // Sector and Industry
            const sector = document.getElementById('sector').value;
            const industry = document.getElementById('industry').value;
            if (sector) {
                addFilter('sector', `Sector: ${sector}`, () => {
                    document.getElementById('sector').value = '';
                    document.getElementById('industry').innerHTML = '<option value="">All</option>';
                    paginationManager.setPage(1);
                    updateAppliedFilters();
                    applyFilters();
                });
            }
            if (industry) {
                addFilter('industry', `Industry: ${industry}`, () => {
                    document.getElementById('industry').value = '';
                    paginationManager.setPage(1);
                    updateAppliedFilters();
                    applyFilters();
                });
            }

            // Slider filters
            const sliders = [
                {
                    id: 'market-cap-range-slider',
                    name: 'marketCap',
                    label: 'Market Cap',
                    format: ([min, max]) => `${min.toLocaleString('en-IN')} Cr - ${max.toLocaleString('en-IN')} Cr`,
                    default: [0, 1000000]
                },
                {
                    id: 'close-price-range-slider',
                    name: 'closePrice',
                    label: 'Close Price',
                    format: ([min, max]) => `₹${min} - ₹${max}`,
                    default: [0, 10000]
                },
                {
                    id: 'debt-equity-range-slider',
                    name: 'debtToEquity',
                    label: 'Debt/Equity',
                    format: ([min, max]) => `${min}% - ${max}%`,
                    default: [0, 500]
                },
                {
                    id: 'beta-range-slider',
                    name: 'beta',
                    label: 'Beta',
                    format: ([min, max]) => `${min} - ${max}`,
                    default: [0, 2]
                },
                {
                    id: 'forward-pe-range-slider',
                    name: 'forwardPe',
                    label: 'Forward PE',
                    format: ([min, max]) => `${min} - ${max}`,
                    default: [0, 100]
                },
                {
                    id: 'dividend-yield-range-slider',
                    name: 'dividendYield',
                    label: 'Dividend Yield',
                    format: ([min, max]) => `${min}% - ${max}%`,
                    default: [0, 10]
                }
            ];

            sliders.forEach(({ id, name, label, format, default: [defaultMin, defaultMax] }) => {
                const slider = document.getElementById(id);
                if (slider.noUiSlider) {
                    const [min, max] = slider.noUiSlider.get().map(Number);
                    if (min > defaultMin || max < defaultMax) {
                        addFilter(name, `${label}: ${format([min, max])}`, () => {
                            slider.noUiSlider.set([defaultMin, defaultMax]);
                            paginationManager.setPage(1);
                            updateAppliedFilters();
                            applyFilters();
                        });
                    }
                }
            });

            const fragment = document.createDocumentFragment();
            activeFilters.forEach(filter => {
                const badge = document.createElement('div');
                badge.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs bg-gray-100 text-gray-800 dark:bg-blue-900 dark:text-blue-200';
                badge.innerHTML = `
                        <span>${filter.text}</span>
                        <button type="button" class="ml-1 text-red-400 hover:text-red-600">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    `;
                badge.querySelector('button').addEventListener('click', filter.clearAction);
                fragment.appendChild(badge);
            });

            const appliedFilters = document.getElementById('applied-filters');
            const filterBadges = document.getElementById('filter-badges');
            filterBadges.innerHTML = '';
            filterBadges.appendChild(fragment);
            appliedFilters.classList.toggle('hidden', activeFilters.length === 0);
        }

        // Reset all filters
        function resetAllFilters() {
            document.getElementById('filter-form').reset();
            document.getElementById('industry').innerHTML = '<option value="">All</option>';
            const sliders = [
                { id: 'market-cap-range-slider', range: SLIDER_CONFIGS["market-cap"].start },
                { id: 'close-price-range-slider', range: SLIDER_CONFIGS["close-price"].start },
                { id: 'debt-equity-range-slider', range: SLIDER_CONFIGS["debt-equity"].start },
                { id: 'beta-range-slider', range: SLIDER_CONFIGS["beta"].start },
                { id: 'forward-pe-range-slider', range: SLIDER_CONFIGS["forward-pe"].start },
                { id: 'dividend-yield-range-slider', range: SLIDER_CONFIGS["dividend-yield"].start }
            ];
            sliders.forEach(({ id, range }) => {
                const slider = document.getElementById(id);
                if (slider.noUiSlider) slider.noUiSlider.set(range);
            });
            updateAppliedFilters();
        }

        // Update industry dropdown based on selected sector
        function updateIndustryOptions(selectedSector) {
            const industryDropdown = document.getElementById('industry');
            let optionsHTML = '<option value="">All</option>';
            if (selectedSector && sectorIndustryMapping[selectedSector]) {
                sectorIndustryMapping[selectedSector].forEach(ind => {
                    optionsHTML += `<option value="${ind}">${ind}</option>`;
                });
            } else {
                // If no sector is selected, show all industries.
                const allIndustries = new Set();
                Object.values(sectorIndustryMapping).forEach(list => list.forEach(ind => allIndustries.add(ind)));
                allIndustries.forEach(ind => {
                    optionsHTML += `<option value="${ind}">${ind}</option>`;
                });
            }
            industryDropdown.innerHTML = optionsHTML;
        }

        const sectorDropdown = document.getElementById('sector');
        sectorDropdown.addEventListener('change', function () {
            updateIndustryOptions(this.value);
            paginationManager.setPage(1);
            updateAppliedFilters();
        });

        let currentSort = { column: null, order: 'asc' };

        const filterForm = document.getElementById('filter-form');
        let debounceTimer;
        filterForm.addEventListener('input', () => {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                paginationManager.setPage(1);
                updateAppliedFilters();
                applyFilters();
            }, 500);
        });

        // Re-render table when column picker changes.
        document.querySelectorAll('.col-picker').forEach(cb => {
            cb.addEventListener('change', () => {
                if (window._lastStocks) {
                    renderResults(window._lastStocks.stocks);
                    updatePagination();
                }
            });
        });

        // Preset Cards: Reset form, fill with preset values, and apply filters.
        document.querySelectorAll('#preset-cards > a').forEach(card => {
            card.addEventListener('click', async (e) => {
                e.preventDefault();
                const presetId = card.getAttribute('data-url');
                paginationManager.baseUrl = `/stocks/${presetId}`;
                paginationManager.setPage(1);
                await fetchFilterData(presetId);
                updateCardSelection('preset-cards', '/stocks/');
            });
        });

        // Column Picker Dropdown toggle.
        const columnPickerBtn = document.getElementById('columnPickerBtn');
        const columnPickerDropdown = document.getElementById('columnPickerDropdown');
        columnPickerBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            columnPickerDropdown.classList.toggle('hidden');
        });
        document.addEventListener('click', () => {
            columnPickerDropdown.classList.add('hidden');
        });

        function showSpinner() {
            document.getElementById('spinner').classList.remove('hidden');
            document.getElementById('results-container').classList.add('hidden');
            document.querySelector('.pagination')?.classList.add('hidden');
        }

        function hideSpinner() {
            document.getElementById('spinner').classList.add('hidden');
            document.getElementById('results-container').classList.remove('hidden');
            document.querySelector('.pagination')?.classList.remove('hidden');
        }

        function applyFilters() {
            const payload = {
                sector: document.getElementById('sector').value || undefined,
                industry: document.getElementById('industry').value || undefined,
                columns: getSelectedHeaders(),
                page: paginationManager.currentPage,
                limit: paginationManager.limit
            };

            // Add slider values
            const sliders = [
                { id: 'market-cap-range-slider', minKey: 'min_market_cap', maxKey: 'max_market_cap', configKey: 'market-cap' },
                { id: 'close-price-range-slider', minKey: 'min_close_price', maxKey: 'max_close_price', configKey: 'close-price' },
                { id: 'debt-equity-range-slider', minKey: 'min_debt_to_equity', maxKey: 'max_debt_to_equity', configKey: 'debt-equity' },
                { id: 'beta-range-slider', minKey: 'min_beta', maxKey: 'max_beta', configKey: 'beta' },
                { id: 'forward-pe-range-slider', minKey: 'min_forward_pe', maxKey: 'max_forward_pe', configKey: 'forward-pe' },
                { id: 'dividend-yield-range-slider', minKey: 'min_dividend_yield', maxKey: 'max_dividend_yield', configKey: 'dividend-yield' }
            ];

            sliders.forEach(({ id, minKey, maxKey, configKey }) => {
                const slider = document.getElementById(id);
                if (slider.noUiSlider) {
                    const [min, max] = slider.noUiSlider.get().map(Number);
                    const config = SLIDER_CONFIGS[configKey];
                    if (id === 'market-cap-range-slider') {
                        // Convert Cr to absolute value (1 Cr = 10,000,000)
                        if (min > config.range.min) payload[minKey] = min * 10000000;
                        if (max < config.range.max) payload[maxKey] = max * 10000000;
                    } else {
                        if (min > config.range.min) payload[minKey] = min;
                        if (max < config.range.max) payload[maxKey] = max;
                    }
                }
            });

            showSpinner();
            axios.post('/screener/search', payload)
                .then(response => {
                    window._lastStocks = response.data;
                    currentSort = { column: null, order: 'asc' };
                    renderResults(response.data.stocks);
                    paginationManager.update(response.data.count);
                    hideSpinner();
                    document.getElementById('result-count').textContent = `(Total: ${response.data.count})`;
                    paginationManager.setPage(currentPage);
                })
                .catch(error => {
                    console.error('Error fetching results:', error);
                    document.getElementById('results-container').innerHTML = '<p class="text-red-500 p-4">Error fetching results.</p>';
                    hideSpinner();
                });
        }

        function renderResults(stocks) {
            const container = document.getElementById('results-container');
            if (!stocks.length) {
                container.innerHTML = '<p class="p-4 text-gray-600">No stocks match the filter criteria.</p>';
                return;
            }
            let html = '<table class="min-w-full w-full table-auto text-xs text-left">';
            const headers = getSelectedHeaders();
            html += '<thead class="sticky top-0 z-10"><tr>';
            headers.forEach(header => {
                html += `<th class="border p-2 sortable bg-gray-50" data-col="${header}">${formatHeader(header)}<span class="sort-indicator"></span></th>`;
            });
            html += '</tr></thead><tbody>';
            stocks.forEach(stock => {
                html += '<tr>';
                headers.forEach(col => {
                    let cellValue = '';
                    if (col === 'symbol') {
                        let sym = (stock.info && stock.info.symbol) ? stock.info.symbol : stock.symbol || '';
                        sym = sym.replace(/\.NS$/, "");
                        cellValue = sym
                            ? `<a href="/stocks/${sym.toLowerCase()}" class="text-blue-500 hover:underline">${sym.toUpperCase()}</a>`
                            : '';
                    } else if (col === 'name') {
                        cellValue = stock.name || stock.info?.longName || stock.info?.shortName || '';
                    } else {
                        cellValue = stock[col] || stock.info?.[col] || '';
                        if (col === 'marketCap' && cellValue) {
                            cellValue = formatMarketCap(Number(cellValue));
                        } else if (['regularMarketPrice', 'debtToEquity', 'beta', 'forwardPE', 'dividendYield'].includes(col) && cellValue) {
                            cellValue = parseFloat(cellValue).toFixed(2);
                        }
                    }
                    html += `<td class="border p-2">${cellValue}</td>`;
                });
                html += '</tr>';
            });
            html += '</tbody></table>';
            container.innerHTML = html;
            addSorting();
        }

        function getSelectedHeaders() {
            let headers = ["symbol"];
            const checkboxes = document.querySelectorAll('.col-picker');
            checkboxes.forEach(cb => {
                if (cb.checked && cb.getAttribute('data-col') !== "symbol") {
                    headers.push(cb.getAttribute('data-col'));
                }
            });
            return headers;
        }

        function formatHeader(header) {
            const mapping = {
                symbol: 'Symbol',
                name: 'Name',
                sector: 'Sector',
                industry: 'Industry',
                marketCap: 'Market Cap',
                regularMarketPrice: 'Close Price',
                debtToEquity: 'Debt/Equity',
                beta: 'Beta',
                forwardPE: 'Forward PE',
                dividendYield: 'Dividend Yield'
            };
            return mapping[header] || header.charAt(0).toUpperCase() + header.slice(1);
        }

        // Format market cap in crores and lakhs
        function formatMarketCap(value) {
            if (!value || isNaN(value)) return '-';
            value = Number(value);
            const crore = 10000000; // 1 crore = 10 million
            const lakh = 100000; // 1 lakh = 100 thousand

            if (value >= crore) {
                return `${(value / crore).toFixed(2)} Cr`;
            } else if (value >= lakh) {
                return `${(value / lakh).toFixed(2)} L`;
            } else {
                return `${value.toFixed(2)}`;
            }
        }

        function addSorting() {
            const table = document.querySelector('#results-container table');
            if (!table) return;
            const headers = table.querySelectorAll('th.sortable');
            headers.forEach(th => {
                th.addEventListener('click', () => {
                    const col = th.getAttribute('data-col');
                    if (currentSort.column === col) {
                        currentSort.order = (currentSort.order === 'asc') ? 'desc' : 'asc';
                    } else {
                        currentSort.column = col;
                        currentSort.order = 'asc';
                    }
                    sortTable(table, col, currentSort.order);
                });
            });
        }

        function sortTable(table, col, order) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const colIndex = Array.from(table.querySelectorAll('th')).findIndex(th => th.getAttribute('data-col') === col);
            rows.sort((a, b) => {
                const aText = a.querySelectorAll('td')[colIndex].textContent.trim();
                const bText = b.querySelectorAll('td')[colIndex].textContent.trim();
                const aNum = parseFloat(aText.replace(/,/g, ''));
                const bNum = parseFloat(bText.replace(/,/g, ''));
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return order === 'asc' ? aNum - bNum : bNum - aNum;
                } else {
                    return order === 'asc' ? aText.localeCompare(bText) : bText.localeCompare(aText);
                }
            });
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));
            updateSortIndicators(table, col, order);
        }

        function updateSortIndicators(table, col, order) {
            table.querySelectorAll('.sort-indicator').forEach(el => el.textContent = '');
            const th = table.querySelector(`th[data-col="${col}"] .sort-indicator`);
            if (th) {
                th.textContent = order === 'asc' ? '▲' : '▼';
            }
        }

        // Hide and Show the filter in Tab/Mobile
        document.addEventListener('DOMContentLoaded', function () {
            window.paginationManager = new PaginationManager('pagination-container', 'pagination-controls');
            const pathSegments = window.location.pathname.split('/').filter(segment => segment);
            const pageFromUrl = parseInt(pathSegments[pathSegments.length - 1]) || 1;
            paginationManager.currentPage = pageFromUrl;
            currentPage = pageFromUrl;
            paginationManager.render(totalCount);
            initRangeFilters();
            initializeCardsCarousel('preset-cards', 'left-arrow', 'right-arrow');
            updateCardSelection('preset-cards', '/stocks/');
            document.getElementById('filter-form').reset();
            const pathParam = window.location.pathname.split('/stocks/')[1]?.split('/')[0] || null;
            if (pathParam && pathParam != currentPage) {
                paginationManager.baseUrl = `/stocks/${pathParam}`;
            }
            fetchFilterData(pathParam);
            const filterSlider = document.querySelector('.filter-slider');
            const openButton = document.querySelector('.open-btn');
            const closeButton = document.querySelector('.close-btn');

            // Open the slider
            openButton.addEventListener('click', function () {
                filterSlider.classList.add('block');
                filterSlider.classList.remove('hidden');
            });

            // Close the slider
            closeButton.addEventListener('click', function () {
                filterSlider.classList.remove('block');
                filterSlider.classList.add('hidden');
                applyFilters();
            });
        });

        class PaginationManager {
            constructor(containerId, controlsId, limit = 100) {
                this.container = document.getElementById(containerId);
                this.controlsElement = document.getElementById(controlsId);
                this.currentPage = currentPage || 1;
                this.limit = Math.min(Math.max(limit, 1), 100);
                this.totalCount = totalCount || 0;
                this.baseUrl = '/stocks';
            }

            setPage(page) {
                this.currentPage = Math.max(1, page);
                const newUrl = this.currentPage === 1 ? this.baseUrl : `${this.baseUrl}/${this.currentPage}`;
                history.pushState({}, '', newUrl);
                currentPage = this.currentPage;
            }

            createLink(text, page, classes) {
                const link = document.createElement('a');
                link.href = `${this.baseUrl}/${page}`;
                link.className = `page-link px-3 py-1 ${classes.join(' ')} text-sm`;
                link.textContent = text;
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.setPage(page);
                    applyFilters();
                });
                return link;
            }

            render(totalCount) {
                this.totalCount = totalCount;
                const totalPages = Math.ceil(totalCount / this.limit);
                this.container.classList.toggle('hidden', totalPages <= 1);
                if (totalPages <= 1) {
                    this.controlsElement.innerHTML = '';
                    return;
                }

                let html = '';

                // Previous button
                const prevDisabled = this.currentPage === 1;
                html += `
                    <button id="prev-page" class="px-3 py-1 border rounded-md ${prevDisabled ? 'bg-gray-100 dark:bg-gray-600 cursor-not-allowed' : 'bg-white dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900'}" 
                            ${prevDisabled ? 'disabled' : ''}>
                        Previous
                    </button>
                `;

                // Generate page numbers
                const maxPagesToShow = 5;
                const halfMax = Math.floor(maxPagesToShow / 2);
                let startPage = Math.max(1, this.currentPage - halfMax);
                let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
                if (endPage - startPage + 1 < maxPagesToShow) {
                    startPage = Math.max(1, endPage - maxPagesToShow + 1);
                }

                if (startPage > 1) {
                    html += `
                        <button class="px-3 py-1 border rounded-md bg-white dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900">1</button>`;
                    if (startPage > 2) {
                        html += `<span class="px-3 py-1">...</span>`;
                    }
                }

                // Generate page number buttons
                for (let i = startPage; i <= endPage; i++) {
                    const isCurrent = i === this.currentPage;
                    html += `
                        ${isCurrent ?
                            `<span class="px-3 py-1 border rounded-md bg-blue-100 text-blue-900 dark:bg-blue-800 dark:text-white">${i}</span>` :
                            `<button class="px-3 py-1 border rounded-md bg-white dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900">${i}</button>`
                        }`;
                }

                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                        html += `<span class="px-3 py-1">...</span>`;
                    }
                    html += `
                        <button class="px-3 py-1 border rounded-md bg-white dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900">${totalPages}</button>`;
                }

                // Next button
                const nextDisabled = this.currentPage === totalPages;
                html += `
                    <button id="next-page" class="px-3 py-1 border rounded-md ${nextDisabled ? 'bg-gray-100 dark:bg-gray-600 cursor-not-allowed' : 'bg-white dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900'}" 
                            ${nextDisabled ? 'disabled' : ''}>
                        Next
                    </button>
                `;

                // Update DOM once
                this.controlsElement.innerHTML = html;

                // Add event listeners after DOM update
                this.controlsElement.querySelector('#prev-page').addEventListener('click', () => {
                    if (this.currentPage > 1) {
                        this.setPage(this.currentPage - 1);
                        applyFilters();
                    }
                });

                this.controlsElement.querySelector('#next-page').addEventListener('click', () => {
                    if (this.currentPage < totalPages) {
                        this.setPage(this.currentPage + 1);
                        applyFilters();
                    }
                });

                this.controlsElement.querySelectorAll('button:not(#prev-page):not(#next-page)').forEach(button => {
                    button.addEventListener('click', () => {
                        const page = parseInt(button.textContent);
                        this.setPage(page);
                        applyFilters();
                    });
                });
            }

            update(totalCount) {
                this.render(totalCount);
            }
        }
    </script>
    <style>
        /* Hide scrollbar for Chrome, Safari and Opera */
        #preset-cards::-webkit-scrollbar {
            display: none;
        }

        /* Hide scrollbar for IE, Edge and Firefox */
        #preset-cards {
            -ms-overflow-style: none;
            /* IE and Edge */
            scrollbar-width: none;
            /* Firefox */
        }
    </style>
</body>

</html>