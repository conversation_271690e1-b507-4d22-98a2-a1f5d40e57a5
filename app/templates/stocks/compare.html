<!DOCTYPE html>
<html lang="en">

<head>
    <title>
        Compare Stocks{% if stocks %} of {% for stock in stocks %}{{ stock.info.symbol.replace(".NS","") }}{% if not loop.last %}, {% endif %}{% endfor %}{% endif %} | AI Bull
    </title>
    <meta name="description"
        content="Compare{% if stocks %} {% for stock in stocks %}{{ stock.info.symbol.replace('.NS','') }}{% if not loop.last %}, {% endif %}{% endfor %}{% endif %} side-by-side with detailed stock analysis, including share price, price to earnings ratio, dividend yield, cash flow, and financial statements to make informed investment decisions." />
    <meta name="keywords"
        content="stock price, stock market, investment decision, stock performance, balance sheet, financial statements, price to earnings ratio, dividend yield, cash flow, company financial position, stock analysis, informed decisions, demat account, trading account, market price, share price" />
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="{{ seo_meta_data.canonical_url | default('https://theaibull.com/screener/compare') }}" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/screener/compare" />
    <meta property="og:title" content="Compare Stock Prices" />
    <meta property="og:description"
        content="Compare stocks with in-depth stock analysis, including share price, market cap, dividend yield, and financial statements to make informed investment decisions in the stock market." />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Compare Stock Prices" />
    <meta name="twitter:description"
        content="Compare stocks with in-depth stock analysis, including share price, market cap, dividend yield, and financial statements to make informed investment decisions in the stock market." />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0"></script>
    <script src="/static/js/chartjs-chart-financial.min.js"></script>
    {% include 'blocks/head.html' %}
    <style>
        /* Ensure the chart container and canvas take the full height */
        .chart-container {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .chart-container canvas {
            width: 100% !important;
            height: 100% !important;
        }

        /* Ensure the parent container has a defined height */
        .chart-wrapper {
            height: 24rem;
            /* Matches h-96 in Tailwind (384px) */
            width: 100%;
            position: relative;
        }
    </style>
</head>

<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen">
    {% include 'blocks/common.html' %}
    {% include 'blocks/info.html' %}
    {% include 'blocks/ai-feedback.html' %}
    <div class="rhs-block duration-300 transition-width relative pb-[62px] xl:pb-0">
        <div class="p-3 mx-auto">
            <div class="mx-auto p-1">
                <!-- Header -->
                <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2 rounded-lg mb-3">
                    <!-- Title and Tabs Row -->
                    <div class="md:flex justify-between items-center">
                        <div class="flex items-center">
                            <h1 class="md:text-xl text-lg font-semibold tracking-tight">Compare Stocks</h1>
                            <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">Compare up to 5 stocks
                                side-by-side to make informed investment decisions.</h2>
                            <!-- Button to trigger modal open -->
                            <button id="openModalBtn" onclick="openModal()" class="ml-2">

                                <div class="flex group text-sm">
                                    <div
                                        class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                        <div
                                            class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-circle-help">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                    <path d="M12 17h.01"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div
                                            class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                            <div
                                                class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                                How to Use This Page</div>
                                        </div>
                                    </div>
                                </div>
                            </button>
                        </div>
                        <a href="/stocks"
                            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm flex items-center font-medium transition-colors duration-200">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Back to Screener
                        </a>
                    </div>
                </div>

                <!-- Popular Stock Comparison Combinations -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm p-8 mb-8 transition-all duration-300">
                    <h2 class="md:text-xl text-lg font-semibold tracking-tight text-gray-900 dark:text-gray-100">Popular
                        Stock Comparisons</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                        Explore popular stock comparison combinations in the Indian stock market to analyze stock
                        performance, share price,
                        and company financial position for better investment decisions. </p>
                    <div id="preset-cards" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- IT Giants -->
                        <a href="/screener/compare/tcs/infy/wipro/hcltech" data-url="tcs/infy/wipro/hcltech"
                            onclick="showCompareLoading('slots-container', 'Analyzing your selected stocks...');"
                            class="comparison-combo relative p-4 rounded-lg text-center transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            data-combo="it">
                            <div
                                class="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 rounded-lg opacity-75">
                            </div>
                            <div class="relative z-10">
                                <h3 class="text-sm font-semibold text-blue-800 dark:text-blue-300">IT Giants</h3>
                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">TCS, Infosys, Wipro, HCL Tech
                                </p>
                            </div>
                        </a>
                        <!-- Banking Leaders -->
                        <a href="/screener/compare/hdfcbank/icicibank/sbin/axisbank"
                            data-url="hdfcbank/icicibank/sbin/axisbank"
                            onclick="showCompareLoading('slots-container', 'Analyzing your selected stocks...');"
                            class="comparison-combo relative p-4 rounded-lg text-center transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                            data-combo="banking">
                            <div
                                class="absolute inset-0 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/30 rounded-lg opacity-75">
                            </div>
                            <div class="relative z-10">
                                <h3 class="text-sm font-semibold text-green-800 dark:text-green-300">Banking Leaders
                                </h3>
                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">HDFC Bank, ICICI Bank, SBI,
                                    Axis Bank</p>
                            </div>
                        </a>
                        <!-- FMCG Titans -->
                        <a href="/screener/compare/hindunilvr/itc/nestleind/britannia"
                            data-url="hindunilvr/itc/nestleind/britannia"
                            onclick="showCompareLoading('slots-container', 'Analyzing your selected stocks...');"
                            class="comparison-combo relative p-4 rounded-lg text-center transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                            data-combo="fmcg">
                            <div
                                class="absolute inset-0 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/30 rounded-lg opacity-75">
                            </div>
                            <div class="relative z-10">
                                <h3 class="text-sm font-semibold text-purple-800 dark:text-purple-300">FMCG Titans</h3>
                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Hindustan Unilever, ITC, Nestlé
                                    India,
                                    Britannia</p>
                            </div>
                        </a>
                        <!-- Energy Giants -->
                        <a href="/screener/compare/reliance/ongc/coalindia/bpcl" data-url="reliance/ongc/coalindia/bpcl"
                            onclick="showCompareLoading('slots-container', 'Analyzing your selected stocks...');"
                            class="comparison-combo relative p-4 rounded-lg text-center transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                            data-combo="energy">
                            <div
                                class="absolute inset-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/30 dark:to-orange-800/30 rounded-lg opacity-75">
                            </div>
                            <div class="relative z-10">
                                <h3 class="text-sm font-semibold text-orange-800 dark:text-orange-300">Energy Giants
                                </h3>
                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Reliance, ONGC, Coal India,
                                    BPCL</p>
                            </div>
                        </a>
                        <!-- Auto Manufacturers -->
                        <a href="/screener/compare/maruti/tatamotors/bajaj-auto/eichermot"
                            data-url="maruti/tatamotors/bajaj-auto/eichermot"
                            onclick="showCompareLoading('slots-container', 'Analyzing your selected stocks...');"
                            class="comparison-combo relative p-4 rounded-lg text-center transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            data-combo="auto">
                            <div
                                class="absolute inset-0 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/30 dark:to-red-800/30 rounded-lg opacity-75">
                            </div>
                            <div class="relative z-10">
                                <h3 class="text-sm font-semibold text-red-800 dark:text-red-300">Auto Manufacturers</h3>
                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Maruti Suzuki, Tata Motors,
                                    Bajaj Auto, Eicher
                                    Motors</p>
                            </div>
                        </a>
                        <!-- Pharma Leaders -->
                        <a href="/screener/compare/sunpharma/drreddy/cipla/auropharma"
                            data-url="sunpharma/drreddy/cipla/auropharma"
                            onclick="showCompareLoading('slots-container', 'Analyzing your selected stocks...');"
                            class="comparison-combo relative p-4 rounded-lg text-center transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500"
                            data-combo="pharma">
                            <div
                                class="absolute inset-0 bg-gradient-to-br from-sky-50 to-sky-100 dark:from-sky-900/30 dark:to-sky-800/30 rounded-lg opacity-75">
                            </div>
                            <div class="relative z-10">
                                <h3 class="text-sm font-semibold text-sky-800 dark:text-sky-300">Pharma Leaders</h3>
                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Sun Pharma, Dr. Reddy’s, Cipla,
                                    Aurobindo
                                    Pharma</p>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Comparison Section -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm p-8 mb-8 transition-all duration-300">
                    <h2 class="md:text-xl text-lg font-semibold tracking-tight">Select Stocks to Compare</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                        Add or remove up to 5 stocks to perform stock analysis, comparing market price, stock
                        performance, and financial
                        statements for informed decisions. </p>
                    <div id="slots-container" class="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
                        {% for i in range(5) %}
                        <div
                            class="comparison-slot bg-gray-100 dark:bg-gray-700 p-4 rounded-lg text-center relative transition-transform duration-200 hover:-translate-y-0.5 hover:shadow-md">
                            <div class="stock-details hidden" data-slot="{{ i }}">
                                <button
                                    class="remove-stock absolute top-2 right-2 text-white bg-red-500 hover:bg-red-600 rounded-full w-6 h-6 flex items-center justify-center"
                                    title="Remove">
                                    ×
                                </button>
                                <p class="stock-name text-sm font-medium text-gray-900 dark:text-gray-100"></p>
                                <p class="stock-sector text-xs text-gray-500 dark:text-gray-400"></p>
                            </div>
                            <div class="add-stock flex flex-col items-center justify-center h-full">
                                <button
                                    class="add-btn text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-2xl"
                                    title="Add Stock">+</button>
                                <input type="text"
                                    class="search-input mt-2 w-full p-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 hidden"
                                    placeholder="Search stock..." />
                                <div
                                    class="autocomplete-results hidden top-20 absolute z-10 w-full max-h-[200px] overflow-y-auto bg-white border border-gray-200 rounded-lg shadow-md">
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center">
                        <button id="compare-btn"
                            class="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-400 transition-all duration-200 opacity-50 cursor-not-allowed"
                            disabled>
                            Compare Now
                        </button>
                    </div>
                </div>

                <main class="compare-section bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    {% if error %}
                    <div
                        class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 p-4 rounded-lg mb-6">
                        <p class="text-sm">{{ error }}</p>
                    </div>
                    {% else %}
                    <!-- Price History Graph -->
                    <div class="mb-6">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                            <div
                                class="bg-gradient-to-bl from-blue-50 via-gray-50 to-gray-50 dark:from-gray-900/30 dark:to-blue-900/30 p-6 border-b border-gray-100 dark:border-gray-700">
                                <div class="p-3 flex justify-between items-center">
                                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left">Stock
                                        Comparison Charts</h3>
                                    <button onclick="analyzeAllStocks();"
                                        class="animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-2 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-sparkles">
                                            <path
                                                d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                            <path d="M20 3v4" />
                                            <path d="M22 5h-4" />
                                            <path d="M4 17v2" />
                                            <path d="M5 18H3" />
                                        </svg> Get AI Feedback
                                    </button>
                                </div>
                                <!-- Tabs -->
                                <div class="flex border-b border-gray-200 dark:border-gray-700 mb-4">
                                    <button id="tab-price"
                                        class="tab-btn px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 border-b-2 border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400 focus:outline-none transition-colors duration-200">Price</button>
                                    <button id="tab-volume"
                                        class="tab-btn px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-600 focus:outline-none transition-colors duration-200">Volume</button>
                                    <button id="tab-financials"
                                        class="tab-btn px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-600 focus:outline-none transition-colors duration-200">Financials</button>
                                </div>
                                <!-- Time Filters -->
                                <!-- Time Filters -->
                                <div id="time-filter-container" class="flex justify-center gap-2 mb-4">
                                    <button id="time-1m"
                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200 active">1M</button>
                                    <button id="time-6m"
                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">6M</button>
                                    <button id="time-1y"
                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">1Y</button>
                                    <button id="time-3y"
                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">3Y</button>
                                    <button id="time-5y"
                                        class="time-filter text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">5Y</button>
                                </div>
                                <!-- Chart Containers -->
                                <div class="chart-wrapper">
                                    <div id="price-chart-container" class="chart-container">
                                        <canvas id="price-chart-compare"></canvas>
                                    </div>
                                    <div id="volume-chart-container" class="chart-container hidden">
                                        <canvas id="volume-chart-compare"></canvas>
                                    </div>
                                    <div id="financials-chart-container" class="chart-container hidden">
                                        <canvas id="financials-chart-compare"></canvas>
                                    </div>
                                </div>
                                <div id="zoom-options" class="flex justify-center gap-3 mt-4">
                                    <button id="reset-zoom-btn"
                                        class="text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">Reset
                                        View</button>
                                    <button id="zoom-in-btn"
                                        class="text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">Zoom
                                        In</button>
                                    <button id="zoom-out-btn"
                                        class="text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-600 px-4 py-2 rounded-lg transition-all duration-200">Zoom
                                        Out</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comparison Table -->
                    <div
                        class="w-full max-h-[900px] overflow-y-auto overflow-x-auto border border-gray-200 rounded-lg dark:border-gray-700">
                        <table class="w-full text-sm text-gray-700 dark:text-gray-300 table-fixed">
                            <thead>
                                <tr
                                    class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900/30 dark:to-blue-900/30">
                                    <th
                                        class="w-48 min-w-48 max-w-48 px-6 py-3 text-left font-medium truncate sticky top-0 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900/30 dark:to-blue-900/30 z-10 border-b-2 border-gray-300 dark:border-gray-600">
                                        Metric</th>
                                    {% for stock in stocks %}
                                    <th
                                        class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate sticky top-0 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900/30 dark:to-blue-900/30 z-10 border-b-2 border-gray-300 dark:border-gray-600">
                                        <div
                                            class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm truncate transition-transform duration-200 hover:-translate-y-0.5 hover:shadow-md">
                                            <a href="/stocks/{{ stock.info.symbol | replace('.NS', '') | lower }}"
                                                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 truncate dark:hover:text-blue-300">{{
                                                stock.info.longName | default('N/A') }}</a>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{
                                                stock.info.sector | default('N/A') }}
                                            </p>
                                            <div class="flex flex-wrap justify-center gap-1 mt-2">
                                                {% if stock.info.industry %}
                                                <span
                                                    class="px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">{{
                                                    stock.info.industry }}</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Stock Details Category -->
                                <tr class="bg-gray-100 dark:bg-gray-800 border-b dark:border-gray-700">
                                    <td class="px-6 py-3 font-bold text-left" colspan="{{ stocks | length + 1 }}">Stock
                                        Details</td>
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Current
                                        Price</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate money">{% if
                                        stock.info.currentPrice %}{{
                                        stock.info.currentPrice | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Previous
                                        Close</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate money">{% if
                                        stock.info.previousClose %}{{
                                        stock.info.previousClose | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Open
                                    </td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate money">{% if
                                        stock.info.open %}{{
                                        stock.info.open | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Day Low
                                    </td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate money">{% if
                                        stock.info.dayLow %}{{
                                        stock.info.dayLow | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Day High
                                    </td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate money">{% if
                                        stock.info.dayHigh %}{{
                                        stock.info.dayHigh | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Market
                                        Cap</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate crmoney">{% if
                                        stock.info.marketCap %}{{
                                        stock.info.marketCap | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">P/E
                                        Ratio</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.trailingPE %}{{
                                        stock.info.trailingPE | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Forward
                                        P/E</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.forwardPE %}{{
                                        stock.info.forwardPE | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Earnings
                                        Per Share</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.trailingEps %}{{
                                        stock.info.trailingEps | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Dividend
                                        Yield (%)</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.dividendYield %}{{
                                        stock.info.dividendYield | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Beta
                                    </td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.beta %}{{ stock.info.beta |
                                        float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">EBITDA
                                    </td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate crmoney">{% if
                                        stock.info.ebitda %}{{
                                        stock.info.ebitda | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Book
                                        Value</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.bookValue %}{{
                                        stock.info.bookValue | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Debt to
                                        Equity</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.debtToEquity %}{{
                                        stock.info.debtToEquity | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">ROCE (%)
                                    </td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.roce %}{{ stock.info.roce |
                                        float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">ROE (%)
                                    </td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.roe %}{{ stock.info.roe |
                                        float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Face
                                        Value</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate money">{% if
                                        stock.info.faceValue %}{{
                                        stock.info.faceValue | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Price to
                                        Book</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.priceToBook %}{{
                                        stock.info.priceToBook | float | round(2) }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>

                                <!-- Performance Category -->
                                <tr class="bg-gray-100 dark:bg-gray-800 border-b dark:border-gray-700">
                                    <td class="px-6 py-3 font-bold text-left" colspan="{{ stocks | length + 1 }}">
                                        Performance</td>
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">1Y
                                        Return (%)</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.fiftyTwoWeekChangePercent
                                        %}{{ (stock.info.fiftyTwoWeekChangePercent * 100) | float | round(2) }}{% else
                                        %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>

                                <!-- About Company Category -->
                                <tr class="bg-gray-100 dark:bg-gray-800 border-b dark:border-gray-700">
                                    <td class="px-6 py-3 font-bold text-left" colspan="{{ stocks | length + 1 }}">About
                                        Company</td>
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Founded
                                        Year</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.yearFounded %}{{
                                        stock.info.yearFounded }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">
                                        Employees</td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.fullTimeEmployees %}{{
                                        stock.info.fullTimeEmployees }}{% else %}N/A{% endif %}</td>
                                    {% endfor %}
                                </tr>
                                <tr class="border-b dark:border-gray-700">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">Website
                                    </td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">
                                        {% if stock.info.website %}
                                        <a href="{{ stock.info.website }}"
                                            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                            target="_blank">
                                            Live Website of {{stock.info.symbol.replace(".NS","")}}
                                        </a>
                                        {% else %}
                                        N/A
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>

                                <!-- Key Officers -->
                                <tr class="bg-gray-100 dark:bg-gray-800 border-b dark:border-gray-700 hidden">
                                    <td class="px-6 py-3 font-bold text-left" colspan="{{ stocks | length + 1 }}">Key
                                        Company Officers</td>
                                </tr>
                                <tr class="border-b dark:border-gray-700 hidden">
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 font-medium text-left truncate">CEO Name
                                    </td>
                                    {% for stock in stocks %}
                                    <td class="w-48 min-w-48 max-w-48 px-6 py-3 text-center truncate">{% if
                                        stock.info.companyOfficers %}
                                        {% for officer in stock.info.companyOfficers %}
                                        {% if officer.title in ["Chief Executive Officer", "MD, CEO & Director", "MD",
                                        "MD & CEO"
                                        "CEO", "Executive
                                        Director"] %}
                                        {{ officer.name }}
                                        {% endif %}
                                        {% endfor %}
                                        {% else %} N/A {% endif %}</td>
                                    {% endfor %}
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!-- Company Overview Section -->
                    <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 transition-all duration-300">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Company Overviews & Financial
                            Position</h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                            Review detailed company profiles, including business summaries and key insights into their
                            balance sheet and company
                            financial position to aid in buying or selling decisions.
                        </p>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {% for stock in stocks %}
                            <div class="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{
                                    stock.info.longName | default('N/A')
                                    }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ stock.info.longBusinessSummary |
                                    default('No
                                    description available.') }}</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Hidden always, Added for SEO -->
                    <div class="hidden bg-white dark:bg-gray-800 rounded-2xl p-8 transition-all duration-300">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Start Investing Today</h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Use a demat account and trading account to start buying or selling stocks in the stock
                            market. Compare share
                            prices and analyze financial statements to make informed investment decisions.
                        </p>
                    </div>

                    <!-- You May Be Interested In Section -->
                    {% if recommended_stocks %}
                    <div class="p-4 relative bg-white dark:bg-gray-800 rounded-lg mb-6 transition-all duration-300">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">You May Be Interested In
                            </h3>
                            <div>
                                <button id="recommended-left-arrow"
                                    class="border z-30 h-10 w-10 bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                    aria-label="Scroll left">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M6 8L2 12L6 16"></path>
                                        <path d="M2 12H22"></path>
                                    </svg>
                                </button>
                                <button id="recommended-right-arrow"
                                    class="h-10 w-10 border bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                                    aria-label="Scroll right">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M18 8L22 12L18 16"></path>
                                        <path d="M2 12H22"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div id="recommended-stocks-cards" class="flex flex-col gap-3 md:flex-row overflow-hidden relative">
                            {% for stock in recommended_stocks %}
                            <a href="/stocks/{{ stock.symbol | replace('.NS', '') | lower }}" class="flex-shrink-0 p-3 rounded-lg cursor-pointer transition-colors w-72 hover:-translate-y-0.5 hover:shadow-md
                                    {% if loop.index0 % 5 == 0 %} bg-blue-500/20
                                    {% elif loop.index0 % 5 == 1 %} bg-emerald-500/20
                                    {% elif loop.index0 % 5 == 2 %} bg-purple-500/20
                                    {% elif loop.index0 % 5 == 3 %} bg-yellow-500/20
                                    {% elif loop.index0 % 5 == 4 %} bg-red-500/20
                                    {% endif %}">
                                <h3 class="font-semibold text-base mb-1">{{ stock.info.longName | default(stock.symbol) }}</h3>
                                <p class="text-xs text-gray-700 dark:text-gray-300">{{ stock.info.sector }} • {{ stock.info.industry }}</p>
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </main>
            </div>
        </div>
    </div>

    <!-- Chart Configurations -->
    <script>
        const businessColors = [
            { border: "#FFC300", background: "rgba(255, 195, 0, 0.1)" },    // golden yellow
            { border: "#FF5733", background: "rgba(255, 87, 51, 0.1)" },   // vibrant orange
            { border: "#33FF57", background: "rgba(51, 255, 87, 0.1)" },   // bright green
            { border: "#3357FF", background: "rgba(51, 87, 255, 0.1)" },   // vivid blue
            { border: "#FF33A1", background: "rgba(255, 51, 161, 0.1)" },  // hot pink
        ];

        function getPriceCompareChartConfig(stocks, timeRange) {
            const timeRanges = { '1m': 30, '6m': 180, '1y': 365, '3y': 1095, '5y': 1825 };
            const days = timeRanges[timeRange] || 365;

            function createGradient(ctx, chartArea, borderColor) {
                let rgb;
                if (borderColor.startsWith('#')) {
                    const hex = borderColor.replace('#', '');
                    rgb = {
                        r: parseInt(hex.substring(0, 2), 16),
                        g: parseInt(hex.substring(2, 4), 16),
                        b: parseInt(hex.substring(4, 6), 16)
                    };
                } else {
                    rgb = { r: 255, g: 195, b: 0 };
                }

                const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                gradient.addColorStop(0, `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`);
                gradient.addColorStop(1, `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.8)`);
                return gradient;
            }

            const datasets = stocks.map((stock, index) => {
                let priceHistory = stock.history;

                if (priceHistory && typeof priceHistory === 'object' && !Array.isArray(priceHistory)) {
                    priceHistory = Object.keys(priceHistory).map(date => ({
                        Date: date,
                        Close: priceHistory[date].Close || priceHistory[date].close || 0
                    }));
                }

                if (!Array.isArray(priceHistory)) {
                    console.warn(`Invalid history for stock ${stock.info?.symbol || 'unknown'}:`, priceHistory);
                    priceHistory = [];
                }

                const cutoffDate = new Date();
                cutoffDate.setDate(cutoffDate.getDate() - days);
                priceHistory = priceHistory.filter(point => {
                    const date = new Date(point?.Date);
                    return !isNaN(date) && date >= cutoffDate && typeof point?.Close === 'number';
                });

                const data = priceHistory.map(point => ({
                    x: point.Date,
                    y: point.Close
                }));

                const color = businessColors[index % businessColors.length];
                const lineColor = color.border;

                return {
                    label: stock.info?.longName || stock.info?.symbol || 'Unknown Stock',
                    data: data,
                    borderColor: lineColor,
                    backgroundColor: (ctx) => {
                        const chart = ctx.chart;
                        const { ctx: canvasCtx, chartArea } = chart;
                        if (!chartArea) return color.background;
                        return createGradient(canvasCtx, chartArea, lineColor);
                    },
                    fill: true,
                    tension: 0.3,
                    pointRadius: 0,
                    borderWidth: 2,
                    yAxisID: stocks.length === 2 ? (index === 0 ? 'y' : 'y1') : 'y' // Assign yAxisID for dual-axis
                };
            });

            const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: timeRange === '1m' ? 'day' : 'month',
                            tooltipFormat: 'MMM dd, yyyy',
                            displayFormats: {
                                day: 'MMM dd',
                                month: 'MMM yyyy'
                            }
                        },
                        title: {
                            display: true,
                            text: 'Date',
                            color: '#1f2937',
                            font: { size: 14 }
                        },
                        grid: { display: false }
                    },
                    y: {
                        title: {
                            display: true,
                            text: stocks.length === 2 && stocks[0]?.info?.longName ? `${stocks[0].info.longName} Price (₹)` : 'Price (₹)',
                            color: '#1f2937',
                            font: { size: 14 }
                        },
                        beginAtZero: false,
                        grid: { color: 'rgba(0, 0, 0, 0.05)' },
                        position: 'left'
                    }
                },
                plugins: {
                    zoom: {
                        zoom: {
                            wheel: { enabled: true },
                            pinch: { enabled: true },
                            mode: 'x'
                        },
                        pan: {
                            enabled: true,
                            mode: 'x'
                        }
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            font: { size: 12 },
                            color: '#1f2937',
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: { size: 14 },
                        bodyFont: { size: 12 },
                        padding: 10,
                        callbacks: {
                            label: function (context) {
                                const label = context.dataset.label || '';
                                const value = context.parsed.y.toFixed(2);
                                return `${label}: ₹${value}`;
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            };

            // Add right Y-axis for dual-axis chart when exactly two stocks are selected
            if (stocks.length === 2) {
                chartOptions.scales.y1 = {
                    title: {
                        display: true,
                        text: stocks[1]?.info?.longName ? `${stocks[1].info.longName} Price (₹)` : 'Price (₹)',
                        color: '#1f2937',
                        font: { size: 14 }
                    },
                    beginAtZero: false,
                    grid: { display: false }, // Avoid overlapping grids
                    position: 'right'
                };
            }

            if (!Chart || !Chart._adapters || !Chart._adapters._date) {
                console.warn('Date adapter not properly loaded. Falling back to linear scale.');
                chartOptions.scales.x = {
                    type: 'linear',
                    title: {
                        display: true,
                        text: 'Index',
                        color: '#1f2937',
                        font: { size: 14 }
                    },
                    grid: { display: false }
                };
                datasets.forEach(dataset => {
                    dataset.data = dataset.data.map((point, idx) => ({
                        x: idx,
                        y: point.y
                    }));
                });
            }

            return {
                type: 'line',
                data: { datasets },
                options: chartOptions
            };
        }

        function getVolumeCompareChartConfig(stocks, timeRange) {
            const timeRanges = { '1m': 30, '6m': 180, '1y': 365, '3y': 1095, '5y': 1825 };
            const days = timeRanges[timeRange] || 365;

            const datasets = stocks.map((stock, index) => {
                let volumeHistory = stock.history;

                if (volumeHistory && typeof volumeHistory === 'object' && !Array.isArray(volumeHistory)) {
                    volumeHistory = Object.keys(volumeHistory).map(date => ({
                        Date: date,
                        Volume: volumeHistory[date].Volume || volumeHistory[date].volume || 0
                    }));
                }

                if (!Array.isArray(volumeHistory)) {
                    console.warn(`Invalid volume history for stock ${stock.info?.symbol || 'unknown'}:`, volumeHistory);
                    volumeHistory = [];
                }

                const cutoffDate = new Date();
                cutoffDate.setDate(cutoffDate.getDate() - days);
                volumeHistory = volumeHistory.filter(point => {
                    const date = new Date(point?.Date);
                    return !isNaN(date) && date >= cutoffDate && typeof point?.Volume === 'number';
                });

                const data = volumeHistory.map(point => ({
                    x: point.Date,
                    y: point.Volume
                }));

                const color = businessColors[index % businessColors.length];

                return {
                    label: stock.info?.longName || stock.info?.symbol || 'Unknown Stock',
                    data: data,
                    backgroundColor: color.border,
                    borderColor: color.border,
                    borderWidth: 1
                };
            });

            const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: timeRange === '1m' ? 'day' : 'month',
                            tooltipFormat: 'MMM dd, yyyy',
                            displayFormats: {
                                day: 'MMM dd',
                                month: 'MMM yyyy'
                            }
                        },
                        title: {
                            display: true,
                            text: 'Date',
                            color: '#1f2937',
                            font: { size: 14 }
                        },
                        grid: { display: false }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Volume',
                            color: '#1f2937',
                            font: { size: 14 }
                        },
                        beginAtZero: true,
                        ticks: {
                            callback: function (value) {
                                if (value >= 1000000) {
                                    return (value / 1000000).toFixed(1) + 'M';
                                } else if (value >= 1000) {
                                    return (value / 1000).toFixed(1) + 'K';
                                }
                                return value;
                            }
                        },
                        grid: { color: 'rgba(0, 0, 0, 0.05)' }
                    }
                },
                plugins: {
                    zoom: {
                        zoom: {
                            wheel: { enabled: true },
                            pinch: { enabled: true },
                            mode: 'x'
                        },
                        pan: {
                            enabled: true,
                            mode: 'x'
                        }
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            font: { size: 12 },
                            color: '#1f2937',
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: { size: 14 },
                        bodyFont: { size: 12 },
                        padding: 10,
                        callbacks: {
                            label: function (context) {
                                const label = context.dataset.label || '';
                                const value = context.parsed.y;
                                return `${label}: ${value.toLocaleString()}`;
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            };

            if (!Chart || !Chart._adapters || !Chart._adapters._date) {
                console.warn('Date adapter not properly loaded. Falling back to linear scale.');
                chartOptions.scales.x = {
                    type: 'linear',
                    title: {
                        display: true,
                        text: 'Index',
                        color: '#1f2937',
                        font: { size: 14 }
                    },
                    grid: { display: false }
                };
                datasets.forEach(dataset => {
                    dataset.data = dataset.data.map((point, idx) => ({
                        x: idx,
                        y: point.y
                    }));
                });
            };

            return {
                type: 'bar',
                data: { datasets },
                options: chartOptions
            };
        }

        function getFinancialsCompareChartConfig(stocks, metric = 'operating_cash_flow') {
            // Map metric to cash-flow type
            const metricMap = {
                'operating_cash_flow': 'Cash from Operating Activity +',
                'investing_cash_flow': 'Cash from Investing Activity +',
                'financing_cash_flow': 'Cash from Financing Activity +',
                'net_cash_flow': 'Net Cash Flow'
            };

            const selectedMetric = metricMap[metric] || metricMap['operating_cash_flow'];

            const datasets = stocks.map((stock, index) => {
                // Find the cash-flow object for the selected metric
                let financialHistory = stock.cash_flow?.["cash-flow"]?.find(item => item[""] !== selectedMetric) || {};

                const data = Object.entries(financialHistory)
                    .filter(([key, value]) => key.startsWith('Mar ') && value !== '')
                    .map(([key, value]) => {
                        const year = key.split(' ')[1];
                        const date = `${year}-03-31`;
                        const numericValue = parseFloat(value.replace(/,/g, '')) || 0;
                        return { x: date, y: numericValue };
                    })
                    .sort((a, b) => new Date(a.x) - new Date(b.x));

                const businessColors = [
                    { border: 'rgba(59, 130, 246, 1)', background: 'rgba(59, 130, 246, 0.2)' },
                    { border: 'rgba(239, 68, 68, 1)', background: 'rgba(239, 68, 68, 0.2)' },
                    { border: 'rgba(16, 185, 129, 1)', background: 'rgba(16, 185, 129, 0.2)' },
                ];
                const color = businessColors[index % businessColors.length];

                return {
                    label: stock.info?.longName || stock.info?.symbol || 'Unknown Stock',
                    data: data,
                    borderColor: color.border,
                    backgroundColor: (context) => {
                        const ctx = context.chart.ctx;
                        const gradient = ctx.createLinearGradient(0, 0, 0, 300);
                        gradient.addColorStop(0, color.background);
                        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
                        return gradient;
                    },
                    fill: true,
                    tension: 0.4,
                    pointRadius: 5,
                    pointHoverRadius: 8,
                    pointHoverBackgroundColor: color.border,
                    pointHoverBorderColor: '#fff',
                    pointHoverBorderWidth: 2,
                    borderWidth: 2,
                };
            });

            const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'year',
                            tooltipFormat: 'MMM yyyy',
                            displayFormats: { year: 'MMM yyyy' }, // Display "Mar YYYY" on x-axis
                        },
                        title: { display: true, text: 'Year', color: '#1f2937', font: { size: 16, family: 'Inter' } },
                        grid: { display: false },
                    },
                    y: {
                        title: {
                            display: true,
                            text: `${selectedMetric} (₹ Cr)`,
                            color: '#1f2937',
                            font: { size: 16, family: 'Inter' }
                        },
                        beginAtZero: false,
                        ticks: {
                            callback: function (value) {
                                return value.toLocaleString('en-IN', { maximumFractionDigits: 0 }) + ' Cr';
                            },
                        },
                        grid: { color: 'rgba(0, 0, 0, 0.05)' },
                    },
                },
                plugins: {
                    zoom: {
                        zoom: { wheel: { enabled: true }, pinch: { enabled: true }, mode: 'x' },
                        pan: { enabled: true, mode: 'x' },
                    },
                    legend: {
                        position: 'top',
                        labels: { font: { size: 12, family: 'Inter' }, color: '#1f2937', padding: 20, usePointStyle: true },
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: { size: 14, family: 'Inter' },
                        bodyFont: { size: 12, family: 'Inter' },
                        padding: 10,
                        callbacks: {
                            label: function (context) {
                                const label = context.dataset.label || '';
                                const value = context.parsed.y.toLocaleString('en-IN', { maximumFractionDigits: 2 });
                                return `${label}: ₹${value} Cr`;
                            },
                            title: function (tooltipItems) {
                                const date = new Date(tooltipItems[0].parsed.x);
                                return date.toLocaleString('en-US', { month: 'short', year: 'numeric' }); // e.g., "Mar 2025"
                            },
                        },
                    },
                },
                interaction: { mode: 'nearest', axis: 'x', intersect: false },
            };

            return {
                type: 'line',
                data: { datasets },
                options: chartOptions,
            };
        }

        function setupZoomControls(chart) {
            const resetZoomBtn = document.getElementById('reset-zoom-btn');
            const zoomInBtn = document.getElementById('zoom-in-btn');
            const zoomOutBtn = document.getElementById('zoom-out-btn');

            if (resetZoomBtn) {
                resetZoomBtn.addEventListener('click', () => chart.resetZoom());
            }
            if (zoomInBtn) {
                zoomInBtn.addEventListener('click', () => chart.zoom(1.2));
            }
            if (zoomOutBtn) {
                zoomOutBtn.addEventListener('click', () => chart.zoom(0.8));
            }
        }
    </script>
    <script>
        // Initialize slot assignments (null for empty slots)
        let stocksData = []; // Will be populated via API
        const urlParts = window.location.pathname.split('/').filter(part => part);
        const slotAssignments = Array(5).fill(null);
        const slots = document.querySelectorAll('.comparison-slot');
        const compareBtn = document.getElementById('compare-btn');

        // Fetch stock data from API
        async function fetchStocksData(symbols) {
            if (!symbols || symbols.length === 0) {
                console.warn('No stock symbols provided for API call.');
                return [];
            }
            try {
                const response = await fetch(`/screener/api/compare/${symbols.map(encodeURIComponent).join('/')}`, {
                    method: 'GET',
                    credentials: 'include' // Include cookies for access_token
                });
                const data = await response.json();
                if (!response.ok || data.error) {
                    console.error('API error:', data.error || 'Failed to fetch stocks');
                    alert('Error: ' + (data.error || 'Unable to load stock data. Please try again.'));
                    return [];
                }
                console.log('Fetched stock data:', data);
                return data.stocks; // Returns the stocks array directly
            } catch (error) {
                console.error('Error fetching stocks from API:', error);
                alert('Failed to connect to the server. Please check your connection.');
                return [];
            }
        }

        function normalizeSymbol(symbol) {
            return symbol ? symbol.replace('.NS', '') : '';
        }

        function initializeCharts(timeRange = '1m') {
            const priceCanvas = document.getElementById("price-chart-compare")?.getContext("2d");
            const volumeCanvas = document.getElementById("volume-chart-compare")?.getContext("2d");
            const financialsCanvas = document.getElementById("financials-chart-compare")?.getContext("2d");

            if (!stocksData || stocksData.length === 0) {
                console.warn('No valid stock data for charts');
                return;
            }

            if (priceCanvas) {
                if (window.priceCompareChart) window.priceCompareChart.destroy();
                window.priceCompareChart = new Chart(priceCanvas, getPriceCompareChartConfig(stocksData, timeRange));
                setupZoomControls(window.priceCompareChart);
                window.priceCompareChart.resize();
            }

            if (volumeCanvas) {
                if (window.volumeCompareChart) window.volumeCompareChart.destroy();
                window.volumeCompareChart = new Chart(volumeCanvas, getVolumeCompareChartConfig(stocksData, timeRange));
                setupZoomControls(window.volumeCompareChart);
                window.volumeCompareChart.resize();
            }

            if (financialsCanvas) {
                if (window.financialsCompareChart) window.financialsCompareChart.destroy();
                window.financialsCompareChart = new Chart(financialsCanvas, getFinancialsCompareChartConfig(stocksData, 'operating_cash_flow'));
                setupZoomControls(window.financialsCompareChart);
                window.financialsCompareChart.resize();
            }
        }

        // Handle tab switching
        function setupTabs() {
            const priceTab = document.getElementById('tab-price');
            const volumeTab = document.getElementById('tab-volume');
            const financialsTab = document.getElementById('tab-financials');
            const priceContainer = document.getElementById('price-chart-container');
            const volumeContainer = document.getElementById('volume-chart-container');
            const financialsContainer = document.getElementById('financials-chart-container');
            const timeFilterContainer = document.getElementById('time-filter-container');

            function activateTab(activeTab, activeContainer) {
                [priceTab, volumeTab, financialsTab].forEach(tab => {
                    tab.classList.remove('border-blue-600', 'dark:border-blue-400', 'text-blue-600', 'dark:text-blue-400');
                    tab.classList.add('border-transparent', 'hover:border-gray-300', 'dark:hover:border-gray-600', 'text-gray-700', 'dark:text-gray-300');
                });
                [priceContainer, volumeContainer, financialsContainer].forEach(container => {
                    container.classList.add('hidden');
                });
                activeTab.classList.add('border-blue-600', 'dark:border-blue-400', 'text-blue-600', 'dark:text-blue-400');
                activeTab.classList.remove('border-transparent', 'hover:border-gray-300', 'dark:hover:border-gray-600', 'text-gray-700', 'dark:text-gray-300');
                activeContainer.classList.remove('hidden');

                // Show/hide time filter based on active tab
                timeFilterContainer.classList.toggle('hidden', activeTab === financialsTab);

                // Resize the active chart
                if (activeContainer === priceContainer && window.priceCompareChart) {
                    window.priceCompareChart.resize();
                } else if (activeContainer === volumeContainer && window.volumeCompareChart) {
                    window.volumeCompareChart.resize();
                } else if (activeContainer === financialsContainer && window.financialsCompareChart) {
                    window.financialsCompareChart.resize();
                }
            }

            if (priceTab) {
                priceTab.addEventListener('click', () => activateTab(priceTab, priceContainer));
            }
            if (volumeTab) {
                volumeTab.addEventListener('click', () => activateTab(volumeTab, volumeContainer));
            }
            if (financialsTab) {
                financialsTab.addEventListener('click', () => activateTab(financialsTab, financialsContainer));
            }
        }

        // Handle time filter buttons
        function setupTimeFilters() {
            const timeFilterButtons = document.querySelectorAll('.time-filter');
            timeFilterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const range = button.id.replace('time-', '');
                    updateFilterButtons(button);
                    initializeCharts(range);
                });
            });
            updateFilterButtons(document.getElementById('time-1m'));
        }

        // Update filter buttons
        function updateFilterButtons(activeButton) {
            const buttons = document.querySelectorAll('.time-filter');
            buttons.forEach(btn => {
                btn.classList.remove('active', 'bg-blue-600', 'text-white');
                btn.classList.add('bg-gray-200', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
            });
            activeButton?.classList.add('active', 'bg-blue-600', 'text-white');
            activeButton?.classList.remove('bg-gray-200', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
        }

        // Pre-populate slots with selected stocks from API data
        async function prePopulateSlots() {
            // Extract stock symbols from the URL path
            const stockSymbols = urlParts.slice(urlParts.indexOf('compare') + 1);
            if (stockSymbols.length > 0) {
                stocksData = await fetchStocksData(stockSymbols);
                stockSymbols.forEach((symbol, index) => {
                    symbol = decodeURIComponent(symbol.toUpperCase())
                    if (index < slots.length) {
                        // Normalize the symbol from URL
                        const normalizedUrlSymbol = normalizeSymbol(symbol);

                        // Find matching stock in stocksData
                        const stock = stocksData.find(s => {
                            const stockSymbol = normalizeSymbol(s.info?.symbol);
                            return stockSymbol === normalizedUrlSymbol ||
                                normalizedUrlSymbol === s.info?.symbol ||
                                symbol === s.info?.symbol;
                        });

                        console.log(`Matching for ${symbol}:`, stock ? 'Found' : 'Not found');

                        if (stock) {
                            const slot = slots[index];
                            const stockDetails = slot.querySelector('.stock-details');
                            const addStockDiv = slot.querySelector('.add-stock');

                            // Show stock details
                            stockDetails.classList.remove('hidden');

                            // Update stock information
                            const stockName = stockDetails.querySelector('.stock-name');
                            const stockSector = stockDetails.querySelector('.stock-sector');

                            stockName.textContent = stock.info.longName || stock.info.symbol || 'N/A';
                            stockSector.textContent = stock.info.sector || 'N/A';
                            stockDetails.dataset.symbol = stock.info.symbol;

                            // Update slot assignment
                            slotAssignments[index] = stock.info.symbol;

                            // Hide add stock div and show stock details
                            stockDetails.style.display = 'block';
                            addStockDiv.style.display = 'none';
                        }
                    }
                });
            }

            // Update compare button state after pre-populating
            updateCompareButton();
        }

        // Update Compare button state
        function updateCompareButton() {
            const assignedCount = slotAssignments.filter(code => code !== null).length;
            compareBtn.disabled = assignedCount < 2;
            compareBtn.classList.toggle('opacity-50', assignedCount < 2);
            compareBtn.classList.toggle('cursor-not-allowed', assignedCount < 2);
        }

        // Fetch stocks for autocomplete
        async function fetchStocks(query) {
            try {
                const response = await fetch('/screener/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ longName: query, columns: ['symbol', 'longName', 'sector'] })
                });
                if (!response.ok) throw new Error(`HTTP error ${response.status}`);
                const data = await response.json();
                return data || [];
            } catch (error) {
                console.error('Error fetching stocks:', error);
                alert('Failed to fetch stocks. Please try again.');
                return [];
            }
        }

        // Show autocomplete results
        function showAutocomplete(input, resultsContainer, slotIndex) {
            input.addEventListener('input', async () => {
                const query = input.value.trim();
                if (query.length < 2) {
                    resultsContainer.innerHTML = '';
                    resultsContainer.classList.add('hidden');
                    return;
                }
                const stocks = await fetchStocks(query);
                resultsContainer.innerHTML = stocks.map(stock => `
                    <div class="autocomplete-item p-2 cursor-pointer hover:bg-gray-100" data-slot="${slotIndex}" data-symbol="${stock.symbol}" data-name="${stock.longName}" data-sector="${stock.sector}">
                        ${stock.longName} (${stock.symbol})
                    </div>
                `).join('');
                resultsContainer.classList.remove('hidden');
            });
        }

        // Handle slot interactions
        slots.forEach((slot, index) => {
            const addBtn = slot.querySelector('.add-btn');
            const searchInput = slot.querySelector('.search-input');
            const resultsContainer = slot.querySelector('.autocomplete-results');
            const stockDetails = slot.querySelector('.stock-details');
            const addStockDiv = slot.querySelector('.add-stock');
            const removeBtn = slot.querySelector('.remove-stock');

            addBtn.addEventListener('click', () => {
                const assignedCount = slotAssignments.filter(code => code !== null).length;
                if (assignedCount >= 5) {
                    alert('Cannot add more than 5 stocks for comparison.');
                    return;
                }
                if (slotAssignments[index] === null) {
                    addBtn.classList.add('hidden');
                    searchInput.classList.remove('hidden');
                    searchInput.focus();
                    showAutocomplete(searchInput, resultsContainer, index);
                }
            });

            resultsContainer.addEventListener('click', (e) => {
                const item = e.target.closest('.autocomplete-item');
                if (item) {
                    const symbol = item.getAttribute('data-symbol');
                    const name = item.getAttribute('data-name');
                    const sector = item.getAttribute('data-sector');

                    if (slotAssignments.includes(symbol)) {
                        alert('This stock is already selected.');
                        return;
                    }

                    const emptySlotIndex = slotAssignments.indexOf(null);
                    if (emptySlotIndex === -1) {
                        alert('No empty slots available.');
                        return;
                    }

                    slotAssignments[emptySlotIndex] = symbol;
                    const targetSlot = slots[emptySlotIndex];
                    const targetStockDetails = targetSlot.querySelector('.stock-details');
                    const targetAddStockDiv = targetSlot.querySelector('.add-stock');
                    targetStockDetails.dataset.symbol = symbol;
                    targetStockDetails.querySelector('.stock-name').textContent = name;
                    targetStockDetails.querySelector('.stock-sector').textContent = sector;
                    targetStockDetails.style.display = 'block';
                    targetAddStockDiv.style.display = 'none';

                    searchInput.classList.add('hidden');
                    resultsContainer.classList.add('hidden');
                    if (slotAssignments[index] === null) {
                        addBtn.classList.remove('hidden');
                    }
                    updateCompareButton();
                }
            });

            removeBtn.addEventListener('click', () => {
                if (slotAssignments[index] !== null) {
                    slotAssignments[index] = null;
                    stockDetails.dataset.symbol = '';
                    stockDetails.style.display = 'none';
                    addStockDiv.style.display = 'block';
                    addBtn.classList.remove('hidden');
                    updateCompareButton();
                }
            });

            document.addEventListener('click', (e) => {
                if (!slot.contains(e.target)) {
                    resultsContainer.classList.add('hidden');
                    searchInput.classList.add('hidden');
                    if (slotAssignments[index] === null) {
                        addBtn.classList.remove('hidden');
                    }
                }
            });
        });

        // Navigate to compare page
        compareBtn.addEventListener('click', () => {
            const assignedStocks = slotAssignments.map((code, index) => ({ code, index }))
                .filter(item => item.code !== null);
            if (assignedStocks.length >= 2) {
                const query = assignedStocks.map(item => `${encodeURIComponent(item.code).replace(".NS", "").toLowerCase()}`).join('/');
                window.location.href = `/screener/compare/${query}`;
            }
            showCompareLoading('slots-container', 'Analyzing your selected stocks...');
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            await prePopulateSlots(); // Fetch and populate stocks
            initComparisonCards();
            initializeCharts();
            setupTabs();
            setupTimeFilters();
            updateCompareButton();
        });
    </script>

    <!-- Formatting in UI for jinja elements -->
    <script>
        // Function to format numbers in Indian style
        const parseNumber = (text) => {
            const match = text.match(/[\d,.]+/g);
            return match ? parseFloat(match.join('').replace(/,/g, '')) : NaN;
        };

        // Shared currency formatting function
        const formatCurrency = (value, isCrore = false) => {
            if (isNaN(value)) return 'N/A';
            const formattedValue = formatIndianNumber(Math.round(value));
            return `₹${formattedValue}${isCrore ? ' Cr' : ''}`;
        };

        // Indian number formatting
        function formatIndianNumber(num) {
            let str = num.toString();
            let lastThree = str.slice(-3);
            let otherNumbers = str.slice(0, -3);
            if (otherNumbers !== '') {
                lastThree = ',' + lastThree;
            }
            return otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ",") + lastThree;
        }

        // Process elements
        document.querySelectorAll('.crmoney').forEach(el => {
            const value = parseNumber(el.textContent);
            const formatted = formatCurrency(value / 10000000, true);
            if (formatted) el.textContent = formatted;
        });
        document.querySelectorAll('.money').forEach(el => {
            const value = parseNumber(el.textContent);
            const formatted = formatCurrency(value);
            if (formatted) el.textContent = formatted;
        });
    </script>

    <script>
        function analyzeAllStocks() {
            if (!Array.isArray(stocksData) || stocksData.length === 0) {
                showAlert("No stock data available for analysis");
                return;
            }

            // Initialize an array to collect all payloads
            const payloads = [];

            stocksData.forEach(stockData => {
                if (!stockData || !stockData.info) return;

                // Prepare payload for AI analysis matching the provided schema
                const payload = {
                    symbol: stockData.info.symbol,
                    company_name: stockData.info.longName || stockData.info.shortName,
                    current_price: stockData.info.currentPrice,
                    sector: stockData.info.sector,
                    industry: stockData.info.industry,
                    market_cap: stockData.info.marketCap ? stockData.info.marketCap / 1e9 : null, // Convert to billions
                    pe_ratio: stockData.info.trailingPE,
                    eps: stockData.info.trailingEps,
                    dividend_yield: stockData.info.dividendYield ? stockData.info.dividendYield / 100 : null, // Convert to decimal
                    '52w_high': stockData.info.fiftyTwoWeekHigh,
                    '52w_low': stockData.info.fiftyTwoWeekLow,
                    business_summary: stockData.info.longBusinessSummary,
                    key_metrics: {
                        revenue_growth: stockData.info.revenueGrowth || null,
                        profit_margin: stockData.info.profitMargins || null,
                        debt_to_equity: stockData.info.debtToEquity || null,
                        return_on_equity: stockData.info.returnOnEquity || null,
                        quick_ratio: stockData.info.quickRatio || null,
                        peg_ratio: stockData.info.pegRatio || null
                    }
                };

                // Add the payload to the payloads array
                payloads.push(payload);
            });

            // Call the AI feedback function with the array of payloads
            if (payloads.length > 0) {
                getAIFeedback({ stocks: payloads }, { analysisType: 'compare_stock' });
            }
        }

        const initComparisonCards = () => {
            const container = document.querySelector('#preset-cards');
            if (!container) return;

            const cards = container.querySelectorAll('a[data-url]');
            const comparisonId = window.location.pathname.split('/screener/compare/')[1] || '';

            // Constants for class names
            const ACTIVE_CLASSES = ['border-2', 'shadow-lg', 'shadow-gray-400'];
            const DEFAULT_COLOR = 'blue';

            cards.forEach(card => {
                const cardUrl = card.getAttribute('data-url') || '';
                const isActive = comparisonId === cardUrl;

                // Extract border color efficiently
                const gradientDiv = card.querySelector('div.absolute.inset-0');
                const colorKey = gradientDiv?.className.match(/-(\w+)-50/)?.[1] || DEFAULT_COLOR;
                const borderClass = `border-${colorKey}-700`;

                const classesToToggle = [...ACTIVE_CLASSES, borderClass];
                classesToToggle.forEach(cls => {
                    card.classList.toggle(cls, isActive);
                });
            });
        };

        initializeCardsCarousel('recommended-stocks-cards', 'recommended-left-arrow', 'recommended-right-arrow');
    </script>

    {% include 'blocks/footer.html' %}
</body>

</html>