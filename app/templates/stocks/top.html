<!DOCTYPE html>
<html lang="en">

<head>
    <title>Stock Recommendations - Strong Buy, Buy, <PERSON> Sell, Sell, Hold | AI Bull</title>
    <meta name="description"
        content="Get AI-powered stock recommendations with AIBull. Discover stocks labeled as Strong Buy, Buy, Strong Sell, Sell, or Hold based on real-time market data and expert analysis for smarter trading decisions." />
    <link rel="canonical" href="https://theaibull.com/stocks/top" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/stocks/top" />
    <meta property="og:title" content="Stock Recommendations - Strong Buy, Buy, Strong Sell, Sell, Hold" />
    <meta property="og:description"
        content="Access AI-powered stock recommendations with AIBull. View stocks rated as Strong Buy, Buy, Strong Sell, Sell, or Hold based on real-time data and market trends for informed trading." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@aibull" />
    <meta name="twitter:title" content="Stock Recommendations - Strong Buy, Buy, Strong Sell, Sell, Hold" />
    <meta name="twitter:description"
        content="Find the best stock recommendations with ratings such as Strong Buy, Buy, Strong Sell, Sell, or Hold, powered by AI analysis for smarter trading decisions." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {%include 'blocks/common.html' %}

    <div
        class="rhs-block duration-300 transition-width flex flex-col justify-between min-h-screen bg-neutral-100 dark:bg-neutral-900">
        <main class="p-3 mx-auto w-full">
            <div class="relative bg-white p-5 mb-4 rounded-md border" style="
            background-image: linear-gradient(0deg, #e8f7fcc4 10%, #fccaca47 45%, #dee9fc57 80%, transparent 100%);
        ">

                <div class="mb-8">
                    <h1 class="text-3xl font-semibold mb-2 text-center dark:text-neutral-100 tracking-tight">NSE Top
                        Stocks</h1>
                    <p class="text-center text-neutral-700 dark:text-neutral-400">Technical Analysis Based Stock
                        Recommendations</p>
                </div>

                <!-- Search Input Field -->
                <div class="max-w-2xl mx-auto">
                    <div class="relative">
                        <input id="stock-search" type="text" placeholder="Search stocks by symbol..."
                            class="w-full px-10  py-3.5 border-2 border-gray-300  rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all" />
                        <svg class="w-5 h-5 absolute left-3 top-4 text-gray-400 dark:text-neutral-500" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            {% set categories = ["Strong Buy", "Buy", "Strong Sell", "Sell", "Hold"] %}

            {% set category_colors = {
            "Strong Buy": "bg-green-300 text-black",
            "Buy": "bg-green-200 text-black",
            "Strong Sell": "bg-red-300 text-black",
            "Sell": "bg-red-200",
            "Hold": "bg-gray-300"
            } %}

            <div class="min-w-full">
                <div class="flex flex-row flex-nowrap gap-3 min-w-full xl:overflow-x-visible overflow-x-auto"
                    id="stock-grid">
                    {% for category in categories %}
                    <div
                        class="bg-white dark:bg-neutral-800 border dark:border-neutral-700 rounded-lg shrink-0 xl:flex-1 w-[85%] md:w-[42%] xl:w-1/5">
                        <div
                            class="dark:border-neutral-700 p-3 {{ category_colors[category] }} rounded-t-md sticky top-0 xl:top-9">
                            <h2 class="text-lg font-semibold text-center dark:text-neutral-100">{{ category }}</h2>
                        </div>
                        {% set matching_stocks = analyzed_stocks.values() | selectattr("recommendation", "equalto",
                        category) | list %}
                        {% if matching_stocks %}
                        <ul
                            class="stock-list divide-y dark:divide-neutral-700 max-h-[calc(100vh_-_100px)] xl:pb-0 pb-16 overflow-y-auto md:max-h-[calc(100vh_-_100px)] xl:max-h-none xl:overflow-y-visible">
                            {% for stock in matching_stocks %}
                            <li class="stock-item hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-colors duration-150"
                                data-symbol="{{ stock.symbol }}">
                                <div class="p-4">
                                    <div class="flex justify-between items-center mb-3">
                                        <div class="font-semibold text-lg dark:text-neutral-100">{{ stock.symbol }}
                                        </div>
                                        <div class="text-base font-medium text-neutral-900 dark:text-neutral-200">
                                            {% if stock.close is not none %}{{ "%.2f"|format(stock.close) }}{% else
                                            %}N/A{%
                                            endif %}
                                        </div>
                                    </div>
                                    <div
                                        class="grid grid-cols-2 gap-2 xl:text-[13px] text-xs text-neutral-600 dark:text-neutral-400">
                                        <div>
                                            RSI: {% if stock.RSI is not none %}{{ "%.2f"|format(stock.RSI) }}{% else
                                            %}N/A{%
                                            endif %}
                                        </div>
                                        <div>
                                            EMA20: {% if stock.EMA20 is not none %}{{ "%.2f"|format(stock.EMA20) }}{%
                                            else
                                            %}N/A{% endif %}
                                        </div>
                                        <div>
                                            EMA50: {% if stock.EMA50 is not none %}{{ "%.2f"|format(stock.EMA50) }}{%
                                            else
                                            %}N/A{% endif %}
                                        </div>
                                        <div>
                                            EMA200: {% if stock.EMA200 is not none %}{{ "%.2f"|format(stock.EMA200) }}{%
                                            else
                                            %}N/A{% endif %}
                                        </div>
                                        {% if stock.SMA20 is defined and stock.SMA20 is not none %}
                                        <div>
                                            SMA20: {{ "%.2f"|format(stock.SMA20) }}
                                        </div>
                                        {% elif stock.SMA50 is defined and stock.SMA50 is not none %}
                                        <div>
                                            SMA50: {{ "%.2f"|format(stock.SMA50) }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="px-4 py-2 bg-gray-50 text-xs text-neutral-700 dark:text-neutral-500">
                                    Last Updated: {{ stock.latest_date }}
                                </div>
                            </li>
                            {% endfor %}
                        </ul>
                        {% else %}
                        <div class="p-8">
                            <p class="text-center text-neutral-500 dark:text-neutral-400">No stocks in this category</p>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Client-side Search Filtering Script -->
            <script>
                const searchInput = document.getElementById("stock-search");

                searchInput.addEventListener("input", function () {
                    const query = this.value.trim().toLowerCase();
                    const stockItems = document.querySelectorAll(".stock-item");

                    stockItems.forEach((item) => {
                        const symbol = item.getAttribute("data-symbol")?.toLowerCase();
                        if (symbol && symbol.includes(query)) {
                            item.style.display = "";
                        } else {
                            item.style.display = "none";
                        }
                    });

                    document.querySelectorAll(".stock-list").forEach((list) => {
                        const existingMsg = list.parentNode.querySelector(".no-match-message");
                        if (existingMsg) {
                            existingMsg.remove();
                        }
                        const visibleItems = list.querySelectorAll(".stock-item:not([style*='display: none'])");
                        if (visibleItems.length === 0) {
                            const li = document.createElement("div");
                            li.className = "no-match-message p-8 text-center text-neutral-500 dark:text-neutral-400";
                            li.textContent = "No matching stocks";
                            list.appendChild(li);
                        }
                    });
                });
            </script>
        </main>
        {% include 'blocks/footer.html' %}
    </div>

</body>

</html>