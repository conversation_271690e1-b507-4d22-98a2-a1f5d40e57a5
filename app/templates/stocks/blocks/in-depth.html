<section class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4">
    <h3 class="text-lg font-semibold mb-3">Company Details</h3>
    <div
        class="[&_.active]:text-blue-600 [&_.active]:border-b-2 [&_.active]:border-blue-600 [&_.active]:pb-2 [&_.active]:mb-[-9px] [&_.active]:transition-colors [&_.active]:font-medium">
        <div>
            <nav class="border-b flex gap-6 pb-2 text-nowrap overflow-x-auto overflow-y-hidden">
                <a href="#key-metrics" class="text-neutral-600 text-sm">

                    <span>Key Metrics</span>
                </a>
                <a href="#quarterly-results" class="text-neutral-600 text-sm">

                    <span>Quarterly Results</span>
                </a>
                <a href="#shareholding" class="text-neutral-600 text-sm">

                    <span>Shareholding</span>
                </a>
                <a href="#cash-flow" class="text-neutral-600 text-sm">

                    <span>Cash Flow</span>
                </a>
                <a href="#balance-sheet" class="text-neutral-600 text-sm">

                    <span>Balance Sheet</span>
                </a>
                <a href="#profit-loss" class="text-neutral-600 text-sm">

                    <span>Profit & Loss</span>
                </a>
                <a href="#ratios" class="text-neutral-600 text-sm">

                    <span>Ratios</span>
                </a>
                <a href="#peers" class="text-neutral-600 text-sm">

                    <span>Peers</span>
                </a>
            </nav>
        </div>
        <div class="pt-4">
            {% include 'stocks/blocks/in-depth-sub/loader.html' %} {% include
            'stocks/blocks/in-depth-sub/key-metrics.html' %} {% include
            'stocks/blocks/in-depth-sub/quarterly-results.html' %} {% include
            'stocks/blocks/in-depth-sub/shareholding.html' %} {% include 'stocks/blocks/in-depth-sub/cash-flow.html' %}
            {% include 'stocks/blocks/in-depth-sub/balance-sheet.html' %} {% include
            'stocks/blocks/in-depth-sub/profit-loss.html' %} {% include 'stocks/blocks/in-depth-sub/ratios.html' %} {%
            include 'stocks/blocks/in-depth-sub/peers.html' %}
        </div>
    </div>
</section>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const loader = document.getElementById("tab-loader");
        const tabContents = document.querySelectorAll(".tab-content");
        const tabLinks = document.querySelectorAll('a[href^="#"]');

        function showLoader() {
            loader.classList.remove("hidden");
        }

        function hideLoader() {
            loader.classList.add("hidden");
        }

        function handleTabClick(event) {
            event.preventDefault();

            // Remove 'active' class from all tabs
            tabLinks.forEach((link) => {
                link.classList.remove("active");
            });

            // Add 'active' class to the clicked tab
            event.currentTarget.classList.add("active");

            const targetId = event.currentTarget.getAttribute("href").substring(1);

            tabContents.forEach((content) => {
                content.classList.add("hidden");
            });

            const selectedContent = document.getElementById(`${targetId}-content`);
            if (selectedContent) {
                selectedContent.classList.remove("hidden");
                showLoader();

                switch (targetId) {
                    case "quarterly-results":
                        if (typeof loadQuarterlyResults === "function") loadQuarterlyResults();
                        break;
                    case "shareholding":
                        if (typeof loadShareholdingData === "function") loadShareholdingData();
                        break;
                    case "cash-flow":
                        if (typeof loadCashFlowData === "function") loadCashFlowData();
                        break;
                    case "balance-sheet":
                        if (typeof loadBalanceSheetData === "function") loadBalanceSheetData();
                        break;
                    case "profit-loss":
                        if (typeof loadProfitLossData === "function") loadProfitLossData();
                        break;
                    case "ratios":
                        if (typeof loadRatiosData === "function") loadRatiosData();
                        break;
                    case "peers":
                        if (typeof loadPeersData === "function") loadPeersData();
                        break;
                    default:
                        hideLoader();
                }
            }
        }

        // Add event listener to each tab link
        tabLinks.forEach((link) => {
            link.addEventListener("click", handleTabClick);
        });

        // Show the first tab and content by default
        document.getElementById("key-metrics-content").classList.remove("hidden");
        document.querySelector('a[href="#key-metrics"]').classList.add("active");
    });
</script>