<div id="key-metrics-content" class="tab-content">
    <div class="grid md:grid-cols-2 grid-cols-1 md:gap-6 gap-4">
        <div class="space-y-3" id="key-metrics-left"></div>
        <div class="space-y-3" id="key-metrics-right"></div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        function createMetricElement(label, value) {
            const div = document.createElement("div");
            div.className = "flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700";

            const labelSpan = document.createElement("span");
            labelSpan.className = "text-gray-600 dark:text-gray-300";
            labelSpan.textContent = label;

            const valueSpan = document.createElement("span");
            valueSpan.className = "font-medium";

            if (value.includes("/")) {
                const [high, low] = value.split("/");
                valueSpan.innerHTML = `<span class="number">${formatNumber(high)}</span> / <span class="number">${formatNumber(low)}</span>`;
            } else if (label === "Market Cap") {
                valueSpan.innerHTML = `<span class="number">${formatNumber(value)}</span> Cr.`;
            } else if (label === "Current Price" || label === "Book Value" || label === "Face Value") {
                valueSpan.innerHTML = `<span class="number">${formatNumber(value)}</span>`;
            } else if (label === "Dividend Yield" || label === "ROCE" || label === "ROE") {
                valueSpan.innerHTML = `<span class="number">${value}</span>%`;
            } else {
                valueSpan.innerHTML = `<span class="number">${value}</span>`;
            }

            div.appendChild(labelSpan);
            div.appendChild(valueSpan);
            return div;
        }

        async function loadKeyMetrics() {
            try {
                const response = await fetch("/api/screener/{{ symbol }}/key-metrics");
                const data = await response.json();

                const metrics = data.key_metrics;
                const leftColumn = document.getElementById("key-metrics-left");
                const rightColumn = document.getElementById("key-metrics-right");

                leftColumn.innerHTML = "";
                rightColumn.innerHTML = "";

                const leftMetrics = ["Market Cap", "Current Price", "High / Low", "Stock P/E", "Book Value"];
                const rightMetrics = ["Dividend Yield", "ROCE", "ROE", "Face Value", "Price to book"];

                leftMetrics.forEach((metric) => {
                    if (metrics[metric]) {
                        leftColumn.appendChild(createMetricElement(metric, metrics[metric]));
                    }
                });

                rightMetrics.forEach((metric) => {
                    if (metrics[metric]) {
                        rightColumn.appendChild(createMetricElement(metric, metrics[metric]));
                    }
                });
            } catch (error) {
                console.error("Error loading key metrics:", error);
            }
        }

        loadKeyMetrics();
    });
</script>