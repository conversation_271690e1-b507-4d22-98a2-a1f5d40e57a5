<div id="profit-loss-content" class="tab-content hidden">
  <div class="relative">
    <div class="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-100 dark:from-gray-700 to-transparent pointer-events-none z-10"></div>

    <div class="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-100 dark:from-gray-700 to-transparent pointer-events-none z-10"></div>

    <button
      id="profit-loss-scroll-left"
      class="absolute left-0 top-1/2 -translate-y-1/2 z-20 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
    >
      <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>
    <button
      id="profit-loss-scroll-right"
      class="absolute right-0 top-1/2 -translate-y-1/2 z-20 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
    >
      <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>

    <div class="overflow-x-auto shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="inline-block min-w-full align-middle">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr id="profit-loss-header"></tr>
          </thead>
          <tbody id="profit-loss-body" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"></tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const scrollContainer = document.querySelector(".overflow-x-auto");
    const scrollLeftBtn = document.getElementById("profit-loss-scroll-left");
    const scrollRightBtn = document.getElementById("profit-loss-scroll-right");

    function updateScrollButtons() {
      const canScrollLeft = scrollContainer.scrollLeft > 0;
      const canScrollRight = scrollContainer.scrollLeft < scrollContainer.scrollWidth - scrollContainer.clientWidth;

      scrollLeftBtn.style.display = canScrollLeft ? "block" : "none";
      scrollRightBtn.style.display = canScrollRight ? "block" : "none";
    }

    scrollContainer.addEventListener("scroll", updateScrollButtons);

    scrollLeftBtn.addEventListener("click", () => {
      scrollContainer.scrollBy({
        left: -200,
        behavior: "smooth",
      });
    });

    scrollRightBtn.addEventListener("click", () => {
      scrollContainer.scrollBy({
        left: 200,
        behavior: "smooth",
      });
    });

    updateScrollButtons();

    function formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function createHeaderCell(text) {
      const th = document.createElement("th");
      th.scope = "col";
      th.className = "sticky top-0 px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700";
      th.textContent = text;
      return th;
    }

    function createTableRow(metric, values, years) {
      const tr = document.createElement("tr");
      tr.className = "hover:bg-gray-50 dark:hover:bg-gray-700";

      const metricCell = document.createElement("td");
      metricCell.className = "sticky left-0 px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800";
      metricCell.textContent = metric.replace(/\u00a0/g, " ").replace(/\+$/, "");
      tr.appendChild(metricCell);

      years.forEach((year) => {
        const valueCell = document.createElement("td");
        valueCell.className = "px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-300";

        let value = values[year];
        if (value) {
          if (value.includes("%")) {
            valueCell.textContent = value;
          } else if (value.includes(".")) {
            valueCell.textContent = value;
          } else {
            valueCell.textContent = formatNumber(value);
          }
        } else {
          valueCell.textContent = "-";
        }

        tr.appendChild(valueCell);
      });

      return tr;
    }

    async function loadProfitLossData() {
      try {
        const response = await fetch("/api/screener/{{ symbol }}/profit-loss");
        const data = await response.json();

        if (!data || !data["profit-loss"] || !Array.isArray(data["profit-loss"]) || data["profit-loss"].length === 0) {
          throw new Error("Invalid profit and loss data structure");
        }

        const years = Object.keys(data["profit-loss"][0]).filter((key) => key !== "");

        const headerRow = document.getElementById("profit-loss-header");
        headerRow.innerHTML = "";

        const metricHeader = document.createElement("th");
        metricHeader.scope = "col";
        metricHeader.className = "sticky left-0 top-0 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700";
        metricHeader.textContent = "Metric";
        headerRow.appendChild(metricHeader);

        years.forEach((year) => {
          headerRow.appendChild(createHeaderCell(year));
        });

        const tbody = document.getElementById("profit-loss-body");
        tbody.innerHTML = "";

        data["profit-loss"].forEach((metric) => {
          const metricName = metric[""];
          if (metricName) {
            tbody.appendChild(createTableRow(metricName, metric, years));
          }
        });

        setTimeout(updateScrollButtons, 100);
      } catch (error) {
        console.error("Error loading profit and loss data:", error);

        const tbody = document.getElementById("profit-loss-body");
        tbody.innerHTML = `
          <tr>
            <td colspan="100%" class="px-6 py-4 text-center text-sm text-red-500 dark:text-red-400">
              Failed to load profit and loss data. Please try again later.
            </td>
          </tr>
        `;
      } finally {
        document.getElementById("tab-loader").classList.add("hidden");
      }
    }

    function handleTabClick(event) {
      event.preventDefault();
      const targetId = event.currentTarget.getAttribute("href").substring(1);

      document.querySelectorAll(".tab-content").forEach((content) => {
        content.classList.add("hidden");
      });

      const selectedContent = document.getElementById(`${targetId}-content`);
      if (selectedContent) {
        selectedContent.classList.remove("hidden");

        if (targetId === "profit-loss") {
          loadProfitLossData();
        }
      }
    }

    document.querySelectorAll('a[href^="#"]').forEach((link) => {
      link.addEventListener("click", handleTabClick);
    });
  });
</script>
