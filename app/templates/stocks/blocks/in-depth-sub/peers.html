<div id="peers-content" class="tab-content hidden">
  <div class="relative">
    <div class="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-100 dark:from-gray-700 to-transparent pointer-events-none z-10"></div>

    <div class="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-100 dark:from-gray-700 to-transparent pointer-events-none z-10"></div>

    <button
      id="peers-scroll-left"
      class="absolute left-0 top-1/2 -translate-y-1/2 z-20 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
    >
      <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>
    <button
      id="peers-scroll-right"
      class="absolute right-0 top-1/2 -translate-y-1/2 z-20 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
    >
      <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>

    <div class="overflow-x-auto shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="inline-block min-w-full align-middle">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" class="sticky left-0 top-0 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700">Name</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">CMP</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">P/E</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Market Cap</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Div Yield %</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Net Profit</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Profit Var %</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Sales</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Sales Var %</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ROCE %</th>
            </tr>
          </thead>
          <tbody id="peers-body" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"></tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const scrollContainer = document.querySelector(".overflow-x-auto");
    const scrollLeftBtn = document.getElementById("peers-scroll-left");
    const scrollRightBtn = document.getElementById("peers-scroll-right");

    function updateScrollButtons() {
      const canScrollLeft = scrollContainer.scrollLeft > 0;
      const canScrollRight = scrollContainer.scrollLeft < scrollContainer.scrollWidth - scrollContainer.clientWidth;

      scrollLeftBtn.style.display = canScrollLeft ? "block" : "none";
      scrollRightBtn.style.display = canScrollRight ? "block" : "none";
    }

    scrollContainer.addEventListener("scroll", updateScrollButtons);

    scrollLeftBtn.addEventListener("click", () => {
      scrollContainer.scrollBy({
        left: -200,
        behavior: "smooth",
      });
    });

    scrollRightBtn.addEventListener("click", () => {
      scrollContainer.scrollBy({
        left: 200,
        behavior: "smooth",
      });
    });

    updateScrollButtons();

    function formatMarketCap(value) {
      const num = parseFloat(value);
      if (num >= 100000) {
        return (num / 100000).toFixed(2) + "L";
      } else if (num >= 1000) {
        return (num / 1000).toFixed(2) + "K";
      }
      return value;
    }

    function formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function createTableRow(peer) {
      const tr = document.createElement("tr");
      tr.className = "hover:bg-gray-50 dark:hover:bg-gray-700";

      const nameCell = document.createElement("td");
      nameCell.className = "sticky left-0 px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800";
      nameCell.textContent = peer.name;
      tr.appendChild(nameCell);

      const cmpCell = document.createElement("td");
      cmpCell.className = "px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-300";
      cmpCell.textContent = formatNumber(peer.cmp);
      tr.appendChild(cmpCell);

      const peCell = document.createElement("td");
      peCell.className = "px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-300";
      peCell.textContent = peer.p_e || "-";
      tr.appendChild(peCell);

      const marketCapCell = document.createElement("td");
      marketCapCell.className = "px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-300";
      marketCapCell.textContent = formatMarketCap(peer.mar_cap);
      tr.appendChild(marketCapCell);

      const divYieldCell = document.createElement("td");
      divYieldCell.className = "px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-300";
      divYieldCell.textContent = peer.div_yld + "%";
      tr.appendChild(divYieldCell);

      const npCell = document.createElement("td");
      npCell.className = "px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-300";
      npCell.textContent = formatNumber(peer.np_qtr);
      tr.appendChild(npCell);

      const profitVarCell = document.createElement("td");
      profitVarCell.className = "px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-300";
      if (peer.qtr_profit_var.startsWith("-")) {
        profitVarCell.classList.add("text-red-500", "dark:text-red-400");
      } else {
        profitVarCell.classList.add("text-green-500", "dark:text-green-400");
      }
      profitVarCell.textContent = peer.qtr_profit_var + "%";
      tr.appendChild(profitVarCell);

      const salesCell = document.createElement("td");
      salesCell.className = "px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-300";
      salesCell.textContent = formatNumber(peer.sales_qtr);
      tr.appendChild(salesCell);

      const salesVarCell = document.createElement("td");
      salesVarCell.className = "px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-300";
      if (peer.qtr_sales_var.startsWith("-")) {
        salesVarCell.classList.add("text-red-500", "dark:text-red-400");
      } else {
        salesVarCell.classList.add("text-green-500", "dark:text-green-400");
      }
      salesVarCell.textContent = peer.qtr_sales_var + "%";
      tr.appendChild(salesVarCell);

      const roceCell = document.createElement("td");
      roceCell.className = "px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-300";
      roceCell.textContent = peer.roce + "%";
      tr.appendChild(roceCell);

      if (peer.name === "{{ symbol }}") {
        tr.classList.add("bg-blue-50", "dark:bg-blue-900");
      }

      return tr;
    }

    async function loadPeersData() {
      try {
        const response = await fetch("/api/screener/{{ symbol }}/peers");
        const data = await response.json();

        const tbody = document.getElementById("peers-body");
        tbody.innerHTML = "";

        data.peers.forEach((peer) => {
          tbody.appendChild(createTableRow(peer));
        });

        setTimeout(updateScrollButtons, 100);
      } catch (error) {
        console.error("Error loading peers data:", error);
      } finally {
        document.getElementById("tab-loader").classList.add("hidden");
      }
    }

    function handleTabClick(event) {
      event.preventDefault();
      const targetId = event.currentTarget.getAttribute("href").substring(1);

      document.querySelectorAll(".tab-content").forEach((content) => {
        content.classList.add("hidden");
      });

      const selectedContent = document.getElementById(`${targetId}-content`);
      if (selectedContent) {
        selectedContent.classList.remove("hidden");

        if (targetId === "peers") {
          loadPeersData();
        }
      }
    }

    document.querySelectorAll('a[href^="#"]').forEach((link) => {
      link.addEventListener("click", handleTabClick);
    });
  });
</script>
