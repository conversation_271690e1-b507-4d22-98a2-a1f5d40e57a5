<div id="balance-sheet-content" class="tab-content hidden">
  <div class="relative">
    <div class="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-100 dark:from-gray-700 to-transparent pointer-events-none z-10"></div>

    <div class="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-100 dark:from-gray-700 to-transparent pointer-events-none z-10"></div>

    <button
      id="balance-sheet-scroll-left"
      class="absolute left-0 top-1/2 -translate-y-1/2 z-20 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
    >
      <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>
    <button
      id="balance-sheet-scroll-right"
      class="absolute right-0 top-1/2 -translate-y-1/2 z-20 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
    >
      <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>

    <div class="overflow-x-auto shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="inline-block min-w-full align-middle">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr id="balance-sheet-header"></tr>
          </thead>
          <tbody id="balance-sheet-body" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"></tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const scrollContainer = document.querySelector(".overflow-x-auto");
    const scrollLeftBtn = document.getElementById("balance-sheet-scroll-left");
    const scrollRightBtn = document.getElementById("balance-sheet-scroll-right");

    function updateScrollButtons() {
      const canScrollLeft = scrollContainer.scrollLeft > 0;
      const canScrollRight = scrollContainer.scrollLeft < scrollContainer.scrollWidth - scrollContainer.clientWidth;

      scrollLeftBtn.style.display = canScrollLeft ? "block" : "none";
      scrollRightBtn.style.display = canScrollRight ? "block" : "none";
    }

    scrollContainer.addEventListener("scroll", updateScrollButtons);

    scrollLeftBtn.addEventListener("click", () => {
      scrollContainer.scrollBy({
        left: -200,
        behavior: "smooth",
      });
    });

    scrollRightBtn.addEventListener("click", () => {
      scrollContainer.scrollBy({
        left: 200,
        behavior: "smooth",
      });
    });

    updateScrollButtons();

    function formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function createHeaderCell(text) {
      const th = document.createElement("th");
      th.scope = "col";
      th.className = "sticky top-0 px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700";
      th.textContent = text;
      return th;
    }

    function createTableRow(metric, values, years) {
      const tr = document.createElement("tr");
      tr.className = "hover:bg-gray-50 dark:hover:bg-gray-700";

      const metricCell = document.createElement("td");
      metricCell.className = "sticky left-0 px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800";
      metricCell.textContent = metric.replace(/\u00a0/g, " ").replace(/\+$/, "");
      tr.appendChild(metricCell);

      years.forEach((year) => {
        const valueCell = document.createElement("td");
        valueCell.className = "px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-300";

        let value = values[year];
        if (value) {
          valueCell.textContent = formatNumber(value);
        } else {
          valueCell.textContent = "-";
        }

        tr.appendChild(valueCell);
      });

      return tr;
    }

    async function loadBalanceSheetData() {
      try {
        const response = await fetch("/api/screener/{{ symbol }}/balance-sheet");
        const data = await response.json();

        if (!data || !data["balance-sheet"] || !Array.isArray(data["balance-sheet"]) || data["balance-sheet"].length === 0) {
          throw new Error("Invalid balance sheet data structure");
        }

        const years = Object.keys(data["balance-sheet"][0]).filter((key) => key !== "");

        const headerRow = document.getElementById("balance-sheet-header");
        headerRow.innerHTML = "";

        const categoryHeader = document.createElement("th");
        categoryHeader.scope = "col";
        categoryHeader.className = "sticky left-0 top-0 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700";
        categoryHeader.textContent = "Category";
        headerRow.appendChild(categoryHeader);

        years.forEach((year) => {
          headerRow.appendChild(createHeaderCell(year));
        });

        const tbody = document.getElementById("balance-sheet-body");
        tbody.innerHTML = "";

        data["balance-sheet"].forEach((category) => {
          const categoryName = category[""];
          if (categoryName) {
            tbody.appendChild(createTableRow(categoryName, category, years));
          }
        });

        setTimeout(updateScrollButtons, 100);
      } catch (error) {
        console.error("Error loading balance sheet data:", error);

        const tbody = document.getElementById("balance-sheet-body");
        tbody.innerHTML = `
          <tr>
            <td colspan="100%" class="px-6 py-4 text-center text-sm text-red-500 dark:text-red-400">
              Failed to load balance sheet data. Please try again later.
            </td>
          </tr>
        `;
      } finally {
        document.getElementById("tab-loader").classList.add("hidden");
      }
    }

    function handleTabClick(event) {
      event.preventDefault();
      const targetId = event.currentTarget.getAttribute("href").substring(1);

      document.querySelectorAll(".tab-content").forEach((content) => {
        content.classList.add("hidden");
      });

      const selectedContent = document.getElementById(`${targetId}-content`);
      if (selectedContent) {
        selectedContent.classList.remove("hidden");

        if (targetId === "balance-sheet") {
          loadBalanceSheetData();
        }
      }
    }

    document.querySelectorAll('a[href^="#"]').forEach((link) => {
      link.addEventListener("click", handleTabClick);
    });
  });
</script>
