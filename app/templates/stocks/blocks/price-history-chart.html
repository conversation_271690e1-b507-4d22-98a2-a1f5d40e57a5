<div class="mb-6">
    <h3 class="text-lg font-semibold mb-2">Stock History</h3>
    <div class="border border-gray-200 flex gap-6 h-10 mb-4 overflow-hidden px-3 rounded-lg space-x-2 w-80">
        <button id="btn1M" onclick="filterData('1M')" class="active-btn btn">1M</button>
        <button id="btn6M" onclick="filterData('6M')" class="btn">6M</button>
        <button id="btn1Y" onclick="filterData('1Y')" class="btn">1Y</button>
        <button id="btn2Y" onclick="filterData('2Y')" class="btn">2Y</button>
        <button id="btn3Y" onclick="filterData('3Y')" class="btn">3Y</button>
        <button id="btnMAX" onclick="filterData('MAX')" class="btn">Max</button>
    </div>
    <div class="chartsContainer w-100 h-100 relative" id="chartsContainer_{{ unique_id }}">
        <!-- Chart canvas will be added dynamically here -->
    </div>
</div>

<!-- Load Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Parse history data passed from the backend (ensure valid JSON)
    let historyData = {};
    let chartInstances = []; // Store chart instances if needed

    function filterData(range) {
        const now = new Date();
        let isLow = false;
        let filteredLabels = [], filteredClose = [], filteredVolume = [], originalDates = [];

        // Update active button styles
        activeButton(range);

        // Sort keys so dates are in order (assuming ISO format)
        Object.keys(historyData).sort().forEach((timestamp) => {
            const date = new Date(timestamp);
            let label = "";
            let valid = false;

            switch (range) {
                case "1M":
                    valid = date >= new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                    label = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                    break;
                case "6M":
                    valid = date >= new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
                    // Show month name or year if January
                    label = date.toLocaleString('en-US', { month: 'short' });
                    if (label === "Jan") {
                        label = date.getFullYear();
                    }
                    break;
                case "1Y":
                    valid = date >= new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
                    label = date.toLocaleString('en-US', { month: 'short' });
                    if (label === "Jan") {
                        label = date.getFullYear();
                    }
                    break;
                case "2Y":
                    valid = date >= new Date(now.getFullYear() - 2, now.getMonth(), now.getDate());
                    label = date.toLocaleString('en-US', { month: 'short' });
                    if (label === "Jan") {
                        label = date.getFullYear();
                    }
                    break;
                case "3Y":
                    valid = date >= new Date(now.getFullYear() - 3, now.getMonth(), now.getDate());
                    label = date.toLocaleString('en-US', { month: 'short' });
                    if (label === "Jan") {
                        label = date.getFullYear();
                    }
                    break;
                case "MAX":
                    valid = true;
                    // Use year from timestamp if in format YYYY-MM-DD
                    label = timestamp.split("-")[0];
                    break;
            }

            if (valid) {
                filteredLabels.push(label);
                originalDates.push(date.toISOString());
                filteredClose.push(historyData[timestamp].Close);
                filteredVolume.push(historyData[timestamp].Volume);
            }
        });

        // Determine color based on price movement (if last price is lower than first)
        isLow = filteredClose[filteredClose.length - 1] < filteredClose[0];

        updateChart(filteredLabels, filteredClose, filteredVolume, originalDates, isLow);
    }

    function updateChart(labels, closeData, volumeData, originalDates, isLow) {
        // Clear previous chart by emptying the container
        const chartContainer = document.getElementById("chartsContainer_{{ unique_id }}");
        chartContainer.innerHTML = '';

        // Create a new canvas element
        const canvas = document.createElement("canvas");
        const chartId = "stockChart_" + Date.now();
        canvas.id = chartId;
        chartContainer.appendChild(canvas);

        const ctx = canvas.getContext("2d");
        // Create the Chart.js instance
        const chartInstance = new Chart(ctx, {
            type: "line",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "Close Price",
                        data: closeData,
                        borderColor: isLow ? "#E15858" : "#7FC148",
                        backgroundColor: "rgba(0,0,0,0)",
                        fill: false,
                        borderWidth: 2,
                        tension: 0.4,
                        pointStyle: 'circle',
                        radius: 2,
                        yAxisID: 'y1'
                    },
                    {
                        label: "Volume",
                        data: volumeData,
                        backgroundColor: "rgba(233,233,233,0.5)",
                        type: "bar",
                        borderWidth: 1,
                        yAxisID: 'y2',
                        barPercentage: 0.8,
                        categoryPercentage: 0.5
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: { display: true, text: "Time Period" },
                        grid: { display: false },
                        ticks: {
                            autoSkip: true,
                            maxTicksLimit: 10
                        }
                    },
                    y1: {
                        title: { display: true, text: "Close" },
                        position: 'left',
                        min: Math.min(...closeData) * 0.97,
                        max: Math.max(...closeData) * 1.03,
                        grid: { display: true }
                    },
                    y2: {
                        title: { display: true, text: "Volume" },
                        position: 'right',
                        grid: { display: false },
                        min: 0,
                        max: Math.max(...volumeData) * 1.1,
                        ticks: {
                            stepSize: Math.max(...volumeData) / 10,
                            callback: function (value) {
                                return (value / 100000).toFixed(1) + 'L';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        labels: {
                            usePointStyle: true,
                            boxWidth: 10,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        enabled: true,
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            title: function (tooltipItems) {
                                let dateIndex = tooltipItems[0].dataIndex;
                                return new Date(originalDates[dateIndex]).toLocaleDateString('en-US', {
                                    year: 'numeric', month: 'short', day: 'numeric'
                                });
                            },
                            label: function (context) {
                                if (context.dataset.label === "Close Price") {
                                    return "Close: " + context.parsed.y.toFixed(2);
                                } else if (context.dataset.label === "Volume") {
                                    return "Volume: " + context.parsed.y;
                                }
                            }
                        }
                    }
                },
                hover: {
                    mode: 'index',
                    intersect: false
                },
                interaction: {
                    mode: 'index',
                    intersect: false
                }
            }
        });

        chartInstances.push(chartInstance);
    }

    // Update button styles on click
    function activeButton(btn) {
        document.querySelectorAll('.btn').forEach(button => {
            button.classList.remove('active-btn', 'text-blue-600', 'border-blue-600');
            button.classList.add('text-gray-500', 'border-transparent');
        });

        const activeBtn = document.getElementById(`btn${btn}`);
        if (activeBtn) {
            activeBtn.classList.add('active-btn', 'text-blue-600', 'border-blue-600');
            activeBtn.classList.remove('text-gray-500', 'border-transparent');
        }
    }
</script>

<style>
    canvas {
        width: 100% !important;
        height: 100% !important;
        background-color: transparent !important;
    }

    .active-btn {
        border-bottom: 2px solid #3B82F6;
        /* blue-500 */
    }
</style>