<div class="flex items-center gap-1">
    <input type="checkbox" id="{{ id|default('use-bid-ask') }}" class="h-4 w-4 cursor-pointer" {% if checked %}checked{%
        endif %} />
    <label for="{{ id|default('use-bid-ask') }}" class="text-sm font-medium cursor-pointer">
        {{ label|default('Use Bid/Ask instead of LTP') }}
    </label>
</div>

<script>
    (function () {
        // Immediately invoked function to avoid polluting global scope
        const checkboxId = "{{ id|default('use-bid-ask') }}";
        const checkbox = document.getElementById(checkboxId);

        if (!checkbox) return;

        // Initialize from localStorage or use default based on "checked" attribute
        const storedPreference = localStorage.getItem('useLTPForPrices');

        if (storedPreference === null) {
            // First time - initialize based on checked attribute
            window.useLTPForPrices = {% if checked %} false{% else %} true{% endif %};
        // Store the initial value
        localStorage.setItem('useLTPForPrices', window.useLTPForPrices);
    } else {
        // Use stored preference
        window.useLTPForPrices = storedPreference === 'true';
    }

    // Set checkbox according to the preference
    checkbox.checked = !window.useLTPForPrices;

    // Add change event listener to update localStorage when checkbox changes
    checkbox.addEventListener('change', function () {
        window.useLTPForPrices = !this.checked;
        localStorage.setItem('useLTPForPrices', window.useLTPForPrices);

        // Dispatch event when checkbox is changed by user
        document.dispatchEvent(new CustomEvent('ltp-preference-changed', {
            detail: {
                source: checkboxId,
                useLTPForPrices: window.useLTPForPrices
            }
        }));
    });

    // Listen for changes to the preference from other sources
    document.addEventListener('ltp-preference-changed', function (event) {
        // Only listen to events from other sources
        if (event.detail.source === checkboxId) return;

        // Update checkbox state without emitting a new event
        const isCurrentlyChecked = checkbox.checked;
        const shouldBeChecked = !event.detail.useLTPForPrices;

        if (isCurrentlyChecked !== shouldBeChecked) {
            // Temporarily remove event listener to avoid creating a loop
            const originalListener = checkbox.onchange;
            checkbox.onchange = null;

            // Update checkbox state
            checkbox.checked = shouldBeChecked;

            // Update localStorage
            localStorage.setItem('useLTPForPrices', event.detail.useLTPForPrices);

            // Restore event listener
            setTimeout(() => {
                checkbox.onchange = originalListener;
            }, 0);
        }
    });
    }) ();
</script>