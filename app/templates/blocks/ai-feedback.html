<!-- AI Feedback <PERSON> -->
<div id="aiFeedbackModal"
    class="modal fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-[100]">
    <div
        class="bg-white dark:bg-neutral-800 rounded-lg md:w-[60rem] w-full relative max-h-[calc(100vh_-_10rem)] flex flex-col overflow-hidden">
        <!-- <PERSON><PERSON> Header with Close Button - Fixed position -->
        <div class="p-4 border-b border-gray-200 dark:border-neutral-700 flex justify-between items-center relative">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">AI Feedback Analysis</h3>
            <button onclick="closeAIModal()" class="text-gray-600 hover:text-gray-900 text-2xl font-bold">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-x">
                    <path d="M18 6 6 18" />
                    <path d="m6 6 12 12" />
                </svg>
            </button>
        </div>

        <!-- Modal Content - Scrollable part -->
        <div style="background-image: radial-gradient(circle at 100% 0%, #f1e9ff, #f2feff, #f2f2f2, #f8f8f8, #f3f3f3, #fcffe969, #e8fdff);"
            class="p-6 overflow-auto custom-scroll flex-1 rounded-b-lg min-h-[400px]">
            <div id="aiFeedbackContent">
                <!-- Spinner Animation -->
                <div id="aiSpinner" class="flex items-center justify-center space-x-2 h-full min-h-[300px]">
                    <div class="w-8 h-8 border-4 border-blue-500 border-dashed rounded-full animate-spin"></div>
                    <p class="text-lg font-medium text-gray-700 dark:text-gray-300">Loading AI Feedback...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Analysis config object to store settings for different types of analysis
    const analysisConfigs = {
        // Portfolio analysis configuration
        portfolio_holdings: {
            endpoint: 'https://iam.theaibull.com/v1/wg7ttpouv7/ai/prompt/67d3393b52e3158b0157e3cf',
            analysisKey: 'portfolio_analysis',
            renderFunction: renderPortfolioHoldingsAnalysisUI,
            loadingText: 'Analyzing your portfolio holdings...'
        },
        portfolio_positions: {
            endpoint: 'https://iam.theaibull.com/v1/wg7ttpouv7/ai/prompt/6808d0716d32c2eeafdb0a04',
            analysisKey: 'portfolio_positions_analysis',
            renderFunction: renderPortfolioPositionsAnalysisUI,
            loadingText: 'Analyzing your portfolio positions...'
        },
        portfolio_fno_positions: {
            endpoint: 'https://iam.theaibull.com/v1/wg7ttpouv7/ai/prompt/682ae1d8676f747dde6f6155',
            analysisKey: 'fo_analysis',
            renderFunction: renderFnoPositionsAnalysisUI,
            loadingText: 'Analyzing your portfolio positions...'
        },
        // Stock analysis configuration
        stock: {
            endpoint: 'https://iam.theaibull.com/v1/wg7ttpouv7/ai/prompt/680a506977302a2956d5ca9b',
            analysisKey: 'stock_analysis',
            renderFunction: renderStockAnalysisUI,
            loadingText: 'Analyzing stock data...'
        },
        // Options strategy analysis configuration
        options: {
            endpoint: 'https://iam.theaibull.com/v1/wg7ttpouv7/ai/prompt/680a558077302a2956d5cab7',
            analysisKey: 'options_analysis',
            renderFunction: renderOptionsStrategyUI,
            loadingText: 'Analyzing options strategy...'
        },
        // Option chain analysis configuration
        optionChain: {
            endpoint: 'https://iam.theaibull.com/v1/wg7ttpouv7/ai/prompt/680a581457a239c0a859a472',
            analysisKey: 'option_chain_analysis',
            renderFunction: renderOptionChainAnalysisUI,
            loadingText: 'Analyzing option chain data...'
        },
        // Mutual fund analysis configuration
        fund: {
            endpoint: 'https://iam.theaibull.com/v1/wg7ttpouv7/ai/prompt/6808779a832d9ebab4db982f',
            analysisKey: 'fund_analysis',
            renderFunction: renderFundAnalysisUI,
            loadingText: 'Analyzing mutual fund...'
        },
        // Compare mutual fund analysis configuration
        compare_fund: {
            endpoint: 'https://iam.theaibull.com/v1/wg7ttpouv7/ai/prompt/6809fe9477302a2956d5c84d',
            analysisKey: 'fund_comparison',
            renderFunction: renderCompareFundAnalysisUI,
            loadingText: 'Comparing mutual funds...'
        },
        // Compare stock analysis configuration
        compare_stock: {
            endpoint: 'https://iam.theaibull.com/v1/wg7ttpouv7/ai/prompt/680b3a8177302a2956d5cc48',
            analysisKey: 'stock_comparison',
            renderFunction: renderCompareStockAnalysisUI,
            loadingText: 'Comparing stocks...'
        },
        // Add more analysis types as needed
    };

    // AI Feedback functionality
    async function getAIFeedback(payload, options = {}) {
        if (!isLoggedIn()) {
            showModal("Login or sign up to get AI feedback!");
            return;
        }

        if (!options.analysisType || !analysisConfigs[options.analysisType]) {
            showAlert("Could not process your request. Please try again later.");
            return;
        }

        // If analysis type is provided, load settings from config
        const settings = { ...analysisConfigs[options.analysisType], ...options };

        // Reset content to spinner on new request
        const contentDiv = document.getElementById('aiFeedbackContent');
        contentDiv.innerHTML = `
            <div id="aiSpinner" class="flex items-center justify-center space-x-2 h-full min-h-[300px]">
                <div class="w-8 h-8 border-4 border-blue-500 border-dashed rounded-full animate-spin"></div>
                <p class="text-lg font-medium text-gray-700 dark:text-gray-300">${settings.loadingText || 'Loading AI Feedback...'}</p>
            </div>
        `;

        // Update loading text if custom text is provided
        const loadingEl = document.querySelector('#aiSpinner p');
        if (loadingEl) loadingEl.textContent = settings.loadingText;

        const modal = document.getElementById('aiFeedbackModal');
        modal.classList.remove('hidden');

        try {
            const response = await fetch(settings.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${getCookie("access_token")}`,
                },
                body: JSON.stringify({
                    input_dict: payload
                })
            });

            const data = await response.json();

            // Handle nested response structure for different analysis types
            let analysis;
            if (settings.analysisType === 'options') {
                analysis = data[settings.analysisKey] || data;
            } else if (settings.analysisType === 'optionChain') {
                analysis = data[settings.analysisKey] || data;
            } else {
                analysis = data[settings.analysisKey];
            }

            // Use the provided render function or default to portfolio analysis
            settings.renderFunction(analysis, contentDiv);

        } catch (error) {
            console.error('Error fetching AI Feedback:', error);
            contentDiv.innerHTML = '<p class="text-center text-red-600">Failed to retrieve AI feedback. Please try again later.</p>';
        }
    }

    function renderPortfolioHoldingsAnalysisUI(analysis, contentDiv = null) {
        // If contentDiv not provided, use the default one
        if (!contentDiv) contentDiv = document.getElementById('aiFeedbackContent');

        let html = `<div class="space-y-6">`;
        html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Overall Assessment</h4><p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.overall_assessment}</p></div>`;
        html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Diversification Assessment</h4><p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.diversification_assessment}</p></div>`;
        html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Risk Assessment</h4><p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.risk_assessment}</p></div>`;

        if (analysis.top_holdings && analysis.top_holdings.length) {
            html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Top Holdings</h4><ul class="dark:text-gray-300 list-disc pl-5 space-y-2 text-gray-700 text-sm marker:text-yellow-400">`;
            analysis.top_holdings.forEach(item => {
                html += `<li><strong>${item.symbol}</strong> - ${item.company_name} (${item.weight}%)</li>`;
            });
            html += `</ul></div>`;
        }

        if (analysis.sector_allocation) {
            html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Sector Allocation</h4><ul class="list-disc space-y-2 marker:text-blue-400 pl-5 text-gray-700 dark:text-gray-300 text-sm">`;
            for (const sector in analysis.sector_allocation) {
                html += `<li><strong>${sector}</strong>: ${analysis.sector_allocation[sector]}</li>`;
            }
            html += `</ul></div>`;
        }

        if (analysis.holding_analysis && analysis.holding_analysis.length) {
            html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Holding Analysis</h4>`;
            analysis.holding_analysis.forEach(item => {
                html += `<div class="p-3 rounded-md mb-3 text-sm" style="background-image:linear-gradient(45deg, #f9f6f1, #f2feff);">
                            <p class="mb-1"><span class="font-medium">${item.symbol}</span> (${item.company_name})</p>
                            <p class="bg-yellow-100 font-medium gap-2 inline-flex mb-2 px-1 py-0.5 text-black"><span class="italic">Suggestion:</span> ${item.suggestion}</p>
                            <p class="mb-1 text-gray-700"><strong class="text-green-600">Pros:</strong> ${item.pros ? item.pros.join(', ') : ""}</p>
                            <p class="mb-1 text-gray-700"><strong class="text-red-500">Cons:</strong> ${item.cons ? item.cons.join(', ') : ""}</p>
                        </div>`;
            });
            html += `</div>`;
        }

        if (analysis.overall_suggestions && analysis.overall_suggestions.length) {
            html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Overall Suggestions</h4><ul class="list-disc pl-5 text-gray-700 space-y-2 dark:text-gray-300 marker:text-green-400 text-sm">`;
            analysis.overall_suggestions.forEach(suggestion => {
                html += `<li>${suggestion}</li>`;
            });
            html += `</ul></div>`;
        }
        html += `</div>`;

        contentDiv.innerHTML = html;
    }

    function renderPortfolioPositionsAnalysisUI(analysis, contentDiv = null) {
        // If contentDiv not provided, use the default one
        if (!contentDiv) contentDiv = document.getElementById('aiFeedbackContent');

        let html = `<div class="space-y-6">`;
        html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Overall Assessment</h4><p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.overall_assessment}</p></div>`;
        html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Risk Assessment</h4><p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.risk_assessment}</p></div>`;
        html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Strategy Assessment</h4><p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.strategy_assessment}</p></div>`;

        if (analysis.top_positions && analysis.top_positions.length) {
            html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Top Positions</h4><ul class="dark:text-gray-300 list-disc pl-5 space-y-2 text-gray-700 text-sm marker:text-yellow-400">`;
            analysis.top_positions.forEach(item => {
                html += `<li><strong>${item.symbol}</strong> - ${item.strategy_type} (${item.pl_percentage}% P&L)</li>`;
            });
            html += `</ul></div>`;
        }

        if (analysis.instruments_allocation) {
            html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Instruments Allocation</h4><ul class="list-disc space-y-2 marker:text-blue-400 pl-5 text-gray-700 dark:text-gray-300 text-sm">`;
            for (const instrument in analysis.instruments_allocation) {
                html += `<li><strong>${instrument}</strong>: ${analysis.instruments_allocation[instrument]}</li>`;
            }
            html += `</ul></div>`;
        }

        if (analysis.positions_analysis && analysis.positions_analysis.length) {
            html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Position Analysis</h4>`;
            analysis.positions_analysis.forEach(item => {
                const colorClass = item.pl_percentage >= 0 ? 'green' : 'red';
                html += `<div class="p-3 rounded-md mb-3 text-sm" style="background-image:linear-gradient(45deg, #f9f6f1, #f2feff);">
                            <div class="flex flex-wrap justify-between items-center mb-2">
                                <p class="mb-1"><span class="font-medium">${item.symbol}</span> - ${item.strategy_type}</p>
                                <p class="text-${colorClass}-600 font-medium">${item.pl_percentage}% P&L</p>
                            </div>
                            <p class="bg-yellow-100 font-medium gap-2 inline-flex mb-2 px-1 py-0.5 text-black"><span class="italic">Action:</span> ${item.suggested_action}</p>
                            <p class="mb-1 text-gray-700"><strong class="text-green-600">Pros:</strong> ${item.pros ? item.pros.join(', ') : ""}</p>
                            <p class="mb-1 text-gray-700"><strong class="text-red-500">Cons:</strong> ${item.cons ? item.cons.join(', ') : ""}</p>
                        </div>`;
            });
            html += `</div>`;
        }

        if (analysis.overall_suggestions && analysis.overall_suggestions.length) {
            html += `<div><h4 class="font-semibold mb-1 text-lg text-neutral-900">Overall Suggestions</h4><ul class="list-disc pl-5 text-gray-700 space-y-2 dark:text-gray-300 marker:text-green-400 text-sm">`;
            analysis.overall_suggestions.forEach(suggestion => {
                html += `<li>${suggestion}</li>`;
            });
            html += `</ul></div>`;
        }
        html += `</div>`;

        contentDiv.innerHTML = html;
    }

    function renderStockAnalysisUI(analysis, contentDiv = null) {
        // If contentDiv not provided, use the default one
        if (!contentDiv) contentDiv = document.getElementById('aiFeedbackContent');

        let html = `<div class="space-y-6">`;

        // Add stock name and symbol at the top
        if (analysis.stock_name && analysis.symbol) {
            html += `<div class="text-center">
                <h2 class="text-2xl font-bold text-gray-900 mb-1">${analysis.stock_name}</h2>
                <p class="text-gray-600 font-medium">${analysis.symbol}</p>
            </div>`;
        }

        // Circular Score visualization - NEW COMPACT COMPONENT
        if (analysis.score !== undefined) {
            html += addScore(analysis.score);
        }

        // Overall assessment
        if (analysis.overall_assessment) {
            html += `<div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-blue-800">Overall Assessment</h4>
                <p class="text-gray-700 text-sm">${analysis.overall_assessment}</p>
            </div>`;
        }

        // Strength & Weaknesses
        if (analysis.strengths && analysis.strengths.length) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-green-700">Strengths</h4>
                <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-green-500 text-sm">`;
            analysis.strengths.forEach(item => {
                html += `<li>${item}</li>`;
            });
            html += `</ul></div>`;
        }

        if (analysis.weaknesses && analysis.weaknesses.length) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-red-700">Weaknesses</h4>
                <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-red-500 text-sm">`;
            analysis.weaknesses.forEach(item => {
                html += `<li>${item}</li>`;
            });
            html += `</ul></div>`;
        }

        // Financial Analysis
        if (analysis.financial_analysis) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Financial Analysis</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.financial_analysis}</p>
            </div>`;
        }

        // Industry Comparison
        if (analysis.industry_comparison) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Industry Comparison</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.industry_comparison}</p>
            </div>`;
        }

        // Investment Recommendation
        if (analysis.investment_recommendation) {
            html += `<div class="bg-yellow-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-yellow-800">Investment Recommendation</h4>
                <p class="text-gray-800 text-sm">${analysis.investment_recommendation}</p>
            </div>`;
        }

        // Risk Factors
        if (analysis.risk_factors && analysis.risk_factors.length) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Risk Factors</h4>
                <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-orange-500 text-sm">`;
            analysis.risk_factors.forEach(item => {
                html += `<li>${item}</li>`;
            });
            html += `</ul></div>`;
        }

        // Price Targets
        if (analysis.price_targets) {
            html += `<div class="bg-indigo-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-indigo-800">Price Targets</h4>
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div class="p-2 bg-white rounded shadow-sm">
                        <p class="text-xs text-gray-500 mb-1">Bearish</p>
                        <p class="font-bold text-red-600">${analysis.price_targets.bearish}</p>
                    </div>
                    <div class="p-2 bg-white rounded shadow-sm">
                        <p class="text-xs text-gray-500 mb-1">Neutral</p>
                        <p class="font-bold text-gray-600">${analysis.price_targets.neutral}</p>
                    </div>
                    <div class="p-2 bg-white rounded shadow-sm">
                        <p class="text-xs text-gray-500 mb-1">Bullish</p>
                        <p class="font-bold text-green-600">${analysis.price_targets.bullish}</p>
                    </div>
                </div>
            </div>`;
        }

        html += `</div>`;
        contentDiv.innerHTML = html;
    }

    function renderOptionsStrategyUI(analysis, contentDiv = null) {
        // If contentDiv not provided, use the default one
        if (!contentDiv) contentDiv = document.getElementById('aiFeedbackContent');

        let html = `<div class="space-y-6">`;

        // Strategy name and type
        if (analysis.underlying_symbol) {
            // If no strategy name but we have a symbol, still show the symbol
            html += `<div class="text-center">
                ${analysis.strategy_name ? `<h2 class="text-2xl font-bold text-gray-900 mb-1">${analysis.strategy_name}</h2>` : ''}
                <p class="text-gray-600 font-medium">${analysis.underlying_symbol}</p>
            </div>`;
        }

        if (analysis.score !== undefined) {
            html += addScore(analysis.score);
        }

        // Overall assessment
        if (analysis.overall_assessment) {
            html += `<div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-blue-800">Strategy Assessment</h4>
                <p class="text-gray-700 text-sm">${analysis.overall_assessment}</p>
            </div>`;
        }

        // Strategy details
        if (analysis.strategy_details) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Strategy Details</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.strategy_details}</p>
            </div>`;
        }

        // Key metrics
        if (analysis.key_metrics) {
            html += `<div class="grid grid-cols-1 md:grid-cols-3 gap-4">`;

            // Max Profit
            if (analysis.key_metrics.max_profit) {
                html += `
                <div class="bg-green-50 p-3 rounded-lg">
                    <p class="text-sm text-gray-600 mb-1">Max Profit</p>
                    <p class="text-green-700 font-bold text-lg">${analysis.key_metrics.max_profit}</p>
                </div>`;
            }

            // Max Loss
            if (analysis.key_metrics.max_loss) {
                html += `
                <div class="bg-red-50 p-3 rounded-lg">
                    <p class="text-sm text-gray-600 mb-1">Max Loss</p>
                    <p class="text-red-700 font-bold text-lg">${analysis.key_metrics.max_loss}</p>
                </div>`;
            }

            // Risk-Reward
            if (analysis.key_metrics.risk_reward) {
                html += `
                <div class="bg-blue-50 p-3 rounded-lg">
                    <p class="text-sm text-gray-600 mb-1">Risk-Reward Ratio</p>
                    <p class="text-blue-700 font-bold text-lg">${analysis.key_metrics.risk_reward}</p>
                </div>`;
            }

            // Breakeven
            if (analysis.key_metrics.breakeven) {
                html += `
                <div class="bg-indigo-50 p-3 rounded-lg">
                    <p class="text-sm text-gray-600 mb-1">Breakeven Points</p>
                    <p class="text-indigo-700 font-bold text-lg">${analysis.key_metrics.breakeven}</p>
                </div>`;
            }

            // POP
            if (analysis.key_metrics.probability_of_profit) {
                html += `
                <div class="bg-purple-50 p-3 rounded-lg">
                    <p class="text-sm text-gray-600 mb-1">Probability of Profit</p>
                    <p class="text-purple-700 font-bold text-lg">${analysis.key_metrics.probability_of_profit}</p>
                </div>`;
            }

            // Net Premium
            if (analysis.key_metrics.net_premium) {
                html += `
                <div class="bg-yellow-50 p-3 rounded-lg">
                    <p class="text-sm text-gray-600 mb-1">Net Premium</p>
                    <p class="text-yellow-700 font-bold text-lg">${analysis.key_metrics.net_premium}</p>
                </div>`;
            }

            html += `</div>`;
        }

        // Strategy pros & cons
        if (analysis.pros && analysis.pros.length) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-green-700">Pros</h4>
                <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-green-500 text-sm">`;
            analysis.pros.forEach(item => {
                html += `<li>${item}</li>`;
            });
            html += `</ul></div>`;
        }

        if (analysis.cons && analysis.cons.length) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-red-700">Cons</h4>
                <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-red-500 text-sm">`;
            analysis.cons.forEach(item => {
                html += `<li>${item}</li>`;
            });
            html += `</ul></div>`;
        }

        // Individual legs analysis
        if (analysis.legs_analysis && analysis.legs_analysis.length) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Option Legs Analysis</h4>`;

            analysis.legs_analysis.forEach(leg => {
                let legType = leg.type;
                let legColor = 'gray';

                // Determine color based on leg type
                if (legType === 'FUT') {
                    legColor = leg.action === 'BUY' ? 'blue' : 'purple';
                } else if (legType.includes('CALL') && leg.action === 'BUY') legColor = 'green';
                else if (legType.includes('CALL') && leg.action === 'SELL') legColor = 'emerald';
                else if (legType.includes('PUT') && leg.action === 'BUY') legColor = 'red';
                else if (legType.includes('PUT') && leg.action === 'SELL') legColor = 'orange';

                html += `<div class="p-3 rounded-md mb-3 text-sm border border-${legColor}-200 bg-${legColor}-50">
                    <div class="flex justify-between mb-2">
                        <span class="font-medium text-${legColor}-700">${leg.action} ${leg.type}${leg.strike ? ` @ ${leg.strike}` : ''}</span>
                        <span class="text-${legColor}-600">Premium: ${leg.premium}</span>
                    </div>
                    <p class="text-gray-700 text-sm mb-2">${leg.analysis}</p>
                </div>`;
            });

            html += `</div>`;
        }

        // Market outlook
        if (analysis.market_outlook) {
            html += `<div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Market Outlook</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.market_outlook}</p>
            </div>`;
        }

        // Recommendations
        if (analysis.recommendations && analysis.recommendations.length) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Recommendations</h4>
                <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-blue-500 text-sm">`;
            analysis.recommendations.forEach(item => {
                html += `<li>${item}</li>`;
            });
            html += `</ul></div>`;
        }

        // Adjustment strategies
        if (analysis.adjustment_strategies) {
            html += `<div class="bg-purple-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-purple-800">Adjustment Strategies</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.adjustment_strategies}</p>
            </div>`;
        }

        html += `</div>`;
        contentDiv.innerHTML = html;
    }

    function renderFundAnalysisUI(analysis, contentDiv = null) {
        // If contentDiv not provided, use the default one
        if (!contentDiv) contentDiv = document.getElementById('aiFeedbackContent');

        let html = `<div class="space-y-6">`;

        // Mutual fund name and symbol
        if (analysis.fund_name) {
            html += `<div class="text-center">
                <h2 class="text-2xl font-bold text-gray-900 mb-1">${analysis.fund_name}</h2>
                ${analysis.symbol ? `<p class="text-gray-600 font-medium">${analysis.symbol}</p>` : ''}
            </div>`;
        }

        // Overall assessment
        if (analysis.overall_assessment) {
            html += `<div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-blue-800">Overall Assessment</h4>
                <p class="text-gray-700 text-sm">${analysis.overall_assessment}</p>
            </div>`;
        }

        // Investment strategy
        if (analysis.investment_strategy) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Investment Strategy</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.investment_strategy}</p>
            </div>`;
        }

        // Performance metrics
        if (analysis.performance_metrics) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Performance Analysis</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.performance_metrics}</p>
            </div>`;
        }

        // Risk analysis
        if (analysis.risk_analysis) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Risk Analysis</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.risk_analysis}</p>
            </div>`;
        }

        // Expense evaluation
        if (analysis.expense_evaluation) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Expense Analysis</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.expense_evaluation}</p>
            </div>`;
        }

        // Portfolio composition
        if (analysis.portfolio_composition) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Portfolio Composition</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.portfolio_composition}</p>
            </div>`;
        }

        // Strengths & Weaknesses in a two-column layout
        if ((analysis.strengths && analysis.strengths.length) || (analysis.weaknesses && analysis.weaknesses.length)) {
            html += `<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">`;

            // Strengths
            if (analysis.strengths && analysis.strengths.length) {
                html += `<div class="bg-green-50 p-4 rounded-lg">
                    <h4 class="font-semibold mb-2 text-lg text-green-700">Strengths</h4>
                    <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-green-500 text-sm">`;
                analysis.strengths.forEach(item => {
                    html += `<li>${item}</li>`;
                });
                html += `</ul></div>`;
            }

            // Weaknesses
            if (analysis.weaknesses && analysis.weaknesses.length) {
                html += `<div class="bg-red-50 p-4 rounded-lg">
                    <h4 class="font-semibold mb-2 text-lg text-red-700">Weaknesses</h4>
                    <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-red-500 text-sm">`;
                analysis.weaknesses.forEach(item => {
                    html += `<li>${item}</li>`;
                });
                html += `</ul></div>`;
            }

            html += `</div>`;
        }

        // Investment recommendation
        if (analysis.investment_recommendation) {
            html += `<div class="bg-yellow-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-yellow-800">Investment Recommendation</h4>
                <p class="text-gray-800 text-sm">${analysis.investment_recommendation}</p>
            </div>`;
        }

        // Suitable for
        if (analysis.suitable_for && analysis.suitable_for.length) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Suitable For</h4>
                <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-blue-500 text-sm">`;
            analysis.suitable_for.forEach(item => {
                html += `<li>${item}</li>`;
            });
            html += `</ul></div>`;
        }

        // Risk factors
        if (analysis.risk_factors && analysis.risk_factors.length) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Risk Factors</h4>
                <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-orange-500 text-sm">`;
            analysis.risk_factors.forEach(item => {
                html += `<li>${item}</li>`;
            });
            html += `</ul></div>`;
        }

        html += `</div>`;
        contentDiv.innerHTML = html;
    }

    function renderOptionChainAnalysisUI(analysis, contentDiv = null) {
        // If contentDiv not provided, use the default one
        if (!contentDiv) contentDiv = document.getElementById('aiFeedbackContent');

        let html = `<div class="space-y-6">`;

        // Symbol and expiry information
        if (analysis.symbol) {
            html += `<div class="text-center">
                <h2 class="text-2xl font-bold text-gray-900 mb-1">${analysis.symbol} Option Chain Analysis</h2>
                <p class="text-gray-600 font-medium">Expiry: ${analysis.expiry_date || 'N/A'}</p>
                <div class="flex justify-center gap-4 mt-2">
                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">Spot: ₹${analysis.spot_price || 'N/A'}</span>
                    ${analysis.future_price ? `<span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">Future: ₹${analysis.future_price}</span>` : ''}
                </div>
            </div>`;
        }

        if (analysis.score !== undefined) {
            html += addScore(analysis.score);
        }

        // Market sentiment
        if (analysis.market_sentiment) {
            const sentimentColor = analysis.market_sentiment.toLowerCase().includes('bullish') ? 'green' :
                analysis.market_sentiment.toLowerCase().includes('bearish') ? 'red' : 'yellow';

            html += `<div class="bg-${sentimentColor}-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-${sentimentColor}-800">Market Sentiment</h4>
                <p class="text-gray-700 text-sm">${analysis.market_sentiment}</p>
            </div>`;
        }

        // Overall analysis
        if (analysis.overall_analysis) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Overall Analysis</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.overall_analysis}</p>
            </div>`;
        }

        // Put-Call Ratio Analysis
        if (analysis.put_call_ratio_analysis) {
            html += `<div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-blue-800">Put-Call Ratio Analysis</h4>
                <p class="text-gray-700 text-sm">${analysis.put_call_ratio_analysis}</p>
                ${analysis.put_call_ratio ? `<p class="mt-2 font-medium">Current PCR: <span class="text-blue-700">${analysis.put_call_ratio}</span></p>` : ''}
            </div>`;
        }

        // Support and Resistance Levels
        if (analysis.support_resistance_levels && (analysis.support_resistance_levels.support || analysis.support_resistance_levels.resistance)) {
            html += `<div class="grid grid-cols-1 md:grid-cols-2 gap-4">`;

            // Support Levels
            if (analysis.support_resistance_levels.support && analysis.support_resistance_levels.support.length) {
                html += `<div class="bg-green-50 p-3 rounded-lg">
                    <h4 class="font-semibold mb-2 text-lg text-green-800">Support Levels</h4>
                    <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-green-500 text-sm">`;
                analysis.support_resistance_levels.support.forEach(level => {
                    html += `<li>₹${level}${level.analysis ? `: ${level.analysis}` : ''}</li>`;
                });
                html += `</ul></div>`;
            }

            // Resistance Levels
            if (analysis.support_resistance_levels.resistance && analysis.support_resistance_levels.resistance.length) {
                html += `<div class="bg-red-50 p-3 rounded-lg">
                    <h4 class="font-semibold mb-2 text-lg text-red-800">Resistance Levels</h4>
                    <ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-red-500 text-sm">`;
                analysis.support_resistance_levels.resistance.forEach(level => {
                    html += `<li>₹${level}${level.analysis ? `: ${level.analysis}` : ''}</li>`;
                });
                html += `</ul></div>`;
            }

            html += `</div>`;
        }

        // Max Pain Analysis
        if (analysis.max_pain) {
            html += `<div class="bg-indigo-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-indigo-800">Max Pain Analysis</h4>
                <p class="text-gray-700 text-sm">${analysis.max_pain.analysis || ''}</p>
                <p class="mt-2 font-medium">Max Pain Point: <span class="text-indigo-700">₹${analysis.max_pain.value || 'N/A'}</span></p>
            </div>`;
        }

        // Implied Volatility Analysis
        if (analysis.implied_volatility_analysis) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Implied Volatility Analysis</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.implied_volatility_analysis}</p>
            </div>`;
        }

        // Notable Strikes
        if (analysis.notable_strikes && analysis.notable_strikes.length) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Notable Strike Prices</h4>`;

            analysis.notable_strikes.forEach(strike => {
                html += `<div class="p-3 rounded-md mb-3 text-sm border border-yellow-200 bg-yellow-50">
                    <div class="flex justify-between mb-2">
                        <span class="font-medium text-yellow-700">Strike: ₹${strike.price}</span>
                    </div>
                    <p class="text-gray-700 text-sm mb-2">${strike.analysis}</p>
                </div>`;
            });

            html += `</div>`;
        }

        // Trading Opportunities
        if (analysis.trading_opportunities && analysis.trading_opportunities.length) {
            html += `<div class="bg-green-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-green-800">Trading Opportunities</h4>
                <ul class="list-disc pl-5 text-gray-700 space-y-2 marker:text-green-500 text-sm">`;
            analysis.trading_opportunities.forEach(opportunity => {
                html += `<li>${opportunity}</li>`;
            });
            html += `</ul></div>`;
        }

        // Risk Factors
        if (analysis.risk_factors && analysis.risk_factors.length) {
            html += `<div class="bg-red-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-red-800">Risk Factors</h4>
                <ul class="list-disc pl-5 text-gray-700 space-y-2 marker:text-red-500 text-sm">`;
            analysis.risk_factors.forEach(risk => {
                html += `<li>${risk}</li>`;
            });
            html += `</ul></div>`;
        }

        // Recommendations
        if (analysis.recommendations) {
            html += `<div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-blue-800">Recommendations</h4>
                <p class="text-gray-700 text-sm">${analysis.recommendations}</p>
            </div>`;
        }

        html += `</div>`;
        contentDiv.innerHTML = html;
    }

    function renderCompareFundAnalysisUI(analysis, contentDiv = null) {
        // If contentDiv not provided, use the default one
        if (!contentDiv) contentDiv = document.getElementById('aiFeedbackContent');

        let html = `<div class="space-y-6">`;

        // Fund names and symbols (kept centered)
        if (analysis && analysis.funds_analyzed) {
            html += `<div class="text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Fund Comparison</h2>
            <div class="flex justify-center gap-4 flex-wrap">`;
            analysis.funds_analyzed.forEach(fund => {
                html += `<div>
                <p class="text-lg font-semibold text-gray-900">${fund.fund_name}</p>
                ${fund.symbol ? `<p class="text-gray-600 font-medium">${fund.symbol}</p>` : ''}
            </div>`;
            });
            html += `</div></div>`;
        }

        // Overall assessment (full width)
        if (analysis.overall_assessment) {
            html += `<div class="bg-blue-50 p-4 rounded-lg">
            <h4 class="font-semibold mb-2 text-lg text-blue-800">Overall Assessment</h4>
            <p class="text-gray-700 text-sm">${analysis.overall_assessment}</p>
        </div>`;
        }

        // Investment strategy (full width)
        if (analysis.investment_strategy) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Investment Strategy</h4>
            <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.investment_strategy}</p>
        </div>`;
        }

        // Performance metrics (full width)
        if (analysis.performance_metrics) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Performance Analysis</h4>
            <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.performance_metrics}</p>
        </div>`;
        }

        // Risk analysis (full width)
        if (analysis.risk_analysis) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Risk Analysis</h4>
            <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.risk_analysis}</p>
        </div>`;
        }

        // Expense evaluation (full width)
        if (analysis.expense_evaluation) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Expense Analysis</h4>
            <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.expense_evaluation}</p>
        </div>`;
        }

        // Portfolio composition (full width)
        if (analysis.portfolio_composition) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Portfolio Composition</h4>
            <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.portfolio_composition}</p>
        </div>`;
        }

        // Side-by-side Strengths section
        if (analysis.strengths && analysis.funds_analyzed) {
            html += `<div class="mb-6">
            <h4 class="font-semibold mb-4 text-lg text-neutral-900">Strengths</h4>
            <div class="grid grid-cols-1 md:grid-cols-${analysis.funds_analyzed.length} gap-6">`;
            analysis.funds_analyzed.forEach(fund => {
                html += `<div class="bg-green-50 p-4 rounded-lg border border-green-200 shadow-sm">
                <h5 class="font-semibold mb-2 text-md text-green-700">${fund.fund_name}</h5>`;
                if (analysis.strengths[fund.fund_name] && analysis.strengths[fund.fund_name].length) {
                    html += `<ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-green-500 text-sm">`;
                    analysis.strengths[fund.fund_name].forEach(item => {
                        html += `<li>${item}</li>`;
                    });
                    html += `</ul>`;
                } else {
                    html += `<p class="text-gray-700 text-sm">No strengths listed</p>`;
                }
                html += `</div>`;
            });
            html += `</div></div>`;
        }

        // Side-by-side Weaknesses section
        if (analysis.weaknesses && analysis.funds_analyzed) {
            html += `<div class="mb-6">
            <h4 class="font-semibold mb-4 text-lg text-neutral-900">Weaknesses</h4>
            <div class="grid grid-cols-1 md:grid-cols-${analysis.funds_analyzed.length} gap-6">`;
            analysis.funds_analyzed.forEach(fund => {
                html += `<div class="bg-red-50 p-4 rounded-lg border border-red-200 shadow-sm">
                <h5 class="font-semibold mb-2 text-md text-red-700">${fund.fund_name}</h5>`;
                if (analysis.weaknesses[fund.fund_name] && analysis.weaknesses[fund.fund_name].length) {
                    html += `<ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-red-500 text-sm">`;
                    analysis.weaknesses[fund.fund_name].forEach(item => {
                        html += `<li>${item}</li>`;
                    });
                    html += `</ul>`;
                } else {
                    html += `<p class="text-gray-700 text-sm">No weaknesses listed</p>`;
                }
                html += `</div>`;
            });
            html += `</div></div>`;
        }

        // Side-by-side Suitable For section
        if (analysis.suitable_for && analysis.funds_analyzed) {
            html += `<div class="mb-6">
            <h4 class="font-semibold mb-4 text-lg text-neutral-900">Suitable For</h4>
            <div class="grid grid-cols-1 md:grid-cols-${analysis.funds_analyzed.length} gap-6">`;
            analysis.funds_analyzed.forEach(fund => {
                html += `<div class="bg-blue-50 p-4 rounded-lg border border-blue-200 shadow-sm">
                <h5 class="font-semibold mb-2 text-md text-blue-700">${fund.fund_name}</h5>`;
                if (analysis.suitable_for[fund.fund_name] && analysis.suitable_for[fund.fund_name].length) {
                    html += `<ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-blue-500 text-sm">`;
                    analysis.suitable_for[fund.fund_name].forEach(item => {
                        html += `<li>${item}</li>`;
                    });
                    html += `</ul>`;
                } else {
                    html += `<p class="text-gray-700 text-sm">No suitability information listed</p>`;
                }
                html += `</div>`;
            });
            html += `</div></div>`;
        }

        // Side-by-side Risk Factors section
        if (analysis.risk_factors && analysis.funds_analyzed) {
            html += `<div class="mb-6">
            <h4 class="font-semibold mb-4 text-lg text-neutral-900">Risk Factors</h4>
            <div class="grid grid-cols-1 md:grid-cols-${analysis.funds_analyzed.length} gap-6">`;
            analysis.funds_analyzed.forEach(fund => {
                html += `<div class="bg-orange-50 p-4 rounded-lg border border-orange-200 shadow-sm">
                <h5 class="font-semibold mb-2 text-md text-orange-700">${fund.fund_name}</h5>`;
                if (analysis.risk_factors[fund.fund_name] && analysis.risk_factors[fund.fund_name].length) {
                    html += `<ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-orange-500 text-sm">`;
                    analysis.risk_factors[fund.fund_name].forEach(item => {
                        html += `<li>${item}</li>`;
                    });
                    html += `</ul>`;
                } else {
                    html += `<p class="text-gray-700 text-sm">No risk factors listed</p>`;
                }
                html += `</div>`;
            });
            html += `</div></div>`;
        }

        // Investment recommendation (full width)
        if (analysis.investment_recommendation) {
            html += `<div class="bg-yellow-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-yellow-800">Investment Recommendation</h4>
                <p class="text-gray-800 text-sm">${analysis.investment_recommendation}</p>
            </div>`;
        }

        html += `</div>`;
        contentDiv.innerHTML = html;
    }

    function renderCompareStockAnalysisUI(analysis, contentDiv = null) {
        // If contentDiv not provided, use the default one
        if (!contentDiv) contentDiv = document.getElementById('aiFeedbackContent');

        let html = `<div class="space-y-6">`;

        // Stock names and symbols (centered)
        if (analysis && analysis.stocks_analyzed) {
            html += `<div class="text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Stock Comparison</h2>
            <div class="flex justify-center gap-4 flex-wrap">`;
            analysis.stocks_analyzed.forEach(stock => {
                html += `<div class="px-6 py-3 bg-white rounded-lg border border-gray-200 shadow-sm">
                <p class="text-lg font-semibold text-gray-900">${stock.company_name}</p>
                ${stock.symbol ? `<p class="text-gray-600 font-medium">${stock.symbol}</p>` : ''}
            </div>`;
            });
            html += `</div></div>`;
        }

        // Overall assessment (full width)
        if (analysis.overall_assessment) {
            html += `<div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-blue-800">Overall Assessment</h4>
                <p class="text-gray-700 text-sm">${analysis.overall_assessment}</p>
            </div>`;
        }

        // Company overviews (full width)
        if (analysis.company_overviews) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Company Overviews</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.company_overviews}</p>
            </div>`;
        }

        // Financial health (full width)
        if (analysis.financial_health) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Financial Health</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.financial_health}</p>
            </div>`;
        }

        // Valuation analysis (full width)
        if (analysis.valuation_analysis) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Valuation Analysis</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.valuation_analysis}</p>
            </div>`;
        }

        // Growth prospects (full width)
        if (analysis.growth_prospects) {
            html += `<div>
                <h4 class="font-semibold mb-2 text-lg text-neutral-900">Growth Prospects</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.growth_prospects}</p>
            </div>`;
        }

        // Dividend analysis (full width)
        if (analysis.dividend_analysis) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Dividend Analysis</h4>
            <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.dividend_analysis}</p>
        </div>`;
        }

        // Technical analysis (full width)
        if (analysis.technical_analysis) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Technical Analysis</h4>
            <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.technical_analysis}</p>
        </div>`;
        }

        // Side-by-side Strengths section
        if (analysis.strengths && analysis.stocks_analyzed) {
            html += `<div class="mb-6">
            <h4 class="font-semibold mb-4 text-lg text-neutral-900">Strengths</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">`;
            analysis.stocks_analyzed.forEach(stock => {
                html += `<div class="bg-green-50 p-4 rounded-lg border border-green-200 shadow-sm">
                <h5 class="font-semibold mb-2 text-md text-green-700">${stock.company_name}</h5>`;
                if (analysis.strengths[stock.symbol] && analysis.strengths[stock.symbol].length) {
                    html += `<ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-green-500 text-sm">`;
                    analysis.strengths[stock.symbol].forEach(item => {
                        html += `<li>${item}</li>`;
                    });
                    html += `</ul>`;
                } else {
                    html += `<p class="text-gray-700 text-sm">No strengths listed</p>`;
                }
                html += `</div>`;
            });
            html += `</div></div>`;
        }

        // Side-by-side Weaknesses section
        if (analysis.weaknesses && analysis.stocks_analyzed) {
            html += `<div class="mb-6">
            <h4 class="font-semibold mb-4 text-lg text-neutral-900">Weaknesses</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">`;
            analysis.stocks_analyzed.forEach(stock => {
                html += `<div class="bg-red-50 p-4 rounded-lg border border-red-200 shadow-sm">
                <h5 class="font-semibold mb-2 text-md text-red-700">${stock.company_name}</h5>`;
                if (analysis.weaknesses[stock.symbol] && analysis.weaknesses[stock.symbol].length) {
                    html += `<ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-red-500 text-sm">`;
                    analysis.weaknesses[stock.symbol].forEach(item => {
                        html += `<li>${item}</li>`;
                    });
                    html += `</ul>`;
                } else {
                    html += `<p class="text-gray-700 text-sm">No weaknesses listed</p>`;
                }
                html += `</div>`;
            });
            html += `</div></div>`;
        }

        // Side-by-side Price Targets section
        if (analysis.price_targets && analysis.stocks_analyzed) {
            html += `<div class="mb-6">
            <h4 class="font-semibold mb-4 text-lg text-neutral-900">Price Targets</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">`;
            analysis.stocks_analyzed.forEach(stock => {
                html += `<div class="bg-indigo-50 p-4 rounded-lg border border-indigo-200 shadow-sm">
                <h5 class="font-semibold mb-2 text-md text-indigo-700">${stock.company_name}</h5>`;
                if (analysis.price_targets[stock.symbol]) {
                    html += `<div class="grid grid-cols-3 gap-4 text-center">
                    <div class="p-2 bg-white rounded shadow-sm">
                        <p class="text-xs text-gray-500 mb-1">Bearish</p>
                        <p class="font-bold text-red-600">₹${analysis.price_targets[stock.symbol].bearish}</p>
                    </div>
                    <div class="p-2 bg-white rounded shadow-sm">
                        <p class="text-xs text-gray-500 mb-1">Neutral</p>
                        <p class="font-bold text-gray-600">₹${analysis.price_targets[stock.symbol].neutral}</p>
                    </div>
                    <div class="p-2 bg-white rounded shadow-sm">
                        <p class="text-xs text-gray-500 mb-1">Bullish</p>
                        <p class="font-bold text-green-600">₹${analysis.price_targets[stock.symbol].bullish}</p>
                    </div>
                </div>`;
                } else {
                    html += `<p class="text-gray-700 text-sm">No price targets available</p>`;
                }
                html += `</div>`;
            });
            html += `</div></div>`;
        }

        // Side-by-side Risk Factors section
        if (analysis.risk_factors && analysis.stocks_analyzed) {
            html += `<div class="mb-6">
            <h4 class="font-semibold mb-4 text-lg text-neutral-900">Risk Factors</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">`;
            analysis.stocks_analyzed.forEach(stock => {
                html += `<div class="bg-orange-50 p-4 rounded-lg border border-orange-200 shadow-sm">
                <h5 class="font-semibold mb-2 text-md text-orange-700">${stock.company_name}</h5>`;
                if (analysis.risk_factors[stock.symbol] && analysis.risk_factors[stock.symbol].length) {
                    html += `<ul class="list-disc pl-5 text-gray-700 space-y-1 marker:text-orange-500 text-sm">`;
                    analysis.risk_factors[stock.symbol].forEach(item => {
                        html += `<li>${item}</li>`;
                    });
                    html += `</ul>`;
                } else {
                    html += `<p class="text-gray-700 text-sm">No risk factors listed</p>`;
                }
                html += `</div>`;
            });
            html += `</div></div>`;
        }

        // Ranking section
        if (analysis.ranking && analysis.ranking.length) {
            html += `<div class="bg-blue-50 p-4 rounded-lg">
            <h4 class="font-semibold mb-2 text-lg text-blue-800">Ranking</h4>
            <ul class="list-disc pl-5 text-gray-700 space-y-2 marker:text-blue-500 text-sm">`;
            analysis.ranking.forEach(item => {
                const stock = analysis.stocks_analyzed.find(s => s.symbol === item.symbol);
                html += `<li><strong>${stock.company_name} (${item.symbol})</strong> - Rank ${item.rank}: ${item.rationale}</li>`;
            });
            html += `</ul></div>`;
        }

        // Investment recommendation (full width)
        if (analysis.investment_recommendation) {
            html += `<div class="bg-yellow-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-yellow-800">Investment Recommendation</h4>
                <p class="text-gray-800 text-sm">${analysis.investment_recommendation}</p>
            </div>`;
        }

        // Summary (full width)
        if (analysis.summary) {
            html += `<div class="bg-green-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2 text-lg text-green-800">Summary</h4>
                <p class="text-gray-700 text-sm">${analysis.summary}</p>
            </div>`;
        }

        html += `</div>`;
        contentDiv.innerHTML = html;
    }

    function renderFnoPositionsAnalysisUI(analysis, contentDiv = null) {
        // If contentDiv not provided, use the default one
        if (!contentDiv) contentDiv = document.getElementById('aiFeedbackContent');

        let html = `<div class="space-y-6">`;

        // Overall Assessment
        if (analysis.overall_assessment) {
            html += `<div class="bg-blue-50 p-4 rounded-lg">
            <h4 class="font-semibold mb-2 text-lg text-blue-800">Overall Assessment</h4>
            <p class="text-gray-700 text-sm">${analysis.overall_assessment}</p>
        </div>`;
        }

        // Risk Assessment
        if (analysis.risk_assessment) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Risk Assessment</h4>
            <p class="text-gray-700 dark:text-gray-300 text-sm">${analysis.risk_assessment}</p>
        </div>`;
        }

        // Top Positions
        if (analysis.top_positions && analysis.top_positions.length) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Top Positions</h4>
            <ul class="dark:text-gray-300 list-disc pl-5 space-y-2 text-gray-700 text-sm marker:text-yellow-400">`;
            analysis.top_positions.forEach(item => {
                html += `<li><strong>${item.symbol}</strong> - ${item.instrument} (${item.weight * 100}% Weight)</li>`;
            });
            html += `</ul></div>`;
        }

        // Strategy Allocation
        if (analysis.strategy_allocation) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Strategy Allocation</h4>
            <ul class="list-disc space-y-2 marker:text-blue-400 pl-5 text-gray-700 dark:text-gray-300 text-sm">`;
            for (const strategy in analysis.strategy_allocation) {
                html += `<li><strong>${strategy.replace('_', ' ')}</strong>: ${analysis.strategy_allocation[strategy]}</li>`;
            };
            html += `</ul></div>`;
        }

        // Position Analysis
        if (analysis.position_analysis && analysis.position_analysis.length) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Position Analysis</h4>`;
            analysis.position_analysis.forEach(item => {
                let positionColor = item.type === 'CE' ? 'green' : 'red';
                html += `<div class="p-3 rounded-md mb-3 text-sm border border-${positionColor}-200 bg-${positionColor}-50">
                <div class="flex flex-wrap justify-between items-center mb-2">
                    <p class="mb-1"><span class="font-medium">${item.symbol} ${item.type}</span> - Strike: ${item.strike}, Expiry: ${item.expiry}</p>
                    <p class="bg-yellow-100 font-medium gap-2 inline-flex px-1 py-0.5 text-black"><span class="italic">Suggestion:</span> ${item.suggestion}</p>
                </div>
                <p class="mb-1 text-gray-700"><strong class="text-green-600">Pros:</strong> ${item.pros ? item.pros.join(', ') : 'None'}</p>
                <p class="mb-1 text-gray-700"><strong class="text-red-500">Cons:</strong> ${item.cons ? item.cons.join(', ') : 'None'}</p>
            </div>`;
            });
            html += `</div>`;
        }

        // Overall Suggestions
        if (analysis.overall_suggestions && analysis.overall_suggestions.length) {
            html += `<div>
            <h4 class="font-semibold mb-2 text-lg text-neutral-900">Overall Suggestions</h4>
            <ul class="list-disc pl-5 text-gray-700 space-y-2 dark:text-gray-300 marker:text-green-400 text-sm">`;
            analysis.overall_suggestions.forEach(suggestion => {
                html += `<li>${suggestion}</li>`;
            });
            html += `</ul></div>`;
        }

        html += `</div>`;
        contentDiv.innerHTML = html;
    }

    function closeAIModal() {
        document.getElementById('aiFeedbackModal').classList.add('hidden');
    }

    function addScore(score) {
        // Ensure score is within range 1-10
        const normalizedScore = Math.min(Math.max(parseInt(score), 1), 10);

        // Determine color based on score
        let scoreColor = "#ef4444"; // red-500
        if (normalizedScore >= 8) scoreColor = "#22c55e"; // green-500
        else if (normalizedScore >= 5) scoreColor = "#facc15"; // yellow-500

        // Calculate stroke-dasharray values for circle
        const circumference = 2 * Math.PI * 20; // radius = 20
        const offset = circumference - (normalizedScore / 10) * circumference;

        return `
        <div class="relative inline-flex items-center justify-center float-right">
            <svg width="60" height="60" viewBox="0 0 60 60">
                <circle cx="30" cy="30" r="20" fill="none" stroke="#e5e7eb" stroke-width="5"></circle>
                <circle cx="30" cy="30" r="20" fill="none" stroke="${scoreColor}" stroke-width="5" 
                        stroke-dasharray="${circumference}" stroke-dashoffset="${offset}"
                        transform="rotate(-90 30 30)" stroke-linecap="round"></circle>
            </svg>
            <div class="absolute text-center">
                <span class="text-lg font-bold" style="color: ${scoreColor}">${normalizedScore}</span>
                <span class="text-xs text-gray-500">/10</span>
            </div>
        </div>`;
    }
</script>

<style>
    .animate-backgroundMove {
        animation: 2s linear infinite backgroundMove;
    }

    @keyframes backgroundMove {
        0% {
            background-position: 200%;
        }

        100% {
            background-position: 0;
        }
    }
</style>