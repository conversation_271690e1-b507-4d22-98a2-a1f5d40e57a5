<script src="https://accounts.google.com/gsi/client" async defer></script> <!-- G<PERSON> Script -->
<script>

    // Function to check if Google GSI is loaded
    function waitForGoogleGSI() {
        let googleLoaded = false;
        return new Promise((resolve) => {
            if (typeof google !== 'undefined' && google.accounts && google.accounts.id) {
                googleLoaded = true;
                resolve();
            } else {
                // Check every 100ms until Google GSI is loaded
                const checkInterval = setInterval(() => {
                    if (typeof google !== 'undefined' && google.accounts && google.accounts.id) {
                        googleLoaded = true;
                        clearInterval(checkInterval);
                        resolve();
                    }
                }, 100);

                // Timeout after 10 seconds
                setTimeout(() => {
                    clearInterval(checkInterval);
                    if (!googleLoaded) {
                        console.error('Google GSI failed to load within 10 seconds');
                    }
                    resolve();
                }, 10000);
            }
        });
    }

    const renderGoogleButton = async function (elementId) {
        // Wait for Google GSI to be loaded
        await waitForGoogleGSI();

        if (typeof google !== 'undefined' && google.accounts && google.accounts.id) {
            try {
                google.accounts.id.initialize({
                    client_id: "************-maikd6b0mof3mn8fgurqt97mkg7g4tgj.apps.googleusercontent.com", // Replace with your actual Google Client ID
                    callback: handleCredentialResponse
                });

                // Render the Google Sign-In button
                google.accounts.id.renderButton(
                    document.getElementById(elementId),
                    {
                        theme: "filled_black",   // Black background
                        size: "large",          // Large size
                        text: "continue_with",  // "Continue with Google" text
                        shape: "rectangular",   // Rectangular button shape
                        logo_alignment: "left"  // Google logo aligned to the left
                    }
                );
            } catch (error) {
                console.error('Error rendering Google button:', error);
            }
        } else {
            console.error('Google GSI is not available');
        }

        applyGoogleButtonStyles(elementId, {});
    };

    // Handle the credential response after successful login
    async function handleCredentialResponse(response) {
        // Make a fetch call to your backend endpoint with the id_token
        const res = await fetch(`${window.Mantra_AI_Server}/sso/google-one-tap`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id_token: response.credential })
        });

        if (res.ok) {
            const data = await res.json();

            // Set the access token as a cookie
            document.cookie = `access_token=${data.access_token}; path=/; max-age=${60 * 24 * 30 * 60}; SameSite=None; Secure`;

            // Handle signup-specific actions and conversion tracking
            if (data.type === "signup") {
                await userData.saveAllItemsToServer();
                if (window.dataLayer) {
                    window.dataLayer.push({
                        event: "signup",
                        contact: data.data.email,
                        otpType: data.type,
                    });
                }
            } else if (data.type === "login" && window.dataLayer) {
                window.dataLayer.push({
                    event: "login",
                    contact: data.data.email,
                    otpType: data.type,
                });
            }

            location.reload();
        } else {
            console.error('Google One Tap authentication failed', res);
        }
    }
</script>
<script>
    // Default style configuration
    const defaultStyles = {
        buttonHeight: '38px',
        fontSize: '16px',
        borderRadius: '5px',
        backgroundColor: '#202124',
        hoverBackgroundColor: '#202124',
        fontFamily: '"Bricolage Grotesque", sans-serif',
        fontWeight: '700',
        lineHeight: '1.75rem',
        logoMarginLeft: '0px'
    };

    // Apply dynamic styles efficiently
    function applyGoogleButtonStyles(elementId, styleConfig) {
        const styles = { ...defaultStyles, ...styleConfig };
        const css = `
            #${elementId} .nsm7Bb-HzV7m-LgbsSe {
                height: ${styles.buttonHeight} !important;
                font-size: ${styles.fontSize} !important;
                border-radius: ${styles.borderRadius} !important;
            }
            #${elementId} .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf {
                margin-left: ${styles.logoMarginLeft} !important;
                border-radius: 100%;
                font-family: "poppins-regular", sans-serif;
            }
            #${elementId} .S9gUrf-YoZ4jf {
                width: 100% !important;
                display: flex;
                background: ${styles.backgroundColor};
                height: 54px;
                padding-top: 8px;
                border-radius: ${styles.borderRadius};
                justify-content: center;
            }
            #${elementId} .S9gUrf-YoZ4jf:hover {
                background: ${styles.hoverBackgroundColor};
            }
            #${elementId} .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb {
                justify-content: center !important;
            }
            #${elementId} .nsm7Bb-HzV7m-LgbsSe-BPrWId {
                display: contents;
                font-family: ${styles.fontFamily};
                font-size: 1rem;
                line-height: ${styles.lineHeight};
                font-weight: ${styles.fontWeight};
            }
        `;

        // Only update if styles have changed
        const styleElement = document.getElementById('google-button-styles') || document.createElement('style');
        if (styleElement.textContent !== css) {
            styleElement.id = 'google-button-styles';
            styleElement.textContent = css;
            if (!styleElement.parentNode) document.head.appendChild(styleElement);
        }
    }
</script>