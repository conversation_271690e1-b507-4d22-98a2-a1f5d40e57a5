<!-- blocks/storage.html -->
<script>
    class UserData {
        constructor() {
            this.data = {};
            this.isLocal = false;

            {% if User_Data %}
            this.data = {{ User_Data | tojson }};
            // console.log("User is Logged in");
        {% else %}
    this.isLocal = true;
    this.data = JSON.parse(localStorage.getItem('UserData'));
    if (!this.data) {
        this.data = {};
    }
    {% endif %}
    }

    getItem(key) {
        // console.log('Getting Locally', this.isLocal, key, this.data);
        if (key in this.data) {
            return this.data[key];
        } else {
            console.log('getItem(' + key + ') - not implemented (key not known)');
            return null;
        }
    }

    async setItem(key, value) {
        console.log('Setting Locally', this.isLocal);
        if (this.isLocal) {
            this.data[key] = value;
            console.log('Setting Locally', this.data);
            localStorage.setItem('UserData', JSON.stringify(this.data));
        } else {
            this.data[key] = value;
            await this.saveItemToServer(key, value);
        }

        if (key === "social" || key === "sites") {
            console.log('Dispatching refresh')
            document.dispatchEvent(new CustomEvent('refreshData'));
        }
    }

    async saveItemToServer(key, value) {
        try {
            const response = await fetch(`${window.Mantra_AI_Server}/profile`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ key, value }),
            });
            if (response.ok) {
                const data = await response.json();
                this.data[key] = value;
            } else {
                console.error('Error updating ' + key + ':', response.statusText);
            }
        } catch (error) {
            console.error('Error updating ' + key + ':', error);
        }
    }

    async saveAllItemsToServer() {
        const keys = ["lists", "apps", "campaigns", "keywords", "templates", "social"];
        for (const key of keys) {
            if (key in this.data) {
                await this.saveItemToServer(key, this.data[key]);
            }
        }
    }
}

    // Instantiate the UserData class
    var userData = new UserData();

</script>
