<!-- Slides -->

<div id="stocksSlide"
    class="fixed inset-0 bg-[#f2efe9] z-50 p-4 shadow-lg overflow-y-auto hidden max-h-[calc(100vh_-_57px)]">
    <div class="flex justify-between items-center mb-3 pb-3 border-b">
        <h2 class="text-lg text-neutral-900 font-semibold">Stocks</h2>
        <button onclick="closeSlide('stocksSlide')" class="text-sm text-gray-400 hover:text-black">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-x">
                <path d="M18 6 6 18" />
                <path d="m6 6 12 12" />
            </svg>
        </button>
    </div>
    <!-- Menu -->
    <div>
        <ul>

            <li>
                <a href="/stocks"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/stock/live' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Screener
                            </p>
                            <p class="text-xs pt-1 text-gray-400">Customize Stock Filters for Precise Investment
                                Decisions
                                .</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>

            <li>
                <a href="/stocks/live"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/stock/live' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Live Analysis</p>
                            <p class="text-xs pt-1 text-gray-400">Access real-time stock analysis and insights.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>
            <li>
                <a href="/screener/compare"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Compare Stocks</p>
                            <p class="text-xs pt-1 text-gray-400">Compare stocks side-by-side for better decisions.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>


        </ul>
    </div>
</div>

<div id="tradingSlide"
    class="fixed inset-0 bg-[#f2efe9] z-50 p-4 shadow-lg overflow-y-auto hidden max-h-[calc(100vh_-_57px)]">
    <div class="flex justify-between items-center mb-3 pb-3 border-b">
        <h2 class="text-lg text-neutral-900 font-semibold">Trading</h2>
        <button onclick="closeSlide('tradingSlide')" class="text-sm text-gray-400 hover:text-black">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-x">
                <path d="M18 6 6 18" />
                <path d="m6 6 12 12" />
            </svg>
        </button>
    </div>

    <!-- Menu -->
    <div>
        <ul>
            <li>
                <a href="/options/spreads"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/options/spreads' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Spreads</p>
                            <p class="text-xs pt-1 text-gray-400">Explore in-depth technical indicators and chart
                                analysis.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>

            <li>
                <a href="/options/chain"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/options/chain' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Option Chain</p>
                            <p class="text-xs pt-1 text-gray-400">Access real-time stock analysis and insights through
                                option chains.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>

            <li>
                <a href="/strategies/straddle-scanner"
                    class="block rounded-md p-3 mb-4  text-[14px] bg-white font-medium text-gray-700">
                    <div class="flex items-center gap-3 justify-between">
                        <div>
                            <p>Straddle Scanner
                            </p>
                            <p class="text-xs pt-1 text-gray-400">Find the best straddle opportunities with AI-powered
                                analysis
                            </p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>

            <li>
                <a href="/strategies/spread-analyzer"
                    class="block rounded-md p-3 mb-4  text-[14px] bg-white font-medium text-gray-700">
                    <div class="flex items-center gap-3 justify-between">
                        <div>
                            <p>Spread Analyzer</p>
                            <p class="text-xs pt-1 text-gray-400">Analyze Bull/Bear spreads across multiple strikes
                            </p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>

            <li>
                <a href="/strategies/strike-scanner"
                    class="block rounded-md p-3 mb-4  text-[14px] bg-white font-medium text-gray-700">
                    <div class="flex items-center gap-3 justify-between">
                        <div>
                            <p>Strike Scanner</p>
                            <p class="text-xs pt-1 text-gray-400">AI-Powered Delta Scanner for Analyzing Covered Call
                                Opportunities
                            </p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>


        </ul>
    </div>

</div>

<div id="strategiesSlide"
    class="fixed inset-0 bg-[#f2efe9] z-50 p-4 shadow-lg overflow-y-auto hidden max-h-[calc(100vh_-_57px)]">
    <div class="flex justify-between items-center mb-3 pb-3 border-b">
        <h2 class="text-lg text-neutral-900 font-semibold">Strategies & Arbitrages</h2>
        <button onclick="closeSlide('strategiesSlide')" class="text-sm text-gray-400 hover:text-black">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-x">
                <path d="M18 6 6 18" />
                <path d="m6 6 12 12" />
            </svg>
        </button>
    </div>

    <!-- Menu -->
    <div>
        <ul>

            <li>
                <a href="/strategies/covered-call"
                    class="block rounded-md p-3 bg-white mb-4 font-medium text-[14px] text-gray-700 hover:bg-gray-50 {% if '/strategies/covered-call' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">
                        <div>
                            <p>Covered Call</p>
                            <p class="text-xs pt-1 text-gray-400">A strategy where you sell call options on stocks you
                                already own to generate additional income.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>
            <li>
                <a href="/strategies/cash-secured-put"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/strategies/cash-secured-put' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Cash Secured Put</p>
                            <p class="text-xs pt-1 text-gray-400">Selling put options while holding enough cash to buy
                                the stock if the option is exercised.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>
            <li>
                <a href="/strategies/straddle-graph"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/strategies/straddle-graph' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Expiration Day</p>
                            <p class="text-xs pt-1 text-gray-400">Discover profitable opportunities with AI-driven
                                insights.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>
            <li>
                <a href="/calculators"
                    class="block rounded-md p-3 mb-4 bg-white text-[14px] font-medium text-gray-700 hover:bg-gray-50">
                    <div class="flex items-center justify-between gap-3">
                        <div>
                            <p>Calculators</p>
                            <p class="text-xs pt-1 text-gray-400">Analyze financial scenarios with AI-powered
                                calculations.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>

            <!-- Arbitrage Options -->
            <li class="border-t pt-4 mt-2">
                <div class="text-sm font-semibold text-neutral-500 mb-3 px-1">Arbitrage Strategies</div>
            </li>
            <li>
                <a href="/strategies/futures-arbitrage"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/strategies/futures-arbitrage' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Futures Arbitrage</p>
                            <p class="text-xs pt-1 text-gray-400">Taking advantage of price differences in futures
                                contracts to generate risk-free profits.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>

            <li>
                <a href="/strategies/exchanges-arbitrage"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/strategies/exchanges-arbitrage' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Exchanges Arbitrage</p>
                            <p class="text-xs pt-1 text-gray-400">Profiting from price discrepancies of the same asset
                                on different exchanges.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>

            <li>
                <a href="/strategies/put-call-parity"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/strategies/put-call-parity' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Put Call Parity</p>
                            <p class="text-xs pt-1 text-gray-400">A pricing principle that ensures a balance between put
                                and call options for the same asset.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>

            <li>
                <a href="/strategies/box-spread-arbitrage"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/strategies/box-spread-arbitrage' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Box Spread Arbitrage</p>
                            <p class="text-xs pt-1 text-gray-400">A strategy that involves using options to create a
                                risk-free position by exploiting pricing inefficiencies.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>
        </ul>
    </div>

</div>



<div id="algoSlide"
    class="fixed inset-0 bg-[#f2efe9] z-50 p-4 shadow-lg overflow-y-auto hidden max-h-[calc(100vh_-_57px)]">
    <div class="flex justify-between items-center mb-3 pb-3 border-b">
        <h2 class="text-lg text-neutral-900 font-semibold">Algos</h2>
        <button onclick="closeSlide('algoSlide')" class="text-sm text-gray-400 hover:text-black">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-x">
                <path d="M18 6 6 18" />
                <path d="m6 6 12 12" />
            </svg>
        </button>
    </div>

    <!-- Menu -->
    <div>
        <ul>

            <li>
                <a href="/algos/options"
                    class="block rounded-md p-3 bg-white mb-4 font-medium text-[14px] text-gray-700 hover:bg-gray-50 {% if '/strategies/covered-call' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">
                        <div>
                            <p>Options</p>
                            <p class="text-xs pt-1 text-gray-400">Sell call options on stocks you own to generate
                                additional income.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>
            <li>
                <a href="/algos/backtesting"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/algos/backtesting' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Backtesting</p>
                            <p class="text-xs pt-1 text-gray-400">Analyze trading strategies based on historical market
                                data.
                            </p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>
            <li>
                <a href="/algos/scalping"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/algos/scalping' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">
                        <div>
                            <p>Algos Scalping</p>
                            <p class="text-xs pt-1 text-gray-400">Review stock trading strategies and performance using
                                past data.
                            </p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>
        </ul>
    </div>

</div>


<div id="profileSlide" class="fixed inset-0  bg-neutral-900/50 z-50 p-4 shadow-lg overflow-hidden hidden">
    <div class="bg-white fixed inset-x-0 bottom-0 rounded-t-lg pb-[60px] p-4">
        <div class="flex justify-between items-center mb-3 pb-3 border-b">
            <h2 class="text-lg text-neutral-900 font-semibold">Profile</h2>
            <button onclick="closeSlide('profileSlide')" class="text-sm text-gray-400 hover:text-black">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-x">
                    <path d="M18 6 6 18" />
                    <path d="m6 6 12 12" />
                </svg>
            </button>
        </div>

        <div class="flex space-x-2 items-center w-full">
            {% if User_Data %}
            <div class="relative group z-50  w-full">

                <div class="">
                    {% if User_Data.type == "secondary" %}
                    <!-- Display primary account details if secondary -->
                    <div
                        class="px-4 flex items-center py-2.5 hover:bg-neutral-100 dark:hover:bg-neutral-700 border-b border-neutral-200 dark:border-neutral-700">
                        <div class="flex h-[33px] items-center mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                stroke-linejoin="round" class="flex-shrink-0 lucide lucide-crown">
                                <path
                                    d="M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z" />
                                <path d="M5 21h14" />
                            </svg>
                        </div>
                        <div>
                            <div class="flex">

                                <span
                                    class="text-sm font-medium text-neutral-800 dark:text-neutral-300 block truncate max-w-[16ch]">{{User_Data.name
                                    }}</span>
                                <span
                                    class="bg-gray-100 border-gray-100 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-300 border font-medium px-1.5 py-0.5 rounded-md text-[9px] text-neutral-600 ml-1">Primary</span>
                            </div>
                            {% if User_Data.email %}
                            <span
                                class="block text-xs text-neutral-600 dark:text-neutral-400 pt-0.5 max-w-[25ch] truncate">{{
                                User_Data.email }}</span>
                            {% elif User_Data.phone %}
                            <span class="block text-xs text-neutral-600 dark:text-neutral-400 truncate pt-0.5">{{
                                User_Data.phone }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <!-- Display secondary account details -->
                    <div
                        class="px-4 py-2.5 flex items-center hover:bg-neutral-100 dark:hover:bg-neutral-700 border-b border-neutral-200 dark:border-neutral-700">
                        <div class="flex h-[33px] items-center mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-user-round flex-shrink-0">
                                <circle cx="12" cy="8" r="5" />
                                <path d="M20 21a8 8 0 0 0-16 0" />
                            </svg>
                        </div>
                        <div>
                            <div class="flex">
                                <span
                                    class="text-sm font-medium text-neutral-800 dark:text-neutral-300 block truncate max-w-[16ch]">{{
                                    User_Data.secondary_user.name }}<span
                                        class="bg-gray-100 border-gray-100 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-300 border font-medium px-1.5 py-0.5 rounded-md text-[9px] text-neutral-600 ml-1">Sub</span></span>
                            </div>
                            {% if User_Data.secondary_user.email %}
                            <span
                                class="block text-xs text-neutral-600 dark:text-neutral-400 pt-0.5 max-w-[25ch] truncate">{{
                                User_Data.secondary_user.email }}</span>
                            {% elif User_Data.secondary_user.phone %}
                            <span class="block text-xs text-neutral-600 dark:text-neutral-400 truncate pt-0.5">{{
                                User_Data.secondary_user.phone }}</span>
                            {% endif %}
                        </div>
                    </div>
                    {% else %}
                    <!-- Display primary account details if primary -->
                    <div
                        class="py-2.5 hover:bg-neutral-100 dark:hover:bg-neutral-700 cursor-pointer border-b border-neutral-200 dark:border-neutral-700">
                        <span class="block text-base font-medium text-neutral-900 dark:text-neutral-200 capitalize">{{
                            User_Data.name }}</span>
                        {% if User_Data.email %}
                        <span class="block text-sm text-neutral-600 dark:text-neutral-400 truncate pt-0.5">{{
                            User_Data.email }}</span>
                        {% elif User_Data.phone %}
                        <span class="block text-base font-medium text-neutral-900 dark:text-neutral-200 capitalize">{{
                            User_Data.phone }}</span>
                        {% endif %}
                    </div>
                    {% endif %}
                    <button onclick="showContactUsModal('support', generateDynamicMessage())"
                        class="flex gap-2 text-sm items-center border-b dark:border-neutral-700 py-4 text-neutral-900 dark:text-white hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">Contact
                        Us</button>
                    <button onclick="showFeedbackModal()"
                        class="flex gap-2 text-sm items-center border-b dark:border-neutral-700 py-4 text-neutral-900 dark:text-white hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">Feedback</button>

                    <a href="/apps"
                        class="flex gap-2 text-sm items-center border-b dark:border-neutral-700 py-4 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-apps">
                            <rect x="3" y="3" width="7" height="7" rx="1.5" />
                            <rect x="14" y="3" width="7" height="7" rx="1.5" />
                            <rect x="14" y="14" width="7" height="7" rx="1.5" />
                            <rect x="3" y="14" width="7" height="7" rx="1.5" />
                        </svg>
                        <span>Apps</span>
                    </a>
                    <a id="demoButton" href="/demo"
                        class="flex gap-2 text-sm items-center border-b dark:border-neutral-700 py-4 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-play-circle">
                            <circle cx="12" cy="12" r="10" />
                            <polygon points="10 8 16 12 10 16 10 8" />
                        </svg>
                        <span>Demo Videos</span>
                    </a>

                    <div class="flex items-center justify-between text-sm border-b dark:border-neutral-700 py-4 px-2 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full cursor-pointer min-h-[48px]"
                        onclick="toggleTickerPreference()">
                        <span class="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-bar-chart-3">
                                <path d="M3 3v18h18" />
                                <rect width="4" height="7" x="7" y="10" rx="1" />
                                <rect width="4" height="12" x="15" y="5" rx="1" />
                            </svg>
                            <span>Show Ticker</span>
                        </span>
                        <span class="relative flex items-center min-w-[40px] min-h-[24px]">
                            <input type="checkbox" id="ticker-toggle-mobile" class="sr-only">
                            <span id="ticker-toggle-bg-mobile"
                                class="block bg-gray-300 dark:bg-neutral-600 w-10 h-5 rounded-full"></span>
                            <span id="ticker-toggle-dot-mobile"
                                class="absolute left-1 top-1 bg-white w-3.5 h-3.5 rounded-full transition-transform duration-300 ease-in-out"></span>
                        </span>
                    </div>
                    <a href="#" onclick="logout()"
                        class="flex gap-2 text-sm items-center dark:border-neutral-700 py-4 text-[#f56b62] dark:text-[#ff5448] hover:text-[#ff5448] dark:hover:text-[#ff3a2e] hover:bg-neutral-100 dark:hover:bg-neutral-700">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-log-out">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                            <polyline points="16 17 21 12 16 7" />
                            <line x1="21" x2="9" y1="12" y2="12" />
                        </svg>
                        Logout
                    </a>
                </div>
            </div>
            {% else %}
            <div class="w-full">
                <button
                    class="bg-blue-50 border border-blue-500 cursor-pointer focus-visible:outline-none font-medium hover:shadow-sm lg:block lg:min-h-0 lg:my-0 lg:w-auto min-h-[46px] my-3 px-4 py-2 rounded-md shadow-sm text-blue-600 text-sm transition-colors w-full"
                    onclick="showLoginModal(true)">
                    Login
                </button>
                <button id="signupButton"
                    class="lg:w-auto w-full lg:min-h-0 border border-blue-500 dark:border-blue-700 bg-blue-500 dark:bg-blue-800 hover:bg-blue-500 dark:hover:bg-blue-600 transition-colors hover:shadow-sm font-medium text-sm text-white rounded-md px-6 trasition-all lg:py-2 border-blue-700 py-2 min-h-[46px]"
                    onclick="showLoginModal(false)">
                    Signup
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</div>


<div id="aiSlide"
    class="fixed inset-0 bg-[#f2efe9] z-50 p-4 shadow-lg overflow-y-auto hidden max-h-[calc(100vh_-_57px)]">
    <div class="flex justify-between items-center mb-3 pb-3 border-b">
        <h2 class="text-lg text-neutral-900 font-semibold">Portfolio & AI Analysis</h2>
        <button onclick="closeSlide('aiSlide')" class="text-sm text-gray-400 hover:text-black">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-x">
                <path d="M18 6 6 18" />
                <path d="m6 6 12 12" />
            </svg>
        </button>
    </div>
    <!-- Menu -->
    <div>
        <ul>

            <li>
                <a href="/portfolio"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/stock/live' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>View Portfolio
                            </p>
                            <p class="text-xs pt-1 text-gray-400">A dashboard to analyze investments, holdings,
                                performance, and risks.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>

            <li>
                <a href="/ai-agent/live"
                    class="block rounded-md p-3 mb-4  bg-white  text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/stock/live' in request.path %}bg-sky-100 text-neutral-950{% endif %}">

                    <div class="flex items-center justify-between gap-3">

                        <div>
                            <p>Live AI Agent</p>
                            <p class="text-xs pt-1 text-gray-400">An AI assistant providing real-time trading
                                recommendations based on trends.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right  text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>


        </ul>
    </div>
</div>

{% if not is_us_market %}
<div id="mutualFundsSlide"
    class="fixed inset-0 bg-[#f2efe9] z-50 p-4 shadow-lg overflow-y-auto hidden max-h-[calc(100vh_-_57px)]">
    <div class="flex justify-between items-center mb-3 pb-3 border-b">
        <h2 class="text-lg text-neutral-900 font-semibold">Mutual Funds</h2>
        <button onclick="closeSlide('mutualFundsSlide')" class="text-sm text-gray-400 hover:text-black">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-x">
                <path d="M18 6 6 18" />
                <path d="m6 6 12 12" />
            </svg>
        </button>
    </div>
    <!-- Menu -->
    <div>
        <ul>
            <li>
                <a href="/mutual-funds"
                    class="block rounded-md p-3 mb-4 bg-white text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if request.path == '/mutual-funds' %}bg-sky-100 text-neutral-950{% endif %}">
                    <div class="flex items-center justify-between gap-3">
                        <div>
                            <p>Screener</p>
                            <p class="text-xs pt-1 text-gray-400">Filter mutual funds by performance, fees, and more to
                                match your goals.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>
            <li>
                <a href="/mutual-funds/compare"
                    class="block rounded-md p-3 mb-4 bg-white text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if '/mutual-funds/compare' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                    <div class="flex items-center justify-between gap-3">
                        <div>
                            <p>Compare</p>
                            <p class="text-xs pt-1 text-gray-400">Compare mutual funds side-by-side to make informed
                                investment decisions.</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>
        </ul>
    </div>
</div>
{% endif %}

<!-- Games Slide -->
<div id="gamesSlide"
    class="fixed inset-0 bg-[#f2efe9] z-50 p-4 shadow-lg overflow-y-auto hidden max-h-[calc(100vh_-_57px)]">
    <div class="flex justify-between items-center mb-3 pb-3 border-b">
        <h2 class="text-lg text-neutral-900 font-semibold">Games</h2>
        <button onclick="closeSlide('gamesSlide')" class="text-sm text-gray-400 hover:text-black">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-x">
                <path d="M18 6 6 18" />
                <path d="m6 6 12 12" />
            </svg>
        </button>
    </div>
    <!-- Menu -->
    <div>
        <ul>
            <li>
                <a href="/quiz"
                    class="block rounded-md p-3 mb-4 bg-white text-[14px] font-medium text-gray-700 hover:bg-gray-50 {% if request.path == '/quiz' %}bg-sky-100 text-neutral-950{% endif %}">
                    <div class="flex items-center justify-between gap-3">
                        <div>
                            <p>Quiz</p>
                            <p class="text-xs pt-1 text-gray-400">Test your trading knowledge with interactive quizzes.
                            </p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-chevron-right text-gray-500 w-6 h-6 flex-shrink-0">
                            <path d="m9 18 6-6-6-6" />
                        </svg>
                    </div>
                </a>
            </li>
        </ul>
    </div>
</div>

<!-- Bottom Navigation -->
<div class="fixed bottom-0 w-full bg-white border-t border-gray-200 shadow-lg z-50 block xl:hidden ">
    <div class="flex justify-around items-center py-2">

        <!-- algos -->
        <button onclick="openSlide('algoSlide')"
            class="cursor-pointer searchButton flex flex-col items-center text-gray-600 hover:text-blue-500">
            <div class="">
                <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-brain-icon lucide-brain w-6 h-6">
                    <path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z" />
                    <path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z" />
                    <path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4" />
                    <path d="M17.599 6.5a3 3 0 0 0 .399-1.375" />
                    <path d="M6.003 5.125A3 3 0 0 0 6.401 6.5" />
                    <path d="M3.477 10.896a4 4 0 0 1 .585-.396" />
                    <path d="M19.938 10.5a4 4 0 0 1 .585.396" />
                    <path d="M6 18a4 4 0 0 1-1.967-.516" />
                    <path d="M19.967 17.484A4 4 0 0 1 18 18" />
                </svg>
            </div>
            <span class="tracking-wider text-[9px] pt-1">Algos</span>
        </button>

        <!-- Stocks -->
        <button onclick="openSlide('stocksSlide')"
            class="cursor-pointer flex flex-col items-center text-gray-600 hover:text-blue-500">
            <div class="">
                <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-chart-candlestick w-6 h-6">
                    <path d="M9 5v4"></path>
                    <rect width="4" height="6" x="7" y="9" rx="1"></rect>
                    <path d="M9 15v2"></path>
                    <path d="M17 3v2"></path>
                    <rect width="4" height="8" x="15" y="5" rx="1"></rect>
                    <path d="M17 13v3"></path>
                    <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
                </svg>
            </div>
            <span class="tracking-wider text-[9px] pt-1">Stocks</span>
        </button>

        <!-- Trading -->
        <button onclick="openSlide('tradingSlide')"
            class="flex flex-col items-center text-gray-600 hover:text-blue-500 {% if '/study' in request.path %}text-blue-500{% endif %}">
            <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-chart-spline w-6 h-6">
                <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
                <path d="M7 16c.5-2 1.5-7 4-7 2 0 2 3 4 3 2.5 0 4.5-5 5-7"></path>
            </svg>
            <span class="tracking-wider text-[9px] pt-1">Trading</span>
        </button>

        <!-- Strategies & Arbitrages -->
        <button onclick="openSlide('strategiesSlide')"
            class="cursor-pointer searchButton flex flex-col items-center text-gray-600 hover:text-blue-500">
            <div class="">
                <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-chart-no-axes-combined w-6 h-6">
                    <path d="M12 16v5"></path>
                    <path d="M16 14v7"></path>
                    <path d="M20 10v11"></path>
                    <path d="m22 3-8.646 8.646a.5.5 0 0 1-.708 0L9.354 8.354a.5.5 0 0 0-.707 0L2 15">
                    </path>
                    <path d="M4 18v3"></path>
                    <path d="M8 14v7"></path>
                </svg>
            </div>
            <span class="tracking-wider text-[9px] pt-1">Strategies & Arbitrages</span>
        </button>




        <!-- Mutual Funds -->
        {% if not is_us_market %}
        <button onclick="openSlide('mutualFundsSlide')"
            class="cursor-pointer flex flex-col items-center text-gray-600 hover:text-blue-500">
            <div class="">
                <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-bar-chart w-6 h-6">
                    <path d="M3 3v18h18" />
                    <path d="M7 16v-6" />
                    <path d="M11 13v-2" />
                    <path d="M15 16v-8" />
                    <path d="M19 16v-4" />
                </svg>
            </div>
            <span class="tracking-wider text-[9px] pt-1">Mutual Funds</span>
        </button>
        {% endif %}

        <!-- Games -->
        <button onclick="openSlide('gamesSlide')"
            class="cursor-pointer flex flex-col items-center text-gray-600 hover:text-blue-500">
            <div class="">
                <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-gamepad-2 w-6 h-6">
                    <line x1="6" x2="10" y1="11" y2="11" />
                    <line x1="8" x2="8" y1="9" y2="13" />
                    <line x1="15" x2="15.01" y1="12" y2="12" />
                    <line x1="18" x2="18.01" y1="10" y2="10" />
                    <path
                        d="M17.32 5H6.68a4 4 0 0 0-3.978 3.59c-.006.052-.01.101-.017.152C2.604 9.416 2 14.456 2 16a3 3 0 0 0 3 3c1 0 1.5-.5 2-1l1.414-1.414A2 2 0 0 1 9.828 16h4.344a2 2 0 0 1 1.414.586L17 18c.5.5 1 1 2 1a3 3 0 0 0 3-3c0-1.544-.604-6.584-.685-7.258-.007-.05-.011-.1-.017-.152A4 4 0 0 0 17.32 5z" />
                </svg>
            </div>
            <span class="tracking-wider text-[9px] pt-1">Games</span>
        </button>

        <!-- Portfolio & AI -->
        <button onclick="openSlide('aiSlide')"
            class="cursor-pointer searchButton flex flex-col items-center text-gray-600 hover:text-blue-500">
            <div class="">
                <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-sparkles  w-6 h-6">
                    <path
                        d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                    <path d="M20 3v4" />
                    <path d="M22 5h-4" />
                    <path d="M4 17v2" />
                    <path d="M5 18H3" />
                </svg>
            </div>
            <span class="tracking-wider text-[9px] pt-1">Portfolio & AI</span>
        </button>


        <!-- Profile -->
        <button onclick="openSlide('profileSlide')"
            class="flex flex-col items-center text-gray-600 hover:text-blue-500">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-user-round-cog h-6 w-6">
                <path d="M2 21a8 8 0 0 1 10.434-7.62" />
                <circle cx="10" cy="8" r="5" />
                <circle cx="18" cy="18" r="3" />
                <path d="m19.5 14.3-.4.9" />
                <path d="m16.9 20.8-.4.9" />
                <path d="m21.7 19.5-.9-.4" />
                <path d="m15.2 16.9-.9-.4" />
                <path d="m21.7 16.5-.9.4" />
                <path d="m15.2 19.1-.9.4" />
                <path d="m19.5 21.7-.4-.9" />
                <path d="m16.9 15.2-.4-.9" />
            </svg>
            <span class="tracking-wider text-[9px] pt-1">Profile</span>
        </button>

    </div>
</div>

<!-- JavaScript -->
<script>
    let currentlyOpenSlide = null;

    function openSlide(slideId) {
        if (currentlyOpenSlide && currentlyOpenSlide !== slideId) {
            closeSlide(currentlyOpenSlide);
        }

        const slide = document.getElementById(slideId);
        slide.classList.remove('hidden', 'animate-slide-out');
        slide.classList.add('animate-slide-in');
        currentlyOpenSlide = slideId;
    }

    function closeSlide(slideId) {
        const slide = document.getElementById(slideId);
        slide.classList.remove('animate-slide-in');
        slide.classList.add('animate-slide-out');
        setTimeout(() => slide.classList.add('hidden'), 300); // Match the animation duration
        if (currentlyOpenSlide === slideId) {
            currentlyOpenSlide = null;
        }
    }
</script>

<style>
    @keyframes slideIn {
        from {
            transform: translateY(100%);
        }

        to {
            transform: translateY(0);
        }
    }

    @keyframes slideOut {
        from {
            transform: translateY(0);
        }

        to {
            transform: translateY(100%);
        }
    }

    .animate-slide-in {
        animation: slideIn 0.3s ease-out forwards;
    }

    .animate-slide-out {
        animation: slideOut 0.3s ease-in forwards;
    }
</style>