{% include 'blocks/feedback.html' %}
<div class="[&_.bg-neutral-800]:bg-blue-500 [&_.bg-neutral-800]:hover:!bg-blue-700">
    {{ get_block("info/contact") }}
</div>
<div class="[&_.bg-neutral-800]:bg-blue-500 [&_.bg-neutral-800]:hover:!bg-blue-700 [&_#personalSettingsModal]:z-[50]">
    {{ get_block("core/personal-settings") }}
</div>

<script>
    // Utility functions
    const $ = (selector, context = document) => context.querySelector(selector);
    const $$ = (selector, context = document) => context.querySelectorAll(selector);

    // Generate dynamic message
    function generateDynamicMessage() {
        return `My email address and phone number: ...

Schedule a Demo - Check out the AIBull Demo, featuring:
- Stocks Analysis: Top Stocks | Live Analysis | Technical Insights | Trade Signals | Screener
- Portfolio: Manage and track your investments
- Spreads Analyzer: Analyze spreads for better trading decisions
- Strategies: Covered Call | Cash Secured Put | Arbitrages | Straddle Scanner
- AI Agent: Real-time AI assistance for smarter trading

All absolutely FREE!`;
    }

    // Modal management
    function closeModal(modalId) {
        $(`#${modalId}`)?.classList.add('hidden');
    }

    function hideAllModals() {
        $$('.modal').forEach(modal => modal.classList.add('hidden'));
    }

    document.addEventListener('keydown', ({ key }) => {
        if (key === 'Escape') hideAllModals();
    });

    // Clarity user tracking
    document.addEventListener('DOMContentLoaded', () => {
        try {
            if (window.clarity && typeof userData !== 'undefined' && userData) {
                const userIdentifier = userData.getItem('email') || userData.getItem('phone');
                window.clarity('set', 'user', userIdentifier);
            }
        } catch (error) {
            console.error('Clarity user identifier error:', error);
        }
    });

    // Logout functionality
    function logout() {
        openConfirmationModal('Are you sure you want to log out?', () => {
            document.cookie = 'access_token=; Max-Age=0; path=/; Secure; SameSite=Strict';
            window.location.href = '/';
        }, []);
    }

    // Ticker toggle functionality
    const tickerConfig = {
        desktop: {
            toggle: '#ticker-toggle',
            bg: '#ticker-toggle-bg',
            dot: '#ticker-toggle-dot',
            menuStyleToggle: '#menu-style-toggle',
            menuStyleBg: '#menu-style-toggle-bg',
            menuStyleDot: '#menu-style-dot'
        },
        mobile: {
            toggle: '#ticker-toggle-mobile',
            bg: '#ticker-toggle-bg-mobile',
            dot: '#ticker-toggle-dot-mobile'
        },
        sidebar: {
            menuStyleToggle: '#menu-style-toggle-sidebar',
            menuStyleBg: '#menu-style-toggle-bg-sidebar',
            menuStyleDot: '#menu-style-dot-sidebar'
        },
        containers: {
            header: '#header-container',
            height: '#header-height',
            topMenu: '#top-menu-container'
        }
    };

    function updateTickerUI(show) {
        const updateToggle = (toggleSelector, bgSelector, dotSelector) => {
            // Query all elements with the given selectors
            const toggleEls = $$(toggleSelector);
            const bgEls = $$(bgSelector);
            const dotEls = $$(dotSelector);

            // Update each matching toggle element
            toggleEls.forEach((toggleEl, index) => {
                toggleEl.checked = show;
                if (dotEls[index]) {
                    dotEls[index].style.transform = show ? 'translateX(16px)' : 'translateX(0)';
                }
                if (bgEls[index]) {
                    bgEls[index].style.background = show ? '#3b82f6' : '#e7e7e7';
                }
            });
        };

        // Update all ticker toggles (desktop, mobile, and any duplicates in menu.html/top-menu.html)
        updateToggle(tickerConfig.desktop.toggle, tickerConfig.desktop.bg, tickerConfig.desktop.dot);
        updateToggle(tickerConfig.mobile.toggle, tickerConfig.mobile.bg, tickerConfig.mobile.dot);

        const { header, height, topMenu } = tickerConfig.containers;
        if ($(header)) {
            $(header).classList.toggle('top-[36px]', show);
            $(header).style.height = show ? 'calc(100vh - 38px)' : '100vh';
            $(height).style.maxHeight = show ? 'calc(100vh - 134px)' : 'calc(100vh - 107px)';
        }

        if ($(topMenu)) {
            $(topMenu).style.top = show ? '36px' : '0px';
            $(topMenu).dataset.tickerVisible = show.toString();
            adjustDropdownPositions(show);
        }
    }

    function adjustDropdownPositions(show) {
        $$('.top-menu-container .group > div.absolute').forEach(dropdown => {
            dropdown.style.top = show ? '89px' : '53px';
        });
    }

    window.toggleTickerPreference = () => {
        const newState = !($(`${tickerConfig.desktop.toggle}`)?.checked ?? true);
        window.toggleTickerVisibility(newState);
        updateTickerUI(newState);

        if (typeof userData !== 'undefined' && userData && userData.getItem('showTicker') !== newState) {
            userData.setItem('showTicker', newState);
        }
    };

    function updateMenuStyleUI(isSidebar) {
        const updateToggle = ({ menuStyleToggle, menuStyleBg, menuStyleDot }) => {
            const toggleEl = $(menuStyleToggle);
            if (toggleEl) {
                toggleEl.checked = isSidebar;
                $(menuStyleDot).style.transform = isSidebar ? 'translateX(16px)' : 'translateX(0)';
                $(menuStyleBg).style.background = isSidebar ? '#3b82f6' : '#e7e7e7';
            }
        };

        updateToggle(tickerConfig.desktop);
        updateToggle(tickerConfig.sidebar);

        const { header, topMenu } = tickerConfig.containers;
        if ($(header) && $(topMenu)) {
            $(header).style.display = isSidebar ? 'block' : 'none';
            $(topMenu).style.display = isSidebar ? 'none' : 'block';
            document.body.classList.toggle('static-mode', isSidebar);
            document.querySelectorAll('.rhs-block').forEach(rhsBlock => {
                rhsBlock.style.paddingLeft = isSidebar ? '215px' : '0px';
            });
        }
    }

    window.toggleMenuStylePreference = () => {
        const newState = !($(`${tickerConfig.desktop.menuStyleToggle}`)?.checked ?? false);
        if (typeof userData !== 'undefined' && userData) {
            userData.setItem('menuStyle', newState ? 'sidebar' : 'top');
        }
        updateMenuStyleUI(newState);
    };

    // Scroll indicator logic
    function initScrollIndicator() {
        const scrollContainer = $('.menu-custom-scroll');
        const scrollIndicator = $('#menu-scroll-indicator');

        if (!scrollContainer || !scrollIndicator) return;

        const checkScroll = () => {
            const isOverflow = scrollContainer.scrollHeight > scrollContainer.clientHeight;
            const isAtBottom = scrollContainer.scrollTop + scrollContainer.clientHeight >= scrollContainer.scrollHeight - 10;
            scrollIndicator.classList.toggle('hidden', !isOverflow || isAtBottom);
        };

        checkScroll();
        scrollContainer.addEventListener('scroll', checkScroll);
        window.addEventListener('resize', checkScroll);
    }

    // User data and navigation
    const userDataExists = {{ "true" if User_Data else "false" | safe }};

    function handleLinkClick(event, route) {
        event.preventDefault();
        if (userDataExists) {
            window.location.href = route;
        } else {
            showModal('Please Login/Signup to access this feature');
        }
    }

    function isLoggedIn() {
        return userDataExists;
    }

    // Initialize on DOM load
    document.addEventListener('DOMContentLoaded', () => {
        const showTicker = userData?.getItem('showTicker') ?? true;
        const menuStyle = userData?.getItem('menuStyle') ?? 'top';
        updateMenuStyleUI(menuStyle === 'sidebar');
        updateTickerUI(showTicker);
        initScrollIndicator();

        if ($(tickerConfig.containers.topMenu)) {
            new MutationObserver(() => {
                adjustDropdownPositions($(tickerConfig.containers.topMenu).dataset.tickerVisible === 'true');
            }).observe($(tickerConfig.containers.topMenu), { attributes: true, attributeFilter: ['data-ticker-visible'] });
        }
    });
</script>