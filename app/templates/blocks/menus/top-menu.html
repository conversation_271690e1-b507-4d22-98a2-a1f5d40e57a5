<div id="top-menu-container" data-ticker-visible="false" style="display: none; top: 0px;"
    class="sticky top-0 z-40 lg:backdrop-blur-[12px] w-full transition-all duration-300">

    <div class="bg-white border-b border-gray-100 px-5">
        <!-- Desktop view -->
        <div class="menu-custom-scroll mx-auto p-0 md:px-0 relative lg:py-0">
            <!-- Main menu row -->
            <div class="lg:flex justify-between gap-4">
                <!-- Main menu will be here -->

                <div class="items-center gap-6 lg:flex">
                    <!-- Logo and Search Box Container -->
                    <div class="flex items-center">
                        <!-- Logo -->
                        <a href="/app" class="hidden lg:flex items-center text-2xl font-bold">
                            <img class="h-8" src="{{ cdn('/static/images/logo.png') }}" alt="AIBull Logo" width="29"
                                height="32" loading="lazy">
                            <span class="ml-2 text-neutral-950">AIBull</span>
                        </a>
                    </div>
                </div>


                <div class="flex-grow">
                    <div class="flex md:flex-row flex-col items-start md:items-start md:gap-6 gap-4">
                        <ul class="hidden lg:flex lg:items-start lg:space-x-2 lg:w-auto">
                            <li class="cursor-pointer py-3 text-sm no-underline transition-all group relative">
                                <div class="cursor-pointer flex gap-1 heading px-2 py-1 font-semibold items-center group hover:bg-neutral-100 rounded-md hover:text-gray-800 top-menu-item"
                                    data-submenu="algos-submenu">
                                    Algos
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-chevron-down group-hover:rotate-180 transition-all">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </div>
                                <div class="absolute hidden group-hover:block max-w-[75vw] lfet-0 rounded-2xl shadow-lg transition-all w-[350px] z-50"
                                    style="top: 53px;">
                                    <div class="bg-white flex p-3 gap-3 overflow-hidden rounded-2xl">
                                        <div class="w-full">
                                            <a href="/algos/options"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-indigo-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-indigo-500">
                                                        <path
                                                            d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2">
                                                        </path>
                                                        <path d="M18 14h-6"></path>
                                                        <path d="M15 18H9"></path>
                                                        <path d="M12 2v20"></path>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Options</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600">Explore
                                                        algorithmic options trading</span>
                                                </div>
                                            </a>
                                            <a href="/algos/backtesting"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-red-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-red-400">
                                                        <path d="M12 2v10l3 3"></path>
                                                        <path d="M2 12h10"></path>
                                                        <path
                                                            d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Backtesting</span>
                                                    <span class="text-xs line-clamp-2 leading-4 text-neutral-600">Test
                                                        strategies with historical data</span>
                                                </div>
                                            </a>
                                            <a href="/algos/backtesting-intraday"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-purple-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-purple-500">
                                                        <path d="M12 2v20"></path>
                                                        <path d="M2 12h20"></path>
                                                        <path d="M6 6h12v12H6z"></path>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Backtesting
                                                        Intraday</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600">Intraday
                                                        backtesting strategies</span>
                                                </div>
                                                <a href="/algos/scalping"
                                                    class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                    <div
                                                        class="aspect-square px-1.5 border  border-green-600 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="1.5" stroke-linecap="round"
                                                            stroke-linejoin="round" class="stroke-green-600">
                                                            <path d="M3 3v18h18"></path>
                                                            <path d="m19 9-5 5-4-4-3 3"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="pl-3">
                                                        <span
                                                            class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Algos
                                                            Scalping</span>
                                                        <span
                                                            class="text-xs line-clamp-2 leading-4 text-neutral-600">High-frequency
                                                            trading strategies</span>
                                                    </div>
                                                </a>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="cursor-pointer py-3 text-sm no-underline transition-all group relative">
                                <div class="cursor-pointer flex gap-1 heading px-2 py-1 font-semibold items-center group hover:bg-neutral-100 rounded-md hover:text-gray-800 top-menu-item"
                                    data-submenu="stocks-submenu">
                                    Stocks
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-chevron-down group-hover:rotate-180 transition-all">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </div>
                                <div class="absolute hidden group-hover:block max-w-[75vw] lfet-0 rounded-2xl shadow-lg transition-all w-[350px] z-50"
                                    style="top: 53px;">
                                    <div class="bg-white flex p-3 gap-3 overflow-hidden rounded-2xl">
                                        <div class="w-full">
                                            <a href="/stocks"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-cyan-400 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-cyan-600">
                                                        <circle cx="11" cy="11" r="8"></circle>
                                                        <line x1="21" x2="16.65" y1="21" y2="16.65"></line>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Screener</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Filter
                                                        stocks based on criteria</span>
                                                </div>
                                            </a>
                                            <a href="/stocks/live"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-green-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-green-600">
                                                        <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Live
                                                        Analysis</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Real-time
                                                        stock analysis</span>
                                                </div>
                                            </a>
                                            <a href="/screener/compare"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-purple-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-purple-600">
                                                        <path d="M3 7V5a2 2 0 0 1 2-2h2"></path>
                                                        <path d="M17 3h2a2 2 0 0 1 2 2v2"></path>
                                                        <path d="M21 17v2a2 2 0 0 1-2 2h-2"></path>
                                                        <path d="M7 12H3"></path>
                                                        <path d="M17 12h4"></path>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Compare</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Compare
                                                        stock performance</span>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </li>



                            <li class="cursor-pointer py-3 text-sm no-underline transition-all group relative">
                                <div class="cursor-pointer flex gap-1 heading px-2 py-1 font-semibold items-center group hover:bg-neutral-100 rounded-md hover:text-gray-800 top-menu-item"
                                    data-submenu="trading-submenu">
                                    Trading
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-chevron-down group-hover:rotate-180 transition-all">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </div>
                                <div class="absolute hidden group-hover:block max-w-[75vw] lfet-0 rounded-2xl shadow-lg transition-all w-[350px] z-50"
                                    style="top: 53px;">
                                    <div class="bg-white flex p-3 gap-3 overflow-hidden rounded-2xl">
                                        <div class="w-full">
                                            <a href="/options/spreads"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-orange-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-chart-network-icon lucide-chart-network text-orange-500">
                                                        <path d="m13.11 7.664 1.78 2.672" />
                                                        <path d="m14.162 12.788-3.324 1.424" />
                                                        <path d="m20 4-6.06 1.515" />
                                                        <path d="M3 3v16a2 2 0 0 0 2 2h16" />
                                                        <circle cx="12" cy="6" r="2" />
                                                        <circle cx="16" cy="12" r="2" />
                                                        <circle cx="9" cy="15" r="2" />
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Spreads</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600">Analyze
                                                        trading spreads</span>
                                                </div>
                                            </a>
                                            <a href="/options/chain"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-red-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-red-400">
                                                        <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Option
                                                        Chain</span>
                                                    <span class="text-xs line-clamp-2 leading-4 text-neutral-600 ">View
                                                        options chain data</span>
                                                </div>
                                            </a>
                                            <a href="/strategies/straddle-scanner"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-indigo-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-indigo-400">
                                                        <circle cx="11" cy="11" r="8"></circle>
                                                        <line x1="21" x2="16.65" y1="21" y2="16.65"></line>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Straddle
                                                        Scanner</span>
                                                    <span class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Scan
                                                        for straddle opportunities</span>
                                                </div>
                                            </a>
                                            <a href="/strategies/spread-analyzer"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-lime-500 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-lime-800">
                                                        <path d="M3 3v18h18"></path>
                                                        <path d="m19 9-5 5-4-4-3 3"></path>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Spread
                                                        Analyzer</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Analyze
                                                        spread strategies</span>
                                                </div>
                                            </a>
                                            <a href="/strategies/strike-scanner"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-red-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-banknote-arrow-up-icon lucide-banknote-arrow-up text-red-500">
                                                        <path
                                                            d="M12 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5" />
                                                        <path d="M18 12h.01" />
                                                        <path d="M19 22v-6" />
                                                        <path d="m22 19-3-3-3 3" />
                                                        <path d="M6 12h.01" />
                                                        <circle cx="12" cy="12" r="2" />
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Strike
                                                        Scanner</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Identify
                                                        optimal strike prices</span>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="cursor-pointer py-3 text-sm no-underline transition-all group relative">
                                <div class="cursor-pointer flex gap-1 heading px-2 py-1 font-semibold items-center group hover:bg-neutral-100 rounded-md hover:text-gray-800 top-menu-item"
                                    data-submenu="strategies-submenu">
                                    Strategies &amp; Arbitrages
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-chevron-down group-hover:rotate-180 transition-all">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </div>
                                <div class="absolute hidden group-hover:block max-w-[75vw] lfet-0 rounded-2xl shadow-lg transition-all w-[700px] z-50"
                                    style="top: 53px;">
                                    <div class="bg-white flex p-3 gap-3 overflow-hidden rounded-2xl">
                                        <div class="w-1/2 border-r pr-2">
                                            <div class="text-xs uppercase text-neutral-500 font-semibold px-4 pb-1">
                                                Strategies
                                            </div>
                                            <a href="/strategies/covered-call"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-green-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-hand-coins-icon lucide-hand-coins text-green-600">
                                                        <path d="M11 15h2a2 2 0 1 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 17" />
                                                        <path
                                                            d="m7 21 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a2 2 0 0 0-2.75-2.91l-4.2 3.9" />
                                                        <path d="m2 16 6 6" />
                                                        <circle cx="16" cy="9" r="2.9" />
                                                        <circle cx="6" cy="5" r="3" />
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Covered
                                                        Call</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Generate
                                                        income with covered calls</span>
                                                </div>
                                            </a>
                                            <a href="/strategies/cash-secured-put"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-amber-500 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-banknote-icon lucide-banknote text-amber-600">
                                                        <rect width="20" height="12" x="2" y="6" rx="2" />
                                                        <circle cx="12" cy="12" r="2" />
                                                        <path d="M6 12h.01M18 12h.01" />
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Cash
                                                        Secured Put</span>
                                                    <span class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Earn
                                                        premiums with puts</span>
                                                </div>
                                            </a>
                                            <a href="/strategies/straddle-graph"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-red-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-calendar-fold-icon lucide-calendar-fold text-red-500">
                                                        <path d="M8 2v4" />
                                                        <path d="M16 2v4" />
                                                        <path
                                                            d="M21 17V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h11Z" />
                                                        <path d="M3 10h18" />
                                                        <path d="M15 22v-4a2 2 0 0 1 2-2h4" />
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Expiration
                                                        Day</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Analyze
                                                        expiration day strategies</span>
                                                </div>
                                            </a>
                                            <a href="/calculators"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-stone-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-calculator-icon lucide-calculator text-stone-600">
                                                        <rect width="16" height="20" x="4" y="2" rx="2" />
                                                        <line x1="8" x2="16" y1="6" y2="6" />
                                                        <line x1="16" x2="16" y1="14" y2="18" />
                                                        <path d="M16 10h.01" />
                                                        <path d="M12 10h.01" />
                                                        <path d="M8 10h.01" />
                                                        <path d="M12 14h.01" />
                                                        <path d="M8 14h.01" />
                                                        <path d="M12 18h.01" />
                                                        <path d="M8 18h.01" />
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Calculators</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Financial
                                                        calculators for trading</span>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="w-1/2 pl-2">
                                            <div class="text-xs uppercase text-neutral-500 font-semibold px-4 pb-1">
                                                Arbitrages
                                            </div>
                                            <a href="/strategies/futures-arbitrage"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-fuchsia-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-piggy-bank-icon lucide-piggy-bank text-fuchsia-600">
                                                        <path
                                                            d="M11 17h3v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-3a3.16 3.16 0 0 0 2-2h1a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1h-1a5 5 0 0 0-2-4V3a4 4 0 0 0-3.2 1.6l-.3.4H11a6 6 0 0 0-6 6v1a5 5 0 0 0 2 4v3a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1z" />
                                                        <path d="M16 10h.01" />
                                                        <path d="M2 8v1a2 2 0 0 0 2 2h1" />
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Futures
                                                        Arbitrage</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Exploit
                                                        futures price differences</span>
                                                </div>
                                            </a>
                                            <a href="/strategies/exchanges-arbitrage"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-blue-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-chart-candlestick-icon lucide-chart-candlestick text-blue-600">
                                                        <path d="M9 5v4" />
                                                        <rect width="4" height="6" x="7" y="9" rx="1" />
                                                        <path d="M9 15v2" />
                                                        <path d="M17 3v2" />
                                                        <rect width="4" height="8" x="15" y="5" rx="1" />
                                                        <path d="M17 13v3" />
                                                        <path d="M3 3v16a2 2 0 0 0 2 2h16" />
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Exchanges
                                                        Arbitrage</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Profit
                                                        from exchange price gaps</span>
                                                </div>
                                            </a>
                                            <a href="/strategies/put-call-parity"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-teal-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-teal-600">
                                                        <path d="M12 2v20"></path>
                                                        <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Put
                                                        Call Parity</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Balance
                                                        put and call options</span>
                                                </div>
                                            </a>
                                            <a href="/strategies/box-spread-arbitrage"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-orange-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-orange-600">
                                                        <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                                                        <path d="M12 7v10"></path>
                                                        <path d="M8 9h8"></path>
                                                        <path d="M8 15h8"></path>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Box
                                                        Spread Arbitrage</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Low-risk
                                                        arbitrage strategy</span>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            {% if not is_us_market %}
                            <li class="cursor-pointer py-3 text-sm no-underline transition-all group relative">
                                <div class="cursor-pointer flex gap-1 heading px-2 py-1 font-semibold items-center group hover:bg-neutral-100 rounded-md hover:text-gray-800 top-menu-item"
                                    data-submenu="mutual-funds-submenu">
                                    Mutual Funds
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-chevron-down group-hover:rotate-180 transition-all">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </div>
                                <div class="absolute hidden group-hover:block max-w-[75vw] lfet-0 rounded-2xl shadow-lg transition-all w-[350px] z-50"
                                    style="top: 53px;">
                                    <div class="bg-white flex p-3 gap-3 overflow-hidden rounded-2xl">
                                        <div class="w-full">
                                            <a href="/mutual-funds"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-yellow-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-yellow-600">
                                                        <path d="M2 3h20"></path>
                                                        <path d="M21 3v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V3"></path>
                                                        <path d="m7 21 5-5 5 5"></path>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Screener</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Filter
                                                        mutual funds</span>
                                                </div>
                                            </a>
                                            <a href="/mutual-funds/compare"
                                                class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                                <div
                                                    class="aspect-square px-1.5 border  border-pink-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" class="stroke-pink-600">
                                                        <path d="M3 7V5a2 2 0 0 1 2-2h2"></path>
                                                        <path d="M17 3h2a2 2 0 0 1 2 2v2"></path>
                                                        <path d="M21 17v2a2 2 0 0 1-2 2h-2"></path>
                                                        <path d="M7 12H3"></path>
                                                        <path d="M17 12h4"></path>
                                                    </svg>
                                                </div>
                                                <div class="pl-3">
                                                    <span
                                                        class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">Compare</span>
                                                    <span
                                                        class="text-xs line-clamp-2 leading-4 text-neutral-600 ">Compare
                                                        mutual fund performance</span>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            {% endif %}
                            <li class="cursor-pointer py-3 text-sm no-underline transition-all group relative">
                                <div class="cursor-pointer flex gap-1 heading px-2 py-1 font-semibold items-center group hover:bg-neutral-100 rounded-md hover:text-gray-800 top-menu-item"
                                    id="quiz-menu-item">
                                    <a href="/quiz" class="no-underline">Quiz</a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="flex items-center">
                    <div class="md:flex-row flex-col items-start lg:items-center lg:space-x-3 hidden lg:flex">
                        <!-- Portfolio & AI Menu -->
                        <div class="cursor-pointer py-3 text-sm no-underline transition-all group relative">
                            <div class="cursor-pointer flex gap-1 heading px-2 py-1 font-semibold items-center group hover:bg-neutral-100 rounded-md hover:text-gray-800 top-menu-item"
                                data-submenu="portfolio-ai-submenu">
                                Portfolio &amp; AI
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-chevron-down group-hover:rotate-180 transition-all">
                                    <path d="m6 9 6 6 6-6"></path>
                                </svg>
                            </div>
                            <div class="absolute hidden group-hover:block max-w-[75vw] right-0 rounded-2xl shadow-lg transition-all w-[350px] z-50"
                                style="top: 53px;">
                                <div class="bg-white flex p-3 gap-3 overflow-hidden rounded-2xl">
                                    <div class="w-full">
                                        <a href="/portfolio"
                                            class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                            <div
                                                class="aspect-square px-1.5 border border-blue-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-gallery-vertical-end-icon lucide-gallery-vertical-end text-blue-500">
                                                    <path d="M7 2h10" />
                                                    <path d="M5 6h14" />
                                                    <rect width="18" height="12" x="3" y="10" rx="2" />
                                                </svg>
                                            </div>
                                            <div class="pl-3">
                                                <span
                                                    class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heading">View
                                                    Portfolio</span>
                                                <span class="text-xs line-clamp-2 leading-4 text-neutral-600">Manage
                                                    and track your investments</span>
                                            </div>
                                        </a>
                                        <a href="/ai-agent/live"
                                            class="hover:bg-blue-50/60 flex items-start px-4 py-3 rounded-lg">
                                            <div
                                                class="aspect-square px-1.5 border border-purple-300 flex h-8 items-center justify-center rounded-lg w-8 mt-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-brain-circuit-icon lucide-brain-circuit text-purple-500">
                                                    <path
                                                        d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z" />
                                                    <path d="M9 13a4.5 4.5 0 0 0 3-4" />
                                                    <path d="M6.003 5.125A3 3 0 0 0 6.401 6.5" />
                                                    <path d="M3.477 10.896a4 4 0 0 1 .585-.396" />
                                                    <path d="M6 18a4 4 0 0 1-1.967-.516" />
                                                    <path d="M12 13h4" />
                                                    <path d="M12 18h6a2 2 0 0 1 2 2v1" />
                                                    <path d="M12 8h8" />
                                                    <path d="M16 8V5a2 2 0 0 1 2-2" />
                                                    <circle cx="16" cy="13" r=".5" />
                                                    <circle cx="18" cy="3" r=".5" />
                                                    <circle cx="20" cy="21" r=".5" />
                                                    <circle cx="20" cy="8" r=".5" />
                                                </svg>
                                            </div>
                                            <div class="pl-3">
                                                <span
                                                    class="text-[14.5px] text-left text-neutral-800 font-medium block mb-1 heaidng">Live
                                                    AI Agent</span>
                                                <span class="text-xs line-clamp-2 leading-4 text-neutral-600">Real-time
                                                    AI trading assistance</span>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div
                            class="lg:space-x-2 space-x-0 lg:items-center items-start lg:flex hidden lg:flex-row flex-col">
                            {% if User_Data %}
                            <div class="relative group z-50 w-full">
                                <button
                                    class="flex justify-between font-medium gap-1 items-center lg:text-neutral-900 text-neutral-950 text-sm transition-colors w-full leading-[46px] dark:text-white">
                                    {% if User_Data.type == "secondary" %}
                                    <div class="flex items-center w-full">
                                        <div
                                            class="bg-blue-600 bg-gradient-to-tr bg-white border flex flex-shrink-0 from-pink-500 h-[30px] items-center justify-center relative rounded-md to-blue-600 w-[30px]">
                                            <span class="block truncate capitalize w-[1ch] text-xl text-white">
                                                {{ User_Data.secondary_user.name }}
                                            </span>
                                        </div>
                                        <span class="block truncate float-child ml-2 max-w-[20ch] capitalize text-sm">
                                            {{ User_Data.secondary_user.name }}
                                            <span
                                                class="ml-1 text-[10px] hidden lg:flex font-semibold text-neutral-400">(Sub
                                                Account)</span>
                                        </span>
                                    </div>
                                    {% else %}
                                    <div class="flex items-center w-full">
                                        <span
                                            class="block truncate float-child mr-2 max-w-[20ch] capitalize text-sm heading">
                                            {{ User_Data.name }}
                                        </span>
                                        <div
                                            class="bg-blue-600 bg-gradient-to-tr bg-white border flex flex-shrink-0 from-pink-500 h-[30px] items-center justify-center relative rounded-md to-blue-600 w-[30px]">

                                            <span class="block truncate capitalize w-[1ch] text-xl text-white heading">
                                                {{ User_Data.name }}
                                            </span>
                                        </div>

                                    </div>
                                    {% endif %}
                                </button>
                                <div
                                    class="absolute dark:bg-neutral-800 dark:border-neutral-700 group-hover:block hidden right-[-7px] rounded-2xl shadow-2xl w-[232px] z-50">
                                    {% if User_Data.type == "secondary" %}
                                    <!-- Display primary account details if secondary -->
                                    <div
                                        class="bg-white px-4 flex items-center py-2.5 hover:bg-neutral-100 dark:hover:bg-neutral-700 border-b border-neutral-200 dark:border-neutral-700">
                                        <div class="flex h-[33px] items-center mr-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="flex-shrink-0 lucide lucide-crown">
                                                <path
                                                    d="M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z" />
                                                <path d="M5 21h14" />
                                            </svg>
                                        </div>
                                        <div class="w-full">
                                            <div class="flex">
                                                <span
                                                    class="text-sm font-semibold text-neutral-800 dark:text-neutral-300 block truncate max-w-[16ch]">{{User_Data.name}}</span>
                                                <span
                                                    class="bg-gray-100 border-gray-100 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-300 border font-medium px-1.5 py-0.5 rounded-md text-[9px] text-neutral-600 ml-1">Primary</span>
                                            </div>
                                            {% if User_Data.email %}
                                            <span
                                                class="block text-xs text-neutral-600 dark:text-neutral-400 pt-0.5 max-w-[25ch] truncate">{{
                                                User_Data.email }}</span>
                                            {% elif User_Data.phone %}
                                            <span
                                                class="block text-xs text-neutral-600 dark:text-neutral-400 truncate pt-0.5">{{
                                                User_Data.phone }}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <!-- Display secondary account details -->
                                    <div
                                        class="bg-white px-4 py-2.5 flex items-center hover:bg-neutral-100 dark:hover:bg-neutral-700 border-b border-neutral-200 dark:border-neutral-700">
                                        <div class="flex h-[33px] items-center mr-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-user-round flex-shrink-0">
                                                <circle cx="12" cy="8" r="5" />
                                                <path d="M20 21a8 8 0 0 0-16 0" />
                                            </svg>
                                        </div>
                                        <div class="w-full">
                                            <div class="flex">
                                                <span
                                                    class="text-sm font-semibold text-neutral-800 dark:text-neutral-300 block truncate max-w-[16ch]">
                                                    {{ User_Data.secondary_user.name }}<span
                                                        class="bg-gray-100 border-gray-100 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-300 border font-medium px-1.5 py-0.5 rounded-md text-[9px] text-neutral-600 ml-1">Sub</span>
                                                </span>
                                            </div>
                                            {% if User_Data.secondary_user.email %}
                                            <span
                                                class="block text-xs text-neutral-600 dark:text-neutral-400 pt-0.5 max-w-[25ch] truncate">{{
                                                User_Data.secondary_user.email }}</span>
                                            {% elif User_Data.secondary_user.phone %}
                                            <span
                                                class="block text-xs text-neutral-600 dark:text-neutral-400 truncate pt-0.5">{{
                                                User_Data.secondary_user.phone }}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% else %}
                                    <!-- Display primary account details if primary -->
                                    <div
                                        class="bg-gradient-to-r bg-white cursor-pointer rounded-t-lg dark:border-neutral-700 dark:hover:bg-neutral-700 from-violet-50 hover:bg-neutral-100 px-4 py-2.5 to-pink-50">
                                        <span
                                            class="block text-sm font-semibold text-neutral-900 dark:text-neutral-200 capitalize">{{
                                            User_Data.name }}</span>
                                        {% if User_Data.email %}
                                        <span
                                            class="block text-xs text-neutral-600 dark:text-neutral-400 truncate pt-0.5">{{
                                            User_Data.email }}</span>
                                        {% elif User_Data.phone %}
                                        <span
                                            class="block text-xs text-neutral-600 dark:text-neutral-400 truncate pt-0.5">{{
                                            User_Data.phone }}</span>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                    <a id="usersButton"
                                        class="bg-white flex gap-2 text-sm items-center px-4 py-2.5 cursor-pointer borer-b border-gray-100 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users">
                                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                                            <circle cx="9" cy="7" r="4" />
                                            <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                                            <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                                        </svg>
                                        Manage Users
                                    </a>
                                    <a id="personalSettingsButton"
                                        class="bg-white flex gap-2 text-sm items-center px-4 border-t py-2.5 cursor-pointer borer-b border-gray-200 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user">
                                            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                                            <circle cx="12" cy="7" r="4" />
                                        </svg>
                                        Personal Settings
                                    </a>
                                    <a id="demoButton" href="/demo"
                                        class="bg-white flex gap-2 text-sm items-center px-4 border-t py-2.5 cursor-pointer borer-b border-gray-200 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-play-circle">
                                            <circle cx="12" cy="12" r="10" />
                                            <polygon points="10 8 16 12 10 16 10 8" />
                                        </svg>
                                        Demo Videos
                                    </a>
                                    <button onclick="showContactUsModal('support', generateDynamicMessage())"
                                        class="bg-white flex gap-2 text-sm items-center px-4 border-t py-2.5 cursor-pointer borer-b border-gray-200 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-mailbox">
                                            <path
                                                d="M22 17a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9.5C2 7 4 5 6.5 5H18c2.2 0 4 1.8 4 4v8Z" />
                                            <polyline points="15,9 18,9 18,11" />
                                            <path d="M6.5 5C9 5 11 7 11 9.5V17a2 2 0 0 1-2 2" />
                                            <line x1="6" x2="7" y1="10" y2="10" />
                                        </svg>
                                        Contact Us
                                    </button>
                                    <!-- Feedback -->
                                    <button onclick="showFeedbackModal()"
                                        class="bg-white flex gap-2 text-sm items-center px-4 border-t py-2.5 cursor-pointer borer-b border-gray-200 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">

                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-message-square-heart">
                                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                                            <path
                                                d="M14.8 7.5a1.84 1.84 0 0 0-2.6 0l-.2.3-.3-.3a1.84 1.84 0 1 0-2.4 2.8L12 13l2.7-2.7c.9-.9.8-2.1.1-2.8" />
                                        </svg>
                                        <span>Feedback</span>
                                    </button>

                                    <!-- Apps -->
                                    <div class="w-full">
                                        <a href="/apps"
                                            class="bg-white flex gap-2 text-sm items-center px-4 border-t py-2.5 cursor-pointer borer-b border-gray-200 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-layout-grid-icon lucide-layout-grid">
                                                <rect width="7" height="7" x="3" y="3" rx="1" />
                                                <rect width="7" height="7" x="14" y="3" rx="1" />
                                                <rect width="7" height="7" x="14" y="14" rx="1" />
                                                <rect width="7" height="7" x="3" y="14" rx="1" />
                                            </svg>
                                            <span>Apps</span>
                                        </a>
                                    </div>

                                    <!-- Toggle Ticker Preference -->
                                    <div class="bg-white flex items-center justify-between text-sm text-neutral-700 dark:text-neutral-200 cursor-pointer px-4 py-2.5 border-t border-gray-200 dark:border-neutral-500 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full"
                                        onclick="toggleTickerPreference()">
                                        <div class="flex items-center">
                                            <span class="float-child flex-shrink-0 text-neutral-950">Show Ticker</span>
                                        </div>
                                        <div class="relative float-child">
                                            <input type="checkbox" id="ticker-toggle" class="sr-only">
                                            <div id="ticker-toggle-bg"
                                                class="block bg-gray-300 dark:bg-neutral-600 w-[2.25rem] h-5 rounded-full">
                                            </div>
                                            <div id="ticker-toggle-dot"
                                                class="absolute left-1 top-[0.2rem] bg-white w-3.5 h-3.5 rounded-full transition-transform duration-300 ease-in-out">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-white flex items-center justify-between text-sm text-neutral-700 dark:text-neutral-200 cursor-pointer px-4 py-2.5 border-t border-gray-200 dark:border-neutral-500 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full"
                                        onclick="toggleMenuStylePreference()">
                                        <div class="flex items-center">
                                            <span class="float-child flex-shrink-0 text-neutral-950">Sidebar Menu</span>
                                        </div>
                                        <div class="relative float-child">
                                            <input type="checkbox" id="menu-style-toggle" class="sr-only">
                                            <div id="menu-style-toggle-bg"
                                                class="block bg-gray-300 dark:bg-neutral-600 w-[2.25rem] h-5 rounded-full">
                                            </div>
                                            <div id="menu-style-dot"
                                                class="absolute left-1 top-[0.2rem] bg-white w-3.5 h-3.5 rounded-full transition-transform duration-300 ease-in-out">
                                            </div>
                                        </div>
                                    </div>

                                    <a href="#" onclick="logout()"
                                        class="flex bg-white gap-2 text-sm items-center border-t dark:border-neutral-700 px-4 py-2.5 text-[#f56b62] dark:text-[#ff5448] hover:text-[#ff5448] dark:hover:text-[#ff3a2e] hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded-b-xl">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-log-out">
                                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                                            <polyline points="16 17 21 12 16 7" />
                                            <line x1="21" x2="9" y1="12" y2="12" />
                                        </svg>
                                        Logout
                                    </a>
                                </div>
                            </div>
                            {% else %}
                            <button id="loginButton"
                                class="bg-white border border-neutral-700 cursor-pointer dark:border-neutral-700 dark:hover:bg-neutral-700 dark:lg:text-neutral-200 focus-visible:outline-none font-medium hidden hover:bg-neutral-100 hover:shadow-sm lg:block lg:border-neutral-200 lg:min-h-0 lg:my-0 lg:text-neutral-900 min-h-[46px] my-3 px-4 py-2 rounded-md shadow-sm text-nowrap text-sm text-white transition-colors w-full"
                                onclick="showLoginModal(true)">
                                Login
                            </button>
                            <button id="signupButton"
                                class="lg:w-auto w-full lg:min-h-0 border lg:border-blue-500 dark:border-blue-700 bg-blue-500 dark:bg-blue-800 hover:bg-blue-600 dark:hover:bg-blue-600 transition-colors hover:shadow-sm font-medium text-sm text-white rounded-md px-6 transition-all lg:py-2 border-blue-700 py-1.5 font-medium"
                                onclick="showLoginModal(false)">
                                Signup
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div> <!-- Close the main menu row div -->


        </div>
    </div>
    <div id="submenu-container" style="background-image: linear-gradient(273deg, #ffffff, #f9fff4);"
        class="flex hidden justify-bwtween items-center  mx-auto w-full relative pl-4">
        <div class="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200">
        </div>

        <!-- Submenu Container - Now as a separate row -->
        <div class="flex-1">
            <!-- Algos Submenu -->
            <div id="algos-submenu" class=" submenu">
                <div class="px-5">
                    <ul class="flex justify-start py-2 space-x-3">
                        <li><a href="/algos/options"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Options</a>
                        </li>
                        <li><a href="/algos/backtesting"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Backtesting</a>
                        </li>
                        <li><a href="/algos/scalping"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Algos
                                Scalping</a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Stocks Submenu -->
            <div id="stocks-submenu" class=" submenu hidden">
                <div class="px-5">
                    <ul class="flex justify-start py-2 space-x-3">
                        <li><a href="/stocks"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Screener</a>
                        </li>
                        <li><a href="/stocks/live"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Live
                                Analysis</a>
                        </li>
                        <li><a href="/screener/compare"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Compare</a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Trading Submenu -->
            <div id="trading-submenu" class=" submenu hidden">
                <div class="px-5">
                    <ul class="flex justify-start py-2 space-x-3">
                        <li><a href="/options/spreads"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Spreads</a>
                        </li>
                        <li><a href="/options/chain"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Option
                                Chain</a></li>
                        <li><a href="/strategies/straddle-scanner"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Straddle
                                Scanner</a></li>
                        <li><a href="/strategies/spread-analyzer"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Spread
                                Analyzer</a></li>
                        <li><a href="/strategies/strike-scanner"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Strike
                                Scanner</a></li>
                    </ul>
                </div>
            </div>

            <!-- Strategies & Arbitrages Submenu -->
            <div id="strategies-submenu" class=" submenu hidden">
                <div class="px-5">
                    <ul class="flex justify-start py-2 space-x-3">
                        <li><a href="/strategies/covered-call"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Covered
                                Call</a></li>
                        <li><a href="/strategies/cash-secured-put"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Cash
                                Secured Put</a></li>
                        <li><a href="/strategies/straddle-graph"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Expiration
                                Day</a></li>
                        <li><a href="/calculators"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Calculators</a>
                        </li>
                        <li><a href="/strategies/futures-arbitrage"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Futures
                                Arbitrage</a></li>
                        <li><a href="/strategies/exchanges-arbitrage"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded">Exchanges
                                Arbitrage</a></li>
                        <li><a href="/strategies/put-call-parity"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Put
                                Call
                                Parity</a></li>
                        <li><a href="/strategies/box-spread-arbitrage"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Box
                                Spread Arbitrage</a></li>
                    </ul>
                </div>
            </div>

            <!-- Mutual Funds Submenu -->
            <div id="mutual-funds-submenu" class=" submenu hidden">
                <div class="px-5">
                    <ul class="flex justify-start py-2 space-x-3">
                        <li><a href="/mutual-funds"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded  font-medium">Screener</a>
                        </li>
                        <li><a href="/mutual-funds/compare"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded  font-medium">Compare</a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Portfolio & AI Submenu -->
            <div id="portfolio-ai-submenu" class=" submenu hidden">
                <div class="px-5">
                    <ul class="flex justify-start py-2 space-x-3">
                        <li><a href="/portfolio"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">View
                                Portfolio</a>
                        </li>
                        <li><a href="/ai-agent/live"
                                class="text-neutral-700 heading text-sm py-1 px-2 hover:bg-blue-100 rounded font-medium">Live
                                AI
                                Agent</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    .font-semibold {
        font-family: poppins-semibold;
    }

    @media (min-width: 768px) and (max-width: 1380px) {

        body,
        .text-sm {
            font-size: 85%;
        }
    }
</style>

<style>
    @media (min-width: 1280px) {

        .static-menu,
        header {
            width: 60px;
        }

        .float-menu,
        header:hover {
            width: 215px;
        }

        .mantra-active-menu.active {
            background: #e0f2fe;
        }

        .carousel-container {
            width: 100%;
            overflow: hidden;
            position: relative;
        }

        .carousel {
            display: flex;
            transition: transform 0.5s ease-in-out;
            /* Set a smooth transition for sliding effect */
        }

        .carousel-item {
            min-width: 100%;
            box-sizing: border-box;
        }
    }

    .noUi-target {
        background: #f2f2f2 !important;
        border-radius: 100px !important;
        height: 5px !important;
    }

    .noUi-horizontal .noUi-handle {
        top: -8px !important;
    }

    .noUi-connect {
        background: #287aff !important;
    }

    .noUi-handle:after,
    .noUi-handle:before {
        height: 7px !important;
        background: #82807f !important;
    }

    .noUi-horizontal .noUi-handle {
        height: 20px !important;
    }

    .noUi-tooltip {
        border-radius: 6px !important;
        color: #000 !important;
        padding: 3px 8px !important;
        font-size: 12px !important;
        box-shadow: 0px -2px 7px #ccc !important;
    }

    /* Submenu Styles */
    .menu-item-active {
        background-color: #e3edfa !important;
        cursor: default !important;
    }

    #submenu-container {

        z-index: 30;

        position: relative;
        /* Ensure it's in the normal document flow */

    }

    .submenu ul li a.active {
        color: #1a73e8;
        background: #e3efff;
    }

    .submenu-active {
        display: block !important;
    }

    .menu-item-active:after {
        content: "";
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #ffd695;
        display: inline-block;
        vertical-align: middle;
        margin-left: 12px;
        position: absolute;
        bottom: 0;
        transform: rotate(180deg);
    }
</style>

<!-- Submenu JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Get all menu items
        const menuItems = document.querySelectorAll('.top-menu-item');
        const submenus = document.querySelectorAll('.submenu');
        const submenuContainer = document.getElementById('submenu-container');

        // URL path mapping to menu sections
        const urlPathMapping = {
            // Algos section
            '/algos/backtesting': 'algos-submenu',
            '/algos/scalping': 'algos-submenu',
            '/algos/options': 'algos-submenu',

            // Stocks section
            '/stocks': 'stocks-submenu',
            '/stocks/live': 'stocks-submenu',
            '/screener/': 'stocks-submenu',

            // Trading section
            '/options/': 'trading-submenu',
            '/strategies/straddle-scanner': 'trading-submenu',
            '/strategies/spread-analyzer': 'trading-submenu',
            '/strategies/strike-scanner': 'trading-submenu',

            // Strategies & Arbitrages section
            '/strategies/': 'strategies-submenu',
            '/arbitrages/': 'strategies-submenu',
            '/calculators': 'strategies-submenu',

            // Mutual Funds section
            '/mutual-funds': 'mutual-funds-submenu',
            '/mutual-funds/compare': 'mutual-funds-submenu',

            // Portfolio & AI section
            '/portfolio': 'portfolio-ai-submenu',
            '/ai-agent/': 'portfolio-ai-submenu',

            // Quiz section (no submenu, just highlight)
            '/quiz': 'quiz'
        };

        // Function to show a specific submenu
        function showSubmenu(menuItem) {
            // Get the submenu ID from the data attribute
            const submenuId = menuItem.getAttribute('data-submenu');

            // Remove active class from all menu items
            menuItems.forEach(item => {
                item.classList.remove('menu-item-active');
            });

            // Add active class to the clicked menu item
            menuItem.classList.add('menu-item-active');

            // Hide all submenus
            submenus.forEach(submenu => {
                submenu.classList.add('hidden');
            });

            // Show the submenu container with border
            submenuContainer.classList.remove('hidden');
            submenuContainer.classList.add('border-gray-200');

            // Show the corresponding submenu
            if (submenuId) {
                const submenu = document.getElementById(submenuId);
                if (submenu) {
                    submenu.classList.remove('hidden');
                }
            }
        }

        // Function to check current URL and show appropriate submenu
        function checkCurrentUrl() {
            const currentPath = window.location.pathname;
            let activeSubmenuId = null;

            // Check if current path matches any of our mappings
            for (const [pathPrefix, submenuId] of Object.entries(urlPathMapping)) {
                if (currentPath.startsWith(pathPrefix)) {
                    activeSubmenuId = submenuId;
                    break;
                }
            }

            // Special case for Quiz menu item (no submenu)
            if (activeSubmenuId === 'quiz') {
                // Highlight the Quiz menu item
                const quizMenuItem = document.getElementById('quiz-menu-item');
                if (quizMenuItem) {
                    quizMenuItem.classList.add('menu-item-active');
                }
                return; // No submenu to show for Quiz
            }

            // If we found a matching submenu, show it
            if (activeSubmenuId) {
                const menuItem = document.querySelector(`.top-menu-item[data-submenu="${activeSubmenuId}"]`);
                if (menuItem) {
                    const parentListItem = menuItem.parentElement;
                    // Disable the dropdown for the selected menu
                    parentListItem.classList.remove('group');
                    // Hide the dropdown icon
                    const dropdownIcon = menuItem.querySelector('svg');
                    if (dropdownIcon) {
                        dropdownIcon.classList.add('hiddenfv');
                    }
                    // Remove cursor pointer from both parent and menu item
                    parentListItem.classList.add('cursor-default');

                    // Show the submenu
                    showSubmenu(menuItem);

                    // Also highlight the specific submenu item that matches the current URL
                    const submenuLinks = document.querySelectorAll(`#${activeSubmenuId} a`);
                    submenuLinks.forEach(link => {
                        // Remove active class from all submenu links
                        link.classList.remove('active');

                        // Get the href attribute of the link
                        const linkPath = link.getAttribute('href');

                        // Handle specific cases for algos submenu to avoid highlighting both backtesting and scalping
                        if (currentPath === linkPath) {
                            link.classList.add('active');
                        }
                        // For all other submenus, use a more flexible approach
                        else {
                            // Partial match - highlight if the current path starts with the link path
                            if (currentPath.startsWith(linkPath) && linkPath.length > 1) {
                                if (currentPath.startsWith('/stocks/live') && linkPath === '/stocks') {
                                    return;
                                }
                                if (currentPath.startsWith('/mutual-funds/compare') && linkPath === '/mutual-funds') {
                                    return;
                                }
                                // Additional check to ensure we're not matching partial paths
                                const nextChar = currentPath.charAt(linkPath.length);
                                if (nextChar === '' || nextChar === '/') {
                                    link.classList.add('active');
                                }
                            }
                        }
                    });
                }
            }
        }
        // Check current URL on page load
        checkCurrentUrl();
    });
</script>