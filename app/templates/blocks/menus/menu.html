<header id="header-container"
    class="bg-white hidden xl:block left-block border-r dark:bg-neutral-900 dark:border-neutral-800 duration-300 fixed left-0 top-[36px] transition-width z-50 float-menu hidden xl:block [&_.text-sm]:text-[0.825rem] [&_.text-gray-700]:text-gray-600 h-[calc(100vh_-_36px)]"
    style="display: none; height: 100vh;">
    <div class="h-full">
        <nav class="flex flex-col h-full justify-between">
            <div>
                <div class="flex justify-between items-center px-2 py-2 border-b dark:border-neutral-800">
                    <!-- Logo and Label -->
                    <div class="flex items-center">
                        <a href="/app" class="shrink-0 flex items-center">
                            <img class="flex-shrink-0 h-[30px] pr-1" src="{{ cdn('/static/images/logo.png') }}"
                                alt="AIBull" />
                            <div>
                                <span
                                    class="text-neutral-950 dark:text-white float-child text-lg font-bold">AIBull</span>
                                <!-- <p class="text-xs pt-[1px] float-child">AI Trading</p> -->
                            </div>
                        </a>
                    </div>
                </div>

                <div id="header-height"
                    class="2xl:p-3 p-2 overflow-auto overflow-x-hidden menu-custom-scroll max-h-[calc(100vh_-_140px)] lg:max-h-[calc(100vh_-_140px)]">
                    <div class="lg:block">
                        <!-- Algos (Primary Feature) -->
                        <div>
                            <div class="block p-1">
                                <div
                                    class="flex items-center font-medium lg:text-neutral-700 dark:lg:text-neutral-100 lg:hover:text-neutral-900 dark:hover:text-neutral-100 text-white transition-colors text-sm relative">
                                    <span class="float-child flex-shrink-0 text-neutral-950">Algos</span>
                                </div>
                            </div>
                            <!-- Expandable link section for Algos -->
                            <ul class="float-child">
                                <li>
                                    <a href="/algos/options"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/algos/options' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Options
                                    </a>
                                </li>
                                <li>
                                    <a href="/algos/backtesting"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if request.path == '/algos/backtesting' %}bg-sky-100 text-neutral-950{% endif %}">
                                        Backtesting
                                    </a>
                                </li>
                                <li>
                                    <a href="/algos/backtesting-intraday"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if request.path == '/algos/backtesting' %}bg-sky-100 text-neutral-950{% endif %}">
                                        Backtesting Intraday
                                    </a>
                                </li>
                                <li>
                                    <a href="/algos/scalping"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if request.path == '/algos/scalping' %}bg-sky-100 text-neutral-950{% endif %}">
                                        Algos Scalping
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <!-- Stocks -->
                        <div>
                            <div class="block p-1 mt-2">
                                <div
                                    class="flex items-center font-medium lg:text-neutral-700 dark:lg:text-neutral-100 lg:hover:text-neutral-900 dark:hover:text-neutral-100 text-white transition-colors text-sm relative">
                                    <span class="float-child flex-shrink-0 text-neutral-950">Stocks</span>
                                </div>
                            </div>
                            <!-- Expandable link section -->
                            <ul class="float-child">
                                <li>
                                    <a href="/stocks"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/stocks' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Screener
                                    </a>
                                </li>

                                <li>
                                    <a href="/stocks/live"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/stocks/live' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Live Analysis
                                    </a>
                                </li>
                                <li>
                                    <a href="/screener/compare"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/screener/compare' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Compare
                                    </a>
                                </li>
                                <!-- <li>
                                                                                    <a href="/stocks/top"
                                                                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if 'stocks/top' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                                                                        Top Stocks
                                                                                    </a>
                                                                                </li> -->


                            </ul>
                        </div>

                        <!-- Portfolio & AI Analysis (Standalone) -->
                        <div>
                            <div class="block p-1 mt-2">
                                <div
                                    class="flex items-center font-medium lg:text-neutral-700 dark:lg:text-neutral-100 lg:hover:text-neutral-900 dark:hover:text-neutral-100 text-white transition-colors text-sm relative">
                                    <span class="float-child flex-shrink-0 text-neutral-950">Portfolio & AI
                                        Analysis</span>
                                </div>
                            </div>
                            <!-- Single link without submenu -->
                            <ul class="float-child">
                                <li>
                                    <a href="/portfolio"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if request.url.path == '/portfolio' %}bg-sky-50{% endif %}">
                                        View Portfolio
                                    </a>
                                </li>

                                <li>
                                    <a href="/ai-agent/live"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/ai-agent/live' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Live AI Agent
                                    </a>
                                </li>

                            </ul>


                        </div>

                        <!-- Trading (Without Portfolio Link) -->
                        <div>
                            <div class="block p-1 mt-2">
                                <div
                                    class="flex items-center font-medium lg:text-neutral-700 dark:lg:text-neutral-100 lg:hover:text-neutral-900 dark:hover:text-neutral-100 text-white transition-colors text-sm relative">
                                    <span class="float-child flex-shrink-0 text-neutral-950">Trading</span>
                                </div>
                            </div>
                            <!-- Expandable link section -->
                            <ul class="float-child">

                                <li>
                                    <a href="/options/spreads"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if request.path == '/options/spreads' %}bg-sky-100 text-neutral-950{% endif %}">
                                        Spreads
                                    </a>
                                </li>
                                <li>
                                    <a href="/options/chain"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/options/chain' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Option Chain
                                    </a>
                                </li>
                                <li>
                                    <a href="/strategies/straddle-scanner"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/strategies/straddle-scanner' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Straddle Scanner
                                    </a>
                                </li>

                                <li>
                                    <a href="/strategies/spread-analyzer"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/strategies/spread-analyzer' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Spread Analyzer
                                    </a>
                                </li>
                                <li>
                                    <a href="/strategies/strike-scanner"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/strategies/strike-scanner' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Strike Scanner
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <!-- Strategies & Arbitrages -->
                        <div>
                            <div class="block p-1 mt-2">
                                <div
                                    class="flex items-center font-medium lg:text-neutral-700 dark:lg:text-neutral-100 lg:hover:text-neutral-900 dark:hover:text-neutral-100 text-white transition-colors text-sm relative">
                                    <span class="float-child flex-shrink-0 text-neutral-950">Strategies &
                                        Arbitrages</span>
                                </div>
                            </div>
                            <!-- Expandable link section -->
                            <ul class="float-child">
                                <!-- Strategies -->
                                <li class="text-xs text-neutral-500 font-semibold pt-1 pb-1">Strategies</li>
                                <li>
                                    <a href="/strategies/covered-call"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/strategies/covered-call' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Covered Call
                                    </a>
                                </li>
                                <li>
                                    <a href="/strategies/cash-secured-put"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/strategies/cash-secured-put' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Cash Secured Put
                                    </a>
                                </li>
                                <li>
                                    <a href="/strategies/straddle-graph"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/strategies/straddle-graph' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Expiration Day
                                    </a>
                                </li>
                                <li>
                                    <a href="/calculators"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/strategies/straddle-graph' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Calculators
                                    </a>
                                </li>

                                <!-- Arbitrages -->
                                <li class="text-xs text-neutral-500 font-semibold pt-3 pb-1">Arbitrages</li>
                                <li>
                                    <a href="/strategies/futures-arbitrage"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/strategies/futures-arbitrage' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Futures Arbitrage
                                    </a>
                                </li>
                                <li>
                                    <a href="/strategies/exchanges-arbitrage"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/strategies/exchanges-arbitrage' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Exchanges Arbitrage
                                    </a>
                                </li>
                                <li>
                                    <a href="/strategies/put-call-parity"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/strategies/put-call-parity' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Put Call Parity
                                    </a>
                                </li>
                                <li>
                                    <a href="/strategies/box-spread-arbitrage"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/strategies/box-spread-arbitrage' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Box Spread Arbitrage
                                    </a>
                                </li>
                            </ul>
                        </div>


                        <!-- Mutual Funds -->
                        {% if not is_us_market %}
                        <div>
                            <div class="block p-1 mt-2">
                                <div
                                    class="flex items-center font-medium lg:text-neutral-700 dark:lg:text-neutral-100 lg:hover:text-neutral-900 dark:hover:text-neutral-100 text-white transition-colors text-sm relative">
                                    <span class="float-child flex-shrink-0 text-neutral-950">Mutual Funds</span>
                                </div>
                            </div>
                            <!-- Expandable link section -->
                            <ul class="float-child">
                                <li>
                                    <a href="/mutual-funds"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if request.path == '/mutual-funds' %}bg-sky-100 text-neutral-950{% endif %}">
                                        Screener
                                    </a>
                                </li>
                                <li>
                                    <a href="/mutual-funds/compare"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if '/mutual-funds/compare' in request.path %}bg-sky-100 text-neutral-950{% endif %}">
                                        Compare
                                    </a>
                                </li>
                            </ul>
                        </div>
                        {% endif %}

                        <!-- Games -->
                        <div>
                            <div class="block p-1 mt-2">
                                <div
                                    class="flex items-center font-medium lg:text-neutral-700 dark:lg:text-neutral-100 lg:hover:text-neutral-900 dark:hover:text-neutral-100 text-white transition-colors text-sm relative">
                                    <span class="float-child flex-shrink-0 text-neutral-950">Games</span>
                                </div>
                            </div>
                            <ul class="float-child">
                                <li>
                                    <a href="/quiz"
                                        class="block rounded-[2px] py-1 pr-2 pl-2 text-sm text-gray-700 hover:bg-gray-50 {% if request.path == '/quiz' %}bg-sky-100 text-neutral-950{% endif %}">
                                        Quiz
                                    </a>
                                </li>
                            </ul>
                        </div>

                    </div>
                </div>
                <!-- Arrow indicator positioned outside scrollable area but inside sidebar -->
                <div id="menu-scroll-indicator"
                    class="-translate-x-1/2 absolute bg-gray-100 bottom-[46px] dark:bg-neutral-900 left-1/2 pointer-events-none text-yellow-800 transform w-full z-10 border-t h-[3px]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                        class="bg-gray-100 lucide lucide-chevrons-down-icon lucide-chevrons-down mx-auto p-0.5 relative rounded-full top-[-12px] border-t animate-bounce">
                        <path d="m6 9 6 6 6-6" />
                    </svg>
                </div>
            </div>

            <div>
                <div
                    class="align-center bg-gray-100 dark:bg-neutral-700 flex gap-x-2 lg:text-nowrmal px-3 py-2 text-nowrap w-full">
                    <div class="flex space-x-2 items-center hidden xl:flex w-full">
                        {% if User_Data %}
                        <div class="relative group z-50 w-full">
                            <button
                                class="flex justify-between font-medium gap-1 items-center lg:text-neutral-900 text-neutral-950 text-sm transition-colors w-full leading-[46px] dark:text-white">
                                {% if User_Data.type == "secondary" %}
                                <div class="flex items-center w-full">
                                    <div
                                        class="bg-blue-600 bg-gradient-to-tr bg-white border flex flex-shrink-0 from-pink-500 h-[30px] items-center justify-center relative rounded-md to-blue-600 w-[30px]">
                                        <span class="block truncate capitalize w-[1ch] text-xl text-white">
                                            {{ User_Data.secondary_user.name }}
                                        </span>
                                    </div>
                                    <span class="block truncate float-child ml-2 max-w-[20ch] capitalize text-sm">
                                        {{ User_Data.secondary_user.name }}
                                        <span
                                            class="ml-1 text-[10px] hidden lg:flex font-semibold text-neutral-400">(Sub
                                            Account)</span>
                                    </span>
                                </div>
                                {% else %}
                                <div class="flex items-center w-full">
                                    <div
                                        class="bg-blue-600 bg-gradient-to-tr bg-white border flex flex-shrink-0 from-pink-500 h-[30px] items-center justify-center relative rounded-md to-blue-600 w-[30px]">
                                        <span class="block truncate capitalize w-[1ch] text-xl text-white">
                                            {{ User_Data.name }}
                                        </span>
                                    </div>
                                    <span class="block truncate float-child ml-2 max-w-[20ch] capitalize text-sm">
                                        {{ User_Data.name }}
                                    </span>
                                </div>
                                {% endif %}
                            </button>
                            <div
                                class="absolute bg-white border bottom-0 dark:bg-neutral-800 dark:border-neutral-700 group-hover:block hidden left-[-7px] bottom-[28px] rounded-md shadow-lg w-[232px] z-50">
                                {% if User_Data.type == "secondary" %}
                                <!-- Display primary account details if secondary -->
                                <div
                                    class="px-4 flex items-center py-2.5 hover:bg-neutral-100 dark:hover:bg-neutral-700 border-b border-neutral-200 dark:border-neutral-700">
                                    <div class="flex h-[33px] items-center mr-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="flex-shrink-0 lucide lucide-crown">
                                            <path
                                                d="M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z" />
                                            <path d="M5 21h14" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="flex">
                                            <span
                                                class="text-sm font-medium text-neutral-800 dark:text-neutral-300 block truncate max-w-[16ch]">{{User_Data.name}}</span>
                                            <span
                                                class="bg-gray-100 border-gray-100 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-300 border font-medium px-1.5 py-0.5 rounded-md text-[9px] text-neutral-600 ml-1">Primary</span>
                                        </div>
                                        {% if User_Data.email %}
                                        <span
                                            class="block text-xs text-neutral-600 dark:text-neutral-400 pt-0.5 max-w-[25ch] truncate">{{
                                            User_Data.email }}</span>
                                        {% elif User_Data.phone %}
                                        <span
                                            class="block text-xs text-neutral-600 dark:text-neutral-400 truncate pt-0.5">{{
                                            User_Data.phone }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <!-- Display secondary account details -->
                                <div
                                    class="px-4 py-2.5 flex items-center hover:bg-neutral-100 dark:hover:bg-neutral-700 border-b border-neutral-200 dark:border-neutral-700">
                                    <div class="flex h-[33px] items-center mr-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-user-round flex-shrink-0">
                                            <circle cx="12" cy="8" r="5" />
                                            <path d="M20 21a8 8 0 0 0-16 0" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="flex">
                                            <span
                                                class="text-sm font-medium text-neutral-800 dark:text-neutral-300 block truncate max-w-[16ch]">
                                                {{ User_Data.secondary_user.name }}<span
                                                    class="bg-gray-100 border-gray-100 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-300 border font-medium px-1.5 py-0.5 rounded-md text-[9px] text-neutral-600 ml-1">Sub</span>
                                            </span>
                                        </div>
                                        {% if User_Data.secondary_user.email %}
                                        <span
                                            class="block text-xs text-neutral-600 dark:text-neutral-400 pt-0.5 max-w-[25ch] truncate">{{
                                            User_Data.secondary_user.email }}</span>
                                        {% elif User_Data.secondary_user.phone %}
                                        <span
                                            class="block text-xs text-neutral-600 dark:text-neutral-400 truncate pt-0.5">{{
                                            User_Data.secondary_user.phone }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% else %}
                                <!-- Display primary account details if primary -->
                                <div
                                    class="px-4 py-2.5 hover:bg-neutral-100 dark:hover:bg-neutral-700 cursor-pointer dark:border-neutral-700">
                                    <span
                                        class="block text-sm font-medium text-neutral-900 dark:text-neutral-200 capitalize">{{
                                        User_Data.name }}</span>
                                    {% if User_Data.email %}
                                    <span
                                        class="block text-xs text-neutral-600 dark:text-neutral-400 truncate pt-0.5">{{
                                        User_Data.email }}</span>
                                    {% elif User_Data.phone %}
                                    <span
                                        class="block text-xs text-neutral-600 dark:text-neutral-400 truncate pt-0.5">{{
                                        User_Data.phone }}</span>
                                    {% endif %}
                                </div>
                                {% endif %}
                                <a id="usersButton"
                                    class="flex gap-2 text-sm items-center px-4 py-2.5 cursor-pointer borer-b border-gray-100 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-users">
                                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                                        <circle cx="9" cy="7" r="4" />
                                        <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                                        <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                                    </svg>
                                    Manage Users
                                </a>
                                <a id="personalSettingsButton"
                                    class="flex gap-2 text-sm items-center px-4 border-t py-2.5 cursor-pointer borer-b border-gray-200 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-user">
                                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                                        <circle cx="12" cy="7" r="4" />
                                    </svg>
                                    Personal Settings
                                </a>
                                <a id="demoButton" href="/demo"
                                    class="bg-white flex gap-2 text-sm items-center px-4 border-t py-2.5 cursor-pointer borer-b border-gray-200 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-play-circle">
                                        <circle cx="12" cy="12" r="10" />
                                        <polygon points="10 8 16 12 10 16 10 8" />
                                    </svg>
                                    Demo Videos
                                </a>
                                <button onclick="showContactUsModal('support', generateDynamicMessage())"
                                    class="flex gap-2 text-sm items-center px-4 border-t py-2.5 cursor-pointer borer-b border-gray-200 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-mailbox">
                                        <path
                                            d="M22 17a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9.5C2 7 4 5 6.5 5H18c2.2 0 4 1.8 4 4v8Z" />
                                        <polyline points="15,9 18,9 18,11" />
                                        <path d="M6.5 5C9 5 11 7 11 9.5V17a2 2 0 0 1-2 2" />
                                        <line x1="6" x2="7" y1="10" y2="10" />
                                    </svg>
                                    Contact Us
                                </button>
                                <!-- Feedback -->
                                <button onclick="showFeedbackModal()"
                                    class="flex gap-2 text-sm items-center px-4 border-t py-2.5 cursor-pointer borer-b border-gray-200 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">

                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-message-square-heart">
                                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                                        <path
                                            d="M14.8 7.5a1.84 1.84 0 0 0-2.6 0l-.2.3-.3-.3a1.84 1.84 0 1 0-2.4 2.8L12 13l2.7-2.7c.9-.9.8-2.1.1-2.8" />
                                    </svg>
                                    <span>Feedback</span>
                                </button>

                                <!-- Apps -->
                                <div>
                                    <a href="/apps"
                                        class="flex gap-2 text-sm items-center px-4 border-t py-2.5 cursor-pointer borer-b border-gray-200 dark:border-neutral-500 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-layout-grid-icon lucide-layout-grid">
                                            <rect width="7" height="7" x="3" y="3" rx="1" />
                                            <rect width="7" height="7" x="14" y="3" rx="1" />
                                            <rect width="7" height="7" x="14" y="14" rx="1" />
                                            <rect width="7" height="7" x="3" y="14" rx="1" />
                                        </svg>
                                        <span>Apps</span>
                                    </a>
                                </div>

                                <!-- Toggle Ticker Preference -->
                                <div class="flex items-center justify-between text-sm text-neutral-700 dark:text-neutral-200 cursor-pointer px-4 py-2.5 border-t border-gray-200 dark:border-neutral-500 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full"
                                    onclick="toggleTickerPreference()">
                                    <div class="flex items-center">
                                        <span class="float-child flex-shrink-0 text-neutral-950">Show Ticker</span>
                                    </div>
                                    <div class="relative float-child">
                                        <input type="checkbox" id="ticker-toggle" class="sr-only">
                                        <div id="ticker-toggle-bg"
                                            class="block bg-gray-300 dark:bg-neutral-600 w-[2.25rem] h-5 rounded-full">
                                        </div>
                                        <div id="ticker-toggle-dot"
                                            class="absolute left-1 top-[0.2rem] bg-white w-3.5 h-3.5 rounded-full transition-transform duration-300 ease-in-out">
                                        </div>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between text-sm text-neutral-700 dark:text-neutral-200 cursor-pointer px-4 py-2.5 border-t border-gray-200 dark:border-neutral-500 hover:bg-neutral-100 dark:hover:bg-neutral-700 w-full"
                                    onclick="toggleMenuStylePreference()">
                                    <div class="flex items-center">
                                        <span class="float-child flex-shrink-0 text-neutral-950">Sidebar Menu</span>
                                    </div>
                                    <div class="relative float-child">
                                        <input type="checkbox" id="menu-style-toggle-sidebar" class="sr-only">
                                        <div id="menu-style-toggle-bg-sidebar"
                                            class="block bg-gray-300 dark:bg-neutral-600 w-[2.25rem] h-5 rounded-full">
                                        </div>
                                        <div id="menu-style-dot-sidebar"
                                            class="absolute left-1 top-[0.2rem] bg-white w-3.5 h-3.5 rounded-full transition-transform duration-300 ease-in-out">
                                        </div>
                                    </div>
                                </div>

                                <a href="#" onclick="logout()"
                                    class="flex gap-2 text-sm items-center border-t dark:border-neutral-700 px-4 py-2.5 text-[#f56b62] dark:text-[#ff5448] hover:text-[#ff5448] dark:hover:text-[#ff3a2e] hover:bg-neutral-100 dark:hover:bg-neutral-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-log-out">
                                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                                        <polyline points="16 17 21 12 16 7" />
                                        <line x1="21" x2="9" y1="12" y2="12" />
                                    </svg>
                                    Logout
                                </a>
                            </div>
                        </div>
                        {% else %}
                        <button id="loginButton"
                            class="bg-white border border-neutral-700 cursor-pointer dark:border-neutral-700 dark:hover:bg-neutral-700 dark:lg:text-neutral-200 focus-visible:outline-none font-medium hidden hover:bg-neutral-100 hover:shadow-sm lg:block lg:border-neutral-200 lg:min-h-0 lg:my-0 lg:text-neutral-900 min-h-[46px] my-3 px-4 py-2 rounded-md shadow-sm text-nowrap text-sm text-white transition-colors w-full"
                            onclick="showLoginModal(true)">
                            Login
                        </button>
                        <button id="signupButton"
                            class="lg:w-auto w-full lg:min-h-0 border lg:border-blue-500 dark:border-blue-700 bg-blue-500 dark:bg-blue-800 hover:bg-blue-600 dark:hover:bg-blue-600 transition-colors hover:shadow-sm font-medium text-sm text-white rounded-md px-6 transition-all lg:py-2 border-blue-700 py-1.5 font-medium"
                            onclick="showLoginModal(false)">
                            Signup
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </nav>
    </div>
</header>
<style>
    .font-semibold {
        font-family: poppins-semibold;
    }

    @media (min-width: 768px) and (max-width: 1380px) {

        body,
        .text-sm {
            font-size: 85%;
        }
    }

    /* Hide scrollbar for Chrome, Safari and Opera */
    .menu-custom-scroll::-webkit-scrollbar {
        display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .menu-custom-scroll {
        -ms-overflow-style: none;
        /* IE and Edge */
        scrollbar-width: none;
        /* Firefox */
    }
</style>
<style>
    @media (min-width: 1280px) {

        .static-menu,
        header {
            width: 60px;
        }

        .float-menu,
        header:hover {
            width: 215px;
        }

        .mantra-active-menu.active {
            background: #e0f2fe;
        }


        .carousel-container {
            width: 100%;
            overflow: hidden;
            position: relative;
        }

        .carousel {
            display: flex;
            transition: transform 0.5s ease-in-out;
            /* Set a smooth transition for sliding effect */
        }

        .carousel-item {
            min-width: 100%;
            box-sizing: border-box;
        }
    }

    .noUi-target {
        background: #f2f2f2 !important;
        border-radius: 100px !important;
        height: 5px !important;
    }

    .noUi-horizontal .noUi-handle {
        top: -8px !important;
    }

    .noUi-connect {
        background: #287aff !important;
    }

    .noUi-handle:after,
    .noUi-handle:before {
        height: 7px !important;
        background: #82807f !important;
    }

    .noUi-horizontal .noUi-handle {
        height: 20px !important;
    }

    .noUi-tooltip {
        border-radius: 6px !important;
        color: #000 !important;
        padding: 3px 8px !important;
        font-size: 12px !important;
        box-shadow: 0px -2px 7px #ccc !important;
    }
</style>