<div class="timestamp-component">
    <!-- Will be populated by JavaScript -->
</div>

<script>
    class TimestampComponent {
        constructor(element, options = {}) {
            this.element = element;
            this.options = {
                showRefresh: options.showRefresh || false,
                onRefresh: options.onRefresh || (() => { })
            };
            this.render();
            this.updateTimestamp();
        }

        render() {
            const container = document.createElement('div');
            container.className = 'flex items-center gap-2';

            // Timestamp div
            const timestampDiv = document.createElement('div');
            timestampDiv.className = 'bg-yellow-100 dark:bg-white/5 dark:text-gray-400 inline-block px-2 py-1 rounded text-yellow-900 text-xs';
            timestampDiv.innerHTML = `As on <span class="timestamp-text">loading...</span>`;
            container.appendChild(timestampDiv);

            // Refresh button (if enabled)
            if (this.options.showRefresh) {
                const refreshBtn = document.createElement('button');
                refreshBtn.className = 'text-orange-700 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded hover:bg-black/5';
                refreshBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
            `;
                refreshBtn.addEventListener('click', async () => {
                    // Add loading state to button
                    refreshBtn.disabled = true;
                    refreshBtn.classList.add('opacity-50');

                    try {
                        // Execute refresh callback
                        await Promise.resolve(this.options.onRefresh());
                    } catch (error) {
                        console.error('Refresh error:', error);
                    } finally {
                        // Update timestamp and restore button state
                        this.updateTimestamp();
                        refreshBtn.disabled = false;
                        refreshBtn.classList.remove('opacity-50');
                    }
                });
                container.appendChild(refreshBtn);
            }

            this.element.innerHTML = '';
            this.element.appendChild(container);
        }

        updateTimestamp() {
            const now = new Date();
            const day = String(now.getDate()).padStart(2, '0');
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const month = months[now.getMonth()];
            const year = now.getFullYear();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const timestamp = `${day}-${month}-${year} ${hours}:${minutes}:${seconds} IST`;

            const timestampElement = this.element.querySelector('.timestamp-text');
            if (timestampElement) {
                timestampElement.textContent = timestamp;
            }
        }
    }

    // Create a global function to initialize timestamp components
    window.createTimestamp = function (elementId, options) {
        const element = document.getElementById(elementId);
        if (element) {
            return new TimestampComponent(element, options);
        }
        return null;
    };
</script>