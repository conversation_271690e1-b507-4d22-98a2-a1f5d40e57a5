<script src="https://assets.theaibull.com/js/app-bundle-c.min.js"></script>
<script src="https://assets.theaibull.com/js/app-bundle-m.min.js"></script>
{% include 'blocks/stock-ticker.html' %}

{% include 'blocks/menus/menu.html' %}
{% include 'blocks/menus/top-menu.html' %}
{% include 'blocks/menus/menu-scripts.html' %}
{% include 'blocks/menus/mobile-menu.html' %}
<link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:opsz,wght@12..96,200..800&display=swap" />
{% include 'blocks/storage.html' %} {{ get_block("core/alert", { "menu": menu})
}}
<style>
    .heading {
        font-family: Bricolage Grotesque, sans-serif !important;
    }
</style>
<div class="[&_.lg\:min-h-\[490px\]]:min-h-[550px] [&_.min-h-\[490px\]]:min-h-[550px] ">
    {{ get_block("auth/login") }}
</div>

<!-- Include One Tap -->
{{ get_block("auth/google-one-tap") }} {% if User_Data %} {{
get_block("auth/users") }} {% endif %}

<!-- Include Usage section -->
{% include 'blocks/usage.html' %}

<script>
    // Define app config globally
    const config = {
        appName: "AI Bull",
        subheading: "AI Powered Stocks and Options Trading.",
        logoSrc: "{{ cdn('/static/images/login.png') }}",
        logoAlt: "AI Bull Logo",
    };

    // Function to hide all modals
    function hideAllModals() {
        const modals = document.querySelectorAll(".modal");
        modals.forEach((modal) => modal.classList.add("hidden"));
    }

    // Event listener for Esc key
    document.addEventListener("keydown", function (event) {
        if (event.key === "Escape") {
            hideAllModals();
        }
    });

    // If modal open body scroll fixed
    const modals = document.querySelectorAll(".modal");
    const body = document.body;

    function updateBodyScroll() {
        const openModal = Array.from(modals).find(
            (modal) => !modal.classList.contains("hidden")
        );

        if (openModal) {
            body.style.overflow = "hidden";
        } else {
            body.style.overflow = "auto";
        }
    }
    // Initial call to set the correct state
    updateBodyScroll();

    // Add event listeners to update scroll when modal visibility changes
    modals.forEach((modal) => {
        const observer = new MutationObserver(updateBodyScroll);
        observer.observe(modal, {
            attributes: true,
            attributeFilter: ["class"],
        });
    });

    // Generic confirmation dialog
    async function showConfirmDialog(options = {}) {
        const {
            title = 'Confirm Action',
            message = 'Are you sure you want to proceed?',
            confirmText = 'Confirm',
            cancelText = 'Cancel',
            type = 'warning' // warning, danger, info
        } = options;

        // Create dialog element
        const dialog = document.createElement('div');
        dialog.className = 'fixed inset-0 z-50 overflow-y-auto';
        dialog.innerHTML = `
        <div class="flex min-h-screen items-center justify-center p-4 text-center sm:p-0">
            <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
            
            <div class="relative transform overflow-hidden rounded-xl bg-white dark:bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
                <!-- Header -->
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-neutral-900 text-white">
                    <div class="flex justify-between items-center">
                        <h3 class="text-xl font-bold">${title}</h3>
                    </div>
                </div>

                <!-- Content -->
                <div class="px-4 py-4">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="flex h-10 w-10 items-center justify-center rounded-full ${type === 'danger' ? 'bg-red-100 dark:bg-red-900' :
                type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900' :
                    'bg-blue-100 dark:bg-blue-900'
            }">
                                <svg class="h-6 w-6 ${type === 'danger' ? 'text-red-600 dark:text-red-400' :
                type === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
                    'text-blue-600 dark:text-blue-400'
            }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-700 dark:text-gray-300">${message}</p>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="px-4 py-3 bg-gray-100 dark:bg-gray-700 flex justify-end gap-3">
                    <button type="button" class="cancel-btn justify-center flex-shrink-0 px-4 py-1.5 text-sm font-medium dark:text-neutral-200 border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 hover:bg-gray-100 hover:shadow-sm rounded-md text-neutral-900 transition-all">
                        ${cancelText}
                    </button>
                    <button type="button" class="confirm-btn justify-center flex-shrink-0 px-4 py-1.5 text-sm font-medium text-white ${type === 'danger' ? 'bg-red-600 hover:bg-red-700' :
                type === 'warning' ? 'bg-yellow-600 hover:bg-yellow-700' :
                    'bg-blue-600 hover:bg-blue-700'
            } rounded-md transition-all">
                        ${confirmText}
                    </button>
                </div>
            </div>
        </div>
    `;

        // Add to document
        document.body.appendChild(dialog);

        // Return promise that resolves when user makes a choice
        return new Promise((resolve) => {
            const confirmBtn = dialog.querySelector('.confirm-btn');
            const cancelBtn = dialog.querySelector('.cancel-btn');

            // Define handlers
            const keydownHandler = function (e) {
                if (e.key === 'Escape') {
                    cleanup();
                    resolve(false);
                }
            };

            const clickHandler = function (e) {
                if (e.target === dialog) {
                    cleanup();
                    resolve(false);
                }
            };

            const confirmHandler = function () {
                cleanup();
                resolve(true);
            };

            const cancelHandler = function () {
                cleanup();
                resolve(false);
            };

            function cleanup() {
                // Remove all event listeners
                document.removeEventListener('keydown', keydownHandler);
                dialog.removeEventListener('click', clickHandler);
                confirmBtn.removeEventListener('click', confirmHandler);
                cancelBtn.removeEventListener('click', cancelHandler);
                // Remove dialog from DOM
                dialog.remove();
            }

            // Add event listeners
            document.addEventListener('keydown', keydownHandler);
            dialog.addEventListener('click', clickHandler);
            confirmBtn.addEventListener('click', confirmHandler);
            cancelBtn.addEventListener('click', cancelHandler);
        });
    }


    // Function to show a loading spinner
    function showLoadingSpinner(sectionId = 'right-section', message) {
        const section = document.getElementById(sectionId);

        if (!section) return; // Exit early if the section doesn't exist

        let loader = section.querySelector('.loading-spinner');

        if (!loader) {
            // Create loader only if it doesn't exist
            loader = document.createElement('div');
            loader.className = 'loading-spinner flex flex-col items-center justify-center p-4 text-center text-gray-600 min-h-[20px]';
            loader.innerHTML = `
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 border-4 border-blue-500 border-dashed rounded-full animate-spin"></div>
                    <span id="spinner-message" class="mt-4 text-sm font-medium text-gray-700">${message}</span>
                </div>
            `;
            section.appendChild(loader);
        } else {
            // If loader exists, just update the message and make sure it's visible
            const messageElement = loader.querySelector('#spinner-message');
            if (messageElement) {
                messageElement.textContent = message;
            }
            loader.classList.remove('hidden'); // Show the loader
        }
    }

    // Function to hide the loading spinner
    function hideLoadingSpinner(sectionId = 'right-section') {
        const section = document.getElementById(sectionId);

        if (section) {
            const loader = section.querySelector('.loading-spinner');
            if (loader) {
                loader.classList.add('hidden'); // Hide the loader
            }
        }
    }

    // Format a number to Indian numbering system using Intl.NumberFormat.
    function formatIndianNumber(num) {
        return new Intl.NumberFormat('en-IN', { maximumFractionDigits: 2 }).format(num);
    }

</script>
{% include 'blocks/google-signup.html' %}
<script>
    const loginModal = document.getElementById("starkAuthForm");
    if (loginModal) {
        const createElement = (tag, idName = "", innerHTML = "") => {
            const elem = document.createElement(tag);
            if (idName) elem.id = idName;
            if (innerHTML) elem.innerHTML = innerHTML;
            renderGoogleButton("g_id_signin_login");
            return elem;
        };

        const container = createElement("div", "google-signin-container", `
            <div class="relative">
                <div class="absolute inset-0 flex items-center" aria-hidden="true">
                    <div class="w-full border-t border-gray-200"></div>
                </div>
                <div class="relative flex justify-center text-sm/6 font-medium">
                    <span class="bg-white px-6 text-gray-900">Or</span>
                </div>
            </div>
            <div id="g_id_signin_login" class="g_id_signin_login mt-[12px] mb-1.5 flex mx-auto justify-center [&_.nsm7Bb-HzV7m-LgbsSe]:h-[53px] [&_.nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf]:ml-[0px]"
                data-type="standard" data-shape="rectangular" data-theme="filled_blue"
                data-text="signin_with" data-size="large" data-logo_alignment="left">
            </div>
        `);

        if (!loginModal.querySelector("#google-signin-container")) {
            loginModal.querySelector("#switchModeText")?.before(container);
        }
        renderGoogleButton("g_id_signin_login");
    }

    // Loader container below the slots
    function showCompareLoading(elementId, message) {
        // Get the container for comparison slots
        const slotsContainer = document.getElementById(elementId);
        if (!slotsContainer) return;

        const loaderContainer = document.createElement('div');
        loaderContainer.className = 'flex items-center justify-center mt-6';
        loaderContainer.innerHTML = `
                <div class="flex items-center space-x-4 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-5 animate-pulse mb-5">
                    <svg class="animate-spin h-7 w-7 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-base font-semibold text-gray-800 dark:text-gray-200">${message}</span>
                </div>
            `;
        slotsContainer.insertAdjacentElement('afterend', loaderContainer);

        const slots = document.querySelectorAll('.comparison-slot');
        slots.forEach(slot => {
            const addBtn = slot.querySelector('.add-btn');
            if (addBtn) addBtn.remove(); // Remove the "+" button
        });

        if (compareBtn) {
            compareBtn.disabled = true;
            compareBtn.classList.add('opacity-50', 'cursor-not-allowed');
        }

        // Smoothly fade out and remove the comparison table
        const comparisonSection = document.querySelector('.compare-section');
        if (comparisonSection) {
            comparisonSection.style.transition = 'opacity 0.4s ease-out';
            comparisonSection.style.opacity = '0';
            setTimeout(() => {
                comparisonSection.remove(); // Remove table after fade-out
            }, 400);
        }
    }

    // For scroll and buttons functionality
    function initializeCardsCarousel(containerId, leftArrowId, rightArrowId) {
        const container = document.getElementById(containerId);
        const leftArrow = document.getElementById(leftArrowId);
        const rightArrow = document.getElementById(rightArrowId);
        
        if (!container || !leftArrow || !rightArrow) return;
        
        const cardWidth = container.querySelector('.flex-shrink-0')?.offsetWidth || 0;

        leftArrow.addEventListener('click', () => {
            container.scrollBy({ left: -cardWidth, behavior: 'smooth' });
        });

        rightArrow.addEventListener('click', () => {
            container.scrollBy({ left: cardWidth, behavior: 'smooth' });
        });

        container.addEventListener('wheel', (e) => {
            e.preventDefault();
            const scrollAmount = e.deltaY;
            container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
        });
    }
</script>