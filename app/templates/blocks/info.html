<!-- Modal Structure -->
<div id="modal" class="hidden fixed inset-0 bg-gray-800 bg-opacity-50 z-50 flex items-center justify-center">
    <div class="max-w-xl bg-gradient-to-tr from-pink-300 mb-3 p-[5px] relative rounded-xl to-blue-400">
        <div class="bg-white p-6 relative rounded-md">

            <div>
                <div class="relative">
                    <h2 class="lg:text-xl text-base font-semibold text-gray-800 text-2xl md:text-3xl leading-tight" id="modalHeading"></h2>
                    <h2 id="modalDescription" class="mt-2 text-gray-600 md:text-sm text-xs lg:text-base leading-5"></h2>
                </div>
                <div class="absolute top-5 right-5">
                    <button onclick="closeModal()" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-x">
                            <path d="M18 6 6 18" />
                            <path d="m6 6 12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Button to trigger modal open usage -->
<button id="openModalBtn" class="hidden fixed bottom-4 right-4 bg-blue-500 text-white p-3 rounded-full"
    onclick="openModal()">Open Modal</button>

<script>
    // Function to open the modal
    function openModal() {
        document.getElementById("modal").classList.remove("hidden"); // Show the modal

        const pageKey = window.location.pathname; // Get the current page path
        const pageName = pageKey.split("/").pop().split(".")[0];

        const data = []; // Initialize data array
        if (pageName === 'chain') {
            data.push({
                "heading": "How to Use This Page",
                "description": "This tool helps you efficiently analyze options by toggling between call and put options, customizing strike prices and expiration dates, and reviewing key data points to make informed trading decisions."
            });
        }
        else if (pageName === 'strike-scanner') {
            data.push({
                "heading": "How to Use This Page",
                "description": "This tool helps you analyze options by selecting an asset, adjusting the desired Delta range, and identifying optimal covered call opportunities. You can then review the results based on Delta values, premiums, and strike prices to make informed trading decisions."
            });
        }
        else if (pageName === 'spread-analyzer') {
            data.push({
                "heading": "How to Use This Page",
                "description": "This tool helps you analyze Bull and Bear spreads by selecting multiple strike prices for an underlying asset. It compares premiums, risks, and rewards for each spread, helping you make informed decisions to optimize your options trading strategy."
            });
        }
        else if (pageName === 'straddle-scanner') {
            data.push({
                "heading": "How to Use This Page",
                "description": "To use the scanner, enter a stock symbol and set the purchase price and minimum premium percentage. The scanner displays available options with varying strike prices, expiration dates, volume, bid/ask prices, premium percentages, and profit potential. This helps identify profitable covered call opportunities for trading."
            });
        }
        else if (pageName === 'covered-call') {
            data.push({
                "heading": "How to Use This Page",
                "description": "To use the scanner, enter a stock symbol and set the purchase price and minimum premium percentage. The scanner displays available options with varying strike prices, expiration dates, volume, bid/ask prices, premium percentages, and profit potential. This helps identify profitable covered call opportunities for trading."
            });
        }
        else if (pageName === 'cash-secured-put') {
            data.push({
                "heading": "How to Use This Page",
                "description": "The purpose of the Cash Secured Put Scanner is to help investors identify the best cash-secured put opportunities by filtering stocks based on criteria like target price, minimum discount, and probability. This tool uses AI-powered analysis to simplify the selection process."
            });
        }
        else if (pageName === 'futures-arbitrage') {
            data.push({
                "heading": "How to Use This Page",
                "description": "This tool helps you identify arbitrage opportunities between stocks and futures. Use the filter box to set the minimum percentage difference you want to see. The table below will update automatically to show only the opportunities that meet your criteria. You can also use this tool for strategies like Put Call Parity (see our Put Call Parity page) or for long stock/short futures setups."
            });
        }
        else if (pageName === 'exchanges-arbitrage') {
            data.push({
                "heading": "How to Use This Page",
                "description": "This tool helps you identify arbitrage opportunities between the NSE and BSE exchanges. Use the filter box to set the minimum percentage difference you want to see.Note: Exchanges are not available for day trading; you should hold the stock on at least one exchange to perform these arbitrages. The recommended action is calculated based on the price differences between the two exchanges."
            });
        }
        else if (pageName === 'put-call-parity') {
            data.push({
                "heading": "How to Use This Page",
                "description": "The scanner helps identify arbitrage opportunities by analyzing discrepancies between put and call options. To use, enter a stock symbol, choose indices or stocks, and click 'Search' or 'Scan All Stocks' to discover potential mispriced options in the market."
            });
        }
        else if (pageName === 'box-spread-arbitrage') {
            data.push({
                "heading": "How to Use This Page",
                "description": "Enter a stock symbol, select your preferred parameters, and click 'Search' or 'Scan All Stocks.' The scanner will identify box spread arbitrage opportunities by comparing the prices of the options' positions, helping you spot mispriced spreads."
            });
        }
        else if (pageName === 'backtesting') {
            data.push({
                "heading": "How to Use This Page",
                "description": "To perform a backtest, select your trading strategy from the dropdown and choose the test year below. Click 'Run Backtest' to fetch the necessary data for analysis."
            });
        }
        else if (pageName === 'screener') {
            data.push({
                "heading": "How to Use This Page",
                "description": "To use the Stock Screener, select your desired criteria like sector, market cap, debt-to-equity, and beta. Apply filters to generate results, analyze stock data, and save or export your findings. Refine filters as needed for better opportunities."
            });
        }
        else if (pageName === 'straddle-graph') {
            data.push({
                "heading": "How to Use This Page",
                "description": "To use the Straddle Analysis page, enter a symbol in the search field, select an expiration date and strike, then click 'Generate Graph' to view the analysis and graph results."
            });
        }
        else if (pageName === 'mutual-funds') {
            data.push({
                "heading": "How to Use the Mutual Fund Screener",
                "description": "To use the Mutual Fund Screener, apply filters such as category, sub-category, fund house, NAV range, or fund size using the sidebar. Sort results by clicking column headers like Scheme Name or NAV. Click on a fund row to view detailed information."
            });
        }
        else {
            // Default data for any other page
            data.push({
                "heading": "Default Page Heading",
                "description": "This is the default description for the page."
            });
        }

        // Use the data to populate the modal
        document.getElementById("modalHeading").textContent = data[0].heading;
        document.getElementById("modalDescription").textContent = data[0].description;

    }

    // Function to close the modal and set it in sessionStorage for the specific page
    function closeModal() {
        const pageKey = window.location.pathname; // Get the current page path as the unique key
        document.getElementById("modal").classList.add("hidden"); // Hide the modal
        sessionStorage.setItem(pageKey + "_modalClosed", "true"); // Save the closed state for this page
    }

</script>
{%include 'blocks/onboarding.html' %}