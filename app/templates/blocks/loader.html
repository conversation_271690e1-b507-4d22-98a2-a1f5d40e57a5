<div id="loader"
    class="hidden fixed inset-0 z-50 flex items-center justify-center bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm">
    <div
        class="bg-white dark:bg-neutral-800 rounded-lg p-6 shadow-xl border border-neutral-200 dark:border-neutral-700 flex flex-col items-center gap-4">
        <img src="/static/gifs/bull-market.gif" alt="Loading..." class="w-32 h-32 rounded-lg">
        <div class="text-center">
            <div class="text-lg font-semibold text-neutral-800 dark:text-neutral-200">Loading Market Data</div>
            <div class="text-sm text-neutral-600 dark:text-neutral-400">Please wait while we fetch the latest option
                chain data...</div>
        </div>
    </div>
</div>

<script>
    const loader = {
        show: () => {
            document.getElementById('loader').classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // Prevent scrolling while loading
        },
        hide: () => {
            document.getElementById('loader').classList.add('hidden');
            document.body.style.overflow = ''; // Restore scrolling
        }
    };
</script>