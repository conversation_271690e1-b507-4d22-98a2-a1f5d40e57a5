<div class="py-24">
    <div class="max-w-7xl mx-auto px-4">
        <div>
            <h3 class="font-bold heading lg:text-4xl text-2xl text-neutral-900 tracking-tight mb-2 capitalize">Explore
                Global Trading Opportunities with The AI Bull</h3>
            <p class="max-w-4xl text-neutral-600 lg:text-lg text-sm leading-6 leading-6">Discover how AI-powered trading
                tools can enhance your trading strategies, optimize performance, and boost decision-making with The AI
                Bull.</p>
        </div>
        <div class="grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 mt-8 gap-[30px]">
            {% for blog in blogs %}
            {% if loop.index <= 3 %} <div
                class="group relative overflow-hidden bg-white rounded-md border hover:shadow-lg">
                <div class="flex-shrink-0">
                    <a href="/blogs/{{ blog.slug }}">
                        <img class="h-48 w-full object-cover"
                            src="{{ blog.image1_url or blog.image2_url or blog.image3_url }}"
                            alt="{{ blog.meta_title }}">
                    </a>
                </div>
                <div class="flex flex-1 flex-col justify-between bg-white p-6">
                    <div class="flex-1">
                        <a href="/blogs/{{ blog.slug }}" class="mt-2 block">
                            <p class="text-xl font-semibold text-gray-900 heading line-clamp-2">
                                {{ blog.meta_title }}
                            </p>    
                            <p class="mt-3 text-base text-neutral-500 line-clamp-3">
                                {{ blog.meta_description }}
                            </p>
                        </a>
                    </div>

                    <!-- Author and Date Info for Homepage Blogs -->
                    {% if blog.author_name or blog.publish_date %}
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <div class="flex items-center gap-3">
                            {% if blog.author_image_url %}
                            <img src="{{ blog.author_image_url }}" alt="{{ blog.author_name or 'Author' }}"
                                class="w-6 h-6 rounded-full object-cover"
                                onerror="this.src='https://ui-avatars.com/api/?name={{ (blog.author_name or 'Author') | urlencode }}&background=6366f1&color=fff&size=24'">
                            {% endif %}
                            <div class="flex-1 min-w-0">
                                {% if blog.author_name %}
                                <p class="text-xs font-medium text-gray-900 truncate">{{ blog.author_name }}</p>
                                {% endif %}
                                {% if blog.publish_date %}
                                <p class="text-xs text-gray-500">{{ blog.publish_date[:10] | replace('-', '/') }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="mt-4">
                        <a href="/blogs/{{ blog.slug }}"
                            class="font-medium hover:text-[#e3ab04] text-[13px] text-neutral-700 flex gap-2 items-center">
                            Read More
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-move-right">
                                <path d="M18 8L22 12L18 16"></path>
                                <path d="M2 12H22"></path>
                            </svg>
                        </a>
                    </div>
                </div>
        </div>
        {% endif %}
        {% else %}
        <div class="col-span-3 text-center py-10">
            <p class="text-neutral-600">No blogs available at the moment. Check back soon!</p>
        </div>
        {% endfor %}
    </div>
    <div class="mt-10 text-center">
        <a href="/blogs" class="block group inline-block relative text-center text-lg">
            <span
                class="relative z-10 block px-5 py-3 overflow-hidden font-medium leading-tight text-gray-800 transition-colors duration-300 ease-out border-2 border-gray-900 rounded-lg group-hover:text-white">
                <span class="absolute inset-0 w-full h-full px-5 py-3 rounded-lg bg-gray-50"></span>
                <span
                    class="absolute left-0 w-64 h-64 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-gray-900 group-hover:-rotate-180 ease"></span>
                <span class="relative">Read More Blogs</span>
            </span>
            <span
                class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-gray-900 rounded-lg group-hover:mb-0 group-hover:mr-0"
                data-rounded="rounded-lg"></span>
        </a>
    </div>
</div>
</div>