<div class="bg-neutral-50 p-3">
    <div
        class="bg-neutral-950 bg-[url(https://employers.stark.ai/static/images/website/footer-bg.avif)] bg-cover bg-no-repeat md:text-center pt-6 md:pt-16  px-0 rounded-2xl">
        <div class="px-5 lg:px-0 pb-16">
            <h3 class="md:text-6xl text-3xl font-bold text-white mb-4 heading">Get Started Now</h3>
            <p class="max-w-2xl mb-8 mx-auto text-gray-200 text-lg">Unlock the power of AI-powered trading with The
                AIBull -
                maximize your returns, optimize strategies, and trade smarter.</p>
            <div
                class="flex flex-col max-w-4xl mx-auto flex-wrap font-medium gap-4 justify-center leading-7 md:flex-row mt-8 text-[#dadada] text-sm">
                <div
                    class="bg-transparent border border-neutral-800 font-medium px-4 py-2 relative rounded-full text-sm">
                    <span>AI-Powered Trading Tools</span>
                </div>
                <div
                    class="bg-transparent border border-neutral-800 font-medium px-4 py-2 relative rounded-full text-sm">
                    Real-Time Market Analysis
                </div>
                <div
                    class="bg-transparent border border-neutral-800 font-medium px-4 py-2 relative rounded-full text-sm">
                    Backtesting with Historical Data
                </div>
                <div
                    class="bg-transparent border border-neutral-800 font-medium px-4 py-2 relative rounded-full text-sm">
                    Automated Strategy Optimization
                </div>
                <div
                    class="bg-transparent border border-neutral-800 font-medium px-4 py-2 relative rounded-full text-sm">
                    Exclusive Market Insights
                </div>
                <div class="bg-transparent border border-neutral-800 font-medium px-4 py-2 relative rounded-full text-sm"
                    style="
        transition: background 2s;
        border: 1px solid transparent;
        background-image: linear-gradient(45deg, #005446, #39003f, #590000), linear-gradient(45deg, #007a65, #7f0e7f, #ff8983);
        background-clip: padding-box, border-box;
        background-origin: border-box, border-box;
    ">

                    All Absolutely Free!
                </div>
            </div>
        </div>
        <div class="bg-neutral-950 rounded-b-2xl">
            <section class="wrapper !bg-neutral-950 grid font-bold uppercase text-white rounded-b-2xl">
                <div class="top">AIBULL</div>
                <div class="bottom" aria-hidden="true">AIBULL</div>
            </section>

            <div
                class="max-w-6xl mx-auto px-0 text-center flex md:flex-row flex-col justify-between items-center py-4 border-t border-neutral-800 mt-3">
                <p class="text-sm text-neutral-300">© 2025 MantraReal Technologies Pvt Ltd. All rights
                    reserved.</p>
                <div class="my-1 text-sm text-neutral-300">
                    <a href="/privacy-policy" target="_blank">Privacy Policy</a>
                    <a href="/terms-conditions" target="_blank">| Terms and Conditions</a>

                </div>
                <div class="flex items-center mt-4 space-x-3 sm:mt-0">
                    <a href="#" rel="noopener noreferrer" target="_blank"
                        class="h-[35px] w-[35px] card-white hover:bg-[#007bb6] border border-neutral-700 hover:border-[#007bb6] text-white/60 hover:text-white hover:shadow-lg flex items-center justify-center rounded-[5px]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-linkedin">
                            <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z">
                            </path>
                            <rect width="4" height="12" x="2" y="9"></rect>
                            <circle cx="4" cy="4" r="2"></circle>
                        </svg>
                    </a>
                    <a href="#" rel="noopener noreferrer" target="_blank"
                        class="h-[35px] w-[35px] card-white hover:bg-neutral-950 border border-neutral-700 hover:border-neutral-950 text-white/60 hover:text-white hover:shadow-lg flex items-center justify-center rounded-[5px]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-twitter">
                            <path
                                d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z">
                            </path>
                        </svg>
                    </a>
                    <a href="#" rel="noopener noreferrer" target="_blank"
                        class="h-[35px] w-[35px] card-white hover:bg-[#3b5998] border border-neutral-700 hover:border-[#3b5998] text-white/60 hover:text-white hover:shadow-lg flex items-center justify-center rounded-[5px]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-facebook">
                            <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                        </svg>
                    </a>
                    <a href="#" rel="noopener noreferrer" target="_blank"
                        class="h-[35px] w-[35px] card-white bg-gradient-to-r hover:from-pink-500 hover:to-orange-500 border border-neutral-700 text-white/60 hover:border-pink-500 hover:text-white hover:shadow-lg flex items-center justify-center rounded-[5px]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-instagram">
                            <rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect>
                            <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                            <line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    @import url('https://fonts.googleapis.com/css2?family=Oswald:wght@200..700&display=swap');

    .wrapper {
        display: grid;
        place-content: center;
        background-color: black;
        font-family: "Oswald", sans-serif;
        font-size: clamp(1.5rem, 1rem + 18vw, 15rem);
    }

    .wrapper>div {
        grid-area: 1/1/-1/-1;
    }

    .top {
        clip-path: polygon(0% 0%, 100% 0%, 100% 48%, 0% 58%);
    }

    .bottom {
        clip-path: polygon(0% 60%, 100% 50%, 100% 100%, 0% 100%);
        color: transparent;
        background: -webkit-linear-gradient(177deg, black 53%, white 65%);
        background: linear-gradient(177deg, black 53%, white 65%);
        background-clip: text;
        -webkit-background-clip: text;
        transform: translateX(-0.02em);
    }
</style>