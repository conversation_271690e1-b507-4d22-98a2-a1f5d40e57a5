<div class="relative py-24" style="
    background-image: linear-gradient(114deg, #fffbf9 45.73%, #f5f7ff 75.72%);
">
    <div class="flex flex-col items-center justify-between lg:flex-row max-w-7xl mx-auto pb-5 px-4 relative">
        <div>
            <h2
                class="heading font-[900] lg:tracking-tight md:leading-[1.2] md:text-5xl text-3xl text-neutral-800 tracking-tight mb-2">
                What Our Users Say</h2>
            <p class="text-lg font-medium text-gray-600 mb-12">Discover how AIBull has transformed the trading
                experience
                for our users.</p>
        </div>
        <div>
            <!-- Previous Button -->
            <button aria-label="Previous testimonial"
                class="carousel-left  border z-30 h-10 w-10 transform bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                onclick="moveSlide(-1)">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-move-left-icon lucide-move-left">
                    <path d="M6 8L2 12L6 16" />
                    <path d="M2 12H22" />
                </svg>
            </button>
            <!-- Next Button -->
            <button aria-label="Next testimonial"
                class="carousel-right transform h-10 w-10 border bg-white text-gray-500 hover:text-gray-800 p-2 rounded-full shadow-lg"
                onclick="moveSlide(1)">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-move-right-icon lucide-move-right">
                    <path d="M18 8L22 12L18 16" />
                    <path d="M2 12H22" />
                </svg>
            </button>
        </div>
    </div>

    <section class="carousel-container relative w-full h-full overflow-hidden">
        <div class="flex relative w-full h-full">
            <!-- Carousel Content -->
            <div class="carousel-content max-w-7xl flex transition-transform duration-500 ease-in-out w-full">
                <!-- Testimonial Items -->
                <div
                    class="carousel-item text-blue-900 bg-blue-50 lg:p-24 lg:mx-4  p-8 mx-2 rounded-[3rem] text-center">
                    <blockquote class="text-xl font-medium">
                        "The AI-driven trading strategies from AIBull helped me optimize my options trading. It’s
                        like
                        having a personal
                        advisor that works 24/7."
                    </blockquote>
                    <figcaption class="flex items-center mt-4">
                        <div class="flex items-center gap-2">
                            <span class="font-bold">John D.</span>
                            <p class="text-sm text-gray-700">Investor</p>
                        </div>
                    </figcaption>
                </div>

                <div
                    class="carousel-item text-green-900 bg-green-50 lg:p-24 lg:mx-4  p-8 mx-2 rounded-[3rem] text-center">
                    <blockquote class="text-xl font-medium">
                        "I’ve seen incredible results after integrating AiBull’s backtesting into my strategy. It
                        gave
                        me the confidence
                        to execute trades in real-time."
                    </blockquote>
                    <figcaption class="flex items-center mt-4">

                        <div class="flex items-center gap-2">
                            <span class="font-bold">Sarah M.</span>
                            <p class="text-sm text-gray-700">Trader</p>
                        </div>
                    </figcaption>
                </div>

                <div
                    class="carousel-item text-violet-900 bg-violet-50 lg:p-24 lg:mx-4  p-8 mx-2 rounded-[3rem] text-center">
                    <blockquote class="text-xl font-medium">
                        "AiBull’s scalping tool has completely changed how we approach high-frequency trading. It’s
                        automated, fast, and
                        incredibly efficient."
                    </blockquote>
                    <figcaption class="flex items-center mt-4">
                        <div class="flex items-center gap-2">
                            <span class="font-bold">Michael R.</span>
                            <p class="text-sm text-gray-700">Hedge Fund Manager</p>
                        </div>
                    </figcaption>
                </div>

                <div
                    class="carousel-item text-amber-900 bg-amber-50 lg:p-24 lg:mx-4  p-8 mx-2 rounded-[3rem] text-center">
                    <blockquote class="text-xl font-medium">
                        "As a beginner, AiBull’s simple interface and powerful tools helped me understand complex
                        trading strategies,
                        improving my overall returns."
                    </blockquote>
                    <figcaption class="flex items-center mt-4">
                        <div class="flex items-center gap-2">
                            <span class="font-bold">Laura B.</span>
                            <p class="text-sm text-gray-700">Retail Investor</p>
                        </div>
                    </figcaption>
                </div>

                <div class="carousel-item text-sky-900 bg-sky-50 lg:p-24 lg:mx-4  p-8 mx-2 rounded-[3rem] text-center">
                    <blockquote class="text-xl font-medium">
                        "Using AiBull’s strike scanner has taken my options trading to the next level. It’s much
                        faster
                        and more
                        efficient than any manual method."
                    </blockquote>
                    <figcaption class="flex items-center mt-4">
                        <div class="flex items-center gap-2">
                            <span class="font-bold">Alex P.</span>
                            <p class="text-sm text-gray-700">Options Trader</p>
                        </div>
                    </figcaption>
                </div>

                <div
                    class="carousel-item text-fuchsia-900 bg-fuchsia-50 lg:p-24 lg:mx-4  p-8 mx-2 rounded-[3rem] text-center">
                    <blockquote class="text-xl font-medium">
                        "AiBull’s spread analyzer helps me identify the best strategies for bull and bear spreads
                        with
                        detailed insights
                        and calculations."
                    </blockquote>
                    <figcaption class="flex items-center mt-4">
                        <div class="flex items-center gap-2">
                            <span class="font-bold">Ethan J.</span>
                            <p class="text-sm text-gray-700">Trader</p>
                        </div>
                    </figcaption>
                </div>

                <div
                    class="carousel-item text-teal-900 bg-teal-50 lg:p-24 lg:mx-4  p-8 mx-2 rounded-[3rem] text-center">
                    <blockquote class="text-xl font-medium">
                        "AIBull has helped me learn to trade smarter, and now I can confidently use cash-secured
                        puts to
                        generate
                        passive income."
                    </blockquote>
                    <figcaption class="flex items-center mt-4">
                        <div class="flex items-center gap-2">
                            <span class="font-bold">Sophia L.</span>
                            <p class="text-sm text-gray-700">Investor</p>
                        </div>
                    </figcaption>
                </div>

                <div
                    class="carousel-item text-blue-900 bg-blue-50 lg:p-24 lg:mx-4  p-8 mx-2 rounded-[3rem] text-center">
                    <blockquote class="text-xl font-medium">
                        "The futures arbitrage tool has been a game-changer for me, helping me profit from price
                        discrepancies with
                        minimal risk."
                    </blockquote>
                    <figcaption class="flex items-center mt-4">
                        <div class="flex items-center gap-2">
                            <span class="font-bold">David S.</span>
                            <p class="text-sm text-gray-700">Hedge Fund Analyst</p>
                        </div>
                    </figcaption>
                </div>

                <div class="carousel-item text-red-900 bg-red-50 lg:p-24 lg:mx-4  p-8 mx-2 rounded-[3rem] text-center">
                    <blockquote class="text-xl font-medium">
                        "AiBull’s live AI agent gives me real-time trading advice that helps me make smarter
                        decisions
                        faster than I
                        could on my own."
                    </blockquote>
                    <figcaption class="flex items-center mt-4">
                        <div class="flex items-center gap-2">
                            <span class="font-bold">Rachel T.</span>
                            <p class="text-sm text-gray-700">Day Trader</p>
                        </div>
                    </figcaption>
                </div>

            </div>


        </div>
    </section>
</div>

<script>
    let currentSlide = 0;
    const totalSlides = document.querySelectorAll('.carousel-item').length;
    const carouselContent = document.querySelector('.carousel-content');

    // Move to the next or previous slide
    function moveSlide(direction) {
        currentSlide += direction;

        if (currentSlide < 0) {
            currentSlide = totalSlides - 1; // Loop to the last slide
        } else if (currentSlide >= totalSlides) {
            currentSlide = 0; // Loop to the first slide
        }

        updateCarouselPosition();
    }

    // Update the position of the carousel
    function updateCarouselPosition() {
        // Adjust the move amount based on the screen size
        const transformValue = -currentSlide * 50; // Move the carousel by 50% per slide
        carouselContent.style.transform = `translateX(${transformValue}%)`;
    }

    // Auto carousel functionality
    setInterval(() => {
        moveSlide(1); // Automatically move to the next slide
    }, 5000); // Change slide every 5 seconds
</script>

<style>
    /* General styling for carousel */
    .carousel-container {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .carousel-content {
        display: flex;
        transition: transform 0.5s ease-in-out;
    }

    .carousel-item {
        flex: 0 0 60%;
        /* By default, show 2 slides at once */
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    /* Media Query for mobile screens */
    @media (max-width: 768px) {
        .carousel-item {
            flex: 0 0 100%;
            /* Show 1 slide at a time for mobile */
        }
    }
</style>