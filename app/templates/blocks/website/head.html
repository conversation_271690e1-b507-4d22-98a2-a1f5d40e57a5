<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<link href="{{ cdn('/static/images/ai-bull-5.png') }}" rel="icon" type="image/x-icon" />
<!-- Include Tailwind CSS -->
{{ get_block("core/css" , { "APP_DEBUG" : APP_DEBUG, "DEPLOYMENT_HASH":
DEPLOYMENT_HASH}) }}
<script src="https://assets.theaibull.com/js/app-bundle-c.min.js"></script>
<script src="https://assets.theaibull.com/js/app-bundle-m.min.js"></script>
{% include 'blocks/storage.html' %} {{ get_block("core/alert", { "menu": menu})
}}
<div class="[&_.lg\:min-h-\[490px\]]:min-h-[550px] [&_.min-h-\[490px\]]:min-h-[550px] ">
    {{
    get_block("auth/login") }}</div>

<!-- Include One Tap -->
{{ get_block("auth/google-one-tap") }} {% if User_Data %} {{
get_block("auth/users") }} {% endif %} {% include 'blocks/stock-ticker.html' %}

<!-- Include Usage section -->
{% include 'blocks/usage.html' %}
<script>
    // Define app config globally
    const config = {
        appName: "AI Bull",
        subheading: "AI Powered Stocks and Options Trading.",
        logoSrc: "{{ cdn('/static/images/login.png') }}",
        logoAlt: "AI Bull Logo",
    };

    window.Mantra_App_Name = "ai_bull";
    window.mantraAppParameterValue = "ai_bull";
    window.Mantra_AI_Server = "https://api.theaibull.com";
    window.Mantra_Apps_Server = "https://apps.theaibull.com";
    window.Mantra_Google_Client_Id = "224531985735-maikd6b0mof3mn8fgurqt97mkg7g4tgj.apps.googleusercontent.com";
</script>

<script>
    // used scoped function and variable
    "use strict";
    (function () {
        // On page load or when changing themes, best to add inline in `head` to avoid FOUC
        if (
            localStorage.theme === "light" ||
            (!("theme" in localStorage) &&
                window.matchMedia("(prefers-color-scheme: light)").matches)
        ) {
            document.documentElement.classList.add("light");
        } else {
            document.documentElement.classList.remove("light");
        }
    })();
</script>

{% include 'blocks/google-signup.html' %}
<script>
    const loginModal = document.getElementById("starkAuthForm");
    if (loginModal) {
        const createElement = (tag, idName = "", innerHTML = "") => {
            const elem = document.createElement(tag);
            if (idName) elem.id = idName;
            if (innerHTML) elem.innerHTML = innerHTML;
            renderGoogleButton("g_id_signin_signup");
            return elem;
        };

        const container = createElement("div", "google-signin-container", `
            <div class="relative">
                <div class="absolute inset-0 flex items-center" aria-hidden="true">
                    <div class="w-full border-t border-gray-200"></div>
                </div>
                <div class="relative flex justify-center text-sm/6 font-medium">
                    <span class="bg-white px-6 text-gray-900">Or</span>
                </div>
            </div>
            <div id="g_id_signin_signup" class="g_id_signin_signup mt-[12px] mb-1.5 flex mx-auto justify-center [&_.nsm7Bb-HzV7m-LgbsSe]:h-[53px] [&_.nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf]:ml-[0px]"
                data-type="standard" data-shape="rectangular" data-theme="filled_blue"
                data-text="signin_with" data-size="large" data-logo_alignment="left">
            </div>
        `);

        if (!loginModal.querySelector("#google-signin-container")) {
            loginModal.querySelector("#switchModeText")?.before(container);
        }
        renderGoogleButton("g_id_signin_signup");
    }
</script>