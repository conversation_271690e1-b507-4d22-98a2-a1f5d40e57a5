<div class="relative">

    <div>

        <div class="max-w-7xl mx-auto">
            <h2
                class="heading font-[900] lg:tracking-tight md:leading-[1.2] md:text-5xl text-3xl text-neutral-800 tracking-tight mb-10 flex">
                Why You Shouldn’t Miss Out on AIBull
            </h2>
            <div>
                <div class="bg-white rounded-2xl p-6 lg:p-12 mb-6 lg:flex gap-16 justify-between border">

                    <div class="lg:w-1/2 w-full">
                        <div>
                            <h3
                                class="heading font-[700] text-3xl  md:leading-[1.3] lg:tracking-tight text-neutral-800">
                                Algo</h3>
                            <p class="mt-2 text-gray-600 text-base">Create custom courses and upload study materials
                                tailored to
                                your unique educational needs.</p>
                        </div>
                        <img class="mt-6 rounded-lg" src="{{ cdn('/static/images/algos.png') }}" alt="Algos">
                    </div>
                    <div class="lg:w-1/2 w-full mt-10 lg:mt-0">
                        <div class="md:px-0 px-4 w-full">
                            <div class="">
                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Options
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">
                                        A tool designed to create, analyze, and manage options strategies through
                                        algorithmic models to optimize trading
                                        performance.

                                    </p>
                                </div>
                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Backtesting
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">
                                        Allows traders to test their strategies against historical data to see how
                                        they
                                        would have performed in real market
                                        conditions before applying them live.

                                    </p>
                                </div>
                                <div class=" duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Scalping</h3>
                                    <p class="text-[15px] text-neutral-700">A high-frequency trading strategy that
                                        aims
                                        to capitalize on small price changes within short time frames. This tool
                                        automates quick trades to take advantage of market inefficiencies.</p>
                                </div>
                                <div class="flex flex-col gap-4 gap-y-2 items-center md:flex-row md:gap-y-2">
                                    <a href="/algos/options" class="relative inline-block text-lg group">
                                        <span
                                            class="relative z-10 block px-5 py-3 overflow-hidden font-medium leading-tight text-gray-800 transition-colors duration-300 ease-out border-2 border-gray-900 rounded-lg group-hover:text-white">
                                            <span
                                                class="absolute inset-0 w-full h-full px-5 py-3 rounded-lg bg-gray-50"></span>
                                            <span
                                                class="absolute left-0 w-64 h-64 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-gray-900 group-hover:-rotate-180 ease"></span>
                                            <span class="relative">Explore Options Algo</span>
                                        </span>
                                        <span
                                            class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-gray-900 rounded-lg group-hover:mb-0 group-hover:mr-0"
                                            data-rounded="rounded-lg"></span>
                                    </a>
                                    {% if not User_Data %}
                                    <a href="#_" class="relative inline-block text-lg group">
                                        <span
                                            class="block border-2 border-black duration-300 ease-out font-medium group-hover:shadow-xl group-hover:text-black leading-tight overflow-hidden px-5 py-3 relative rounded-lg shadow-lg text-white transition-colors z-10">
                                            <span class="absolute inset-0 w-full h-full px-5 py-3  bg-black"></span>
                                            <span
                                                class="absolute left-0 w-48 h-48 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-white group-hover:-rotate-180 ease"></span>
                                            <span class="relative">Signup Today!</span>
                                        </span>
                                        <span
                                            class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-amber-500 rounded-lg group-hover:mb-0 group-hover:mr-0 shadow-lg group-hover:shadow-xl"
                                            data-rounded="rounded-lg"></span>
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- algo ends here -->

    <div class="max-w-7xl mb-6 mx-auto">
        <div
            class="bg-gray-900 isolate items-center lg:flex  md:pt-16 overflow-hidden lg:pl-16 lg:pt-0 relative shadow-2xl rounded-xl lg:rounded-2xl">
            <svg viewBox="0 0 1024 1024"
                class="absolute left-1/2 top-1/2 -z-10 h-[64rem] w-[64rem] -translate-y-1/2 [mask-image:radial-gradient(closest-side,white,transparent)] sm:left-full sm:-ml-80 lg:left-1/2 lg:ml-0 lg:-translate-x-1/2 lg:translate-y-0"
                aria-hidden="true">
                <circle cx="512" cy="512" r="512" fill="url(#759c1415-0410-454c-8f7c-9a820de03641)" fill-opacity="0.7">
                </circle>
                <defs>
                    <radialGradient id="759c1415-0410-454c-8f7c-9a820de03641">
                        <stop stop-color="#7775D6"></stop>
                        <stop offset="1" stop-color="#E935C1"></stop>
                    </radialGradient>
                </defs>
            </svg>
            <div class="lg:flex-auto lg:mx-0 mx-auto p-8 lg:p-0 lg:py-10 w-full lg:w-1/2">
                <h2 class="font-bold heading sm:text-4xl text-3xl text-white tracking-tight">Portfolio &amp; AI
                    Analysis
                </h2>
                <p class="leading-6 mt-3 pr-3 text-gray-300">Leverage advanced AI to optimize strategies, automate
                    trades,
                    and gain real-time insights, empowering traders to enhance performance, reduce risk, and
                    maximize
                    profits in a dynamic market.</p>
                <div class="pt-12">

                    <div>
                        <ul role="list" class="gap-20 grid grid-cols-1 lg:grid-cols-2 md:grid-cols-2">
                            <li data-aos="fade-up" data-aos-delay="50" data-aos-duration="500" data-aos-once="true"
                                class="aos-init aos-animate">
                                <h3 class="flex gap-x-2 font-medium text-white heading font-semibold text-xl">
                                    Live AI Agent</h3>
                                <p class="leading-6 mt-2 text-gray-300">An intelligent assistant powered by
                                    artificial
                                    intelligence that can help you make trading decisions by providing
                                    real-time recommendations based on market trends and your portfolio.</p>
                            </li>
                            <li data-aos="fade-up" data-aos-delay="60" data-aos-duration="500" data-aos-once="true"
                                class="aos-init aos-animate">
                                <h3 class="flex gap-x-2 font-medium text-white heading font-semibold text-xl">
                                    View Portfolio</h3>
                                <p class="leading-6 mt-2 text-gray-300">A comprehensive dashboard to view and
                                    analyze your
                                    current investments and holdings, offering insights into their
                                    performance and risks.
                                </p>
                            </li>



                        </ul>
                    </div>
                </div>
            </div>
            <div class="flex-col hidden justify-end lg:w-1/2 lg:flex w-full">
                <img class="h-[500px] object-[0px_10px] object-cover w-full" src="{{ cdn('/static/images/login.png') }}"
                    alt="webinar" width="588" height="408" loading="lazy" style="clip-path: polygon(24% 0%,100% 0%,100% 100%,0% 100%);
                ">
            </div>
        </div>
    </div>
    <!-- ai ends here -->

    <div>
        <div class="max-w-7xl mx-auto">
            <div>
                <div class="bg-white border rounded-2xl p-6 lg:p-12 mb-6 lg:flex gap-16 justify-between ">
                    <div class="lg:w-1/2 w-full">
                        <div>
                            <h3
                                class="heading font-[700] text-3xl  md:leading-[1.3] lg:tracking-tight text-neutral-800">
                                Trading</h3>
                            <p class="mt-2 text-gray-600 text-base">Create custom courses and upload study materials
                                tailored to
                                your unique educational needs.</p>
                        </div>
                        <img src="{{ cdn('/static/images/trading.png') }}" width="488" height="500" loading="lazy" alt="Sales Agent"
                            class=" w-full">
                    </div>
                    <div class="lg:w-1/2 w-full mt-10 lg:mt-0">
                        <div class="md:px-0 px-4 w-full">
                            <div class="">
                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Spreads
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">
                                        A trading strategy where a trader buys and sells options of the same class
                                        (call or put) on the same underlying asset
                                        but with different strike prices or expiration dates.

                                    </p>
                                </div>
                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Option Chain
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">
                                        Displays a list of available options for a particular asset, showing their
                                        prices, expiration dates, and strike prices
                                        to help traders select appropriate contracts for trading.
                                    </p>
                                </div>

                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Straddle Scanner
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">Scans for straddle opportunities, where
                                        a trader buys both a call and a put option with the same strike price and
                                        expiration date, typically used when expecting high volatility.</p>
                                </div>

                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Spread Analyzer
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">Analyzes different spread strategies,
                                        providing insights into potential profits, risks, and the best combinations
                                        of
                                        options for a chosen strategy.
                                    </p>
                                </div>

                                <div class="duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Strike Scanner
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">A tool that scans various strike prices
                                        for options to identify which ones may offer the best risk-to-reward ratio
                                        based
                                        on current market conditions.
                                    </p>
                                </div>
                                <div class="flex flex-col gap-4 gap-y-2 items-center md:flex-row md:gap-y-2">
                                    <a href="/options/spreads" class="relative inline-block text-lg group">
                                        <span
                                            class="relative z-10 block px-5 py-3 overflow-hidden font-medium leading-tight text-gray-800 transition-colors duration-300 ease-out border-2 border-gray-900 rounded-lg group-hover:text-white">
                                            <span
                                                class="absolute inset-0 w-full h-full px-5 py-3 rounded-lg bg-gray-50"></span>
                                            <span
                                                class="absolute left-0 w-64 h-64 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-gray-900 group-hover:-rotate-180 ease"></span>
                                            <span class="relative">Analyze Spreads</span>
                                        </span>
                                        <span
                                            class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-gray-900 rounded-lg group-hover:mb-0 group-hover:mr-0"
                                            data-rounded="rounded-lg"></span>
                                    </a>

                                    {% if not User_Data %}
                                    <a href="#_" class="relative inline-block text-lg group">
                                        <span
                                            class="block border-2 border-black duration-300 ease-out font-medium group-hover:shadow-xl group-hover:text-black leading-tight overflow-hidden px-5 py-3 relative rounded-lg shadow-lg text-white transition-colors z-10">
                                            <span class="absolute inset-0 w-full h-full px-5 py-3  bg-black"></span>
                                            <span
                                                class="absolute left-0 w-48 h-48 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-white group-hover:-rotate-180 ease"></span>
                                            <span class="relative">Signup Today!</span>
                                        </span>
                                        <span
                                            class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-amber-500 rounded-lg group-hover:mb-0 group-hover:mr-0 shadow-lg group-hover:shadow-xl"
                                            data-rounded="rounded-lg"></span>
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Trading ends heree  -->

    <div class="max-w-7xl mb-6 mx-auto">
        <div
            class="bg-black isolate items-center lg:flex lg:rounded-2xl overflow-hidden relative rounded-xl shadow-2xl">
            <svg viewBox="0 0 1024 1024"
                class="-translate-y-1/2 -z-10 [mask-image:radial-gradient(closest-side,white,transparent)] absolute h-[64rem] left-1/2 lg:-translate-x-1/2 lg:left-1/2 lg:ml-0 lg:translate-y-0 sm:-ml-80 sm:left-full top-1/2 w-[64rem]"
                aria-hidden="true">
                <circle cx="512" cy="512" r="512" fill="url(#759c1415-0410-454c-8f7c-9a820de03641)" fill-opacity="0.7">
                </circle>
                <defs>
                    <radialGradient id="759c1415-0410-454c-8f7c-9a820de03641">
                        <stop stop-color="#7775D6"></stop>
                        <stop offset="1" stop-color="#E935C1"></stop>
                    </radialGradient>
                </defs>
            </svg>


            <div class="lg:flex-auto lg:mx-0 w-full">

                <div class="flex">
                    <div class="lg:w-[25%] hidden lg:flex">
                        <img src="https://img.freepik.com/free-photo/3d-rendering-financial-neon-bull_23-2151691931.jpg?t=st=1745997861~exp=1746001461~hmac=cd737423bcd870a284306f2758d19370b91da39fd378d33b506476a46072124d&amp;w=740"
                            alt="" class="h-full object-cover">
                    </div>
                    <div class="lg:p-10 p-6 lg:w-[75%] w-full">
                        <div class="mb-5">
                            <h2 class="font-bold heading sm:text-4xl text-3xl text-white tracking-tight">AI Analysis
                            </h2>
                            <p class="leading-6 mt-2 pr-3 text-gray-300">AI-Powered Insights for Smarter, Safer
                                Portfolio
                                Management
                                Decisions</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Performance Evaluation -->
                            <div class="bg-neutral-950 hover:shadow-md p-6 rounded-xl shadow-sm transition">
                                <h3 class="font-semibold heading mb-2 text-gray-100 text-xl">Performance Evaluation
                                </h3>
                                <p class="text-gray-400">
                                    AI analyzes historical returns and growth metrics to identify high-performing
                                    assets
                                    and
                                    detect overconcentration risks.
                                </p>
                            </div>

                            <!-- Diversification Insights -->
                            <div class="bg-neutral-950 hover:shadow-md p-6 rounded-xl shadow-sm transition">
                                <h3 class="font-semibold heading mb-2 text-gray-100 text-xl">Diversification
                                    Insights
                                </h3>
                                <p class="text-gray-400">
                                    It assesses sector and stock diversity, recommending additions from
                                    underrepresented
                                    sectors to reduce volatility.
                                </p>
                            </div>

                            <!-- Risk Monitoring -->
                            <div class="bg-neutral-950 hover:shadow-md p-6 rounded-xl shadow-sm transition">
                                <h3 class="font-semibold heading mb-2 text-gray-100 text-xl">Risk Monitoring</h3>
                                <p class="text-gray-400">
                                    AI continuously tracks sector-specific and macroeconomic risks, issuing alerts
                                    for
                                    potential exposure to downturns.
                                </p>
                            </div>

                            <!-- Holding Analysis -->
                            <div class="bg-neutral-950 hover:shadow-md p-6 rounded-xl shadow-sm transition">
                                <h3 class="font-semibold heading mb-2 text-gray-100 text-xl">Holding Analysis</h3>
                                <p class="text-gray-400">
                                    Each stock is evaluated based on financials, technical indicators, and market
                                    sentiment
                                    to guide buy/hold/sell decisions.
                                </p>
                            </div>

                            <!-- Smart Recommendations -->
                            <div
                                class="bg-neutral-950 hover:shadow-md p-6 rounded-xl shadow-sm transition md:col-span-2">
                                <h3 class="font-semibold heading mb-2 text-gray-100 text-xl">Smart Recommendations
                                </h3>
                                <p class="text-gray-400">
                                    AI suggests rebalancing actions, portfolio adjustments, and entry into emerging
                                    sectors
                                    for better long-term stability.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- ai analysis ends here -->

    <div>
        <div class="max-w-7xl mx-auto">
            <div>
                <div class="bg-white border rounded-2xl p-6 lg:p-12 mb-6 lg:flex gap-16 justify-between ">
                    <div class="lg:w-1/2 w-full">

                        <img src="{{ cdn('/static/images/strategies.png') }}" width="488" height="500" loading="lazy"
                            alt="strategies" class=" w-full">
                    </div>
                    <div class="lg:w-1/2 w-full mt-10 lg:mt-0">
                        <div class="mb-6">
                            <h3
                                class="heading font-[700] text-3xl  md:leading-[1.3] lg:tracking-tight text-neutral-800">
                                Strategies</h3>
                            <p class="mt-2 text-gray-600 text-base">Create custom courses and upload study materials
                                tailored to
                                your unique educational needs.</p>
                        </div>

                        <div class="md:px-0 px-4 w-full">
                            <div class="">
                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Covered Call
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">A strategy where a trader holds a long
                                        position in an asset while simultaneously selling a call option on that
                                        asset to
                                        generate income from option premiums.

                                    </p>
                                </div>
                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Cash Secured Put
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">
                                        A strategy in which a trader sells a put option while ensuring they have
                                        enough cash in their account to purchase the
                                        underlying asset if the option is exercised.
                                    </p>
                                </div>
                                <div class="duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Expiration Day
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">
                                        A strategy in which a trader sells a put option while ensuring they have
                                        enough cash in their account to purchase the
                                        underlying asset if the option is exercised.
                                    </p>
                                </div>
                                <div class="flex flex-col gap-4 gap-y-2 items-center md:flex-row md:gap-y-2">
                                    <a href="/strategies/cash-secured-put" class="relative inline-block text-lg group">
                                        <span
                                            class="relative z-10 block px-5 py-3 overflow-hidden font-medium leading-tight text-gray-800 transition-colors duration-300 ease-out border-2 border-gray-900 rounded-lg group-hover:text-white">
                                            <span
                                                class="absolute inset-0 w-full h-full px-5 py-3 rounded-lg bg-gray-50"></span>
                                            <span
                                                class="absolute left-0 w-64 h-64 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-gray-900 group-hover:-rotate-180 ease"></span>
                                            <span class="relative">Try Cash Secured Put</span>
                                        </span>
                                        <span
                                            class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-gray-900 rounded-lg group-hover:mb-0 group-hover:mr-0"
                                            data-rounded="rounded-lg"></span>
                                    </a>

                                    {% if not User_Data %}
                                    <a href="#_" class="relative inline-block text-lg group">
                                        <span
                                            class="block border-2 border-black duration-300 ease-out font-medium group-hover:shadow-xl group-hover:text-black leading-tight overflow-hidden px-5 py-3 relative rounded-lg shadow-lg text-white transition-colors z-10">
                                            <span class="absolute inset-0 w-full h-full px-5 py-3  bg-black"></span>
                                            <span
                                                class="absolute left-0 w-48 h-48 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-white group-hover:-rotate-180 ease"></span>
                                            <span class="relative">Signup Today!</span>
                                        </span>
                                        <span
                                            class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-amber-500 rounded-lg group-hover:mb-0 group-hover:mr-0 shadow-lg group-hover:shadow-xl"
                                            data-rounded="rounded-lg"></span>
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Strategies ends heree  -->

    <div>
        <div class="max-w-7xl mx-auto">
            <div>
                <div class="bg-white border gap-16 lg:p-12 mb-6 p-6 relative rounded-2xl shadow-sm" style="
    background-image: linear-gradient(45deg, #fff7fe 26%, #f1fdff 40%, #ffffff 78%);
">
                    <div class="w-full">
                        <div>
                            <h3
                                class="heading font-[700] text-3xl  md:leading-[1.3] lg:tracking-tight text-neutral-800">
                                Arbitrages</h3>
                            <p class="mt-2 text-gray-600 text-base">Create custom courses and upload study materials
                                tailored to
                                your unique educational needs.</p>
                        </div>

                    </div>
                    <div class="">
                        <div class="md:px-0 px-4 w-full">
                            <div class="gap-6 grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 mt-8">
                                <div class="border duration-500 p-3 relative rounded-xl">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Futures Arbitrage
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">A strategy involving the simultaneous
                                        buying and selling of futures contracts to profit from price differences
                                        between
                                        related markets or instruments.
                                    </p>
                                </div>
                                <div class="border duration-500 p-3 relative rounded-xl">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Exchanges Arbitrage
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">
                                        A principle that states the price relationship between puts and calls with
                                        the same strike prices and expiration dates
                                        should remain in a balanced equilibrium, allowing traders to exploit
                                        discrepancies.
                                    </p>
                                </div>
                                <div class="border duration-500 p-3 relative rounded-xl">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Box Spread
                                        Arbitrage
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">
                                        A market-neutral strategy that involves using four options (a combination of
                                        calls and puts) to lock in a risk-free
                                        profit from the price discrepancy between them.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Arbitrages ends heree  -->

    <div>
        <div class="max-w-7xl mx-auto">

            <div>
                <div class="bg-white border rounded-2xl p-6 lg:p-12 mb-6 lg:flex gap-16 justify-between ">
                    <div class="lg:w-1/2 w-full">

                        <img src="{{ cdn('/static/images/stocks.png') }}" width="488" height="500" loading="lazy" alt="Sales Agent"
                            class=" w-full">
                    </div>
                    <div class="lg:w-1/2 w-full mt-10 lg:mt-0">
                        <div class="mb-6">
                            <h3
                                class="heading font-[700] text-3xl  md:leading-[1.3] lg:tracking-tight text-neutral-800">
                                Stocks</h3>
                            <p class="mt-2 text-gray-600 text-base">Create custom courses and upload study materials
                                tailored to
                                your unique educational needs.</p>
                        </div>
                        <div class="md:px-0 px-4 w-full">
                            <div class="">
                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Screener
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">A tool that filters stocks based on
                                        predefined
                                        criteria such as market capitalization, sector, dividend yield, etc., to
                                        help traders find stocks that match their investment strategy.
                                    </p>
                                </div>
                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Live Analysis
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">
                                        Provides real-time analysis of stock
                                        performance,
                                        including trends, technical indicators, and news, helping traders make
                                        informed decisions based on live market data.
                                    </p>
                                </div>
                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Compare
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">
                                        A market-neutral strategy that involves using four options (a combination of
                                        calls and puts) t]]]]]]0o lock in a risk-free
                                        profit from the price discrepancy between them.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Stocks ends heree  -->

    <div>
        <div class="max-w-7xl mx-auto">
            <div>
                <div class="bg-white border rounded-2xl p-6 lg:p-12 mb-6 lg:flex gap-16 justify-between ">
                    <div class="lg:w-1/2 w-full">

                        <img src="{{ cdn('/static/images/mutual-funds.png') }}" width="488" height="500" loading="lazy"
                            alt="Sales Agent" class=" w-full">
                    </div>
                    <div class="lg:w-1/2 w-full mt-10 lg:mt-0">

                        <div class="mb-6">
                            <h3
                                class="heading font-[700] text-3xl  md:leading-[1.3] lg:tracking-tight text-neutral-800">
                                Mutual Funds</h3>
                            <p class="mt-2 text-gray-600 text-base">Create custom courses and upload study materials
                                tailored to
                                your unique educational needs.</p>
                        </div>
                        <div class="md:px-0 px-4 w-full">
                            <div class="">
                                <div class="border-b border-dashed duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Mutual Fund
                                        Screener
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">Filter mutual funds based on
                                        performance, expense ratio, asset allocation, and category to find options
                                        that
                                        align with your investment goals.
                                    </p>
                                </div>
                                <div class="duration-500 mb-5 pb-5 dots relative">
                                    <h3 class="text-[18px] font-semibold mb-2 leading-6 heading">Mutual Fund Compare
                                    </h3>
                                    <p class="text-[15px] text-neutral-700">
                                        Compare mutual funds side-by-side based on performance, expense ratio, asset
                                        allocation, and other key metrics to make
                                        informed investment decisions.
                                    </p>
                                </div>
                                <div class="flex flex-col gap-4 gap-y-2 items-center md:flex-row md:gap-y-2">
                                    <a href="/mutual-funds" class="relative inline-block text-lg group">
                                        <span
                                            class="relative z-10 block px-5 py-3 overflow-hidden font-medium leading-tight text-gray-800 transition-colors duration-300 ease-out border-2 border-gray-900 rounded-lg group-hover:text-white">
                                            <span
                                                class="absolute inset-0 w-full h-full px-5 py-3 rounded-lg bg-gray-50"></span>
                                            <span
                                                class="absolute left-0 w-64 h-64 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-gray-900 group-hover:-rotate-180 ease"></span>
                                            <span class="relative">Find Top Equity Funds</span>
                                        </span>
                                        <span
                                            class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-gray-900 rounded-lg group-hover:mb-0 group-hover:mr-0"
                                            data-rounded="rounded-lg"></span>
                                    </a>

                                    {% if not User_Data %}
                                    <a href="#_" class="relative inline-block text-lg group">
                                        <span
                                            class="block border-2 border-black duration-300 ease-out font-medium group-hover:shadow-xl group-hover:text-black leading-tight overflow-hidden px-5 py-3 relative rounded-lg shadow-lg text-white transition-colors z-10">
                                            <span class="absolute inset-0 w-full h-full px-5 py-3  bg-black"></span>
                                            <span
                                                class="absolute left-0 w-48 h-48 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-white group-hover:-rotate-180 ease"></span>
                                            <span class="relative">Signup Today!</span>
                                        </span>
                                        <span
                                            class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-amber-500 rounded-lg group-hover:mb-0 group-hover:mr-0 shadow-lg group-hover:shadow-xl"
                                            data-rounded="rounded-lg"></span>
                                    </a>
                                    {% endif %}
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Mutual Funds ends heree  -->
</div>

<style>
    .dots:before {
        background-color: #ff980069;
        border-radius: 100%;
        content: "";
        height: 6px;
        left: -16px;
        position: absolute;
        top: 7px;
        width: 6px;
    }
</style>