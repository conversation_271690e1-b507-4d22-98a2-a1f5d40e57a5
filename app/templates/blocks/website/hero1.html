<div class="relative isolate overflow-hidden bg-neutral-50 pt-6 lg:pt-20">
    <div
        class="absolute inset-x-0 bottom-[calc(-702/16*1rem)] top-0 -z-10 bg-[radial-gradient(154.86%_76.83%_at_50%_22.26%,_rgba(243,244,246,0.4)_8.98%,_rgba(243,244,246,1)_45.99%)]">
    </div>
    <svg viewBox="-1000 0 3504 918"
        class="absolute -top-6 left-1/2 -z-10 ml-[calc(-3504/2/16*1rem)] w-[calc(3504/16*1rem)] mix-blend-overlay">
        <path fill="url(#hero-gradient)" d="M3504 918H-1000V0h3504v918Z"></path>
        <defs>
            <radialGradient id="hero-gradient" cx="0" cy="0" r="1"
                gradientTransform="matrix(0 707.279 -1739.2 0 741 159.991)" gradientUnits="userSpaceOnUse">
                <stop stop-color="#6C47FF" stop-opacity="0.6"></stop>
                <stop offset=".412" stop-color="#FFF963" stop-opacity=".8"></stop>
                <stop offset=".623" stop-color="#38DAFD" stop-opacity=".6"></stop>
                <stop offset=".919" stop-color="#6248F6" stop-opacity="0"></stop>
            </radialGradient>
        </defs>
    </svg>
    <svg class="absolute inset-x-0 top-0 -z-10 h-full w-full stroke-gray-300/80 [mask-image:radial-gradient(32rem_32rem_at_center,white,transparent)]"
        aria-hidden="true">
        <defs>
            <pattern id="1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84" width="200" height="200" x="50%" y="-1"
                patternUnits="userSpaceOnUse">
                <path d="M.5 200V.5H200" fill="none"></path>
            </pattern>
        </defs>
        <svg x="50%" y="-1" class="overflow-visible fill-gray-50">
            <path d="M-200 0h201v201h-201Z M600 0h201v201h-201Z M-400 600h201v201h-201Z M200 800h201v201h-201Z"
                stroke-width="0"></path>
        </svg>
        <rect width="100%" height="100%" stroke-width="0" fill="url(#1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84)"></rect>
    </svg>
    <div class="max-w-6xl mx-auto lg:px-0 px-5 relative">

        <div class="lg:text-center">
            <div
                class="inline-flex leading-[1.25] mb-5 px-[22px] py-[17px] relative text-neutral-900 text-base lg:text-xl">
                <div class="flex items-center text-[#e98b00] font-bold">

                    AI
                    Trading Platform
                </div>
                <div class="absolute inset-0">
                    <div class="bg-[#ff9800] opacity-40 w-full h-px absolute inset-x-0 top-2"></div>
                    <div class="bg-[#ff9800] opacity-40 w-px h-full absolute inset-y-0 left-2"></div>
                    <div class="bg-[#ff9800] opacity-40 w-px h-full absolute inset-y-0 right-2"></div>
                    <div class="bg-[#ff9800] opacity-40 w-full h-px absolute inset-x-0 bottom-2"></div>
                </div>
            </div>

            <h1
                class="heading font-[900] leading-[42px] md:leading-[1] lg:text-[68px] lg:text-center max-w-4xl mb-5 md:text-[52px] mx-auto text-[36px] text-neutral-950 tracking-tight">
                Grow Your Investment Potential with AI Precision</h1>


            <p class="leading-7 lg:leading-7 lg:mx-auto max-w-4xl mt-3 text-base text-neutral-800 md:text-lg">
                Empower your investments with advanced AI-driven tools that offer precision, speed, and smarter trading
                decisions.
                Leverage cutting-edge technology to enhance your market strategies, optimize performance, and achieve
                consistent,
                data-backed investment success.
            </p>



            <div class="bg-white shadow-xl rounded-t-xl relative mt-10">
                <div class="absolute flex h-full inset-0 items-center justify-center rounded-t-xl z-[99]"
                    style="background: #65656512;">
                    <div class="flex flex-col gap-2 gap-y-2 items-center lg:justify-center md:flex-row md:gap-y-2">
                        <!-- Watch Demo Button -->


                        <a href="/app" class="relative inline-block text-lg group">
                            <span
                                class="relative z-10 block px-5 py-3 overflow-hidden font-medium leading-tight text-gray-800 transition-colors duration-300 ease-out border-2 border-gray-900 rounded-lg group-hover:text-white">
                                <span class="absolute inset-0 w-full h-full px-5 py-3 rounded-lg bg-gray-50"></span>
                                <span
                                    class="absolute left-0 w-64 h-64 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-gray-900 group-hover:-rotate-180 ease"></span>
                                <span class="relative">Try AIBull Today</span>
                            </span>
                            <span
                                class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-gray-900 rounded-lg group-hover:mb-0 group-hover:mr-0"
                                data-rounded="rounded-lg"></span>
                        </a>

                        <button onclick="showContactUsModal('sales', generateContactUsDynamicMessage())" class="relative inline-block text-lg group">
                            <span
                                class="block border-2 border-black duration-300 ease-out font-medium group-hover:shadow-xl group-hover:text-black leading-tight overflow-hidden px-5 py-3 relative rounded-lg shadow-lg text-white transition-colors z-10">
                                <span class="absolute inset-0 w-full h-full px-5 py-3  bg-black"></span>
                                <span
                                    class="absolute left-0 w-48 h-48 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-white group-hover:-rotate-180 ease"></span>
                                <span class="relative">Schedule a Demo</span>
                            </span>
                            <span
                                class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-amber-500 rounded-lg group-hover:mb-0 group-hover:mr-0 shadow-lg group-hover:shadow-xl"
                                data-rounded="rounded-lg"></span>
                        </button>
                    </div>
                </div>
                <div class="relative">
                    <div class="-inset-2 absolute blur-[2px] duration-1000 filter group-hover:-inset-1 group-hover:duration-200 group-hover:opacity-100 opacity-30 rounded-xl transitiona-all z-0"
                        style="background: linear-gradient(90deg, rgb(176, 46, 216) -0.55%, rgb(97, 50, 29) 22.86%, rgb(222, 146, 131) 48.36%, rgb(77, 96, 153) 73.33%, rgb(39, 79, 85) 99.34%);">
                    </div>
                    <img class="border rounded-t-lg relative"
                        src="https://ap1-infinity-user-data.s3.amazonaws.com/13416/teams/2037_image-1745933195890.png" />

                </div>
            </div>
        </div>
    </div>
</div>