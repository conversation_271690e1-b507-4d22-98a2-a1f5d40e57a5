<script>
    // Function to prepare and send option chain data to AI Analysis
    function getChainAiAnalysis(customData = null) {
        let currentSymbol, chainData, modalCurrentExpiry, currentSpotPrice, futurePrices;

        // Use custom data if provided (from chain.html), otherwise use window values (from option-chain.html)
        if (customData) {
            currentSymbol = customData.symbol;
            chainData = customData.chainData;
            modalCurrentExpiry = customData.currentExpiry;
            currentSpotPrice = customData.spotPrice;
            futurePrices = customData.futurePrices;
        } else {
            // Use window globals (from the modal view)
            if (!window.chainData || !window.chainData.options || !window.modalCurrentExpiry) {
                showAlert("No option chain data available for analysis");
                return;
            }
            currentSymbol = window.currentSymbol;
            chainData = window.chainData;
            modalCurrentExpiry = window.modalCurrentExpiry;
            currentSpotPrice = window.currentSpotPrice;
            futurePrices = window.futurePrices;
        }

        if (!isLoggedIn()) {
            showModal("Login or sign up to get AI feedback!");
            return;
        }

        try {
            // Filter options by current expiry
            const options = chainData.options.filter(opt => opt.expiryDate === modalCurrentExpiry);
            const spotPrice = currentSpotPrice;
            const futurePrice = futurePrices[modalCurrentExpiry] || null;

            if (options.length === 0) {
                showAlert("No options found for this expiry date");
                return;
            }

            // Find the ATM strike (closest to current spot price)
            let atmIndex = 0;
            let minDiff = Infinity;

            options.forEach((opt, index) => {
                const diff = Math.abs(opt.strikePrice - spotPrice);
                if (diff < minDiff) {
                    minDiff = diff;
                    atmIndex = index;
                }
            });

            // Calculate total OI and volume for calls and puts
            let totalCallOI = 0;
            let totalPutOI = 0;
            let totalCallVolume = 0;
            let totalPutVolume = 0;
            let maxPainPoint = calculateMaxPain(options);

            options.forEach(opt => {
                if (opt.CE && opt.CE.openInterest) totalCallOI += opt.CE.openInterest;
                if (opt.PE && opt.PE.openInterest) totalPutOI += opt.PE.openInterest;
                if (opt.CE && opt.CE.totalTradedVolume) totalCallVolume += opt.CE.totalTradedVolume;
                if (opt.PE && opt.PE.totalTradedVolume) totalPutVolume += opt.PE.totalTradedVolume;
            });

            // Calculate PCR (Put-Call Ratio)
            const oiPCR = totalPutOI / totalCallOI || 0;
            const volumePCR = totalPutVolume / totalCallVolume || 0;

            // Get a range of strikes around ATM for analysis (15 strikes above and below ATM)
            const startIndex = Math.max(0, atmIndex - 15);
            const endIndex = Math.min(options.length - 1, atmIndex + 15);

            // Extract the relevant option chain data
            const optionChainData = options.slice(startIndex, endIndex + 1).map(opt => {
                const callData = opt.CE || {};
                const putData = opt.PE || {};

                return {
                    strike: opt.strikePrice,
                    call: {
                        lastPrice: callData.lastPrice || 0,
                        openInterest: callData.openInterest || 0,
                        changeinOpenInterest: callData.changeinOpenInterest || 0,
                        totalTradedVolume: callData.totalTradedVolume || 0,
                        impliedVolatility: callData.impliedVolatility || 0,
                        change: callData.change || 0,
                        pChange: callData.pChange || 0,
                        bidQty: callData.bidQty || 0,
                        bidprice: callData.bidprice || 0,
                        askQty: callData.askQty || 0,
                        askPrice: callData.askPrice || 0,
                        delta: callData.delta || null,
                        gamma: callData.gamma || null,
                        theta: callData.theta || null,
                        vega: callData.vega || null
                    },
                    put: {
                        lastPrice: putData.lastPrice || 0,
                        openInterest: putData.openInterest || 0,
                        changeinOpenInterest: putData.changeinOpenInterest || 0,
                        totalTradedVolume: putData.totalTradedVolume || 0,
                        impliedVolatility: putData.impliedVolatility || 0,
                        change: putData.change || 0,
                        pChange: putData.pChange || 0,
                        bidQty: putData.bidQty || 0,
                        bidprice: putData.bidprice || 0,
                        askQty: putData.askQty || 0,
                        askPrice: putData.askPrice || 0,
                        delta: putData.delta || null,
                        gamma: putData.gamma || null,
                        theta: putData.theta || null,
                        vega: putData.vega || null
                    }
                };
            });

            // Identify strikes with highest OI for calls and puts
            const callOIArray = options.map(opt => ({
                strike: opt.strikePrice,
                oi: opt.CE ? opt.CE.openInterest || 0 : 0
            })).sort((a, b) => b.oi - a.oi);

            const putOIArray = options.map(opt => ({
                strike: opt.strikePrice,
                oi: opt.PE ? opt.PE.openInterest || 0 : 0
            })).sort((a, b) => b.oi - a.oi);

            const topCallOI = callOIArray.slice(0, 3).map(item => ({
                strike: item.strike,
                oi: item.oi
            }));

            const topPutOI = putOIArray.slice(0, 3).map(item => ({
                strike: item.strike,
                oi: item.oi
            }));

            // Calculate days to expiry
            const today = new Date();
            const expiryParts = modalCurrentExpiry.split('-');
            const expiryDate = new Date(
                parseInt(expiryParts[2]), // Year
                parseInt(expiryParts[1]) - 1, // Month (0-indexed)
                parseInt(expiryParts[0]) // Day
            );
            const daysToExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

            // Prepare the payload
            const payload = {
                optionChain: {
                    symbol: currentSymbol,
                    spotPrice: spotPrice,
                    expiryDate: modalCurrentExpiry,
                    futurePrice: futurePrice,
                    daysToExpiry: daysToExpiry,
                    atmStrike: options[atmIndex]?.strikePrice || spotPrice,
                    putCallRatio: {
                        openInterest: oiPCR.toFixed(2),
                        volume: volumePCR.toFixed(2)
                    },
                    maxPain: maxPainPoint,
                    options: optionChainData,
                    marketStats: {
                        totalCallOI: totalCallOI,
                        totalPutOI: totalPutOI,
                        totalCallVolume: totalCallVolume,
                        totalPutVolume: totalPutVolume,
                        highestOI: {
                            calls: topCallOI,
                            puts: topPutOI
                        }
                    }
                }
            };

            // Call the AI analysis function with option chain analysis type
            getAIFeedback(payload, { analysisType: 'optionChain' });
        } catch (error) {
            console.error("Error preparing option chain for analysis:", error);
            showAlert("Error analyzing option chain. Please try again.");
        }
    }

    // Function to calculate max pain point
    function calculateMaxPain(options) {
        // Create an array of all strikes
        const strikes = options.map(opt => opt.strikePrice);

        if (strikes.length === 0) return null;

        // Calculate total pain at each strike
        const painAtStrikes = strikes.map(strike => {
            let totalPain = 0;

            options.forEach(opt => {
                // Call options pain: max(0, strike - strikePrice) * openInterest
                if (opt.CE && opt.CE.openInterest) {
                    const callPain = Math.max(0, strike - opt.strikePrice) * opt.CE.openInterest;
                    totalPain += callPain;
                }

                // Put options pain: max(0, strikePrice - strike) * openInterest
                if (opt.PE && opt.PE.openInterest) {
                    const putPain = Math.max(0, opt.strikePrice - strike) * opt.PE.openInterest;
                    totalPain += putPain;
                }
            });

            return { strike, totalPain };
        });

        // Find the strike with minimum pain
        const maxPainPoint = painAtStrikes.reduce((minPain, current) => {
            return current.totalPain < minPain.totalPain ? current : minPain;
        }, painAtStrikes[0]);

        return maxPainPoint.strike;
    }
</script>