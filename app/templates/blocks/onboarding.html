<!-- Modal Structure -->
<div id="onboardingModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white lg:p-0 p-3 rounded-lg max-w-7xl m-2 xl:m-0 relative">
        <div class="absolute top-2 right-2 lg:top-[-1.75rem] lg:right-[-1.75rem] z-50">
            <button id="closeOnboardingModalButton" class="text-gray-500 lg:text-gray-100 hover:text-red-500 focus:ring-2 focus:ring-red-500 focus:outline-none">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-x">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                </svg>
            </button>
        </div>
        <div class="flex justify-between">
            <div class="bg-gray-900 relative rounded-l-lg text-white/90 w-[50%] hidden lg:block">
                <img src="{{ cdn('/static/images/onboarding/onboard.jpg') }}" alt="Onboarding Image"

                    class="absolute h-full rounded-l-lg">
                <div class="bg-gray-900/90 h-full p-10 relative rounded-l-lg">
                    <h2 class="text-3xl font-semibold mb-8 text-white leading-tight">Welcome to The AIBull</h2>
                    <p class="mb-4">Congratulations on joining The AIBull! This is your gateway to advanced trading
                        powered by AI
                        and automated strategies.</p>
                    <p class="mb-4">You will use AIBull to execute trades, analyze market trends, and implement
                        algorithmic
                        strategies for better results. Access AIBull anytime to manage your trades and portfolio
                        seamlessly.</p>
                    <p class="mb-4">Explore a variety of <a href="#" class="text-white">trading platforms</a> we
                        offer, and
                        discover which one suits your trading style best.</p>
                    <p class="mb-8">We recommend <a href="#" class="text-white">funding your account</a> today so you
                        can start
                        using the full range of tools and strategies at your disposal right away.</p>
                    <p>Thank you for choosing AIBull. We look forward to helping you achieve your trading goals.</p>
                    <p class="mt-4 font-semibold">The AIBull Team</p>
                </div>
            </div>

            <div class="lg:max-h-[70vh] max-h-[90vh] overflow-auto lg:w-[50%] w-full relative" id="modalScrollable">
                <div class="p-6">
                    <div id="activePathDisplay"
                        class="bg-red-50 fixed hidden font-bold left-0 left-[48%] mt-4 text-center text-xs top-[80px]">
                        <!-- Active page indicator with arrow will appear here -->
                        <span id="activePageText"></span>
                        <span id="arrowIndicator"></span>
                    </div>
                    <!-- Algos Section -->
                    <div class="mb-10">
                        <h2 class="lg:text-xl text-xl font-semibold text-gray-800 mb-3 pb-3  border-b mx-3">Algos</h2>
                        <div class="grid lg:grid-cols-1 md:grid-cols-1 grid-cols-1 gap-4">
                            <div class="grid grid-cols-2 gap-4 options-box p-2 rounded-lg">
                                <a href="/algos/options">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Options</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A tool designed to
                                            create,
                                            analyze, and
                                            manage options
                                            strategies
                                            through
                                            algorithmic models to optimize trading performance.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/options.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 backtesting-box p-3">
                                <a href="/algos/backtesting">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Backtesting</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">Allows traders to
                                            test
                                            their
                                            strategies
                                            against
                                            historical data to see how they would have performed in real market
                                            conditions before applying them live.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/backtesting.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 scalping-box p-3">
                                <a href="/algos/scalping">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Algos Scalping</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A high-frequency
                                            trading
                                            strategy that
                                            aims
                                            to
                                            capitalize on small price changes within short time frames. This tool
                                            automates quick trades to take advantage of market inefficiencies.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/scalping.png') }}" alt="">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Portfolio & AI Analysis Section -->
                    <div class="mb-10">
                        <h2 class="lg:text-xl text-xl font-semibold text-gray-800 mb-3 pb-3  border-b mx-3">Portfolio &
                            AI Analysis
                        </h2>
                        <div class="grid lg:grid-cols-1 md:grid-cols-1 grid-cols-1 gap-4">
                            <div class="grid grid-cols-2 gap-4 portfolio-box p-3">
                                <a href="/portfolio">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">View Portfolio</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A comprehensive
                                            dashboard
                                            to
                                            view and
                                            analyze your
                                            current investments and holdings, offering insights into their
                                            performance and risks.
                                        </p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/portfolio.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 live-box p-3">
                                <a href="/ai-agent/live">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Live AI Agent</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">An intelligent
                                            assistant
                                            powered by
                                            artificial
                                            intelligence that can help you make trading decisions by providing
                                            real-time recommendations based on market trends and your portfolio.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/live.png') }}" alt="">
                                </div>
                            </div>

                        </div>
                    </div>

                    <!-- Trading Section -->
                    <div class="mb-10">
                        <h2 class="lg:text-xl text-xl font-semibold text-gray-800 mb-3 pb-3  border-b mx-3">Trading</h2>
                        <div class="grid lg:grid-cols-1 md:grid-cols-1 grid-cols-1 gap-4">
                            <div class="grid grid-cols-2 gap-4 chain-box p-3">
                                <a class="" href="/options/chain">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Option Chain</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">Displays a list of
                                            available
                                            options
                                            for a
                                            particular
                                            asset, showing their prices, expiration dates, and strike prices
                                            to help traders select appropriate contracts for trading.
                                        </p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images//onboarding/chain.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 spreads-box p-3">
                                <a class="" href="/options/spreads">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Spreads</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A trading strategy
                                            where a
                                            trader buys
                                            and
                                            sells
                                            options of the same class (call or put) on the same underlying asset
                                            but with different strike prices or expiration dates.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/spreads.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 straddle-scanner-box p-3">
                                <a class="" href="/strategies/straddle-scanner">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Straddle Scanner</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">Scans for straddle
                                            opportunities, where
                                            a
                                            trader buys
                                            both a call and a put option with the same strike price and
                                            expiration date, typically used when expecting high volatility.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/straddle.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 spread-analyzer-box p-3">
                                <a class="" href="/strategies/spread-analyzer">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Spread Analyzer</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">Analyzes different
                                            spread
                                            strategies,
                                            providing
                                            insights into potential profits, risks, and the best combinations of
                                            options for a chosen strategy.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/spread-analyzer.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 strike-scanner-box p-3">
                                <a class="" href="/strategies/strike-scanner">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Strike Scanner</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A tool that scans
                                            various
                                            strike prices
                                            for
                                            options to
                                            identify which ones may offer the best risk-to-reward ratio based
                                            on current market conditions.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/strike-scaner.png') }}" alt="">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Strategies Section -->
                    <div class="mb-10">
                        <h2 class="lg:text-xl text-xl font-semibold text-gray-800 mb-3 pb-3  border-b mx-3">Strategies
                        </h2>
                        <div class="grid lg:grid-cols-1 md:grid-cols-1 grid-cols-1 gap-4">
                            <div class="grid grid-cols-2 gap-4 covered-call-box p-3">
                                <a class="" href="/strategies/covered-call">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Covered Call</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A strategy where a
                                            trader
                                            holds
                                            a long
                                            position in an
                                            asset while simultaneously selling a call option on that asset to
                                            generate income from option premiums.
                                        </p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/covered-call.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 cash-secured-put-box p-3">
                                <a class="" href="/strategies/cash-secured-put">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Cash Secured Put</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A strategy in which a
                                            trader
                                            sells a
                                            put
                                            option while
                                            ensuring they have enough cash in their account to purchase the
                                            underlying asset if the option is exercised.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/covered-call.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 futures-arbitrage-box p-3">
                                <a class="" href="/strategies/futures-arbitrage">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Futures Arbitrage</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A strategy involving
                                            the
                                            simultaneous
                                            buying
                                            and
                                            selling of futures contracts to profit from price differences between
                                            related markets or instruments.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/futures-arbitrage.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 exchanges-arbitrage-box p-3">
                                <a class="" href="/strategies/exchanges-arbitrage">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Exchanges Arbitrage</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A principle that states
                                            the
                                            price
                                            relationship between
                                            puts and calls with the same strike prices and expiration dates
                                            should remain in a balanced equilibrium, allowing traders to exploit
                                            discrepancies.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/exchanges-arbitrage.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 put-call-parity-box p-3">
                                <a class="" href="/strategies/put-call-parity">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Put Call Parity</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A tool that scans
                                            various
                                            strike prices
                                            for
                                            options to
                                            identify which ones may offer the best risk-to-reward ratio based
                                            on current market conditions.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/put-call-parity.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 box-spread-arbitrage-box p-3">
                                <a class="" href="/strategies/box-spread-arbitrage">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Box Spread Arbitrage</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A market-neutral
                                            strategy
                                            that
                                            involves
                                            using four
                                            options (a combination of calls and puts) to lock in a risk-free
                                            profit from the price discrepancy between them.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/exchanges-arbitrage.png') }}" alt="">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stocks Section -->
                    <div class="mb-10">
                        <h2 class="lg:text-xl text-xl font-semibold text-gray-800 mb-3 pb-3  border-b mx-3">Stocks</h2>
                        <div class="grid lg:grid-cols-1 md:grid-cols-1 grid-cols-1 gap-4">
                            <div class="grid grid-cols-2 gap-4 screener-box p-3">
                                <a class="" href="/stocks">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Screener</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">A tool that filters
                                            stocks
                                            based on
                                            predefined
                                            criteria such as market capitalization, sector, dividend yield, etc., to
                                            help traders find stocks that match their investment strategy.
                                        </p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/exchanges-arbitrage.png') }}" alt="">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 live-box p-3">
                                <a cclass="" href="/stocks/live">
                                    <div>
                                        <h3 class="font-semibold text-neutral-800">Live Analysis</h3>
                                        <p class="mt-2 text-gray-500 text-[13px] leading-[21px]">Provides real-time
                                            analysis
                                            of
                                            stock
                                            performance,
                                            including trends, technical indicators, and news, helping traders make
                                            informed decisions based on live market data.</p>
                                    </div>
                                </a>
                                <div>
                                    <img src="{{ cdn('/static/images/onboarding/exchanges-arbitrage.png') }}" alt="">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    window.onload = function () {
        const pageKey = window.location.pathname; // Get the current page path
        const onboardingModal = document.getElementById('onboardingModal');
        const closeOnboardingModalButton = document.getElementById('closeOnboardingModalButton');

        // Check if the modal was closed for this page in the current session
        const isOnboardingCompleted = userData.getItem("onboarding");
        if (isOnboardingCompleted) {
            onboardingModal.classList.add("hidden"); // Add hidden class if closed
        } else {
            onboardingModal.classList.remove("hidden"); // Remove hidden to make it visible initially
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape' && onboardingModal.classList.contains('flex')) {
                    closeOnboardingModal();
                }
            });
        }

        // Close modal logic
        if (closeOnboardingModalButton) {
            closeOnboardingModalButton.addEventListener('click', () => {
                closeOnboardingModal();
            });
        }

        // Close modal when clicking outside the modal content
        onboardingModal.addEventListener('click', (event) => {
            if (event.target === onboardingModal) {
                closeOnboardingModal();
            }
        });

        function closeOnboardingModal() {
            userData.setItem("onboarding", true);
            onboardingModal.classList.remove('flex');
            onboardingModal.classList.add('hidden');
        }

        // Function to highlight the active box and set the text dynamically
        const highlightActiveBox = () => {
            const pageKey = window.location.pathname; // Get the current page path
            const pageName = pageKey.split("/").pop(); // Get the last part of the URL

            const activePathDisplay = document.getElementById('activePathDisplay');
            const activePageText = document.getElementById('activePageText');
            const arrowIndicator = document.getElementById('arrowIndicator');

            // Define a mapping object to handle dynamic active class assignments
            const sectionMapping = {
                'options': {
                    class: 'options-box',
                    name: 'Options'
                },
                'backtesting': {
                    class: 'backtesting-box',
                    name: 'Backtesting'
                },
                'scalping': {
                    class: 'scalping-box',
                    name: 'Scalping'
                },
                'live': {
                    class: 'live-box',
                    name: 'Live AI Agent'
                },
                'portfolio': {
                    class: 'portfolio-box',
                    name: 'View Portfolio'
                },
                'chain': {
                    class: 'trading-box',
                    name: 'Option Chain'
                },
                'spreads': {
                    class: 'spreads-box',
                    name: 'Spreads'
                },
                'straddle-scanner': {
                    class: 'straddle-scanner-box',
                    name: 'Straddle Scanner'
                },
                'spread-analyzer': {
                    class: 'spread-analyzer-box',
                    name: 'Spread Analyzer'
                },
                'strike-scanner': {
                    class: 'strike-scanner-box',
                    name: 'Strike Scanner'
                },
                'covered-call': {
                    class: 'covered-call-box',
                    name: 'Covered Call'
                },
                'cash-secured-put': {
                    class: 'cash-secured-put-box',
                    name: 'Cash Secured Put'
                },
                'futures-arbitrage': {
                    class: 'futures-arbitrage-box',
                    name: 'Futures Arbitrage'
                },
                'exchanges-arbitrage': {
                    class: 'exchanges-arbitrage-box',
                    name: 'Exchanges Arbitrage'
                },
                'put-call-parity': {
                    class: 'put-call-parity-box',
                    name: 'Put Call Parity'
                },
                'box-spread-arbitrage': {
                    class: 'box-spread-arbitrage-box',
                    name: 'Box Spread Arbitrage'
                },
                'screener-arbitrage': {
                    class: 'screener-box',
                    name: 'Screener'
                },
                // Add more mappings as needed for other sections
            };

            // Create an array of all class names in the sectionMapping
            const allBoxClasses = Object.values(sectionMapping).map(section => section.class);

            // Reset any existing active classes from all the boxes listed in the sectionMapping
            document.querySelectorAll(allBoxClasses.join(", ")).forEach(box =>
                box.classList.remove('bg-blue-200', 'border-blue-500')
            );

            // Check if the current page name exists in the sectionMapping
            if (sectionMapping[pageName]) {
                // Get the class for the current section from the mapping and add the active class
                document.querySelectorAll(`.${sectionMapping[pageName].class}`).forEach(box =>
                    box.classList.add('bg-gradient-to-r', 'from-blue-50', 'to-yellow-50', 'via-purple-50')
                );

                // Set the active page text and arrow
                activePageText.innerHTML = `You are here: ${sectionMapping[pageName].name}`;
                arrowIndicator.style.display = 'inline'; // Show the arrow indicator

                // Scroll the active box into view
                const activeBox = document.querySelector(`.${sectionMapping[pageName].class}`);
                if (activeBox) {
                    const modalScrollable = document.getElementById('modalScrollable'); // Target the scrollable container
                    const offset = 100;
                    const elementPosition = activeBox.getBoundingClientRect().top - modalScrollable.getBoundingClientRect().top + modalScrollable.scrollTop;
                    const offsetPosition = elementPosition - offset;

                    modalScrollable.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });
                }
            }

            // Set the text to a generic "You are here"
            activePathDisplay.innerHTML = 'You are here';
        };

        // Call the function to highlight the active box and set the text
        highlightActiveBox();
    };

</script>