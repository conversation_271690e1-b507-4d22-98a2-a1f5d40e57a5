{% include 'blocks/stock-symbols.html' %}
<div
    class="bg-neutral-100 bg-neutral-50 dark:bg-neutral-900 duration-300 flex flex-col isolate min-h-screen overflow-hidden  relative rhs-block transition-width xl:pb-0">
    <div
        class="absolute inset-x-0 bottom-[calc(-702/16*1rem)] top-0 -z-10 bg-[radial-gradient(154.86%_76.83%_at_50%_22.26%,_rgba(243,244,246,0.4)_8.98%,_rgba(243,244,246,1)_45.99%)]">
    </div>

    <svg viewBox="-1000 0 3504 918"
        class="absolute -top-6 left-1/2 -z-10 ml-[calc(-3504/2/16*1rem)] w-[calc(3504/16*1rem)] mix-blend-overlay">
        <path fill="url(#hero-gradient)" d="M3504 918H-1000V0h3504v918Z"></path>
        <defs>
            <radialGradient id="hero-gradient" cx="0" cy="0" r="1"
                gradientTransform="matrix(0 707.279 -1739.2 0 741 159.991)" gradientUnits="userSpaceOnUse">
                <stop stop-color="#6C47FF" stop-opacity="0.6"></stop>
                <stop offset=".412" stop-color="#FFF963" stop-opacity=".8"></stop>
                <stop offset=".623" stop-color="#38DAFD" stop-opacity=".6"></stop>
                <stop offset=".919" stop-color="#6248F6" stop-opacity="0"></stop>
            </radialGradient>
        </defs>
    </svg>

    <svg class="absolute inset-x-0 top-0 -z-10 h-full w-full stroke-gray-300/80 [mask-image:radial-gradient(32rem_32rem_at_center,white,transparent)]"
        aria-hidden="true">
        <defs>
            <pattern id="1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84" width="200" height="200" x="50%" y="-1"
                patternUnits="userSpaceOnUse">
                <path d="M.5 200V.5H200" fill="none"></path>
            </pattern>
        </defs>
        <svg x="50%" y="-1" class="overflow-visible fill-gray-50">
            <path d="M-200 0h201v201h-201Z M600 0h201v201h-201Z M-400 600h201v201h-201Z M200 800h201v201h-201Z"
                stroke-width="0"></path>
        </svg>
        <rect width="100%" height="100%" stroke-width="0" fill="url(#1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84)"></rect>
    </svg>

    <div class="max-w-7xl p-3 mx-auto">

        <div class="flex justify-between mb-5 md:p-6 p-6 pt-6 rounded-xl" style="
                    background: linear-gradient(271deg, #fdfdfd 0%, #e8f6ff 100%);
            ">
            <h1 class="flex flex-wrap font-semibold lg:flex-row lg:items-end md:text-2xl text-xl xl:flex xl:text-3xl">
                <img src="{{ cdn('/static/images/waving-hand.png') }}" alt="Waving Hand" width="60" height="60"
                    class="hidden pr-2 xl:block" />
                Hello,
                {% if User_Data %}
                <span
                    class="bg-clip-text bg-gradient-to-r capitalize from-blue-600 inline-block lg:px-2 overflow-hidden text-transparent to-indigo-600 via-pink-600">{{
                    User_Data.name }}</span>
                <span id="greeting"></span>
                {% else %}
                <span class="flex flex-wrap lg:pl-1">
                    Welcome to <span
                        class="bg-clip-text bg-gradient-to-r capitalize from-blue-600 inline-block px-2 overflow-hidden text-transparent to-indigo-600 via-pink-600">AI
                        Powered</span> Stocks and Options Trading
                </span>
                {% endif %}
            </h1>
        </div>

        <!-- Global search for stocks -->
        <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-6">
            <!-- Stock Input Form -->
            <div>
                <div class="flex flex-col gap-6">
                    <div class="w-full">
                        {% with show_submit_button=true, show_indices=false %}
                        {% include 'blocks/search-active-recent-stocks.html' %}
                        {% endwith %}
                    </div>
                </div>
            </div>
        </div>

        <!-- World Indices Section -->
        {% include 'blocks/home/<USER>' %}

        <!-- Fear factor Section -->
        {% include 'blocks/home/<USER>' %}

        {% if not User_Data %}
        <div class="relative mx-auto max-w-7xl mt-6">
            <div class="rounded-xl p-1"
                style="background-image: linear-gradient(to right bottom, rgb(79, 70, 229) 0%, rgb(165, 56, 164) 50%, rgb(220, 38, 38) 100%);">
                <div class="rounded-lg bg-black/90 backdrop-blur">
                    <div
                        class="flex w-full flex-wrap items-center justify-between gap-4 px-8 py-10 sm:px-16 lg:flex-nowrap">
                        <div class="lg:max-w-xl">
                            <h2
                                class="block w-full pb-2 bg-gradient-to-b from-white to-gray-400 bg-clip-text font-bold text-transparent text-3xl sm:text-4xl">
                                Boost Your Trading Efficiency with AI Precision
                            </h2>
                            <p class="my-4 bg-transparent font-medium leading-relaxed tracking-wide text-gray-400">
                                Artificial Intelligence (AI) Integration: The SmartAI analyzes and interprets
                                market trends and trading
                                strategies, helping users make informed decisions effortlessly.
                            </p>
                        </div>
                        <div class="flex flex-wrap items-center justify-center gap-6 ">

                            <button type="button" onclick="showLoginModal(false)"
                                class="relative inline-block text-lg group">
                                <span
                                    class="relative z-10 block px-5 py-3 overflow-hidden font-medium leading-tight text-gray-800 transition-colors duration-300 ease-out border-2 border-gray-900 rounded-lg group-hover:text-white">
                                    <span class="absolute inset-0 w-full h-full px-5 py-3 rounded-lg bg-gray-50"></span>
                                    <span
                                        class="absolute left-0 w-64 h-64 -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-12 bg-gray-900 group-hover:-rotate-180 ease"></span>
                                    <span class="relative">Try AIBull Today</span>
                                </span>
                                <span
                                    class="absolute bottom-0 right-0 w-full h-12 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-gray-900 rounded-lg group-hover:mb-0 group-hover:mr-0"
                                    data-rounded="rounded-lg"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}


        <!-- Most Active Contracts Section -->
        {% include 'blocks/home/<USER>' %}

        <!-- Most Active Contracts Section -->
        {% include 'blocks/home/<USER>' %}

        <!-- Navigations Section -->
        {% include 'blocks/home/<USER>' %}
    </div>
</div>

{% if User_Data %}
<script>
    function getGreeting() {
        const currentHour = new Date().getHours();
        if (currentHour >= 5 && currentHour < 12) {
            return "Good Morning!";
        } else if (currentHour >= 12 && currentHour < 17) {
            return "Good Afternoon!";
        } else {
            return "Good Evening!";
        }
    }

    document.getElementById("greeting").textContent = getGreeting();
</script>
{% endif %}

<script>
    // Listen for custom event from search component
    document.getElementById('stock-input-submit-btn').addEventListener('click', () => {
        const symbol = document.getElementById('stock-input').value
        handleSearch(symbol);
    });

    function handleSearch(symbol) {
        // Update the route without reloading the page
        window.location.href = `/stocks/${symbol.toLowerCase()}`;
    }
</script>