<!-- Including the supporting files for the options.html -->
{% include 'blocks/payoff-utils/payoff.html' %}
{% include 'blocks/trade/order.html' %}
<script>
    window.OptionsUtils = {
        getStrikePriceForLeg: function (leg, chainData, futurePrices, spotPrice) {
            if (leg.type === 'FUT') {
                leg.price = futurePrices?.[leg?.expiry] ?? spotPrice;
                leg.iv = 0;
                return;
            }

            const option = chainData.find(o =>
                o.expiryDate === (leg.expiryDate) &&
                o.strikePrice === leg.strike
            );

            if (option) {
                const useLTP = window.useLTPForPrices || false;
                const optData = leg.type === 'CE' ? option.CE : option.PE;
                if (optData) {
                    if (useLTP) {
                        // Use LTP if useLTP is true
                        leg.price = optData.lastPrice || optData.ltp || 0;
                    } else {
                        // Use askPrice for buy actions and bidPrice for sell actions if useLTP is false
                        if (leg.action === 'buy') {
                            leg.price = optData.askPrice || optData.lastPrice || optData.ltp || 0;
                        } else {
                            leg.price = optData.bidprice || optData.lastPrice || optData.ltp || 0;
                        }
                    }
                    leg.iv = optData.impliedVolatility || 20;
                }
            }
            return leg;
        },

        // Get available strikes for a given expiry
        getAvailableStrikes: function (chainData, expiry) {
            if (!chainData) return [];
            const strikes = chainData
                .filter(o => o.expiryDate === expiry)
                .map(o => o.strikePrice);
            return Array.from(new Set(strikes)).sort((a, b) => a - b);
        },

        findNearestStrike: function (targetStrike, chainData, expiry) {
            if (!chainData?.length) return targetStrike;
            const strikes = this.getAvailableStrikes(chainData, expiry);
            return strikes.reduce((prev, curr) => {
                return Math.abs(curr - targetStrike) < Math.abs(prev - targetStrike) ? curr : prev;
            });
        },

        getIVFromChain: function (strike, type, chainData) {
            if (!chainData?.length) return 20; // Default IV

            const option = chainData.find(opt =>
                opt.strikePrice === strike &&
                ((type === 'CE' && opt.CE) || (type === 'PE' && opt.PE))
            );

            if (!option) return 20;
            return type === 'CE' ?
                option.CE?.impliedVolatility || 20 :
                option.PE?.impliedVolatility || 20;
        },

        legsToUrlParams: function (legs, symbol, multiplier, backtestDate) {
            const params = new URLSearchParams();

            // Add symbol
            params.set('s', symbol);

            // Add multiplier if it exists and is not 1
            if (multiplier && multiplier !== 1) {
                params.set('multiplier', multiplier);
            }

            // Add backtesting date if it exists
            if (backtestDate) {
                params.set('backtest', backtestDate);
            }

            // Prepare legs data matching the format used in spreads-new
            const legsData = legs.map(leg => ({
                type: leg.type,
                strike: leg.strike,
                action: leg.action,
                price: leg.price,
                lots: leg.lots || 1,
                enabled: leg.enabled !== false,  // default to true if not specified
                expiryDate: leg.expiryDate,
                iv: leg.iv
            })).filter(leg =>
                leg.type &&
                leg.action &&
                (leg.type === 'FUT' || leg.strike) &&
                typeof leg.price === 'number' &&
                leg.expiryDate  // Ensure required fields exist
            );

            if (legsData.length === 0) {
                return window.location.pathname;
            }

            params.set('l', encodeURIComponent(JSON.stringify(legsData)));

            return `${window.location.origin}/options/spreads?${params.toString()}`;
        },

        loadChainData: async function (symbol) {
            try {
                // Build the date query string if window.backtestDate exists.
                let dateQuery = "";
                if (window.backtestDate) {
                    const d = new Date(window.backtestDate);
                    const dd = String(d.getDate()).padStart(2, "0");
                    const mm = String(d.getMonth() + 1).padStart(2, "0");
                    const yyyy = d.getFullYear();
                    dateQuery = `?date=${dd}-${mm}-${yyyy}`;
                }

                // Build options URL using the dateQuery if available.
                let optionsUrl = `https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${symbol}`;
                if (dateQuery) {
                    optionsUrl += dateQuery;
                }
                const chainRes = await fetch(optionsUrl);

                const chainResData = await chainRes.json();
                const records = chainResData?.records;

                if (!records || !records.data || records.data.length === 0) {
                    throw new Error("No chain data found");
                }

                // Extract spot price
                let spotPrice = 0;
                for (const option of records.data) {
                    if (option.CE?.underlyingValue) {
                        spotPrice = option.CE.underlyingValue;
                        break;
                    }
                    if (option.PE?.underlyingValue) {
                        spotPrice = option.PE.underlyingValue;
                        break;
                    }
                }

                // Load futures data
                const futRes = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/futures/${symbol}`);
                const futData = await futRes.json();

                return {
                    chainData: records.data,
                    expiryDates: records.expiryDates || [],
                    spotPrice,
                    futurePrices: futData || {}
                };
            } catch (error) {
                console.error("Error loading chain data:", error);
                return null;
            }
        },


        validateOptionPrice: function (price, strike, type, chainData) {
            if (!chainData?.length) return { valid: true };

            const option = chainData.find(opt =>
                opt.strikePrice === strike &&
                ((type === 'CE' && opt.CE) || (type === 'PE' && opt.PE))
            );

            if (!option) return { valid: true };
            const optData = type === 'CE' ? option.CE : option.PE;

            if (optData?.bidprice && optData?.askprice) {
                const isOutsideSpread = price < optData.bidprice || price > optData.askprice;
                return {
                    valid: !isOutsideSpread,
                    bidprice: optData.bidprice,
                    askprice: optData.askprice
                };
            }

            return { valid: true };
        },

        generateLegId: function (leg) {
            // Use expiryDate if available, otherwise fallback to expiry
            const exp = leg.expiryDate;
            return leg.type === 'FUT'
                ? `FUT-${leg.action}-${exp}`
                : `${leg.type}-${leg.strike}-${leg.action}-${exp}`;
        },

        updateStrike: function (leg, newStrike, mode, chainData, futurePrices, spotPrice) {
            if (!leg || leg.type === 'FUT') return;

            // Preserve the original expiry date by ensuring expiryDate exists.
            if (!leg.expiryDate) {
                leg.expiryDate = leg.expiryDate;
            }

            let updatedStrike;
            if (mode === 'direct') {
                // Direct strike update (from input)
                updatedStrike = this.findNearestStrike(
                    parseFloat(newStrike),
                    chainData,
                    leg.expiryDate
                );
            } else {
                // Increment/Decrement
                const strikes = this.getAvailableStrikes(chainData, leg.expiryDate);
                const currentIndex = strikes.indexOf(leg.strike);
                if (currentIndex === -1) return;

                const increment = mode === 'increment' ? 1 : -1;
                const newIndex = Math.min(Math.max(currentIndex + increment, 0), strikes.length - 1);
                updatedStrike = strikes[newIndex];
            }

            // Update strike and ID
            leg.strike = updatedStrike;
            leg.id = this.generateLegId(leg);

            // Update price based on new strike
            this.getStrikePriceForLeg(
                leg,
                chainData,
                futurePrices,
                spotPrice
            );

            return leg;
        }
    };
</script>

<style>
    /* Toggle switch styles */
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 36px;
        height: 20px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked+.toggle-slider {
        background-color: #3b82f6;
    }

    input:checked+.toggle-slider:before {
        transform: translateX(16px);
    }

    /* Custom scrollbar */
    .custom-scroll::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    .custom-scroll::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .custom-scroll::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    .custom-scroll::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
    }

    /* Expiry slider styles */
    .expiry-slider {
        -webkit-appearance: none;
        appearance: none;
        width: 100%;
        height: 6px;
        border-radius: 5px;
        background: #d3d3d3;
        outline: none;
    }

    .expiry-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #3b82f6;
        cursor: pointer;
    }

    .expiry-slider::-moz-range-thumb {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #3b82f6;
        cursor: pointer;
    }
</style>