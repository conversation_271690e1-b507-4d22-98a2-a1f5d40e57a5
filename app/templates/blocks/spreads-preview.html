<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@^2.0.0"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
{% include 'blocks/options.html' %}

<!-- Preview Modal Block -->
<div id="preview-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
    <!-- Modal background overlay -->
    <div class="absolute inset-0 bg-black opacity-50" onclick="window.PreviewModal.hide()"></div>
    <!-- Modal container - increase max width -->
    <div class="bg-white z-10 rounded shadow-lg w-[95vw] h-[90vh] flex flex-col rounded-lg overflow-hidden">
        <!-- Modal header -->
        <div class="flex justify-between items-center p-4 border-b bg-white sticky top-0 z-10">
            <div>
                <h2 class="text-lg font-semibold">Strategy Preview &amp; Payoff</h2>
                <div class="flex items-center gap-2">
                    <div class="text-sm text-gray-600">
                        <span id="preview-symbol" class="font-medium"></span>
                        <span class="mx-2">•</span>
                        <span id="preview-spot"></span>
                    </div>
                    <!-- Add timestamp component -->
                    <div id="preview-timestamp" class="timestamp-component"></div>
                </div>
            </div>
            <div>
                <div class="flex items-center gap-2">
                    <button onclick="window.PreviewModal.openInSpreads()"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                        View More
                    </button>
                    <button onclick="window.PreviewModal.hide()"
                        class="text-red-600 hover:text-red-800 hover:bg-red-50 rounded-full w-8 h-8 flex items-center justify-center text-2xl font-bold transition-colors">
                        &times;
                    </button>
                </div>
            </div>
        </div>
        <!-- Modal body - make scrollable -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Left Panel: Display Legs - with its own scroll -->
            <div id="preview-legs" class="w-1/4 p-4 border-r overflow-y-auto">
                <!-- Interactive legs table will be injected here -->
            </div>
            <!-- Right Panel: Display Payoff Info & Graph - with its own scroll -->
            <div id="preview-payoff" class="w-3/4 overflow-y-auto">
                <!-- Payoff content will be injected here by renderPayoffUI -->
            </div>
        </div>
    </div>
</div>

<script>
    class PreviewModal {
        constructor() {
            this.state = {
                legs: [],
                symbol: '',
                spotPrice: 0,
                optionChain: [],
                expiryDates: [],
                allowedTypes: ["CE", "PE", "FUT"],
                chart: null,
                timestamp: null,
                multiplier: 1
            };

            this.init();
        }

        init() {
            // Set up event listeners for modal closing
            document.querySelectorAll('.close-modal').forEach(btn => {
                btn.addEventListener('click', () => this.hide());
            });
        }

        show(config) {
            // Clear previous chart if it exists
            if (window.payoffChartInstance) {
                window.payoffChartInstance.destroy();
            }

            // Clear previous UI elements
            document.getElementById('preview-legs').innerHTML = '';
            document.getElementById('preview-payoff').innerHTML = '';
            document.getElementById('preview-symbol').textContent = '';
            document.getElementById('preview-spot').textContent = '';

            this.state = {
                ...this.state,
                legs: JSON.parse(JSON.stringify(config.legs)).map(leg => {
                    // Add IV calculation and ensure enabled is true for each leg
                    const updatedLeg = {
                        ...leg,
                        // Update leg ID
                        id: leg.type === 'FUT'
                            ? `FUT-${leg.action}-${leg.expiryDate}`
                            : `${leg.type}-${leg.strike}-${leg.action}-${leg.expiryDate}`,
                        enabled: true  // Set enabled to true by default
                    };

                    // Update leg price and IV using the utility function
                    window.OptionsUtils.getStrikePriceForLeg(
                        updatedLeg,
                        config.optionChain,
                        config.futurePrices,
                        config.spotPrice
                    );

                    return updatedLeg;
                }),
                symbol: config.symbol,
                spotPrice: config.spotPrice,
                optionChain: config.optionChain ? JSON.parse(JSON.stringify(config.optionChain)) : [],
                expiryDates: config.expiryDates || [],
                futurePrices: config.futurePrices || {},
                allowedTypes: config.allowedTypes || ["CE", "PE", "FUT"]
            };

            // Update header content
            document.getElementById('preview-symbol').textContent = this.state.symbol;
            document.getElementById('preview-spot').textContent = `Spot: ${this.state.spotPrice}`;

            // Initialize timestamp component
            if (!this.state.timestamp) {
                this.state.timestamp = window.createTimestamp('preview-timestamp', {
                    showRefresh: true,
                    onRefresh: () => this.refreshData()
                });
            } else {
                this.state.timestamp.updateTimestamp();
            }

            // Render payoff UI
            const payoffContainer = document.getElementById('preview-payoff');
            window.PayoffUtils.renderPayoffUI(payoffContainer, {
                legs: this.state.legs,
                symbol: this.state.symbol,
                spotPrice: this.state.spotPrice,
                chainData: this.state.optionChain,
                multiplier: this.state.multiplier,
                chartId: 'preview-payoutChart',
                showShare: false,  // Disable share button for spreads preview
                expiryDates: this.state.expiryDates
            });

            this.renderLegsTable();
            document.getElementById('preview-modal').classList.remove('hidden');
        }

        hide() {
            document.getElementById('preview-modal').classList.add('hidden');
        }

        renderLegsTable() {
            const container = document.getElementById('preview-legs');
            if (!this.state.legs?.length) {
                container.innerHTML = "<p class='text-center text-gray-600'>No legs available.</p>";
                return;
            }

            let html = `
        <table class="w-full text-sm border-collapse">
            <thead>
                <tr class="bg-gray-100">
                    <th class="border p-1 text-center">Type</th>
                    <th class="border p-1 text-center">Action</th>
                    <th class="border p-1 text-center">Strike</th>
                    <th class="border p-1 text-center">Expiry</th>
                    <th class="border p-1 text-center">Price</th>
                </tr>
            </thead>
            <tbody>`;

            this.state.legs.forEach((leg, index) => {
                html += `
        <tr class="hover:bg-gray-50">
            <td class="border p-1 text-center">
                <select data-index="${index}" class="leg-type bg-gray-100 border rounded px-1 py-0.5">
                    ${this.state.allowedTypes.includes("CE") ?
                        `<option value="CE" ${leg.type === 'CE' ? 'selected' : ''}>CE</option>` : ''}
                    ${this.state.allowedTypes.includes("PE") ?
                        `<option value="PE" ${leg.type === 'PE' ? 'selected' : ''}>PE</option>` : ''}
                    ${this.state.allowedTypes.includes("FUT") ?
                        `<option value="FUT" ${leg.type === 'FUT' ? 'selected' : ''}>FUT</option>` : ''}
                </select>
            </td>
            <td class="border p-1 text-center">
                <button data-index="${index}" 
                        class="leg-action border px-2 py-0.5 rounded focus:outline-0 
                        ${leg.action === 'buy' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}">
                    ${leg.action === 'buy' ? 'B' : 'S'}
                </button>
            </td>
            <td class="border p-1 text-center">
                ${leg.type === 'FUT' ?
                        `<div class="text-center text-gray-500">N/A</div>` :
                        `<div class="flex items-center">
                        <button class="bg-gray-200 border hover:bg-gray-300 px-1" 
                                onclick="window.PreviewModal.updateStrike(${index}, null, 'decrement')" 
                                ${leg.type === 'FUT' ? 'disabled' : ''}>-</button>
                        <input type="number" 
                               value="${leg.strike || ''}" 
                               class="border px-1 text-center w-20 focus:outline-0 focus:border-blue-400 ${leg.type === 'FUT' ? 'bg-gray-100' : ''}" 
                               onchange="window.PreviewModal.updateStrike(${index}, this.value, 'direct')" 
                               ${leg.type === 'FUT' ? 'disabled' : ''} />
                        <button class="bg-gray-200 border hover:bg-gray-300 px-1" 
                                onclick="window.PreviewModal.updateStrike(${index}, null, 'increment')" 
                                ${leg.type === 'FUT' ? 'disabled' : ''}>+</button>
                    </div>`
                    }
            </td>
            <td class="border p-1 text-xs text-center">
                ${(leg.expiry || leg.expiryDate)?.split('-')?.slice(0, 2).join('-')}
            </td>
            <td class="border p-1 text-center">
                <input type="number" disabled value="${leg.price}" 
                       data-index="${index}"
                       class="leg-price border px-1 text-center w-20" />
            </td>
        </tr>`;
            });
            html += '</tbody></table>';
            container.innerHTML = html;

            this.setupLegsTableListeners();
        }

        setupLegsTableListeners() {
            // Type change 
            document.querySelectorAll('.leg-type').forEach(select => {
                select.addEventListener('change', (e) => {
                    const index = parseInt(e.target.dataset.index);
                    const leg = this.state.legs[index];
                    const newType = e.target.value;

                    // Update type
                    leg.type = newType;

                    // Update price based on new type
                    if (leg.type === 'FUT') {
                        // For futures, use the future price if available
                        leg.price = this.state.futurePrices[leg.expiry] || this.state.spotPrice;
                        leg.iv = 0;
                    } else if (leg.strike) {
                        // For options, get the market price for the new option type at current strike
                        window.OptionsUtils.getStrikePriceForLeg(
                            leg,
                            this.state.optionChain,
                            this.state.futurePrices,
                            this.state.spotPrice
                        );
                    }

                    this.renderLegsTable();
                    this.updatePayoff();
                });
            });

            // Action toggle B/S
            document.querySelectorAll('.leg-action').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const index = parseInt(e.target.dataset.index);
                    const leg = this.state.legs[index];

                    // Toggle action
                    leg.action = leg.action === 'buy' ? 'sell' : 'buy';

                    // Update price based on new action if it's an option
                    if (leg.type !== 'FUT') {
                        window.OptionsUtils.getStrikePriceForLeg(
                            leg,
                            this.state.optionChain,
                            this.state.futurePrices,
                            this.state.spotPrice
                        );
                    }

                    this.renderLegsTable();
                    this.updatePayoff();
                });
            });

            // Price change
            document.querySelectorAll('.leg-price').forEach(input => {
                input.addEventListener('change', (e) => {
                    const index = parseInt(e.target.dataset.index);
                    const leg = this.state.legs[index];
                    leg.price = parseFloat(e.target.value) || 0;

                    if (leg.type !== 'FUT') {
                        const validation = window.OptionsUtils.validateOptionPrice(
                            leg.price,
                            leg.strike,
                            leg.type,
                            this.state.optionChain
                        );

                        input.classList.toggle('bg-red-50', !validation.valid);
                        input.title = !validation.valid ?
                            `Warning: Price outside bid-ask spread (${validation.bidprice}-${validation.askprice})` : '';
                    }

                    this.updatePayoff();
                });
            });
        }

        updatePayoff() {
            // Re-render payoff UI with current state
            const payoffContainer = document.getElementById('preview-payoff');
            window.PayoffUtils.renderPayoffUI(payoffContainer, {
                legs: this.state.legs,
                symbol: this.state.symbol,
                spotPrice: this.state.spotPrice,
                chainData: this.state.optionChain,
                multiplier: this.state.multiplier,
                chartId: 'preview-payoutChart',
                showShare: false,  // Disable share button for spreads preview
                expiryDates: this.state.expiryDates
            });
        }

        refreshData() {
            // Show loading spinner in the preview-payoff section
            showLoadingSpinner('preview-payoff-info', 'Refreshing data...');

            window.OptionsUtils.loadChainData(this.state.symbol).then(data => {
                // Hide loading spinner once data is received
                hideLoadingSpinner('preview-payoff-info');

                if (!data) {
                    console.error('No chain data found');
                    return;
                }

                // Update state with new data
                this.state.optionChain = data.chainData;
                this.state.expiryDates = data.expiryDates;
                this.state.spotPrice = data.spotPrice;
                this.state.futurePrices = data.futurePrices;

                // Update prices and IVs for all legs
                this.state.legs.forEach(leg => {
                    window.OptionsUtils.getStrikePriceForLeg(
                        leg,
                        this.state.optionChain,
                        this.state.futurePrices,
                        this.state.spotPrice
                    );
                });

                // Update header content
                document.getElementById('preview-symbol').textContent = this.state.symbol;
                document.getElementById('preview-spot').textContent = `Spot: ${this.state.spotPrice}`;

                // Update the legs table
                this.renderLegsTable();

                // Update payoff
                this.updatePayoff();

                // Update the timestamp
                if (this.state.timestamp) {
                    this.state.timestamp.updateTimestamp();
                }
            }).catch(error => {
                hideLoadingSpinner('preview-payoff-info');
                console.error('Error refreshing data:', error);
            });
        }

        openInSpreads() {
            if (!this.state.legs?.length) {
                showAlert("No legs available");
                return;
            }

            try {
                const newUrl = window.OptionsUtils.legsToUrlParams(
                    this.state.legs,
                    this.state.symbol,
                    this.state.multiplier
                );
                window.open(newUrl, '_blank');
            } catch (error) {
                console.error('Error generating spreads URL:', error);
                showAlert("Error opening in spreads");
            }
        }

        updateStrike(index, newStrike, mode = 'direct') {
            const leg = this.state.legs[index];
            window.OptionsUtils.updateStrike(
                leg,
                newStrike,
                mode,
                this.state.optionChain,
                this.state.futurePrices,
                this.state.spotPrice
            );

            this.renderLegsTable();
            this.updatePayoff();
        }
    }

    // Create a singleton instance
    const previewModal = new PreviewModal();

    // Export for global use
    window.PreviewModal = {
        show: (config) => previewModal.show(config),
        hide: () => previewModal.hide(),
        openInSpreads: () => previewModal.openInSpreads(),
        updateStrike: (index, newStrike, mode) => previewModal.updateStrike(index, newStrike, mode)
    };
</script>