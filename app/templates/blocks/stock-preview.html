<div id="stock-preview" class="modal fixed opacity-0 z-50 hidden">
    <div class="relative bg-white border border-white dark:bg-neutral-800 rounded-xl shadow-2xl w-[350px] md:w-[500px] "
        style="background-image: radial-gradient(circle at 100% 0%, #f1e9ff, #f2feff, #f2f2f2, #f8f8f8, #f3f3f3, #fcffe969, #e8fdff);">
        <div class="absolute w-3 h-3 bg-[#9473cf] z-[-1] shadow-2xl dark:bg-neutral-800 transform rotate-45 -translate-y-1.5 hidden"
            id="modal-arrow"></div>
        <div id="modal-content"
            class="p-4 md:max-h-[500px] max-h-[600px] overflow-y-auto overflow-x-hidden custom-scroll">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<style>
    #stock-preview {
        transition: opacity 0.2s ease-in-out;
    }

    #stock-preview.hidden {
        display: none;
    }
</style>

<script>
    class StockModal {
        constructor() {
            this.modal = document.getElementById('stock-preview');
            this.modalContent = document.getElementById('modal-content');
            this.isLoading = false;
            this.currentSymbol = null;
            this.currentController = null;
            this.isAnimating = false;
            this.modalContainer = this.modal.querySelector('div');
            this.modalArrow = document.getElementById('modal-arrow');

            // Bind methods to this instance
            this.close = this.close.bind(this);
            this.show = this.show.bind(this);
            this.handleClickOutside = this.handleClickOutside.bind(this);

            // Initialize event listeners
            this.initializeEventListeners();

            // Add event delegation initialization
            this.initializeStockClickHandlers();

            // Add window resize handler
            this.handleWindowResize = this.handleWindowResize.bind(this);
            window.addEventListener('resize', this.handleWindowResize);
        }

        static loadingHTML = `
        <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
    `;

        initializeEventListeners() {
            this.modal.addEventListener('click', this.handleClickOutside);
        }

        handleClickOutside(e) {
            if (e.target === this.modal) {
                this.close();
            }
        }

        positionModal(targetElement) {
            // Make modal visible but transparent to get dimensions
            this.modal.classList.remove('hidden');

            // Force a reflow to get accurate dimensions
            void this.modal.offsetHeight;

            const targetRect = targetElement.getBoundingClientRect();
            const modalRect = this.modalContainer.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;

            // Calculate positions
            let top = targetRect.bottom + 10;
            let left = targetRect.left;
            let arrowLeft = 20;

            // Check if modal would go below viewport
            if (top + modalRect.height > viewportHeight) {
                top = targetRect.top - modalRect.height - 10;
                this.modalArrow.style.top = 'auto';
                this.modalArrow.style.bottom = '-6px';
                this.modalArrow.style.transform = 'rotate(225deg)';
            } else {
                this.modalArrow.style.top = '-6px';
                this.modalArrow.style.bottom = 'auto';
                this.modalArrow.style.transform = 'rotate(45deg)';
            }

            // Check if modal would go off right edge
            if (left + modalRect.width > viewportWidth) {
                const overflow = left + modalRect.width - viewportWidth;
                left -= overflow + 20;
                arrowLeft = targetRect.left - left + targetRect.width / 2;
            }

            // Check if modal would go off left edge
            if (left < 0) {
                arrowLeft = targetRect.left + targetRect.width / 2;
                left = 20;
            }

            // Position modal
            this.modal.style.top = `${top}px`;
            this.modal.style.left = `${left}px`;

            // Position arrow
            this.modalArrow.style.left = `${arrowLeft}px`;
            this.modalArrow.classList.remove('hidden');

            // Make modal visible with animation
            requestAnimationFrame(() => {
                this.modal.style.opacity = '1';
            });
        }

        handleWindowResize() {
            if (!this.modal.classList.contains('hidden') && this.lastClickedElement) {
                this.positionModal(this.lastClickedElement);
            }
        }

        close(e) {
            e?.stopPropagation();
            this.modal.style.opacity = '0';

            // Wait for fade out animation before hiding
            setTimeout(() => {
                this.modal.classList.add('hidden');
                this.modalArrow.classList.add('hidden');
                this.lastClickedElement = null;
                this.isAnimating = true;

                // Abort any ongoing fetch request
                if (this.currentController) {
                    this.currentController.abort();
                    this.currentController = null;
                }

                // Reset states
                this.isLoading = false;
                this.currentSymbol = null;
            }, 200); // Match the transition duration
        }

        formatNumber(value, decimals = 2) {
            if (!value || isNaN(value)) return 'N/A';
            return Number(value).toFixed(decimals);
        }

        formatMarketCap(value) {
            if (!value || isNaN(value)) return 'N/A';
            value = Number(value);
            const billion = 1000000000;
            const million = 1000000;
            if (value >= billion) {
                return `₹${(value / billion).toFixed(2)}B`;
            }
            return `₹${(value / million).toFixed(2)}M`;
        }

        formatLargeNumber(value) {
            if (!value || isNaN(value)) return 'N/A';
            return new Intl.NumberFormat('en-IN').format(Number(value));
        }

        async show(symbol, clickedElement) {
            this.lastClickedElement = clickedElement;

            // Remove backdrop classes
            this.modal.classList.remove('inset-0', 'bg-black/70', 'backdrop-blur-sm');
            this.modal.style.opacity = '0';

            // Position the modal
            if (clickedElement) {
                this.positionModal(clickedElement);
            }

            // Show the modal
            this.modal.classList.remove('hidden');

            // Prevent multiple requests for the same symbol
            if (symbol === this.currentSymbol && !this.modal.classList.contains('hidden')) {
                return;
            }

            // If already loading, cancel previous request
            if (this.isLoading && this.currentController) {
                this.currentController.abort();
            }

            // Setup new request
            this.isAnimating = false;
            this.isLoading = true;
            this.currentSymbol = symbol;
            this.currentController = new AbortController();

            // Show loading state immediately
            this.modalContent.innerHTML = StockModal.loadingHTML;

            try {
                const response = await fetch(`/api/stock/${symbol}`, {
                    signal: this.currentController.signal
                });

                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const stockData = await response.json();
                if (!stockData?.info) throw new Error('Invalid stock data received');

                // Only update UI if this is still the current symbol being viewed
                if (symbol === this.currentSymbol) {
                    const priceChange = stockData.info.regularMarketChangePercent || 0;
                    const changeColor = priceChange >= 0 ? 'text-green-500' : 'text-red-500';
                    const changeArrow = priceChange >= 0 ? '▲' : '▼';

                    this.modalContent.innerHTML = `
                    <div class="space-y-3">
                        <!-- Header Section with adjusted layout -->
                        <div class="pb-3 mb-3 border-b dark:border-neutral-700 relative">
                            <!-- Close button with onclick handler -->
                            <button onclick="stockModal.close(event)" class="absolute right-[-17px] top-[-9px] p-1 group hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-full transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 group-hover:text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>

                            <div class="md:flex items-start justify-between pr-8"> <!-- Added right padding -->
                                <div>
                                    <h2 class="text-neutral-900 font-semibold text-2xl md:text-3xl leading-tight">
                                        ${stockData.info.longName || stockData.info.shortName || symbol}
                                    </h2>
                                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-0.5">
                                        ${stockData.info.symbol} • ${stockData.info.industryDisp || 'N/A'}
                                    </p>
                                </div>
                                <div class="md:text-right mt-3 md:mt-0">
                                    <div class="text-lg text-neutral-900  font-semibold">₹${stockData.info.regularMarketPrice?.toFixed(2) || 'N/A'}</div>
                                    <div class="flex items-center md:justify-end gap-1 ${changeColor} text-sm">
                                        <span>${changeArrow}</span>
                                        <span>${Math.abs(priceChange).toFixed(2)}%</span>
                                        <span class="text-xs">(₹${stockData.info.regularMarketChange?.toFixed(2) || '0.00'})</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Key Stats Grid -->
                        <div class="grid md:grid-cols-3 grid-cols-2 gap-2 text-sm">
                            <div class="bg-gray-50 dark:bg-neutral-900 p-2 rounded-lg">
                                <div class="text-xs text-gray-500 dark:text-gray-400">Day Range</div>
                                <div class="font-medium mt-0.5">₹${this.formatNumber(stockData.info.dayLow)} - ₹${this.formatNumber(stockData.info.dayHigh)}</div>
                            </div>
                            <div class="bg-gray-50 dark:bg-neutral-900 p-2 rounded-lg">
                                <div class="text-xs text-gray-500 dark:text-gray-400">Volume</div>
                                <div class="font-medium mt-0.5">${this.formatLargeNumber(stockData.info.volume)}</div>
                            </div>
                            <div class="bg-gray-50 dark:bg-neutral-900 p-2 rounded-lg">
                                <div class="text-xs text-gray-500 dark:text-gray-400">Market Cap</div>
                                <div class="font-medium mt-0.5">${this.formatMarketCap(stockData.info.marketCap)}</div>
                            </div>
                        </div>

                        <!-- Metrics Grid -->
                        <div class="grid lg:grid-cols-4 md:grid-cols-4 grid-cols-2 gap-2 text-sm">
                            <div class="bg-gray-50 dark:bg-neutral-900 p-2 rounded-lg">
                                <div class="text-xs text-gray-500 dark:text-gray-400">P/E Ratio</div>
                                <div class="font-medium mt-0.5">${this.formatNumber(stockData.info.trailingPE)}</div>
                            </div>
                            <div class="bg-gray-50 dark:bg-neutral-900 p-2 rounded-lg">
                                <div class="text-xs text-gray-500 dark:text-gray-400">Book Value</div>
                                <div class="font-medium mt-0.5">₹${this.formatNumber(stockData.info.bookValue)}</div>
                            </div>
                            <div class="bg-gray-50 dark:bg-neutral-900 p-2 rounded-lg">
                                <div class="text-xs text-gray-500 dark:text-gray-400">Beta</div>
                                <div class="font-medium mt-0.5">${this.formatNumber(stockData.info.beta)}</div>
                            </div>
                            <div class="bg-gray-50 dark:bg-neutral-900 p-2 rounded-lg">
                                <div class="text-xs text-gray-500 dark:text-gray-400">EPS</div>
                                <div class="font-medium mt-0.5">₹${this.formatNumber(stockData.info.epsTrailingTwelveMonths)}</div>
                            </div>
                        </div>

                        <!-- About Section -->
                        <div class="bg-gray-50 dark:bg-neutral-900 p-3 rounded-lg">
                            <h2 class="text-sm font-medium mb-1">About</h2>
                            <p class="text-xs text-gray-500 dark:text-gray-300 line-clamp-3">
                                ${stockData.info.longBusinessSummary || 'No description available.'}
                            </p>
                        </div>

                        <!-- Contact Section -->
                        <div class="bg-gray-50 dark:bg-neutral-900 p-3 rounded-lg text-xs">
                            <h2 class="text-sm font-medium mb-1">Contact</h2>
                            <div class="md:divide-x gap-3 grid grid-cols1 md:grid-cols-2">
                                <div>
                                    <div class="text-gray-600 dark:text-gray-400 mb-1">Address</div>
                                    <div>
                                        ${stockData.info.address1}<br>
                                        ${stockData.info.address2}<br>
                                        ${stockData.info.city}, ${stockData.info.zip}
                                    </div>
                                </div>
                                <div class="md:pl-3">
                                    <div class="text-gray-600 dark:text-gray-400 mb-1">Details</div>
                                    <div class="space-y-1">
                                        <div>${stockData.info.phone || 'N/A'}</div>
                                        <a href="${stockData.info.website}" target="_blank" class="text-blue-600 hover:underline block truncate">${stockData.info.website}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                }
            } catch (error) {
                console.error("Error fetching stock details:", error);
                // Don't show error if request was aborted
                if (error.name === 'AbortError') {
                    return;
                }

                if (symbol === this.currentSymbol) {
                    this.modalContent.innerHTML = `
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">Unable to load data</h3>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Please try again later.</p>
                    </div>
                `;
                }
            } finally {
                if (symbol === this.currentSymbol) {
                    this.isLoading = false;
                    this.currentController = null;
                }
            }
        }

        initializeStockClickHandlers() {
            document.addEventListener('click', (e) => {
                const stockElement = e.target.closest('.stock');
                if (stockElement) {
                    const symbol = stockElement.dataset.stock;
                    if (symbol) {
                        this.show(symbol, stockElement);
                    }
                }
            });

            // Close modal when clicking outside
            document.addEventListener('click', (e) => {
                if (!this.modal.contains(e.target) &&
                    !e.target.closest('.stock') &&
                    !this.modal.classList.contains('hidden')) {
                    this.close();
                }
            });
        }
    }

    // Initialize the modal instance
    const stockModal = new StockModal();

    // This function can still be kept for backward compatibility
    function showStockPreview(symbol, element) {
        stockModal.show(symbol, element);
    }
</script>