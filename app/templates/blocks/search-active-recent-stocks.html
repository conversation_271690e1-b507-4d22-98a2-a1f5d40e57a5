<!-- Search bar at the top -->
<div>
    <label for="stock-input" class="block font-medium mb-2">Search Symbol</label>
    <div class="relative">
        <div class="md:flex items-center relative w-full">
            <div class="relative flex-1">
                <input id="stock-input" type="text" autocomplete="off" class="w-full px-4 py-3.5 border-2 border-gray-300 
                    {% if show_submit_button is not defined or show_submit_button %}
                   rounded-l-md rounded-r-md md:border-r-0 md:rounded-r-none md:rounded-l-md 
                    {% else %}
                    rounded-md
                    {% endif %}
                    dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all"
                    placeholder="e.g. RELIANCE, HDFC, TCS" />
                <button id="clear-search"
                    class="hidden items-center justify-center p-2 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-full absolute right-2 top-1/2 -translate-y-1/2 transition-all"
                    type="button" aria-label="Clear search">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-x h-4 w-4 text-gray-500 hover:text-red-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                    </svg>
                </button>
            </div>

            {% if show_submit_button is not defined or show_submit_button %}
            <button id="stock-input-submit-btn"
                class="mt-3 min-w-[200px] md:mt-0 w-full md:w-fit bg-blue-700 block focus:outline-0 font-medium hover:bg-blue-900 px-6 py-4 rounded-md md:rounded-l-none md:rounded-r-md text-white transition-colors">
                Search
            </button>
            {% endif %}
        </div>
        <!-- Recent Searches -->
        <div id="recent-searches" class="my-3">
            <div id="recent-searches-list" class="flex flex-wrap gap-1">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>


        {% if enable_backtesting %}
        <!-- Backtesting area: Initially only a button to open the modal -->
        <div class="mt-4  text-sm flex items-center text-nowrap">
            <!-- The checkbox and label will be shown after a date is selected -->
            <input type="checkbox" id="enable-backtesting" onchange="window.toggleBacktesting(this.checked)"
                class="hidden mr-1" />
            <label id="backtesting-label" for="enable-backtesting"
                class="text-sm text-gray-700 dark:text-gray-300 hidden">
                Enable Backtesting (<span id="selected-date"></span>)
            </label>
            <button id="select-date-btn" onclick="window.openBacktestingModal()"
                class="md:text-sm text-xs text-blue-500 pl-1 underline">
                Enable Backtesting

            </button>
        </div>
        {% endif %}
    </div>
</div>

{% if enable_backtesting %}
<!-- Modal for selecting the backtesting date -->
<div id="backtesting-modal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50">
    <div class="bg-white dark:bg-neutral-800 p-6 rounded-lg max-w-md">
        <h2 class="text-lg font-medium mb-2 dark:text-white">Select Backtesting Date</h2>
        <p class="mb-4 text-sm text-gray-600 dark:text-gray-300">
            AIBull supports backtesting. Selecting a calendar date will fetch the option chain for that particular day.
        </p>
        <input type="date" id="modal-date-picker"
            class="w-full border px-3 py-2 rounded dark:bg-neutral-700 dark:border-neutral-600 dark:text-white" />
        <div class="mt-4 flex justify-end">
            <button id="modal-cancel-btn" onclick="window.closeBacktestingModal()"
                class="mr-3 px-4 py-1.5  font-medium text-sm border rounded dark:border-neutral-600 dark:text-white hover:bg-gray-100 dark:hover:bg-neutral-700 transition-colors">Cancel</button>
            <button id="modal-ok-btn"
                onclick="window.setBacktestingDate(document.getElementById('modal-date-picker').value)"
                class="px-4 py-1.5 text-sm font-medium bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">Confirm</button>
        </div>
    </div>
</div>
{% endif %}

<div id="stock-tabs-container">
    <div id="most-active-stocks" class="border border-white mt-3 p-2 rounded-md"
        style="background-image: linear-gradient(45deg, #f0faff, #ffffff);">
        {% if show_indices is not defined or show_indices %}
        <h3 class="font-medium mb-2 text-neutral-600 uppercase text-xs">Indices</h3>
        <div class="flex flex-wrap gap-1">
            {% set indices = ['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY', 'NIFTYNXT50'] %}
            {% for index in indices %}
            <div class="most-active-stock px-2 py-1.5 text-neutral-700 text-sm border bg-white dark:border-neutral-600 
                            rounded cursor-pointer hover:bg-blue-100 dark:hover:bg-neutral-700 transition-colors"
                onclick="handleStockSymbolClick('{{ index }}')">
                <div class="flex justify-between items-center">
                    <span class="font-medium stock-symbol-title text-xs">{{ index }}</span>
                </div>
            </div>
            {% endfor %}
        </div>
        <hr class="my-3 border-gray-200 border-dashed">
        {% endif %}


        {% if active_stocks %}
        <h3 class="font-medium mb-2 text-neutral-600 uppercase text-xs">Most Active Stocks</h3>
        <div class="flex flex-wrap gap-1">
            {% for stock in active_stocks %}
            <div class="most-active-stock px-2 py-1.5 text-neutral-700 text-sm border bg-white dark:border-neutral-600 
                                              rounded cursor-pointer hover:bg-blue-100 dark:hover:bg-neutral-700 transition-colors"
                onclick="handleStockSymbolClick('{{ stock.symbol }}')">
                <div class="flex justify-between items-center">
                    <span class="font-medium stock-symbol-title text-xs">{{ stock.symbol }}</span>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Saved strategies Load -->
{% if load_strategies == "true" %}
{% include 'options/spreads/strategy.html' %}
{% endif %}

<!-- Backtesting Stock strategies UI -->
{% if load_scalping_strategies == "true" %}
{% include 'algos/scalping/load-save-strategies.html' %}
{% endif %}

<!-- Configuration block for show_indices -->
<div id="config" data-show-indices="{{ show_indices | default('true') }}"></div>
<script>
    // Retrieve the value from the data attribute and convert it to a Boolean.
    const configEl = document.getElementById('config');
    window.show_indices = configEl.getAttribute('data-show-indices') === 'true';
</script>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        // Initialize recent searches from storage
        const recentSearchesKey = 'recentSearches';

        window.isOptionsChainPage = window.location.href.includes('options/chain');
        const backtestingLabel = document.getElementById('backtesting-label');
        if (backtestingLabel) {
            backtestingLabel.innerHTML = window.isOptionsChainPage ? `Show History (<span id="selected-date"></span>)` : `Enable Backtesting (<span id="selected-date"></span>)`;
        }
        // Function to update recent searches UI
        function updateRecentSearchesUI() {
            let recentSearches = userData.getItem(recentSearchesKey) || [];
            if (!Array.isArray(recentSearches)) {
                recentSearches = [];
            }
            const searchInputValue = document.getElementById('stock-input')?.value || "";
            const recentSearchesSection = document.getElementById('recent-searches');
            const container = document.getElementById('recent-searches-list');
            if (!container || !recentSearchesSection) return;
            if (recentSearches.length === 0) {
                recentSearchesSection.style.display = 'none';
                return;
            }
            recentSearchesSection.style.display = 'block';
            recentSearches = recentSearches
                .filter(symbol => symbol !== searchInputValue)
                .filter(symbol => {
                    if (!window.show_indices) {
                        return !indexTickers.includes(symbol);
                    }
                    return true;
                });
            container.innerHTML = recentSearches
                .map(symbol => `
                    <div class="recent-search-item px-2 py-1.5 text-neutral-700 text-sm border bg-white 
                                dark:border-neutral-600 rounded cursor-pointer hover:bg-blue-100 
                                dark:hover:bg-neutral-700 transition-colors"
                             onclick="handleStockSymbolClick('${symbol}')">
                        <div class="flex justify-between items-center gap-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-history"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/><path d="M3 3v5h5"/><path d="M12 7v5l4 2"/></svg>
                            <span class="font-medium text-xs">${symbol}</span>
                        </div>
                    </div>
                `).join('');
        }

        function addToRecentSearches(symbol) {
            let recentSearches = userData.getItem(recentSearchesKey) || [];
            if (!Array.isArray(recentSearches)) {
                recentSearches = [];
            }
            if (!symbol || typeof symbol !== 'string') return;
            symbol = symbol.trim().toUpperCase();
            if (symbol.length < 2 || symbol.length > 20) {
                console.log('Invalid symbol length:', symbol);
                return;
            }
            if (recentSearches?.length > 0 && recentSearches[0] === symbol) {
                return;
            }
            recentSearches = [symbol, ...recentSearches.filter(s => s !== symbol)];
            recentSearches = recentSearches.slice(0, 10);
            userData.setItem(recentSearchesKey, recentSearches);
            updateRecentSearchesUI();
        }

        window.removeFromRecentSearches = function (symbol) {
            let recentSearches = userData.getItem(recentSearchesKey) || [];
            if (!symbol || typeof symbol !== 'string') return;
            symbol = symbol.trim().toUpperCase();
            recentSearches = recentSearches.filter(s => s !== symbol);
            userData.setItem(recentSearchesKey, recentSearches);
            updateRecentSearchesUI();
        }

        updateRecentSearchesUI();

        const searchIds = ["stock-input", "stockSearchInput", "stock-search", "search-input"];
        const debounce = (func, delay) => {
            let timer;
            return function (...args) {
                clearTimeout(timer);
                timer = setTimeout(() => func.apply(this, args), delay);
            };
        };

        searchIds.forEach(id => {
            const input = document.getElementById(id);
            if (!input) return;
            const dropdown = document.createElement('div');
            dropdown.className = 'autocomplete-dropdown bg-white border border-gray-300 shadow-md dark:bg-neutral-800 dark:border-neutral-600';
            dropdown.style.display = 'none';
            dropdown.style.position = 'absolute';
            dropdown.style.zIndex = 1000;
            const parent = input.parentNode;
            if (parent) {
                if (window.getComputedStyle(parent).position === 'static') {
                    parent.style.position = 'relative';
                }
                parent.appendChild(dropdown);
                dropdown.style.top = (input.offsetTop + input.offsetHeight) + 'px';
                dropdown.style.left = input.offsetLeft + 'px';
                dropdown.style.width = input.offsetWidth + 'px';
            }
            input.addEventListener('input', debounce(async (e) => {
                const query = e.target.value.trim();
                if (!query) {
                    dropdown.style.display = 'none';
                    return;
                }
                try {
                    const response = await fetch(`/api/search/find?query=${encodeURIComponent(query)}`);
                    const data = await response.json();
                    const suggestions = data.result || [];
                    if (!suggestions.length) {
                        dropdown.style.display = 'none';
                        return;
                    }
                    dropdown.innerHTML = '';
                    suggestions.forEach(suggestion => {
                        const suggestionItem = document.createElement('div');
                        suggestionItem.className = 'p-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700';
                        suggestionItem.textContent = suggestion;
                        suggestionItem.addEventListener('click', () => {
                            input.value = suggestion;
                            dropdown.style.display = 'none';
                            const enterEvent = new KeyboardEvent('keydown', {
                                key: 'Enter',
                                code: 'Enter',
                                keyCode: 13,
                                which: 13,
                                bubbles: true
                            });
                            input.dispatchEvent(enterEvent);
                        });
                        dropdown.appendChild(suggestionItem);
                    });
                    dropdown.style.display = 'block';
                } catch (error) {
                    console.error('Error fetching suggestions:', error);
                    dropdown.style.display = 'none';
                }
            }, 300));

            input.addEventListener("keydown", (e) => {
                if (e.key === "Enter") {
                    e.preventDefault();
                    if (dropdown.style.display === "block" && dropdown.children.length > 0) {
                        const firstSuggestion = dropdown.children[0].textContent;
                        input.value = firstSuggestion;
                        dropdown.style.display = 'none';
                    }
                    handleStockSymbolClick(input.value);
                }
            });

            document.addEventListener('click', (e) => {
                if (e.target !== input && !dropdown.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });

            window.addEventListener('resize', () => {
                if (parent) {
                    dropdown.style.top = (input.offsetTop + input.offsetHeight) + 'px';
                    dropdown.style.left = input.offsetLeft + 'px';
                    dropdown.style.width = input.offsetWidth + 'px';
                }
            });
        });

        searchIds.forEach(id => {
            const input = document.getElementById(id);
            if (!input) return;
            input.addEventListener("keydown", (e) => {
                if (e.key === "Enter") {
                    const symbol = input.value.trim().toUpperCase();
                    if (symbol) {
                        addToRecentSearches(symbol);
                    }
                }
            });
            const searchButton = document.getElementById(`${input.id}-submit-btn`);
            if (searchButton) {
                searchButton.addEventListener("click", (e) => {
                    addToRecentSearches(input.value);
                });
            }
        });

        window.handleStockSymbolClick = function (symbol) {
            const searchInput = searchIds.map(id => document.getElementById(id)).find(el => el);
            if (searchInput) {
                searchInput.value = symbol;
                window.handleSearchInputChange(symbol);
                const searchButton = document.getElementById(`${searchInput.id}-submit-btn`);
                if (searchButton) {
                    searchButton.click();
                } else {
                    const form = searchInput.closest("form");
                    if (form) form.submit();
                }
            }
        };

        searchIds.forEach(id => {
            const input = document.getElementById(id);
            if (!input) return;
            const submitBtn = document.getElementById(`${input.id}-submit-btn`);
            if (submitBtn) {
                submitBtn.addEventListener('click', () => {
                    document.dispatchEvent(new CustomEvent('stockSearchTriggered', {
                        detail: { symbol: input.value.trim().toUpperCase() }
                    }));
                });
            }
        });

        const searchInput = document.getElementById('stock-input');
        const clearButton = document.getElementById('clear-search');
        const tabsContainer = document.getElementById('stock-tabs-container');

        function toggleClearButton(show) {
            if (clearButton) {
                updateRecentSearchesUI();
                clearButton.style.display = show ? 'flex' : 'none';
            }
        }

        function toggleTabs(show) {
            if (tabsContainer) {
                tabsContainer.style.display = show ? 'block' : 'none';
            }
        }

        searchInput.addEventListener('input', (e) => {
            window.handleSearchInputChange(e.target.value);
        });

        window.handleSearchInputChange = function (value) {
            const hasValue = value.trim().length > 0;
            toggleClearButton(hasValue);
            toggleTabs(!hasValue);
        }

        clearButton.addEventListener('click', () => {
            searchInput.value = '';
            toggleClearButton(false);
            toggleTabs(true);
            searchInput.focus();

            // Emit a custom event when search is cleared
            document.dispatchEvent(new CustomEvent('stockSearchCleared', {
                detail: { action: 'clear' }
            }));
        });

        searchInput.addEventListener('focus', () => {
            if (searchInput.value.trim()) {
                toggleTabs(false);
            }
        });
    });
</script>

<script>
    document.addEventListener("DOMContentLoaded", () => {
        document.querySelectorAll('.most-active-stock').forEach(stockEl => {
            const symbol = stockEl.querySelector('.stock-symbol-title').textContent.trim();
            if (!foTickers.includes(symbol) && !indexTickers.includes(symbol)) {
                stockEl.onclick = null;
                stockEl.classList.add('cursor-not-allowed', 'opacity-50');
                stockEl.setAttribute('title', `${symbol} is not available for derivatives trading`);
            }
        });
    });
</script>

{% if enable_backtesting %}
<script>
    document.addEventListener('DOMContentLoaded', () => {
        // Elements for the modal and backtesting UI
        const selectDateBtn = document.getElementById('select-date-btn');
        const backtestingModal = document.getElementById('backtesting-modal');
        const modalDatePicker = document.getElementById('modal-date-picker');
        const backtestingCheckbox = document.getElementById('enable-backtesting');
        const backtestingLabel = document.getElementById('backtesting-label');
        const selectedDateSpan = document.getElementById('selected-date');

        selectDateBtn.textContent = window.isOptionsChainPage ? 'Show History' : 'Enable Backtesting';

        // Ensure the checkbox and label are hidden initially.
        backtestingCheckbox.classList.add('hidden');
        backtestingLabel.classList.add('hidden');

        // Set modal date picker's maximum date to yesterday.
        const today = new Date();
        today.setDate(today.getDate() - 1);
        const dd = String(today.getDate()).padStart(2, '0');
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const yyyy = today.getFullYear();
        modalDatePicker.max = `${yyyy}-${mm}-${dd}`;

        // Function to open the backtesting modal
        window.openBacktestingModal = function () {
            // If a date has been previously selected, retain it.
            if (window.backtestDate) {
                modalDatePicker.value = window.backtestDate;
            }
            backtestingModal.classList.remove('hidden');
        };

        // Function to close the backtesting modal
        window.closeBacktestingModal = function () {
            backtestingModal.classList.add('hidden');
        };

        // Function to set the backtesting date
        window.setBacktestingDate = function (selectedDate) {
            if (selectedDate) {
                // Format date as DD-MM-YYYY.
                const dateObj = new Date(selectedDate);
                const dd = String(dateObj.getDate()).padStart(2, '0');
                const mm = String(dateObj.getMonth() + 1).padStart(2, '0');
                const yyyy = dateObj.getFullYear();
                const formattedDate = `${dd}-${mm}-${yyyy}`;
                selectedDateSpan.textContent = formattedDate;

                // Show the checkbox and label.
                backtestingCheckbox.classList.remove('hidden');
                backtestingLabel.classList.remove('hidden');

                // Check the box.
                backtestingCheckbox.checked = true;

                // Update the button text.
                selectDateBtn.textContent = 'Change Date';

                // Set the global variable.
                window.backtestDate = selectedDate;

                // Update URL if needed
                if (typeof updateUrlParams === "function") {
                    updateUrlParams();
                }
            }
            window.closeBacktestingModal();
        };

        // Function to disable backtesting
        window.toggleBacktesting = function (checked) {
            if (!checked) {
                // When unchecked, remove the global variable and hide the checkbox and label.
                window.backtestDate = undefined;
                selectedDateSpan.textContent = '';
                backtestingCheckbox.classList.add('hidden');
                backtestingLabel.classList.add('hidden');
                // Reset the select date button text.
                selectDateBtn.textContent = window.isOptionsChainPage ? 'Show History' : 'Enable Backtesting';

                // Update URL
                updateUrlParams();
            }
        };

        // Function to reset backtesting state
        window.resetBacktestingState = function () {
            if (window.backtestDate) {
                // Reset the backtesting UI elements if they exist
                const backtestingCheckbox = document.getElementById('enable-backtesting');
                const backtestingLabel = document.getElementById('backtesting-label');
                const selectDateBtn = document.getElementById('select-date-btn');
                const selectedDateSpan = document.getElementById('selected-date');

                if (backtestingCheckbox) {
                    backtestingCheckbox.checked = false;
                    backtestingCheckbox.classList.add('hidden');
                }

                if (backtestingLabel) {
                    backtestingLabel.classList.add('hidden');
                }

                if (selectDateBtn) {
                    selectDateBtn.textContent = window.isOptionsChainPage ? 'Show History' : 'Enable Backtesting';
                }

                if (selectedDateSpan) {
                    selectedDateSpan.textContent = '';
                }

                // Clear the global backtesting date
                window.backtestDate = undefined;
            }
        };
    });
</script>
{% endif %}