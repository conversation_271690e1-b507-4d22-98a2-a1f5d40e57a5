<div class="mb-8 md:mb-0 md:max-w-lg">
    <h1 class="lg:text-3xl text-2xl font-bold">Sign Up for AIBull Today
        <span class="text-black"> <span class="relative"> <svg class="absolute h-full w-[110px] md:right-0 top-6"
                    viewBox="0 0 135 9" height="9" width="135" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M36.54 1.016C40.01.912 43.39.78 46.95.712 50.51.644 54.071.567 57.81.566c2.744.002 5.018-.162 7.897-.113 9.89.085 20.486.459 31.646 1.116 2.484.151 4.835.242 7.296.39 2.461.147 4.924.282 7.34.413 1.528.069 3.186.202 4.684.31a187 187 0 0 1 4.89.34c3.416.326 6.937.738 10.5 1.23 2.316.32 2.482.762 1.474 1.152-1.082.485-3.3.708-6.3.635-.705-.026-1.39-.039-2.117-.076l-2.202-.137-4.43-.268a899.607 899.607 0 0 1-8.75-.477c-2.953-.174-5.754-.262-8.71-.427-2.955-.165-5.803-.257-8.829-.424-1.786-.084-3.509-.137-5.156-.16-1.697-.039-3.396-.07-5.027-.081l-9.497.059c-6.873.071-13.98.132-20.388.403-4.089.123-7.886.344-11.683.565l-8.169.637c-2.596.256-5.236.496-7.537.828-1.768.261-3.332.576-4.873.895-1.541.319-2.877.683-4.575.95-.775.112-1.367.265-2.142.376-2.903.406-4.781.312-8.094-.282a79.95 79.95 0 0 1-2.301-.412C.465 7.541-.327 6.866.558 6.205c.714-.471 1.384-.971 2.398-1.395 1.013-.424 2.483-.741 3.838-1.08 1.355-.34 3.28-.546 5.025-.802 1.744-.256 3.69-.446 5.594-.66C23.24 1.688 29.49 1.233 36.13.904l.408.112Z"
                        class="fill-current" fill-rule="nonzero" opacity=".32"></path>
                </svg> </span> </span>
    </h1>
    <p class="mt-6 text-lg">Optimize your trading portfolio with
        AI-powered tools
        and strategies. Automate trading insights & maximize returns</p>
</div>

<div
    class="absolute bg-white shadow-lg  flex-shrink-0 hidden lg:block p-8 right-[3rem] rounded-[20px] top-[-50px] z-[1] w-[440px]">
    <div>
        <div>
            <form id="signupForm" class="space-y-6">
                <div class="space-y-6">
                    <div>
                        <div class="flex items-center justify-between">
                            <label class="text-base heading font-medium text-gray-900">
                                Name
                            </label>
                        </div>
                        <div class="mt-2.5 relative text-gray-400 focus-within:text-gray-600">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-circle-user w-5 h-5">
                                    <circle cx="12" cy="12" r="10" />
                                    <circle cx="12" cy="10" r="3" />
                                    <path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662" />
                                </svg>
                            </div> <input id="signupName" type="name" autocomplete="current-password"
                                required="required" placeholder="Enter your name"
                                class="block w-full py-3 pl-10 pr-4 text-black placeholder-gray-400 transition-all duration-200 border border-gray-200 rounded-md bg-gray-50 focus:outline-none focus:border-neutral-600 focus:bg-white caret-blue-600">
                        </div>
                    </div>
                    <div>
                        <label class="font-medium heading flex justify-start text-base text-gray-900 text-start">
                            Email or Phone
                            Number </label>
                        <div class="mt-2.5 relative text-gray-400 focus-within:text-gray-600">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-at-sign w-5 h-5">
                                    <circle cx="12" cy="12" r="4" />
                                    <path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8" />
                                </svg>
                            </div>
                            <input id="emailPhoneSignup" name="email" type="text" autocomplete="off" required="required"
                                placeholder="Enter your email or phone number"
                                class="block w-full py-3 pl-10 pr-4 text-black placeholder-gray-400 transition-all duration-200 border border-gray-200 rounded-md bg-gray-50 focus:outline-none focus:border-neutral-600 focus:bg-white caret-blue-600">
                        </div>
                    </div>
                    <div>
                        <div class="relative group w-full mb-[12px]" type="submit">
                            <button type="submit" id="signupSubmit"
                                class="active:text-blue-100 bg-neutral-900 border border-neutral-900 cta-button cursor-pointer ease-in-out flex flex-col focus-visible:outline-2 focus-visible:outline-neutral-900 focus-visible:outline-offset-2 focus:outline-none font-bold font-semiBold group heading hover:shadow-xl inline-flex items-center justify-center px-10 py-3 relative rounded-[5px] text-white text-base transition-all w-full">
                                <span class="absolute inset-0 overflow-hidden rounded-full">
                                    <span
                                        class="absolute inset-0 rounded-full bg-[image:radial-gradient(75%_100%_at_50%_0%,rgba(125,76,3)_0%,rgba(56,189,248,0)_75%)] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>
                                </span>
                                Signup Today
                                <span
                                    class="absolute -bottom-0 left-[1.125rem] h-px w-[calc(100%-2.25rem)] bg-gradient-to-r from-amber-400/0 via-amber-400/90 to-amber-400/0 transition-opacity duration-500 group-hover:opacity-40"></span>
                            </button>
                        </div>
                        <!-- OTP Form (initially hidden) -->
                        <div id="signupOtpForm" class="hidden my-[12px]">
                            <h3 class="text-sm font-medium hidden" id="otpFormHeader">Please
                                Verify Your Phone
                                or Email
                            </h3>
                            <p class="my-2 text-xs text-neutral-500" id="otpFormDescription">We
                                have sent an OTP
                                to your
                                contact. Please enter it below to verify.</p>
                            <div class="flex border rounded border-neutral-300">
                                <input type="text" id="otpSignupInput" placeholder="Enter OTP" autocomplete="off"
                                    class="shadow lowercase placeholder:normal-case appearance-none rounded w-full py-2.5 px-3 md:text-[16px] text-[13px] text-neutral-800 focus:outline-none focus:shadow-outline" />
                                <button id="verifySignOtpBtn"
                                    class="bg-neutral-800 focus:outline-none focus:shadow-outline font-semibold px-4 py-2 rounded-r text-white">Verify</button>
                            </div>
                        </div>

                        <div class="relative">
                            <div class="absolute inset-0 flex items-center" aria-hidden="true">
                                <div class="w-full border-t border-gray-200"></div>
                            </div>
                            <div class="relative flex justify-center text-sm/6 font-medium">
                                <span class="bg-white px-6 text-gray-900">Or</span>
                            </div>
                        </div>

                        <div id="g_id_signin"
                            class="g_id_signin mt-[12px] mb-1.5 flex mx-auto justify-center [&_.nsm7Bb-HzV7m-LgbsSe]:h-[53px] [&_.nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf]:ml-[0px]"
                            data-type="standard" data-shape="rectangular" data-theme="filled_blue"
                            data-text="signin_with" data-size="large" data-logo_alignment="left">
                        </div>

                    </div>
                </div>
            </form>
        </div>
    </div>
    <div id="validationSignupMessage" class="text-red-500 text-sm mb-1"></div>
</div>

{% include 'blocks/google-signup.html' %}
<script>
    window.onload = function () {
        renderGoogleButton("g_id_signin");
    };
</script>
<script>
    const signupForm = document.getElementById("signupForm");
    const signupOtpForm = document.getElementById("signupOtpForm");
    const verifySignupOtpButton = document.getElementById("verifySignOtpBtn");
    const otpSignupInput = document.getElementById("otpSignupInput");
    const signupSubmit = document.getElementById("signupSubmit");
    const contactSignupInput = document.getElementById("emailPhoneSignup");
    let encSignupGlobal; // Define this in the global scope

    verifySignupOtpButton.addEventListener("click", function () {
        const otp = otpSignupInput.value.trim();
        verifySignupOTP(otp, encSignupGlobal); // Function to verify OTP
    });

    otpSignupInput.addEventListener("keydown", function (event) {
        if (event.key === "Enter") {
            event.preventDefault();
            verifySignupOtpButton.click();
        }
    });

    signupForm.addEventListener("submit", function (event) {
        event.preventDefault(); // Prevent form submission
        if (validateSignupForm()) {
            sendSignupFormData();
        }
    });
    function verifySignupOTP(otp, enc_str) {
        const requestData = {
            otp,
            enc_str,
        };

        // Send the request to verify the OTP
        fetch(`${window.Mantra_AI_Server}/auth/verify`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(requestData),
        })
            .then((response) => response.json())
            .then(async (data) => {
                if (data.status === "verified") {

                    showModal("Success", "OTP verified successfully", false);
                    starkAuthForm.classList.add("hidden");
                    otpForm.classList.add("hidden");

                    // Set the access token as a cookie
                    // document.cookie = `access_token=${data.access_token}; path=/; max-age=${60 * 24 * 30 * 60}; SameSite=Lax`;
                    document.cookie = `access_token=${data.access_token}; path=/; max-age=${60 * 24 * 30 * 60}; SameSite=None; Secure`;

                    if (data.data.type === "signup") {
                        // Save to Database
                        await userData.saveAllItemsToServer();

                        if (window.dataLayer) {
                            window.dataLayer.push({
                                event: "signup",
                                contact: data.data.contact,
                                otpType: data.data.type,
                            });
                        }
                    }
                    window.location.href = "/";
                    document.getElementById('signupName').value = ''
                } else {
                    showModal("Please try again", "Failed to verify OTP");
                }
            });
    }

    function showSignupError(message) {
        const validationMessage = document.getElementById("validationSignupMessage");
        validationMessage.innerHTML = ""; // Clear previous error messages

        const errorMessage = document.createElement("div");
        errorMessage.textContent = message;
        errorMessage.classList.add("text-red-500", "text-sm", "mt-1");
        validationMessage.appendChild(errorMessage);

        // Clear the error message after 3 seconds
        setTimeout(() => {
            validationMessage.innerHTML = "";
        }, 3000);
    }

    function validateSignupForm() {
        const nameInput = document.getElementById("signupName");
        const contactValue = contactSignupInput.value.trim();
        const nameValue = nameInput.value.trim();

        // Clear previous validation messages
        const validationMessage = document.getElementById("validationSignupMessage");
        validationMessage.innerHTML = "";

        // Validate name if signup
        if (nameValue.length < 4) {
            showSignupError("Name must be at least 4 characters long.");
            return false;
        }

        // Validate contact input (must be a valid phone number or email)
        const isPhoneNumber = /^\+?[1-9]\d{1,14}$/.test(contactValue);
        const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contactValue);

        if (!isPhoneNumber && !contactValue.includes('@') && contactValue.startsWith('+')) {
            showSignupError("Please enter a valid phone number or email.");
            return false;
        }

        if (isPhoneNumber && !contactValue.startsWith('+')) {
            showSignupError("Please enter your country code, e.g., +1 (for USA), +44 (for UK), +91 (for India), etc.");
            return false;
        }

        if (!isPhoneNumber && !isEmail) {
            showValidationError("Please enter a valid phone number or email.");
            return false;
        }

        return true;
    }

    function sendSignupFormData() {
        const signupName = document.getElementById('signupName').value
        const emailSignup = document.getElementById('emailPhoneSignup').value
        const path = "/auth/signup";
        const url = `${window.Mantra_AI_Server}${path}`;

        const body = { name: signupName, contact: emailSignup };


        fetch(url, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(body),
        })
            .then((response) => {
                if (!response.ok) {
                    // If the response status is not in the 200-299 range, throw an error
                    return response.json().then((data) => {
                        throw new Error(data.detail || "Error sending OTP");
                    });
                }
                return response.json();
            })
            .then((data) => {
                if (data.status === "success") {
                    encSignupGlobal = data.enc_str;
                    signupOtpForm.classList.remove("hidden"); // Show the OTP form
                    signupSubmit.textContent = "Resend OTP";

                    if (isSignupPhoneNumber(contact)) {
                        otpFormHeader.textContent = "Please Verify Your Phone";
                        otpFormDescription.textContent = "We have sent an OTP to your phone number. Please enter it below to verify.";
                    } else {
                        otpFormHeader.textContent = "Please Verify Your Email";
                        otpFormDescription.textContent = "We have sent an OTP to your email. Please enter it below to verify.";
                    }
                } else {
                    console.error("Error sending OTP");
                }
            })
            .catch((error) => {
                showSignupError(error.message);
                console.error("Error:", error);
            });
    }

    window.addEventListener("pageshow", (event) => {

        // Check if the page was loaded from the bfcache (indicating back/forward navigation)
        if (event.persisted) {

            // Optionally refresh the page
            window.location.reload();
        }
    });
    function resetOtpForm() {
        signupOtpForm.classList.add("hidden");
        otpSignupInput.value = "";
    }
    function isSignupPhoneNumber(contact) {
        return /^\+?[1-9]\d{1,14}$/.test(contact);
    }

    function isEmail(contact) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact);
    }
</script>