<div id="feedbackModal"
    class="modal fixed inset-0 bg-black bg-opacity-50 hidden z-[51] overflow-hidden flex items-center justify-center">
    <div
        class="bg-white alret-modal dark:bg-neutral-900 max-w-[800px] overflow-auto overflow-x-hidden rounded-2xl w-full relative">
        <div
            class="flex justify-between sticky top-0 left-0 py-3 pl-6 pr-2 mb-4 z-[99] bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700">
            <div>
                <h3 class="text-xl font-bold text-neutral-900 dark:text-neutral-200">Feedback Form</h3>
                <p class="mt-1 text-neutral-600 text-sm dark:text-neutral-300">Thank you for trying our AI-driven app!
                    Your feedback is valuable in helping us improve the app and its features. Please take a few minutes
                    to share your thoughts.</p>
            </div>
            <button onclick="closeFeedbackModal()"
                class="dark:hover:text-neutral-100 dark:text-neutral-300 flex hover:text-neutral-800 pt-3 pr-3 text-neutral-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-x">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                </svg>
            </button>
        </div>
        <form onsubmit="submitFeedback(event)" class="w-full mx-auto">
            <div class="px-6 max-h-[calc(100vh_-_15rem)] overflow-y-auto overflow-x-hidden custom-scroll">
                <div class="mb-5 pb-3 border-dashed border-b border-gray-200 dark:border-neutral-600">
                    <label class="block text-sm font-medium text-neutral-900 dark:text-neutral-100 mb-2">How easy was it
                        to navigate the app?<span class="text-red-500">*</span></label>
                    <div class="flex space-x-6 text-neutral-500 items-center">
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="navigate_experience" value="1" required
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">1</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="navigate_experience" value="2"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">2</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="navigate_experience" value="3"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">3</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="navigate_experience" value="4"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">4</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="navigate_experience" value="5"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">5</span>
                        </label>
                    </div>
                </div>

                <div class="mb-5 pb-3 border-dashed border-b border-gray-200 dark:border-neutral-600">
                    <label class="block text-sm font-medium text-neutral-900 dark:text-neutral-100 mb-2">Did you
                        encounter
                        any technical issues while using the app? <span class="text-red-500">*</span></label>
                    <div class="flex space-x-6 text-neutral-500 items-center">
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="technical_issues" value="yes" required
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">Yes</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="technical_issues" value="no"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">No</span>
                        </label>
                    </div>
                </div>


                <div class="mb-5">
                    <label
                        class="block text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-100 mb-2">If
                        yes, please
                        describe the issue.</label>
                    <textarea id="technical_issue_description" name="technical_issue_description" rows="3"
                        class="border text-[14px] px-3 py-2.5 rounded w-full border-gray-200 focus:outline-none text-neutral-900 md:min-h-[100px] min-h-[150px]"
                        placeholder="Describe the issue..." disabled></textarea>
                </div>

                <div class="mb-5 pb-3 border-dashed border-b border-gray-200 dark:border-neutral-600">
                    <label
                        class="block text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-100 mb-2">How
                        helpful did you
                        find the AI-Driven tools like Strike Scanner, Spread Analyzer, and Long Straddle
                        Scanner?</label>
                    <div class="flex space-x-6 text-neutral-500 items-center">
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="ai_tools_experience" value="1"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">1</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="ai_tools_experience" value="2"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">2</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="ai_tools_experience" value="3"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">3</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="ai_tools_experience" value="4"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">4</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="ai_tools_experience" value="5"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">5</span>
                        </label>
                    </div>
                </div>

                <div class="mb-5 pb-3 border-dashed border-b border-gray-200 dark:border-neutral-600">
                    <label class="block text-sm font-medium text-neutral-900 dark:text-neutral-100 mb-2">How relevant
                        and
                        accurate were the trading tools like Covered Call, Cash Secured Put, and Put Call
                        Parity?</label>
                    <div class="flex space-x-6 text-neutral-500 items-center">
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="trading_tools_experience" value="1"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">1</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="trading_tools_experience" value="2"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">2</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="trading_tools_experience" value="3"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">3</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="trading_tools_experience" value="4"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">4</span>
                        </label>
                        <label
                            class="flex items-center text-sm font-medium cursor-pointer text-neutral-900 dark:text-neutral-400 ">
                            <input type="radio" name="trading_tools_experience" value="5"
                                class="form-radio w-4 h-4 accent-black" />
                            <span class="ml-2">5</span>
                        </label>
                    </div>
                </div>

                <div class="mb-5">
                    <label class="block text-sm font-medium text-neutral-900 dark:text-neutral-100 mb-2">Do you have any
                        suggestions or features you'd like to see in the app?</label>
                    <textarea id="additional_comments" name="additional_comments" rows="3"
                        class="border text-[14px] px-3 py-2.5 rounded w-full border-gray-200 focus:outline-none text-neutral-900 md:min-h-[100px] min-h-[150px]"
                        placeholder="Your comments..."></textarea>
                </div>

                <div class="mb-5">
                    <label class="block text-sm font-medium text-neutral-900 dark:text-neutral-100 mb-2">What did you
                        like
                        most about the app?.</label>
                    <textarea id="platform_feedback" name="platform_feedback" rows="3"
                        class="border text-[14px] px-3 py-2.5 rounded w-full border-gray-300 focus:outline-none text-neutral-900 md:min-h-[100px] min-h-[150px]"
                        placeholder="Your answer..."></textarea>
                </div>
            </div>
            <div class="flex justify-end bg-neutral-100 dark:bg-neutral-800 px-6 py-3 rounded-b-md">
                <button type="submit"
                    class="bg-blue-500 block focus:outline-0 font-medium hover:bg-blue-600 px-6 py-2.5 text-sm rounded-md text-white transition-colors">
                    Submit
                </button>
            </div>
        </form>
    </div>
</div>


<script>
    const feedbackCrud = new Crud('feedback');

    async function showFeedbackModal() {
        document.getElementById('feedbackModal').classList.remove('hidden');
    }

    function closeFeedbackModal() {
        document.getElementById('feedbackModal').classList.add('hidden');
    }

    async function submitFeedback(event) {
        event.preventDefault(); // Prevent the default form submission

        const formData = {
            navigate_experience: document.querySelector('input[name="navigate_experience"]:checked').value,
            ai_tools_experience: document.querySelector('input[name="ai_tools_experience"]:checked') ? document.querySelector('input[name="ai_tools_experience"]:checked').value : '',
            questions_quizzes_experience: document.querySelector('input[name="questions_quizzes_experience"]:checked').value,
            flash_card_smart_notes_experience: document.querySelector('input[name="flash_card_smart_notes_experience"]:checked').value,
            mugs_experience: document.querySelector('input[name="mugs_experience"]:checked') ? document.querySelector('input[name="mugs_experience"]:checked').value : '',
            ai_generated_ques_experience: document.querySelector('input[name="ai_generated_ques_experience"]:checked') ? document.querySelector('input[name="ai_generated_ques_experience"]:checked').value : '',
            recommend: document.querySelector('input[name="recommend"]:checked') ? document.querySelector('input[name="recommend"]:checked').value : '',
            technical_issues: document.querySelector('input[name="technical_issues"]:checked').value,
            technical_issue_description: document.querySelector('textarea[name="technical_issue_description"]').value,
            platform_feedback: document.querySelector('textarea[name="platform_feedback"]').value,
            additional_comments: document.querySelector('textarea[name="additional_comments"]').value,
            user_name: userData?.data?.name,
            user_email: userData?.data?.email
        };

        // Ensure the technical issue description is included only if 'Yes' is selected for technical issues.
        if (formData.technical_issues === 'no') {
            formData.technical_issue_description = '';
        }

        try {
            const response = await feedbackCrud.create(formData);
            if (response.status === "success") {
                showAlert("Thank you for your valuable feedback");
                closeFeedbackModal();
            } else {
                showModal('Failed to submit feedback. Please try again');
                closeFeedbackModal();
            }
        } catch (error) {
            showModal('Failed to submit feedback. Please try again');
            closeFeedbackModal();
        }
    }

    // Enable or disable the description field based on the selected option for technical issues.
    document.querySelectorAll('input[name="technical_issues"]').forEach((radio) => {
        radio.addEventListener('change', function () {
            const descriptionField = document.querySelector('textarea[name="technical_issue_description"]');
            if (this.value === 'yes') {
                descriptionField.disabled = false;
            } else {
                descriptionField.disabled = true;
                descriptionField.value = '';
            }
        });
    });

</script>
<style>
    /* Custom scrollbar styling */
    .overflow-y-auto::-webkit-scrollbar {
        width: 8px;
    }


    .overflow-y-auto::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
    }


    .overflow-y-auto:hover::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.4);
    }
</style>