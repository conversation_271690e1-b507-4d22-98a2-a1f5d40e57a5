<!-- Fear Factor Component -->
<div>
    <div class="bg-white dark:bg-neutral-800 rounded-xl p-6"
        style="background-image: linear-gradient(45deg, #ffffff, #f0fbff);">
        <div class="border-b border-dashed flex items-center justify-between mb-3 pb-3">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
                Fear Factor
            </h2>
        </div>

        <div class="grid md:grid-cols-2 gap-8 items-center">
            <!-- Gauge Component -->
            <div class="relative flex items-center justify-center">
                <div class="relative w-48 h-48 md:w-80 md:h-56">
                    <svg viewBox="0 0 200 150" class="w-full">
                        <!-- Arc backgrounds -->
                        <path d="M20 100 A 80 80 0 0 1 48.28 41.72" fill="none" stroke="#f8d7da" stroke-width="16"
                            stroke-linecap="round" />
                        <path d="M48.28 41.72 A 80 80 0 0 1 87.52 25.08" fill="none" stroke="#f3aa4e" stroke-width="16"
                            stroke-linecap="round" />
                        <path d="M87.52 25.08 A 80 80 0 0 1 112.48 25.08" fill="none" stroke="#ffc107" stroke-width="16"
                            stroke-linecap="round" />
                        <path d="M112.48 25.08 A 80 80 0 0 1 151.72 41.72" fill="none" stroke="#4ade80"
                            stroke-width="16" stroke-linecap="round" />
                        <path d="M151.72 41.72 A 80 80 0 0 1 180 100" fill="none" stroke="#16a34a" stroke-width="16"
                            stroke-linecap="round" />
                        <!-- Labels -->
                        <text x="20" y="115" font-size="8" fill="#ef4444" font-weight="bold" text-anchor="middle"
                            class="dark:fill-red-300">EXTREME</text>
                        <text x="20" y="125" font-size="8" fill="#ef4444" font-weight="bold" text-anchor="middle"
                            class="dark:fill-red-300">FEAR</text>
                        <text x="60" y="15" font-size="9" fill="#f97316" font-weight="bold" text-anchor="middle"
                            class="dark:fill-orange-300">FEAR</text>
                        <text x="100" y="10" font-size="9" fill="#eab308" font-weight="bold" text-anchor="middle"
                            class="dark:fill-yellow-300">NEUTRAL</text>
                        <text x="140" y="15" font-size="9" fill="#22c55e" font-weight="bold" text-anchor="middle"
                            class="dark:fill-green-300">GREED</text>
                        <text x="180" y="115" font-size="8" fill="#16a34a" font-weight="bold" text-anchor="middle"
                            class="dark:fill-green-400">EXTREME</text>
                        <text x="180" y="125" font-size="8" fill="#16a34a" font-weight="bold" text-anchor="middle"
                            class="dark:fill-green-400">GREED</text>
                        <!-- Needle -->
                        <g id="gauge-needle" transform="rotate(-90 100 100)">
                            <circle cx="100" cy="100" r="4" fill="#fb923c" />
                            <path id="needle" d="M100 100 L100 30" stroke="#fb923c" stroke-width="4"
                                stroke-linecap="round" />
                        </g>
                        <!-- Value display -->
                        <text id="gauge-value" x="100" y="140" font-size="16" fill="#fb923c" font-weight="bold"
                            text-anchor="middle">0.00</text>
                    </svg>
                </div>
            </div>

            <!-- Info Section -->
            <div class="space-y-6 bg-gray-50 rounded-lg p-4">
                <div>
                    <div class="mb-2">
                        <span class="text-xl font-semibold text-gray-900 dark:text-gray-200">Current Market Sentiment -
                        </span>
                        <span id="sentiment-text" class="text-2xl font-bold text-gray-800 dark:text-gray-200"></span>
                    </div>
                    <p id="sentiment-desc" class="text-gray-700 dark:text-gray-300"></p>
                </div>
                <div class="grid md:grid-cols-3 grid-cols-2 gap-2 text-center text-lg">
                    <div
                        class="py-2 px-1 rounded-md bg-red-500/90 text-white font-medium  flex flex-col font-medium md:h-[80px] justify-center px-1 py-2">
                        <span class="block font-bold">0-25</span>
                        Extreme Fear
                    </div>
                    <div
                        class="py-2 px-1 rounded-md bg-orange-500/90 text-white font-medium  flex flex-col font-medium md:h-[80px] justify-center px-1 py-2">
                        <span class="block font-bold">26-45</span>
                        Fear
                    </div>
                    <div
                        class="py-2 px-1 rounded-md bg-yellow-400/90 text-gray-700 font-medium  flex flex-col font-medium md:h-[80px] justify-center px-1 py-2">
                        <span class="block font-bold">46-55</span>
                        Neutral
                    </div>
                    <div
                        class="py-2 px-1 rounded-md bg-green-400/90 text-gray-700 font-medium  flex flex-col font-medium md:h-[80px] justify-center px-1 py-2">
                        <span class="block font-bold">56-75</span>
                        Greed
                    </div>
                    <div
                        class="py-2 px-1 rounded-md bg-green-600/90 text-white font-medium  flex flex-col font-medium md:h-[80px] justify-center px-1 py-2">
                        <span class="block font-bold">76-100</span>
                        Extreme Greed
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    const fear_greed = {{ fear_and_greed | safe }};

    const sentimentConfig = {
        'Extreme Fear': { range: [0, 25], color: 'text-rose-600', needleColor: '#ef4444', desc: 'Extreme fear suggests investors are highly risk-averse. Market may be oversold, potentially indicating a buying opportunity.' },
        'Fear': { range: [26, 45], color: 'text-amber-600', needleColor: '#f97316', desc: 'Fearful sentiment indicates caution. Investors are hesitant, which may lead to selling pressure.' },
        'Neutral': { range: [46, 55], color: 'text-yellow-500', needleColor: '#eab308', desc: 'Neutral sentiment indicates a balanced market with no strong emotional bias.' },
        'Greed': { range: [56, 75], color: 'text-green-500', needleColor: '#22c55e', desc: 'Greedy sentiment suggests optimism. Markets may be approaching overvalued territory.' },
        'Extreme Greed': { range: [76, 100], color: 'text-green-700', needleColor: '#16a34a', desc: 'Extreme greed signals high confidence. Market may be overheated and due for a correction.' }
    };

    // Cache DOM elements
    const elements = {
        needleGroup: document.getElementById('gauge-needle'),
        needle: document.getElementById('needle'),
        needleCircle: document.querySelector('#gauge-needle circle'),
        gaugeValue: document.getElementById('gauge-value'),
        sentimentText: document.getElementById('sentiment-text'),
        sentimentDesc: document.getElementById('sentiment-desc')
    };

    const getSentiment = value => Object.entries(sentimentConfig).find(([, { range }]) => value >= range[0] && value <= range[1]) || ['Neutral', sentimentConfig.Neutral];

    const updateNeedle = value => {
        const angle = -90 + (value / 100) * 180;
        elements.needleGroup.setAttribute('transform', `rotate(${angle} 100 100)`);
    };

    const updateGauge = (value, { needleColor }) => {
        elements.gaugeValue.textContent = value.toFixed(2);
        elements.gaugeValue.setAttribute('fill', needleColor);
        elements.needle.setAttribute('stroke', needleColor);
        elements.needleCircle.setAttribute('fill', needleColor);
    };

    const setSentimentText = (value, { color, desc }) => {
        elements.sentimentText.className = `text-2xl  font-semibold ${color}`;
        elements.sentimentText.textContent = `${getSentiment(value)[0]}`;
        elements.sentimentDesc.textContent = desc;
    };

    const animateGauge = (targetValue, duration = 1000) => {
        const startTime = performance.now();
        const update = timestamp => {
            const progress = Math.min((timestamp - startTime) / duration, 1);
            const currentValue = progress * targetValue;
            const [, config] = getSentiment(currentValue);
            updateGauge(currentValue, config);
            updateNeedle(currentValue);
            if (progress < 1) {
                requestAnimationFrame(update);
            } else {
                updateGauge(targetValue, getSentiment(targetValue)[1]);
                updateNeedle(targetValue);
            }
        };
        requestAnimationFrame(update);
    };

    // Initialize on DOM load
    document.addEventListener('DOMContentLoaded', () => {
        const score = fear_greed?.score || 0;
        const [, config] = getSentiment(score);
        setSentimentText(score, config);
        animateGauge(score, 1000);
    });
</script>