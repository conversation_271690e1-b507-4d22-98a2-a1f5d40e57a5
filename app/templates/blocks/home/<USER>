<div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="lg:text-2xl text-xl font-semibold text-gray-800 dark:text-gray-200">Global Markets</h2>
        <div id="indices-timestamp" class="text-sm text-gray-500 dark:text-gray-400">
            <!-- Timestamp will be updated dynamically -->
        </div>
    </div>

    <!-- Tabs for different regions -->
    <div class="mb-4 border-b border-gray-200">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="indices-tabs" role="tablist">
            <li class="mr-2" role="presentation">
                <button class="inline-block p-4 border-b-2 text-blue-600 border-blue-600 rounded-t-lg" id="asia-tab"
                    data-region="Asia" type="button" role="tab" aria-selected="true">
                    Asia
                </button>
            </li>
            <li class="mr-2" role="presentation">
                <button
                    class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-blue-600 hover:border-blue-600"
                    id="india-tab" data-region="India" type="button" role="tab" aria-selected="false">
                    India
                </button>
            </li>
            <li class="mr-2" role="presentation">
                <button
                    class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-blue-600 hover:border-blue-600"
                    id="us-tab" data-region="US" type="button" role="tab" aria-selected="false">
                    US
                </button>
            </li>
            <li class="mr-2" role="presentation">
                <button
                    class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-blue-600 hover:border-blue-600"
                    id="europe-tab" data-region="Europe" type="button" role="tab" aria-selected="false">
                    Europe
                </button>
            </li>
            <li class="mr-2" role="presentation">
                <button
                    class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-blue-600 hover:border-blue-600"
                    id="crypto-tab" data-region="Crypto" type="button" role="tab" aria-selected="false">
                    Crypto
                </button>
            </li>
            <li class="mr-2" role="presentation">
                <button
                    class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-blue-600 hover:border-blue-600"
                    id="bonds-tab" data-region="Bonds" type="button" role="tab" aria-selected="false">
                    Bonds
                </button>
            </li>
            <li class="mr-2" role="presentation">
                <button
                    class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-blue-600 hover:border-blue-600"
                    id="commodities-tab" data-region="Commodities" type="button" role="tab" aria-selected="false">
                    Commodities
                </button>
            </li>
            <li class="mr-2" role="presentation">
                <button
                    class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-blue-600 hover:border-blue-600"
                    id="forex-tab" data-region="Forex" type="button" role="tab" aria-selected="false">
                    Forex
                </button>
            </li>
        </ul>
    </div>

    <!-- Tab content -->
    <div id="indices-content">
        <!-- Loading indicator -->
        <div id="indices-loading" class="flex justify-center items-center h-32">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-gray-600 dark:text-gray-400">Loading Global Markets...</span>
        </div>

        <!-- Grids for each region, initially hidden -->
        <div id="indices-grid-Asia" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3 hidden"></div>
        <div id="indices-grid-India" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3 hidden"></div>
        <div id="indices-grid-US" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3 hidden"></div>
        <div id="indices-grid-Europe" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3 hidden"></div>
        <div id="indices-grid-Crypto" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3 hidden"></div>
        <div id="indices-grid-Bonds" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3 hidden"></div>
        <div id="indices-grid-Commodities" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3 hidden"></div>
        <div id="indices-grid-Forex" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3 hidden"></div>

        <div id="indices-error"
            class="hidden bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mt-4">
            <p class="text-red-600 dark:text-red-400">
                Failed to fetch indices data. Please try again later.
            </p>
        </div>
    </div>
</div>

<script>
    // Current active region
    let activeRegion = "Asia";
    let allRegionsData = {};

    // Function to toggle loading state
    function toggleLoading(show) {
        const loadingDiv = document.getElementById('indices-loading');
        const contentGrids = document.querySelectorAll('[id^="indices-grid-"]');
        const errorDiv = document.getElementById('indices-error');

        if (show) {
            loadingDiv.classList.remove('hidden');
            contentGrids.forEach(grid => grid.classList.add('hidden'));
            errorDiv.classList.add('hidden');
        } else {
            loadingDiv.classList.add('hidden');
        }
    }

    // Function to render indices for a given region
    function renderIndices(region, regionData) {
        const grid = document.getElementById(`indices-grid-${region}`);
        if (!grid) return;

        // Clear existing content
        grid.innerHTML = '';

        // Check if regionData and indices exist
        if (!regionData || !regionData.indices) {
            return;
        }

        // Render each index
        Object.entries(regionData.indices).forEach(([indexName, indexData]) => {
            if (indexData && indexData.price_data) {
                const { currentPrice, change, percentChange } = indexData.price_data;
                const isPositive = change >= 0;
                const changeColor = isPositive ? 'text-green-500' : 'text-red-500';
                const changeArrow = isPositive ? '▲' : '▼';

                const indexDiv = document.createElement('div');
                indexDiv.className = 'bg-gray-50 dark:bg-neutral-700 p-3 rounded-lg shadow-sm';
                indexDiv.innerHTML = `
                    <div class="flex flex-col">
                        <h3 class="font-semibold text-gray-800 dark:text-gray-200 text-sm mb-1 truncate" title="${indexName}">
                            ${indexName}
                        </h3>
                        <div class="flex justify-between items-center">
                            <div class="text-base font-semibold text-gray-900 dark:text-gray-100">
                                ${currentPrice.toFixed(2)}
                            </div>
                            <div class="flex flex-col items-end">
                                <div class="flex items-center ${changeColor} text-sm">
                                    <span>${changeArrow}</span>
                                    <span class="ml-1">${Math.abs(change).toFixed(2)}</span>
                                </div>
                                <div class="${changeColor} text-xs">
                                    ${Math.abs(percentChange).toFixed(2)}%
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                grid.appendChild(indexDiv);
            }
        });
    }

    // Function to switch between regions by toggling visibility
    function switchRegion(region) {
        const indicesError = document.getElementById('indices-error');
        indicesError.classList.add('hidden');

        // Hide all region grids
        document.querySelectorAll('[id^="indices-grid-"]').forEach(grid => {
            grid.classList.add('hidden');
        });

        // Show the selected region grid
        const selectedGrid = document.getElementById(`indices-grid-${region}`);
        if (selectedGrid) {
            selectedGrid.classList.remove('hidden');

            // Update timestamp if available
            const regionData = allRegionsData[region];
            if (regionData && regionData.timestamp) {
                document.getElementById('indices-timestamp').textContent = `Last updated: ${regionData.timestamp}`;
            } else {
                document.getElementById('indices-timestamp').textContent = '';
            }
        } else {
            // Show error if grid not found
            indicesError.classList.remove('hidden');
        }
    }

    // Function to handle tab clicks
    function handleIndexRegionClick(event) {
        const region = event.target.dataset.region;
        if (!region) return;

        // Update active tab
        document.querySelectorAll('#indices-tabs button').forEach(tab => {
            tab.classList.remove('text-blue-600', 'border-blue-600');
            tab.classList.add('border-transparent');
            tab.setAttribute('aria-selected', 'false');
        });

        event.target.classList.add('text-blue-600', 'border-blue-600');
        event.target.classList.remove('border-transparent');
        event.target.setAttribute('aria-selected', 'true');

        // Update active region and switch to that region's grid
        activeRegion = region;
        switchRegion(region);
    }

    // Function to fetch and render indices data
    async function fetchIndicesData() {
        toggleLoading(true); // Show loading indicator

        const indicesError = document.getElementById('indices-error');
        indicesError.classList.add('hidden');

        try {
            const response = await fetch('/app/indices');

            if (!response.ok) {
                throw new Error('Failed to fetch indices data');
            }

            allRegionsData = await response.json();

            // Render all regions
            Object.entries(allRegionsData).forEach(([region, regionData]) => {
                renderIndices(region, regionData);
            });

            // Show the active region's grid
            switchRegion(activeRegion);
        } catch (error) {
            console.error('Error fetching indices:', error);
            indicesError.classList.remove('hidden');
        } finally {
            toggleLoading(false); // Hide loading indicator
        }
    }

    // Add event listeners to tabs
    document.querySelectorAll('#indices-tabs button').forEach(tab => {
        tab.addEventListener('click', handleIndexRegionClick);
    });

    // Fetch indices data on page load
    document.addEventListener('DOMContentLoaded', fetchIndicesData);
</script>