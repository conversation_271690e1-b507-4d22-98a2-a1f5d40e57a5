{% include 'blocks/timestamp.html' %}
<div class="bg-white dark:bg-neutral-800 rounded-xl p-6 my-6">
    <div class="border-b border-dashed flex items-center justify-between mb-6 pb-3">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Top Performing Stocks</h2>
        <div class="text-right flex flex-col gap-y-2">
            <div id="preview-timestamp-stocks" class="timestamp-component justify-end mt-3 md:mt-0"></div>
        </div>
    </div>
    <div class="grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-0">
        <!-- Top Volume Stocks -->
        <div class="bg-gradient-to-r from-blue-50 via-white p-4 pr-0 rounded-lg transition-all">
            <h3 class="font-semibold text-neutral-800 mb-2">Top Volume</h3>
            <table class="border border-collapse font-semibold text-gray-600 text-xs w-full">
                <thead>
                    <tr>
                        <th class="text-left p-2 border-b border-r">Stock</th>
                        <th class="text-right p-2 border-b border-r">Open</th>
                        <th class="text-right p-2 border-b border-r">High</th>
                        <th class="text-right p-2 border-b border-r">LTP</th>
                        <th class="text-right p-2 border-b border-r">%Chng</th>
                    </tr>
                </thead>
                <tbody id="top-volume-stocks-body">
                    <!-- Data will be populated by JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- Leading Gainers -->
        <div class="bg-gradient-to-r from-white via-green-50 p-4 pr-0 rounded-lg transition-all">
            <h3 class="font-semibold text-neutral-800 mb-2">Leading Gainers</h3>
            <table class="border border-collapse font-semibold text-gray-600 text-xs w-full">
                <thead>
                    <tr>
                        <th class="text-left p-2 border-b border-r">Stock</th>
                        <th class="text-right p-2 border-b border-r">Open</th>
                        <th class="text-right p-2 border-b border-r">High</th>
                        <th class="text-right p-2 border-b border-r">LTP</th>
                        <th class="text-right p-2 border-b border-r">%Chng</th>
                    </tr>
                </thead>
                <tbody id="leading-gainers-body">
                    <!-- Data will be populated by JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- Major Losers -->
        <div class="bg-gradient-to-r from-white to-red-50 p-4 rounded-lg transition-all">
            <h3 class="font-semibold text-neutral-800 mb-2">Major Losers</h3>
            <table class="border border-collapse font-semibold text-gray-600 text-xs w-full">
                <thead>
                    <tr>
                        <th class="text-left p-2 border-b border-r">Stock</th>
                        <th class="text-right p-2 border-b border-r">Open</th>
                        <th class="text-right p-2 border-b border-r">High</th>
                        <th class="text-right p-2 border-b border-r">LTP</th>
                        <th class="text-right p-2 border-b border-r">%Chng</th>
                    </tr>
                </thead>
                <tbody id="major-losers-body">
                    <!-- Data will be populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>
    <a href="/stocks/live" class="hover:text-blue-600 font-medium inline-flex items-center gap-2 mt-4">View More
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-move-right-icon lucide-move-right">
            <path d="M18 8L22 12L18 16" />
            <path d="M2 12H22" />
        </svg>
    </a>
</div>

<script>
    const activeStocksData = {{ capital | tojson | safe }};

    // Generate table row
    const generateStocksTableRow = (stock) => {
        const symbol = stock.symbol;
        const open = stock.open || stock.open_price;
        const high = stock.dayHigh || stock.high_price;
        const low = stock.dayLow || stock.low_price;
        const prevClose = stock.previousClose || stock.prev_price;
        const lastPrice = stock.lastPrice || stock.ltp;
        const pChange = stock.pChange || stock.perChange;

        return `
            <tr>
                <td class="border-b border-r p-2">${symbol}</td>
                <td class="text-right border-b border-r p-2">₹${open.toFixed(2)}</td>
                <td class="text-right border-b border-r p-2">₹${high.toFixed(2)}</td>
                <td class="text-right border-b border-r p-2">₹${lastPrice.toFixed(2)}</td>
                <td class="text-right border-b border-r p-2 ${pChange < 0 ? 'text-red-600' : 'text-green-600'}">${pChange.toFixed(2)}%</td>
            </tr>
        `;
    };

    // Display data
    const displayTopPerformingStocks = () => {
        const tableBodies = {
            volume: document.getElementById('top-volume-stocks-body'),
            gainers: document.getElementById('leading-gainers-body'),
            losers: document.getElementById('major-losers-body')
        };

        // Sort and map data to tables
        tableBodies.volume.innerHTML = [
            ...(activeStocksData.active.securities.volume.data || []),
            ...(activeStocksData.active.etf.volume.data || []),
            ...(activeStocksData.active.sme.volume.data || [])
        ]
            .sort((a, b) => (b.totalTradedVolume || 0) - (a.totalTradedVolume || 0))
            .slice(0, 5)
            .map(stock => generateStocksTableRow(stock, true))
            .join('');
        tableBodies.gainers.innerHTML = activeStocksData.gainers.allSec.data
            .sort((a, b) => (b.pChange || 0) - (a.pChange || 0))
            .slice(0, 5)
            .map(generateStocksTableRow)
            .join('');
        tableBodies.losers.innerHTML = activeStocksData.loosers.allSec.data
            .sort((a, b) => (b.pChange || 0) - (a.pChange || 0))
            .slice(0, 5)
            .map(generateStocksTableRow)
            .join('');
    };

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
        updateTimestamp("preview-timestamp-stocks");
        displayTopPerformingStocks();
    });
</script>