<div class="bg-white dark:bg-neutral-800 rounded-xl p-6 my-6">
    <div class="border-b border-dashed flex items-center justify-between mb-6 pb-3">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Most Active Contracts</h2>
        <div class="text-right flex flex-col gap-y-2">
            <div id="preview-timestamp-contracts" class="timestamp-component justify-end mt-3 md:mt-0">
            </div>
        </div>
    </div>
    <div class="grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-0">
        <!-- Most Active Calls -->
        <div class="bg-gradient-to-r from-blue-50 via-white p-4 pr-0 rounded-lg transition-all">
            <h3 class="font-semibold text-neutral-800 mb-2">Most Active Calls</h3>
            <table class="border border-collapse font-semibold text-gray-600 text-xs w-full">
                <thead>
                    <tr>
                        <th class="text-left p-2 border-b border-r">Contract</th>
                        <th class="text-right p-2 border-b border-r">LTP</th>
                        <th class="text-right p-2 border-b border-r">Chng</th>
                        <th class="text-right p-2 border-b border-r">%Chng</th>
                    </tr>
                </thead>
                <tbody id="most-active-calls-body"></tbody>
            </table>
        </div>

        <!-- Most Active Puts -->
        <div class="bg-gradient-to-r from-white via-purple-50 p-4 pr-0 rounded-lg transition-all">
            <h3 class="font-semibold text-neutral-800 mb-2">Most Active Puts</h3>
            <table class="border border-collapse font-semibold text-gray-600 text-xs w-full">
                <thead>
                    <tr>
                        <th class="text-left p-2 border-b border-r">Contract</th>
                        <th class="text-right p-2 border-b border-r">LTP</th>
                        <th class="text-right p-2 border-b border-r">Chng</th>
                        <th class="text-right p-2 border-b border-r">%Chng</th>
                    </tr>
                </thead>
                <tbody id="most-active-puts-body"></tbody>
            </table>
        </div>

        <!-- Most Active Contracts by OI -->
        <div class="bg-gradient-to-r from-white to-yellow-50  p-4 rounded-lg transition-all">
            <h3 class="font-semibold text-neutral-800 mb-2">Most Active Contracts by OI</h3>
            <table class="border border-collapse font-semibold text-gray-600 text-xs w-full">
                <thead>
                    <tr>
                        <th class="text-left p-2 border-b border-r">Contract</th>
                        <th class="text-right p-2 border-b border-r">LTP</th>
                        <th class="text-right p-2 border-b border-r">Chng</th>
                        <th class="text-right p-2 border-b border-r">%Chng</th>
                    </tr>
                </thead>
                <tbody id="most-active-oi-body"></tbody>
            </table>
        </div>
    </div>
    <a href="/stocks/live" class="hover:text-blue-600 font-medium inline-flex items-center gap-2 mt-4">View More

        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-move-right-icon lucide-move-right">
            <path d="M18 8L22 12L18 16" />
            <path d="M2 12H22" />
        </svg>
    </a>
</div>

<script>
    const derivativesData = {{ derivatives | tojson | safe }};

    const monthMap = {
        '01': 'JAN', '02': 'FEB', '03': 'MAR', '04': 'APR', '05': 'MAY', '06': 'JUN',
        '07': 'JUL', '08': 'AUG', '09': 'SEP', '10': 'OCT', '11': 'NOV', '12': 'DEC'
    };

    // Utility functions
    const formatContractName = (identifier) => {
        if (!identifier) return '-';

        const [prefixSymbolExpiry, strike] = identifier.split(/CE|PE/);
        const optionType = identifier.includes('CE') ? 'CE' : 'PE';
        const prefixMatch = prefixSymbolExpiry.match(/^(OPTIDX|OPTSTK)([A-Z]+)([0-9]{2}-[0-9]{2}-[0-9]{4})$/);

        if (!prefixMatch) return identifier;

        const [, , symbol, expiry] = prefixMatch;
        const [day, month, year] = expiry.split('-');
        const formattedExpiry = `${day}${monthMap[month]}${year.slice(2)}`;
        const formattedStrike = parseFloat(strike).toFixed(0);

        return `${symbol} ${formattedExpiry} ${formattedStrike} ${optionType}`;
    };

    const calculatePriceMetrics = (lastPrice, pChange) => {
        const prevClose = lastPrice / (1 + pChange / 100);
        const change = lastPrice - prevClose;
        return { prevClose, change };
    };

    const generateTableRow = (stock) => {
        const { lastPrice = 0, pChange = 0 } = stock;
        const { change } = calculatePriceMetrics(lastPrice, pChange);

        return `
            <tr>
                <td class="border-b border-r p-2">${formatContractName(stock.identifier)}</td>
                <td class="text-right border-b border-r  p-2">₹${lastPrice.toFixed(2)}</td>
                <td class="text-right border-b border-r  p-2 ${change < 0 ? 'text-red-600' : 'text-green-600'}">${change.toFixed(2)}</td>
                <td class="text-right border-b border-r  p-2 ${pChange < 0 ? 'text-red-600' : 'text-green-600'}">${pChange.toFixed(2)}%</td>
            </tr>
        `;
    };

    const getSortedData = (indexData = [], stockData = [], type, sortKey, limit = 5) => {
        return [...indexData, ...stockData]
            .filter(item => type ? item.optionType === type : true)
            .sort((a, b) => b[sortKey] - a[sortKey])
            .slice(0, limit);
    };

    // Main functions
    const updateTimestamp = (elementId) => {
        const previewTimestamp = document.getElementById(elementId);
        if (previewTimestamp) {
            window.createTimestamp(elementId, {
                showRefresh: true,
                onRefresh: () => window.location.reload()
            });
        }
    };

    const displayMostActiveContracts = () => {
        const tableBodies = {
            calls: document.getElementById('most-active-calls-body'),
            puts: document.getElementById('most-active-puts-body'),
            oi: document.getElementById('most-active-oi-body')
        };

        const dataSources = {
            calls: getSortedData(
                derivativesData?.index_calls?.OPTIDX?.data || [],
                derivativesData?.stock_calls?.OPTSTK?.data || [],
                'Call',
                'numberOfContractsTraded'
            ),
            puts: getSortedData(
                derivativesData?.index_puts?.OPTIDX?.data || [],
                derivativesData?.stock_puts?.OPTSTK?.data || [],
                'Put',
                'numberOfContractsTraded'
            ),
            oi: getSortedData(
                derivativesData?.by_oi?.value?.data || [],
                [],
                null,
                'numberOfContractsTraded'
            )
        };

        Object.entries(tableBodies).forEach(([key, element]) => {
            element.innerHTML = dataSources[key].map(generateTableRow).join('');
        });
    };

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
        updateTimestamp('preview-timestamp-contracts');
        displayMostActiveContracts();
    });
</script>