<!-- Shared Table Sorting Functionality -->
<script>
    // Global addSorting function that can be used by multiple pages
    window.addSorting = function (tableId) {
        const table = document.getElementById(tableId);
        if (!table) return;

        const headers = table.querySelectorAll('thead th[data-col]');
        headers.forEach((header, index) => {
            if (!header.hasAttribute('data-no-sort')) { // Skip columns marked as non-sortable
                header.classList.add('cursor-pointer', 'relative');
                header.dataset.sortDirection = 'none';
                header.onclick = () => {
                    // Reset other headers
                    headers.forEach(h => {
                        if (h !== header) {
                            h.dataset.sortDirection = 'none';
                            // Remove any existing sort indicators
                            const indicator = h.querySelector('.sort-indicator');
                            if (indicator) indicator.remove();
                        }
                    });

                    // Toggle sort direction
                    const currentDir = header.dataset.sortDirection;
                    const newDir = currentDir === 'asc' ? 'desc' : 'asc';
                    header.dataset.sortDirection = newDir;

                    // Update sort indicator
                    let indicator = header.querySelector('.sort-indicator');
                    if (!indicator) {
                        indicator = document.createElement('span');
                        indicator.classList.add('sort-indicator', 'ml-1', 'inline-block');
                        header.appendChild(indicator);
                    }
                    indicator.innerHTML = newDir === 'asc' ? '▲' : '▼';

                    // Sort the table
                    const rows = Array.from(table.querySelectorAll('tbody tr'));
                    rows.sort((a, b) => {
                        const aValue = a.cells[index].textContent.trim();
                        const bValue = b.cells[index].textContent.trim();

                        // Try to parse as numbers first
                        const aNumMatch = aValue.match(/-?\d+(\.\d+)?/);
                        const bNumMatch = bValue.match(/-?\d+(\.\d+)?/);

                        // Check if both seem potentially numeric (contain numbers)
                        const isPotentiallyNumericA = aNumMatch !== null;
                        const isPotentiallyNumericB = bNumMatch !== null;

                        if (isPotentiallyNumericA && isPotentiallyNumericB) {
                            let aEffValue = parseFloat(aNumMatch[0]);
                            let bEffValue = parseFloat(bNumMatch[0]);

                            // Adjust for Credit/Debit only if the text indicates it
                            if (aValue.toLowerCase().includes('credit')) {
                                aEffValue = -Math.abs(aEffValue); // Make credits negative
                            } else if (aValue.toLowerCase().includes('debit')) {
                                aEffValue = Math.abs(aEffValue); // Make debits positive (explicitly)
                            }
                            // Else: it's just a number without Debit/Credit prefix, keep its original sign

                            if (bValue.toLowerCase().includes('credit')) {
                                bEffValue = -Math.abs(bEffValue); // Make credits negative
                            } else if (bValue.toLowerCase().includes('debit')) {
                                bEffValue = Math.abs(bEffValue); // Make debits positive (explicitly)
                            }
                            // Else: it's just a number without Debit/Credit prefix, keep its original sign

                            // Only apply numeric sort if both were valid numbers after processing
                            // isNaN check is implicitly handled by parseFloat initial parsing and the check above
                            return newDir === 'asc' ? aEffValue - bEffValue : bEffValue - aEffValue;
                        }

                        // Fallback to string comparison if not reliably numeric or mixed types
                        return newDir === 'asc'
                            ? aValue.localeCompare(bValue)
                            : bValue.localeCompare(aValue);
                    });

                    // Re-append in sorted order
                    const tbody = table.querySelector('tbody');
                    rows.forEach(row => tbody.appendChild(row));
                };
            }
        });
    };
</script>