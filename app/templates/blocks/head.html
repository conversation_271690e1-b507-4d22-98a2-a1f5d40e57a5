<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<link href="{{ cdn('/static/images/ai-bull-5.png') }}" rel="icon" type="image/x-icon" />
<script>
    window.Mantra_App_Name = "ai_bull";
    window.mantraAppParameterValue = "ai_bull";
    window.Mantra_AI_Server = "https://api.theaibull.com";
    window.Mantra_Apps_Server = "https://apps.theaibull.com";
    window.Mantra_Google_Client_Id = "224531985735-maikd6b0mof3mn8fgurqt97mkg7g4tgj.apps.googleusercontent.com";
</script>
<!-- Include Tailwind CSS -->
{{ get_block("core/css" , { "APP_DEBUG" : APP_DEBUG, "DEPLOYMENT_HASH":
DEPLOYMENT_HASH}) }}
<script>
    // used scoped function and variable
    "use strict";
    (function () {
        // On page load or when changing themes, best to add inline in `head` to avoid FOUC
        if (
            localStorage.theme === "light" ||
            (!("theme" in localStorage) &&
                window.matchMedia("(prefers-color-scheme: light)").matches)
        ) {
            document.documentElement.classList.add("light");
        } else {
            document.documentElement.classList.remove("light");
        }
    })();
</script>