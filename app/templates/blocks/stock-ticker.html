<div id="stock-ticker-container" class="flex items-center bg-black text-white sticky top-0 z-50 w-full"
    style="display: none;">
    <!-- Left Arrow -->
    <button id="scroll-left" class="p-2 hover:text-white dark:hover:text-white text-white/50">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
    </button>

    <!-- Ticker Container -->
    <div class="relative flex-1 overflow-hidden">
        <div id="stock-ticker" class="flex items-center">
            <div id="stock-list" class="flex items-center whitespace-nowrap">
                <!-- Ticker items will be inserted here by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Right Arrow -->
    <button id="scroll-right" class="p-2 hover:text-white dark:hover:text-white text-white/50">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
    </button>
</div>

{% include "blocks/stock-preview.html" %}

<!-- Emitter library -->
<script src="https://cdn.jsdelivr.net/npm/emitter-io@1.39.0/build/emitter.min.js"></script>

<style>
    /* Animation styles for price updates */
    @keyframes flashGreen {
        0% {
            background-color: rgba(34, 197, 94, 0);
        }

        50% {
            background-color: rgba(34, 197, 94, 0.3);
        }

        100% {
            background-color: rgba(34, 197, 94, 0);
        }
    }

    @keyframes flashRed {
        0% {
            background-color: rgba(239, 68, 68, 0);
        }

        50% {
            background-color: rgba(239, 68, 68, 0.3);
        }

        100% {
            background-color: rgba(239, 68, 68, 0);
        }
    }

    .flash-green {
        animation: flashGreen 1s ease-out;
    }

    .flash-red {
        animation: flashRed 1s ease-out;
    }
</style>

<script>
    /*** STEP 1: Initialize global variables and state ***/
    // Global variable to store ticker data
    window.tickerData = null;
    window.isTickerAnimating = true;
    window.emitterClient = null;
    window.activeSubscriptions = new Map(); // Map of symbol -> subscription
    window.visibleStocks = new Set(); // Track which stocks are currently visible
    window.pendingSubscriptions = new Set(); // Track subscriptions in progress

    // Throttling variables
    const THROTTLE_INTERVAL = 2000; // Process updates every 250ms
    let bufferedUpdates = new Map(); // Map of instrumentId -> { ltp, pChange, symbol }
    let updateTimeoutId = null;

    document.addEventListener('DOMContentLoaded', async () => {
        /*** STEP 2: Setup initial configuration and variables ***/
        const tickerVisibilityKey = 'showTicker';
        const showTicker = userData.getItem(tickerVisibilityKey) ?? true;
        const tickerContainer = document.getElementById('stock-ticker-container');
        const tickerViewport = document.querySelector('.relative.flex-1.overflow-hidden');

        // Scrolling animation variables
        let position = 0;
        const scrollSpeed = 1;
        let currentSpeed = scrollSpeed;
        let animationFrameId = null;
        let stockItemWidth = 0;
        let visibleWidth = 0;
        let bufferCount = 0; // Buffer stocks on each side

        /*** STEP 3: Define animation function for continuous scrolling ***/
        function animate() {
            if (window.isTickerAnimating && document.getElementById('stock-preview').classList.contains('hidden')) {
                position -= currentSpeed;
                const stockList = document.getElementById('stock-list');

                if (stockList && stockList.children.length > 0) {
                    // Reset position when we've scrolled through all stocks
                    const totalWidth = stockList.scrollWidth / 2;
                    if (-position >= totalWidth) {
                        position = 0;
                    }

                    stockList.style.transform = `translateX(${position}px)`;

                    // Only update visible stocks when position changes significantly
                    if (Math.abs(position - (window.lastUpdatePosition || 0)) > stockItemWidth) {
                        updateVisibleStocks();
                        window.lastUpdatePosition = position;
                    }
                }
            }
            animationFrameId = requestAnimationFrame(animate);
        }

        /*** STEP 4: Setup real-time data connection functions ***/
        function setupEmitter() {
            if (window.emitterClient) return window.emitterClient;

            // Get access token from cookie or use default
            let accessToken = getCookie("access_token");
            if (!accessToken) {
                return;
            }

            const connectOptions = {
                host: "streamer.emc2.io",
                port: 443,
                secure: true,
                path: "/bifrost?t=" + accessToken,
            };


            window.emitterClient = emitter.connect(connectOptions);

            window.emitterClient.on("connect", () => {
                console.log("Connected to Emitter server");

                // After connection, publish a single request with all security IDs
                if (window.tickerData && window.tickerData.length > 0) {
                    const allSecurityIds = window.tickerData.map(stock => stock.dhan_instrument_id);

                    // Send all security IDs at once in a single batch
                    window.emitterClient.publish({
                        key: "Zr6yJ4lM8Kd_7dLEV2hAK0rIBimDTsbb",
                        channel: "streamer/",
                        message: JSON.stringify({
                            exchangeSegment: "NSE_EQ",
                            securityIds: allSecurityIds
                        })
                    });

                    // console.log(`Published batch subscription request for all ${allSecurityIds.length} securities`);
                }
            });

            window.emitterClient.on("disconnect", () => {
                console.log("Disconnected from Emitter server");
            });

            window.emitterClient.on("error", (error) => {
                console.log("Emitter error: " + error);
            });

            window.emitterClient.on('message', handleEmitterMessage);
            return window.emitterClient;
        }

        /*** STEP 5: Manage subscriptions for visible stocks ***/
        function subscribeToSymbol(symbol, instrumentId) {
            if (!window.emitterClient) {
                setupEmitter();
            }

            if (!window.emitterClient) return;

            // Check if already subscribed or pending
            if (window.activeSubscriptions.has(symbol) || window.pendingSubscriptions.has(symbol)) {
                return;
            }

            // Add to pending set
            window.pendingSubscriptions.add(symbol);

            // Subscribe to updates channel only (no need for individual publish)
            const subscription = window.emitterClient.subscribe({
                key: "Zr6yJ4lM8Kd_7dLEV2hAK0rIBimDTsbb",
                channel: `streamer/${instrumentId}/NSE_EQ`
            });

            window.activeSubscriptions.set(symbol, subscription);
            window.pendingSubscriptions.delete(symbol);
            // console.log(`Subscribed to ${symbol} (ID: ${instrumentId})`);
        }

        function unsubscribeFromSymbol(symbol, instrumentId) {
            if (window.emitterClient && window.activeSubscriptions.has(symbol)) {
                window.emitterClient.unsubscribe({
                    key: "Zr6yJ4lM8Kd_7dLEV2hAK0rIBimDTsbb",
                    channel: `streamer/${instrumentId}/NSE_EQ`
                });

                window.activeSubscriptions.delete(symbol);
                // console.log(`Unsubscribed from ${symbol} (ID: ${instrumentId})`);
            }
        }

        /*** STEP 6: Track which stocks are currently visible in viewport ***/
        function updateVisibleStocks() {
            const stockList = document.getElementById('stock-list');
            if (!stockList || !window.tickerData) return;

            // Get current scroll position and viewport dimensions
            const viewportLeft = -position;
            const viewportRight = viewportLeft + visibleWidth;

            // Track newly visible stocks
            const newlyVisible = new Set();
            const originalStocksCount = window.tickerData.length;

            // Check each original stock element (skip duplicates)
            Array.from(stockList.children).forEach((stockElement, index) => {
                // Skip duplicate stocks
                if (index >= originalStocksCount) return;

                const elementLeft = stockElement.offsetLeft;
                const elementRight = elementLeft + stockElement.offsetWidth;

                // Check if element is in visible range (including buffer)
                if ((elementRight >= viewportLeft - (stockItemWidth * bufferCount)) &&
                    (elementLeft <= viewportRight + (stockItemWidth * bufferCount))) {

                    const symbol = stockElement.dataset.stock;
                    const instrumentId = stockElement.dataset.instrumentId;

                    newlyVisible.add(symbol);

                    // Only subscribe if not already subscribed
                    if (!window.activeSubscriptions.has(symbol) && !window.pendingSubscriptions?.has(symbol)) {
                        subscribeToSymbol(symbol, instrumentId);
                    }
                }
            });

            // Unsubscribe stocks that are no longer visible
            window.visibleStocks.forEach(symbol => {
                if (!newlyVisible.has(symbol)) {
                    const stock = window.tickerData.find(s => s.symbol === symbol);
                    if (stock) {
                        unsubscribeFromSymbol(symbol, stock.dhan_instrument_id);
                    }
                }
            });

            // Update visible stocks set
            window.visibleStocks = newlyVisible;
        }

        /*** STEP 7: Process incoming real-time stock updates (Apply Throttling) ***/
        function handleEmitterMessage(msg) {
            try {
                if (msg.binary) {
                    const buffer = msg.binary.buffer.slice(
                        msg.binary.byteOffset,
                        msg.binary.byteOffset + msg.binary.byteLength
                    );

                    // Parse binary data based on message size
                    let parsedData;
                    const instrumentId = msg.channel.split('/')[1]; // Extract Instrument ID from channel

                    if (!instrumentId) {
                        // console.warn("Could not extract instrument ID from channel:", msg.channel);
                        return;
                    }

                    // Find the stock in our ticker data using the instrument ID *before* parsing
                    // This avoids unnecessary parsing if the stock isn't relevant
                    let stock = window.tickerData.find(s => s.dhan_instrument_id.toString() === instrumentId);
                    if (!stock) {
                        // console.warn("Stock data not found for instrument ID:", instrumentId);
                        return; // Skip if stock data isn't preloaded or relevant
                    }

                    // Now parse the data
                    if (msg.binary.byteLength === 18) {
                        // Index data format
                        const view = new DataView(buffer);
                        parsedData = {
                            ltp: view.getFloat32(0, true)?.toFixed(2),
                            avgPrice: view.getFloat32(10, true)?.toFixed(2)
                        };
                    } else {
                        // Regular stock data format
                        const binaryContent = parseBinaryData(new Uint8Array(buffer));
                        parsedData = {
                            ltp: binaryContent.LTP?.toFixed(2),
                            close: binaryContent.Close?.toFixed(2)
                        };
                    }

                    // Calculate percentage change
                    const closePrice = parsedData.close !== undefined ? parsedData.close : parsedData.avgPrice;
                    const pChange = closePrice ? ((parsedData.ltp - closePrice) / closePrice) * 100 : 0;
                    // console.table([{
                    //     symbol: stock.symbol,
                    //     ltp: parsedData.ltp,
                    //     percentageChange: pChange.toFixed(2),
                    //     closePrice: parsedData.close,
                    //     lastTradePrice: parsedData.ltp
                    // }]);
                    // --- Throttling Logic ---
                    // Store the latest update in the buffer, keyed by instrumentId
                    bufferedUpdates.set(instrumentId, {
                        ltp: parseFloat(parsedData.ltp),
                        pChange: parseFloat(pChange.toFixed(2)),
                        symbol: stock.symbol // Store symbol for later use in processBufferedUpdates
                    });

                    // Schedule processing if not already scheduled
                    if (!updateTimeoutId) {
                        updateTimeoutId = setTimeout(processBufferedUpdates, THROTTLE_INTERVAL);
                    }
                    // --- End Throttling Logic ---

                }
            } catch (e) {
                console.error("Error processing real-time update:", e, msg);
                // Clear any pending timeout on error to prevent potential issues
                if (updateTimeoutId) {
                    clearTimeout(updateTimeoutId);
                    updateTimeoutId = null;
                }
                bufferedUpdates.clear();
            }
        }

        // *** MODIFIED STEP: Process Buffered Updates Sequentially ***
        function processBufferedUpdates() {
            // Clear the timeout ID now that we are processing
            updateTimeoutId = null;

            // Create a copy of the buffer and clear the original immediately
            // This prevents race conditions if new messages arrive while processing
            const updatesToProcess = new Map(bufferedUpdates);
            bufferedUpdates.clear();

            // Convert map entries to an array for sequential processing
            const updatesArray = Array.from(updatesToProcess.entries());
            // Define the interval (in milliseconds) between processing each stock update
            const UPDATE_INTERVAL_PER_STOCK = 200; // e.g., 50ms delay

            // Function to process updates one by one with a delay
            function processNextUpdate(index) {
                // Stop if we've processed all updates in the array
                if (index >= updatesArray.length) {
                    return;
                }

                // Get the current update data: [instrumentId, { ltp, pChange, symbol }]
                const [instrumentId, update] = updatesArray[index];

                try {
                    // Find the corresponding stock in our main data array
                    let stock = window.tickerData.find(s => s.dhan_instrument_id.toString() === instrumentId);

                    // If stock data exists, update its last price and percentage change
                    if (stock) {
                        stock.lastPrice = update.ltp;
                        stock.pChange = update.pChange;

                        // Log the update being processed (optional)
                        // console.log('Processing buffered update for:', stock.symbol, update.ltp, update.pChange);

                        // Update the UI only if the stock is currently marked as visible
                        if (window.visibleStocks.has(update.symbol)) {
                            // Use requestAnimationFrame to ensure UI updates are smooth and efficient
                            requestAnimationFrame(() => {
                                // console.log('updating stock', stock.symbol, update.ltp, update.pChange);
                                updateStockPrice(update.symbol, update.ltp, update.pChange);
                            });
                        }
                    } else {
                        // Warn if stock data wasn't found (might happen in edge cases)
                        console.warn(`Stock data not found for instrument ID ${instrumentId} during buffered update processing.`);
                    }
                } catch (error) {
                    // Log any errors during processing but continue with the next update
                    console.error(`Error processing buffered update for instrument ID ${instrumentId}:`, error, update);
                }

                // Schedule the processing of the next update after the defined interval
                // setTimeout(() => processNextUpdate(index + 1), UPDATE_INTERVAL_PER_STOCK);
                processNextUpdate(index + 1)
            }

            // Start processing the first update in the array if there are any updates
            if (updatesArray.length > 0) {
                processNextUpdate(0);
            }
        }

        // Binary data parser function remains the same
        function parseBinaryData(binaryData) {
            const buffer = new ArrayBuffer(binaryData.length);
            const uint8View = new Uint8Array(buffer);
            uint8View.set(binaryData);

            const view = new DataView(buffer);

            const data = {
                LTP: view.getFloat32(0, true),
                LTT: view.getUint32(6, true),
                Close: view.getFloat32(42, true)
            };

            return data;
        }

        /*** STEP 8: Update stock prices in the UI with animations ***/
        function updateStockPrice(symbol, price, change) {
            // Update all stock elements with this symbol (original and duplicate)
            const stockItems = document.querySelectorAll(`.stock-item[data-stock="${symbol}"]`);
            if (!stockItems.length) return;

            // Helper function to restart animation
            function restartAnimation(element, className) {
                if (!element) return;
                element.classList.remove('flash-green', 'flash-red');
                requestAnimationFrame(() => {
                    element.classList.add(className);
                });
            }

            // Update all items with this symbol
            stockItems.forEach(item => {
                if (!item) return;
                const priceElement = item.querySelector('.stock-price');
                const changeElement = item.querySelector('.stock-change');

                if (priceElement) {
                    // Store the old price for comparison
                    const oldPrice = parseFloat(priceElement.textContent);

                    // Skip update if price hasn't changed
                    if (!price || oldPrice === price) return;

                    priceElement.textContent = price?.toFixed(2);

                    // Apply animation based on price movement
                    if (price > oldPrice) {
                        restartAnimation(priceElement, 'flash-green');
                    } else if (price < oldPrice) {
                        restartAnimation(priceElement, 'flash-red');
                    }
                }

                if (changeElement) {
                    const arrow = change >= 0 ? '▲' : '▼';
                    const colorClass = change >= 0 ? 'text-green-500' : 'text-red-500';
                    const newText = `${arrow} ${Math.abs(change).toFixed(2)}%`;

                    // Skip update if change percentage hasn't changed
                    if (changeElement.textContent === newText) return;

                    // Only animate and update if the value has changed
                    restartAnimation(changeElement, change >= 0 ? 'flash-green' : 'flash-red');
                    changeElement.className = `ml-2 stock-change ${colorClass}`;
                    changeElement.textContent = newText;
                }
            });
        }

        /*** STEP 9: Create and initialize the ticker display ***/
        function updateTickerUI() {
            if (!window.tickerData) return;

            const stockList = document.getElementById('stock-list');
            const fragment = document.createDocumentFragment();

            // Create all stock elements
            window.tickerData.forEach((stock, index) => {
                const arrow = stock.pChange >= 0 ? '▲' : '▼';
                const colorClass = stock.pChange >= 0 ? 'text-green-500' : 'text-red-500';

                const stockElement = document.createElement('div');
                stockElement.className = 'stock-item cursor-pointer px-3 py-1 dark:hover:bg-neutral-700 rounded inline-flex items-center text-sm stock';
                stockElement.dataset.stock = stock.symbol;
                stockElement.dataset.instrumentId = stock.dhan_instrument_id; // Add instrument ID to element
                stockElement.dataset.index = index.toString();

                stockElement.innerHTML = `
                    <span class="font-medium">${stock.symbol}</span>
                    <span class="ml-2 stock-price">${stock.lastPrice.toFixed(2)}</span>
                    <span class="ml-2 stock-change ${colorClass}">${arrow} ${Math.abs(stock.pChange).toFixed(2)}%</span>
                `;

                fragment.appendChild(stockElement);
            });

            // Duplicate all stocks for continuous scrolling
            const originalStocks = Array.from(fragment.children);
            originalStocks.forEach(stock => {
                fragment.appendChild(stock.cloneNode(true));
            });

            // Clear and append all elements
            stockList.innerHTML = '';
            stockList.appendChild(fragment);

            // Get measurements
            stockItemWidth = stockList.children[0]?.offsetWidth || 100;
            visibleWidth = tickerViewport.offsetWidth;

            // Reset position
            position = 0;
            stockList.style.transform = `translateX(${position}px)`;

            // Initialize visible stocks tracking
            updateVisibleStocks();

            // Start animation if not already running
            if (!animationFrameId) {
                animate();
            }
        }

        /*** STEP 10: Handle ticker visibility toggling ***/
        window.toggleTickerVisibility = async function (show, saveprefrence = true) {
            if (saveprefrence) userData.setItem(tickerVisibilityKey, show);
            tickerContainer.style.display = show ? 'flex' : 'none';

            // Update top menu position
            const topMenuContainer = document.getElementById('top-menu-container');
            if (topMenuContainer) {
                topMenuContainer.style.top = show ? '36px' : '0px';
                topMenuContainer.dataset.tickerVisible = show.toString();
            }

            if (show) {
                // If we don't have data yet, fetch it
                if (!window.tickerData) {
                    try {
                        const response = await fetch('/api/ticker');
                        window.tickerData = await response.json();
                    } catch (error) {
                        console.error("Error fetching ticker data:", error);
                        return;
                    }
                }

                // Initialize the ticker with all stocks
                updateTickerUI();

                // Setup emitter for subscriptions
                setupEmitter();
            } else {
                // Cancel animation when hiding
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                    animationFrameId = null;
                }

                // Unsubscribe from all updates
                unsubscribeFromAllSymbols();
                if (window.emitterClient) {
                    window.emitterClient.disconnect();
                    window.emitterClient = null;
                }
            }
        };

        /*** STEP 11: Clean up subscriptions when ticker is hidden (Add timeout clearing) ***/
        function unsubscribeFromAllSymbols() {
            if (window.emitterClient) {
                // Clear pending update timeout
                if (updateTimeoutId) {
                    clearTimeout(updateTimeoutId);
                    updateTimeoutId = null;
                }
                bufferedUpdates.clear(); // Clear buffer as well

                // Clear all subscriptions
                window.activeSubscriptions.forEach((subscription, symbol) => {
                    const stock = window.tickerData.find(s => s.symbol === symbol);
                    if (stock) {
                        unsubscribeFromSymbol(symbol, stock.dhan_instrument_id);
                    }
                });

                // Clear all tracking sets
                window.activeSubscriptions.clear();
                window.pendingSubscriptions.clear();
                window.visibleStocks.clear();

                // Remove message handler
                window.emitterClient.off('message', handleEmitterMessage);
            }
        }

        /*** STEP 12: Initialize ticker on page load if enabled ***/
        if (showTicker) {
            try {
                // Fetch all tickers (up to 100)
                const response = await fetch('/api/ticker');
                window.tickerData = await response.json();
                tickerContainer.style.display = 'flex';

                // Set top menu position
                const topMenuContainer = document.getElementById('top-menu-container');
                if (topMenuContainer) {
                    topMenuContainer.style.top = '36px';
                    topMenuContainer.dataset.tickerVisible = 'true';
                }

                // Initialize with all stocks
                setTimeout(() => {
                    updateTickerUI();
                    setupEmitter();
                }, 100);
            } catch (error) {
                console.error("Error fetching ticker data:", error);
            }
        }

        /*** STEP 13: Add window event handlers ***/
        // Handle window resize
        window.addEventListener('resize', () => {
            // Debounce for performance
            if (window.resizeTimer) clearTimeout(window.resizeTimer);

            window.resizeTimer = setTimeout(() => {
                visibleWidth = tickerViewport.offsetWidth;
                updateVisibleStocks();
            }, 200);
        });

        // Pause animation on hover
        const stockList = document.getElementById('stock-list');
        stockList.addEventListener('mouseenter', () => {
            window.isTickerAnimating = false;
        });

        stockList.addEventListener('mouseleave', () => {
            window.isTickerAnimating = true;
            currentSpeed = scrollSpeed;
        });

        /*** STEP 14: Set up manual scrolling controls ***/
        let buttonTimer;
        const acceleratedSpeed = 5;
        const scrollLeftBtn = document.getElementById('scroll-left');
        const scrollRightBtn = document.getElementById('scroll-right');

        scrollLeftBtn.addEventListener('mousedown', () => {
            window.isTickerAnimating = true;
            currentSpeed = -acceleratedSpeed;
            buttonTimer = setInterval(() => {
                position += acceleratedSpeed;
                stockList.style.transform = `translateX(${position}px)`;

                // Update visible stocks occasionally
                if (!window.scrollUpdateTimer) {
                    window.scrollUpdateTimer = setTimeout(() => {
                        updateVisibleStocks();
                        window.scrollUpdateTimer = null;
                    }, 100);
                }
            }, 16);
        });

        scrollRightBtn.addEventListener('mousedown', () => {
            window.isTickerAnimating = true;
            currentSpeed = acceleratedSpeed;
            buttonTimer = setInterval(() => {
                position -= acceleratedSpeed;
                stockList.style.transform = `translateX(${position}px)`;

                // Update visible stocks occasionally
                if (!window.scrollUpdateTimer) {
                    window.scrollUpdateTimer = setTimeout(() => {
                        updateVisibleStocks();
                        window.scrollUpdateTimer = null;
                    }, 100);
                }
            }, 16);
        });

        const resetScroll = () => {
            clearInterval(buttonTimer);
            currentSpeed = scrollSpeed;
            window.isTickerAnimating = true;

            // Update visible stocks when stopping manual scroll
            updateVisibleStocks();
        };

        scrollLeftBtn.addEventListener('mouseup', resetScroll);
        scrollLeftBtn.addEventListener('mouseleave', resetScroll);
        scrollRightBtn.addEventListener('mouseup', resetScroll);
        scrollRightBtn.addEventListener('mouseleave', resetScroll);

        /*** STEP 15: Add cleanup handlers for page unload (Add timeout clearing) ***/
        window.addEventListener('beforeunload', () => {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            clearInterval(buttonTimer);
            if (window.resizeTimer) clearTimeout(window.resizeTimer);
            if (window.scrollUpdateTimer) clearTimeout(window.scrollUpdateTimer);

            // Clear pending update timeout
            if (updateTimeoutId) {
                clearTimeout(updateTimeoutId);
                updateTimeoutId = null;
            }

            // Disconnect Emitter client
            if (window.emitterClient) {
                unsubscribeFromAllSymbols(); // This already clears buffer and timeout
                window.emitterClient.disconnect();
            }
        });
    });
</script>