<script>
    window.GreeksUtils = {
        templates: {
            // Template for Greeks Summary
            greeksSummary: `
                <div id="total-greeks-summary" class="mt-4 bg-white rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-base font-semibold text-gray-800">Greeks</h3>
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center space-x-2 text-sm cursor-pointer">
                                <div class="relative">
                                    <input type="checkbox" id="multiply-greeks-by-lot-size" class="sr-only peer">
                                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                </div>
                                <span>Multiply by Lot Size</span>
                            </label>
                            <label class="flex items-center space-x-2 text-sm cursor-pointer">
                                <div class="relative">
                                    <input type="checkbox" id="multiply-greeks-by-number-lots" class="sr-only peer">
                                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                </div>
                                <span>Multiply by Number of Lots</span>
                            </label>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between border-b pb-2">
                            <div class="flex items-center">
                                <span class="text-gray-700">Delta</span>
                                <span class="ml-1 text-gray-400">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" stroke-width="2"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 16v-4m0-4h.01"/>
                                    </svg>
                                </span>
                            </div>
                            <p id="summary-total-delta" class="font-medium">-</p>
                        </div>
                        <div class="flex justify-between border-b pb-2">
                            <div class="flex items-center">
                                <span class="text-gray-700">Theta</span>
                                <span class="ml-1 text-gray-400">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" stroke-width="2"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 16v-4m0-4h.01"/>
                                    </svg>
                                </span>
                            </div>
                            <p id="summary-total-theta" class="font-medium">-</p>
                        </div>
                        <div class="flex justify-between border-b pb-2">
                            <div class="flex items-center">
                                <span class="text-gray-700">Rho</span>
                                <span class="ml-1 text-gray-400">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" stroke-width="2"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 16v-4m0-4h.01"/>
                                    </svg>
                                </span>
                            </div>
                            <p id="summary-total-rho" class="font-medium">-</p>
                        </div>
                        <div class="flex justify-between border-b pb-2">
                            <div class="flex items-center">
                                <span class="text-gray-700">Gamma</span>
                                <span class="ml-1 text-gray-400">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" stroke-width="2"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 16v-4m0-4h.01"/>
                                    </svg>
                                </span>
                            </div>
                            <p id="summary-total-gamma" class="font-medium">-</p>
                        </div>
                        <div class="flex justify-between">
                            <div class="flex items-center">
                                <span class="text-gray-700">Vega</span>
                                <span class="ml-1 text-gray-400">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" stroke-width="2"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 16v-4m0-4h.01"/>
                                    </svg>
                                </span>
                            </div>
                            <p id="summary-total-vega" class="font-medium">-</p>
                        </div>
                    </div>
                </div>
            `
        },
        // Helper function to add collapsible Greeks explanation
        addGreeksExplanation: function (greeksContent) {
            if (!greeksContent) return;

            // Check if explanation already exists
            if (greeksContent.querySelector('#toggle-greeks-explanation')) return;

            // Create the explanation div
            const explanationDiv = document.createElement('div');
            explanationDiv.className = 'mt-8';
            explanationDiv.innerHTML = `
                <button id="toggle-greeks-explanation" class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-colors duration-200">
                    <span>What are the Greeks?</span>
                    <svg id="greeks-chevron" class="w-5 h-5 transform transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
                <div id="greeks-explanation-content" class="hidden mt-2 px-4 py-3 bg-white rounded-lg text-sm text-gray-700 border border-gray-200">
                    <ul class="space-y-2">
                        <li><span class="font-semibold text-purple-500">Delta (Δ):</span> Measures how much an option's price changes when the underlying price changes by ₹1. Ranges from -1 to 1.</li>
                        <li><span class="font-semibold text-green-500">Gamma (Γ):</span> Measures the rate of change of Delta. Higher gamma means Delta will change more rapidly as the underlying price moves.</li>
                        <li><span class="font-semibold text-orange-500">Theta (Θ):</span> Measures annual time decay - how much an option will lose value over one year as it approaches expiration (shown as annual value).</li>
                        <li><span class="font-semibold text-blue-500">Vega (ν):</span> Measures sensitivity to volatility. Shows how much an option's price changes for each 1% change in implied volatility.</li>
                        <li><span class="font-semibold text-amber-500">Rho (ρ):</span> Measures sensitivity to interest rates. Shows how much an option's price changes for each 1% change in interest rates.</li>
                    </ul>
                    <p class="mt-3 text-gray-600 italic">Note: Positive Greeks generally benefit long positions, while negative Greeks benefit short positions.</p>
                </div>
            `;

            greeksContent.appendChild(explanationDiv);

            // Add event listener for the toggle
            const toggleButton = explanationDiv.querySelector('#toggle-greeks-explanation');
            if (toggleButton) {
                toggleButton.onclick = function () {
                    const explanationContent = document.getElementById('greeks-explanation-content');
                    const chevron = document.getElementById('greeks-chevron');
                    if (explanationContent && chevron) {
                        explanationContent.classList.toggle('hidden');
                        chevron.classList.toggle('rotate-180');
                    }
                };
            }
        },

        // Render Greeks table for the selected legs
        renderGreeksTable: function (legs, chainData, tableBodySelector = '.greeks-table-body', symbol, multiplier = 1, perTradeDetails = []) {
            // Skip rendering if backtesting is enabled
            if (window.payoffBacktestingEnabled) {
                const tableBody = document.querySelector(tableBodySelector);
                if (tableBody) tableBody.innerHTML = '';

                // Also hide the Greeks explanation if it exists
                const greeksExplanation = document.querySelector('[data-render="greeks-explanation"]');
                if (greeksExplanation) greeksExplanation.innerHTML = '';

                return;
            }

            const tableBody = document.querySelector(tableBodySelector);
            if (!tableBody) return;

            // Clear existing rows
            tableBody.innerHTML = '';

            // Initialize totals
            let totalDelta = 0;
            let totalTheta = 0;
            let totalGamma = 0;
            let totalVega = 0;
            let totalRho = 0;

            // Get lot size for the current symbol
            const lotSize = LotSizes[symbol] || 1;
            const effectiveLotSize = lotSize * multiplier;

            // Helper function to format numbers
            const formatNumber = (num, decimals = 4) => {
                if (num === undefined || num === null) return '-';
                return parseFloat(num).toFixed(decimals);
            };

            // Helper function to format color based on value
            const getValueColor = (value) => {
                if (value > 0) return 'text-green-600';
                if (value < 0) return 'text-red-600';
                return 'text-gray-600';
            };

            // Process each leg
            legs.forEach((leg, legIndex) => {
                if (!leg.enabled) return; // Skip disabled legs

                // Find the corresponding leg details from perTradeDetails
                const legDetail = perTradeDetails.find(detail => {
                    // Check if the detail is for the current leg
                    const legId = leg.type === 'FUT' ?
                        `leg:${symbol}FUT` :
                        `leg:${symbol}${leg.expiryDate}${leg.strike?.toFixed(1)}${leg.type}`;
                    return detail.tag === legId;
                });

                // Get Greeks from legDetail or set defaults
                let delta = 0, gamma = 0, theta = 0, vega = 0, rho = 0;

                if (legDetail && legDetail.greeks) {
                    delta = legDetail.greeks.delta || 0;
                    gamma = legDetail.greeks.gamma || 0;
                    theta = legDetail.greeks.theta || 0;
                    vega = legDetail.greeks.vega || 0;
                    rho = legDetail.greeks.rho || 0;
                } else if (leg.type === 'FUT') {
                    // For futures, delta is always 1 for long and -1 for short if we don't have details
                    delta = leg.action === 'buy' ? leg.lots : -leg.lots;
                }

                // Add to totals
                totalDelta += delta;
                totalGamma += gamma;
                totalTheta += theta;
                totalVega += vega;
                totalRho += rho;

                // Create row
                const row = document.createElement('tr');

                // Format the instrument label
                let instrumentLabel = '';
                if (leg.type === 'FUT') {
                    instrumentLabel = `${leg.action === 'buy' ? 'B' : 'S'} ${symbol} Futures`;
                } else {
                    instrumentLabel = `${leg.action === 'buy' ? 'B' : 'S'} ${leg.lots} x ${symbol} ${leg.strike} ${leg.type}`;
                }

                // Create row HTML
                row.innerHTML = `
                    <td class="px-4 py-2 whitespace-nowrap">
                        <div class="flex items-center">
                            <span class="inline-flex items-center justify-center h-6 w-6 rounded-full ${leg.action === 'buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} mr-2 font-medium text-xs">
                                ${leg.action === 'buy' ? 'B' : 'S'}
                            </span>
                            <span class="font-medium text-gray-900">${instrumentLabel}</span>
                        </div>
                    </td>
                    <td class="px-4 py-2 text-center ${getValueColor(delta)}">${formatNumber(delta, 4)}</td>
                    <td class="px-4 py-2 text-center ${getValueColor(theta)}">${formatNumber(theta, 1)}</td>
                    <td class="px-4 py-2 text-center ${getValueColor(rho)}">${formatNumber(rho, 1)}</td>
                    <td class="px-4 py-2 text-center ${getValueColor(gamma)}">${formatNumber(gamma, 6)}</td>
                    <td class="px-4 py-2 text-center ${getValueColor(vega)}">${formatNumber(vega, 1)}</td>
                `;

                tableBody.appendChild(row);
            });

            // Update totals
            const container = tableBody.closest('.payoff-content');
            if (container) {
                container.querySelector('#total-delta').innerHTML = `<span class="${getValueColor(totalDelta)}">${formatNumber(totalDelta, 4)}</span>`;
                container.querySelector('#total-theta').innerHTML = `<span class="${getValueColor(totalTheta)}">${formatNumber(totalTheta, 1)}</span>`;
                container.querySelector('#total-rho').innerHTML = `<span class="${getValueColor(totalRho)}">${formatNumber(totalRho, 1)}</span>`;
                container.querySelector('#total-gamma').innerHTML = `<span class="${getValueColor(totalGamma)}">${formatNumber(totalGamma, 6)}</span>`;
                container.querySelector('#total-vega').innerHTML = `<span class="${getValueColor(totalVega)}">${formatNumber(totalVega, 1)}</span>`;
            }

            // Add the collapsible Greeks explanation after the table
            const greeksContentContainer = tableBody.closest('.greeks-content'); // Find the parent greeks content div
            this.addGreeksExplanation(greeksContentContainer);

            // Return the calculated totals
            return { totalDelta, totalGamma, totalTheta, totalVega, totalRho };
        },

        // Update the Greeks Summary section
        updateGreeksSummary: function (data) {
            if (!data || !data.summary || !data.summary.greeks) return;
            if (window.payoffBacktestingEnabled) {
                return;
            }

            const getValueColorClass = (value) => { // Simplified color logic for class names
                if (value > 0) return 'text-green-600';
                if (value < 0) return 'text-red-600';
                return 'text-gray-700'; // Use a neutral color for zero/NaN
            };

            // Store original values in data attributes to support toggling
            const deltaEl = document.getElementById('summary-total-delta');
            const gammaEl = document.getElementById('summary-total-gamma');
            const thetaEl = document.getElementById('summary-total-theta');
            const vegaEl = document.getElementById('summary-total-vega');
            const rhoEl = document.getElementById('summary-total-rho');

            const greeks = data.summary.greeks;
            const lotSize = data.summary.lot_size || 1;

            // Set the base values and store them as data attributes
            if (deltaEl) {
                deltaEl.textContent = window.PayoffChartUtils.formatNumber(greeks.delta, 2);
                deltaEl.className = `font-medium ${getValueColorClass(greeks.delta)}`;
                deltaEl.dataset.baseValue = greeks.delta;
            }

            if (gammaEl) {
                gammaEl.textContent = window.PayoffChartUtils.formatNumber(greeks.gamma, 6);
                gammaEl.className = `font-medium ${getValueColorClass(greeks.gamma)}`;
                gammaEl.dataset.baseValue = greeks.gamma;
            }

            if (thetaEl) {
                thetaEl.textContent = window.PayoffChartUtils.formatNumber(greeks.theta, 1);
                thetaEl.className = `font-medium ${getValueColorClass(greeks.theta)}`;
                thetaEl.dataset.baseValue = greeks.theta;
            }

            if (vegaEl) {
                vegaEl.textContent = window.PayoffChartUtils.formatNumber(greeks.vega, 2);
                vegaEl.className = `font-medium ${getValueColorClass(greeks.vega)}`;
                vegaEl.dataset.baseValue = greeks.vega;
            }

            if (rhoEl) {
                rhoEl.textContent = window.PayoffChartUtils.formatNumber(greeks.rho, 2);
                rhoEl.className = `font-medium ${getValueColorClass(greeks.rho)}`;
                rhoEl.dataset.baseValue = greeks.rho;
            }
        },

        // Setup event handlers for the Greeks-related UI elements
        setupGreeksEventHandlers: function (recalculatePayoff) {
            const multiplyGreeksByLotSizeCheckbox = document.getElementById('multiply-greeks-by-lot-size');
            const multiplyGreeksByNumberOfLotsCheckbox = document.getElementById('multiply-greeks-by-number-lots');
            const perTradeGreeksMultiplyByLotSize = document.getElementById('per-trade-greeks-multiply-by-lot-size');
            const perTradeGreeksMultiplyByNumberLots = document.getElementById('per-trade-greeks-multiply-by-number-lots');

            // Set the checkboxes to the stored values
            multiplyGreeksByLotSizeCheckbox.checked = window.multiplyGreeksByLotSizeCheckbox || false;
            multiplyGreeksByNumberOfLotsCheckbox.checked = window.multiplyGreeksByNumberOfLotsCheckbox || false;
            perTradeGreeksMultiplyByLotSize.checked = window.perTradeGreeksMultiplyByLotSize || false;
            perTradeGreeksMultiplyByNumberLots.checked = window.perTradeGreeksMultiplyByNumberLots || false;


            // Define update function for checkbox changes
            const updateDisplayedValues = () => {
                window.greeksMultiplyByLotSize = multiplyGreeksByLotSizeCheckbox.checked;
                window.greeksMultiplyByNumberOfLots = multiplyGreeksByNumberOfLotsCheckbox.checked;
                window.perTradeGreeksMultiplyByLotSize = perTradeGreeksMultiplyByLotSize.checked;
                window.perTradeGreeksMultiplyByNumberOfLots = perTradeGreeksMultiplyByNumberLots.checked;
                recalculatePayoff();
            };

            // Add event listeners to checkboxes
            if (multiplyGreeksByLotSizeCheckbox) {
                multiplyGreeksByLotSizeCheckbox.onchange = updateDisplayedValues;
            }

            if (multiplyGreeksByNumberOfLotsCheckbox) {
                multiplyGreeksByNumberOfLotsCheckbox.onchange = updateDisplayedValues;
            }

            if (perTradeGreeksMultiplyByLotSize) {
                perTradeGreeksMultiplyByLotSize.onchange = updateDisplayedValues;
            }

            if (perTradeGreeksMultiplyByNumberLots) {
                perTradeGreeksMultiplyByNumberLots.onchange = updateDisplayedValues;
            }
        }
    };
</script>