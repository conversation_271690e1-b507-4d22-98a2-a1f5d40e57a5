<script>
    window.StandardDeviationUtils = {
        templates: {
            // Template for Standard Deviation Summary
            standardDeviationSummary: `
                <div id="standard-deviation-summary" class="mt-4 bg-white rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-1">
                            <h3 class="text-sm font-semibold text-gray-800">Standard Deviation</h3>
                            <div class="relative group">
                                <svg class="w-4 h-4 text-gray-400 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" stroke-width="2"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 16v-4m0-4h.01"/>
                                </svg>
                                <div class="hidden group-hover:block absolute left-0 bottom-full mb-2 p-3 bg-gray-800 text-white text-xs rounded w-64 z-10">
                                    Standard deviation gives you the market's expectation of how much the stock can move. There is a 66% chance that the stock will stay between (+1SD) and (-1SD) limits on your target day, and there is a 95% chance that it will stay between (+2SD) and (-2SD) limits on your target day.
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <span id="sd-info-badge" class="bg-gray-100 text-xs text-gray-600 px-2 py-1 rounded-full">
                                <span id="sd-days-display">7</span>D
                            </span>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="grid grid-cols-3 border-b pb-2">
                            <span class="text-xs text-gray-700">SD</span>
                            <span class="text-xs text-gray-700 text-center">Points</span>
                            <span class="text-xs text-gray-700 text-right">Price</span>
                        </div>
                        <div class="grid grid-cols-3 border-b pb-2">
                            <span class="text-xs text-gray-700">1 SD</span>
                            <span id="sd-1-points" class="text-xs text-blue-600 text-center">-</span>
                            <span id="sd-1-price" class="text-xs font-medium text-right">-</span>
                        </div>
                        <div class="grid grid-cols-3 border-b pb-2">
                            <span class="text-xs text-gray-700">2 SD</span>
                            <span id="sd-2-points" class="text-xs text-blue-600 text-center">-</span>
                            <span id="sd-2-price" class="text-xs font-medium text-right">-</span>
                        </div>
                    </div>
                </div>
            `
        },

        // Function to update the Standard Deviation Summary section
        updateStandardDeviationSummary: function (data) {
            if (!data || !data.standard_deviation) return;
            if (window.payoffBacktestingEnabled) {
                return;
            }

            // Get the standard deviation data
            const sd = data.standard_deviation;
            const sdValue = sd.standard_deviation;
            const currentPrice = sd.Current;
            const days = sd.Days;
            const iv = sd.IV;

            // Update the SD days display
            const sdDaysDisplay = document.getElementById('sd-days-display');
            if (sdDaysDisplay) {
                sdDaysDisplay.textContent = days;
            }

            // Calculate and update 1 SD
            const sd1Points = document.getElementById('sd-1-points');
            const sd1Price = document.getElementById('sd-1-price');
            if (sd1Points && sd1Price) {
                const sd1Value = sdValue;
                const sd1Percentage = (sd1Value / currentPrice) * 100;
                sd1Points.textContent = `${sd1Value.toFixed(2)} (${sd1Percentage.toFixed(2)}%)`;

                // Create two price values (current + SD and current - SD)
                const upPrice = (currentPrice + sd1Value).toFixed(2);
                const downPrice = (currentPrice - sd1Value).toFixed(2);
                sd1Price.innerHTML = `<div>${upPrice}</div><div>${downPrice}</div>`;
            }

            // Calculate and update 2 SD
            const sd2Points = document.getElementById('sd-2-points');
            const sd2Price = document.getElementById('sd-2-price');
            if (sd2Points && sd2Price) {
                const sd2Value = sdValue * 2;
                const sd2Percentage = (sd2Value / currentPrice) * 100;
                sd2Points.textContent = `${sd2Value.toFixed(2)} (${sd2Percentage.toFixed(2)}%)`;

                // Create two price values (current + 2*SD and current - 2*SD)
                const upPrice = (currentPrice + sd2Value).toFixed(2);
                const downPrice = (currentPrice - sd2Value).toFixed(2);
                sd2Price.innerHTML = `<div>${upPrice}</div><div>${downPrice}</div>`;
            }
        },

        // Setup event handlers for SD-related UI elements
        setupStandardDeviationEventHandlers: function (container, recalculatePayoff) {
            // Setup SD options toggle
            const sdOptionsToggle = container.querySelector('#sd-options-toggle');
            const sdModePanel = container.querySelector('#sd-mode-panel');
            const sdDaysInput = container.querySelector('#sd-days-input');
            const sdDaysDecrement = container.querySelector('#sd-days-decrement');
            const sdDaysIncrement = container.querySelector('#sd-days-increment');
            const sdModeRadios = container.querySelectorAll('input[name="sd-mode"]');

            if (sdOptionsToggle && sdModePanel) {
                // Initialize window.sdMode and window.sdDays if not already set
                window.sdMode = window.sdMode || 'fixed';
                window.sdDays = window.sdDays || 7;

                // Set initial values based on window variables
                if (sdDaysInput) {
                    sdDaysInput.value = window.sdDays;
                }
                
                // Set the correct radio button based on window.sdMode
                sdModeRadios.forEach(radio => {
                    if (radio.value === window.sdMode) {
                        radio.checked = true;
                    }
                });

                // Toggle SD options panel
                sdOptionsToggle.addEventListener('click', () => {
                    sdModePanel.classList.toggle('hidden');
                });

                // Close panel when clicking outside
                document.addEventListener('click', (e) => {
                    if (!sdOptionsToggle.contains(e.target) &&
                        !sdModePanel.contains(e.target)) {
                        sdModePanel.classList.add('hidden');
                    }
                });

                // Handle SD days input changes
                if (sdDaysInput) {
                    sdDaysInput.addEventListener('change', () => {
                        let days = parseInt(sdDaysInput.value);
                        if (isNaN(days) || days < 1) {
                            days = 1;
                            sdDaysInput.value = days;
                        } else if (days > 365) {
                            days = 365;
                            sdDaysInput.value = days;
                        }
                        window.sdDays = days;
                        recalculatePayoff();
                    });
                }

                // Handle SD days decrement button
                if (sdDaysDecrement) {
                    sdDaysDecrement.addEventListener('click', () => {
                        let days = parseInt(sdDaysInput.value);
                        if (isNaN(days) || days <= 1) {
                            days = 1;
                        } else {
                            days--;
                        }
                        sdDaysInput.value = days;
                        window.sdDays = days;
                        recalculatePayoff();
                    });
                }

                // Handle SD days increment button
                if (sdDaysIncrement) {
                    sdDaysIncrement.addEventListener('click', () => {
                        let days = parseInt(sdDaysInput.value);
                        if (isNaN(days)) {
                            days = 1;
                        } else if (days >= 365) {
                            days = 365;
                        } else {
                            days++;
                        }
                        sdDaysInput.value = days;
                        window.sdDays = days;
                        recalculatePayoff();
                    });
                }

                // Handle SD mode radio buttons
                sdModeRadios.forEach(radio => {
                    radio.addEventListener('change', () => {
                        window.sdMode = radio.value;
                        
                        // Update the toggle button text
                        const toggleText = sdOptionsToggle.querySelector('span');
                        if (toggleText) {
                            toggleText.textContent = radio.value === 'fixed' ? 'SD Fixed' : 'SD Dynamic';
                        }
                        
                        recalculatePayoff();
                    });
                });
            }
        }
    };
</script>
