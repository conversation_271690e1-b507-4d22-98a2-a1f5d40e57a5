{% include 'blocks/payoff-utils/payoff-chart.html' %}
{% include 'blocks/payoff-utils/greeks-utils.html' %}
{% include 'blocks/payoff-utils/standard-deviation-utils.html' %}
{% include 'blocks/payoff-utils/strikewise-iv-utils.html' %}
<!-- AI Feedback Component -->
{% include 'blocks/ai-feedback.html' %}

<script>
    window.PayoffUtils = {
        templates: {
            payoffContainer: `
                <div class="bg-white rounded-lg md:p-5 p-3 mb-3">
                    <div class="md:flex justify-between items-center mb-3">
                        <h2 class="text-base font-semibold">Payoff / P&amp;L</h2>
                        <div class="flex gap-2 items-center">
                            <button class="backtest-btn bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm hidden">Backtest</button>
                            <button id="tradingBtn"
                                class="bg-blue-100 flex font-medium gap-1 hover:bg-blue-600 hover:text-white items-center px-3 py-1 rounded-md text-blue-600 text-sm transition-colors">
                                Trade
                                <span
                                    class="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 whitespace-nowrap bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                                    Open Trading Panel
                                </span>
                            </button>
                            <button class="share-btn bg-blue-100 flex font-medium gap-1 hover:bg-blue-600 hover:text-white items-center px-3 py-1 rounded-md text-blue-600 text-sm transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"/>
                                </svg>
                                Share
                            </button>
                            <button id="strategy-ai-analysis-btn"
                                class="ai-analysis-btn animate-backgroundMove bg-[200%_auto] bg-gradient-to-r border-none capitalize cursor-pointer flex font-medium from-[#ff9d11] hover:bg-[0%_center] px-4 py-1 relative rounded-md text-lg text-sm to-[#ff9f16] transition-[background-position_0.5s_ease] via-[#ffd450] gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-sparkles">
                                    <path
                                        d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                    <path d="M20 3v4" />
                                    <path d="M22 5h-4" />
                                    <path d="M4 17v2" />
                                    <path d="M5 18H3" />
                                </svg> Get AI Analysis
                            </button>
                        </div>
                    </div>
                    <div class="payoff-info text-sm text-gray-700"></div>
                </div>
                <div class="bg-white rounded-lg md:p-5 p-3 mb-3">
                <div class="payoff-tabs flex text-sm gap-5 border-b">
                    <button class="payoff-tab pb-2 border-blue-600 text-blue-600 font-medium" data-tab="graph">
                        Payoff Graph
                    </button>
                    <button class="payoff-tab pb-2 text-gray-600  font-medium" data-tab="table">
                        Payoff Table
                    </button>
                    <button class="payoff-tab pb-2 text-gray-600 font-medium" data-tab="pnl-table">
                        P&L Table
                    </button>
                    <button class="payoff-tab pb-2 text-gray-600 font-medium" data-tab="greeks">
                        Greeks
                    </button>
                    <button class="payoff-tab pb-2 text-gray-600 font-medium" data-tab="strategy-chart">
                        Strategy Chart
                    </button>
                </div>
                <div class="payoff-content">
                    <div class="payoff-tab-content graph-content pt-4">
                        <div class="float-right flex items-center gap-3 mb-2">
                            <!-- SD Options Dropdown -->
                            <div class="relative">
                                <button id="sd-options-toggle" class="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-xs border border-gray-300">
                                    <span>SD Fixed</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>
                                <!-- SD Mode Dropdown Panel -->
                                <div id="sd-mode-panel" class="hidden absolute right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 w-64">
                                    <div class="p-3 border-b">
                                        <p class="text-sm font-medium mb-2">SD Mode</p>
                                        <div class="space-y-3">
                                            <!-- Fixed SD Option -->
                                            <label class="flex items-center">
                                                <input type="radio" name="sd-mode" value="fixed" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded-full">
                                                <span class="ml-2 text-sm text-gray-700">Fixed SD</span>
                                            </label>
                                            <div class="pl-6">
                                                <p class="text-xs text-gray-500 mb-2">SD will be based on a fixed number of days</p>
                                                <div class="flex items-center">
                                                    <button id="sd-days-decrement" class="px-2 py-1 border border-gray-300 rounded-l-md bg-white text-gray-600 hover:bg-gray-100">−</button>
                                                    <input type="number" id="sd-days-input" value="7" min="1" max="365" class="w-12 text-center border-y border-gray-300 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                                    <button id="sd-days-increment" class="px-2 py-1 border border-gray-300 rounded-r-md bg-white text-gray-600 hover:bg-gray-100">+</button>
                                                    <span class="ml-2 text-sm text-gray-700">Days</span>
                                                </div>
                                            </div>

                                            <!-- Dynamic SD Option -->
                                            <label class="flex items-center">
                                                <input type="radio" name="sd-mode" value="dynamic" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded-full">
                                                <span class="ml-2 text-sm text-gray-700">Dynamic SD</span>
                                            </label>
                                            <p class="pl-6 text-xs text-gray-500">SD will be based on the selected target date and time</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- OI Data Display Options -->
                            <div class="relative">
                                <button id="oi-options-toggle" class="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-xs border border-gray-300">
                                    <span>OI Options</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>
                                <!-- Dropdown Panel -->
                                <div id="oi-options-panel" class="hidden absolute right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 w-64">
                                    <!-- Toggle switch -->
                                    <div class="p-3 border-b">
                                        <label class="flex items-center justify-between">
                                            <span class="text-sm font-medium">Show Open Interest</span>
                                            <div class="relative inline-block w-10 align-middle select-none transition duration-200 ease-in">
                                                <input type="checkbox" id="show-oi-toggle" class="sr-only peer"/>
                                                <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                            </div>
                                        </label>
                                    </div>
                                    <!-- OI Type Selection -->
                                    <div class="p-3 border-b">
                                        <p class="text-sm font-medium mb-2">OI Type</p>
                                        <div class="space-y-2">
                                            <label class="flex items-center">
                                                <input type="radio" name="oi-type" value="oi" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded-full">
                                                <span class="ml-2 text-sm text-gray-700">Open Interest</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" name="oi-type" value="oi_change" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded-full">
                                                <span class="ml-2 text-sm text-gray-700">Change in OI</span>
                                            </label>
                                        </div>
                                    </div>
                                    <!-- Expiry Selection -->
                                    <div class="p-3">
                                        <p class="text-sm font-medium mb-2">Expiry Used</p>
                                        <div id="expiry-checkboxes" class="space-y-2 max-h-48 overflow-y-auto">
                                            <!-- Checkboxes will be populated dynamically -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="relative group">
                                <button class="reset-zoom bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-xs hover:bg-gray-300 transition-colors flex items-center gap-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                    </svg>
                                    Reset Zoom
                                </button>
                                <div class="hidden group-hover:block absolute right-0 bottom-full mb-2 p-2 bg-gray-800 text-white text-xs rounded w-48 z-10">
                                    Drag horizontally to zoom in on a specific area. Pan by clicking and dragging.
                                </div>
                            </div>
                        </div>
                        <div class="w-full overflow-auto custom-scroll">
                            <canvas class="payoff-chart pt-12" style="min-width:800px; height:400px;"></canvas>
                        </div>
                    </div>
                    <div class="payoff-tab-content pnl-table-content hidden pt-4">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 text-sm">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Instrument</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Target P&L</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Target Price</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Entry Price</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">LTP</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200 pnl-table-body">
                                </tbody>
                                <tfoot class="bg-gray-50">
                                    <tr>
                                        <th scope="row" class="px-4 py-3 text-left text-xs font-medium text-gray-500">Total</th>
                                        <td id="total-target-pnl" class="px-4 py-3 text-center"></td>
                                        <td id="total-theoretical-price" class="px-4 py-3 text-center"></td>
                                        <td id="total-entry-price" class="px-4 py-3 text-center"></td>
                                        <td id="total-ltp" class="px-4 py-3 text-center"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    <div class="payoff-tab-content table-content hidden pt-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-2">
                                <span class="text-sm font-medium">Target Interval</span>
                                <div class="relative">
                                    <button id="target-interval-button" class="flex items-center gap-2 bg-white border border-gray-300 text-gray-700 text-sm rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <span id="target-interval-display">50</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                    <!-- Target Interval Dropdown Panel -->
                                    <div id="target-interval-panel" class="hidden absolute left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 w-48">
                                        <div class="p-2">
                                            <p class="text-sm font-medium mb-2">Select Target Interval</p>
                                            <div class="grid grid-cols-3 gap-2">
                                                <button class="interval-option px-3 py-1.5 border border-gray-300 rounded-md hover:bg-gray-100 text-sm" data-value="10">10</button>
                                                <button class="interval-option px-3 py-1.5 border border-gray-300 rounded-md hover:bg-gray-100 text-sm" data-value="25">25</button>
                                                <button class="interval-option px-3 py-1.5 border border-gray-300 rounded-md hover:bg-gray-100 text-sm bg-blue-500 text-white" data-value="50">50</button>
                                                <button class="interval-option px-3 py-1.5 border border-gray-300 rounded-md hover:bg-gray-100 text-sm" data-value="100">100</button>
                                                <button class="interval-option px-3 py-1.5 border border-gray-300 rounded-md hover:bg-gray-100 text-sm" data-value="200">200</button>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Hidden select for compatibility with existing code -->
                                    <select id="target-interval-select" class="hidden">
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50" selected>50</option>
                                        <option value="100">100</option>
                                        <option value="200">200</option>
                                    </select>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <label class="flex items-center space-x-2 text-sm cursor-pointer">
                                    <div class="relative">
                                        <input type="checkbox" id="show-percentage" class="sr-only peer" checked>
                                        <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                    </div>
                                    <span>Show %</span>
                                </label>
                            </div>
                        </div>
                        <div class="payoff-table"></div>
                    </div>
                    <div class="payoff-tab-content greeks-content hidden pt-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-base font-semibold text-gray-800">Greeks</h3>
                                <div class="flex items-center space-x-4">
                                    <label class="flex items-center space-x-2 text-sm cursor-pointer">
                                        <div class="relative">
                                            <input type="checkbox" id="per-trade-greeks-multiply-by-lot-size" class="sr-only peer">
                                            <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                        </div>
                                        <span>Multiply by Lot Size</span>
                                    </label>
                                    <label class="flex items-center space-x-2 text-sm cursor-pointer">
                                        <div class="relative">
                                            <input type="checkbox" id="per-trade-greeks-multiply-by-number-lots" class="sr-only peer">
                                            <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                        </div>
                                        <span>Multiply by Number of Lots</span>
                                    </label>
                                </div>
                            </div>
                            <div class="overflow-x-auto border border-gray-200 rounded-md">
                                <table class="min-w-full divide-y divide-gray-200 text-sm">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Instrument</th>
                                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Delta</th>
                                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Theta</th>
                                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Rho</th>
                                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Gamma</th>
                                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Vega</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200 greeks-table-body">
                                    </tbody>
                                    <tfoot class="bg-gray-50">
                                        <tr>
                                            <th scope="row" class="px-4 py-3 text-left text-xs font-medium text-gray-500">Total</th>
                                            <td id="total-delta" class="px-4 py-3 text-center"></td>
                                            <td id="total-theta" class="px-4 py-3 text-center"></td>
                                            <td id="total-rho" class="px-4 py-3 text-center"></td>
                                            <td id="total-gamma" class="px-4 py-3 text-center"></td>
                                            <td id="total-vega" class="px-4 py-3 text-center"></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <!-- Greeks explanation - (Added dynamically in renderGreeksTable) -->
                    </div>
                    <!-- Strategy chart -->
                    <div class="payoff-tab-content strategy-chart-content hidden pt-4">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-base font-medium text-gray-800">Strategy Price Chart</h3>
                            <div class="flex items-center gap-2">
                                <button id="fetch-strategy-data" class="hidden px-3 py-1 text-sm bg-blue-100 text-blue-700 hover:bg-blue-600 hover:text-white rounded transition-colors">
                                    Refresh Data
                                </button>
                                <label class="flex items-center space-x-2 text-sm cursor-pointer">
                                <div class="relative">
                                    <input type="checkbox" id="invert-price-of-strategy-chart" class="sr-only peer">
                                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                </div>
                                <span>Invert Price</span>
                            </label>
                            </div>
                        </div>
                        <div id="strategy-chart-container" class="w-full overflow-auto custom-scroll">
                            <canvas id="strategy-price-chart" style="min-width:800px; height:400px;"></canvas>
                        </div>
                        <div id="strategy-chart-loading" class="py-10 flex items-center justify-center hidden">
                            <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span class="ml-3 text-gray-700">Loading strategy data...</span>
                        </div>
                        <div id="strategy-chart-error" class="py-10 text-center text-red-600 hidden">
                            <p>Failed to load strategy data. Please try again.</p>
                        </div>
                        <div id="strategy-chart-empty" class="py-10 text-center text-gray-600">
                            <p>Click "Refresh Data" to see how your strategy has performed over the past days.</p>
                        </div>
                    </div>
                </div>
            `,
            emptyState: `
                <div class="bg-white rounded-lg lg:p-8 p-3 text-neutral-600 text-center">
                    <p class="text-lg font-medium">No options selected.</p>
                    <p class="mt-2">Start adding option legs to view your payoff, risk/reward, and breakeven details.</p>
                </div>
            `,
            sliderControls: `
                <div id="slider-controls" class="mt-4 bg-white rounded-lg md:p-5 p-3">
                    <div class="space-y-6">
                        <!-- Price Slider -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <div class="flex items-center gap-2">
                                    <h3 class="text-sm font-medium text-gray-700"><span class="symbol-target-label">Symbol</span> Target</h3>
                                    <span id="price-change-percentage" class="text-sm font-medium text-gray-600">0.0%</span>
                                    <button class="price-reset-btn text-sm text-blue-600 hover:text-blue-800">Reset</button>
                                </div>
                                <div class="flex items-center gap-2">
                                    <button class="price-decrement-btn px-2 py-1 border border-gray-300 rounded-md bg-white text-gray-600 hover:bg-gray-100">-</button>
                                    <input type="number" step="10" id="price-slider-value" class="w-20 text-right border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                                    <button class="price-increment-btn px-2 py-1 border border-gray-300 rounded-md bg-white text-gray-600 hover:bg-gray-100">+</button>
                                </div>
                            </div>
                            <div id="price-slider" class="slider-blue"></div>
                        </div>

                        <!-- Expiry Date Slider -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <div class="flex items-center gap-2">
                                    <h3 class="text-sm font-medium text-gray-700">Date:</h3>
                                    <span id="days-to-expiry" class="text-sm text-gray-600">6D to expiry</span>
                                    <button class="date-reset-btn text-sm text-blue-600 hover:text-blue-800">Reset</button>
                                </div>
                                <div class="flex items-center gap-2">
                                    <button class="date-prev-btn px-1 py-1 border border-gray-300 rounded-md bg-white text-gray-600 hover:bg-gray-100">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                        </svg>
                                    </button>
                                    <span id="expiry-slider-date" class="text-sm font-medium">Fri, 11 Apr 12:16 PM</span>
                                    <button class="date-next-btn px-1 py-1 border border-gray-300 rounded-md bg-white text-gray-600 hover:bg-gray-100">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="flex text-xs text-gray-500 justify-between">
                                <span id="expiry-slider-start">09 Apr</span>
                                <span id="expiry-slider-end">17 Apr</span>
                            </div>
                            <div id="expiry-slider" class="slider-blue"></div>
                        </div>
                    </div>
                </div>
            `,
        },

        // This is the entry point for the payoff UI
        renderPayoffUI: function (container, config = {}) {
            // Store 'this' reference to use inside callbacks
            const self = this;

            const {
                legs = [],
                symbol = '',
                spotPrice = 0,
                chainData = null,
                multiplier = 1,
                backtestDate = null,
                onBacktest = null,
                onShare = null,
                chartId = 'payoffChart',
                showShare = false,
                expiryDates = []
            } = config;

            window.payoffChainData = chainData;

            // Setup backtest button
            if (backtestDate) {
                window.payoffBacktestingEnabled = true;
            } else {
                window.payoffBacktestingEnabled = false;
            }

            // Initialize container with template
            container.innerHTML = legs.length ? this.templates.payoffContainer : this.templates.emptyState;
            if (!legs.length) return;

            // Append slider controls after the tab content
            const payoffContent = container.querySelector('.payoff-content');
            let sliderContainer = null; // Define sliderContainer variable
            if (payoffContent && !window.payoffBacktestingEnabled) {
                payoffContent.insertAdjacentHTML('afterend', this.templates.sliderControls);
                // Find the slider container *after* inserting it
                sliderContainer = payoffContent.nextElementSibling;
            }

            // Append Greeks summary and Standard Deviation sections after the slider controls if sliders exist
            if (sliderContainer && !window.payoffBacktestingEnabled) {
                // Create a flex container for the Greeks and Standard Deviation sections
                const flexContainer = document.createElement('div');
                flexContainer.className = 'md:flex md:gap-4';
                flexContainer.innerHTML = `
                    <div class="md:w-1/2">${window.GreeksUtils.templates.greeksSummary}</div>
                    <div class="md:w-1/2">${window.StandardDeviationUtils.templates.standardDeviationSummary}</div>
                `;
                sliderContainer.insertAdjacentElement('afterend', flexContainer);

                // Create a container for the Strikewise IVs section
                const strikewiseIvContainer = document.createElement('div');
                strikewiseIvContainer.id = 'strikewise-iv-container';
                strikewiseIvContainer.className = 'mt-4';
                flexContainer.insertAdjacentElement('afterend', strikewiseIvContainer);

                // Setup Greeks event handlers using GreeksUtils
                window.GreeksUtils.setupGreeksEventHandlers(recalculatePayoff);
                // Setup Standard Deviation event handlers
                window.StandardDeviationUtils.setupStandardDeviationEventHandlers(container, recalculatePayoff);
                // Setup Strikewise IV event handlers
                window.StrikewiseIvUtils.setupStrikewiseIvUI(container, legs, recalculatePayoff);
            }

            // Setup elements
            const elements = {
                info: container.querySelector('.payoff-info'),
                chart: container.querySelector('.payoff-chart'),
                table: container.querySelector('.payoff-table'),
                greeksBody: container.querySelector('.greeks-table-body'),
                tabs: container.querySelectorAll('.payoff-tab'),
                contents: container.querySelectorAll('.payoff-tab-content'),
                share: container.querySelector('.share-btn'),
                resetZoom: container.querySelector('.reset-zoom'),
                backtestButton: container.querySelector('.backtest-btn'),
                tradingButton: container.querySelector('#tradingBtn'),
                oiOptionsToggle: container.querySelector('#oi-options-toggle'),
                oiOptionsPanel: container.querySelector('#oi-options-panel'),
                showOiToggle: container.querySelector('#show-oi-toggle'),
                oiTypeRadios: container.querySelectorAll('input[name="oi-type"]'),
                expiryCheckboxesContainer: container.querySelector('#expiry-checkboxes'),
                priceSlider: container.querySelector('#price-slider'),
                priceInput: container.querySelector('#price-slider-value'),
                expirySlider: container.querySelector('#expiry-slider'),
                expiryDate: container.querySelector('#expiry-slider-date')
            };

            // Set initial state
            let showOI = true;
            let oiType = 'oi';
            let selectedExpiries = [];

            // Initialize SD mode settings
            window.sdMode = window.sdMode || 'fixed';
            window.sdDays = window.sdDays || 7;

            // Initialize target interval
            window.targetInterval = window.targetInterval || '50';

            // Clear cached payoff table since legs are changing
            window.payoffTable = null;
            window.payoffData = null;

            // Setup backtest button
            if (backtestDate) {
                window.payoffBacktestingEnabled = true;
                window.lastSelectedPayoffTab = 'graph'; // Set the last selected tab to graph
                // Hide the tabs
                const tabsToHide = ['pnl-table', 'greeks'];
                tabsToHide.forEach(tab => {
                    const tabElement = container.querySelector(`[data-tab="${tab}"]`);
                    if (tabElement) {
                        tabElement.classList.add('hidden');
                    }
                });
                elements.backtestButton.classList.remove('hidden');
                elements.backtestButton.addEventListener('click', () => onBacktest?.());
            }

            if (elements.showOiToggle) {
                elements.showOiToggle.checked = showOI;
            }

            
            // Function to handle the "Trade" button click
            elements.tradingButton.onclick = function () {
                window.TradeUtils.openTradingPanel({
                    symbol: symbol,
                    spotPrice: spotPrice,
                    multiplier: multiplier,
                    legs: legs,
                    optionChainData: chainData,
                    lotSize: LotSizes[symbol] || 1
                });
            };

            // Initialize Price Slider
            if (elements.priceSlider && typeof noUiSlider !== 'undefined' && !window.payoffBacktestingEnabled) {
                // Calculate min and max prices (±10% of spot price)
                const minPrice = Math.round(spotPrice * 0.9);
                const maxPrice = Math.round(spotPrice * 1.1);

                // Store spot price for percentage calculations
                window.currentSpotPrice = spotPrice;

                // Set the symbol name in the label
                const symbolLabel = container.querySelector('.symbol-target-label');
                if (symbolLabel && symbol) {
                    symbolLabel.textContent = symbol;
                }

                // Get elements
                const priceInput = elements.priceInput;
                const priceChangePercentage = container.querySelector('#price-change-percentage');
                const decrementBtn = container.querySelector('.price-decrement-btn');
                const incrementBtn = container.querySelector('.price-increment-btn');
                const resetPriceBtn = container.querySelector('.price-reset-btn');

                // Initialize price input value
                if (priceInput) {
                    priceInput.value = spotPrice;
                    priceInput.min = minPrice;
                    priceInput.max = maxPrice;
                    priceInput.step = 10;
                }

                // Single update function to handle all price changes
                function updatePriceDisplay(value, updateSlider = true, updateInput = true) {
                    const numValue = parseFloat(value);

                    // Update input if needed and not already at this value
                    if (updateInput && priceInput && parseInt(priceInput.value) !== numValue) {
                        priceInput.value = numValue;
                    }

                    // Update slider if needed
                    if (updateSlider && elements.priceSlider.noUiSlider) {
                        const currentSliderValue = parseInt(elements.priceSlider.noUiSlider.get());
                        if (currentSliderValue !== numValue) {
                            elements.priceSlider.noUiSlider.set([numValue]);
                        }
                    }

                    // Update percentage display
                    if (priceChangePercentage && spotPrice) {
                        const percentChange = ((numValue - spotPrice) / spotPrice) * 100;
                        const formattedChange = (percentChange >= 0 ? '+' : '') + percentChange.toFixed(1) + '%';
                        priceChangePercentage.textContent = formattedChange;

                        // Set appropriate color
                        priceChangePercentage.classList.remove('text-green-600', 'text-red-600', 'text-gray-600');
                        if (percentChange > 0) {
                            priceChangePercentage.classList.add('text-green-600');
                        } else if (percentChange < 0) {
                            priceChangePercentage.classList.add('text-red-600');
                        } else {
                            priceChangePercentage.classList.add('text-gray-600');
                        }
                    }
                }

                // Create the price slider
                if (!elements.priceSlider.noUiSlider && !window.payoffBacktestingEnabled) {
                    noUiSlider.create(elements.priceSlider, {
                        start: [spotPrice],
                        connect: false,
                        step: 10,
                        range: {
                            'min': minPrice,
                            'max': maxPrice
                        },
                        format: {
                            to: function (value) {
                                return Math.round(value);
                            },
                            from: function (value) {
                                return Math.round(value);
                            }
                        }
                    });

                    // Update input when slider changes - don't update the slider again
                    elements.priceSlider.noUiSlider.on('update', function (values, handle) {
                        updatePriceDisplay(values[handle], false, true);
                    });

                    // Recalculate payoff when user stops dragging
                    elements.priceSlider.noUiSlider.on('change', function (values, handle) {
                        // Trigger payoff recalculation
                        recalculatePayoff();
                    });

                    // Handle input change
                    if (priceInput) {
                        priceInput.addEventListener('change', function () {
                            let newValue = parseInt(this.value);

                            // Enforce min/max bounds
                            if (newValue < minPrice) newValue = minPrice;
                            if (newValue > maxPrice) newValue = maxPrice;

                            // Update price display - update slider but not input (already changed)
                            updatePriceDisplay(newValue, true, false);

                            // Trigger payoff recalculation
                            recalculatePayoff();
                        });
                    }

                    // Decrement button
                    if (decrementBtn) {
                        decrementBtn.addEventListener('click', function () {
                            const currentValue = parseInt(priceInput.value);
                            const newValue = Math.max(currentValue - 10, minPrice);
                            updatePriceDisplay(newValue, true, true);
                            // Trigger payoff recalculation
                            recalculatePayoff();
                        });
                    }

                    // Increment button
                    if (incrementBtn) {
                        incrementBtn.addEventListener('click', function () {
                            const currentValue = parseInt(priceInput.value);
                            const newValue = Math.min(currentValue + 10, maxPrice);
                            updatePriceDisplay(newValue, true, true);
                            // Trigger payoff recalculation
                            recalculatePayoff();
                        });
                    }

                    // Reset button
                    if (resetPriceBtn) {
                        resetPriceBtn.addEventListener('click', function () {
                            updatePriceDisplay(spotPrice, true, true);
                            // Trigger payoff recalculation
                            recalculatePayoff();
                        });
                    }
                } else {
                    // Update existing slider
                    elements.priceSlider?.noUiSlider?.updateOptions({
                        start: [spotPrice],
                        connect: false,
                        step: 10,
                        range: {
                            'min': minPrice,
                            'max': maxPrice
                        }
                    });
                    updatePriceDisplay(spotPrice, false, true);
                }
            }

            // Initialize Expiry Date Slider
            if (elements.expirySlider && typeof noUiSlider !== 'undefined' && !window.payoffBacktestingEnabled) {
                // Find nearest expiry from legs
                const nearestExpiry = this.getNearestExpiry(legs);

                if (nearestExpiry) {
                    // Get current date as minimum
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);

                    // Calculate total days between today and expiry
                    const diffTime = nearestExpiry - today;
                    const totalDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                    // Find date elements
                    const expiryDateDisplay = container.querySelector('#expiry-slider-date');
                    const daysToExpiryDisplay = container.querySelector('#days-to-expiry');
                    const sliderRangeStart = container.querySelector('.flex.text-xs.text-gray-500 span:first-child');
                    const sliderRangeEnd = container.querySelector('.flex.text-xs.text-gray-500 span:last-child');

                    // Set range display
                    if (sliderRangeStart) {
                        sliderRangeStart.textContent = today.toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'short'
                        });
                    }

                    if (sliderRangeEnd) {
                        sliderRangeEnd.textContent = nearestExpiry.toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'short'
                        });
                    }

                    // Format today's date for display (default)
                    const formattedToday = formatDateWithDay(today);

                    // Update the date display with today's date
                    if (expiryDateDisplay) {
                        expiryDateDisplay.textContent = formattedToday;
                    }

                    // Update days to expiry
                    if (daysToExpiryDisplay) {
                        daysToExpiryDisplay.textContent = `${totalDays}D to expiry`;
                    }

                    // Helper function to format date with day of week
                    function formatDateWithDay(date) {
                        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                        const day = days[date.getDay()];
                        const formattedDate = date.toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'short'
                        });

                        // Use current time for display
                        const now = new Date();
                        const hours = now.getHours();
                        const minutes = now.getMinutes();
                        const ampm = hours >= 12 ? 'PM' : 'AM';
                        const formattedHours = hours % 12 || 12;
                        const formattedMinutes = minutes.toString().padStart(2, '0');
                        const timeString = `${formattedHours}:${formattedMinutes} ${ampm}`;

                        return `${day}, ${formattedDate} ${timeString}`;
                    }

                    // Create the expiry slider
                    if (!elements.expirySlider.noUiSlider) {
                        noUiSlider.create(elements.expirySlider, {
                            start: [0], // Start at 0 (today)
                            connect: false, // Disable connection
                            step: 1,
                            range: {
                                'min': 0,
                                'max': totalDays
                            },
                            format: {
                                to: function (value) {
                                    return Math.round(value);
                                },
                                from: function (value) {
                                    return Math.round(value);
                                }
                            }
                        });

                        // Update date display when slider changes (without recalculating payoff)
                        elements.expirySlider.noUiSlider.on('update', function (values, handle) {
                            const daysFromToday = parseInt(values[handle]);
                            const daysToExpiry = totalDays - daysFromToday;
                            const selectedDate = new Date(today);
                            selectedDate.setDate(today.getDate() + daysFromToday);

                            // Format with day of week
                            const formattedSelectedDate = formatDateWithDay(selectedDate);

                            if (expiryDateDisplay) {
                                expiryDateDisplay.textContent = formattedSelectedDate;
                            }

                            if (daysToExpiryDisplay) {
                                daysToExpiryDisplay.textContent = `${daysToExpiry}D to expiry`;
                            }
                        });

                        // Recalculate payoff only when user stops dragging the slider
                        elements.expirySlider.noUiSlider.on('change', function (values, handle) {
                            recalculatePayoff();
                        });

                        // Handle date navigation buttons
                        const prevDateBtn = container.querySelector('.date-prev-btn');
                        const nextDateBtn = container.querySelector('.date-next-btn');

                        if (prevDateBtn) {
                            prevDateBtn.addEventListener('click', function () {
                                const currentValue = elements.expirySlider.noUiSlider.get();
                                const newValue = Math.max(parseInt(currentValue) - 1, 0);
                                elements.expirySlider.noUiSlider.set(newValue);
                                // Explicitly call recalculatePayoff since setting the value doesn't trigger the 'change' event
                                recalculatePayoff();
                            });
                        }

                        if (nextDateBtn) {
                            nextDateBtn.addEventListener('click', function () {
                                const currentValue = elements.expirySlider.noUiSlider.get();
                                const newValue = Math.min(parseInt(currentValue) + 1, totalDays);
                                elements.expirySlider.noUiSlider.set(newValue);
                                // Explicitly call recalculatePayoff since setting the value doesn't trigger the 'change' event
                                recalculatePayoff();
                            });
                        }

                        // Handle date reset button
                        const resetDateBtn = container.querySelector('.date-reset-btn');
                        if (resetDateBtn) {
                            resetDateBtn.addEventListener('click', function () {
                                elements.expirySlider.noUiSlider.set(0); // Reset to today
                                // Explicitly call recalculatePayoff since setting the value doesn't trigger the 'change' event
                                recalculatePayoff();
                            });
                        }
                    } else {
                        // Update existing slider
                        elements.expirySlider.noUiSlider.updateOptions({
                            start: [0], // Start at 0 (today)
                            connect: false, // Disable connection
                            range: {
                                'min': 0,
                                'max': totalDays
                            }
                        });
                    }
                }
            }

            // Setup event listeners for payoff tab switching
            elements.tabs.forEach(tab => {
                tab.onclick = function () {
                    const targetTab = tab.dataset.tab;
                    elements.tabs.forEach(t => t.classList.remove('border-blue-600', 'text-blue-600'));
                    elements.tabs.forEach(t => t.classList.add('text-gray-600'));
                    elements.contents.forEach(c => c.classList.add('hidden'));
                    tab.classList.add('border-blue-600', 'text-blue-600');
                    tab.classList.remove('text-gray-600');
                    container.querySelector(`.${targetTab}-content`).classList.remove('hidden');

                    // Save last selected tab
                    window.lastSelectedPayoffTab = targetTab;

                    // If the chart tab is clicked and we have payoff data, update the chart
                    if (targetTab === 'graph' && window.payoffTable) {
                        const isChartVisible = elements.chart && !elements.chart.closest('.payoff-tab-content')?.classList.contains('hidden');
                        if (!isChartVisible) return;
                        window.PayoffChartUtils.updateChart(
                            elements.chart,
                            legs,
                            spotPrice,
                            chainData,
                            symbol,
                            chartId,
                            showOI,
                            oiType,
                            selectedExpiries
                        );
                    }
                }
            });

            // If previously selected tab then select it
            if (window.lastSelectedPayoffTab) {
                elements.tabs.forEach(tab => {
                    if (tab.dataset.tab === window.lastSelectedPayoffTab) {
                        tab.click();
                    }
                });
            }

            // Populate expiry checkboxes if available
            if (elements.expiryCheckboxesContainer && expiryDates && expiryDates.length > 0) {
                elements.expiryCheckboxesContainer.innerHTML = '';

                // Sort expiry dates (assuming DD-MMM-YYYY format)
                const sortedExpiries = [...expiryDates].sort((a, b) => {
                    const dateA = new Date(a.split('-').reverse().join('-'));
                    const dateB = new Date(b.split('-').reverse().join('-'));
                    return dateA - dateB;
                });

                // Add checkboxes for each expiry
                sortedExpiries.forEach((expiry, index) => {
                    // Extract date info for display
                    const expiryDate = new Date(expiry.split('-').reverse().join('-'));
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);

                    const diffTime = expiryDate - today;
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                    if (diffDays < 0) {
                        return; // Skip past dates without adding to selectedExpiries
                    }

                    // Format day, month as needed
                    const day = expiryDate.getDate();
                    const month = expiryDate.toLocaleString('default', { month: 'short' });

                    // TODO: Logic for the asterisk sign will add here, later

                    // Create checkbox for this expiry
                    const checkboxId = `expiry-${index}`;
                    const checkbox = document.createElement('div');
                    checkbox.innerHTML = `
                        <label class="flex items-center">
                            <input type="checkbox" id="${checkboxId}" value="${expiry}" class="expiry-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                            <span class="ml-2 text-sm text-gray-700">
                                ${day} ${month} (${diffDays} Days)
                            </span>
                        </label>
                    `;
                    elements.expiryCheckboxesContainer.appendChild(checkbox);

                    // Add to selected expiries
                    selectedExpiries.push(expiry);
                });
            }

            // Setup OI options toggle
            if (elements.oiOptionsToggle && elements.oiOptionsPanel) {
                elements.oiOptionsToggle.addEventListener('click', () => {
                    elements.oiOptionsPanel.classList.toggle('hidden');
                });

                // Close panel when clicking outside
                document.addEventListener('click', (e) => {
                    if (!elements.oiOptionsToggle.contains(e.target) &&
                        !elements.oiOptionsPanel.contains(e.target)) {
                        elements.oiOptionsPanel.classList.add('hidden');
                    }
                });
            }

            // Setup OI toggle
            if (elements.showOiToggle) {
                elements.showOiToggle.onchange = () => {
                    showOI = elements.showOiToggle.checked;
                    window.PayoffChartUtils.updateChart(
                        elements.chart,
                        legs,
                        spotPrice,
                        chainData,
                        symbol,
                        chartId,
                        showOI,
                        oiType,
                        selectedExpiries
                    );
                };
            }

            // Setup OI type radios
            elements.oiTypeRadios.forEach(radio => {
                radio.onchange = () => {
                    if (radio.checked) {
                        oiType = radio.value;
                        window.PayoffChartUtils.updateChart(
                            elements.chart,
                            legs,
                            spotPrice,
                            chainData,
                            symbol,
                            chartId,
                            showOI,
                            oiType,
                            selectedExpiries
                        );
                    }
                };
            });

            // Setup expiry checkboxes
            container.querySelectorAll('.expiry-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    // Update selected expiries
                    selectedExpiries = Array.from(container.querySelectorAll('.expiry-checkbox:checked'))
                        .map(cb => cb.value);

                    window.PayoffChartUtils.updateChart(
                        elements.chart,
                        legs,
                        spotPrice,
                        chainData,
                        symbol,
                        chartId,
                        showOI,
                        oiType,
                        selectedExpiries
                    );
                });
            });

            // Setup share button
            if (showShare && elements.share) {
                elements.share.addEventListener('click', () => {
                    if (onShare) {
                        onShare();
                    } else {
                        this.handleShare(elements.share);
                    }
                });
            } else if (elements.share) {
                elements.share.classList.add('hidden');
            }

            // Setup AI Analysis button
            const aiAnalysisBtn = container.querySelector('.ai-analysis-btn');
            if (aiAnalysisBtn) {
                aiAnalysisBtn.addEventListener('click', () => {
                    this.getOptionsAiAnalysis(legs, window.payoffData, spotPrice, symbol);
                });
            }

            // Setup reset zoom button
            if (elements.resetZoom) {
                elements.resetZoom.addEventListener('click', () => window.PayoffChartUtils.resetZoom(chartId));
            }

            // Function to get current target days from expiry slider
            function getTargetExpiryDays() {
                if (elements.expirySlider && elements.expirySlider.noUiSlider) {
                    return parseInt(elements.expirySlider.noUiSlider.get());
                }
                return null;
            }

            // Function to get current target expiry from expiry slider
            function getTargetDatetime() {
                if (elements.expirySlider && elements.expirySlider.noUiSlider) {
                    const today = new Date();
                    // Get the value from the slider
                    const daysFromToday = parseInt(elements.expirySlider.noUiSlider.get(), 10);
                    const expiryDate = new Date(today);
                    expiryDate.setDate(today.getDate() + daysFromToday);
                    return expiryDate.toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short'
                    });
                }
                return null;
            }

            // Standard Deviation handling is now in StandardDeviationUtils.setupStandardDeviationEventHandlers

            // Function to recalculate payoff data (using self declared at the top of renderPayoffUI)
            function recalculatePayoff() {
                // Abort any ongoing calculation
                if (window.payoffAbortController) {
                    window.payoffAbortController.abort();
                }

                // Create a new abort controller
                window.payoffAbortController = new AbortController();

                const target_expiry_days = getTargetExpiryDays();
                const target_datetime = getTargetDatetime();

                // Get the current price from the slider or input
                let targetPrice = spotPrice; // Default to original spot price
                if (elements.priceInput && elements.priceInput.value) {
                    targetPrice = parseFloat(elements.priceInput.value);
                }

                // Get the target interval value
                const targetIntervalSelect = document.getElementById('target-interval-select');
                const targetInterval = targetIntervalSelect ? targetIntervalSelect.value : window.targetInterval || '50';

                self.calculatePayoffData(legs, targetPrice, symbol, multiplier, target_expiry_days, target_datetime, targetInterval, window.payoffAbortController)
                    .then(data => {
                        if (data?.error || data?.aborted) {
                            window.payoffTable = null;
                            window.payoffData = null;
                            console.error('Error or aborted', data);
                            return;
                        }

                        // Cache the payoff table for future updates
                        window.payoffTable = data["Payoff Table"];
                        window.payoffData = data;
                        // Render all components
                        self.renderPayoffInfo(elements.info, data, symbol, spotPrice, multiplier);

                        // Only update chart if the chart container is visible
                        const isChartVisible = elements.chart && !elements.chart.closest('.payoff-tab-content')?.classList.contains('hidden');
                        if (isChartVisible) {
                            window.PayoffChartUtils.updateChart(
                                elements.chart,
                                legs,
                                spotPrice,
                                chainData,
                                symbol,
                                chartId,
                                showOI,
                                oiType,
                                selectedExpiries
                            );
                        }
                        // Update P&L table with target price
                        self.renderPnLTable(legs, chainData, targetPrice, '.pnl-table-body', symbol, data.per_trade_details);

                        self.renderPayoffTable(elements.table, data["Payoff Table"], recalculatePayoff);
                        self.payoffTableEventHandler(elements.table, recalculatePayoff, self.renderPayoffTable);
                        // Render Greek table with the full trade details
                        window.GreeksUtils.renderGreeksTable(legs, chainData, '.greeks-table-body', symbol, multiplier, data.per_trade_details);
                        // Update the Greeks summary with the complete data
                        window.GreeksUtils.updateGreeksSummary(data);

                        // Update the Standard Deviation summary with the complete data
                        window.StandardDeviationUtils.updateStandardDeviationSummary(data);
                    })
                    .catch(error => {
                        // Don't log error if it was just aborted
                        if (error.name === 'AbortError') {
                            console.log('Payoff calculation aborted');
                            return;
                        }
                        console.error("Error calculating payoff:", error);
                        elements.info.innerHTML = `<div class="text-red-600">Error calculating payoff: ${error.message}</div>`;
                    });
            }

            // Initial calculation of payoff data
            recalculatePayoff();
            // Initial fetching for the strategy chart
            self.fetchStrategyData(symbol, legs);
        },

        calculatePayoffData: async function (legs, spotPrice, symbol, multiplier = 1, target_expiry_days = null, target_datetime = null, target_interval = null, abortController = null) {
            const enabledLegs = legs.filter(l => l.enabled);
            const globalLotSize = LotSizes[symbol] || 1;
            const currentDate = new Date();

            // Create a new abort controller if none is provided
            const controller = abortController || new AbortController();
            const signal = controller.signal;

            const legsWithExpiryDays = enabledLegs.map(leg => {
                // Preserve expiryDate if it exists; otherwise, use expiry.
                const expDate = leg.expiryDate;
                const expiryDateObj = new Date(expDate);
                const timeDiff = expiryDateObj - currentDate;
                const expiryDays = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
                return {
                    id: leg.id,
                    type: leg.type,
                    id: leg.id,
                    strike: leg.type !== 'FUT' ? parseFloat(leg.strike) : undefined,
                    op_pr: leg.type !== 'FUT' ? parseFloat(leg.price) : undefined,
                    entry_price: leg.type === 'FUT' ? parseFloat(leg.price) : undefined,
                    tr_type: leg.action === 'buy' ? 'b' : 's',
                    lots: parseInt(leg.lots) || 1,
                    lot_size: globalLotSize,
                    expiry_days: expiryDays > 0 ? expiryDays : 0,
                    iv: leg.iv,
                    expiryDate: expDate,
                    symbol
                };
            });

            // Calculate strikePricesDelta from chainData
            let strikePricesDelta = 0;
            if (window.payoffChainData && window.payoffChainData.length > 0) {
                // Get the first expiry date from the legs
                const firstExpiry = enabledLegs.length > 0 ? enabledLegs[0].expiryDate : null;

                if (firstExpiry) {
                    // Filter chain data for this expiry
                    const expiryOptions = window.payoffChainData.filter(opt => opt.expiryDate === firstExpiry);

                    if (expiryOptions.length > 1) {
                        // Sort by strike price
                        const sortedStrikes = expiryOptions.map(opt => opt.strikePrice).sort((a, b) => a - b);

                        // Find the middle of the strike prices
                        const middleIndex = Math.floor(sortedStrikes.length / 2);
                        strikePricesDelta = sortedStrikes[middleIndex] - sortedStrikes[middleIndex - 1];
                    }
                }
            }

            // Prepare request body
            const body = {
                legs: [...legsWithExpiryDays],
                spot_price: spotPrice,
                lot_size: globalLotSize,
                multiplier: multiplier,
                greeks_multiply_by_lot_size: window.greeksMultiplyByLotSize || false,
                greeks_multiply_by_number_of_lots: window.greeksMultiplyByNumberOfLots || false,
                per_trade_greeks_multiply_by_lot_size: window.perTradeGreeksMultiplyByLotSize || false,
                per_trade_greeks_multiply_by_number_of_lots: window.perTradeGreeksMultiplyByNumberOfLots || false,
                sd_mode: window.sdMode || 'fixed',
                sd_days: window.sdDays || 7,
                strikePricesDelta: strikePricesDelta
            };

            // Apply custom IVs to legs if available
            if (window.StrikewiseIvUtils) {
                // Modify legs with custom IVs
                body.legs = window.StrikewiseIvUtils.getCustomIVsForBackend(body.legs);
            }

            // Add target_expiry_days parameter if available
            if (target_expiry_days !== null) {
                body.target_expiry_days = target_expiry_days;

                // Calculate the target datetime based on current date and target days
                const targetDate = new Date(currentDate);
                targetDate.setDate(currentDate.getDate() + target_expiry_days);

                // Add the target_datetime in ISO format
                body.target_datetime = targetDate.toISOString();

                // Set target_interval from parameter if provided, otherwise null
                body.target_interval = target_interval;
            }

            try {
                // First fetch only payoff data
                const payoffResponse = await fetch("/opstrat/options/payoff", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify(body),
                    signal: signal // Use the abort signal
                });

                const payoffData = await payoffResponse.json();

                // Return payoff data immediately
                const result = { ...payoffData, margins: [] };

                // Calculate margins asynchronously
                this.calculateMargin(legsWithExpiryDays, multiplier, signal).then(marginData => {
                    if (marginData?.error) {
                        result.margins = { error: marginData.error };
                    } else {
                        result.margins = marginData || [];
                    }
                    this.updateMarginInfo(result);
                }).catch(error => {
                    // Check if this is an abort error
                    if (error.name === 'AbortError') {
                        console.log('Margin calculation aborted');
                    } else {
                        console.error("Error calculating margin:", error);
                        result.margins = { error: error.message };
                        this.updateMarginInfo(result);
                    }
                });

                return result;
            } catch (error) {
                // Check if this is an abort error
                if (error.name === 'AbortError') {
                    console.log('Payoff calculation aborted');
                    return { aborted: true };
                }
                console.error("Error calculating payoff:", error);
                return { error: error.message };
            }
        },

        calculateMargin: async function (legs, multiplier, signal = null) {
            try {
                const response = await fetch("/margins/compute", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ legs_json: legs, multiplier: multiplier }),
                    signal: signal // Use the abort signal if provided
                });
                const marginData = await response.json();

                // Check if there's an error in the response
                if (marginData?.error) {
                    throw new Error(marginData.error);
                }

                return marginData?.margins || [];
            } catch (error) {
                // Re-throw abort errors
                if (error.name === 'AbortError') {
                    throw error;
                }
                console.error("Margin calculation error:", error);
                // Return error information along with empty margins
                return { error: error.message, margins: [] };
            }
        },

        // New method to calculate bulk payoff data for backtesting
        calculatePayoffDataBulk: async function (legs, symbol, start, end, interval, multiplier = 1, abortController = null) {
            const enabledLegs = legs.filter(l => l.enabled);
            const globalLotSize = LotSizes[symbol] || 1;
            const currentDate = new Date();

            // Create a new abort controller if none is provided
            const controller = abortController || new AbortController();
            const signal = controller.signal;

            const legsWithExpiryDays = enabledLegs.map(leg => {
                // Preserve expiryDate if it exists; otherwise, use expiry.
                const expDate = leg.expiryDate;
                const expiryDateObj = new Date(expDate);
                const timeDiff = expiryDateObj - currentDate;
                const expiryDays = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
                return {
                    id: leg.id,
                    type: leg.type,
                    strike: leg.type !== 'FUT' ? parseFloat(leg.strike) : undefined,
                    op_pr: leg.type !== 'FUT' ? parseFloat(leg.price) : undefined,
                    entry_price: leg.type === 'FUT' ? parseFloat(leg.price) : undefined,
                    tr_type: leg.action === 'buy' ? 'b' : 's',
                    lots: parseInt(leg.lots) || 1,
                    expiry_days: expiryDays > 0 ? expiryDays : 0,
                    iv: leg.iv,
                    expiryDate: expDate
                };
            });

            // Calculate strikePricesDelta from chainData (same logic as in calculatePayoffData)
            let strikePricesDelta = 0;
            if (window.chainData && window.chainData.options && window.chainData.options.length > 0) {
                // Get the first expiry date from the legs
                const firstExpiry = enabledLegs.length > 0 ? enabledLegs[0].expiryDate : null;

                if (firstExpiry) {
                    // Filter chain data for this expiry
                    const expiryOptions = window.chainData.options.filter(opt => opt.expiryDate === firstExpiry);

                    if (expiryOptions.length > 1) {
                        // Sort by strike price
                        const sortedStrikes = expiryOptions.map(opt => opt.strikePrice).sort((a, b) => a - b);

                        // Calculate differences between consecutive strikes
                        const differences = [];
                        for (let i = 1; i < sortedStrikes.length; i++) {
                            const diff = sortedStrikes[i] - sortedStrikes[i - 1];
                            if (diff > 0) differences.push(diff);
                        }

                        if (differences.length > 0) {
                            // Find the most common difference (the step size)
                            const frequencyMap = {};
                            differences.forEach(diff => {
                                frequencyMap[diff] = (frequencyMap[diff] || 0) + 1;
                            });

                            let maxFreq = 0;
                            let mostCommonDiff = differences[0];

                            for (const diff in frequencyMap) {
                                if (frequencyMap[diff] > maxFreq) {
                                    maxFreq = frequencyMap[diff];
                                    mostCommonDiff = parseFloat(diff);
                                }
                            }

                            strikePricesDelta = mostCommonDiff;
                        }
                    }
                }
            }

            console.log("Bulk Strike Prices Delta:", strikePricesDelta);

            const body = {
                legs: legsWithExpiryDays,
                symbol: symbol,
                start: start,
                end: end,
                interval: interval,
                lot_size: globalLotSize,
                multiplier: multiplier,
                strikePricesDelta: strikePricesDelta
            };

            try {
                const response = await fetch("/opstrat/options/payoff/bulk", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify(body),
                    signal: signal // Use the abort signal
                });
                return await response.json();
            } catch (error) {
                // Check if this is an abort error
                if (error.name === 'AbortError') {
                    console.log('Bulk payoff calculation aborted');
                    return { aborted: true };
                }
                console.error("Bulk payoff calculation error:", error);
                return null;
            }
        },

        // Helper function to get the nearest expiry date from legs
        getNearestExpiry: function (legs) {
            if (!legs || !legs.length) return null;

            const now = new Date();
            // Set time to beginning of day for date comparison
            now.setHours(0, 0, 0, 0);

            const activeLegExpiries = legs
                .filter(leg => leg.enabled !== false)
                .map(leg => {
                    const expDate = new Date(leg.expiryDate);
                    expDate.setHours(0, 0, 0, 0); // Normalize time for comparison
                    return expDate;
                });

            if (!activeLegExpiries.length) return null;

            // Find expiry dates that are today or in the future
            const todayOrFutureExpiries = activeLegExpiries.filter(date => date >= now);

            if (todayOrFutureExpiries.length > 0) {
                // Sort and get the closest one (which could be today)
                return new Date(Math.min(...todayOrFutureExpiries));
            }

            // If no today or future expiries, return the most recent one
            return new Date(Math.max(...activeLegExpiries));
        },

        // Render payoff information in the container
        renderPayoffInfo: function (container, data, symbol, spotPrice, multiplier = 1) {
            if (!data || Object.keys(data).length === 0) {
                container.innerHTML = `
                    <div class="p-4 text-center text-neutral-600">
                        <p class="text-lg font-medium">No options selected.</p>
                        <p class="mt-2">Start adding option legs to view your payoff details.</p>
                    </div>`;
                return;
            }

            const effectiveValue = (multiplier || 1) * (LotSizes[symbol] || 1);

            // Initial HTML with loading state for margins
            container.innerHTML = `
                <div class="bg-gray-50 border grid md:grid-cols-3 grid-cols-1 rounded-md text-neutral-600 divide-x">
                    <!-- Column 1: Current Spot, Max Profit & Max Loss -->
                    <div class="p-3">
                        <div class="flex gap-1 items-center mb-2">
                            <p>Current Spot:</p>
                            <p class="text-neutral-950 font-medium">${spotPrice}</p>
                        </div>
                        <div class="flex gap-1 items-center mb-2">
                            <p>Max Profit:</p>
                            <p class="text-green-700 bg-green-50 p-1 font-medium">
                                ${(data["Max Profit"])} <span class="hidden" id="max-profit-percentage"></span>
                                <span id="max-profit-info" class="group relative inline-block ml-1 cursor-help hidden">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="hidden group-hover:block absolute z-10 bottom-full left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded w-48">
                                        % calculated as (Max Profit / Margin Required) × 100
                                    </span>
                                </span>
                            </p>
                        </div>
                        <div class="flex gap-1 items-center mb-2">
                            <p>Max Loss:</p>
                            <p class="text-red-700 bg-red-50 p-1 font-medium">
                                ${(data["Max Loss"])} <span class="hidden" id="max-loss-percentage"></span>
                                <span id="max-loss-info" class="group relative inline-block ml-1 cursor-help hidden">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="hidden group-hover:block absolute z-10 bottom-full left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded w-48">
                                        % calculated as (Max Loss / Margin Required) × 100
                                    </span>
                                </span>
                            </p>
                        </div>
                    </div>
                    <!-- Column 2: Breakeven, Risk-Reward, POP, Time Value -->
                    <div class="p-3">
                        <div class="flex gap-1 items-center mb-2">
                            <p>Breakeven:</p>
                            <p class="text-neutral-950 font-medium">${Array.isArray(data["Breakeven"]) && data["Breakeven"].length > 0
                    ? data["Breakeven"].map(price => price).join(", ")
                    : "N/A"
                }</p>
                        </div>
                        <div class="flex gap-1 items-center mb-2">
                            <p>Risk-Reward:</p>
                            <p class="text-neutral-950 font-medium">${data["Risk-Reward"]}</p>
                        </div>
                        <div class="flex gap-1 items-center mb-2">
                            <p>POP:</p>
                            <p class="text-neutral-950 font-medium">${data["POP"]}</p>
                        </div>
                        <div class="flex gap-1 items-center mb-2">
                            <p>Time Value:</p>
                            <p class="text-neutral-950 font-medium">${data["Time Value"]}</p>
                        </div>
                    </div>
                    <!-- Column 3: Intrinsic Value, Effective Lot Value, Net Premium -->
                    <div class="p-3">
                        <div class="flex gap-1 items-center mb-2">
                            <p>Intrinsic Value:</p>
                            <p class="text-neutral-950 font-medium">${data["Intrinsic Value"]}</p>
                        </div>
                        <div class="flex gap-1 items-center mb-2">
                            <p>Net Premium:</p>
                            <p class="text-neutral-950 font-medium">${data["Net Premium"]}</p>
                        </div>
                    </div>
                </div>
                <div id="payoff-margins-container" class="margins-container">
                    <div class="mt-3 bg-gray-50 border rounded-md text-neutral-600">
                        <div class="p-3">
                            <div class="flex items-center justify-center gap-2">
                                <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span class="font-medium text-sm">Calculating margins...</span>
                            </div>
                        </div>
                    </div>
                </div>`;
        },

        // Method to update just the margins section
        updateMarginInfo: function (data, container = null) {
            // Find or create margins container
            let marginsContainer = container
            if (!marginsContainer) {
                marginsContainer = document.getElementById("payoff-margins-container");
            }

            // First, hide the info icons by default
            const maxProfitInfoIcon = document.getElementById("max-profit-info");
            const maxLossInfoIcon = document.getElementById("max-loss-info");
            if (maxProfitInfoIcon) maxProfitInfoIcon.classList.add("hidden");
            if (maxLossInfoIcon) maxLossInfoIcon.classList.add("hidden");

            // Calculate profit/loss percentages based on margin
            let marginAppName = null;
            if (Array.isArray(data?.margins) && !data?.margins?.error) {
                const validMargins = data.margins.filter(item => item?.margin?.total > 0);
                if (validMargins.length > 0) {
                    // Find the margin with the minimum value
                    const minMarginObj = validMargins.reduce((min, current) =>
                        (current.margin.total < min.margin.total) ? current : min, validMargins[0]);

                    const minMargin = minMarginObj.margin.total;
                    marginAppName = minMarginObj.display_name;

                    if (minMargin && data["Max Profit"] && data["Max Loss"]) {
                        data["Max Profit %"] = ((data["Max Profit"] / minMargin) * 100).toFixed(2);
                        data["Max Loss %"] = ((data["Max Loss"] / minMargin) * 100).toFixed(2);

                        // Update DOM elements with calculated percentages
                        const maxProfitEl = document.getElementById("max-profit-percentage");
                        const maxLossEl = document.getElementById("max-loss-percentage");
                        if (maxProfitEl) maxProfitEl.textContent = `(${data["Max Profit %"]}%)`;
                        if (maxLossEl) maxLossEl.textContent = `(${data["Max Loss %"]}%)`;

                        // Show and update the info icons with app-specific information
                        if (maxProfitInfoIcon) {
                            maxProfitEl.classList.remove("hidden");
                            maxProfitInfoIcon.classList.remove("hidden");
                            const tooltip = maxProfitInfoIcon.querySelector(".group-hover\\:block");
                            if (tooltip) {
                                tooltip.textContent = `% calculated as (Max Profit / ${marginAppName} Margin) x 100`;
                            }
                        }
                        if (maxLossInfoIcon) {
                            maxLossEl.classList.remove("hidden");
                            maxLossInfoIcon.classList.remove("hidden");
                            const tooltip = maxLossInfoIcon.querySelector(".group-hover\\:block");
                            if (tooltip) {
                                tooltip.textContent = `% calculated as (Max Loss / ${marginAppName} Margin) x 100`;
                            }
                        }
                    }
                } else {
                    // No valid margins, clear the percentage displays
                    const maxProfitEl = document.getElementById("max-profit-percentage");
                    const maxLossEl = document.getElementById("max-loss-percentage");
                    if (maxProfitEl) maxProfitEl.textContent = '';
                    if (maxLossEl) maxLossEl.textContent = '';
                }
            }

            // Check if there's an error in the margins structure itself
            if (data?.margins?.error) {
                marginsContainer.innerHTML = `
                <div class="mt-3 bg-red-50 border border-red-200 rounded-md text-red-600">
                    <div class="p-3">
                        <div class="flex items-center justify-between">
                            <span class="font-medium text-sm">Margin Calculation Error</span>
                            <span class="text-sm">
                                ${data.margins.error.includes("Reauthorization") ?
                        `<a href="/apps" class="ml-auto font-medium hover:bg-blue-500 hover:text-white px-4 py-2 rounded-md text-sm transition-colors">
                                        Re-Authorize
                                </a>` : data.margins.error}
                            </span>
                        </div>
                    </div>
                </div>`;
                return;
            }

            // Process each broker's margin data
            let validMargins = [];
            let errorsHtml = '';

            // Process each broker app
            if (Array.isArray(data?.margins)) {
                data.margins.forEach(brokerMargin => {
                    if (brokerMargin.error) {
                        // Handle apps with errors
                        errorsHtml += `
                        <div class="mt-2 bg-red-50 border border-red-200 rounded-md text-red-600 p-2">
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-sm">${brokerMargin.display_name} Error</span>
                                <span class="text-sm">${brokerMargin.error}</span>
                            </div>
                        </div>`;
                    } else if (brokerMargin.margin && brokerMargin.margin.total > 0) {
                        // Add valid margins to the array
                        validMargins.push(brokerMargin);
                    }
                });
            }

            // Sort margins by total value (ascending)
            validMargins.sort((a, b) => a.margin.total - b.margin.total);

            // Build the HTML for the margins section
            let marginsHTML = '';

            // If we have valid margins, show them in a table
            if (validMargins.length > 0) {
                marginsHTML = `
                <div class="mt-3 bg-white border border-gray-200 rounded-lg shadow-sm">
                    <div class="p-4">
                        <h3 class="text-sm font-semibold text-gray-700 mb-3">Margin Required</h3>
                        <div class="space-y-3">
                            ${validMargins.map(brokerMargin => `
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-2">
                                        <span class="font-medium text-gray-600">${brokerMargin.display_name}</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <span class="text-gray-900 font-semibold">${Number(brokerMargin.margin.total).toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
                                    </div>
                                </div>
                            `).join('<div class="border-t border-gray-100 my-2"></div>')}
                            
                            ${errorsHtml ? `
                            <div class="border-t border-gray-200 pt-3 mt-2">
                                <h4 class="text-sm font-medium text-red-600 mb-2">Issues with Margin Calculation</h4>
                                <div class="space-y-2">
                                    ${errorsHtml}
                                </div>
                            </div>` : ''}
                        </div>
                    </div>
                </div>`;
            } else if (errorsHtml) {
                // If we have errors but no valid margins, show the errors
                marginsHTML = errorsHtml;
            } else {
                // If no margins data at all, show a message
                marginsHTML = `
                <div class="mt-3 bg-gray-50 border rounded-md text-neutral-600">
                    <div class="p-3">
                        <div class="flex items-center justify-center">
                            <span class="font-medium text-sm">No margin data available</span>
                        </div>
                    </div>
                </div>`;
            }

            marginsContainer.innerHTML = marginsHTML;
        },

        // Method to get AI analysis for options strategy
        getOptionsAiAnalysis: function (legs, payoffData, spotPrice, symbol) {
            if (!legs || legs.length === 0 || !payoffData) {
                showAlert("No options strategy data available for analysis");
                return;
            }

            try {
                // Filter enabled legs
                const enabledLegs = legs.filter(leg => leg.enabled !== false);

                // Extract strategy metrics from payoffData
                const metrics = {
                    max_profit: payoffData["Max Profit"],
                    max_loss: payoffData["Max Loss"],
                    risk_reward: payoffData["Risk-Reward"],
                    breakeven: Array.isArray(payoffData["Breakeven"]) ? payoffData["Breakeven"].join(", ") : payoffData["Breakeven"],
                    probability_of_profit: payoffData["POP"],
                    net_premium: payoffData["Net Premium"]
                };

                // Prepare the payload
                const payload = {
                    strategy: {
                        underlying_symbol: symbol,
                        underlying_price: spotPrice,
                        metrics: metrics,
                        legs: enabledLegs.map(leg => ({
                            type: leg.type,
                            strike: leg.strike,
                            price: leg.price,
                            action: leg.action,
                            lots: leg.lots,
                            expiry: leg.expiryDate
                        }))
                    }
                };

                // Call the AI analysis function
                getAIFeedback(payload, { analysisType: 'options' });
            } catch (error) {
                console.error("Error preparing options strategy for analysis:", error);
                showAlert("Error analyzing options strategy. Please try again.");
            }
        },

        // Function to fetch strategy chart data
        fetchStrategyData: async function (symbol, legs) {
            try {
                // Show loading state
                const loadingEl = document.getElementById('strategy-chart-loading');
                const errorEl = document.getElementById('strategy-chart-error');
                const emptyEl = document.getElementById('strategy-chart-empty');
                const chartContainer = document.getElementById('strategy-chart-container');

                if (loadingEl) loadingEl.classList.remove('hidden');
                if (errorEl) errorEl.classList.add('hidden');
                if (emptyEl) emptyEl.classList.add('hidden');
                if (chartContainer) chartContainer.classList.add('hidden');

                // Get current date and calculate start date (15 days ago)
                const endDate = new Date();
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - 15);

                // Format dates as DD-MM-YYYY for API
                const formatDate = (date) => {
                    const dd = String(date.getDate()).padStart(2, '0');
                    const mm = String(date.getMonth() + 1).padStart(2, '0');
                    const yyyy = date.getFullYear();
                    return `${dd}-${mm}-${yyyy}`;
                };

                const from = formatDate(startDate);
                const to = formatDate(endDate);

                // Get expiry from legs
                const expiry = this.getNearestExpiry(legs);
                if (!expiry) {
                    throw new Error('No valid expiry found in strategy');
                }

                // Format the expiry for API call (DD-MMM-YYYY)
                const expiryMonth = expiry.toLocaleString('en-US', { month: 'short' });
                const expiryFormatted = `${String(expiry.getDate()).padStart(2, '0')}-${expiryMonth}-${expiry.getFullYear()}`;

                // Construct API URL
                const url = `https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${symbol}?from=${from}&to=${to}`;

                // Fetch the data
                const response = await fetch(url);

                if (!response.ok) {
                    throw new Error(`API request failed with status: ${response.status}`);
                }

                const data = await response.json();

                // Process data
                return this.processStrategyData(data, legs, expiryFormatted);
            } catch (error) {
                console.error('Error fetching strategy data:', error);
                const errorEl = document.getElementById('strategy-chart-error');
                const loadingEl = document.getElementById('strategy-chart-loading');
                const emptyEl = document.getElementById('strategy-chart-empty');
                const chartContainer = document.getElementById('strategy-chart-container');

                if (errorEl) {
                    errorEl.classList.remove('hidden');
                    errorEl.querySelector('p').textContent = `Failed to load strategy data: ${error.message}`;
                }
                if (loadingEl) loadingEl.classList.add('hidden');
                if (emptyEl) emptyEl.classList.add('hidden');
                if (chartContainer) chartContainer.classList.add('hidden');

                return null;
            }
        },

        // Process the raw API data into a format for rendering the chart
        processStrategyData: function (apiData, legs, expiryFormatted) {
            if (!apiData || !apiData.records || !apiData.records.data || !apiData.records.data.length) {
                throw new Error('No data available for strategy chart');
            }

            // Extract data that matches the expiry date for our legs
            const strategyData = apiData.records.data.filter(item => {
                // Only include data with matching expiry date
                return item.expiryDate === expiryFormatted;
            });

            if (!strategyData.length) {
                throw new Error('No data available for strategy chart');
            }

            // Group by timestamp to ensure we have one data point per day
            const dataByTimestamp = {};

            strategyData.forEach(item => {
                const timestamp = item.timestamp;
                if (!dataByTimestamp[timestamp]) {
                    dataByTimestamp[timestamp] = {
                        timestamp,
                        strikes: {},
                        spotPrice: null
                    };
                }

                // Add strike price data
                dataByTimestamp[timestamp].strikes[item.strikePrice] = {
                    CE: item.CE || null,
                    PE: item.PE || null
                };

                // Get spot price (from CE or PE, whichever is available)
                if (!dataByTimestamp[timestamp].spotPrice) {
                    if (item.CE && item.CE.underlyingValue) {
                        dataByTimestamp[timestamp].spotPrice = item.CE.underlyingValue;
                    } else if (item.PE && item.PE.underlyingValue) {
                        dataByTimestamp[timestamp].spotPrice = item.PE.underlyingValue;
                    }
                }
            });

            // Calculate strategy value for each timestamp
            const result = {
                timestamps: [],
                strategyPrices: [],
                spotPrices: [],
            };

            // Sort timestamps chronologically
            const sortedTimestamps = Object.keys(dataByTimestamp).sort((a, b) => {
                return new Date(a) - new Date(b);
            });

            // Calculate strategy value for each timestamp
            sortedTimestamps.forEach(timestamp => {
                const dayData = dataByTimestamp[timestamp];
                let strategyValue = 0;

                // Calculate strategy value based on legs
                legs.forEach(leg => {
                    if (!leg.enabled) return;

                    const strike = parseInt(leg.strike);
                    const optionType = leg.type; // 'CE' or 'PE'
                    const action = leg.action; // 'buy' or 'sell'
                    const lots = parseInt(leg.lots) || 1;

                    // Skip if no data for this strike
                    if (!dayData.strikes[strike] || !dayData.strikes[strike][optionType]) return;

                    const optionData = dayData.strikes[strike][optionType];
                    if (!optionData || !optionData.lastPrice) return;

                    // Add or subtract based on action
                    if (action === 'buy') {
                        strategyValue -= optionData.lastPrice * lots;
                    } else {
                        strategyValue += optionData.lastPrice * lots;
                    }
                });

                // Add data to result
                result.timestamps.push(timestamp);
                result.strategyPrices.push(strategyValue);
                result.spotPrices.push(dayData.spotPrice);
            });

            document.getElementById('invert-price-of-strategy-chart').onchange = () => {
                this.renderStrategyChart(result);
            };

            this.renderStrategyChart(result);
            return result;
        },

        // Render the strategy chart with the processed data
        renderStrategyChart: function (data) {
            const invertPrice = document.getElementById('invert-price-of-strategy-chart').checked;
            // Hide loading and error states
            const loadingEl = document.getElementById('strategy-chart-loading');
            const errorEl = document.getElementById('strategy-chart-error');
            const emptyEl = document.getElementById('strategy-chart-empty');
            const chartContainer = document.getElementById('strategy-chart-container');

            if (loadingEl) loadingEl.classList.add('hidden');
            if (errorEl) errorEl.classList.add('hidden');

            // Check if we have data to display
            if (!data || !data.timestamps.length) {
                if (emptyEl) emptyEl.classList.remove('hidden');
                if (chartContainer) chartContainer.classList.add('hidden');
                return;
            }

            // Show chart container
            if (emptyEl) emptyEl.classList.add('hidden');
            if (chartContainer) chartContainer.classList.remove('hidden');

            // Get canvas context
            const ctx = document.getElementById('strategy-price-chart').getContext('2d');

            // Destroy existing chart if it exists
            if (window.strategyChartInstance) {
                window.strategyChartInstance.destroy();
            }

            // Format dates for display
            const formattedDates = data.timestamps.map(timestamp => {
                const date = new Date(timestamp);
                return date.toLocaleDateString();
            });

            // Invert strategy prices if invertPrice is checked
            const strategyPrices = invertPrice
                ? data.strategyPrices.map(price => -price)
                : data.strategyPrices;

            // Create the chart
            window.strategyChartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: formattedDates,
                    datasets: [
                        {
                            label: 'Strategy Price',
                            data: strategyPrices,
                            borderColor: '#3b82f6', // blue
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            tension: 0.1,
                            pointRadius: 3,
                            pointHoverRadius: 6,
                            fill: true
                        },
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        },
                        y: {
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Strategy Price'
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    }
                }
            });
        },

        resetZoom: function (chartId = 'payoffChartInstance') {
            window.PayoffChartUtils.resetZoom(chartId);
        },

        // Function to prepare OI data for chart - delegates to PayoffChartUtils
        getOIDataForChart: function (prices, legs, chainData, selectedExpiries = [], oiType = 'oi') {
            return window.PayoffChartUtils.getOIDataForChart(prices, legs, chainData, selectedExpiries, oiType);
        },

        // Render P&L table for the selected legs
        renderPayoffTable: function (container, payoffTable, recalculatePayoff) {
            if (!payoffTable?.length) {
                container.innerHTML = `<p class="text-center text-gray-500">No payoff data available.</p>`;
                return;
            }

            // Check if Target Day P&L or On Target Date exists in the data
            const hasTargetDayData = payoffTable.some(row => "Target Day P&L" in row) && !window.payoffBacktestingEnabled;

            // Get target date and expiry date from the current state
            let targetDateStr = "On Target Date";
            let expiryDateStr = "On Expiry";

            // For target date, get the date from the expiry slider
            const expirySlider = document.getElementById('expiry-slider');
            const expiryDateDisplay = document.getElementById('expiry-slider-date');

            if (expiryDateDisplay) {
                // Extract just the date part (remove day of week and time)
                const displayText = expiryDateDisplay.textContent;
                const dateParts = displayText.split(',');
                if (dateParts.length > 1) {
                    // Get just the date part (e.g., "13 Apr")
                    const dateOnly = dateParts[1].trim().split(' ').slice(0, 2).join(' ');
                    targetDateStr = `On Target Date: ${dateOnly}`;
                }
            }

            // For expiry date, try to get from the nearest expiry in legs
            const daysToExpiryDisplay = document.getElementById('expiry-slider-end');
            if (daysToExpiryDisplay) {
                expiryDateStr = `On Expiry: ${daysToExpiryDisplay.textContent}`;
            }

            // Get the current spot price to highlight the row
            const spotPrice = window.currentSpotPrice || 0;

            // Get the selected target interval
            const targetIntervalSelect = document.getElementById('target-interval-select');
            const targetInterval = targetIntervalSelect ? targetIntervalSelect.value : '50';

            // Check if we should show percentages
            const showPercentageToggle = document.getElementById('show-percentage');
            const showPercentage = showPercentageToggle ? showPercentageToggle.checked : true;

            // Use tabular_payoffs if available, otherwise use the regular payoff table
            const tabularPayoffs = window.payoffData?.tabular_payoffs || [];

            let html = `
            <div class="overflow-x-auto lg:max-h-[550px] overflow-y-auto border border-gray-200 rounded custom-scroll">
                <table class="min-w-full lg:text-sm text-xs text-left text-gray-700">
                    <thead class="sticky top-0 bg-gray-50 border-b border-gray-200">
                        <tr>
                            <th scope="col" class="px-6 py-2 text-xs font-semibold tracking-wider uppercase">
                                Target
                            </th>
                            ${hasTargetDayData ? `
                            <th scope="col" class="px-6 py-2 text-xs font-semibold tracking-wider uppercase">
                                ${targetDateStr}
                            </th>
                            ` : ''}
                            <th scope="col" class="px-6 py-2 text-xs font-semibold tracking-wider uppercase">
                                ${expiryDateStr}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white">`;

            // Find the center row (closest to current spot price)
            const centerTabularIndex = tabularPayoffs?.findIndex(row => row.percent_diff_from_current_price === 0);

            // Use tabular payoffs if available
            if (tabularPayoffs.length > 0) {

                tabularPayoffs.forEach((row, index) => {
                    // Calculate percentage difference from spot price
                    const percentDiff = row.percent_diff_from_current_price || ((row.at - spotPrice) / spotPrice * 100);
                    const formattedPercentDiff = percentDiff.toFixed(1);
                    const isCurrentRow = Math.abs(row.at - spotPrice) < 0.01;

                    // Get profit/loss values
                    const targetDayPnL = row.payoff_at_target;
                    const expiryPnL = row.payoff_at_expiry;

                    const isCenterRow = index === centerTabularIndex;

                    // Highlight the center row
                    const highlightClass = isCenterRow ? 'bg-yellow-50' : 'hover:bg-gray-50';
                    html += `
                    <tr class="${isCenterRow ? 'bg-yellow-50' : 'hover:bg-gray-50'}">
                        <td class="px-6 py-2 whitespace-nowrap">
                            ${Math.round(row.at)} ${showPercentage ? `<span class="text-gray-500">(${formattedPercentDiff}%)</span>` : ''}
                        </td>
                        ${hasTargetDayData && targetDayPnL !== undefined ? `
                        <td class="px-6 py-2 whitespace-nowrap ${parseFloat(targetDayPnL || 0) >= 0 ? 'text-green-600' : 'text-red-600'}">${Math.round(targetDayPnL || 0)}</td>
                        ` : ''}
                        <td class="px-6 py-2 whitespace-nowrap ${parseFloat(expiryPnL || 0) >= 0 ? 'text-green-600' : 'text-red-600'}">${Math.round(expiryPnL || 0)}</td>
                    </tr>`;
                });
            }

            html += `
                    </tbody>
                </table>
            </div>`;

            container.innerHTML = html;

            // Scroll to the centre
            const centerRow = document.querySelector('.bg-yellow-50');
            if (centerRow) {
                centerRow.scrollIntoView({ behavior: 'smooth' });
            }
        },

        payoffTableEventHandler: function (container, recalculatePayoff, renderPayoffTable) {
            // Event listener to the payoff table            // Setup target interval dropdown
            const targetIntervalButton = document.getElementById('target-interval-button');
            const targetIntervalPanel = document.getElementById('target-interval-panel');
            const targetIntervalDisplay = document.getElementById('target-interval-display');
            const intervalSelect = document.getElementById('target-interval-select');
            const intervalOptions = document.querySelectorAll('.interval-option');

            // Store event handler references in window object to be able to remove them later
            if (!window.payoffTableEventHandlers) {
                window.payoffTableEventHandlers = {
                    documentClickHandler: null
                };
            }

            // Document click handler for closing dropdown
            window.payoffTableEventHandlers.documentClickHandler = function (e) {
                if (targetIntervalButton && targetIntervalPanel &&
                    !targetIntervalButton.contains(e.target) &&
                    !targetIntervalPanel.contains(e.target)) {
                    targetIntervalPanel.classList.add('hidden');
                }
            };

            // Toggle dropdown when button is clicked
            if (targetIntervalButton && targetIntervalPanel) {
                targetIntervalButton.onclick = function () {
                    targetIntervalPanel.classList.toggle('hidden');
                }

                // Clean up document click handler
                if (window.payoffTableEventHandlers.documentClickHandler) {
                    document.removeEventListener('click', window.payoffTableEventHandlers.documentClickHandler, true);
                }

                // Close dropdown when clicking outside - use capture phase to ensure it runs before other handlers
                document.addEventListener('click', window.payoffTableEventHandlers.documentClickHandler, true);
            }

            // Handle interval option selection
            if (intervalOptions.length) {
                intervalOptions.forEach((option, index) => {
                    // Create a handler for each option
                    const handler = function () {
                        const value = this.getAttribute('data-value');

                        // Update the display
                        if (targetIntervalDisplay) {
                            targetIntervalDisplay.textContent = value;
                        }

                        // Update the hidden select
                        if (intervalSelect) {
                            intervalSelect.value = value;

                            // Store the value in the window object for persistence
                            window.targetInterval = value;

                            // Trigger change event on the select
                            const event = new Event('change');
                            intervalSelect.dispatchEvent(event);
                        }

                        // Update the active styling
                        intervalOptions.forEach(opt => {
                            opt.classList.remove('bg-blue-500', 'text-white');
                        });
                        this.classList.add('bg-blue-500', 'text-white');

                        // Hide the dropdown
                        if (targetIntervalPanel) {
                            targetIntervalPanel.classList.add('hidden');
                        }

                        // Recalculate payoff with the new target interval
                        recalculatePayoff();
                    };

                    // Add the event listener
                    option.onclick = handler;
                });
            }

            // Add event listener to the show percentage toggle
            const percentageToggle = document.getElementById('show-percentage');
            if (percentageToggle) {
                percentageToggle.onchange = function () {
                    // Just re-render the table with the current data
                    renderPayoffTable(container, window.payoffTable, recalculatePayoff);
                }
            }
        },

        renderPnLTable: function (legs, chainData, targetPrice, tableBodySelector = '.pnl-table-body', symbol, perTradeDetails = []) {
            const tableBody = document.querySelector(tableBodySelector);
            if (!tableBody) return;

            // Clear existing rows
            tableBody.innerHTML = '';

            // Skip rendering if backtesting is enabled
            if (window.payoffBacktestingEnabled) {
                return;
            }

            // Initialize totals
            let totalTargetPnL = 0;
            let totalEntryPrice = 0;
            let totalLTP = 0;
            let totalTargetTheoreticalPrice = 0;

            // Process each leg
            legs.forEach(leg => {
                if (!leg.enabled) return; // Skip disabled legs

                // Find corresponding trade details
                const tradeDetail = perTradeDetails.find(detail => {
                    const legId = leg.type === 'FUT' ?
                        `leg:${symbol}FUT` :
                        `leg:${symbol}${leg.expiryDate}${leg.strike.toFixed(1)}${leg.type}`;
                    return detail.tag === legId;
                });

                // Format instrument label
                let instrumentLabel = '';
                if (leg.type === 'FUT') {
                    instrumentLabel = `${leg.action === 'buy' ? 'B' : 'S'} ${symbol} Futures`;
                } else {
                    instrumentLabel = `${leg.action === 'buy' ? 'B' : 'S'} ${leg.lots} x ${symbol} ${leg.strike} ${leg.type}`;
                }

                // Get LTP from chain data
                let ltp = 0;
                if (leg.type === 'FUT') {
                    ltp = targetPrice; // For futures, LTP is the current spot/target price
                } else {
                    // Find the option in chain data
                    const option = chainData.find(opt =>
                        opt.strikePrice === leg.strike &&
                        opt.expiryDate === (leg.expiryDate)
                    );

                    if (option) {
                        const optData = leg.type === 'CE' ? option.CE : option.PE;
                        if (optData) {
                            ltp = optData.lastPrice || optData.ltp || 0;
                        }
                    }
                }

                // Get values from trade details
                const targetPnL = tradeDetail?.target_pnl;
                const targetTheoretical = tradeDetail?.price_at_target;
                const entryPrice = leg.op_pr || leg.entry_price || leg.price;

                // Update totals based on action (buy/sell)
                if (typeof targetPnL === 'number') {
                    totalTargetPnL += targetPnL;  // Target PnL already accounts for buy/sell
                }
                if (typeof entryPrice === 'number') {
                    // For sell orders, subtract from total
                    totalEntryPrice += (leg.action === 'buy' ? 1 : -1) * entryPrice * leg.lots;
                }
                if (typeof ltp === 'number') {
                    // For sell orders, subtract from total
                    totalLTP += (leg.action === 'buy' ? 1 : -1) * ltp * leg.lots;
                }
                if (typeof targetTheoretical === 'number') {
                    // For sell orders, subtract from total
                    totalTargetTheoreticalPrice += (leg.action === 'buy' ? 1 : -1) * targetTheoretical * leg.lots;
                }

                // Create row
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-4 py-2 whitespace-nowrap">
                        <div class="flex items-center">
                            <span class="inline-flex items-center justify-center h-6 w-6 rounded-full ${leg.action === 'buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} mr-2 font-medium text-xs">
                                ${leg.action === 'buy' ? 'B' : 'S'}
                            </span>
                            <span class="font-medium text-gray-900">${instrumentLabel}</span>
                        </div>
                    </td>
                    <td class="px-4 py-2 text-center ${typeof targetPnL === 'number' ? (targetPnL >= 0 ? 'text-green-600' : 'text-red-600') : ''}">${typeof targetPnL === 'number' ? targetPnL.toFixed(2) : 'NaN'}</td>
                    <td class="px-4 py-2 text-center">${typeof targetTheoretical === 'number' ? targetTheoretical.toFixed(2) : 'NaN'}</td>
                    <td class="px-4 py-2 text-center">${typeof entryPrice === 'number' ? entryPrice.toFixed(2) : 'NaN'}</td>
                    <td class="px-4 py-2 text-center">${typeof ltp === 'number' ? ltp.toFixed(2) : 'NaN'}</td>
                `;

                tableBody.appendChild(row);
            });

            // Update total row
            const totalPnLElement = document.getElementById('total-target-pnl');
            const totalTheoreticalPriceElement = document.getElementById('total-theoretical-price');
            const totalEntryPriceElement = document.getElementById('total-entry-price');
            const totalLTPElement = document.getElementById('total-ltp');

            if (totalPnLElement) {
                if (isNaN(totalTargetPnL)) {
                    totalPnLElement.innerHTML = 'NaN';
                } else {
                    totalPnLElement.innerHTML = `<span class="${totalTargetPnL >= 0 ? 'text-green-600' : 'text-red-600'}">${totalTargetPnL.toFixed(2)}</span>`;
                }
            }

            if (totalTheoreticalPriceElement) {
                totalTheoreticalPriceElement.innerHTML = totalTargetTheoreticalPrice.toFixed(2);
            }

            if (totalEntryPriceElement) {
                totalEntryPriceElement.innerHTML = totalEntryPrice.toFixed(2);
            }

            if (totalLTPElement) {
                totalLTPElement.innerHTML = totalLTP.toFixed(2);
            }
        },

        handleShare: async function (shareBtn) {
            if (!shareBtn) return;

            try {
                await navigator.clipboard.writeText(window.location.href);

                // Store original content and classes
                const originalContent = shareBtn.innerHTML;
                const originalClasses = shareBtn.className;

                // Update button to show success state
                shareBtn.innerHTML = `
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Copied!
                `;
                shareBtn.className = 'bg-green-600 text-white flex font-medium gap-1 hover:bg-green-700 items-center px-3 py-1 rounded-md text-sm transition-colors';

                // Reset button after 1 second
                setTimeout(() => {
                    shareBtn.innerHTML = originalContent;
                    shareBtn.className = originalClasses;
                }, 1000);
            } catch (err) {
                console.error("Failed to copy URL:", err);
                // Show error state
                shareBtn.innerHTML = `
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Error
                `;
                shareBtn.className = 'bg-red-600 text-white flex font-medium gap-1 hover:bg-red-700 items-center px-3 py-1 rounded-md text-sm transition-colors';

                setTimeout(() => {
                    shareBtn.innerHTML = originalContent;
                    shareBtn.className = originalClasses;
                }, 1000);
            }
        },
    }
</script>