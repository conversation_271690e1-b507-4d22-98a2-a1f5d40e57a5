<script>
    window.StrikewiseIvUtils = {
        templates: {
            strikewiseIvSummary: `
                <div class="bg-white rounded-lg md:p-5 p-3 mt-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-base font-semibold text-gray-800">Strikewise IVs</h3>
                        <button id="reset-ivs-btn" class="text-blue-600 hover:text-blue-800 text-sm font-medium">Reset IVs</button>
                    </div>
                    <div class="mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm text-gray-700">Offset</span>
                            <div class="flex items-center">
                                <button id="iv-offset-decrement" class="px-2 py-1 border border-gray-300 rounded-l-md bg-white text-gray-600 hover:bg-gray-100">−</button>
                                <input type="number" id="iv-offset-input" value="0" step="1" class="w-12 text-center border-y border-gray-300 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button id="iv-offset-increment" class="px-2 py-1 border border-gray-300 rounded-r-md bg-white text-gray-600 hover:bg-gray-100">+</button>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 text-sm">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Strike</th>
                                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Expiry</th>
                                    <th scope="col" class="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase">IV</th>
                                    <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Chg</th>
                                </tr>
                            </thead>
                            <tbody id="strikewise-iv-table-body" class="bg-white divide-y divide-gray-200">
                                <!-- Rows will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            `
        },

        // Initialize the Strikewise IV UI
        setupStrikewiseIvUI: function (container, legs, recalculatePayoff) {
            // Find the container where we'll add the Strikewise IV UI
            const strikewiseIvContainer = container.querySelector('#strikewise-iv-container');
            if (!strikewiseIvContainer) return;

            // Filter out futures and only include enabled options
            const optionLegs = legs.filter(leg => leg.type !== 'FUT' && leg.enabled);

            // Hide the container if there are no option legs to display
            if (optionLegs.length === 0) {
                strikewiseIvContainer.style.display = 'none';
                return;
            } else {
                strikewiseIvContainer.style.display = 'block';
            }

            // Render the template
            strikewiseIvContainer.innerHTML = this.templates.strikewiseIvSummary;

            // Get UI elements
            const tableBody = container.querySelector('#strikewise-iv-table-body');
            const offsetInput = container.querySelector('#iv-offset-input');
            const decrementBtn = container.querySelector('#iv-offset-decrement');
            const incrementBtn = container.querySelector('#iv-offset-increment');
            const resetBtn = container.querySelector('#reset-ivs-btn');

            // Initialize custom IV storage
            window.customIVs = {};

            // Populate the table with legs (excluding futures)
            this.populateIvTable(tableBody, legs);

            // Setup event handlers
            if (offsetInput) {
                offsetInput.onchange = () => {
                    const offset = parseFloat(offsetInput.value) || 0;
                    this.applyOffsetToAllIVs(offset, tableBody, legs);
                    recalculatePayoff();
                };
            }

            if (decrementBtn) {
                decrementBtn.onclick = () => {
                    const currentOffset = parseFloat(offsetInput.value) || 0;
                    offsetInput.value = (currentOffset - 1).toFixed(1);
                    this.applyOffsetToAllIVs(parseFloat(offsetInput.value), tableBody, legs);
                    recalculatePayoff();
                };
            }

            if (incrementBtn) {
                incrementBtn.onclick = () => {
                    const currentOffset = parseFloat(offsetInput.value) || 0;
                    offsetInput.value = (currentOffset + 1).toFixed(1);
                    this.applyOffsetToAllIVs(parseFloat(offsetInput.value), tableBody, legs);
                    recalculatePayoff();
                };
            }

            if (resetBtn) {
                resetBtn.onclick = () => {
                    offsetInput.value = '0';
                    window.customIVs = {};
                    this.populateIvTable(tableBody, legs);
                    recalculatePayoff();
                };
            }

            // Setup individual IV adjustment handlers
            this.setupIndividualIvHandlers(tableBody, legs, recalculatePayoff);
        },

        // Populate the IV table with legs data
        populateIvTable: function (tableBody, legs) {
            if (!tableBody) return;

            tableBody.innerHTML = '';

            // Filter out futures and only include options
            const optionLegs = legs.filter(leg => leg.type !== 'FUT' && leg.enabled);

            optionLegs.forEach(leg => {
                const row = document.createElement('tr');

                // Format expiry date for display
                const expiryDate = new Date(leg.expiryDate);
                const formattedExpiry = expiryDate.getDate() + ' ' +
                    expiryDate.toLocaleString('default', { month: 'short' });

                // Get the original IV and any custom IV that might have been set
                const originalIv = parseFloat(leg.iv) || 0;
                const legKey = `${leg.strike}-${leg.expiryDate}-${leg.type}`;
                const customIv = window.customIVs[legKey] !== undefined ? window.customIVs[legKey] : originalIv;
                const ivChange = customIv - originalIv;
                const ivChangeFormatted = ivChange.toFixed(1);
                const ivChangeClass = ivChange > 0 ? 'text-green-600' : (ivChange < 0 ? 'text-red-600' : 'text-gray-600');
                const ivChangePrefix = ivChange > 0 ? '+' : '';

                // Create a unique ID for each input and button
                const inputId = `iv-input-${leg.strike}-${leg.type}-${leg.expiryDate.replace(/[^a-zA-Z0-9]/g, '')}`;
                const decrementId = `iv-decrement-${inputId}`;
                const incrementId = `iv-increment-${inputId}`;

                row.innerHTML = `
                    <td class="px-4 py-2 whitespace-nowrap">
                        <div class="flex items-center">
                            <span class="text-sm text-gray-900">${leg.strike}</span>
                            <span class="ml-1 text-gray-500">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="10" stroke-width="2"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 9l-6 6m0-6l6 6"/>
                                </svg>
                            </span>
                        </div>
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${formattedExpiry}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-center">
                        <div class="flex items-center justify-center">
                            <button id="${decrementId}" class="iv-decrement p-[5px] border border-gray-300 rounded-l-md bg-white text-gray-600 hover:bg-gray-100 text-xs" data-leg-key="${legKey}">−</button>
                            <input id="${inputId}" type="text" class="iv-input w-12 text-center border-y border-gray-300 py-0.5 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent" value="${customIv.toFixed(1)}" data-original-iv="${originalIv}" data-leg-key="${legKey}">
                            <button id="${incrementId}" class="iv-increment p-[5px] border border-gray-300 rounded-r-md bg-white text-gray-600 hover:bg-gray-100 text-xs" data-leg-key="${legKey}">+</button>
                        </div>
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap text-right ${ivChangeClass}">(${ivChangePrefix}${ivChangeFormatted})</td>
                `;

                tableBody.appendChild(row);
            });
        },

        // Setup event handlers for individual IV adjustments
        setupIndividualIvHandlers: function (tableBody, legs, recalculatePayoff) {
            if (!tableBody) return;

            // Create a handler function for IV input changes
            const handleIvChange = (input) => {
                const legKey = input.getAttribute('data-leg-key');
                const originalIv = parseFloat(input.getAttribute('data-original-iv')) || 0;
                const newIv = parseFloat(input.value) || 0;

                // Update the custom IV
                window.customIVs[legKey] = newIv;

                // Update the change display
                const row = input.closest('tr');
                const changeCell = row.querySelector('td:last-child');
                const ivChange = newIv - originalIv;
                const ivChangeFormatted = ivChange.toFixed(1);
                const ivChangePrefix = ivChange > 0 ? '+' : '';

                changeCell.textContent = `(${ivChangePrefix}${ivChangeFormatted})`;
                changeCell.className = `px-4 py-2 whitespace-nowrap text-right ${ivChange > 0 ? 'text-green-600' : (ivChange < 0 ? 'text-red-600' : 'text-gray-600')}`;

                // Recalculate payoff with the new IVs
                recalculatePayoff();
            };

            // Create handlers for decrement and increment buttons
            const handleDecrement = (button) => {
                const legKey = button.getAttribute('data-leg-key');
                const input = tableBody.querySelector(`.iv-input[data-leg-key="${legKey}"]`);
                const currentIv = parseFloat(input.value) || 0;
                const newIv = Math.max(0, currentIv - 1).toFixed(1);

                input.value = newIv;
                handleIvChange(input);
            };

            const handleIncrement = (button) => {
                const legKey = button.getAttribute('data-leg-key');
                const input = tableBody.querySelector(`.iv-input[data-leg-key="${legKey}"]`);
                const currentIv = parseFloat(input.value) || 0;
                const newIv = (currentIv + 1).toFixed(1);

                input.value = newIv;
                handleIvChange(input);
            };

            // Apply handlers to all elements
            tableBody.querySelectorAll('.iv-input').forEach(input => {
                input.onchange = function () { handleIvChange(this); };
            });

            tableBody.querySelectorAll('.iv-decrement').forEach(button => {
                button.onclick = function () { handleDecrement(this); };
            });

            tableBody.querySelectorAll('.iv-increment').forEach(button => {
                button.onclick = function () { handleIncrement(this); };
            });
        },

        // Apply an offset to all IVs
        applyOffsetToAllIVs: function (offset, tableBody, legs) {
            if (!tableBody) return;

            tableBody.querySelectorAll('.iv-input').forEach(input => {
                const legKey = input.getAttribute('data-leg-key');
                const originalIv = parseFloat(input.getAttribute('data-original-iv')) || 0;
                const newIv = Math.max(0, originalIv + offset).toFixed(1);

                input.value = newIv;
                window.customIVs[legKey] = parseFloat(newIv);

                // Update the change display
                const row = input.closest('tr');
                const changeCell = row.querySelector('td:last-child');
                const ivChange = parseFloat(newIv) - originalIv;
                const ivChangeFormatted = ivChange.toFixed(1);
                const ivChangePrefix = ivChange > 0 ? '+' : '';

                changeCell.textContent = `(${ivChangePrefix}${ivChangeFormatted})`;
                changeCell.className = `px-4 py-2 whitespace-nowrap text-right ${ivChange > 0 ? 'text-green-600' : (ivChange < 0 ? 'text-red-600' : 'text-gray-600')}`;
            });
        },

        // Apply custom IVs directly to legs and return the modified legs
        getCustomIVsForBackend: function (legs) {
            if (!window.customIVs || Object.keys(window.customIVs).length === 0) return legs;

            // Create a deep copy of legs to avoid modifying the original array
            const modifiedLegs = JSON.parse(JSON.stringify(legs));

            // Apply custom IVs to the legs
            modifiedLegs.forEach(leg => {
                if (leg.type !== 'FUT') {
                    const legKey = `${leg.strike}-${leg.expiryDate}-${leg.type}`;
                    if (window.customIVs[legKey] !== undefined) {
                        // Store the original IV if not already stored
                        if (leg.originalIv === undefined) {
                            leg.originalIv = parseFloat(leg.iv) || 0;
                        }
                        // Update the IV with the custom value
                        leg.iv = window.customIVs[legKey];
                    }
                }
            });

            return modifiedLegs;
        }
    }
</script>