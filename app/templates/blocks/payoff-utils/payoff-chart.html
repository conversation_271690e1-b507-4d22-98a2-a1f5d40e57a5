<script>
    window.PayoffChartUtils = {
        // Format numbers with appropriate suffixes (K, M, Cr)
        formatNumber: function (value, decimals = 0) {
            if (value === null || value === undefined) return '-';

            value = parseFloat(value);

            if (Math.abs(value) >= 10000000) {
                return (value / 10000000).toFixed(decimals) + 'Cr';
            } else if (Math.abs(value) >= 100000) {
                return (value / 100000).toFixed(decimals) + 'L';
            }
            return value.toFixed(decimals);
        },

        // Format price based on axis range
        formatPrice: function (price, range) {
            if (range < 5) return price.toFixed(4);
            if (range < 20) return price.toFixed(3);
            if (range < 50) return price.toFixed(2);
            if (range < 200) return price.toFixed(1);
            return Math.round(price);
        },

        // Get color based on value (positive/negative)
        getValueColor: function (value, type = 'standard') {
            const colors = {
                standard: {
                    positive: 'rgb(40, 167, 69)',
                    negative: 'rgb(220, 53, 69)',
                    neutral: 'rgb(108, 117, 125)'
                },
                fill: {
                    positive: 'rgba(40, 167, 69, 0.1)',
                    negative: 'rgba(220, 53, 69, 0.1)',
                    neutral: 'rgba(108, 117, 125, 0.1)'
                },
                bar: {
                    call: 'rgba(40, 167, 69, 0.7)',
                    put: 'rgba(220, 53, 69, 0.7)'
                }
            };

            if (type === 'call') return colors.bar.call;
            if (type === 'put') return colors.bar.put;

            if (value > 0) return type === 'fill' ? colors.fill.positive : colors.standard.positive;
            if (value < 0) return type === 'fill' ? colors.fill.negative : colors.standard.negative;
            return type === 'fill' ? colors.fill.neutral : colors.standard.neutral;
        },

        // Function to prepare OI data for chart
        getOIDataForChart: function (prices, legs, chainData, selectedExpiries = [], oiType = 'oi') {
            if (!legs || !legs.length || !chainData || !chainData.length || !prices || !prices.length) {
                return null;
            }

            // If no expiries are selected or the array is empty, return null
            if (!selectedExpiries || selectedExpiries.length === 0) {
                return null;
            }

            // Initialize arrays for CE and PE OI data
            const ceData = Array(prices.length).fill(0);
            const peData = Array(prices.length).fill(0);

            // Process each selected expiry date
            selectedExpiries.forEach(expiry => {
                // Find all strikes in the chain data that match the expiry date
                const strikes = chainData
                    .filter(option => option.expiryDate === expiry)
                    .map(option => option.strikePrice);

                // Create a sorted set of unique strikes
                const uniqueStrikes = Array.from(new Set(strikes)).sort((a, b) => a - b);

                // Map strikes to price indexes
                uniqueStrikes.forEach(strike => {
                    // Find the closest price point
                    const closestPriceIndex = this.findClosestPriceIndex(strike, prices);
                    if (closestPriceIndex === -1) return;

                    // Find OI data for this strike
                    const optionData = chainData.find(option =>
                        option.expiryDate === expiry &&
                        option.strikePrice === strike
                    );

                    if (optionData) {
                        if (optionData.CE) {
                            // Get the right OI field based on the type
                            let ceOI = 0;
                            if (oiType === 'oi') {
                                ceOI = optionData.CE.openInterest || optionData.CE.oi || optionData.CE.open_interest || 0;
                            } else { // oi_change
                                ceOI = optionData.CE.changeinOpenInterest || optionData.CE.change_in_oi || 0;
                            }
                            ceData[closestPriceIndex] += ceOI;
                        }

                        if (optionData.PE) {
                            // Get the right OI field based on the type
                            let peOI = 0;
                            if (oiType === 'oi') {
                                peOI = optionData.PE.openInterest || optionData.PE.oi || optionData.PE.open_interest || 0;
                            } else { // oi_change
                                peOI = optionData.PE.changeinOpenInterest || optionData.PE.change_in_oi || 0;
                            }
                            peData[closestPriceIndex] += peOI;
                        }
                    }
                });
            });

            // Check if we have any non-zero values
            const hasData = ceData.some(val => val !== 0) || peData.some(val => val !== 0);

            return hasData ? { ceData, peData } : null;
        },

        // Helper function to find the closest price index
        findClosestPriceIndex: function (strike, prices) {
            if (!prices || !prices.length) return -1;

            let closestIndex = 0;
            let closestDiff = Math.abs(prices[0] - strike);

            for (let i = 1; i < prices.length; i++) {
                const diff = Math.abs(prices[i] - strike);
                if (diff < closestDiff) {
                    closestDiff = diff;
                    closestIndex = i;
                }
            }

            return closestIndex;
        },

        // Helper function to update the chart with current settings
        updateChart: function (chartElement, legs, spotPrice, chainData, symbol, chartId, showOI, oiType, selectedExpiries) {
            if (!window.payoffTable) {
                throw new Error('No payoff table found');
            }
            // Use the cached payoff table if available
            this.renderPayoffChart(
                chartElement,
                window.payoffTable,
                spotPrice,
                chartId,
                legs,
                chainData,
                symbol,
                showOI,
                oiType,
                selectedExpiries
            );
        },

        resetZoom: function (chartId = 'payoffChartInstance') {
            const chart = window[chartId];
            if (chart) {
                chart.resetZoom();
            }
        },

        renderPayoffChart: function (ctx, payoffTable, spotPrice, chartId = 'payoffChartInstance', legs = window.selectedLegs, chainData = window.chainData?.options, symbol = window.symbol, showOI = true, oiType = 'oi', selectedExpiries = []) {
            try {
                // Destroy existing chart if it exists
                if (window[chartId]) {
                    window[chartId].destroy();
                }

                if (!payoffTable?.length) return;

                // Extract data for better processing
                let prices = payoffTable.map(pt => parseFloat(pt["Spot Price"]));
                const payoffs = payoffTable.map(pt => parseFloat(pt["Profit/Loss"]));

                // Check if we have target date data available
                const hasTargetDateData = payoffTable.some(row => "Target Day P&L" in row);
                let targetDatePayoffs = [];

                if (hasTargetDateData) {
                    // Extract target date payoffs, handling both possible field names
                    targetDatePayoffs = payoffTable.map(pt => {
                        const targetDayValue = pt["Target Day P&L"]
                        return parseFloat(targetDayValue || 0);
                    });
                }

                // Calculate min and max for Profit/Loss, including target date values if available
                let minPayoff = Math.min(...payoffs);
                let maxPayoff = Math.max(...payoffs);

                if (hasTargetDateData) {
                    minPayoff = Math.min(minPayoff, ...targetDatePayoffs);
                    maxPayoff = Math.max(maxPayoff, ...targetDatePayoffs);
                }

                // Format spot price nicely for display
                const roundedSpotPrice = Math.round(parseFloat(spotPrice));

                // Get OI data for the appropriate expiry and type if showOI is enabled
                let oiData = null;
                if (showOI && legs && chainData) {
                    oiData = this.getOIDataForChart(prices, legs, chainData, selectedExpiries, oiType);
                }

                // Determine the overall range to align zero
                const maxAbsPayoff = Math.max(Math.abs(minPayoff), Math.abs(maxPayoff));
                const payoffRange = maxAbsPayoff * 1.1; // Add 10% padding

                // Calculate standard deviation lines
                const sdLines = [];

                // Get SD days based on mode (fixed or dynamic)
                let sdDays = 7; // Default
                if (window.sdMode === 'fixed') {
                    sdDays = window.sdDays || 7;
                } else if (window.sdMode === 'dynamic') {
                    // For dynamic mode, use target_expiry_days from the expiry slider
                    const expirySlider = document.querySelector('#expiry-slider');
                    if (expirySlider && expirySlider.noUiSlider) {
                        sdDays = parseInt(expirySlider.noUiSlider.get());
                    }
                }

                // Get standard deviation from backend data
                const sd = window.payoffData.standard_deviation ? window.payoffData.standard_deviation.standard_deviation : 0;

                // Create SD values array for charting
                const sdMultipliers = [-2, -1, 1, 2];
                sdMultipliers.forEach(multiplier => {
                    const sdPrice = spotPrice + (multiplier * sd);
                    sdLines.push({
                        value: sdPrice,
                        label: `${multiplier > 0 ? '+' : ''}${multiplier}SD`
                    });
                });

                // Create the chart configuration
                const chartConfig = {
                    type: "line",
                    data: {
                        labels: prices,
                        datasets: [
                            {
                                type: 'line',
                                label: "On Expiry",
                                data: payoffs,
                                pointRadius: 0,
                                pointHoverRadius: 5,
                                pointBackgroundColor: '#16a34a', // Green point
                                pointBorderColor: '#fff',
                                pointBorderWidth: 2,
                                pointHoverBorderWidth: 2,
                                pointHitRadius: 10,
                                borderWidth: 2,
                                borderColor: "#16a34a", // Green line for expiry
                                tension: 0.1,
                                fill: {
                                    target: { value: 0 },
                                    above: window.PayoffChartUtils.getValueColor(1, 'fill'),
                                    below: window.PayoffChartUtils.getValueColor(-1, 'fill'),
                                },
                                order: 2,
                            }
                        ],
                    },
                    options: {
                        scales: {
                            x: {
                                type: 'linear',
                                title: { display: true, text: "Price" },
                                grid: {
                                    display: false,
                                },
                                ticks: {
                                    callback: function (value) {
                                        const range = this.chart.scales.x.max - this.chart.scales.x.min;
                                        return window.PayoffChartUtils.formatPrice(value, range);
                                    }
                                },
                                min: prices[0],
                                max: prices[prices.length - 1],
                                offset: false,
                                alignToPixels: true
                            },
                            y: {
                                title: { display: true, text: "Profit/Loss" },
                                grid: {
                                    display: false,
                                },
                                ticks: {
                                    callback: function (value) {
                                        return window.PayoffChartUtils.formatNumber(value);
                                    }
                                },
                                min: -payoffRange,
                                max: payoffRange,
                                beginAtZero: false,
                                offset: false,
                                alignToPixels: true
                            },
                        },
                        plugins: {
                            annotation: {
                                annotations: {}
                            },
                            legend: {
                                display: hasTargetDateData,
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    pointStyle: 'line',
                                    boxWidth: 40,
                                    padding: 10
                                }
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                position: 'nearest',
                                callbacks: {
                                    title: function (tooltipItems) {
                                        return 'When price is at';
                                    },
                                    beforeBody: function (tooltipItems) {
                                        // Get the price for the first item (they all share the same index)
                                        const dataIndex = tooltipItems[0].dataIndex;
                                        // Ensure prices array is accessible here (it's defined in the outer scope)
                                        const price = prices[dataIndex];
                                        return [`${window.PayoffChartUtils.formatPrice(price, 5)}`]; // Return price as the first line in the body
                                    },
                                    label: function (context) {
                                        const dataIndex = context.dataIndex;
                                        let value, formattedValue, label;

                                        // Handle 'On Expiry' dataset
                                        if (context.dataset.label === "On Expiry") {
                                            // payoffs array is defined in the outer scope
                                            value = payoffs[dataIndex];
                                            formattedValue = window.PayoffChartUtils.formatNumber(value);
                                            label = `On Expiry: ${formattedValue}`;
                                            return label;
                                        }

                                        // Handle 'On Target Date' dataset
                                        if (context.dataset.label === "On Target Date") {
                                            value = context.raw;
                                            formattedValue = window.PayoffChartUtils.formatNumber(value);
                                            label = `On Target Date: ${formattedValue}`;
                                            return label;
                                        }

                                        // Handle OI datasets
                                        if (context.dataset.label.includes('Open Interest') || context.dataset.label.includes('Change in OI')) {
                                            value = context.raw;
                                            // If value is 0 or null, return an empty string
                                            if (value === 0 || value === null) {
                                                return '';
                                            }
                                            const sign = value >= 0 ? '+' : '';
                                            // Value is already adjusted for lot size in chart data preparation
                                            formattedValue = `${sign}${window.PayoffChartUtils.formatNumber(value)}`;
                                            label = `${context.dataset.label}: ${formattedValue}`;
                                            return label;
                                        }

                                        // Fallback for any other dataset
                                        return `${context.dataset.label}: ${window.PayoffChartUtils.formatNumber(context.raw)}`;
                                    },
                                    labelTextColor: function (context) {
                                        // For profit/loss values (On Expiry and On Target Date)
                                        if (context.dataset.label === "On Expiry") {
                                            const value = payoffs[context.dataIndex];
                                            return value >= 0 ? '#4caf50' : '#f44336'; // Green for profit, red for loss
                                        }
                                        if (context.dataset.label === "On Target Date") {
                                            const value = context.raw;
                                            return value >= 0 ? '#4caf50' : '#f44336'; // Green for profit, red for loss
                                        }
                                        // For OI datasets
                                        if (context.dataset.label.includes('Open Interest') || context.dataset.label.includes('Change in OI')) {
                                            return '#b39ddb'; // Purple color for OI
                                        }
                                        // Default
                                        return '#ffffff';
                                    }
                                },
                                backgroundColor: 'rgba(0, 0, 0, 0.85)', // Slightly darker background
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                borderColor: 'rgba(255, 255, 255, 0.2)', // Subtle white border
                                borderWidth: 1,
                                cornerRadius: 4, // Rounded corners
                                displayColors: false,
                                padding: 12, // Increased padding
                                caretPadding: 8, // Adjust caret padding
                                caretSize: 6, // Adjust caret size
                                titleSpacing: 6, // Space below title
                                bodySpacing: 4 // Space between body lines
                            },
                            zoom: {
                                zoom: {
                                    wheel: { enabled: false },
                                    pinch: { enabled: false },
                                    drag: {
                                        enabled: true,
                                        modifierKey: null, // No modifier key required
                                        borderColor: 'rgba(65, 105, 225, 0.3)', // Light blue border
                                        borderWidth: 1,
                                        backgroundColor: 'rgba(135, 206, 250, 0.2)', // Light blue background
                                        threshold: 10,
                                        mode: 'x' // Only allow horizontal zooming
                                    }
                                },
                                pan: {
                                    enabled: true,
                                    mode: 'x'
                                }
                            },
                        },
                        animation: { duration: 0 },
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'index',
                            intersect: false
                        },
                        // Ensure chart is properly sized and aligned
                        resizeDelay: 0,
                        devicePixelRatio: window.devicePixelRatio || 1
                    }
                };

                // Add target date dataset if we have that data
                if (hasTargetDateData) {
                    chartConfig.data.datasets.push({
                        type: 'line',
                        label: "On Target Date",
                        data: targetDatePayoffs,
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointBackgroundColor: '#3b82f6', // Blue point
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointHoverBorderWidth: 2,
                        pointHitRadius: 10,
                        borderWidth: 2,
                        borderColor: "#3b82f6", // Blue line
                        tension: 0.1,
                        fill: false,
                        order: 1 // Higher priority than regular payoff line
                    });

                    // Always enable the legend when we have the target date data
                    chartConfig.options.plugins.legend.display = true;
                }

                // Add vertical line annotation if spot price is valid
                if (roundedSpotPrice) {
                    chartConfig.options.plugins.annotation.annotations.verticalLine = {
                        type: 'line',
                        xMin: roundedSpotPrice,
                        xMax: roundedSpotPrice,
                        borderColor: 'red',
                        borderWidth: 1,
                        borderDash: [5, 5], // Makes the line dashed
                        z: 5,
                        label: {
                            display: true,
                            content: `Current Price: ${spotPrice}`,
                            position: 'end',
                            yAdjust: -5
                        }
                    };
                }

                // Add standard deviation vertical lines
                sdLines.forEach((line, index) => {
                    const lineColor = line.label.includes('-') ? 'rgba(239, 68, 68, 0.7)' : 'rgba(34, 197, 94, 0.7)';
                    chartConfig.options.plugins.annotation.annotations[`sdLine${index}`] = {
                        type: 'line',
                        xMin: line.value,
                        xMax: line.value,
                        borderColor: lineColor,
                        borderWidth: 1,
                        borderDash: [3, 3],
                        z: 4,
                        label: {
                            display: true,
                            content: line.label,
                            position: 'start',
                            yAdjust: -5,
                            backgroundColor: lineColor,
                            color: 'white',
                            padding: 4,
                            font: {
                                size: 10
                            }
                        }
                    };
                });

                // If we have OI data, add it as a bar chart
                if (oiData && oiData.ceData && oiData.peData) {
                    // Get lot size for the current symbol
                    const lotSize = LotSizes[symbol] || 1;

                    // Determine labels based on OI type
                    const oiLabel = oiType === 'oi' ? 'Open Interest' : 'Change in OI';
                    const ceLabel = `CE ${oiLabel}`;
                    const peLabel = `PE ${oiLabel}`;

                    // Calculate min and max OI values and keep references for later
                    const lotSizeMultiplier = lotSize;
                    let maxOI = 0;
                    let minOI = 0;

                    // Store these for easy access in the alignment function
                    window.chartOIData = {
                        type: oiType,
                        ceData: oiData.ceData.map(val => val * lotSizeMultiplier),
                        peData: oiData.peData.map(val => val * lotSizeMultiplier)
                    };

                    if (oiType === 'oi') {
                        // For regular OI, find max value (min is always 0 or positive)
                        const ceMax = Math.max(...oiData.ceData.filter(val => val !== null).map(val => val * lotSizeMultiplier));
                        const peMax = Math.max(...oiData.peData.filter(val => val !== null).map(val => val * lotSizeMultiplier));
                        maxOI = Math.max(ceMax, peMax);
                        minOI = 0; // OI is always positive
                    } else {
                        // For Change in OI, find max absolute value for symmetry
                        const ceValues = oiData.ceData.map(val => val * lotSizeMultiplier);
                        const peValues = oiData.peData.map(val => val * lotSizeMultiplier);
                        const ceMax = Math.max(...ceValues.map(val => Math.abs(val)));
                        const peMax = Math.max(...peValues.map(val => Math.abs(val)));
                        maxOI = Math.max(ceMax, peMax);
                        minOI = -maxOI; // Symmetric around zero
                    }

                    // Save these for use in alignment
                    window.originalOIRange = {
                        min: minOI,
                        max: maxOI * 1.1 // Add 10% padding
                    };

                    // Add datasets for OI
                    chartConfig.data.datasets.push({
                        type: 'bar',
                        label: ceLabel,
                        data: oiData.ceData.map(val => {
                            // For regular OI, use Math.max(0, val) to ensure non-negative values
                            // For Change in OI, allow negative values
                            return oiType === 'oi' ? Math.max(0, val * lotSizeMultiplier) : val * lotSizeMultiplier;
                        }),
                        backgroundColor: 'rgba(75, 192, 128, 0.5)',
                        borderColor: 'rgba(75, 192, 128, 0.8)',
                        borderWidth: 1,
                        yAxisID: 'y-oi',
                        order: 3,
                        barPercentage: 1,
                        categoryPercentage: 1,
                        grouped: false
                    });

                    chartConfig.data.datasets.push({
                        type: 'bar',
                        label: peLabel,
                        data: oiData.peData.map(val => {
                            // For regular OI, use Math.max(0, val) to ensure non-negative values
                            // For Change in OI, allow negative values
                            return oiType === 'oi' ? Math.max(0, val * lotSizeMultiplier) : val * lotSizeMultiplier;
                        }),
                        backgroundColor: window.PayoffChartUtils.getValueColor(-1, 'put'),
                        borderColor: window.PayoffChartUtils.getValueColor(-1, 'standard'),
                        borderWidth: 1,
                        yAxisID: 'y-oi',
                        order: 4,
                        barPercentage: 1,
                        categoryPercentage: 1,
                        grouped: false
                    });

                    // Configure the OI axis
                    chartConfig.options.scales['y-oi'] = {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: oiType === 'oi' ? 'Open Interest' : 'Change in OI'
                        },
                        grid: {
                            drawOnChartArea: false
                        },
                        ticks: {
                            precision: 0,
                            callback: function (value) {
                                return window.PayoffChartUtils.formatNumber(value);
                            }
                        },
                        offset: false,
                        alignToPixels: true
                    };

                    // Set initial scales based on OI type
                    if (oiType === 'oi') {
                        // For regular OI, scale from 0 to maxOI
                        chartConfig.options.scales['y-oi'].min = 0;
                        chartConfig.options.scales['y-oi'].max = maxOI * 1.1;
                    } else {
                        // For Change in OI, ensure symmetric range around zero
                        chartConfig.options.scales['y-oi'].min = -maxOI * 1.1;
                        chartConfig.options.scales['y-oi'].max = maxOI * 1.1;
                    }

                    // Save the initial P&L range for alignment calculations
                    window.originalPNLRange = {
                        min: -payoffRange,
                        max: payoffRange
                    };

                    chartConfig.options.plugins.legend.display = true;
                }

                // Create the chart and store it in the window object with the specified ID
                window[chartId] = new Chart(ctx, chartConfig);

                // Ensure axes stay properly aligned during zoom/pan operations
                if (oiData && oiData.ceData && oiData.peData) {
                    const chart = window[chartId];

                    // Function to align axes based on the current P&L axis state
                    const alignAxes = function () {
                        try {
                            const mainAxis = chart.scales.y;
                            const syncAxis = chart.scales['y-oi'];

                            if (!mainAxis || !syncAxis) return;

                            // Get the original ranges saved during initialization
                            const originalPNLRange = window.originalPNLRange || { min: -payoffRange, max: payoffRange };
                            const originalOIRange = window.originalOIRange || { min: 0, max: maxOI * 1.1 };
                            const oiType = window.chartOIData?.type || 'oi';

                            // Calculate the current P&L axis range
                            const currentPNLRange = mainAxis.max - mainAxis.min;

                            // Calculate relative zero position within the current view
                            // This handles cases where zero might be out of view
                            const zeroPosition = -mainAxis.min / currentPNLRange;

                            // Calculate scale factor based on the ratio of current range to original range
                            const scaleFactor = currentPNLRange / (originalPNLRange.max - originalPNLRange.min);

                            if (oiType === 'oi') {
                                // For regular OI, align zero position with P&L axis
                                // Get original max OI (with padding)
                                const origMaxOI = originalOIRange.max;

                                // Scale the max OI value based on the P&L axis scaling
                                const newMaxOI = origMaxOI * scaleFactor;

                                // Position the axis with zero at the same relative position
                                syncAxis.options.max = newMaxOI;

                                // If zeroPosition is between 0 and 1, we need to offset the minimum
                                // to ensure the zero lines align at the correct position
                                if (zeroPosition > 0 && zeroPosition < 1) {
                                    // Calculate where zero should be
                                    syncAxis.options.min = -(newMaxOI * zeroPosition / (1 - zeroPosition));
                                } else {
                                    // If zero is outside the visible range, just use 0
                                    syncAxis.options.min = 0;
                                }
                            } else {
                                // For Change in OI, maintain symmetry around zero
                                // First we need to determine where the zero line is in the P&L chart

                                // Calculate the range of visible values on the P&L axis
                                const visiblePNLRange = {
                                    min: mainAxis.min,
                                    max: mainAxis.max
                                };

                                // Calculate appropriate scale for OI Change
                                // The key is to ensure zero aligns on both axes
                                const maxAbsOI = originalOIRange.max * scaleFactor;

                                // If zero is within view
                                if (visiblePNLRange.min <= 0 && visiblePNLRange.max >= 0) {
                                    // Calculate the ratio of the positive and negative areas
                                    const negativeRatio = Math.abs(visiblePNLRange.min) / currentPNLRange;
                                    const positiveRatio = visiblePNLRange.max / currentPNLRange;

                                    // Scale OI axis proportionally to maintain zero alignment
                                    syncAxis.options.min = -maxAbsOI * (negativeRatio / Math.max(negativeRatio, positiveRatio));
                                    syncAxis.options.max = maxAbsOI * (positiveRatio / Math.max(negativeRatio, positiveRatio));
                                } else if (visiblePNLRange.max < 0) {
                                    // Entire view is in negative territory
                                    syncAxis.options.min = -maxAbsOI;
                                    syncAxis.options.max = 0;
                                } else if (visiblePNLRange.min > 0) {
                                    // Entire view is in positive territory
                                    syncAxis.options.min = 0;
                                    syncAxis.options.max = maxAbsOI;
                                } else {
                                    // Default to symmetric around zero
                                    syncAxis.options.min = -maxAbsOI;
                                    syncAxis.options.max = maxAbsOI;
                                }
                            }

                            // Update the chart without animation
                            chart.update('none');
                        } catch (error) {
                            console.error('Error adjusting chart axes:', error);
                        }
                    };

                    // Add direct event listeners for zoom and pan events
                    chart.canvas.addEventListener('mouseup', function () {
                        // This will catch both zoom end and pan end events
                        setTimeout(alignAxes, 50); // Small delay to ensure the zoom/pan operation is complete
                    });

                    // Also handle reset zoom button clicks
                    const resetZoomBtn = document.querySelector('.reset-zoom');
                    if (resetZoomBtn) {
                        resetZoomBtn.addEventListener('click', function () {
                            // When reset zoom is clicked, first reset the chart zoom
                            chart.resetZoom();

                            // Then manually run the alignment algorithm after a short delay
                            setTimeout(() => {
                                alignAxes();
                            }, 0);
                        });
                    }

                    // Initial alignment to ensure consistent starting state
                    setTimeout(alignAxes, 100);
                }
            } catch (error) {
                console.error('Error rendering payoff chart:', error);
                // Clear any existing chart
                if (window[chartId]) {
                    try {
                        window[chartId].destroy();
                    } catch (e) {
                        console.error('Error destroying existing chart:', e);
                    }
                }

                // Provide a fallback or error message
                if (ctx && ctx.canvas) {
                    try {
                        const context = ctx.canvas.getContext('2d');
                        if (context) {
                            context.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
                            context.font = '14px Arial';
                            context.fillStyle = 'red';
                            context.textAlign = 'center';
                            context.fillText('Error creating chart. Please try again.', ctx.canvas.width / 2, ctx.canvas.height / 2);
                        }
                    } catch (e) {
                        console.error('Error displaying fallback message:', e);
                    }
                }
            }
        },
    }
</script>