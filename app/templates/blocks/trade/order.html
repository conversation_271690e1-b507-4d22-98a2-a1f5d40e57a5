<script>
    window.TradeUtils = {
        // Templates (from templates.html)
        templates: {},

        // State (from state.html)
        state: {
            authenticatedApps: [],
            activeTabUuid: null,
            isModalOpen: false,
            optionChainData: null,
            selectedLegs: [],
            marginData: null,
            spotPrice: 0,
            symbol: '',
            multiplier: 1,
            lotSize: null,
            orderDuration: 'intraday', // Default order duration
            eventListeners: {}, // Store event listener references
            currentAbortController: null, // Controller for fetch requests within the modal session
            marginAbortController: null, // Separate controller for margin calculations
            marginDebounceTimer: null, // Timer for debouncing margin calculations
        },

        // Core functions (from core_functions.html)
        saveActiveTabPreference: function () { },
        loadActiveTabPreference: function () { },
        openTradingPanel: function () { },
        updateTradingPanelHeader: function () { },
        closeModal: function () { },
        getAuthenticatedApps: function () { },

        // Order management (from order_management.html)
        createLegTradingPanels: function () { },
        setupOrderPanelListeners: function () { },
        placeTrade: function () { },
        placeAllTrades: function () { },
        getOrderDetailsFromPanel: function () { },
        isAfterMarketHours: function () { },
        sendOrderRequest: function () { },
        fetchAndDisplayOrderStatus: function () { },

        // UI management (from ui_management.html)
        setupOrderDurationRadios: function () { },
        buildAuthenticatedAppsTabs: function () { },
        switchAuthenticatedAppsTab: function () { },
        setupFooterUI: function () { },
        showMarginInfoModal: function () { },
        showChargesModal: function () { },
        rearrangeOrders: function () { },
        setButtonLoading: function () { },
        setPlaceAllButtonLoading: function () { },
        updatePlaceAllButtonState: function () { },
        disablePlaceAllButton: function () { },
        disableLegPanel: function () { },

        // Market data (from market_data.html)
        fetchMarketDataForLegs: function () { },
        resetAllPanelsLoadingState: function () { },
        updatePanelWithMarketData: function () { },

        // Margin utilities (from margin_utils.html)
        calculateMargins: function () { },
        updateMarginUI: function () { },
        debouncedMarginCalculation: function () { },
        resetMarginDisplay: function () { }
    };
</script>

<!-- Include the components -->
{% include 'blocks/trade/components/templates.html' %}
{% include 'blocks/trade/components/core-functions.html' %}
{% include 'blocks/trade/components/order-management.html' %}
{% include 'blocks/trade/components/ui-management.html' %}
{% include 'blocks/trade/components/market-data.html' %}
{% include 'blocks/trade/components/margin-utils.html' %}