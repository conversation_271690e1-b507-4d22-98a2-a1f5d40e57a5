<script>
    // Core trading panel functionality

    // Handle saving and loading user preferences
    window.TradeUtils.saveActiveTabPreference = function () {
        // Save the current active tab UUID to localStorage
        if (this.state.activeTabUuid) {
            localStorage.setItem('tradePanel_activeTab', this.state.activeTabUuid);
        }
    };

    window.TradeUtils.loadActiveTabPreference = function () {
        // Load the previously selected tab UUID from localStorage
        return localStorage.getItem('tradePanel_activeTab');
    };

    // Main function to open the trading panel
    window.TradeUtils.openTradingPanel = async function (marketData = {}) {
        // Abort any lingering controller from a previous session
        this.state.currentAbortController?.abort("New trading panel opened");
        this.state.marginAbortController?.abort("New trading panel opened");

        // Create a new controller for this modal session
        const controller = new AbortController();
        this.state.currentAbortController = controller;
        this.state.marginAbortController = null;
        const signal = controller.signal;

        // Extract and store market data
        this.state.symbol = marketData.symbol || window.currentSymbol || 'NIFTY';
        this.state.spotPrice = marketData.spotPrice || window.currentSpotPrice || 0;
        this.state.multiplier = marketData.multiplier || 1;
        this.state.lotSize = marketData.lotSize || LotSizes ? LotSizes[this.state.symbol] : null;
        this.state.selectedLegs = marketData.legs.filter(leg => leg.enabled) || [];
        this.state.optionChainData = marketData.optionChainData || null;

        // Add the modal to the DOM first
        const modalContainer = document.createElement('div');
        modalContainer.id = 'trading-modal-container';
        modalContainer.innerHTML = this.templates.tradingPanel;
        document.body.appendChild(modalContainer);

        // Set up event listeners - use arrow function to preserve 'this' context
        document.getElementById('close-trading-modal').onclick = () => {
            this.closeModal();
        };

        // Add event listener for Update Prices button
        const updatePricesBtn = document.querySelector('button.text-sm.border.border-gray-300.rounded.py-1.px-3.bg-blue-50.text-blue-600');
        if (updatePricesBtn) {
            updatePricesBtn.onclick = () => {
                // First update all panels with latest LTP values
                this.updateLTPForAllPanels();
                // Then calculate margins
                this.calculateMargins();
            };
        }

        // Update the header with market data
        this.updateTradingPanelHeader();

        // Set isModalOpen to true
        this.state.isModalOpen = true;

        // Show loader while fetching apps
        const tabsContent = document.getElementById('trading-tabs-content');
        tabsContent.innerHTML = this.templates.loader;

        // Fetch authenticated apps
        try {
            // Pass the signal to getAuthenticatedApps
            await this.getAuthenticatedApps(signal);

            // Check if the signal was aborted (e.g., modal closed quickly)
            if (signal.aborted) {
                console.log("Authenticated apps fetch aborted.");
                return; // Stop processing if aborted
            }

            // Check if there are any authenticated apps
            if (!this.state.authenticatedApps || this.state.authenticatedApps.length === 0) {
                // Show no apps message
                tabsContent.innerHTML = this.templates.noApps;
                document.getElementById('close-no-apps-btn').onclick = () => this.closeModal();
            } else {
                // Check for previously saved tab preference
                const savedTabUuid = this.loadActiveTabPreference();

                // See if the saved tab UUID is in the current authenticated apps
                const isSavedTabAvailable = savedTabUuid && this.state.authenticatedApps.some(app => app.uuid === savedTabUuid);

                // If we have a saved tab and it's available, set it as active else set the first app as active
                if (isSavedTabAvailable) {
                    this.state.activeTabUuid = savedTabUuid;
                } else {
                    this.state.activeTabUuid = this.state.authenticatedApps[0].uuid;
                }

                // Set up the tabs
                this.buildAuthenticatedAppsTabs();

                // Set up intraday/overnight radio buttons
                this.setupOrderDurationRadios();

                // Set up footer UI
                this.setupFooterUI();
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('Fetch aborted in openTradingPanel:', error.message);
            } else {
                console.error("Error loading authenticated apps:", error);
                tabsContent.innerHTML = `
                        <div class="flex flex-col items-center justify-center p-8">
                            <div class="w-16 h-16 text-red-300 mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <p class="text-gray-600 mb-2">Error loading trading apps</p>
                            <p class="text-sm text-gray-500">${error.message || "Please try again later."}</p>
                            <button class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm" id="close-error-btn">Close</button>
                        </div>
                    `;
                document.getElementById('close-error-btn').addEventListener('click', this.closeModal);
            }
        }
    },

        // Update the trading panel header with market data
        window.TradeUtils.updateTradingPanelHeader = function () {
            // Update symbol and price in the header
            const symbolEl = document.getElementById('trading-panel-symbol');
            const priceEl = document.getElementById('trading-panel-price');

            if (symbolEl) symbolEl.textContent = this.state.symbol;

            if (priceEl) {
                priceEl.textContent = this.state.spotPrice.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            }
        };

    // Close the trading panel and clean up resources
    window.TradeUtils.closeModal = function () {
        // Abort any ongoing fetch requests initiated by this modal instance
        this.state.currentAbortController?.abort('Modal closed by user');
        this.state.currentAbortController = null; // Clear the controller

        // Also abort any margin calculations in progress
        this.state.marginAbortController?.abort('Modal closed by user');
        this.state.marginAbortController = null; // Clear the margin controller

        const modalContainer = document.getElementById('trading-modal-container');
        if (modalContainer) {
            document.body.removeChild(modalContainer);
        }


        // Fully clear all market data subscriptions when modal is closing
        this.unsubscribeAllMarketData(true);

        // Clear the event listeners object
        this.state.eventListeners = {};

        window.TradeUtils.state.isModalOpen = false;
    };

    // Get authenticated apps from the server
    window.TradeUtils.getAuthenticatedApps = async function (signal) {
        try {
            const response = await fetch(`/apps/authenticated-apps`, { signal });
            // Check if aborted after fetch started but before response completed
            if (signal?.aborted) {
                console.log("getAuthenticatedApps fetch aborted after starting.");
                throw new DOMException('Aborted', 'AbortError'); // Simulate AbortError
            }
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            const apps = userData.getItem("apps");
            this.state.authenticatedApps = apps.filter(app => data.apps.includes(app.uuid));
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('Fetch aborted in getAuthenticatedApps:', error.message);
                // Re-throw the error so the caller (openTradingPanel) can handle it
                throw error;
            } else {
                this.state.authenticatedApps = [];
                console.error("Error fetching authenticated apps:", error);
                // Optionally re-throw or handle differently
                throw error; // Re-throw to be caught by openTradingPanel
            }
        }
    };
</script>