<script>
    // Order Management Functions for the trading panel

    // Create trading panels for legs
    window.TradeUtils.createLegTradingPanels = function (legs) {
        if (!legs || legs.length === 0) return 'No legs found.';

        let html = `
        <div class="grid grid-cols-1 gap-4 p-4 max-h-[60vh] overflow-y-auto custom-scroll">
        `;

        legs.forEach((leg, index) => {
            if (leg.enabled) {
                const actionLabel = leg.action === 'buy' ? 'BUY' : 'SELL';
                const orderColor = leg.action === 'buy' ? 'bg-blue-50 text-blue-800' : 'bg-red-50 text-red-800';
                const buttonColor = leg.action === 'buy' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-red-500 hover:bg-red-600';

                const isFut = leg.type === 'FUT';

                // Calculate the quantity based on lots, multiplier and lot size
                const lotSize = this.state.lotSize || 1;
                const multiplier = this.state.multiplier || 1;
                const quantity = (leg.lots || 1) * multiplier * lotSize;

                html += `
                <div class="leg-panel order-panel" data-leg-id="${leg.id}">
                    <div class="rounded-lg border overflow-hidden">
                        <!-- Order Header -->
                        <div class="flex items-center ${orderColor} px-3 py-2">
                            <div class="text-sm font-medium mr-3">ORDER ${index + 1}</div>
                            <div class="text-xs font-medium px-2 py-0.5 rounded ${leg.action === 'buy' ? 'bg-blue-600 text-white' : 'bg-red-500 text-white'}">${actionLabel}</div>
                            <div class="ml-3 text-sm font-medium flex-1">
                                ${this.state.symbol} ${leg.type} ${leg.type === 'FUT' ? '' : leg.strike} ${leg.expiryDate}
                            </div>
                        </div>
                        
                        <!-- Market Data and Order Book Side by Side -->
                        <div class="flex border-t">
                            <!-- Left Side - Order Book -->
                            <div class="w-1/2 p-3 bg-white border-r">
                                <div class="market-data-order-book">
                                    <div class="order-book-wrapper">
                                        <div class="order-book-table-container">
                                            <table class="order-book-table w-full text-xs">
                                                <thead>
                                                    <tr class="bg-gray-100 text-gray-700 border-b border-gray-200">
                                                        <th class="py-1 px-2 text-center">BIDS</th>
                                                        <th class="py-1 px-2 text-center">ORDERS</th>
                                                        <th class="py-1 px-2 text-center">QTY</th>
                                                        <th class="py-1 px-2 text-center">QTY</th>
                                                        <th class="py-1 px-2 text-center">ORDERS</th>
                                                        <th class="py-1 px-2 text-center">OFFERS</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="order-book-body">
                                                    <!-- Order book data will be populated by market data -->
                                                    <tr class="bg-white">
                                                        <td class="py-1 px-2 text-center text-green-600 font-semibold">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-red-600 font-semibold">--</td>
                                                        <td class="py-1 px-2 text-center text-red-600 font-semibold">--</td>
                                                    </tr>
                                                    <tr class="bg-gray-50">
                                                        <td class="py-1 px-2 text-center text-green-600 font-semibold">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-red-600 font-semibold">--</td>
                                                        <td class="py-1 px-2 text-center text-red-600 font-semibold">--</td>
                                                    </tr>
                                                    <tr class="bg-white">
                                                        <td class="py-1 px-2 text-center text-green-600 font-semibold">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-red-600 font-semibold">--</td>
                                                        <td class="py-1 px-2 text-center text-red-600 font-semibold">--</td>
                                                    </tr>
                                                    <tr class="bg-gray-50">
                                                        <td class="py-1 px-2 text-center text-green-600 font-semibold">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-red-600 font-semibold">--</td>
                                                        <td class="py-1 px-2 text-center text-red-600 font-semibold">--</td>
                                                    </tr>
                                                    <tr class="bg-white">
                                                        <td class="py-1 px-2 text-center text-green-600 font-semibold">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                                                        <td class="py-1 px-2 text-center text-red-600 font-semibold">--</td>
                                                        <td class="py-1 px-2 text-center text-red-600 font-semibold">--</td>
                                                    </tr>
                                                </tbody>
                                                <tfoot>
                                                    <tr class="bg-gray-100 text-xs border-t border-gray-200">
                                                        <td colspan="3" class="py-1 px-2 text-left font-semibold text-gray-700">TOTAL: <span class="total-bids text-green-600">0</span></td>
                                                        <td colspan="3" class="py-1 px-2 text-right font-semibold text-gray-700">TOTAL: <span class="total-offers text-red-600">0</span></td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Right Side - Market Data Summary -->
                            <div class="w-1/2 p-3 bg-white">
                                <div class="grid grid-cols-2 gap-x-4 gap-y-2 text-sm h-full">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Last Price</span>
                                        <span class="market-data text-right font-medium market-data-ltp">--</span>
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Volume</span>
                                        <span class="market-data text-right font-medium market-data-volume">--</span>
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Open</span>
                                        <span class="market-data text-right font-medium market-data-open">--</span>
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">LTT</span>
                                        <span class="market-data text-right font-medium market-data-ltt">--</span>
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">High</span>
                                        <span class="market-data text-right font-medium market-data-high">--</span>
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Avg. Price</span>
                                        <span class="market-data text-right font-medium market-data-avgprice">--</span>
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Low</span>
                                        <span class="market-data text-right font-medium market-data-low">--</span>
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">OI</span>
                                        <span class="market-data text-right font-medium market-data-oi">--</span>
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Close</span>
                                        <span class="market-data text-right font-medium market-data-close">--</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Controls at Bottom - Single Row -->
                        <div class="p-3 bg-white border-t">
                            <div class="flex flex-wrap items-center gap-2">
                                <!-- Order Type Selection -->
                                <div class="order-type-controls">
                                    <label class="inline-flex items-center">
                                        <input type="radio" name="leg-${index}-order-type" class="mr-1 order-type-radio" value="market">
                                        <span class="text-sm">Market</span>
                                    </label>
                                    <label class="inline-flex items-center ml-3">
                                        <input type="radio" name="leg-${index}-order-type" class="mr-1 order-type-radio" value="limit" checked="checked">
                                        <span class="text-sm">Limit</span>
                                    </label>
                                </div>

                                <div class="flex flex-wrap ml-4 gap-2">
                                    <!-- Price Input -->
                                    <div class="flex justify-center items-center price-control max-w-[150px]">
                                        <label class="block text-xs text-gray-600 mb-1">Price</label>
                                        <div class="relative">
                                            <input type="number" ${isFut ? 'disabled' : ''} name="price" class="price-input w-full border rounded p-1.5 text-sm pr-7 disabled:bg-gray-100" value="${leg.price || 0}" min="0" step="0.05">
                                            <button class="absolute right-0 top-0 h-full px-1 text-gray-500 hover:text-gray-700">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- Quantity Input -->
                                    <div class="flex justify-center items-center qty-control max-w-[150px]">
                                        <label class="block text-xs text-gray-600 mb-1">Qty</label>
                                        <div class="relative">
                                            <input id="qty-input-${leg.id}" name="quantity" type="number" class="qty-input w-full border rounded p-1.5 text-sm pr-7" value="${quantity}" min="${lotSize}" step="${lotSize}">
                                            <button class="absolute right-0 top-0 h-full px-1 text-gray-500 hover:text-gray-700">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>                                
                                
                                <!-- Action Buttons -->
                                <div class="flex gap-2 ml-auto">
                                    <button class="cancel-btn px-4 py-1.5 text-sm font-medium bg-gray-200 hover:bg-gray-300 rounded">Cancel</button>
                                    <button class="action-btn px-4 py-1.5 text-sm font-medium ${buttonColor} text-white rounded">${actionLabel}</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Placeholder for Order Status Details -->
                        <div class="order-status-details" data-leg-id="${leg.id}"></div>
                    </div>
                </div>
                `;
            }
        });

        html += '</div>';
        return html;
    };

    // Set up event listeners for order panels
    window.TradeUtils.setupOrderPanelListeners = function (containerElement) {
        if (!containerElement) {
            console.error("setupOrderPanelListeners called with null or undefined container");
            return;
        }

        const panelsInActiveTab = containerElement.querySelectorAll('.leg-panel');

        if (panelsInActiveTab.length === 0 && this.state.selectedLegs?.length > 0) {
            console.warn("No leg panels found in the container, although selectedLegs exist. Check createLegTradingPanels or timing.");
        }

        panelsInActiveTab.forEach(panel => {
            const legId = panel.dataset.legId;
            const leg = this.state.selectedLegs.find(l => l.id === legId);
            const isFut = leg.type === 'FUT';

            if (!leg) {
                console.warn(`Leg data not found for legId ${legId} during listener setup.`);
                // Optionally, disable the panel or show an error within it
                return; // Skip setting up listeners for this panel
            }

            leg.isAttempted = false;

            // Ensure limit radio is checked by default
            const limitRadio = panel.querySelector('.order-type-radio[value="limit"]');
            // console.log("Limit radio:", limitRadio);
            if (limitRadio) {
                limitRadio.checked = true;
            } else {
                console.warn("Limit radio button not found in panel for leg:", legId);
            }

            // Set up event listeners for radio buttons
            panel.querySelectorAll('.order-type-radio').forEach(radio => {
                radio.onchange = (e) => {
                    const priceInput = panel.querySelector('.price-input');
                    if (!priceInput) {
                        console.error('Price input not found for radio change in leg:', legId);
                        return;
                    }
                    if (e.target.value === 'market' || isFut) {
                        priceInput.disabled = true;
                        priceInput.classList.add('bg-gray-100');
                    } else {
                        priceInput.disabled = false;
                        priceInput.classList.remove('bg-gray-100');
                    }

                    // Recalculate margins after order type change (with debounce)
                    this.debouncedMarginCalculation();
                };
            });

            // Set up event listeners for quantity input to ensure it's always a multiple of lotSize
            const qtyInput = panel.querySelector('.qty-input');
            const lotSize = this.state.lotSize || 1;
            if (qtyInput) {
                qtyInput.onchange = (e) => {
                    let value = parseInt(e.target.value);
                    if (isNaN(value) || value < lotSize) {
                        value = lotSize;
                        e.target.value = value;
                        showAlert(`Quantity must be at least ${lotSize}. Adjusted to ${value}.`, 'warning');
                        return;
                    } else {
                        // Check if it's already a multiple of lotSize
                        if (value % lotSize !== 0) {
                            const oldValue = value;
                            // Round to nearest multiple of lotSize
                            value = Math.round(value / lotSize) * lotSize;
                            e.target.value = value;
                            showAlert(`Quantity must be a multiple of ${lotSize}. Adjusted from ${oldValue} to ${value}.`, 'warning');
                            return;
                        }
                    }

                    // Recalculate margins after quantity change (with debounce)
                    this.debouncedMarginCalculation();
                };
            }

            const priceInput = panel.querySelector('.price-input');
            if (priceInput) {
                priceInput.oninput = (e) => {
                    const price = priceInput.value;
                    if (price === '' || price === undefined || price <= 0) {
                        priceInput.value = 0;
                        showAlert('Price cannot be empty or negative. Adjusted to 0.', 'warning');
                        return;
                    }
                    this.debouncedMarginCalculation();
                };
            }

            // Set up event listeners for action buttons
            const cancelBtn = panel.querySelector('.cancel-btn');
            const actionBtn = panel.querySelector('.action-btn');

            if (cancelBtn) {
                cancelBtn.onclick = () => {
                    this.closeModal();
                };
            } else {
                console.warn("Cancel button not found in panel for leg:", legId);
            }

            if (actionBtn) {
                actionBtn.onclick = () => {
                    // Ensure leg data is still valid before placing trade
                    const currentLeg = this.state.selectedLegs.find(l => l.id === legId);
                    if (!currentLeg) {
                        showAlert('Error: Leg data missing. Cannot place trade.', 'error');
                        return;
                    }
                    const actionType = currentLeg.action;
                    this.placeTrade(legId, actionType, panel);
                };
            } else {
                console.warn("Action button not found in panel for leg:", legId);
            }
        });
    };

    // Place a single trade for a leg
    window.TradeUtils.placeTrade = async function (legId, action, panel) {
        // First show loading state on the button
        const actionBtn = panel.querySelector('.action-btn');
        const cancelBtn = panel.querySelector('.cancel-btn');

        // Set loading state
        this.setButtonLoading(actionBtn, true);
        if (cancelBtn) cancelBtn.disabled = true;

        // For a single order, prepare order data and call sendOrderRequest
        try {
            const leg = this.state.selectedLegs.find(l => l.id === legId);
            if (!leg) {
                console.error('Leg not found:', legId);
                showAlert('Error: Leg not found', 'error');
                this.setButtonLoading(actionBtn, false);
                if (cancelBtn) cancelBtn.disabled = false;
                return;
            }

            // Get order details from the panel
            const orderDetails = this.getOrderDetailsFromPanel(panel, leg, action);
            if (!orderDetails) {
                // Error already shown to user
                this.setButtonLoading(actionBtn, false);
                if (cancelBtn) cancelBtn.disabled = false;
                return;
            }

            // Send the single order
            await this.sendOrderRequest([orderDetails], 'Single order placement');
            // Note: we don't need to reset button states here as the leg panel will be disabled after order placement
        } catch (error) {
            console.error('Error preparing trade:', error);
            showAlert(error.message || 'Failed to prepare order', 'error');
            // Reset button states on error
            this.setButtonLoading(actionBtn, false);
            if (cancelBtn) cancelBtn.disabled = false;
        }
    },
        window.TradeUtils.placeAllTrades = function (orderType) {
            // Check if there's at least one broker and one leg
            if (!this.state.authenticatedApps.length || !this.state.selectedLegs || !this.state.selectedLegs.length) {
                showAlert('No orders available to place.', 'error');
                return;
            }

            // Get active broker
            const activeBroker = this.state.authenticatedApps.find(app => app.uuid === this.state.activeTabUuid);
            if (!activeBroker) {
                showAlert('Please select a broker to place orders.', 'error');
                return;
            }

            // Set loading state on the Place All button
            const placeMarketBtn = document.getElementById('place-market-btn');

            // Confirm order placement using showConfirmDialog
            const legsCount = this.state.selectedLegs.filter(l => l.enabled !== false).length;
            const confirmMsg = `You are about to place ${legsCount} ${orderType} order(s) with ${activeBroker.name}. Do you want to proceed?`;

            // Use showConfirmDialog instead of standard confirm
            showConfirmDialog({
                title: 'Confirm Order Placement',
                message: confirmMsg,
                confirmText: 'Place Orders',
                cancelText: 'Cancel',
                type: orderType === 'market' ? 'warning' : 'info'
            }).then(async (confirmed) => {
                if (confirmed) {
                    // Start loading on confirmation
                    this.setPlaceAllButtonLoading(true);

                    // Collect orders from all leg panels
                    const orders = [];
                    document.querySelectorAll('.leg-panel').forEach(panel => {
                        const legId = panel.dataset.legId;
                        const leg = this.state.selectedLegs.find(l => l.id === legId);

                        if (leg && leg.enabled !== false) {
                            // Get order details for this leg
                            const orderDetails = this.getOrderDetailsFromPanel(panel, leg, leg.action, orderType);
                            if (orderDetails) {
                                orders.push(orderDetails);
                            }
                        }
                    });

                    // Send all orders to the server/broker
                    if (orders.length > 0) {
                        await this.sendOrderRequest(orders, 'Batch order placement');
                        // Note: The button will be disabled by disablePlaceAllButton if all legs are attempted
                    } else {
                        showAlert('No valid orders found to place.', 'error');
                        // Stop loading, but keep enabled/disabled state based on leg attempts
                        this.setPlaceAllButtonLoading(false);
                    }
                }
            }).catch((error) => {
                console.error("Error in order confirmation dialog:", error);
                // Stop loading
                this.setPlaceAllButtonLoading(false);
            });
        },
        window.TradeUtils.getOrderDetailsFromPanel = function (panel, leg, action, forceOrderType = null) {
            // If forceOrderType is provided, use that instead of panel's radio selection
            let orderType;
            if (forceOrderType) {
                orderType = forceOrderType;
            } else {
                const orderTypeRadio = panel.querySelector('.order-type-radio:checked');
                if (!orderTypeRadio) {
                    console.error('Order type radio button not found');
                    showAlert('Error: Order type not selected', 'error');
                    return null;
                }
                orderType = orderTypeRadio.value;
            }

            // Get price input
            const priceInput = panel.querySelector('.price-input');
            if (!priceInput) {
                console.error('Price input not found');
                showAlert('Error: Price input not found', 'error');
                return null;
            }

            // Get quantity input
            const qtyInput = panel.querySelector('.qty-input');
            if (!qtyInput) {
                console.error('Quantity input not found');
                showAlert('Error: Quantity input not found', 'error');
                return null;
            }

            const price = priceInput.value;
            let qty = parseInt(qtyInput.value);

            // Ensure quantity is a multiple of lotSize
            const lotSize = this.state.lotSize || 1;
            if (isNaN(qty) || qty < lotSize) {
                qty = lotSize;
                qtyInput.value = qty;
                showAlert(`Quantity must be at least ${lotSize}. Adjusted to ${qty}.`, 'warning');
                return null;
            } else if (qty % lotSize !== 0) {
                const oldQty = qty;
                // Round to nearest multiple of lotSize
                qty = Math.round(qty / lotSize) * lotSize;
                qtyInput.value = qty;
                showAlert(`Quantity must be a multiple of ${lotSize}. Adjusted from ${oldQty} to ${qty}.`, 'warning');
                return null;
            }

            // Get product type from modal selection
            const orderDurationText = document.getElementById('order-duration-text')?.textContent?.trim();
            let product = "INTRADAY"; // Default to Intraday
            if (orderDurationText && orderDurationText.toLowerCase() === "overnight") {
                product = "DELIVERY";
            }

            // Check if the order should be AMO based on trading hours
            const isAmo = this.isAfterMarketHours() ? "TRUE" : "FALSE";

            // Prepare trade details
            return {
                symbol: this.state.symbol,
                id: leg.id,
                type: leg.type, // CE or PE or FUT
                strike: leg.strike?.toString() || "", // strike price
                expiryDate: leg.expiryDate, // expiry date
                action: action, // buy or sell
                product_type: product, // Set from modal selection
                order_type: orderType === 'limit' ? "LIMIT" : "MARKET",
                quantity: qty.toString(), // Convert to string as required
                validity: "DAY", // Default to DAY can be changed to IOC but keep it as DAY for now
                price: orderType === 'limit' ? price.toString() : "0", // Convert to string, 0 for market orders
                tag: "AIBULL", // Default tag
                transaction_type: action.toUpperCase(), // BUY or SELL
                // disclosed_quantity: qty.toString(), // Same as quantity not using for now
                // trigger_price: "0", // Default to 0 for non-SL orders Not using for now
                is_amo: isAmo // TRUE for After Market Orders, FALSE for regular orders
            };
        },

        window.TradeUtils.isAfterMarketHours = function () {
            // Convert current time to Indian Standard Time (IST)
            const now = new Date();

            // Create date formatter for checking day of the week in IST
            const istFormatter = new Intl.DateTimeFormat('en-US', {
                timeZone: 'Asia/Kolkata',
                weekday: 'long'
            });

            // Get day of week in IST
            const dayOfWeek = istFormatter.format(now);

            // Check if it's a weekend (Saturday or Sunday)
            if (dayOfWeek === 'Saturday' || dayOfWeek === 'Sunday') {
                return true; // Weekend, so it's after market hours
            }

            // Get current hours and minutes in IST
            const istDate = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
            const hours = istDate.getHours();
            const minutes = istDate.getMinutes();
            const timeInMinutes = hours * 60 + minutes;

            // NSE market hours: 9:15 AM to 3:30 PM IST
            const marketOpenMinutes = 9 * 60 + 15;   // 9:15 AM
            const marketCloseMinutes = 15 * 60 + 30; // 3:30 PM

            // If current time is before market open or after market close, it's after market hours
            return timeInMinutes < marketOpenMinutes || timeInMinutes >= marketCloseMinutes;
        },
        window.TradeUtils.sendOrderRequest = async function (orders, requestType = 'Order placement') {
            // Abort previous request and create new controller for this trade
            this.state.currentAbortController?.abort(`New ${requestType} requested`);
            const controller = new AbortController();
            this.state.currentAbortController = controller;
            const signal = controller.signal;

            let responseData = null; // To store response data for later use

            try {
                const requestData = {
                    app_uuid: this.state.activeTabUuid,
                    orders: orders
                };

                console.log(`${requestType} details:`, requestData);

                // Make api call to place the order(s)
                const response = await fetch(`/apps/place-orders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData),
                    signal: signal // Pass the signal
                });

                // Check if aborted during fetch
                if (signal.aborted) {
                    console.log(`${requestType} fetch aborted.`);
                    showAlert('Order placement cancelled.', 'warning');
                    // Handle results for any orders that might have been processed before abort?
                    // For now, assume abort means no results to process.
                    return false; // Indicate cancellation
                }

                responseData = await response.json(); // Store response data

                if (!response.ok) {
                    // Even if response is not ok overall, process individual order results
                    console.warn(`Order placement API call failed with status ${response.status}`);
                    // Throw an error only if responseData is not available or has no results
                    if (!responseData || !responseData.results) {
                        throw new Error(responseData?.message || `Failed to place order(s)`);
                    }
                }
                return true; // Indicate attempt was made (individual success/fail handled below)

            } catch (error) {
                if (error.name === 'AbortError') {
                    console.log(`${requestType} aborted:`, error.message);
                    showAlert('Order placement cancelled.', 'warning');
                } else {
                    console.error(`Error in ${requestType}:`, error);
                    showAlert(error.message || 'Failed to place order', 'error');
                }
                // Even on general catch, try to process any partial responseData if available
                // return false; // Indicate failure
            } finally {
                // Process results regardless of overall success/failure or catch
                if (responseData && responseData.results) {
                    let allSuccessful = true;
                    responseData.results.forEach(result => {
                        const legId = result.order?.id;
                        if (legId) {
                            const panel = document.querySelector(`.leg-panel[data-leg-id="${legId}"]`);
                            // Also update the state for this leg
                            const legInState = this.state.selectedLegs.find(l => l.id === legId);
                            if (legInState) {
                                legInState.isAttempted = true; // Mark as attempted
                            }

                            if (panel) {
                                // 1. Disable the panel
                                this.disableLegPanel(panel, result);
                                // 2. Fetch and display status (pass original order and result)
                                const originalOrder = orders.find(o => o.id === legId);
                                this.fetchAndDisplayOrderStatus(panel, originalOrder, result);
                                if (result.error) {
                                    allSuccessful = false;
                                }
                            } else {
                                console.warn(`Could not find panel for legId: ${legId} to update status.`);
                            }
                        } else {
                            console.warn("Result object missing order.id:", result);
                        }
                    });

                    // After processing all results, disable the 'Place All' button
                    this.disablePlaceAllButton();

                    // Show alert based on combined results
                    const isMultiple = orders.length > 1;
                    if (allSuccessful) {
                        showAlert(`Order${isMultiple ? 's' : ''} placed successfully.`, 'success');
                    } else {
                        showAlert(`Order${isMultiple ? 's' : ''} placement attempted with some errors. Check status below each order.`, 'warning');
                    }

                } else if (!signal?.aborted) { // Don't show error if just cancelled
                    console.error("No results data received from /apps/place-orders endpoint.");
                    // Potentially show a generic error for all involved legs if responseData is missing
                    // If the request failed before getting results, disable Place All button anyway
                    this.disablePlaceAllButton();
                }
            }
            return false; // Default return if finally is reached without success path
        },

        // Fetch and display order status
        window.TradeUtils.fetchAndDisplayOrderStatus = async function (panel, orderDetails, initialResult) {
            const statusContainer = panel.querySelector('.order-status-details');
            if (!statusContainer) {
                console.error("Status container not found for leg:", panel.dataset.legId);
                return;
            }

            statusContainer.innerHTML = `
                <div class="mt-2 p-2 border-t text-xs text-gray-500 flex items-center justify-between">
                     <span>Status: Fetching...</span>
                     <div class="w-4 h-4 border-2 border-gray-300 border-t-transparent rounded-full animate-spin"></div>
                 </div>`;

            // Extract order ID from initialResult if available
            const orderId = initialResult.result?.result?.order_id;
            const uniqueOrderId = initialResult.result?.result?.unique_order_id;
            if (!orderId) {
                // No order ID found, display error or initial result directly
                let statusHTML = '';
                const qty = orderDetails?.quantity || 'N/A';

                if (initialResult.error) {
                    statusHTML = `
                        <div class="flex items-center space-x-2">
                            <span class="font-medium text-gray-700">Completed 0 / ${qty}</span>
                            <span class="px-2 py-0.5 text-xs font-semibold text-red-800 bg-red-100 rounded-full">Error</span>
                        </div>
                        <div class="mt-1 text-red-600">
                            Error: ${initialResult.error || 'Unknown placement error'}
                        </div>
                    `;
                } else {
                    // No order ID, but no error either
                    statusHTML = `
                        <div class="flex items-center space-x-2">
                            <span class="font-medium text-gray-700">Status Pending</span>
                        </div>
                        <div class="mt-1 text-gray-600">
                            Order placed, waiting for confirmation...
                        </div>
                    `;
                }

                statusContainer.innerHTML = `
                    <div class="mt-2 p-2 border-t text-xs">
                        ${statusHTML}
                    </div>
                `;
                return;
            }

            try {
                // Call the API to get detailed order status
                const response = await fetch('/apps/get-order-history', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        app_uuid: this.state.activeTabUuid,
                        order_id: orderId,
                        unique_order_id: uniqueOrderId // AngelOne returns unique order id for each order
                    })
                });

                if (!response.ok) {
                    throw new Error(`Error fetching order details: ${response.status}`);
                }

                const responseData = await response.json();

                let statusHTML = '';
                const qty = orderDetails?.quantity || 'N/A';

                if (responseData.status === 'error') {
                    // API returned an error
                    statusHTML = `
                        <div class="flex items-center space-x-2">
                            <span class="font-medium text-gray-700">Status Unknown</span>
                            <span class="px-2 py-0.5 text-xs font-semibold text-yellow-800 bg-yellow-100 rounded-full">Warning</span>
                        </div>
                        <div class="mt-1 text-yellow-600">
                            Order ID: ${orderId} - Unable to fetch status: ${responseData.message}
                        </div>
                    `;
                } else {
                    // Process the order history
                    const orderData = responseData.order;

                    // Handle history that could be either an object or an array
                    let latestEntry;

                    // Check if history is an array
                    if (Array.isArray(orderData.history)) {
                        // Check if history array exists and has entries
                        if (!orderData.history.length) {
                            statusHTML = `
                                <div class="flex items-center space-x-2">
                                    <span class="font-medium text-gray-700">No History</span>
                                </div>
                                <div class="mt-1 text-gray-600">
                                    Order ID: ${orderId} - No history available
                                </div>
                            `;
                            statusContainer.innerHTML = `
                                <div class="mt-2 p-2 border-t text-xs">
                                    ${statusHTML}
                                </div>
                            `;
                            return;
                        }

                        // Get the most recent history entry (last in the array)
                        latestEntry = orderData.history[orderData.history.length - 1];
                    } else if (typeof orderData.history === 'object' && orderData.history !== null) {
                        // AngelOne format: history is a single object
                        latestEntry = orderData.history;
                    } else {
                        // No valid history found
                        statusHTML = `
                            <div class="flex items-center space-x-2">
                                <span class="font-medium text-gray-700">Invalid History</span>
                            </div>
                            <div class="mt-1 text-gray-600">
                                Order ID: ${orderId} - Invalid history format
                            </div>
                        `;
                        statusContainer.innerHTML = `
                            <div class="mt-2 p-2 border-t text-xs">
                                ${statusHTML}
                            </div>
                        `;
                        return;
                    }

                    // Extract relevant data
                    const orderStatus = latestEntry.status || 'PENDING';
                    const filledQty = latestEntry.filled_quantity || 0;
                    const totalQty = latestEntry.quantity || qty;
                    const orderPrice = latestEntry.price || 'N/A';
                    const orderType = latestEntry.order_type || 'N/A';
                    const tradingSymbol = latestEntry.trading_symbol || 'N/A';

                    // Determine status badges and colors
                    let statusBadge = '';
                    let statusClass = '';

                    // Check status keywords (case insensitive)
                    const statusLower = orderStatus.toLowerCase();

                    if (statusLower.includes('complete') || statusLower.includes('success') && filledQty == totalQty) {
                        statusBadge = `<span class="px-2 py-0.5 text-xs font-semibold text-green-800 bg-green-100 rounded-full">Complete</span>`;
                        statusClass = 'text-green-700';
                    } else if (statusLower.includes('reject')) {
                        statusBadge = `<span class="px-2 py-0.5 text-xs font-semibold text-red-800 bg-red-100 rounded-full">Rejected</span>`;
                        statusClass = 'text-red-700';
                    } else if (statusLower.includes('cancel')) {
                        statusBadge = `<span class="px-2 py-0.5 text-xs font-semibold text-gray-800 bg-gray-100 rounded-full">Canceled</span>`;
                        statusClass = 'text-gray-700';
                    } else if (statusLower.includes('pending') || statusLower.includes('received')) {
                        statusBadge = `<span class="px-2 py-0.5 text-xs font-semibold text-blue-800 bg-blue-100 rounded-full">Pending</span>`;
                        statusClass = 'text-blue-700';
                    } else if (statusLower.includes('open')) {
                        statusBadge = `<span class="px-2 py-0.5 text-xs font-semibold text-blue-800 bg-blue-100 rounded-full">Open</span>`;
                        statusClass = 'text-blue-700';
                    } else if (statusLower.includes('partial') || (filledQty > 0 && filledQty < totalQty)) {
                        statusBadge = `<span class="px-2 py-0.5 text-xs font-semibold text-purple-800 bg-purple-100 rounded-full">Partial</span>`;
                        statusClass = 'text-purple-700';
                    } else {
                        // Generic status badge
                        statusBadge = `<span class="px-2 py-0.5 text-xs font-semibold text-gray-800 bg-gray-100 rounded-full">${orderStatus}</span>`;
                        statusClass = 'text-gray-700';
                    }

                    // Build status HTML
                    statusHTML = `
                        <div class="flex items-center space-x-2">
                            <span class="font-medium ${statusClass}">Completed ${filledQty} / ${totalQty}</span>
                            ${statusBadge}
                        </div>
                        <div class="mt-1 text-gray-700">
                            Order ID: ${orderId}
                        </div>
                        <div class="mt-1 text-gray-600">
                            ${tradingSymbol} | Price: ${orderPrice} | Type: ${orderType}
                        </div>
                    `;

                    // Add status description if it's informative
                    if (orderStatus && !['COMPLETE', 'SUCCESS', 'PENDING', 'OPEN'].includes(orderStatus.toUpperCase())) {
                        statusHTML += `
                            <div class="mt-1 text-gray-600">
                                Status: ${orderStatus}
                            </div>
                        `;
                    }
                }

                statusContainer.innerHTML = `
                    <div class="mt-2 p-2 border-t text-xs">
                        ${statusHTML}
                    </div>
                `;

            } catch (error) {
                console.error("Error fetching order details:", error);

                // Show error message
                statusContainer.innerHTML = `
                    <div class="mt-2 p-2 border-t text-xs">
                        <div class="flex items-center space-x-2">
                            <span class="font-medium text-gray-700">Order ID: ${orderId}</span>
                        </div>
                        <div class="mt-1 text-red-600">
                            Failed to fetch status: ${error.message}
                        </div>
                    </div>
                `;
            }
        }
</script>