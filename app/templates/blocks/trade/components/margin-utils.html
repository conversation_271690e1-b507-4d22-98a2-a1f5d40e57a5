<script>
    // Margin calculation and utility functions

    // Calculate margin requirements for the current legs
    window.TradeUtils.calculateMargins = async function () {
        // Create a separate abort controller for margin calculation
        // This ensures market data and margin calculations don't interfere with each other
        if (this.state.marginAbortController) {
            this.state.marginAbortController.abort('New margin calculation requested');
        }
        const marginController = new AbortController();
        this.state.marginAbortController = marginController;
        const marginSignal = marginController.signal;

        // Show loading indicator in the margin container
        const marginNeededElement = document.getElementById('margin-needed');
        const marginAvailableElement = document.getElementById('margin-available');
        if (marginNeededElement) {
            marginNeededElement.innerHTML = `
                        <div class="flex items-center">
                            <div class="w-3 h-3 border-2 border-blue-400 border-t-transparent rounded-full animate-spin mr-1"></div>
                            <span>Calculating...</span>
                        </div>
                    `;
        }
        if (marginAvailableElement) {
            marginAvailableElement.innerHTML = `
                        <div class="flex items-center">
                            <div class="w-3 h-3 border-2 border-blue-400 border-t-transparent rounded-full animate-spin mr-1"></div>
                            <span>Calculating...</span>
                        </div>
                    `;
        }

        try {

            // Get active broker
            const activeApp = this.state.authenticatedApps.find(app => app.uuid === this.state.activeTabUuid);
            if (!activeApp) {
                throw new Error('No active broker selected');
            }

            // Convert selected legs to the format expected by the margin API
            // IMPORTANT: Use current price and quantity values from the panels
            const legsData = [];
            for (const leg of this.state.selectedLegs) {
                // Find the panel for this leg to get current price and quantity values
                const panel = document.querySelector(`.leg-panel[data-leg-id="${leg.id}"]`);
                let currentPrice = null;
                let currentQuantity = null;
                const lotSize = this.state.lotSize || 1;

                // If panel exists, try to get the price and quantity from the input fields
                if (panel) {
                    // Get current price
                    const priceInput = panel.querySelector('.price-input');
                    if (priceInput && !isNaN(parseFloat(priceInput.value))) {
                        currentPrice = parseFloat(priceInput.value);
                    }

                    // Get current quantity
                    const qtyInput = panel.querySelector('.qty-input');
                    if (qtyInput && !isNaN(parseInt(qtyInput.value))) {
                        currentQuantity = parseInt(qtyInput.value);
                        // Calculate lots based on quantity
                        if (lotSize > 0) {
                            currentQuantity = currentQuantity / lotSize;
                        }
                    }
                }

                // Verify we have valid price
                if (currentPrice === null) {
                    console.warn(`No valid price found for leg ${leg.id}`);
                    showAlert(`No valid price found for leg ${leg.id}`);
                    return;
                }

                // Verify we have valid quantity
                if (currentQuantity === null) {
                    console.warn(`No valid quantity found for leg ${leg.id}`);
                    showAlert(`No valid quantity found for leg ${leg.id}`);
                    return;
                }

                // Add leg data with current values
                legsData.push({
                    symbol: this.state.symbol,
                    type: leg.type,
                    tr_type: leg.action === 'buy' ? 'b' : 's',
                    lots: currentQuantity, // Use current quantity from UI (in lots)
                    strike: leg.type === 'FUT' ? null : leg.strike,
                    expiryDate: leg.expiryDate,
                    entry_price: currentPrice, // Use current price from UI
                    op_pr: currentPrice,      // Use current price from UI
                    lot_size: lotSize,
                    iv: leg.iv || 0
                });
            }

            // Use the correct endpoint format: broker as query param, legs as body array
            const response = await fetch(`/margins/calculate-margin-for-broker?broker=${activeApp.key}&multiplier=${this.state.multiplier || 1}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(legsData),
                signal: marginSignal // Use the margin-specific signal
            });

            // Check if aborted during fetch
            if (marginSignal.aborted) {
                console.log("Margin calculation fetch aborted.");
                // Optionally update UI to show aborted state
                document.getElementById('margin-needed').textContent = 'Cancelled';
                document.getElementById('margin-available').textContent = 'Cancelled';
                return;
            }

            if (!response.ok) {
                throw new Error('Failed to calculate margins');
            }

            const data = await response.json();
            this.state.marginData = data;

            // Update UI with margin data
            this.updateMarginUI(data);
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('Margin calculation aborted:', error.message);
                // Update UI if needed, e.g., show 'Cancelled' or revert loading state
                if (document.getElementById('margin-needed')) document.getElementById('margin-needed').textContent = 'Cancelled';
                if (document.getElementById('margin-available')) document.getElementById('margin-available').textContent = 'Cancelled';
            } else {
                console.error('Error calculating margins:', error);
                document.getElementById('margin-needed').textContent = 'Unavailable';
                document.getElementById('margin-available').textContent = 'Unavailable';
            }
        } finally {
            // Make sure the trading footer is visible regardless of success/failure
            const tradingFooter = document.getElementById('trading-footer');
            if (tradingFooter && tradingFooter.classList.contains('hidden')) {
                tradingFooter.classList.remove('hidden');
            }
        }
    },
        // Update the margin display in the UI
        window.TradeUtils.updateMarginUI = function (marginData) {
            const marginNeededElement = document.getElementById('margin-needed');
            const marginAvailableElement = document.getElementById('margin-available');

            // Make sure the trading footer is visible
            const tradingFooter = document.getElementById('trading-footer');
            if (tradingFooter && tradingFooter.classList.contains('hidden')) {
                tradingFooter.classList.remove('hidden');
            }

            if (!marginNeededElement || !marginAvailableElement) {
                console.error('Margin elements not found in the DOM');
                return;
            }

            if (marginData && marginData.margin) {
                // Format margin values
                const formatCurrency = (value) => {
                    return new Intl.NumberFormat('en-IN').format(Math.round(value));
                };

                // Update margin needed
                marginNeededElement.textContent = formatCurrency(marginData.margin.total || 0);

                // Update margin available if present
                if (marginData.margin.legs && marginData.margin.legs.length > 0 &&
                    marginData.margin.legs[0].available_balance !== undefined) {
                    marginAvailableElement.textContent =
                        formatCurrency(marginData.margin.legs[0].available_balance);
                } else {
                    marginAvailableElement.textContent = 'Unavailable';
                }
            } else if (marginData && marginData.error) {
                marginNeededElement.textContent = 'Unavailable';
                marginAvailableElement.textContent = 'Unavailable';
                console.error('Margin calculation error:', marginData.error);
            } else {
                marginNeededElement.textContent = 'Unavailable';
                marginAvailableElement.textContent = 'Unavailable';
            }
        };

    // Debounced version of margin calculation to avoid excessive API calls
    window.TradeUtils.debouncedMarginCalculation = function () {
        // Clear any existing timer
        if (this.state.marginDebounceTimer) {
            clearTimeout(this.state.marginDebounceTimer);
        }

        // Set up new timer
        this.state.marginDebounceTimer = setTimeout(() => {
            this.calculateMargins();
        }, 500); // 500ms debounce time
    };

    // Reset margin display
    window.TradeUtils.resetMarginDisplay = function () {
        const marginNeeded = document.getElementById('margin-needed');
        const marginAvailable = document.getElementById('margin-available');

        if (marginNeeded) marginNeeded.textContent = '0';
        if (marginAvailable) {
            marginAvailable.textContent = '0';
            marginAvailable.classList.remove('text-red-600');
        }

        // Hide rearrange button
        const rearrangeBtn = document.getElementById('rearrange-orders-btn');
        if (rearrangeBtn) rearrangeBtn.classList.add('hidden');

        // Clear any stored margin data
        this.state.marginData = null;
    };
</script>