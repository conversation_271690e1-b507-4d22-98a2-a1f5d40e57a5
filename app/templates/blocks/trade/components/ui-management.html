<script>
    // Set up radio buttons for order duration (intraday/overnight)
    window.TradeUtils.setupOrderDurationRadios = function () {
        // Get all radio buttons with the class 'order-duration-radio'
        const radioButtons = document.querySelectorAll('.order-duration-radio');

        if (!radioButtons || radioButtons.length === 0) return;

        // Set up event listeners for radio buttons
        radioButtons.forEach(radio => {
            radio.onclick = (e) => {
                // If you need to store the selected value in state
                this.state.orderDuration = e.target.value;
            };
        });
    };


    // Build tabs for authenticated apps
    window.TradeUtils.buildAuthenticatedAppsTabs = function () {
        const tabsNav = document.getElementById('trading-tabs-nav');
        const tabsContent = document.getElementById('trading-tabs-content');

        // Clear existing tabs and content
        tabsNav.innerHTML = '';
        tabsContent.innerHTML = ''; // Clear all previous content

        // If no active tab is set, use the first app's uuid
        if (!this.state.activeTabUuid && this.state.authenticatedApps.length > 0) {
            this.state.activeTabUuid = this.state.authenticatedApps[0].uuid;
            this.saveActiveTabPreference(); // Save if we defaulted
        }

        let activeTabContentElement = null; // To store the element for the active tab

        // Create tabs navigation for each authenticated app
        this.state.authenticatedApps.forEach((app) => {
            // Create tab nav item
            const tabNav = document.createElement('li');
            const isActive = app.uuid === this.state.activeTabUuid;
            tabNav.className = `cursor-pointer px-4 py-2 text-sm font-medium ${isActive ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}`;
            tabNav.dataset.tabUuid = app.uuid;
            tabNav.textContent = app.name + " (" + (app.fields?.profile?.name || app.uuid.substring(0, 6)) + ")"; // Shorten UUID if no profile name
            tabNav.style.maxWidth = '180px';
            tabNav.style.overflow = 'hidden';
            tabNav.style.textOverflow = 'ellipsis';
            tabNav.title = app.name + " (" + (app.fields?.profile?.name || app.uuid) + ")"; // Full name on hover
            tabNav.addEventListener('click', () => this.switchAuthenticatedAppsTab(app.uuid));
            tabsNav.appendChild(tabNav);
        });

        // Set up order panel event listeners only for the initially active tab's content
        if (this.state.activeTabUuid) {
            this.switchAuthenticatedAppsTab(this.state.activeTabUuid, true);
        } else if (this.state.authenticatedApps.length > 0) {
            console.warn("No active tab content was created initially, though apps exist.");
        }
    };

    // Handle switching between authenticated app tabs
    window.TradeUtils.switchAuthenticatedAppsTab = function (newTabUuid, isInitial = false) {
        // If the active tab is the same as the newTabUuid, do nothing
        if (this.state.activeTabUuid === newTabUuid && !isInitial) {
            return;
        }

        // Reset margin display when switching tabs
        this.resetMarginDisplay();

        // Unsubscribe from all market data sockets before switching tabs
        // We pass false to indicate we're just switching tabs, not closing the modal
        if (this.state.marketDataSocket && this.state.activeMarketDataSubscriptions.size > 0) {
            console.log(`Unsubscribing from market data for ${this.state.activeMarketDataSubscriptions.size} legs before switching tabs`);
            this.unsubscribeAllMarketData(false);
        }

        const tabsContentContainer = document.getElementById('trading-tabs-content');

        // Remove the previously active tab content from the DOM
        const previousActiveContent = tabsContentContainer.querySelector(`div[data-tab-content="${this.state.activeTabUuid}"]`);
        if (previousActiveContent) {
            tabsContentContainer.removeChild(previousActiveContent);
        }

        // Update tab navigation appearance
        const tabNavs = document.querySelectorAll('#trading-tabs-nav li');
        tabNavs.forEach(tab => {
            const isActive = tab.dataset.tabUuid === newTabUuid;
            tab.className = `cursor-pointer px-4 py-2 text-sm font-medium ${isActive ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}`;
        });

        // Update active tab in state and save preference
        this.state.activeTabUuid = newTabUuid;
        this.saveActiveTabPreference();

        // Content for the new tab will always need to be created because we removed the old one
        const newTabContentElement = document.createElement('div');
        newTabContentElement.className = 'block'; // Make it visible
        newTabContentElement.dataset.tabContent = newTabUuid;

        // Add trading content
        if (this.state.selectedLegs && this.state.selectedLegs.length > 0) {
            newTabContentElement.innerHTML = this.createLegTradingPanels(this.state.selectedLegs);
        } else {
            newTabContentElement.innerHTML = `
                <div class="p-6 text-center text-gray-500">
                    <p>No options legs selected. Please add legs from the options chain first.</p>
                </div>
            `;
        }

        tabsContentContainer.appendChild(newTabContentElement);

        // Set up order panel event listeners specifically for the new content
        this.setupOrderPanelListeners(newTabContentElement);

        // Reset the loading state of the "Place All" button and update its disabled state
        this.setPlaceAllButtonLoading(false);
        this.updatePlaceAllButtonState();

        // First fetch market data, then calculate margins sequentially
        // This prevents the margin calculation from aborting the market data fetch
        if (this.state.selectedLegs && this.state.selectedLegs.length > 0) {
            this.fetchMarketDataForLegs().then((success) => {
                // Only calculate margins if we successfully received market data or if the margin data is not available
                if (success || !this.state.marginData) {
                    this.calculateMargins();
                } else {
                    console.warn("Skipping margin calculation due to failed market data fetch");
                }
            }).catch(error => {
                console.error("Error in data fetch sequence:", error);
            });
        }
    };

    // Set up UI for the footer section
    window.TradeUtils.setupFooterUI = function () {
        const footer = document.getElementById('trading-footer');

        // Show footer if we have legs to trade
        if (this.state.selectedLegs && this.state.selectedLegs.length > 0) {
            footer.classList.remove('hidden');

            // Set up dropdown for place market button
            const placeMarketBtn = document.getElementById('place-market-btn');
            const dropdown = document.getElementById('place-market-dropdown');

            // Close dropdown when clicking outside
            document.onclick = (e) => {
                if (!placeMarketBtn.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.classList.add('hidden');
                }
            };

            // Add event listeners for dropdown items
            dropdown.querySelectorAll('a').forEach(item => {
                item.onclick = (e) => {
                    e.preventDefault();
                    const action = e.target.dataset.action;
                    this.placeAllTrades(action);
                    dropdown.classList.add('hidden');
                };
            });

            // Add event listener for place market button direct click
            placeMarketBtn.onclick = (e) => {
                // If clicking the button text (not the dropdown arrow)
                if (e.target === placeMarketBtn) {
                    this.placeAllTrades('market');
                } else {
                    dropdown.classList.toggle('hidden');
                }
            };

            // Add event listener for rearrange orders button
            document.getElementById('rearrange-orders-btn').onclick = () => {
                this.rearrangeOrders();
            };

            // Add event listener for charges link
            document.getElementById('charges-link').onclick = (e) => {
                e.preventDefault();
                this.showChargesModal();
            };

            // Add event listener for margin info button
            document.getElementById('margin-info-btn').onclick = () => {
                this.showMarginInfoModal();
            };
        }
    };

    // Show charges information in a modal
    window.TradeUtils.showChargesModal = function () {
        // Get the charges modal HTML from the templates
        const chargesHTML = window.TradeUtils.templates.chargesModal;

        // Use a generic modal function - replace with your actual modal implementation
        if (typeof showModalHTML === 'function') {
            showModalHTML('Charges', chargesHTML);
        } else {
            // Fallback to alert if no modal function exists
            showAlert('Charges information is available on the broker website.');
        }
    };

    // Show modal with information about margin calculations
    window.TradeUtils.showMarginInfoModal = function () {
        // Show explanatory text for margin calculation
        const infoHTML = `
            <div class="p-5 space-y-4">
                <p class="text-sm text-gray-700">
                    This is the margin to complete this order if the trades in this order are executed (not placed, but completed) in the sequence below. We usually show the execution sequence that has the least margin requirement. Margin required will usually be higher if the orders are not executed in the sequence below. You can change the order sequence by clicking on the rearrange orders button and dragging the orders
                </p>

                <p class="text-sm text-gray-700 pt-3">
                    If there are futures and options trades together in this order, a different execution sequence from our suggested sequence may give you lower margins.
                </p>

                ${this.state.marginData && this.state.marginData.margin ? `                    <div class="pt-3 mt-3 border-t">
                    <h3 class="font-semibold mb-2">Margin Details</h3>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm text-gray-600">Total Margin Required:</span>
                        <span class="text-sm font-medium">${new Intl.NumberFormat('en-IN').format(Math.round(this.state.marginData.margin.total || 0))}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Available Margin:</span>
                        <span class="text-sm font-medium">${this.state.marginData.margin.legs &&
                    this.state.marginData.margin.legs.length > 0 &&
                    this.state.marginData.margin.legs[0].available_balance !== undefined ?
                    new Intl.NumberFormat('en-IN').format(Math.round(this.state.marginData.margin.legs[0].available_balance)) :
                    'Unavailable'
                }</span>
                    </div>
                </div>
                ` : ''}
            </div>
        `;

        showModalHTML('Margin Information', infoHTML);
    };

    // Button loading state management functions
    window.TradeUtils.setButtonLoading = function (button, isLoading) {
        if (!button) return;

        const originalText = button.dataset.originalText || button.textContent;

        if (isLoading) {
            // Save original text if not already saved
            if (!button.dataset.originalText) {
                button.dataset.originalText = button.textContent;
            }

            // Disable button
            button.disabled = true;

            // Add loading spinner and text
            button.innerHTML = `
                <div class="flex items-center justify-center">
                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span>Processing...</span>
                </div>
            `;

            button.classList.add('opacity-75');
        } else {
            // Re-enable button
            button.disabled = false;

            // Restore original text
            button.textContent = originalText;

            button.classList.remove('opacity-75');
        }
    };

    window.TradeUtils.setPlaceAllButtonLoading = function (isLoading) {
        const placeMarketBtn = document.getElementById('place-market-btn');
        if (!placeMarketBtn) return;

        if (isLoading) {
            // Save original text if not saved
            if (!placeMarketBtn.dataset.originalText) {
                placeMarketBtn.dataset.originalText = placeMarketBtn.textContent.trim();
            }

            // Add loading indicator (don't change disabled state here)
            placeMarketBtn.innerHTML = `
                <div class="flex items-center justify-center">
                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span>Processing...</span>
                </div>
            `;
            placeMarketBtn.classList.add('opacity-75');
        } else {
            // Remove loading indicator but preserve disabled state
            const wasDisabled = placeMarketBtn.disabled;

            // Restore original text
            const originalText = placeMarketBtn.dataset.originalText || 'Place All at Market';
            placeMarketBtn.innerHTML = originalText + `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            `;

            placeMarketBtn.classList.remove('opacity-75');

            // Apply the correct disabled state based on leg attempts
            this.updatePlaceAllButtonState();
        }
    };

    window.TradeUtils.updatePlaceAllButtonState = function () {
        const placeMarketBtn = document.getElementById('place-market-btn');
        if (!placeMarketBtn) return;

        // Check if ALL selected legs have been attempted
        const allAttempted = this.state.selectedLegs.every(leg => leg.isAttempted === true);

        if (allAttempted) {
            placeMarketBtn.disabled = true;
            placeMarketBtn.classList.add('opacity-50', 'cursor-not-allowed');

            // Hide dropdown
            const dropdown = document.getElementById('place-market-dropdown');
            if (dropdown) {
                dropdown.classList.add('hidden');
            }
        } else {
            placeMarketBtn.disabled = false;
            placeMarketBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    };

    window.TradeUtils.disablePlaceAllButton = function () {
        // Check if ALL selected legs have been attempted
        const allAttempted = this.state.selectedLegs.every(leg => leg.isAttempted === true);

        const placeMarketBtn = document.getElementById('place-market-btn');
        const dropdown = document.getElementById('place-market-dropdown');

        if (allAttempted) {
            if (placeMarketBtn) {
                // Remove any loading indicator
                this.setPlaceAllButtonLoading(false);

                // Disable the button
                placeMarketBtn.disabled = true;
                placeMarketBtn.classList.add('opacity-50', 'cursor-not-allowed');

                // Optionally hide the dropdown arrow part or change text
                const arrowSvg = placeMarketBtn.querySelector('svg');
                if (arrowSvg) arrowSvg.style.display = 'none';
            }
            // Prevent dropdown from opening
            if (dropdown) {
                dropdown.classList.add('hidden'); // Ensure it's hidden
            }
        } else {
            // Ensure button is enabled if not all are attempted (e.g., initial state)
            if (placeMarketBtn) {
                // Remove any loading indicator
                this.setPlaceAllButtonLoading(false);

                placeMarketBtn.disabled = false;
                placeMarketBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                const arrowSvg = placeMarketBtn.querySelector('svg');
                if (arrowSvg) arrowSvg.style.display = ''; // Show arrow
            }
        }
    };

    window.TradeUtils.disableLegPanel = function (panel, result) {
        // Add 'disabled' attribute and styling to inputs and buttons
        panel.querySelectorAll('input, button').forEach(el => {
            el.disabled = true;
        });
        // Add visual indication (e.g., opacity) to the whole panel or specific parts
        panel.classList.add('opacity-60', 'pointer-events-none'); // Disable interaction

        // Maybe change button text
        const actionBtn = panel.querySelector('.action-btn');
        if (actionBtn) {
            actionBtn.textContent = result.error ? 'Failed' : 'Placed';
            // Remove color classes and add gray
            actionBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700', 'bg-red-500', 'hover:bg-red-600');
            actionBtn.classList.add('bg-gray-400', 'cursor-not-allowed');
        }
        const cancelBtn = panel.querySelector('.cancel-btn');
        if (cancelBtn) {
            cancelBtn.textContent = 'Close'; // Or hide it?
            cancelBtn.onclick = () => this.closeModal(); // Make it close the modal
        }
    };

    // Function to rearrange the order of legs
    window.TradeUtils.rearrangeOrders = function () {
        // This would enable users to reorder the legs
        showAlert('Order rearrangement feature will be implemented in future updates.');
    };
</script>