<script>

    // Global state variables
    window.TradeUtils.state.marketDataSocket = null;
    window.TradeUtils.state.activeMarketDataSubscriptions = new Map(); // Map of legId -> subscription
    window.TradeUtils.state.instrumentToLegMap = new Map(); // Map of instrumentId -> legId for faster lookups
    window.TradeUtils.state.legToInstrumentMap = new Map(); // Map of legId -> instrumentId to persist across tab changes
    window.TradeUtils.state.legToSymbolMap = new Map(); // Map of legId -> tradingSymbol to persist across tab changes

    // Setup socket connection - called before any subscription
    window.TradeUtils.setupMarketDataSocket = function () {
        if (window.TradeUtils.state.marketDataSocket) return window.TradeUtils.state.marketDataSocket;

        // Get access token from cookie
        let accessToken = getCookie("access_token");
        if (!accessToken) {
            console.error("No access token available for market data socket");
            return null;
        }

        const connectOptions = {
            host: "streamer.emc2.io",
            port: 443,
            secure: true,
            path: "/bifrost?t=" + accessToken,
        };

        window.TradeUtils.state.marketDataSocket = emitter.connect(connectOptions);

        window.TradeUtils.state.marketDataSocket.on("connect", () => {
            console.log("Connected to market data socket");
        });

        window.TradeUtils.state.marketDataSocket.on("disconnect", () => {
            console.log("Disconnected from market data socket");
        });

        window.TradeUtils.state.marketDataSocket.on("error", (error) => {
            console.error("Market data socket error:", error);
        });

        // Set up message handler
        window.TradeUtils.state.marketDataSocket.on('message', window.TradeUtils.handleMarketDataMessage);

        return window.TradeUtils.state.marketDataSocket;
    };

    // Clean up all subscriptions and disconnect
    window.TradeUtils.unsubscribeAllMarketData = function (isModalClosing = false) {
        console.log("Unsubscribing from all market data, modal closing:", isModalClosing);
        if (!window.TradeUtils.state.marketDataSocket) return;

        window.TradeUtils.state.activeMarketDataSubscriptions.forEach((subscription, legId) => {
            window.TradeUtils.unsubscribeFromLegMarketData(legId);
        });

        window.TradeUtils.state.activeMarketDataSubscriptions.clear();
        window.TradeUtils.state.instrumentToLegMap.clear(); // Clear the instrument-to-leg mapping

        // Only clear the persistent mappings if the modal is closing
        if (isModalClosing) {
            window.TradeUtils.state.legToInstrumentMap.clear();
            window.TradeUtils.state.legToSymbolMap.clear();
            //  console.log("Modal is closing, cleared all mappings");

            // Disconnect socket only when modal is closing
            window.TradeUtils.state.marketDataSocket.disconnect();
            window.TradeUtils.state.marketDataSocket = null;
        } else {
            // console.log("Tab switching, keeping leg-to-instrument and leg-to-symbol mappings and socket connection");
        }
    };

    // Main function to fetch market data for all legs
    window.TradeUtils.fetchMarketDataForLegs = async function () {
        console.log("Fetching market data for legs");

        try {

            // Reset all panels to loading state
            window.TradeUtils.resetAllPanelsLoadingState(true);

            // Get app details
            const appUuid = window.TradeUtils.state.activeTabUuid;
            if (!appUuid) {
                console.error('No active tab UUID found');
                return false;
            }

            const app = window.TradeUtils.state.authenticatedApps.find(app => app.uuid === appUuid);
            if (!app) {
                console.error('App not found with UUID:', appUuid);
                return false;
            }

            // Get enabled legs from state
            const selectedLegs = window.TradeUtils.state.selectedLegs || [];
            const enabledLegs = selectedLegs.filter(leg => leg.enabled !== false);

            if (!enabledLegs.length) {
                console.warn('No enabled legs found for market data fetch');
                return false;
            }

            // Map of legId -> panel for later use
            const legPanels = new Map();

            // Process legs into three categories:
            // 1. Legs with cached instrument IDs
            // 2. Legs that need instrument IDs from the server
            // 3. Combined list of all legs with instrument IDs (to be filled)
            const legsWithInstrumentIds = [];
            const legsToFetch = [];

            // Sort legs into appropriate categories
            enabledLegs.forEach(leg => {
                // Find the panel for this leg
                const panel = document.querySelector(`.order-panel[data-leg-id="${leg.id}"]`);
                if (!panel || panel.classList.contains('disabled')) return;

                // Store panel reference
                legPanels.set(leg.id, panel);

                // Check if we already have the instrument ID cached
                if (window.TradeUtils.state.legToInstrumentMap.has(leg.id)) {
                    // Use cached instrument ID
                    const instrumentId = window.TradeUtils.state.legToInstrumentMap.get(leg.id);
                    const tradingSymbol = window.TradeUtils.state.legToSymbolMap.get(leg.id) || '';

                    legsWithInstrumentIds.push({
                        legId: leg.id,
                        instrumentId: instrumentId,
                        tradingSymbol: tradingSymbol,
                        exchange: "NSE_FNO" // Default exchange
                    });

                    console.log(`Using cached instrument ID ${instrumentId} for leg ${leg.id}`);
                } else {
                    // Need to fetch from server
                    legsToFetch.push({
                        id: leg.id,
                        symbol: leg.symbol || window.TradeUtils.state.symbol,
                        type: leg.type,
                        expiryDate: leg.expiryDate,
                        strike: leg.type === 'FUT' ? undefined : leg.strike,
                        tr_type: leg.action === 'buy' ? 'b' : 's',
                        lots: leg.quantity,
                        entry_price: leg.price,
                        op_pr: leg.price,
                        lot_size: leg.lotSize,
                        iv: leg.iv || 0
                    });
                }
            });

            // Fetch missing instrument IDs if needed
            if (legsToFetch.length > 0) {
                console.log(`Fetching instrument IDs for ${legsToFetch.length} legs from server`);
                try {
                    const response = await fetch(`/apps/get-instrument-ids-for-legs?broker=${app.key}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(legsToFetch)
                    });

                    if (!response.ok) {
                        throw new Error(`Failed to fetch instrument IDs: ${response.status}`);
                    }

                    const data = await response.json();

                    // Process fetched legs
                    if (data.legs && Array.isArray(data.legs)) {
                        data.legs.forEach(leg => {
                            const legId = leg.id;
                            const instrumentId = leg.instrument_id;
                            const tradingSymbol = leg.trading_symbol;

                            if (!instrumentId) {
                                console.error(`No instrument ID found for leg ${legId}`);
                                return;
                            }

                            // Store in our persistent maps
                            window.TradeUtils.state.legToInstrumentMap.set(legId, instrumentId);
                            window.TradeUtils.state.legToSymbolMap.set(legId, tradingSymbol);

                            // Add to our list of legs with instrument IDs
                            legsWithInstrumentIds.push({
                                legId: legId,
                                instrumentId: instrumentId,
                                tradingSymbol: tradingSymbol,
                                exchange: "NSE_FNO" // Default exchange
                            });
                        });
                    } else {
                        console.error('Invalid response from get-instrument-ids-for-legs:', data);
                    }
                } catch (error) {
                    console.error('Error fetching instrument IDs:', error);
                }
            }

            // If we have no legs with instrument IDs after all that, we're done
            if (legsWithInstrumentIds.length === 0) {
                console.warn('No valid instrument IDs found for any legs');
                return false;
            }

            // Setup for WebSocket communication
            // Only setup the socket if needed, don't unsubscribe existing connections
            if (!window.TradeUtils.state.marketDataSocket) {
                // Setup new socket connection
                if (!window.TradeUtils.setupMarketDataSocket()) {
                    console.error('Failed to set up market data socket');
                    return false;
                }
            }

            // Build a map of current instrument IDs
            const existingInstrumentIds = new Set();
            window.TradeUtils.state.activeMarketDataSubscriptions.forEach((sub) => {
                existingInstrumentIds.add(sub.instrumentId.toString());
            });

            // Subscribe to each instrument individually
            let newSubscriptionsCount = 0;
            // Remove the loading state
            window.TradeUtils.resetAllPanelsLoadingState(false);
            legsWithInstrumentIds.forEach(item => {
                // Skip if we're already subscribed to this leg
                if (window.TradeUtils.state.activeMarketDataSubscriptions.has(item.legId)) {
                    console.log(`Already subscribed to leg ${item.legId}, skipping`);
                    return;
                }

                // Skip if we're already subscribed to this instrument
                if (existingInstrumentIds.has(item.instrumentId.toString())) {
                    console.log(`Already subscribed to instrument ${item.instrumentId}, mapping to leg ${item.legId}`);
                    // Just update the mapping
                    window.TradeUtils.state.instrumentToLegMap.set(item.instrumentId.toString(), item.legId);
                    return;
                }

                // Call the subscription function for this leg
                window.TradeUtils.subscribeToLegMarketData(item.legId, item.instrumentId, item.exchange);

                // Store in the instrument-to-leg mapping
                window.TradeUtils.state.instrumentToLegMap.set(item.instrumentId.toString(), item.legId);

                newSubscriptionsCount++;
            });
            console.log(`Subscribed to ${newSubscriptionsCount} new instruments individually`);
            return true;
        } catch (error) {
            window.TradeUtils.resetAllPanelsLoadingState(false);
            console.error("Error fetching market data:", error);
            return false;
        }
    };

    // Subscribe to market data for a leg
    window.TradeUtils.subscribeToLegMarketData = function (legId, instrumentId, exchange = "NSE_FNO") {
        // Skip if already subscribed
        if (window.TradeUtils.state.activeMarketDataSubscriptions.has(legId)) {
            return;
        }

        // Ensure socket is connected
        if (!window.TradeUtils.state.marketDataSocket) {
            console.log("Setting up market data socket");
            window.TradeUtils.setupMarketDataSocket();
        }

        if (!window.TradeUtils.state.marketDataSocket) {
            console.error("Failed to set up market data socket");
            return;
        }

        const subscriptionRequest = {
            exchangeSegment: exchange,
            securityIds: [instrumentId.toString()],
        };

        console.log('subscription request', subscriptionRequest);


        // First publish the subscription request
        window.TradeUtils.state.marketDataSocket.publish({
            key: "Zr6yJ4lM8Kd_7dLEV2hAK0rIBimDTsbb",
            channel: "streamer/",
            message: JSON.stringify(subscriptionRequest)
        });

        // Then subscribe to the channel
        const subscription = window.TradeUtils.state.marketDataSocket.subscribe({
            key: "Zr6yJ4lM8Kd_7dLEV2hAK0rIBimDTsbb",
            channel: `streamer/${instrumentId}/${exchange}`,
            last: 1
        });

        // Store the subscription
        window.TradeUtils.state.activeMarketDataSubscriptions.set(legId, {
            subscription: subscription,
            instrumentId: instrumentId,
            exchange: exchange
        });

        console.log(`Subscribed to market data for leg ${legId} (ID: ${instrumentId})`);
    };


    // Unsubscribe from market data for a leg
    window.TradeUtils.unsubscribeFromLegMarketData = function (legId) {
        const subscription = window.TradeUtils.state.activeMarketDataSubscriptions.get(legId);
        if (!subscription) {
            console.log(`No subscription found for leg ${legId}`);
            return;
        }

        if (!window.TradeUtils.state.marketDataSocket || !window.TradeUtils.state.marketDataSocket.connected) {
            // console.log(`Socket already disconnected for leg ${legId}, just cleaning up state`);
            window.TradeUtils.state.activeMarketDataSubscriptions.delete(legId);
            return;
        }

        try {
            console.log(`Unsubscribing from market data for leg ${legId} (ID: ${subscription.instrumentId})`);
            window.TradeUtils.state.marketDataSocket.unsubscribe({
                key: "Zr6yJ4lM8Kd_7dLEV2hAK0rIBimDTsbb",
                channel: `streamer/${subscription.instrumentId}/${subscription.exchange}`
            });
        } catch (error) {
            console.error(`Error unsubscribing from leg ${legId}:`, error);
        } finally {
            // Always clean up subscription state even if unsubscribe fails
            window.TradeUtils.state.activeMarketDataSubscriptions.delete(legId);
        }
    };

    // Market da            ta parser function (copied from the user's code)
    window.TradeUtils.parseBinaryData = function (binaryData) {
        const buffer = new ArrayBuffer(binaryData.length);
        const uint8View = new Uint8Array(buffer);
        uint8View.set(binaryData);

        const view = new DataView(buffer);
        let offset = 0;

        const data = {
            LTP: view.getFloat32(offset, true),
            LTQ: view.getUint16(offset + 4, true),
            LTT: view.getUint32(offset + 6, true),
            AvgPrice: view.getFloat32(offset + 10, true),
            Volume: view.getUint32(offset + 14, true),
            TotalSellQty: view.getUint32(offset + 18, true),
            TotalBuyQty: view.getUint32(offset + 22, true),
            OI: view.getUint32(offset + 26, true),
            OIDayHigh: view.getUint32(offset + 30, true),
            OIDayLow: view.getUint32(offset + 34, true),
            Open: view.getFloat32(offset + 38, true),
            Close: view.getFloat32(offset + 42, true),
            High: view.getFloat32(offset + 46, true),
            Low: view.getFloat32(offset + 50, true),
            Depth: [],
        };

        offset = 54;
        for (let i = 0; i < 5; i++) {
            const depthPacket = {
                BidQuantity: view.getUint32(offset, true),
                AskQuantity: view.getUint32(offset + 4, true),
                BidOrders: view.getUint16(offset + 8, true),
                AskOrders: view.getUint16(offset + 10, true),
                BidPrice: view.getFloat32(offset + 12, true),
                AskPrice: view.getFloat32(offset + 16, true),
            };
            data.Depth.push(depthPacket);
            offset += 20;
        }

        return data;
    };

    // Handle incoming market data message
    window.TradeUtils.handleMarketDataMessage = function (msg) {
        try {
            if (!msg.binary) return;

            const buffer = msg.binary.buffer.slice(
                msg.binary.byteOffset,
                msg.binary.byteOffset + msg.binary.byteLength
            );

            // Extract instrument ID from channel
            const instrumentId = msg.channel.split('/')[1];
            if (!instrumentId) {
                console.warn("Could not extract instrument ID from channel:", msg.channel);
                return;
            }

            // Find leg ID that corresponds to this instrument ID
            let targetLegId = null;

            // First check the map for faster lookup
            if (window.TradeUtils.state.instrumentToLegMap && window.TradeUtils.state.instrumentToLegMap.has(instrumentId)) {
                targetLegId = window.TradeUtils.state.instrumentToLegMap.get(instrumentId);
            } else {
                // Fallback to iterating through subscriptions
                window.TradeUtils.state.activeMarketDataSubscriptions.forEach((sub, legId) => {
                    if (sub.instrumentId.toString() === instrumentId) {
                        targetLegId = legId;
                    }
                });
            }

            if (!targetLegId) {
                console.warn("No matching leg found for instrument ID:", instrumentId);
                return;
            }

            // Parse binary data
            const marketData = window.TradeUtils.parseBinaryData(new Uint8Array(buffer));

            // Update the panel with the new data
            window.TradeUtils.updatePanelWithMarketData(targetLegId, marketData);

        } catch (e) {
            window.TradeUtils.resetAllPanelsLoadingState(false);
            console.error("Error processing market data update:", e);
        }
    };

    // Update panel with market data summary (details section)
    window.TradeUtils.updateMarketDataSummary = function (panel, marketData) {
        if (!panel || !marketData) return;

        // Find market data elements and update them
        const fields = {
            'ltp': marketData.LTP,
            'open': marketData.Open,
            'high': marketData.High,
            'low': marketData.Low,
            'close': marketData.Close,
            'volume': marketData.Volume,
            'oi': marketData.OI,
            'ltt': marketData.LTT,
            'avgprice': marketData.AvgPrice
        };

        for (const [key, value] of Object.entries(fields)) {
            if (value === undefined) continue;

            const el = panel.querySelector(`.market-data-${key}`);
            if (el) {
                if (key === 'oi' || key === 'volume') {
                    el.textContent = value.toLocaleString();
                } else if (key === 'ltt') {
                    // Format LTT as HH:MM:SS without timezone conversion
                    const date = new Date(value * 1000);
                    const hours = date.getUTCHours().toString().padStart(2, '0');
                    const minutes = date.getUTCMinutes().toString().padStart(2, '0');
                    const seconds = date.getUTCSeconds().toString().padStart(2, '0');
                    el.textContent = `${hours}:${minutes}:${seconds}`;
                } else {
                    el.textContent = value.toFixed(2);
                }
            }
        }
    };

    // Update order book with depth data
    window.TradeUtils.updateOrderBook = function (depth, legId) {
        // Get the container for this leg's order book
        const panel = document.querySelector(`.order-panel[data-leg-id="${legId}"]`);
        if (!panel) return;

        // Find order book container in this panel
        const tbody = panel.querySelector('.order-book-body');
        if (!tbody) return;

        // Clear existing rows
        tbody.innerHTML = '';

        // Sort depth for bids (descending) and asks (ascending)
        const bids = [];
        const asks = [];

        let totalBidQty = 0;
        let totalAskQty = 0;

        // Process depth data
        depth.forEach(item => {
            if (item.BidPrice && item.BidQuantity > 0) {
                bids.push({
                    price: item.BidPrice,
                    quantity: item.BidQuantity,
                    orders: item.BidOrders
                });
                totalBidQty += item.BidQuantity;
            }

            if (item.AskPrice && item.AskQuantity > 0) {
                asks.push({
                    price: item.AskPrice,
                    quantity: item.AskQuantity,
                    orders: item.AskOrders
                });
                totalAskQty += item.AskQuantity;
            }
        });

        // Sort bids in descending order (highest first)
        bids.sort((a, b) => b.price - a.price);

        // Sort asks in ascending order (lowest first)
        asks.sort((a, b) => a.price - b.price);

        // Update the totals
        const totalBidsElement = panel.querySelector('.total-bids');
        const totalOffersElement = panel.querySelector('.total-offers');

        if (totalBidsElement) totalBidsElement.textContent = totalBidQty.toLocaleString();
        if (totalOffersElement) totalOffersElement.textContent = totalAskQty.toLocaleString();

        // Create rows for the order book
        const maxRows = 5; // Match the image with exactly 5 rows

        for (let i = 0; i < maxRows; i++) {
            const bid = bids[i] || { price: '', quantity: '', orders: '' };
            const ask = asks[i] || { price: '', quantity: '', orders: '' };

            const row = document.createElement('tr');
            row.className = i % 2 === 0 ? 'bg-white' : 'bg-gray-50';

            row.innerHTML = `
                <td class="py-1 px-2 text-center text-green-600 font-semibold">${bid.price ? bid.price.toFixed(2) : ''}</td>
                <td class="py-1 px-2 text-center text-gray-700">${bid.orders ? bid.orders.toLocaleString() : ''}</td>
                <td class="py-1 px-2 text-center text-gray-700">${bid.quantity ? bid.quantity.toLocaleString() : ''}</td>
                <td class="py-1 px-2 text-center text-gray-700">${ask.quantity ? ask.quantity.toLocaleString() : ''}</td>
                <td class="py-1 px-2 text-center text-gray-700">${ask.orders ? ask.orders.toLocaleString() : ''}</td>
                <td class="py-1 px-2 text-center text-red-600 font-semibold">${ask.price ? ask.price.toFixed(2) : ''}</td>
            `;

            tbody.appendChild(row);
        }
    };

    // Update panel with market data
    window.TradeUtils.updatePanelWithMarketData = function (legId, marketData) {
        const panel = document.querySelector(`.order-panel[data-leg-id="${legId}"]`);
        if (!panel) return;

        // Update market data summary
        window.TradeUtils.updateMarketDataSummary(panel, marketData);

        // Update order book with depth data
        if (marketData.Depth && marketData.Depth.length > 0) {
            window.TradeUtils.updateOrderBook(marketData.Depth, legId);
        }
    };

    // Reset all panels loading state
    window.TradeUtils.resetAllPanelsLoadingState = function (showSpinner = true) {
        // Use selectedLegs directly from state
        const selectedLegs = window.TradeUtils.state.selectedLegs || [];

        // Filter to only enabled legs
        const enabledLegs = selectedLegs.filter(leg => leg.enabled !== false);

        // Set loading state for each leg's panel
        enabledLegs.forEach(leg => {
            const panel = document.querySelector(`.order-panel[data-leg-id="${leg.id}"]`);
            if (panel) {
                window.TradeUtils.resetPanelLoadingState(panel, showSpinner);
            }
        });
    };

    // Reset loading state for a single panel
    window.TradeUtils.resetPanelLoadingState = function (panel, showSpinner = true) {
        if (!panel) return;
        const marketDataElements = panel.querySelectorAll('.market-data');

        marketDataElements.forEach(element => {
            if (element) {
                if (showSpinner) {
                    // Create a small spinner
                    element.innerHTML = `
                        <div class="flex justify-end">
                        <div class="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                `;
                } else {
                    element.innerHTML = '--';
                }
            }
        });

        // Order book loading state - set placeholder values
        const orderBookBody = panel.querySelector('.order-book-body');
        if (orderBookBody) {
            // Clear existing rows and add loading rows
            orderBookBody.innerHTML = '';

            // Create 5 loading rows
            for (let i = 0; i < 5; i++) {
                const row = document.createElement('tr');
                row.className = i % 2 === 0 ? 'bg-white' : 'bg-gray-50';
                if (showSpinner) {
                    row.innerHTML = `
                    <td class="py-1 px-2 text-center text-green-600 font-semibold">
                        <div class="flex justify-center">
                            <div class="w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
                        </div>
                    </td>
                    <td class="py-1 px-2 text-center text-gray-700">
                        <div class="flex justify-center">
                            <div class="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                        </div>
                    </td>
                    <td class="py-1 px-2 text-center text-gray-700">
                        <div class="flex justify-center">
                            <div class="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                        </div>
                    </td>
                    <td class="py-1 px-2 text-center text-gray-700">
                        <div class="flex justify-center">
                            <div class="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                        </div>
                    </td>
                    <td class="py-1 px-2 text-center text-gray-700">
                        <div class="flex justify-center">
                            <div class="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                        </div>
                    </td>
                    <td class="py-1 px-2 text-center text-red-600 font-semibold">
                        <div class="flex justify-center">
                            <div class="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                        </div>
                    </td>
                `;
                } else {
                    row.innerHTML = `
                        <td class="py-1 px-2 text-center text-green-600 font-semibold">--</td>
                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                        <td class="py-1 px-2 text-center text-gray-700">--</td>
                        <td class="py-1 px-2 text-center text-red-600 font-semibold">--</td>
                    `;
                }

                orderBookBody.appendChild(row);
            }
        }

        // Reset totals with spinners
        const totalBidsElement = panel.querySelector('.total-bids');
        const totalOffersElement = panel.querySelector('.total-offers');

        if (totalBidsElement) {
            if (showSpinner) {
                totalBidsElement.innerHTML = `
                    <div class="inline-flex">
                        <div class="w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                `;
            } else {
                totalBidsElement.innerHTML = '--';
            }
        }

        if (totalOffersElement) {
            if (showSpinner) {
                totalOffersElement.innerHTML = `
                    <div class="inline-flex">
                        <div class="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                    </div>
            `;
            } else {
                totalOffersElement.innerHTML = '--';
            }
        }
    };

    // Function to update LTP values in all price inputs from market data
    window.TradeUtils.updateLTPForAllPanels = function () {
        // Get all the active subscriptions with their leg IDs
        window.TradeUtils.state.activeMarketDataSubscriptions.forEach((subscription, legId) => {
            const panel = document.querySelector(`.order-panel[data-leg-id="${legId}"]`);
            if (!panel) return;

            // Find the price input for this panel
            const ltpInput = panel.querySelector('[name="price"]');
            if (!ltpInput) return;

            // Find the LTP value from the market data summary
            const ltpElement = panel.querySelector('.market-data-ltp');
            if (ltpElement && ltpElement.textContent && !isNaN(parseFloat(ltpElement.textContent))) {
                // Update the price input with the LTP value
                ltpInput.value = parseFloat(ltpElement.textContent).toFixed(2);

                // Trigger change event to update calculations
                const event = new Event('change', { bubbles: true });
                ltpInput.dispatchEvent(event);

                console.log(`Updated price for leg ${legId} to ${ltpInput.value}`);
            }
        });
    };
</script>