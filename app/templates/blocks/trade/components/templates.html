<script>
    // Define HTML templates for trading panel UI components
    window.TradeUtils.templates = {
        tradingPanel: `
            <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                <div class="bg-white rounded-lg shadow-lg w-full max-w-5xl max-h-[85vh] flex flex-col">
                    <!-- Mo<PERSON> Header -->
                    <div class="flex items-center justify-between px-4 py-3 border-b">
                        <div class="flex items-center space-x-4">
                            <!-- Symbol and Price Display -->
                            <div class="font-semibold text-gray-900">
                                <span id="trading-panel-symbol">NIFTY</span>
                                <span id="trading-panel-price"></span>
                            </div>

                            <!-- Intraday/Overnight Radio Buttons -->
                            <div class="flex items-center">
                                <div class="flex text-sm order-duration-controls">
                                    <label class="inline-flex items-center">
                                        <input type="radio" name="order-duration" class="mr-1 order-duration-radio" value="intraday" checked="checked">
                                        <span>Intraday</span>
                                    </label>
                                    <label class="inline-flex items-center ml-5">
                                        <input type="radio" name="order-duration" class="mr-1 order-duration-radio" value="overnight">
                                        <span>Overnight</span>
                                    </label>
                                </div>

                                <!-- Help Icon with Tooltip -->
                                <div class="relative group ml-1">
                                    <button class="text-gray-400 hover:text-gray-600 h-6 w-6 flex items-center justify-center rounded-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                    <!-- Tooltip -->
                                    <div class="absolute z-10 w-64 bg-gray-800 text-white text-xs rounded p-2 opacity-0 group-hover:opacity-100 transition-opacity bottom-full mb-1 left-1/2 -translate-x-1/2 pointer-events-none">
                                        <strong>Intraday</strong> - Has to be exited by you before market close on the same day. If you do not exit, your broker may square off the positions from this order.
                                        <br><br>
                                        <strong>Overnight</strong> - These positions are not closed at the end of the trading day. They are carried over to the next days and stay open till you manually exit them.
                                    </div>
                                </div>
                            </div>


                            <!-- Update Prices Button -->
                            <button class="text-sm border border-gray-300 rounded py-1 px-3 bg-blue-50 text-blue-600 hover:bg-blue-100 flex items-center space-x-1">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                </svg>
                                <span>Update Prices</span>
                            </button>
                        </div>
                        <button id="close-trading-modal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Tabs Navigation -->
                    <div class="border-b">
                        <ul id="trading-tabs-nav" class="flex px-4 overflow-x-auto custom-scroll scrollbar-thin whitespace-nowrap">
                            <!-- Tabs will be dynamically inserted here -->
                        </ul>
                    </div>

                    <!-- Tab Content -->
                    <div id="trading-tabs-content" class="flex-1 overflow-y-auto custom-scroll">
                        <!-- Tab content will be dynamically inserted here -->
                    </div>

                    <!-- Footer with Margin Details -->
                    <div id="trading-footer" class="border-t p-3 bg-gray-50 hidden">
                        <div class="grid grid-cols-3 gap-4">
                            <!-- Margin Info -->
                            <div class="flex space-x-4">
                                <div class="bg-gray-100 border rounded p-2 flex-1">
                                    <div class="text-xs text-gray-500">Margin Needed</div>
                                    <div class="flex items-center">
                                        <span id="margin-needed" class="font-medium text-sm">0</span>
                                        <button class="ml-1 text-gray-400 hover:text-gray-600" id="margin-info-btn">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="bg-gray-100 border rounded p-2 flex-1">
                                    <div class="text-xs text-gray-500">Margin Available</div>
                                    <div id="margin-available" class="font-medium text-sm">0</div>
                                </div>
                            </div>

                            <!-- Charges Link -->
                            <div class="flex justify-center items-center">
                                <a href="#" id="charges-link" class="text-blue-500 hover:underline text-sm">Charges</a>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex justify-end items-center space-x-2">
                                <button id="rearrange-orders-btn" class="hidden text-blue-600 border border-blue-500 rounded px-3 py-1.5 text-sm hover:bg-blue-50">
                                    Re-arrange orders
                                </button>
                                <div class="relative">
                                    <button id="place-market-btn" class="bg-blue-600 text-white rounded px-3 py-1.5 text-sm hover:bg-blue-700 flex items-center">
                                        Place All at Market
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                    <div id="place-market-dropdown" class="absolute right-0 mt-1 w-48 bg-white rounded shadow-lg z-10 hidden">
                                        <ul class="py-1 text-sm">
                                            <li>
                                                <a href="#" class="block px-4 py-2 hover:bg-gray-100" data-action="market">Place All at Market</a>
                                            </li>
                                            <li>
                                                <a href="#" class="block px-4 py-2 hover:bg-gray-100" data-action="limit">Place All as Limit</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `,
        loader: `
            <div class="flex flex-col items-center justify-center p-8">
                <div class="w-12 h-12 border-4 border-blue-400 border-t-transparent rounded-full animate-spin mb-4"></div>
                <p class="text-gray-600">Loading trading apps...</p>
            </div>
        `,
        noApps: `
            <div class="flex flex-col items-center justify-center p-8">
                <div class="w-16 h-16 text-gray-300 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                </div>
                <p class="text-gray-600 mb-2">No authenticated trading apps available.</p>
                <p class="text-sm text-gray-500">Please connect apps from the settings page.</p>
                <button class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm" id="close-no-apps-btn">Close</button>
            </div>
        `,
        chargesModal: `
            <div class="p-4">
                <h3 class="text-lg font-semibold mb-4">Estimated Charges</h3>
                <p class="text-sm text-gray-600 mb-4">
                    These are approximate charges based on the selected orders. Actual charges may vary.
                </p>
                <div class="overflow-x-auto mb-4">
                    <table class="w-full text-sm border-collapse">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="p-2 text-left border">Charge Type</th>
                                <th class="p-2 text-center border">Futures</th>
                                <th class="p-2 text-center border">Options</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="p-2 border">STT</td>
                                <td class="p-2 border text-center">0.02% <br><span class="text-xs text-gray-500">(SELL only)</span></td>
                                <td class="p-2 border text-center">0.1% on premium <br><span class="text-xs text-gray-500">(SELL only)</span></td>
                            </tr>
                            <tr>
                                <td class="p-2 border">Stamp Duty</td>
                                <td class="p-2 border text-center">0.002% <br><span class="text-xs text-gray-500">(BUY only)</span></td>
                                <td class="p-2 border text-center">0.003% <br><span class="text-xs text-gray-500">(BUY only)</span></td>
                            </tr>
                            <tr>
                                <td class="p-2 border">Exchange Transaction</td>
                                <td class="p-2 border text-center">NSE: 0.00173%<br>BSE: 0.00%</td>
                                <td class="p-2 border text-center">NSE: 0.03503%<br>BSE: 0.0325%<br><span class="text-xs text-gray-500">(on premium)</span></td>
                            </tr>
                            <tr>
                                <td class="p-2 border">SEBI Turnover</td>
                                <td class="p-2 border text-center">0.0001%</td>
                                <td class="p-2 border text-center">0.0001%</td>
                            </tr>
                            <tr>
                                <td class="p-2 border">GST</td>
                                <td class="p-2 border text-center" colspan="2">18% on brokerage and exchange transaction charges</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <p class="text-xs text-gray-500 mt-2">
                    Note: Brokerage charges vary by broker. The above charges are based on Groww.in and may differ for other brokers.
                </p>
            </div>
        `
    };
</script>