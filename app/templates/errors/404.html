<!DOCTYPE html>
<html lang="en">

<head>
    {%include 'blocks/head.html' %}
    <title>Page Not Found - {{ APP_NAME }}</title>
    <meta name="description"
        content="The page you're looking for could not be found. Return to AI Bull for stock analysis and trading tools.">
    <meta name="robots" content="noindex, nofollow">
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    <!-- Include common components -->
    {% include 'blocks/common.html' %}

    <!-- Main Error Content -->
    <div class="min-h-screen flex items-center justify-center px-4 py-16">
        <div class="max-w-2xl mx-auto text-center">

            <!-- Error Illustration -->
            <div class="mb-8">
                <div
                    class="mx-auto w-64 h-64 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 rounded-full flex items-center justify-center">
                    <div class="text-8xl font-bold text-blue-600 dark:text-blue-400">404</div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="mb-8">
                <h1 class="text-4xl md:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 heading">
                    Page Not Found
                </h1>
                <p class="text-lg text-neutral-600 dark:text-neutral-400 mb-6">
                    Oops! The page you're looking for seems to have wandered off into the market volatility.
                    Don't worry, we'll help you get back on track.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <a href="/"
                    class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6">
                        </path>
                    </svg>
                    Back to Home
                </a>

                <a href="/app"
                    class="inline-flex items-center px-6 py-3 bg-neutral-200 hover:bg-neutral-300 dark:bg-neutral-700 dark:hover:bg-neutral-600 text-neutral-900 dark:text-neutral-100 font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 012 2v6a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                        </path>
                    </svg>
                    Go to Dashboard
                </a>
            </div>

            <!-- Popular Links -->
            <div class="border-t border-neutral-200 dark:border-neutral-700 pt-8">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
                    Popular Pages
                </h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 text-sm max-w-xl mx-auto">
                    <a href="/stocks" class="flex items-center justify-center px-4 py-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-800/30 transition-colors duration-200 group">
                        <span class="text-blue-700 dark:text-blue-300 font-medium group-hover:text-blue-800 dark:group-hover:text-blue-200">Stock Screener</span>
                    </a>
                    <a href="/options/chain" class="flex items-center justify-center px-4 py-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-800/30 transition-colors duration-200 group">
                        <span class="text-blue-700 dark:text-blue-300 font-medium group-hover:text-blue-800 dark:group-hover:text-blue-200">Options Chain</span>
                    </a>
                    <a href="/portfolio" class="flex items-center justify-center px-4 py-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-800/30 transition-colors duration-200 group">
                        <span class="text-blue-700 dark:text-blue-300 font-medium group-hover:text-blue-800 dark:group-hover:text-blue-200">Portfolio</span>
                    </a>
                    <a href="/mutual-funds" class="flex items-center justify-center px-4 py-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-800/30 transition-colors duration-200 group">
                        <span class="text-blue-700 dark:text-blue-300 font-medium group-hover:text-blue-800 dark:group-hover:text-blue-200">Mutual Funds</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    {% include 'blocks/footer.html' %}
</body>

</html>