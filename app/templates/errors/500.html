<!DOCTYPE html>
<html lang="en">

{%include 'blocks/head.html' %}

<head>
    <title>Server Error - {{ APP_NAME }}</title>
    <meta name="description"
        content="We're experiencing technical difficulties. Our team is working to resolve the issue.">
    <meta name="robots" content="noindex, nofollow">
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    <!-- Include common components -->
    {% include 'blocks/common.html' %}

    <!-- Main Error Content -->
    <div class="min-h-screen flex items-center justify-center px-4 py-16">
        <div class="max-w-2xl mx-auto text-center">

            <!-- Error Illustration -->
            <div class="mb-8">
                <div
                    class="mx-auto w-64 h-64 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900 dark:to-red-800 rounded-full flex items-center justify-center">
                    <div class="text-8xl font-bold text-red-600 dark:text-red-400">500</div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="mb-8">
                <h1 class="text-4xl md:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 heading">
                    Server Error
                </h1>
                <p class="text-lg text-neutral-600 dark:text-neutral-400 mb-6">
                    We're experiencing some technical difficulties on our end.
                    Our team has been notified and is working to resolve the issue as quickly as possible.
                </p>
            </div>

            <!-- Status Information -->
            <div
                class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-8">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-3" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 9v2m0 4h.01m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z">
                        </path>
                    </svg>
                    <div class="text-left">
                        <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                            Service Temporarily Unavailable
                        </p>
                        <p class="text-sm text-yellow-700 dark:text-yellow-300">
                            Error ID: {{ error_id if error_id else 'N/A' }} | Time: {{ timestamp if timestamp else
                            'Unknown' }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <button onclick="window.location.reload()"
                    class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                        </path>
                    </svg>
                    Try Again
                </button>

                <a href="/"
                    class="inline-flex items-center px-6 py-3 bg-neutral-200 hover:bg-neutral-300 dark:bg-neutral-700 dark:hover:bg-neutral-600 text-neutral-900 dark:text-neutral-100 font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6">
                        </path>
                    </svg>
                    Back to Home
                </a>
            </div>

            <!-- Report Issue -->
            <div class="border-t border-neutral-200 dark:border-neutral-700 pt-8">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
                    Need Help?
                </h3>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <button id="contactSupportBtn"
                        class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                            </path>
                        </svg>
                        Contact Our Support Team
                    </button>
                    <span class="text-neutral-500 dark:text-neutral-400 text-sm">
                        We typically respond within 24 hours
                    </span>
                </div>
            </div>

            <!-- What you can do -->
            <div class="mt-8 text-left max-w-md mx-auto">
                <h4 class="text-md font-medium text-neutral-900 dark:text-neutral-100 mb-3">
                    What you can do:
                </h4>
                <ul class="text-sm text-neutral-600 dark:text-neutral-400 space-y-2">
                    <li class="flex items-start">
                        <span class="text-blue-600 mr-2">•</span>
                        Wait a few minutes and try refreshing the page
                    </li>
                    <li class="flex items-start">
                        <span class="text-blue-600 mr-2">•</span>
                        Check if other pages on our site are working
                    </li>
                    <li class="flex items-start">
                        <span class="text-blue-600 mr-2">•</span>
                        Clear your browser cache and cookies
                    </li>
                    <li class="flex items-start">
                        <span class="text-blue-600 mr-2">•</span>
                        Contact our support team if the problem persists
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Footer -->
    {% include 'blocks/footer.html' %}

    <script>
        // Error details for support contact
        const errorDetails = {
            errorId: '{{ error_id if error_id else "N/A" }}',
            timestamp: '{{ timestamp if timestamp else "Unknown" }}',
            page: '{{ request.url if request else "Unknown" }}',
            userAgent: '{{ request.headers.get("user-agent", "Unknown") if request else "Unknown" }}'
        };

        // Contact support button handler
        document.getElementById('contactSupportBtn').addEventListener('click', function () {
            const errorMessage = `Server Error Report - Error ID: ${errorDetails.errorId} | Time: ${errorDetails.timestamp} | Page: ${errorDetails.page} | User Agent: ${errorDetails.userAgent}`;
            showContactUsModal('support', errorMessage);
        });
    </script>

</body>

</html>