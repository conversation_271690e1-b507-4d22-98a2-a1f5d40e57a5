<script>

    function renderResults(results) {
        const resultsContent = document.getElementById('results-content');
        if (!results.length) {
            resultsContent.innerHTML = '<div class="p-4 text-gray-600">No results.</div>';
            return;
        }
        // --- Tabbed UI ---
        let tabHeaders = '';
        let tabContents = '';
        results.forEach((res, idx) => {
            const tabId = `tab-${idx}`;
            const isActive = idx === 0;
            tabHeaders += `<button class="tab-btn px-3 py-2 mr-2 mb-2 rounded border font-semibold${isActive ? ' bg-blue-700 text-white' : ' bg-white text-blue-700'}" data-tab="${tabId}">${res.symbol}</button>`;
            let content = '';
            if (res.error) {
                content = `<div class="text-red-600">${res.error}</div>`;
            } else if (res.data && res.data.ohlc_data) {
                content = renderStockTable(res.data.ohlc_data);
            } else {
                content = `<div class="text-gray-500">No data.</div>`;
            }
            tabContents += `<div id="${tabId}" class="tab-content" style="display:${isActive ? 'block' : 'none'};">${content}</div>`;
        });
        resultsContent.innerHTML = `
        <div class="mb-2 flex flex-wrap">${tabHeaders}</div>
        <div>${tabContents}</div>
    `;
        // Tab switching logic
        const tabBtns = resultsContent.querySelectorAll('.tab-btn');
        const tabDivs = resultsContent.querySelectorAll('.tab-content');
        tabBtns.forEach((btn, idx) => {
            btn.addEventListener('click', function () {
                tabBtns.forEach((b, i) => {
                    b.classList.remove('bg-blue-700', 'text-white', 'bg-white', 'text-blue-700');
                    b.classList.add('bg-white', 'text-blue-700');
                });
                tabDivs.forEach(div => div.style.display = 'none');
                btn.classList.remove('bg-white', 'text-blue-700');
                btn.classList.add('bg-blue-700', 'text-white');
                const tabId = btn.getAttribute('data-tab');
                const tabDiv = resultsContent.querySelector(`#${tabId}`);
                if (tabDiv) {
                    tabDiv.style.display = 'block';
                }
            });
        });
    }
    function renderStockTable(ohlcData, matched) {
        // console.log("Rendering stock table with ohlcData:", ohlcData);
        // console.log("Matched data:", matched);
        matched = matched || [];
        const keys = Object.keys(ohlcData).sort((a, b) => Number(a) - Number(b));
        if (!keys.length) return '<div class="text-gray-500">No candles found.</div>';
        let html = `<div class='overflow-x-auto w-full'>
<table class="min-w-full text-xs border border-gray-300 rounded-lg overflow-hidden shadow-sm mb-2">
<thead class="bg-gray-100 sticky top-0 z-10">
    <tr>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 left-0 bg-gray-100 z-30 text-neutral-800 dark:text-neutral-100">Time</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20 text-neutral-800 dark:text-neutral-100">O</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20 text-neutral-800 dark:text-neutral-100">H</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20 text-neutral-800 dark:text-neutral-100">L</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20 text-neutral-800 dark:text-neutral-100">C</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20 text-neutral-800 dark:text-neutral-100">V</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">Supertrend</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">Patterns</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">RSI</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">EMA9</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">EMA20</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">EMA50</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">EMA100</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">EMA200</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">VWAP</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">ADX</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">MACD</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">MACD Signal</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">MACD Hist</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">SMA</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">BB Upper</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">BB Middle</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">BB Lower</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">ATR</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">CCI</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">TEMA</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">STOCH K</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">STOCH D</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">MFI</th>
        <th class="px-2 py-2 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">SAR</th>
        <th class="px-2 py-1 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">Support</th>
        <th class="px-2 py-1 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">Resistance</th>
        <th class="px-2 py-1 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">Support Breakdown</th>
        <th class="px-2 py-1 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">Resistance Breakout</th>
        <th class="px-2 py-1 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">RSI Divergence</th>
        <th class="px-2 py-1 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">RSI Convergence</th>
        <th class="px-2 py-1 border-b border-gray-200 text-left sticky top-0 bg-gray-100 z-20">Volume</th>
    </tr>
</thead>
<tbody>`;
        for (const ts of keys) {
            const c = ohlcData[ts];
            const dt = new Date(Number(ts) * 1000);
            const time = dt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            // Highlight if matched
            const highlight = matched.includes(ts) ? 'bg-yellow-100' : '';
            // Supertrend color: green if close > supertrend, red if close < supertrend
            let supertrendVal = c.supertrend ?? '-';
            let supertrendColor = '';
            let supertrendDir = c.supertrend_dir;
            let supertrendArrow = '';
            if (typeof supertrendDir === 'number') {
                if (supertrendDir === 1) supertrendArrow = ' <span title="Up" style="color:green">&#8593;</span>';
                else if (supertrendDir === -1) supertrendArrow = ' <span title="Down" style="color:red">&#8595;</span>';
            }
            if (typeof c.supertrend === 'number' && typeof c.c === 'number') {
                if (c.c > c.supertrend) supertrendColor = 'text-green-600 font-bold';
                else if (c.c < c.supertrend) supertrendColor = 'text-red-600 font-bold';
            }
            let macdColor = '';
            if (typeof c.macd === 'number' && typeof c.macds === 'number') {
                if (c.macd > c.macds) macdColor = 'text-green-600 font-bold';
                else if (c.macd < c.macds) macdColor = 'text-red-600 font-bold';
            }
            let ema9Color = '';
            let ema20Color = '';
            let ema50Color = '';
            let ema100Color = '';
            let ema200Color = '';
            if (typeof c.ema9 === 'number' && typeof c.ema50 === 'number') {
                if (c.ema9 > c.ema50) ema9Color = 'text-green-600 font-bold';
                else if (c.ema9 < c.ema50) ema9Color = 'text-red-600 font-bold';
            }
            if (typeof c.ema20 === 'number' && typeof c.ema50 === 'number') {
                if (c.ema20 > c.ema50) ema20Color = 'text-green-600';
                else if (c.ema20 < c.ema50) ema20Color = 'text-red-600';
            }
            if (typeof c.ema50 === 'number' && typeof c.ema200 === 'number') {
                if (c.ema50 > c.ema200) ema50Color = 'text-green-600';
                else if (c.ema50 < c.ema200) ema50Color = 'text-red-600';
            }
            if (typeof c.ema100 === 'number' && typeof c.ema200 === 'number') {
                if (c.ema100 > c.ema200) ema100Color = 'text-green-600';
                else if (c.ema100 < c.ema200) ema100Color = 'text-red-600';
            }
            if (typeof c.ema200 === 'number' && typeof c.ema50 === 'number') {
                if (c.ema200 > c.ema50) ema200Color = 'text-green-600';
                else if (c.ema200 < c.ema50) ema200Color = 'text-red-600';
            }
            let rsiColor = '';
            if (typeof c.rsi === 'number') {
                if (c.rsi >= 70) rsiColor = 'text-red-600 font-bold'; // Overbought
                else if (c.rsi <= 30) rsiColor = 'text-green-600 font-bold'; // Oversold
            }
            let adxColor = '';
            if (typeof c.adx === 'number') {
                if (c.adx < 20) adxColor = 'text-gray-400'; // No/weak trend
                else if (c.adx < 25) adxColor = 'text-yellow-500'; // Starting trend
                else if (c.adx < 40) adxColor = 'text-green-600 font-bold'; // Strong trend
                else if (c.adx < 60) adxColor = 'text-blue-600 font-bold'; // Very strong trend
                else adxColor = 'text-red-600 font-bold'; // Extreme trend
            }
            let vwapColor = '';
            if (typeof c.vwap === 'number' && typeof c.atr === 'number' && typeof c.c === 'number') {
                if (c.c > c.vwap + c.atr) vwapColor = 'text-green-600 font-bold';
                else if (c.c < c.vwap - c.atr) vwapColor = 'text-red-600 font-bold';
                else vwapColor = 'text-yellow-500 font-bold';
            }
            html += `<tr class="${highlight}">
            <td class="px-2 py-1 border-b border-gray-100 sticky left-0 bg-white z-20">${time}</td>
            <td class="px-2 py-1 border-b border-gray-100">${c.o ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-100">${c.h ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-100">${c.l ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-100">${c.c ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-100">${c.v ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-100 ${supertrendColor}">${supertrendVal}${supertrendArrow}</td>
            <td class="px-2 py-1 border-b border-gray-100">${c.p ?? ''}</td>
            <td class="px-2 py-1 border-b border-gray-100 ${rsiColor}">${c.rsi ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-100 ${ema9Color}">${c.ema9 ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-100 ${ema20Color}">${c.ema20 ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-100 ${ema50Color}">${c.ema50 ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200 ${ema100Color}">${c.ema100 ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200 ${ema200Color}">${c.ema200 ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200 ${vwapColor}">${c.vwap ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200 ${adxColor}">${c.adx ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200 ${macdColor}">${c.macd ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.macds ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.macdh ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.sma ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.bb_upper ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.bb_middle ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.bb_lower ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.atr ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.cci ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.tema ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.stoch_k ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.stoch_d ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.mfi ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.sar ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.v ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.support_breakdown ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.resistance_breakout ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.rsi_divergence ?? '-'}</td>
            <td class="px-2 py-1 border-b border-gray-200">${c.rsi_convergence ?? '-'}</td>
        </tr>`;
        }
        html += '</tbody></table></div>';
        return html;
    }


</script>