<!-- Simple Screener Textarea UI with Multi-Timeframe Columns and Tag Templates at Bottom -->
<div class="mb-6" id="filter-section">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div>
            <label for="screener-5m" class="font-medium text-sm mb-2 block text-blue-700">5 min Conditions</label>
            <textarea id="screener-5m" name="screener_5m" rows="7" class="w-full border rounded px-3 py-2 text-sm mt-2"
                placeholder="e.g.
RSI &lt; 30
EMA9 &gt; EMA20
..."></textarea>
        </div>
        <div>
            <label for="screener-15m" class="font-medium text-sm mb-2 block text-purple-700">15 min Conditions</label>
            <textarea id="screener-15m" name="screener_15m" rows="7"
                class="w-full border rounded px-3 py-2 text-sm mt-2" placeholder="e.g.
RSI &lt; 30
..."></textarea>
        </div>
        <div>
            <label for="screener-30m" class="font-medium text-sm mb-2 block text-pink-700">30 min Conditions</label>
            <textarea id="screener-30m" name="screener_30m" rows="7"
                class="w-full border rounded px-3 py-2 text-sm mt-2" placeholder="e.g.
RSI &lt; 30
..."></textarea>
        </div>
    </div>
    <div class="flex flex-wrap gap-2 mb-2 text-xs">
        <span class="font-semibold text-blue-700 text-xs">Bullish:</span>

        <span tabindex="0" role="button"
            class="tag bg-blue-100 text-blue-700 border border-blue-200 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 text-xs"
            data-template="# Supertrend Bullish\nSuperTrend_Color[-1] = Red\nSuperTrend_Color = Green">Supertrend
            Bullish</span>

        <span tabindex="0" role="button"
            class="tag bg-blue-100 text-blue-700 border border-blue-200 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 text-xs"
            data-template="# EMA Bullish\nEMA9 > EMA20">EMA Bullish</span>

        <span tabindex="0" role="button"
            class="tag bg-blue-100 text-blue-700 border border-blue-200 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 text-xs"
            data-template="# MACD Bullish\nMACD > MACDS in last 5">MACD Bullish</span>

        <span tabindex="0" role="button"
            class="tag bg-blue-100 text-blue-700 border border-blue-200 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 text-xs"
            data-template="# RSI Bullish\nRSI > 50">RSI Bullish</span>

        <span tabindex="0" role="button"
            class="tag bg-blue-100 text-blue-700 border border-blue-200 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 text-xs"
            data-template="# RSI Oversold Cross\nRSI <= 30\nRSI[-1] > 30">RSI Oversold Cross</span>

        <span tabindex="0" role="button"
            class="tag bg-blue-100 text-blue-700 border border-blue-200 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 text-xs"
            data-template="# VWAP Strong Bullish\nVWAP > Close + ATR">VWAP Strong Bullish</span>

        <span tabindex="0" role="button"
            class="tag bg-blue-100 text-blue-700 border border-blue-200 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 text-xs"
            data-template="# Bollinger Band Bullish\nClose > BB_Upper">Bollinger Band Bullish</span>

        <span tabindex="0" role="button"
            class="tag bg-blue-100 text-blue-700 border border-blue-200 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 text-xs"
            data-template="# Stochastic Bullish\nStoch_K crosses above Stoch_D below 20">Stochastic Bullish</span>

        <span tabindex="0" role="button"
            class="tag bg-blue-100 text-blue-700 border border-blue-200 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 text-xs"
            data-template="# TEMA Bullish\nTEMA > EMA20">TEMA Bullish</span>

        <span tabindex="0" role="button"
            class="tag bg-blue-100 text-blue-700 border border-blue-200 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 text-xs"
            data-template="# CCI Bullish\nCCI < -100\nCCI > -100">CCI Bullish</span>

        <span tabindex="0" role="button"
            class="tag bg-blue-100 text-blue-700 border border-blue-200 rounded px-2 py-0.5 cursor-pointer hover:bg-blue-200 text-xs"
            data-template="# ATR Spike\nATR > 2 * ATR_Avg">ATR Spike</span>
    </div>

    <div class="flex flex-wrap gap-2 mb-2 text-xs">
        <span class="font-semibold text-red-700 text-xs">Bearish:</span>

        <span tabindex="0" role="button"
            class="tag bg-red-100 text-red-700 border border-red-200 rounded px-2 py-0.5 cursor-pointer hover:bg-red-200 text-xs"
            data-template="# Supertrend Bearish\nSuperTrend_Color[-1] = Green\nSuperTrend_Color = Red">Supertrend
            Bearish</span>

        <span tabindex="0" role="button"
            class="tag bg-red-100 text-red-700 border border-red-200 rounded px-2 py-0.5 cursor-pointer hover:bg-red-200 text-xs"
            data-template="# EMA Bearish\nEMA9 < EMA20">EMA Bearish</span>

        <span tabindex="0" role="button"
            class="tag bg-red-100 text-red-700 border border-red-200 rounded px-2 py-0.5 cursor-pointer hover:bg-red-200 text-xs"
            data-template="# MACD Bearish\nMACD < MACDS in last 5">MACD Bearish</span>

        <span tabindex="0" role="button"
            class="tag bg-red-100 text-red-700 border border-red-200 rounded px-2 py-0.5 cursor-pointer hover:bg-red-200 text-xs"
            data-template="# RSI Bearish\nRSI < 50">RSI Bearish</span>

        <span tabindex="0" role="button"
            class="tag bg-red-100 text-red-700 border border-red-200 rounded px-2 py-0.5 cursor-pointer hover:bg-red-200 text-xs"
            data-template="# RSI Overbought Cross\nRSI >= 70\nRSI[-1] < 70">RSI Overbought Cross</span>

        <span tabindex="0" role="button"
            class="tag bg-red-100 text-red-700 border border-red-200 rounded px-2 py-0.5 cursor-pointer hover:bg-red-200 text-xs"
            data-template="# VWAP Strong Bearish\nVWAP < Close - ATR">VWAP Strong Bearish</span>

        <span tabindex="0" role="button"
            class="tag bg-red-100 text-red-700 border border-red-200 rounded px-2 py-0.5 cursor-pointer hover:bg-red-200 text-xs"
            data-template="# Bollinger Band Bearish\nClose < BB_Lower">Bollinger Band Bearish</span>

        <span tabindex="0" role="button"
            class="tag bg-red-100 text-red-700 border border-red-200 rounded px-2 py-0.5 cursor-pointer hover:bg-red-200 text-xs"
            data-template="# Stochastic Bearish\nStoch_K crosses below Stoch_D above 80">Stochastic Bearish</span>

        <span tabindex="0" role="button"
            class="tag bg-red-100 text-red-700 border border-red-200 rounded px-2 py-0.5 cursor-pointer hover:bg-red-200 text-xs"
            data-template="# TEMA Bearish\nTEMA < EMA20">TEMA Bearish</span>

        <span tabindex="0" role="button"
            class="tag bg-red-100 text-red-700 border border-red-200 rounded px-2 py-0.5 cursor-pointer hover:bg-red-200 text-xs"
            data-template="# CCI Bearish\nCCI > 100\nCCI < 100">CCI Bearish</span>

        <span tabindex="0" role="button"
            class="tag bg-red-100 text-red-700 border border-red-200 rounded px-2 py-0.5 cursor-pointer hover:bg-red-200 text-xs"
            data-template="# ATR Spike\nATR > 2 * ATR_Avg">ATR Spike</span>
    </div>



    <div class="flex flex-wrap gap-2 mb-2 text-xs">
        <span class="font-semibold text-gray-700 text-xs">Others:</span>
        <span tabindex="0" role="button"
            class="tag bg-gray-100 text-gray-700 border border-gray-200 rounded px-2 py-0.5 cursor-pointer hover:bg-gray-200 text-xs"
            data-template="# ADX Trend Strength\nADX > 25">ADX Trend Strength</span>
    </div>
    <script>
        // Track which textarea is focused
        let lastFocusedBox = 'screener-5m';
        ['screener-5m', 'screener-15m', 'screener-30m'].forEach(id => {
            document.getElementById(id).addEventListener('focus', function () {
                lastFocusedBox = id;
            });
        });
        // On tag click, append the template to the focused box (with a blank line if not empty)
        document.querySelectorAll('#filter-section .tag[data-template]').forEach(tag => {
            tag.addEventListener('click', function () {
                const textarea = document.getElementById(lastFocusedBox);
                const template = this.getAttribute('data-template').replace(/\\n/g, '\n');
                if (textarea.value.trim() !== '') {
                    textarea.value += '\n\n' + template;
                } else {
                    textarea.value = template;
                }
                textarea.focus();
            });
        });
    </script>
</div>
<!-- End Simple Screener UI -->