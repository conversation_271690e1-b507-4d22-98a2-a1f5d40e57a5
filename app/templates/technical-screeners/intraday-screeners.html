<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Intraday Technical Screeners | AI Bull</title>
    <meta name="description" content="Screen multiple stocks for intraday technical patterns and candlesticks." />
    <link rel="canonical" href="https://theaibull.com/technical-screeners/intraday-screeners" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Intraday Technical Screeners" />
    <meta property="og:description"
        content="Screen multiple stocks for intraday technical patterns and candlesticks." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Intraday Technical Screeners" />
    <meta name="twitter:description"
        content="Screen multiple stocks for intraday technical patterns and candlesticks." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
    {% include 'technical-screeners/render.html' %}
    <style>
        .tag {
            display: inline-flex;
            align-items: center;
            background: linear-gradient(90deg, #eef2ff 60%, #e0e7ff 100%);
            color: #3730a3;
            border-radius: 9999px;
            padding: 0.25rem 0.75rem 0.25rem 0.75rem;
            margin: 0.25rem 0.25rem 0 0;
            font-size: 0.95em;
            font-weight: 500;
            border: 1px solid #c7d2fe;
            box-shadow: 0 1px 2px rgba(55, 48, 163, 0.07);
            transition: box-shadow 0.15s;
        }

        .tag-remove {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 1.5em;
            height: 1.5em;
            margin-left: 0.5em;
            color: #ef4444;
            background: #fff;
            border-radius: 50%;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            border: 1px solid #fca5a5;
            transition: background 0.15s, color 0.15s, box-shadow 0.15s;
        }

        .tag-remove:hover {
            background: #fee2e2;
            color: #b91c1c;
            box-shadow: 0 2px 6px rgba(239, 68, 68, 0.10);
        }

        .autocomplete-dropdown div:hover {
            background: #dbeafe;
        }
    </style>
</head>

<body class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100">
    {% include 'blocks/common.html' %}
    <div class="flex flex-col p-3 mx-auto max-w-7xl w-full min-h-[80vh]">
        <!-- All blocks in a scrollable panel, button fixed at bottom -->
        <aside class="w-full mx-auto mb-6 flex flex-col bg-white dark:bg-neutral-800 rounded-lg shadow relative">
            <div class="flex-1 overflow-y-auto p-4 space-y-6">
                <!-- Block 1: Stock symbols, timeframe, date, search -->
                <div>
                    <h2 class="text-lg font-semibold mb-1">Select Stocks and Indexes to Scan</h2>
                    <p class="text-gray-500 text-xs mb-3">Enter one or more stock symbols or indexes to screen. You can
                        use autocomplete or paste a comma-separated list.</p>
                    <form id="screener-form" onsubmit="event.preventDefault(); runIntradayScreener();">
                        <div class="flex flex-col gap-4">
                            <div>
                                <label for="stock-input" class="block font-medium mb-2 text-sm">Enter Stock
                                    Symbols</label>
                                <div class="flex flex-wrap gap-2 mb-2">
                                    <button type="button"
                                        class="px-2 py-1 rounded bg-indigo-100 text-indigo-800 text-xs font-semibold border border-indigo-200 hover:bg-indigo-200"
                                        onclick="addNiftyStocks()">Nifty 50</button>
                                    <button type="button"
                                        class="px-2 py-1 rounded bg-green-100 text-green-800 text-xs font-semibold border border-green-200 hover:bg-green-200"
                                        onclick="addFoStocks()">F&O</button>
                                </div>
                                <input id="stock-input" type="text" class="w-full border px-3 py-2 rounded"
                                    placeholder="e.g. RELIANCE, TCS, INFY" autocomplete="off" />
                                <div id="selected-stocks" class="mt-2"></div>
                            </div>
                            <div>
                                <label for="date-input" class="block font-medium mb-2 text-sm">Date
                                    <span class="ml-2 text-xs text-blue-700 font-semibold space-x-2">
                                        <a href="#" onclick="setQuickDate(0);return false;">Today</a>
                                        <span>|</span>
                                        <a href="#" onclick="setQuickDate(1);return false;">Yesterday</a>
                                        <span>|</span>
                                        <a href="#" onclick="setQuickDate(2);return false;">Day-2</a>
                                        <span>|</span>
                                        <a href="#" onclick="setQuickDate(3);return false;">Day-3</a>
                                        <span>|</span>
                                        <a href="#" onclick="focusCustomDate();return false;">Custom</a>
                                    </span>
                                </label>
                                <input id="date-input" type="date" class="w-full border px-3 py-2 rounded" />
                            </div>
                        </div>
                    </form>
                </div>
                <!-- Block 2: Filter (Screener conditions) -->
                <div
                    class="bg-neutral-50 dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4 relative">
                    <h2 class="text-lg font-semibold mb-1">Entry Rules</h2>
                    <p class="text-gray-500 text-xs mb-3">Define your entry conditions for each timeframe. Use the quick
                        tags or write your own logic for any combination of 5m, 15m, 30m, 1h, daily, weekly, etc.
                        (Default: 5m, 15m, 30m)</p>
                    {% include 'technical-screeners/filter.html' %}
                </div>
                <!-- Block 3: Exit Rules -->
                <div
                    class="bg-neutral-50 dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4 relative">
                    <h2 class="text-lg font-semibold mb-1">Exit Rules</h2>
                    <p class="text-gray-500 text-xs mb-3">Specify your exit or stop-loss conditions. (Coming soon:
                        advanced exit builder.)</p>
                    <div class="text-gray-400 italic text-xs">Exit rule builder coming soon.</div>
                </div>
                <!-- Block 4: Trade Type -->
                <div
                    class="bg-neutral-50 dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4 relative">
                    <h2 class="text-lg font-semibold mb-1">Trade Type</h2>
                    <p class="text-gray-500 text-xs mb-3">Choose the type of trade you want to scan for: Intraday, F&O,
                        or Stocks (Buy/Sell).</p>
                    <form id="trade-type-form" class="mb-2 flex flex-wrap gap-4 items-center">
                        <label class="flex items-center mr-4">
                            <input type="radio" name="trade-type" value="buy" checked class="mr-1"> Buy
                        </label>
                        <label class="flex items-center mr-4">
                            <input type="radio" name="trade-type" value="sell" class="mr-1"> Sell
                        </label>
                        <label class="flex items-center mr-4">
                            Ticks: <input type="number" id="trade-ticks" min="1" value="1"
                                class="ml-1 w-16 border rounded px-2 py-1 text-xs">
                        </label>
                        <label class="flex items-center mr-4">
                            Stop Loss:
                            <select id="trade-sl" class="ml-1 border rounded px-2 py-1 text-xs"
                                onchange="toggleFixedSLInput()">
                                <option value="fixed">Fixed</option>
                                <option value="1xATR">1x ATR</option>
                                <option value="2xATR" selected>2x ATR</option>
                            </select>
                        </label>
                        <label class="flex items-center mr-4" id="fixed-sl-label" style="display:none;">
                            Fixed SL Value:
                            <input type="number" id="fixed-sl-value" min="0.01" step="0.01" placeholder="e.g. 5.0"
                                class="ml-1 w-20 border rounded px-2 py-1 text-xs">
                        </label>
                        <label class="flex items-center mr-4">
                            Book Profit:
                            <select id="trade-bp" class="ml-1 border rounded px-2 py-1 text-xs">
                                <option value="1xSL">1x SL</option>
                                <option value="1.5xSL">1.5x SL</option>
                                <option value="2xSL">2x SL</option>
                                <option value="4xATR">4x ATR</option>
                            </select>
                        </label>
                    </form>
                </div>
            </div>
            <!-- Fixed Button at Bottom -->
            <div
                class="p-4 border-t border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 sticky bottom-0 z-10">
                <button type="button" onclick="runIntradayScreener()"
                    class="bg-blue-700 text-white px-6 py-2 rounded font-semibold hover:bg-blue-800 text-base w-full">Find
                    Trades</button>
            </div>
        </aside>
        <!-- Results block at the bottom -->
        <div class="w-full">
            <div id="resultsSection" class="border mb-6 p-4 rounded-lg bg-white dark:bg-neutral-800 min-h-[60vh]">
                <div id="results-content" class="overflow-x-auto text-center py-20">
                    <span class="text-gray-400">Results will appear here.</span>
                </div>
            </div>
        </div>
    </div>
    <div class="mt-auto">
        {% include 'blocks/footer.html' %}
    </div>
    <script>
        // --- Multi-stock input logic ---
        const stockInput = document.getElementById('stock-input');
        const selectedStocksDiv = document.getElementById('selected-stocks');
        let selectedStocks = [];

        // --- URL param helpers ---
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                stocks: params.get('s') ? params.get('s').split(',').map(s => s.trim().toUpperCase()).filter(Boolean) : []
            };
        }
        function setUrlParams(stocks) {
            const params = new URLSearchParams(window.location.search);
            if (stocks.length > 0) params.set('s', stocks.join(','));
            else params.delete('s');
            const newUrl = window.location.pathname + '?' + params.toString();
            window.history.replaceState({}, '', newUrl);
        }

        // --- On page load, prefill from URL ---
        window.addEventListener('DOMContentLoaded', function () {
            const params = getUrlParams();
            if (params.stocks.length > 0) {
                selectedStocks = params.stocks;
                renderSelectedStocks();
            }
        });

        // --- Autocomplete dropdown ---
        let autocompleteDropdown = document.createElement('div');
        autocompleteDropdown.className = 'autocomplete-dropdown bg-white border border-gray-300 shadow-md';
        autocompleteDropdown.style.display = 'none';
        autocompleteDropdown.style.position = 'absolute';
        autocompleteDropdown.style.zIndex = 1000;
        stockInput.parentNode.appendChild(autocompleteDropdown);

        stockInput.addEventListener('input', async function (e) {
            const val = stockInput.value.trim();
            if (!val) {
                autocompleteDropdown.style.display = 'none';
                return;
            }
            try {
                const resp = await fetch(`/technical-screeners/autocomplete?q=${encodeURIComponent(val)}`);
                if (!resp.ok) throw new Error('Autocomplete error');
                const data = await resp.json();
                if (!Array.isArray(data) || data.length === 0) {
                    autocompleteDropdown.style.display = 'none';
                    return;
                }
                autocompleteDropdown.innerHTML = data.map(item =>
                    `<div class="px-3 py-2 cursor-pointer hover:bg-blue-100" data-symbol="${item.symbol}"><b>${item.symbol}</b> <span class='text-gray-500 text-xs ml-2'>${item.name || ''}</span></div>`
                ).join('');
                const rect = stockInput.getBoundingClientRect();
                autocompleteDropdown.style.left = '0px';
                autocompleteDropdown.style.top = (stockInput.offsetTop + stockInput.offsetHeight) + 'px';
                autocompleteDropdown.style.width = stockInput.offsetWidth + 'px';
                autocompleteDropdown.style.display = 'block';
            } catch (err) {
                autocompleteDropdown.style.display = 'none';
            }
        });

        autocompleteDropdown.addEventListener('mousedown', function (e) {
            // Traverse up to find the element with data-symbol
            let target = e.target;
            while (target && !target.dataset.symbol && target !== autocompleteDropdown) {
                target = target.parentElement;
            }
            if (target && target.dataset && target.dataset.symbol) {
                addStock(target.dataset.symbol);
                stockInput.value = '';
                autocompleteDropdown.style.display = 'none';
                // Prevent input blur until after click
                e.preventDefault();
            }
        });

        document.addEventListener('click', function (e) {
            if (!autocompleteDropdown.contains(e.target) && e.target !== stockInput) {
                autocompleteDropdown.style.display = 'none';
            }
        });

        function renderSelectedStocks() {
            selectedStocksDiv.innerHTML = selectedStocks.map(s => `<span class="tag">${s}<span class="tag-remove" onclick="removeStock('${s}')">&times;</span></span>`).join('');
            // Update URL params when stocks change
            setUrlParams(selectedStocks);
        }
        function addStock(symbol) {
            symbol = symbol.trim().toUpperCase();
            if (symbol && !selectedStocks.includes(symbol)) {
                selectedStocks.push(symbol);
                renderSelectedStocks();
            }
        }
        function removeStock(symbol) {
            selectedStocks = selectedStocks.filter(s => s !== symbol);
            renderSelectedStocks();
        }
        stockInput.addEventListener('keydown', function (e) {
            if (e.key === 'Enter' || e.key === ',' || e.key === ' ') {
                e.preventDefault();
                const val = stockInput.value.trim().toUpperCase();
                if (val) addStock(val);
                stockInput.value = '';
                autocompleteDropdown.style.display = 'none';
            }
        });
        stockInput.addEventListener('blur', function () {
            setTimeout(() => { autocompleteDropdown.style.display = 'none'; }, 150);
            const val = stockInput.value.trim().toUpperCase();
            if (val) addStock(val);
            stockInput.value = '';
        });
        // --- Set default date to today ---
        const dateInput = document.getElementById('date-input');
        function setQuickDate(daysAgo) {
            const d = new Date();
            d.setDate(d.getDate() - daysAgo);
            // If the resulting date is Sat/Sun, roll back to previous Friday
            while (d.getDay() === 6 || d.getDay() === 0) {
                d.setDate(d.getDate() - 1);
            }
            dateInput.value = d.toISOString().split('T')[0];
        }
        function focusCustomDate() {
            dateInput.focus();
        }
        (function setDefaultDate() {
            const d = new Date();
            // If today is Sat/Sun, roll back to previous Friday
            while (d.getDay() === 6 || d.getDay() === 0) {
                d.setDate(d.getDate() - 1);
            }
            dateInput.value = d.toISOString().split('T')[0];
        })();
        // --- Run Screener ---
        async function runIntradayScreener() {
            if (selectedStocks.length === 0) {
                alert('Please enter at least one stock symbol.');
                return;
            }
            const date = dateInput.value;
            // Collect entry rules from textareas (5m, 15m, 30m, etc.)
            const entry_rules = [];
            const timeframes = [
                { id: 'screener-5m', tf: '5m' },
                { id: 'screener-15m', tf: '15m' },
                { id: 'screener-30m', tf: '30m' }
            ];
            for (const { id, tf } of timeframes) {
                const textarea = document.getElementById(id);
                if (textarea && textarea.value.trim()) {
                    entry_rules.push({
                        timeframe: tf,
                        rules_config: textarea.value.trim()
                    });
                }
            }
            if (entry_rules.length === 0) {
                alert('Please enter at least one screener condition.');
                return;
            }
            // --- Collect trade config ---
            const tradeType = document.querySelector('input[name="trade-type"]:checked')?.value || 'buy';
            const ticksDelayed = parseInt(document.getElementById('trade-ticks').value) || 1;
            const stopLoss = document.getElementById('trade-sl').value;
            const bookProfit = document.getElementById('trade-bp').value;
            let fixedStopLoss = undefined;
            if (stopLoss === 'fixed') {
                fixedStopLoss = parseFloat(document.getElementById('fixed-sl-value').value);
                if (isNaN(fixedStopLoss) || fixedStopLoss <= 0) {
                    alert('Please enter a valid fixed stop loss value.');
                    return;
                }
            }
            const trade = {
                type: tradeType,
                ticks_delayed: ticksDelayed,
                stop_loss: stopLoss,
                book_profit: bookProfit
            };
            if (stopLoss === 'fixed') {
                trade.fixed_stop_loss = fixedStopLoss;
            }
            const resultsSection = document.getElementById('resultsSection');
            const resultsContent = document.getElementById('results-content');
            resultsSection.classList.remove('hidden');
            // Show loading message while scanning
            resultsContent.innerHTML = `<div class='flex flex-col items-center justify-center py-16'><span class='animate-pulse text-blue-700 font-semibold text-lg'>Scanning stocks...</span></div>`;
            // --- Summary Table ---
            let summaryRows = [];
            for (const symbol of selectedStocks) {
                let result = { symbol };
                let matchedCount = 0;
                let matchTimes = '';
                try {
                    const payload = {
                        symbol: symbol,
                        date: date,
                        entry_rules: entry_rules,
                        trade: trade
                    };
                    const response = await fetch('/technical-screeners/intraday-screen', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) throw new Error(`Error for ${symbol}: ${response.status}`);
                    const data = await response.json();
                    result.data = data[symbol] || data;
                    // --- Extract ohlcData, matched, and pl_stats robustly for array/object ---
                    let matched = [];
                    let pl_stats = {};
                    let pl_details = {};
                    let tfResult = Array.isArray(result.data) ? result.data[0] : result.data;
                    if (tfResult && tfResult.matched) {
                        matched = tfResult.matched;
                        pl_stats = tfResult.pl_stats || {};
                        pl_details = tfResult.pl_details || {};
                    } else if (result.data && typeof result.data === 'object') {
                        for (const tf in result.data) {
                            if (result.data[tf] && result.data[tf].matched) {
                                matched = result.data[tf].matched;
                                pl_stats = result.data[tf].pl_stats || {};
                                pl_details = result.data[tf].pl_details || {};
                                break;
                            }
                        }
                    }
                    matchedCount = Array.isArray(matched) ? matched.length : 0;
                    if (matchedCount > 0) {
                        matchTimes = matched.map(ts => {
                            const dt = new Date(Number(ts) * 1000);
                            const tstr = dt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                            const pl = pl_stats && pl_stats[ts] ? pl_stats[ts] : { pl_pos: '-', pl_neg: '-', pl_zero: '-' };
                            const pld = pl_details && pl_details[ts] ? pl_details[ts] : null;
                            let plStr = `${tstr} (<span style='color:green'>+${pl.pl_pos}</span>/<span style='color:red'>-${pl.pl_neg}</span>/<span style='color:gray'>${pl.pl_zero}</span>)`;
                            if (pld) {
                                plStr += `<br><span class='text-xs text-gray-700'>Entry: <b>${pld.entry_price}</b> @ ${new Date(Number(pld.entry_time) * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}, Exit: <b>${pld.exit_price}</b> @ ${new Date(Number(pld.exit_time) * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}<br>PL: <b>${pld.pl.toFixed(2)}</b>, SL: <b>${pld.stop_loss}</b>, BP: <b>${pld.book_profit}</b>, Reason: <b>${pld.exit_reason}</b></span>`;
                            }
                            return plStr;
                        }).join(', ');
                    }
                } catch (err) {
                    matchedCount = 0;
                    matchTimes = '';
                }
                summaryRows.push(`<tr><td class='px-3 py-2 border-b border-gray-200 font-semibold'>${symbol}</td><td class='px-3 py-2 border-b border-gray-200 text-blue-700 font-bold text-center'>${matchedCount}</td><td class='px-3 py-2 border-b border-gray-200 text-gray-700 text-xs'>${matchTimes}</td></tr>`);
            }
            const summaryTable = `<div class='mb-4'><table class='min-w-[300px] text-sm border border-gray-300 rounded-lg overflow-hidden shadow-sm bg-white mx-auto'><thead class='bg-indigo-50'><tr><th class='px-3 py-2 border-b border-gray-200 text-left'>Stock Name</th><th class='px-3 py-2 border-b border-gray-200 text-left'># Matches</th><th class='px-3 py-2 border-b border-gray-200 text-left'>Match Times</th></tr></thead><tbody>${summaryRows.join('')}</tbody></table></div>`;
            // --- Progressive rendering: initialize tabs ---
            resultsContent.innerHTML = `<div class='mb-2 flex flex-wrap' id='tab-header-row'></div><div id='tab-content-row'></div>`;
            const tabHeaderRow = document.getElementById('tab-header-row');
            const tabContentRow = document.getElementById('tab-content-row');
            let firstTabId = null;
            // --- Prepare summary table as a tab ---
            const summaryTabId = 'tab-summary';
            const summaryTabBtn = document.createElement('button');
            summaryTabBtn.className = 'tab-btn px-3 py-2 mr-2 mb-2 rounded border font-semibold bg-blue-700 text-white';
            summaryTabBtn.setAttribute('data-tab', summaryTabId);
            summaryTabBtn.textContent = 'Summary';
            // Tab header row
            tabHeaderRow.appendChild(summaryTabBtn);
            // Tab content row
            const summaryDiv = document.createElement('div');
            summaryDiv.id = summaryTabId;
            summaryDiv.className = 'tab-content';
            summaryDiv.style.display = 'block';
            summaryDiv.innerHTML = summaryTable;
            tabContentRow.appendChild(summaryDiv);
            for (const symbol of selectedStocks) {
                let result = { symbol };
                try {
                    const payload = {
                        symbol: symbol,
                        date: date,
                        entry_rules: entry_rules,
                        trade: trade
                    };
                    const response = await fetch('/technical-screeners/intraday-screen', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) throw new Error(`Error for ${symbol}: ${response.status}`);
                    const data = await response.json();
                    result.data = data[symbol] || data;
                    // --- Extract ohlcData and matched robustly for array/object ---
                    let ohlcData = null;
                    let matched = [];
                    for (const tf in result.data) {
                        if (result.data[tf] && result.data[tf].ohlc_data) {
                            ohlcData = result.data[tf].ohlc_data;
                            matched = result.data[tf].matched || [];
                            break;
                        }
                    }
                } catch (err) {
                    result.error = err.message;
                }
                const tabId = `tab-${symbol.replace(/[^a-zA-Z0-9]/g, '')}`;
                const btn = document.createElement('button');
                btn.className = 'tab-btn px-3 py-2 mr-2 mb-2 rounded border font-semibold bg-white text-blue-700';
                btn.setAttribute('data-tab', tabId);
                btn.textContent = symbol;
                btn.addEventListener('click', function () {
                    tabHeaderRow.querySelectorAll('.tab-btn').forEach(b => {
                        b.classList.remove('bg-blue-700', 'text-white', 'bg-white', 'text-blue-700');
                        b.classList.add('bg-white', 'text-blue-700');
                    });
                    tabContentRow.querySelectorAll('.tab-content').forEach(div => div.style.display = 'none');
                    btn.classList.remove('bg-white', 'text-blue-700');
                    btn.classList.add('bg-blue-700', 'text-white');
                    const tabDiv = tabContentRow.querySelector(`#${tabId}`);
                    if (tabDiv) tabDiv.style.display = 'block';
                });
                tabHeaderRow.appendChild(btn);
                const div = document.createElement('div');
                div.id = tabId;
                div.className = 'tab-content';
                div.style.display = 'none';
                let content = '';
                if (result.error) {
                    content = `<div class=\"text-red-600\">${result.error}</div>`;
                } else if (result.data && Object.keys(result.data).length) {
                    // Try to find ohlc_data and matched in any timeframe result
                    let ohlcData = null;
                    let matched = [];
                    for (const tf in result.data) {
                        if (result.data[tf] && result.data[tf].ohlc_data) {
                            ohlcData = result.data[tf].ohlc_data;
                            matched = result.data[tf].matched || [];
                            break;
                        }
                    }
                    if (ohlcData) {
                        content = renderStockTable(ohlcData, matched);
                    } else {
                        content = `<div class=\"text-gray-500\">No data.</div>`;
                    }
                } else {
                    content = `<div class=\"text-gray-500\">No data.</div>`;
                }
                div.innerHTML = content;
                tabContentRow.appendChild(div);
            }
            // Summary tab click logic
            summaryTabBtn.addEventListener('click', function () {
                tabHeaderRow.querySelectorAll('.tab-btn').forEach(b => {
                    b.classList.remove('bg-blue-700', 'text-white', 'bg-white', 'text-blue-700');
                    b.classList.add('bg-white', 'text-blue-700');
                });
                tabContentRow.querySelectorAll('.tab-content').forEach(div => div.style.display = 'none');
                summaryTabBtn.classList.remove('bg-white', 'text-blue-700');
                summaryTabBtn.classList.add('bg-blue-700', 'text-white');
                summaryDiv.style.display = 'block';
            });
        }


        function toggleFixedSLInput() {
            const sl = document.getElementById('trade-sl').value;
            const label = document.getElementById('fixed-sl-label');
            if (sl === 'fixed') {
                label.style.display = '';
            } else {
                label.style.display = 'none';
                document.getElementById('fixed-sl-value').value = '';
            }
        }
        document.getElementById('trade-sl').addEventListener('change', toggleFixedSLInput);
        window.addEventListener('DOMContentLoaded', function () {
            document.getElementById('trade-sl').value = '2xATR';
            document.getElementById('trade-bp').value = '4xATR';
            toggleFixedSLInput();
        });


        function addNiftyStocks() {
            niftyTickers.forEach(s => addStock(s));
        }
        function addFoStocks() {
            foTickers.forEach(s => addStock(s));
        }
    </script>
    {% include 'blocks/stock-symbols.html' %}
</body>

</html>