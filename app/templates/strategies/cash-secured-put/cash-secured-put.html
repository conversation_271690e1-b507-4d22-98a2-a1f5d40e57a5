<!DOCTYPE html>
<html lang="en">

<head>

    <title>Cash Secured Put Scanner | AI-Powered Opportunities | AI Bull</title>
    <meta name="description"
        content="Discover the best cash secured put opportunities using AI-powered analysis to maximize your returns. Find optimal stocks for cash secured put strategies with ease and minimize risks in options trading." />
    <link rel="canonical" href="https://theaibull.com/strategies/cash-secured-put" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/strategies/cash-secured-put" />
    <meta property="og:title"
        content="Cash Secured Put Scanner - Find the Best Cash Secured Put Opportunities with AI-Powered Analysis" />
    <meta property="og:description"
        content="Discover the best cash secured put opportunities using AI-powered analysis to maximize your returns. Find optimal stocks for cash secured put strategies with ease." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@yourhandle" />
    <meta name="twitter:title"
        content="Cash Secured Put Scanner - Find the Best Cash Secured Put Opportunities with AI-Powered Analysis" />
    <meta name="twitter:description"
        content="Find the best cash secured put opportunities with AI-powered analysis to optimize your options trading strategy." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
    <style>
        /* Sort indicator styles */
        .sort-indicator {
            margin-left: 4px;
            display: inline-block;
        }

        th[data-col] {
            cursor: pointer;
        }
    </style>
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {% include 'blocks/common.html' %}
    {%include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <main>
            <div class="p-3 mx-auto max-w-7xl w-full">


                <div
                    class="flex flex-col md:flex-row justify-between bg-white dark:bg-neutral-800 px-3 py-2  rounded-lg mb-3">
                    <div class="flex items-center">
                        <h1 class="text-xl font-semibold tracking-tight">
                            Cash Secured Put Scanner
                        </h1>
                        <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">
                            Find the best cash secured put opportunities with AI-powered analysis
                        </h2>
                        <!-- Button to trigger modal open -->
                        <button id="openModalBtn" onclick="openModal()" class="ml-2">

                            <div class="flex group text-sm">
                                <div
                                    class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                    <div
                                        class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                        <div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-circle-help">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                <path d="M12 17h.01"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div
                                        class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                        <div
                                            class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                            How to Use This Page</div>
                                    </div>
                                </div>
                            </div>
                        </button>
                    </div>
                    <div class="rounded-lg bg-slate-100 p-0.5 w-[300px] mt-2 md:mt-0">
                        <nav class="-mb-px flex" aria-label="Tabs">
                            <button id="search-tab"
                                class="tab-button w-1/2 py-2.5 px-1 text-center shadow-md font-medium text-sm text-blue-600 dark:text-blue-500 bg-white rounded-md"
                                aria-current="page">
                                Search Stock
                            </button>
                            <button id="all-stocks-tab"
                                class="tab-button w-1/2 py-2.5 px-1 text-center font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300">
                                Scan All Stocks
                            </button>
                        </nav>
                    </div>
                </div>


                <!-- Search Tab Content -->
                <div id="search-content" class="tab-content">
                    <div class="flex flex-col gap-3">
                        <!-- Search Input Area -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                            <!-- Stock Input Form -->
                            <div>
                                <div class="flex flex-col gap-6">
                                    <div class="w-full">
                                        {% with show_submit_button=false, show_indices=false %}
                                        {% include 'blocks/search-active-recent-stocks.html' %}
                                        {% endwith %}
                                    </div>

                                    <div class="flex flex-col md:flex-row gap-4 md:items-end">

                                        <div class="w-full md:w-1/3">
                                            <label class="block font-medium mb-2 text-sm">Target Price</label>
                                            <input id="cost-input" type="number" step="0.01" placeholder="Optional"
                                                class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all" />
                                        </div>

                                        <div class="w-full md:w-1/3">
                                            <label class="block font-medium mb-2 text-sm">Min Discount %</label>
                                            <input id="min-discount" type="number" step="0.1" value="1"
                                                class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all" />
                                        </div>

                                        <div class="w-full md:w-1/3">
                                            <label class="block font-medium mb-2 text-sm">Min Probability %</label>
                                            <input id="min-prob" type="number" step="0.1" value="15"
                                                class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all" />
                                        </div>

                                        <div class="w-full md:w-1/3">
                                            <button id="stock-input-submit-btn"
                                                class="bg-blue-500 block focus:outline-0 font-medium hover:bg-blue-600 px-6 py-2.5 rounded-md text-white transition-colors w-full">
                                                Show Put Options
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Price Info Area -->
                        <div id="price-info-container" class="bg-white dark:bg-neutral-800 rounded-lg p-4 hidden">
                            <div id="price-info"></div>
                        </div>

                        <!-- Options Table Area -->
                        <div id="options-table-container" class="bg-white dark:bg-neutral-800 rounded-lg p-4 hidden">
                            <div id="expiry-tabs"
                                class="border flex gap-0 mb-4 overflow-x-auto p-1 rounded-md text-nowrap"></div>
                            <div id="cash-put-table" class="overflow-x-auto"></div>
                        </div>
                    </div>
                </div>

                <!-- All Stocks Tab Content -->
                <div id="all-stocks-content" class="tab-content hidden">
                    {% include 'strategies/cash-secured-put/cash-secured-put-scanner.html' %}
                </div>

                <!-- FAQ Section in Accordion Format -->
                <div class="mt-8 text-sm">
                    <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
                    <div class="space-y-4">
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What is a Cash Secured Put?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    A Cash Secured Put is an options strategy where you sell a put option while setting
                                    aside enough cash to buy the
                                    underlying stock if the option is exercised. The goal is to earn premium income
                                    while being ready to purchase the stock.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How do I use the Cash Secured Put Scanner?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Enter a stock symbol, set your desired target price, minimum discount, and minimum
                                    probability percentages, and click
                                    "Search Stock" or "Scan All Stocks" to find potential opportunities. The scanner
                                    uses AI to filter the best trades based
                                    on these criteria.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What criteria can I filter by?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    You can filter by target price, minimum discount percentage, and minimum probability
                                    percentage to find options with the
                                    best risk-to-reward profiles based on your investment preferences.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How does the AI-powered analysis help?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    The AI-powered analysis scans market data in real time to identify stocks with
                                    favorable conditions, such as higher
                                    premiums, lower volatility, or higher probability of success, helping you make more
                                    informed decisions.

                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What are the risks of selling cash secured puts?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Selling cash secured puts comes with the risk of having to buy the underlying stock
                                    if it falls below the strike price.
                                    This can result in owning a stock at a price higher than its current market value,
                                    leading to potential losses.
                                </p>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
        </main>

        {% include 'blocks/table-sorting.html' %}

        <script>
            document.addEventListener("DOMContentLoaded", async () => {
                const stockInput = document.getElementById("stock-input");
                const costInput = document.getElementById("cost-input");
                const minDiscountInput = document.getElementById("min-discount");
                const minProbInput = document.getElementById("min-prob");
                const submitButton = document.getElementById("stock-input-submit-btn");

                const priceInfoContainer = document.getElementById("price-info");
                const expiryTabsContainer = document.getElementById("expiry-tabs");
                const tableContainer = document.getElementById("cash-put-table");



                // Create dropdown container for suggestions
                const dropdown = document.createElement("div");
                dropdown.className =
                    "absolute w-full mt-1 bg-white dark:bg-neutral-700 border dark:border-neutral-600 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto";
                dropdown.style.display = "none";
                stockInput.parentNode.appendChild(dropdown);

                document.addEventListener('stockSearchTriggered', async (e) => {
                    const symbol = e.detail.symbol;
                    let costBasis = parseFloat(costInput.value) || 0;
                    // If the current symbol is same as the one we are searching for, then use the current purchase price
                    if (window.lastSearchedSymbol != symbol) {
                        costBasis = 0;
                    }
                    document.getElementById("price-info-container").classList.add("hidden");
                    document.getElementById("options-table-container").classList.add("hidden");
                    const minDiscount = parseFloat(minDiscountInput.value) || 1;
                    const minProbability = parseFloat(minProbInput.value) || 15;

                    if (!symbol) {
                        showAlert("Please enter a valid Stock Symbol.");
                        return;
                    }

                    // Clear previous data
                    priceInfoContainer.innerHTML = "";
                    expiryTabsContainer.innerHTML = "";
                    tableContainer.innerHTML = "";

                    try {
                        // First fetch price info
                        const priceResponse = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/price/${symbol}`);
                        const dataPrice = await priceResponse.json();
                        const lastPrice = dataPrice.priceInfo ? dataPrice.priceInfo.lastPrice : 0;
                        costBasis = costBasis || lastPrice || 0; // use last price as cost basis if not provided
                        costInput.value = costBasis.toFixed(2); // update the input field with the actual cost basis
                        renderPriceInfo(dataPrice);

                        // Then fetch option chain
                        const response = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${symbol}`);
                        const data = await response.json();

                        if (window.lastSearchedSymbol != symbol) {
                            costInput.value = costBasis.toFixed(2);
                        }
                        window.lastSearchedSymbol = symbol;
                        buildExpiryData(data.records, costBasis, minDiscount, minProbability);
                        document.getElementById("price-info-container").classList.remove("hidden");
                        document.getElementById("options-table-container").classList.remove("hidden");
                    } catch (err) {
                        console.error("Error fetching data:", err);
                        tableContainer.innerHTML = '<p class="text-red-500">Failed to fetch data</p>';
                    }
                });

                // Render price info
                function renderPriceInfo(dataPrice) {
                    const costBasis = costInput.value;
                    const { info, priceInfo } = dataPrice;
                    if (!info || !priceInfo) {
                        priceInfoContainer.innerHTML = '<p class="text-gray-500">No price info found.</p>';
                        return;
                    }

                    priceInfoContainer.innerHTML = `
                        <div>
                            <h2 class="font-semibold mb-3 text-left">${info.companyName || info.symbol}</h2>
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-2">
                                <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                    <div class="text-xs text-gray-600 dark:text-gray-400">Last Price</div>
                                    <div class="font-medium">${(priceInfo.lastPrice || 0).toFixed(2)}</div>
                                </div>
                                
                                <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                    <div class="text-xs text-gray-600 dark:text-gray-400">Change</div>
                                    <div class="font-medium ${priceInfo.change > 0 ? 'text-green-600' : 'text-red-500'}">
                                        ${(priceInfo.change || 0).toFixed(2)}
                                    </div>
                                </div>
                                
                                <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                    <div class="text-xs text-gray-600 dark:text-gray-400">% Change</div>
                                    <div class="font-medium ${priceInfo.pChange > 0 ? 'text-green-600' : 'text-red-500'}">
                                        ${priceInfo.pChange ? priceInfo.pChange.toFixed(2) + '%' : '0%'}
                                    </div>
                                </div>
                                
                                <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                    <div class="text-xs text-gray-600 dark:text-gray-400">Open</div>
                                    <div class="font-medium">${(priceInfo.open || 0).toFixed(2)}</div>
                                </div>
                                
                                <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                    <div class="text-xs text-gray-600 dark:text-gray-400">Previous Close</div>
                                    <div class="font-medium">${(priceInfo.previousClose || 0).toFixed(2)}</div>
                                </div>
                            </div>
                            ${!costBasis ? `
                                <p class="text-xs text-gray-600 mt-2">
                                    Using Last Price = <span class="text-neutral-950 dark:text-neutral-100 font-medium px-1">${(priceInfo.lastPrice || 0).toFixed(2)}</span> as your target price.
                                </p>
                            ` : ''}
                        </div>
                    `;
                }

                // Build expiry data
                function buildExpiryData(records, costBasis, minDiscount, minProbability) {
                    const { expiryDates, data } = records;
                    if (!expiryDates || !expiryDates.length) {
                        tableContainer.innerHTML = '<p class="text-gray-500">No expiry data found.</p>';
                        return;
                    }

                    const putsByExpiry = {};
                    expiryDates.forEach((ed) => {
                        putsByExpiry[ed] = [];
                    });

                    // Filter for PE (put) data
                    data.forEach((opt) => {
                        if (opt.PE) {
                            if (!putsByExpiry[opt.expiryDate]) putsByExpiry[opt.expiryDate] = [];
                            putsByExpiry[opt.expiryDate].push(opt);
                        }
                    });

                    // Render tabs
                    const tabsHtml = expiryDates
                        .map(
                            (date, idx) => `
                        <button 
                            class="tab-btn px-4 py-1.5 rounded-sm text-xs font-medium 
                                   ${idx === 0 ? "bg-blue-500 text-white" : "bg-white dark:bg-neutral-800"}"
                            data-expiry="${date}"
                        >
                            ${date}
                        </button>
                    `
                        )
                        .join("");
                    expiryTabsContainer.innerHTML = tabsHtml;

                    // Show first expiry by default
                    showCashSecuredPuts(putsByExpiry[expiryDates[0]] || [], costBasis, minDiscount, minProbability, expiryDates[0]);

                    // Add tab click handlers
                    document.querySelectorAll(".tab-btn").forEach((btn) => {
                        btn.addEventListener("click", function () {
                            document.querySelectorAll(".tab-btn").forEach((b) => {
                                b.classList.remove("bg-blue-500", "text-white");
                                b.classList.add("bg-white", "dark:bg-neutral-800");
                            });
                            this.classList.remove("bg-white", "dark:bg-neutral-800");
                            this.classList.add("bg-blue-500", "text-white");

                            const ed = this.dataset.expiry;
                            showCashSecuredPuts(putsByExpiry[ed], costBasis, minDiscount, minProbability, ed);
                        });
                    });
                }

                // Show cash secured puts table
                function showCashSecuredPuts(putOptions, costBasis, minDiscount, minProbability, expiryDate) {
                    if (!putOptions.length) {
                        tableContainer.innerHTML = `<p class="text-gray-500">No Put data for ${expiryDate}</p>`;
                        return;
                    }

                    // Process put options data
                    let rowData = putOptions.map((opt) => {
                        const pe = opt.PE;
                        const strike = pe.strikePrice;
                        const premium = pe.lastPrice || 0;
                        const volume = pe.totalTradedVolume || 0;
                        const effectivePrice = strike - premium;
                        const moneySaved = costBasis > 0 ? costBasis - effectivePrice : 0;
                        const discountPercent = costBasis > 0 ? (moneySaved / costBasis) * 100 : 0;
                        const probAcquire = pe.delta != null ? Math.abs(pe.delta) * 100 : 0;
                        const premiumPercent = strike > 0 ? (premium / strike) * 100 : 0;
                        const isITM = costBasis > 0 && strike > costBasis;

                        return {
                            strikePrice: strike,
                            premium,
                            premiumPercent,
                            volume,
                            effectivePrice,
                            discountPercent,
                            probAcquire,
                            isITM,
                        };
                    });

                    // Apply filters
                    rowData = rowData.filter((row) => row.discountPercent >= minDiscount);
                    rowData = rowData.filter((row) => row.probAcquire >= minProbability);

                    if (!rowData.length) {
                        tableContainer.innerHTML = `
                            <p class="text-gray-500">
                                No puts match your criteria:
                                (effective discount >= ${minDiscount}%, probability >= ${minProbability}%).
                            </p>
                        `;
                        return;
                    }

                    // Sort by discount percentage
                    rowData.sort((a, b) => b.discountPercent - a.discountPercent);

                    // Build table HTML
                    let tableHtml = `
                        <div class="mb-2 md:flex justify-between items-center">
                            <span class="block text-xs text-neutral-800 font-medium">
                                Min Discount%: ${minDiscount.toFixed(2)}%; 
                                Min Probability: ${minProbability.toFixed(2)}%;
                                Target Price = ${costBasis.toFixed(2)}
                            </span>
                            <div class="text-gray-600 dark:text-gray-300 text-xs px-2 py-1 bg-red-50 inline-flex mt-2 md:mt-0">
                            <span class="text-neutral-600">Expiry:</span><span class="font-medium pl-2 text-neutral-800">${expiryDate}</span>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full border-collapse border dark:border-neutral-700 text-sm w-full" id="cash-put-table-el">
                                <thead class="bg-gray-50 dark:bg-neutral-700">
                                    <tr>
                                        <th class="border dark:border-neutral-600 p-2" data-col="strikePrice">Strike</th>
                                        <th class="border dark:border-neutral-600 p-2" data-col="premium">Premium</th>
                                        <th class="border dark:border-neutral-600 p-2" data-col="premiumPercent">Premium %</th>
                                        <th class="border dark:border-neutral-600 p-2" data-col="volume">Volume</th>
                                        <th class="border dark:border-neutral-600 p-2" data-col="effectivePrice">Eff. Price</th>
                                        <th class="border dark:border-neutral-600 p-2" data-col="discountPercent">Discount%</th>
                                        <th class="border dark:border-neutral-600 p-2" data-col="probAcquire">Prob. Acquire%</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;

                    rowData.forEach((row, idx) => {
                        const highlightRow = idx < 3 ? "bg-yellow-50 dark:bg-yellow-900/20" : "";
                        const colorDisc = row.discountPercent < 0 ? "text-red-500" : "text-green-600";

                        tableHtml += `
                            <tr class="hover:bg-gray-50 dark:hover:bg-neutral-700/50 ${highlightRow}">
                                <td class="border dark:border-neutral-600 p-2 text-center">
                                    ${row.strikePrice}
                                    ${row.isITM ? '<span class="text-xs text-gray-500 ml-1">ITM</span>' : ""}
                                </td>
                                <td class="border dark:border-neutral-600 p-2 text-center">${row.premium.toFixed(2)}</td>
                                <td class="border dark:border-neutral-600 p-2 text-center">${row.premiumPercent.toFixed(2)}%</td>
                                <td class="border dark:border-neutral-600 p-2 text-center">${row.volume}</td>
                                <td class="border dark:border-neutral-600 p-2 text-center">${row.effectivePrice.toFixed(2)}</td>
                                <td class="border dark:border-neutral-600 p-2 text-center ${colorDisc}">${row.discountPercent.toFixed(2)}%</td>
                                <td class="border dark:border-neutral-600 p-2 text-center">${row.probAcquire.toFixed(2)}%</td>
                            </tr>
                        `;
                    });

                    tableHtml += `</tbody></table></div>`;
                    tableContainer.innerHTML = tableHtml;
                    window.addSorting("cash-put-table-el");
                }

                // Add tab switching functionality
                const searchTab = document.getElementById("search-tab");
                const allStocksTab = document.getElementById("all-stocks-tab");
                const searchContent = document.getElementById("search-content");
                const allStocksContent = document.getElementById("all-stocks-content");

                function switchTab(activeTab, activeContent, inactiveTab, inactiveContent) {
                    activeTab.classList.remove("text-gray-500", "border-transparent", "hover:text-gray-700", "hover:border-gray-300");
                    activeTab.classList.add("bg-white", "text-blue-600", "rounded-md", "shadow-md");

                    inactiveTab.classList.add("text-gray-500", "border-transparent", "hover:text-gray-700", "hover:border-gray-300");
                    inactiveTab.classList.remove("bg-white", "text-blue-600", "rounded-md", "shadow-md");

                    activeContent.classList.remove("hidden");
                    inactiveContent.classList.add("hidden");
                }

                searchTab.addEventListener("click", () => {
                    switchTab(searchTab, searchContent, allStocksTab, allStocksContent);
                });

                allStocksTab.addEventListener("click", () => {
                    switchTab(allStocksTab, allStocksContent, searchTab, searchContent);
                });
            });
        </script>

        {% include 'blocks/footer.html' %}
    </div>
</body>

</html>