<main class="h-full">
    <div class="">
        <div>
            <!-- Left Column -->
            <div class="w-full">
                <!-- Stock Category Selector -->
                <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-3">
                    <div class="flex gap-4">
                        <label class="flex items-center">
                            <input type="radio" name="cash-secured-put-stock-category" value="nifty" checked
                                class="mr-2" />
                            Nifty Stocks
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="cash-secured-put-stock-category" value="fo" class="mr-2" />
                            F&O Stocks
                        </label>
                    </div>
                </div>

                <!-- Ticker Selection Section -->
                <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 flex flex-col h-[350px] lg:max-h-[calc(60vh)]">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="font-semibold">Select Stocks</h4>
                        <div class="flex gap-2 items-center">
                            <button id="select-all" class="text-xs hover:text-blue-500 text-neutral-700 font-medium">
                                Select All
                            </button>
                            <span class="text-gray-400 text-xs">|</span>
                            <button id="unselect-all" class="text-xs hover:text-red-500 text-neutral-700 font-medium">
                                Unselect All
                            </button>
                        </div>
                    </div>
                    <div id="cash-secured-put-ticker-selection" class="mb-4 flex-1 overflow-y-auto custom-scroll">
                    </div>
                    <div class="pt-4 border-t mt-auto">
                        <div class="flex gap-2 justify-between">

                            <button id="cash-secured-put-show-button"
                                class="bg-blue-500 block focus:outline-0 font-medium hover:bg-blue-600 px-6 py-3 rounded-md text-white transition-colors w-full">
                                Scan Selected
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="mt-3">
                <!-- Filter Section (initially hidden) -->
                <div id="results-filter-section" class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4 hidden">
                    <div class="md:flex justify-between">
                        <h3 class="font-semibold mb-4">Filter Options</h3>
                    </div>
                    <div class="grid md:grid-cols-3 md:gap-12 gap-4 mb-4">
                        <!-- Cost/Target Price -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">Cost/Target Price</label>
                            <input id="cash-secured-put-cost-input" type="number" placeholder="Optional"
                                class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all" />
                        </div>

                        <!-- Min Discount Slider -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Min Discount (<span id="discount-label-value">3</span>%)
                            </label>
                            <div id="min-discount-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span id="discount-min-value">0%</span>
                                <span id="discount-max-value">20%</span>
                            </div>
                        </div>

                        <!-- Add Volume Slider -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Minimum Volume (<span id="volume-label-value">100</span>)
                            </label>
                            <div id="volume-range-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span id="volume-min-value">0</span>
                                <span id="volume-max-value">1000</span>
                            </div>
                        </div>

                        <!-- Min Probability Slider -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Min Prob. of Being Acquired (<span id="probability-label-value">25</span>%)
                            </label>
                            <div id="min-probability-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span id="probability-min-value">0%</span>
                                <span id="probability-max-value">100%</span>
                            </div>
                        </div>

                        <!-- Add Prob of Assignment Slider -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Min Prob. of Being Assigned (<span id="prob-assign-label-value">25</span>%)
                            </label>
                            <div id="prob-assign-range-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span id="prob-assign-min-value">0%</span>
                                <span id="prob-assign-max-value">100%</span>
                            </div>
                        </div>

                    </div>

                    <!-- Expiry Tabs -->
                    <div id="cash-secured-put-scanner-expiry-tabs"
                        class="border flex gap-0 mb-4 overflow-x-auto p-1 rounded-md mt-4 md:mt-0 overflow-auto text-nowrap">
                    </div>

                    <!-- View Summary Button -->
                    <div class="flex justify-between items-center mt-4 pt-4 border-t">
                        <button id="view-summary-btn" class="text-blue-500 text-blue-800 underline text-sm ml-auto">
                            View Summary
                        </button>
                    </div>
                </div>


                <!-- Scanner Results -->
                <div id="cash-put-scanner-table" class="lg:max-h-[calc(100vh_-_360px)] custom-scroll overflow-y-auto">
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Modal -->
    <div id="summaryModal"
        class="modal fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center hidden z-50 p-4">
        <div
            class="modal-content bg-white dark:bg-neutral-800 relative w-full max-w-7xl max-h-[90vh] rounded-lg shadow-lg flex flex-col">
            <!-- Modal Header -->
            <div class="p-4 border-b dark:border-neutral-700">
                <h2 class="text-xl font-bold">Cash Secured Put Summary</h2>
                <button id="closeSummaryModal"
                    class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 text-2xl font-bold">
                    &times;
                </button>
            </div>

            <!-- Modal Body with Scrollable Content -->
            <div class="flex-1 overflow-auto p-4">
                <div id="summaryTableContainer"></div>
            </div>
        </div>
    </div>
</main>

<script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.css" />

{%include 'blocks/stock-symbols.html' %}

<style>
    /* Make sliders show pointer cursor on hover */
    .noUi-target,
    .noUi-handle,
    .noUi-connect {
        cursor: pointer !important;
    }
</style>

<script>
    document.addEventListener("DOMContentLoaded", () => {

        // Global variables
        let currentTickers = niftyTickers;
        const scannedData = {};
        let selectedExpiry = null;
        let scannedTickers = [];

        // DOM elements
        const tickerSelectionContainer = document.getElementById(
            "cash-secured-put-ticker-selection"
        );
        const selectAllBtn = document.getElementById("select-all");
        const unselectAllBtn = document.getElementById("unselect-all");
        const scanButton = document.getElementById("cash-secured-put-show-button");
        const expiryTabsContainer = document.getElementById(
            "cash-secured-put-scanner-expiry-tabs"
        );
        const tableContainer = document.getElementById("cash-put-scanner-table");
        const costInput = document.getElementById("cash-secured-put-cost-input");
        const stockCategoryRadios = document.getElementsByName(
            "cash-secured-put-stock-category"
        );

        // Initialize sliders
        const minDiscountSlider = document.getElementById('min-discount-slider');
        const minProbabilitySlider = document.getElementById('min-probability-slider');

        noUiSlider.create(minDiscountSlider, {
            start: [3],
            connect: [true, false],
            range: {
                'min': 0,
                'max': 20
            },
            step: 0.1,
            format: {
                to: value => value.toFixed(1),
                from: value => parseFloat(value)
            }
        });

        noUiSlider.create(minProbabilitySlider, {
            start: [25],
            connect: [true, false],
            range: {
                'min': 0,
                'max': 100
            },
            step: 0.1,
            format: {
                to: value => value.toFixed(1),
                from: value => parseFloat(value)
            }
        });

        // Update value displays
        minDiscountSlider.noUiSlider.on('update', function (values) {
            document.getElementById('discount-max-value').textContent = '20%';
            document.getElementById('discount-label-value').textContent = values[0];
            updateResults();
        });

        minProbabilitySlider.noUiSlider.on('update', function (values) {
            document.getElementById('probability-max-value').textContent = '100%';
            document.getElementById('probability-label-value').textContent = values[0];
            updateResults();
        });

        // Initialize volume slider
        const volumeSlider = document.getElementById("volume-range-slider");
        noUiSlider.create(volumeSlider, {
            start: [100],
            connect: [true, false],
            range: {
                'min': 0,
                'max': 1000
            },
            step: 10,
            format: {
                to: value => Math.round(value),
                from: value => parseInt(value)
            }
        });

        // Initialize probability of assignment slider
        const probAssignSlider = document.getElementById("prob-assign-range-slider");
        noUiSlider.create(probAssignSlider, {
            start: [25],
            connect: [true, false],
            range: {
                'min': 0,
                'max': 100
            },
            step: 1,
            format: {
                to: value => Math.round(value),
                from: value => parseInt(value)
            }
        });

        // Add update handlers
        volumeSlider.noUiSlider.on('update', function (values) {
            document.getElementById('volume-label-value').textContent = values[0];
            updateResults();
        });

        probAssignSlider.noUiSlider.on('update', function (values) {
            document.getElementById('prob-assign-label-value').textContent = values[0];
            updateResults();
        });

        // Build ticker selection checkboxes
        function buildTickerSelection(tickerArray) {
            let html = '<div class="border border-l-0 divide-x divide-y grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 md:grid-cols-4">';
            tickerArray.forEach((ticker) => {
                html += `
                    <label class="flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-neutral-700 first:border-l text-sm">
                        <input type="checkbox" value="${ticker}" class="ticker-checkbox" checked>
                        <span class="text-sm">${ticker}</span>
                    </label>
                `;
            });
            html += "</div>";
            tickerSelectionContainer.innerHTML = html;
        }
        buildTickerSelection(currentTickers);

        // Stock category radio change handler
        stockCategoryRadios.forEach((radio) => {
            radio.addEventListener("change", function () {
                currentTickers = this.value === "nifty" ? niftyTickers : foTickers;
                buildTickerSelection(currentTickers);
            });
        });

        // Select/Unselect all handlers
        selectAllBtn.addEventListener("click", () => {
            document
                .querySelectorAll(".ticker-checkbox")
                .forEach((cb) => (cb.checked = true));
        });
        unselectAllBtn.addEventListener("click", () => {
            document
                .querySelectorAll(".ticker-checkbox")
                .forEach((cb) => (cb.checked = false));
        });

        // Scan button handler
        scanButton.addEventListener("click", async () => {
            // Clear previous data
            tableContainer.innerHTML = "";
            expiryTabsContainer.innerHTML = "";
            Object.keys(scannedData).forEach((key) => delete scannedData[key]);
            scannedTickers = [];
            selectedExpiry = null;

            // Hide filter section at start of new scan
            document.getElementById("results-filter-section").classList.add("hidden");

            const globalCost = parseFloat(costInput.value) || 0;

            // Get selected tickers
            const selected = Array.from(
                document.querySelectorAll(".ticker-checkbox:checked")
            ).map((cb) => cb.value);

            if (selected.length === 0) {
                showAlert("Please select at least one ticker.");
                return;
            }

            scannedTickers = selected;

            // Show loading state
            tableContainer.innerHTML = ``;

            try {
                // Process all tickers concurrently
                await Promise.all(
                    scannedTickers.map((ticker) => processTicker(ticker, globalCost))
                );

                const firstTicker = scannedTickers[0];
                if (scannedData[firstTicker]?.expiryDates?.length > 0) {
                    buildExpiryTabs(scannedData[firstTicker].expiryDates);
                    selectedExpiry = scannedData[firstTicker].expiryDates[0];

                    // Show filter section only if we have valid data
                    if (Object.keys(scannedData).some(ticker =>
                        scannedData[ticker].putsByExpiry[selectedExpiry]?.length > 0)) {
                        document.getElementById("results-filter-section").classList.remove("hidden");
                    }

                    showAllTickersData();
                } else {
                    tableContainer.innerHTML = `
                        <p class="text-red-500">No expiry data available for ${firstTicker}</p>
                    `;
                }
            } catch (err) {
                console.error("Error processing tickers:", err);
                // Hide filter section on error
                document.getElementById("results-filter-section").classList.add("hidden");
                tableContainer.innerHTML = `
                    <p class="text-red-500">Error processing tickers. Please try again.</p>
                `;
            }
        });

        // Process single ticker
        async function processTicker(ticker, globalCost) {
            let tickerContainer = document.getElementById("ticker-" + ticker);
            if (!tickerContainer) {
                tickerContainer = document.createElement("div");
                tickerContainer.id = "ticker-" + ticker;
                tickerContainer.className = "mb-3 p-3 rounded-md bg-white dark:bg-neutral-800 dark:border-neutral-700 rounded-lg";
                tickerContainer.innerHTML = `
                        <h2 id="ticker-header-${ticker}" class="flex flex-col md:flex-row gap-3 font-medium md:items-center justify-between mb-2 text-base">${ticker} - Scanning...</h2>
                        <div id="ticker-content-${ticker}"></div>
                    `;
                tableContainer.appendChild(tickerContainer);
            }

            const contentContainer = document.getElementById("ticker-content-" + ticker);
            try {
                // Remove the extra price fetch.
                // Instead, fetch the options data and extract the underlying value.
                const optionsResponse = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${ticker}`);

                const optionsData = await optionsResponse.json();

                // Validate options data
                if (!optionsData || !optionsData.records) {
                    console.warn(`No options data available for ${ticker}`);
                    return;
                }

                // Extract underlying price from the first available put option (PE)
                let extractedPrice = 0;
                if (optionsData.records.data && optionsData.records.data.length > 0) {
                    for (const opt of optionsData.records.data) {
                        if (opt.PE && opt.PE.underlyingValue) {
                            extractedPrice = opt.PE.underlyingValue;
                            break;
                        }
                    }
                }

                // Use globalCost if provided, otherwise the extracted underlying price
                const costBasis = globalCost || extractedPrice;

                // Store data
                scannedData[ticker] = {
                    costBasis: costBasis,
                    expiryDates: optionsData.records.expiryDates || [],
                    putsByExpiry: processOptionsData(optionsData.records)
                };

                tickerContainer.querySelector("h2").innerHTML = `${ticker} - Price: ${extractedPrice.toFixed(2)}`;
            } catch (error) {
                console.error(`Failed to process ${ticker}:`, error);
                // Remove the ticker from scannedTickers if processing failed
                scannedTickers = scannedTickers.filter(t => t !== ticker);
                contentContainer.innerHTML = `<p class="text-red-500">Failed to fetch data for ${ticker}</p>`;
            }
        }

        // Process options data
        function processOptionsData(records) {
            const putsByExpiry = {};
            if (records && records.expiryDates && records.expiryDates.length) {
                records.expiryDates.forEach((date) => (putsByExpiry[date] = []));
                if (records.data) {
                    records.data.forEach((opt) => {
                        if (opt && opt.PE) {
                            putsByExpiry[opt.expiryDate].push(opt);
                        }
                    });
                }
            }
            return putsByExpiry;
        }

        // Build expiry tabs
        function buildExpiryTabs(expiryDates) {
            expiryTabsContainer.innerHTML = expiryDates
                .map(
                    (date, idx) => `
                <button 
                    class="tab-btn px-4 py-1.5 rounded-sm text-xs font-medium
                           ${idx === 0
                            ? "bg-blue-500 text-white"
                            : "bg-white dark:bg-neutral-800"
                        }"
                    data-expiry="${date}"
                >
                    ${date}
                </button>
            `
                )
                .join("");

            document.querySelectorAll(".tab-btn").forEach((btn) => {
                btn.addEventListener("click", function () {
                    document.querySelectorAll(".tab-btn").forEach((b) => {
                        b.classList.remove("bg-blue-500", "text-white");
                        b.classList.add("bg-white", "dark:bg-neutral-800");
                    });
                    this.classList.remove("bg-white", "dark:bg-neutral-800");
                    this.classList.add("bg-blue-500", "text-white");

                    selectedExpiry = this.dataset.expiry;
                    showAllTickersData();
                });
            });
        }

        // Show data for all tickers
        function showAllTickersData() {
            if (!selectedExpiry) {
                document.getElementById("results-filter-section").classList.add("hidden");
                tableContainer.innerHTML = '<p class="text-center py-4">No expiry dates available.</p>';
                return;
            }

            const minDiscount = parseFloat(minDiscountSlider.noUiSlider.get());
            const minProb = parseFloat(minProbabilitySlider.noUiSlider.get());

            let allPutsHtml = "";
            let hasValidData = false;

            scannedTickers.forEach((ticker) => {
                const tickerData = scannedData[ticker];
                if (!tickerData) return;

                const putOptions = tickerData.putsByExpiry[selectedExpiry] || [];
                const filteredData = filterPutOptions(
                    putOptions,
                    tickerData.costBasis,
                    minDiscount,
                    minProb
                );

                if (filteredData.length) {
                    hasValidData = true;
                    allPutsHtml += generateTickerTableHtml(
                        ticker,
                        filteredData,
                        tickerData.costBasis
                    );
                }
            });

            if (!hasValidData) {
                tableContainer.innerHTML = '<p class="text-center py-4">No matching puts found.</p>';
            } else {
                document.getElementById("results-filter-section").classList.remove("hidden");
                tableContainer.innerHTML = allPutsHtml;
            }
        }

        // Filter put options
        function filterPutOptions(putOptions, costBasis, minDiscount, minProb) {
            const minVolume = parseInt(volumeSlider.noUiSlider.get());
            const minProbAssign = parseInt(probAssignSlider.noUiSlider.get());

            return putOptions
                .map((opt) => {
                    const pe = opt.PE;
                    const strike = pe.strikePrice;
                    const premium = pe.lastPrice || 0;
                    const volume = pe.totalTradedVolume || 0;
                    const effectivePrice = strike - premium;
                    const discountPercent = costBasis > 0 ? ((costBasis - effectivePrice) / costBasis) * 100 : 0;
                    const probAcquire = pe.delta != null ? Math.abs(pe.delta) * 100 : 0;
                    const probAssign = pe.delta != null ? (1 - Math.abs(pe.delta)) * 100 : 0;
                    const premiumPct = strike > 0 ? (premium / strike) * 100 : 0;

                    return {
                        strike,
                        premium,
                        premiumPct,
                        volume,
                        effectivePrice,
                        discountPercent,
                        probAcquire,
                        probAssign,
                        isITM: costBasis > 0 && strike > costBasis,
                    };
                })
                .filter(
                    (row) =>
                        !row.isITM &&
                        row.discountPercent >= minDiscount &&
                        row.probAcquire >= minProb &&
                        row.volume >= minVolume &&
                        row.probAssign >= minProbAssign
                )
                .sort((a, b) => b.discountPercent - a.discountPercent);
        }

        // Generate HTML for ticker table
        function generateTickerTableHtml(ticker, putData, costBasis) {
            const tableId = `csp-table-${ticker}`;
            const html = `
                <div class="mb-3 p-4 rounded-lg bg-white dark:bg-neutral-800 dark:border-neutral-700">
                    <h3 class="text-base font-medium mb-2">${ticker} - Cost: ${costBasis.toFixed(2)}</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border dark:border-neutral-700 xl:text-sm text-xs" id="${tableId}">
                            <thead class="bg-gray-50 dark:bg-neutral-700 text-neutral-800">
                                <tr>
                                    <th class="border dark:border-neutral-600 p-2" data-col="strike">Strike</th>
                                    <th class="border dark:border-neutral-600 p-2" data-col="premium">Premium</th>
                                    <th class="border dark:border-neutral-600 p-2" data-col="premiumPct">Premium %</th>
                                    <th class="border dark:border-neutral-600 p-2" data-col="volume">Volume</th>
                                    <th class="border dark:border-neutral-600 p-2 text-nowrap" data-col="effectivePrice">Eff. Price</th>
                                    <th class="border dark:border-neutral-600 p-2 text-nowrap" data-col="discountPercent">Discount%</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${putData.map(row => {
                const premiumPct = (row.premium / costBasis) * 100;
                return `
                                        <tr class="hover:bg-gray-50 dark:hover:bg-neutral-700/50">
                                            <td class="border dark:border-neutral-600 p-2 text-center">${row.strike}</td>
                                            <td class="border dark:border-neutral-600 p-2 text-center">${row.premium.toFixed(2)}</td>
                                            <td class="border dark:border-neutral-600 p-2 text-center">${premiumPct.toFixed(2)}%</td>
                                            <td class="border dark:border-neutral-600 p-2 text-center">${row.volume}</td>
                                            <td class="border dark:border-neutral-600 p-2 text-center">${row.effectivePrice.toFixed(2)}</td>
                                            <td class="border dark:border-neutral-600 p-2 text-center ${row.discountPercent >= 0 ? "text-green-600" : "text-red-500"}">
                                                ${row.discountPercent.toFixed(2)}%
                                            </td>
                                        </tr>
                                    `;
            }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            // Return the HTML content
            setTimeout(() => window.addSorting(tableId), 0);
            return html;
        }

        // Build Summary Modal
        function buildSummaryModal() {
            const summaryData = [];
            const minDiscount = parseFloat(minDiscountSlider.noUiSlider.get());
            const minProb = parseFloat(minProbabilitySlider.noUiSlider.get());
            const minVolume = parseInt(volumeSlider.noUiSlider.get());
            const minProbAssign = parseInt(probAssignSlider.noUiSlider.get());

            scannedTickers.forEach((ticker) => {
                const tickerData = scannedData[ticker];
                if (!tickerData) return;

                const putOptions = tickerData.putsByExpiry[selectedExpiry] || [];
                putOptions.forEach((opt) => {
                    const pe = opt.PE;
                    const strike = pe.strikePrice;
                    const premium = pe.lastPrice || 0;
                    const volume = pe.totalTradedVolume || 0;
                    const effectivePrice = strike - premium;
                    const costBasis = tickerData.costBasis;
                    const discountPercent = costBasis > 0 ? ((costBasis - effectivePrice) / costBasis) * 100 : 0;
                    const premiumPct = strike > 0 ? (premium / strike) * 100 : 0;
                    const probAcquire = pe.delta != null ? Math.abs(pe.delta) * 100 : 0;
                    const probAssign = pe.delta != null ? (1 - Math.abs(pe.delta)) * 100 : 0;

                    // Apply all filters consistently
                    if (costBasis > 0 && strike > costBasis) return; // Skip ITM puts
                    if (discountPercent < minDiscount) return;
                    if (probAcquire < minProb) return;
                    if (volume < minVolume) return;
                    if (probAssign < minProbAssign) return;

                    summaryData.push({
                        ticker,
                        costBasis,
                        strike,
                        premium,
                        premiumPct,
                        effectivePrice,
                        discountPercent,
                        probAcquire,
                        probAssign,
                        volume
                    });
                });
            });

            summaryData.sort((a, b) => b.discountPercent - a.discountPercent);

            const summaryHtml = `
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border dark:border-neutral-700 text-sm" id="csp-summary-table">
                        <thead class="bg-gray-50 dark:bg-neutral-700">
                            <tr>
                                <th class="border dark:border-neutral-600 p-2" data-col="ticker">Stock</th>
                                <th class="border dark:border-neutral-600 p-2" data-col="costBasis">Stock Price</th>
                                <th class="border dark:border-neutral-600 p-2" data-col="strike">Strike</th>
                                <th class="border dark:border-neutral-600 p-2" data-col="premium">Premium</th>
                                <th class="border dark:border-neutral-600 p-2" data-col="premiumPct">Premium %</th>
                                <th class="border dark:border-neutral-600 p-2" data-col="volume">Volume</th>
                                <th class="border dark:border-neutral-600 p-2" data-col="effectivePrice">Eff. Price</th>
                                <th class="border dark:border-neutral-600 p-2" data-col="discountPercent">Discount%</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${summaryData.map((item) => `
                                <tr class="hover:bg-gray-50 dark:hover:bg-neutral-700/50">
                                    <td class="border dark:border-neutral-600 p-2 text-center">${item.ticker}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center">${item.costBasis.toFixed(2)}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center">${item.strike}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center">${item.premium.toFixed(2)}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center">${item.premiumPct.toFixed(2)}%</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center">${item.volume}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center">${item.effectivePrice.toFixed(2)}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center ${item.discountPercent >= 0 ? "text-green-600" : "text-red-500"}">
                                        ${item.discountPercent.toFixed(2)}%
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById("summaryTableContainer").innerHTML = summaryHtml;
            document.getElementById("summaryModal").classList.remove("hidden");

            // Add sorting to summary table
            setTimeout(() => window.addSorting('csp-summary-table'), 0);
        }

        // Close summary modal handler
        document
            .getElementById("closeSummaryModal")
            .addEventListener("click", () => {
                document.getElementById("summaryModal").classList.add("hidden");
            });

        // View Summary button handler
        document
            .getElementById("view-summary-btn")
            .addEventListener("click", () => {
                if (selectedExpiry) {
                    buildSummaryModal();
                } else {
                    showAlert("Please scan stocks first");
                }
            });

        // Update results function
        function updateResults() {
            if (selectedExpiry && scannedTickers.length > 0) {
                showAllTickersData();
            }
        }

        // Update cost basis when cost input changes
        costInput.addEventListener('input', function () {
            const globalCost = parseFloat(this.value) || 0;
            scannedTickers.forEach(ticker => {
                if (scannedData[ticker]) {
                    // Override the previously extracted costBasis with the global cost if provided
                    scannedData[ticker].costBasis = globalCost || scannedData[ticker].costBasis;
                }
            });
            updateResults();
        });
    });
</script>