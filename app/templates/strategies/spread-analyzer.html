<!DOCTYPE html>
<html lang="en">

<head>
    <title>Bull/Bear Spread Analyzer Across Multiple Strikes | AI Bull</title>
    <meta name="description"
        content="Analyze Bull and Bear spreads across multiple strike prices with ease. Compare premiums, risks, and rewards to optimize your options strategy for better trading decisions." />
    <link rel="canonical" href="https://theaibull.com/strategies/spread-analyzer" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/strategies/spread-analyzer" />
    <meta property="og:title" content="Spread Analyzer - Analyze Bull/Bear Spreads Across Multiple Strikes" />
    <meta property="og:description"
        content="Analyze Bull and Bear spreads across multiple strike prices with ease. Compare premiums, risks, and rewards to optimize your options strategy for better trading decisions." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title" content="Spread Analyzer - Analyze Bull/Bear Spreads Across Multiple Strikes" />
    <meta name="twitter:description"
        content="Easily analyze Bull and Bear spreads across multiple strikes. Optimize your options strategy by comparing premiums, risks, and rewards for better trading decisions." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />

    {% include 'blocks/head.html' %}
</head>

<body class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100">
    {% include 'blocks/common.html' %}
    {%include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <main>
            <div class="p-3 mx-auto max-w-7xl hover:[&_.spread-cell]:shadow-xl">
                <!-- Header Section -->
                <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2 rounded-lg mb-3">
                    <div class="md:flex justify-between items-center">
                        <div class="flex items-center">
                            <h1 class="md:text-xl text-lg font-semibold tracking-tight">Spread Analyzer</h1>
                            <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">
                                Analyze Bull/Bear spreads across multiple strikes
                            </h2>
                            <!-- Button to trigger modal open -->
                            <button id="openModalBtn" onclick="openModal()" class="ml-2">

                                <div class="flex group text-sm">
                                    <div
                                        class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                        <div
                                            class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-circle-help">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                    <path d="M12 17h.01"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div
                                            class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                            <div
                                                class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                                How to Use This Page</div>
                                        </div>
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Search Tab Content -->
                <div id="search-content" class="tab-content">
                    <div class="flex flex-col gap-3">
                        <!-- Search Input Area -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                            <div class="flex flex-col gap-6">
                                <div>
                                    {% with show_submit_button=false %}
                                    {% include 'blocks/search-active-recent-stocks.html' %}
                                    {% endwith %}
                                </div>

                                <!-- Symbol + Min Volume Inputs -->
                                <div class="flex flex-col md:flex-row gap-4 items-center">
                                    <div class="w-full md:w-1/3">
                                        <button id="stock-input-submit-btn"
                                            class="bg-blue-500 block focus:outline-0 font-medium hover:bg-blue-600 px-6 py-2.5 rounded-md text-white w-full">
                                            Generate Spread Table
                                        </button>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <label for="min-volume-input" class="text-sm font-medium">Min Volume:</label>
                                        <input type="number" id="min-volume-input" value="100"
                                            class="w-20 px-2 py-1 border rounded text-sm">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Loading Spinner -->
                        <div id="loading-animation" class="hidden flex justify-center items-center m-4 bg-white p-4">
                            <div class="w-16 h-16 border-4 border-blue-500 border-dashed rounded-full animate-spin">
                            </div>
                        </div>

                        <!-- Strategy Toggle Buttons -->
                        <div id="strategy-toggle" class="bg-white dark:bg-neutral-800 rounded-lg border hidden">
                            <div class="flex justify-center">
                                <button id="bull-toggle"
                                    class="bg-green-700 text-white px-4 py-3 w-1/2 font-medium rounded-l-lg">Bullish</button>
                                <button id="bear-toggle"
                                    class="bg-gray-200 text-gray-800 px-4 py-3 w-1/2 font-medium rounded-r-lg">Bearish</button>
                            </div>
                        </div>

                        <!-- Expiry Date Tabs -->
                        <div id="expiry-tabs"
                            class="bg-white dark:bg-neutral-800 rounded-lg p-3 hidden overflow-x-auto">
                            <div class="flex md:flex-wrap gap-1" id="expiry-tabs-container">
                                <!-- Expiry date tabs will be added here dynamically -->
                            </div>
                        </div>

                        <!-- Spread Table Area -->
                        <div id="spread-table-container"
                            class="bg-white dark:bg-neutral-800 rounded-lg p-4 hidden overflow-auto relative">
                            <!-- Use border-collapse: collapse for a tight layout -->
                            <table id="spread-table"
                                class="w-full border-collapse border dark:border-neutral-700 xl:text-sm text-xs">
                                <thead>
                                    <tr style="background-color: #f2f2f2;">
                                        <th class="border border-gray-300 p-2 font-medium" colspan="5">Left Spread</th>
                                        <th class="border border-gray-300 p-2 font-medium">Central Strike</th>
                                        <th class="border border-gray-300 p-2 font-medium" colspan="5">Right Spread</th>
                                    </tr>
                                </thead>
                                <tbody id="spread-table-body">
                                    <!-- Rows generated dynamically -->

                                </tbody>
                            </table>

                            <!-- Spread Preview Modal -->
                            <div id="spread-preview"
                                class="fixed top-[20%] right-[20%] bg-white rounded-lg border z-[50] max-w-[400px] shadow-2xl p-3 transiition-all"
                                style="display: none;">
                                <div style="position: relative;">
                                    <div style="position: absolute; top: 0; right: 0; cursor: pointer; font-size: 18px;"
                                        onclick="document.getElementById('spread-preview').style.display='none'">
                                        ×</div>
                                    <div id="spread-preview-content"></div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- FAQ Section in Accordion Format -->
                <div class="mt-8 text-sm">
                    <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
                    <div class="space-y-4">
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What is a Bull/Bear Spread?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    A Bull/Bear spread involves buying and selling options at different strike prices to
                                    capitalize on expected price
                                    movements, either upward (Bull) or downward (Bear), with limited risk and reward.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How do I analyze spreads with this tool?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Select the asset, set multiple strike prices, and the tool will display potential
                                    Bull/Bear spreads, comparing premiums,
                                    risk, and reward for each strike combination to help optimize your strategy.

                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                Can I analyze both Bull and Bear spreads?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Yes, this tool allows you to analyze both Bull and Bear spreads by adjusting strike
                                    prices and market outlook to suit
                                    your trading preference.

                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What factors are considered in the analysis?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    The tool analyzes strike prices, premiums, potential returns, and associated risks
                                    to help you make strategic decisions
                                    on Bull/Bear spread trades.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                Why should I use a Bull/Bear spread?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Bull/Bear spreads offer limited risk while taking advantage of market trends. They
                                    are useful for traders looking for
                                    defined profit potential with controlled exposure to market movements.
                                </p>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
        </main>
        <div class="mt-auto">
            {% include 'blocks/footer.html' %}
        </div>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.js"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.css">

        {%include 'blocks/stock-symbols.html' %}

        <style>
            /* Spinner animation */
            @keyframes spin {
                100% {
                    transform: rotate(360deg);
                }
            }

            .spin {
                animation: spin 1s linear infinite;
            }

            .spot-row {
                background-color: #dbeeff;
                font-weight: bold;
            }

            .spread-cell {
                cursor: pointer;
            }

            .itm-strong {
                background-color: #ffea00;
            }

            .itm-light {
                background-color: #fffacd;
            }

            .center-strike-cell {
                background-color: #f9f7d9;
                /* highlight color for central strike cell */
                text-align: center;
                padding: 0px;
            }
        </style>
        <script>
            let currentStrategy = 'bull';    // default strategy
            let lastRecords = null;          // store fetched records
            let minVolume = 100;             // default min volume
            let currentExpiry = null;        // current selected expiry date

            document.addEventListener('DOMContentLoaded', () => {
                const stockInput = document.getElementById('stock-input');
                const submitButton = document.getElementById('stock-input-submit-btn');
                const spreadTableBody = document.getElementById('spread-table-body');
                const spreadTableContainer = document.getElementById('spread-table-container');
                const strategyToggle = document.getElementById('strategy-toggle');
                const expiryTabs = document.getElementById('expiry-tabs');
                const expiryTabsContainer = document.getElementById('expiry-tabs-container');
                const bullToggle = document.getElementById('bull-toggle');
                const bearToggle = document.getElementById('bear-toggle');
                const loadingEl = document.getElementById('loading-animation');
                const minVolumeInput = document.getElementById('min-volume-input');

                bullToggle.addEventListener('click', () => {
                    currentStrategy = 'bull';
                    bullToggle.classList.add('bg-green-700', 'text-white');
                    bearToggle.classList.remove('bg-red-700', 'text-white');
                    if (lastRecords) renderFullTable(lastRecords);
                });
                bearToggle.addEventListener('click', () => {
                    currentStrategy = 'bear';
                    bearToggle.classList.add('bg-red-700', 'text-white');
                    bearToggle.classList.remove('text-gray-800');
                    bullToggle.classList.remove('bg-green-700', 'text-white');
                    if (lastRecords) renderFullTable(lastRecords);
                });

                // On change of minVolume
                minVolumeInput.addEventListener('change', () => {
                    minVolume = parseInt(minVolumeInput.value) || 0;
                    if (lastRecords) renderFullTable(lastRecords);
                });

                submitButton.addEventListener('click', () => {
                    const symbol = stockInput.value.trim().toUpperCase();
                    if (!symbol) {
                        showAlert('Please enter a valid Stock Symbol.');
                        return;
                    }
                    fetchOptionChain(symbol);
                });

                async function fetchOptionChain(symbol) {
                    // Clear previous results
                    spreadTableBody.innerHTML = '';
                    expiryTabsContainer.innerHTML = '';
                    currentExpiry = null;

                    // Hide containers until new data is loaded
                    strategyToggle.classList.add('hidden');
                    expiryTabs.classList.add('hidden');
                    spreadTableContainer.classList.add('hidden');

                    loadingEl.classList.remove('hidden');
                    try {
                        const response = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${symbol}`);
                        const data = await response.json();
                        lastRecords = data.records;

                        // get spot
                        let spot = 0;
                        for (const opt of data.records.data) {
                            if (opt.CE?.underlyingValue) {
                                spot = parseFloat(opt.CE.underlyingValue);
                                break;
                            }
                            if (opt.PE?.underlyingValue) {
                                spot = parseFloat(opt.PE.underlyingValue);
                                break;
                            }
                        }
                        window.currentStockPrice = spot;

                        strategyToggle.classList.remove('hidden');

                        // Generate expiry date tabs
                        generateExpiryTabs(data.records);

                        spreadTableContainer.classList.remove('hidden');
                        renderFullTable(data.records);
                    } catch (err) {
                        console.error('Error fetching option chain:', err);
                        showAlert('Failed to fetch option chain data.');
                    } finally {
                        loadingEl.classList.add('hidden');
                    }
                }

                // Function to generate expiry date tabs
                function generateExpiryTabs(records) {
                    if (!records.expiryDates || !records.expiryDates.length) {
                        return;
                    }

                    expiryTabsContainer.innerHTML = '';

                    // Show the expiry tabs container
                    expiryTabs.classList.remove('hidden');

                    // Use the currently selected expiry or find first valid one
                    let validExpiry = null;
                    currentExpiry = records.expiryDates?.[0];
                    if (!validExpiry) {
                        for (const expiry of records.expiryDates) {
                            const callOptions = records.data.filter(opt => opt.CE && opt.expiryDate === expiry);
                            const putOptions = records.data.filter(opt => opt.PE && opt.expiryDate === expiry);

                            if (callOptions.length > 0 && putOptions.length > 0) {
                                validExpiry = expiry;
                                currentExpiry = expiry;
                                break;
                            }
                        }
                    }

                    if (!currentExpiry) {
                        showAlert('No expiry dates found with complete option data.');
                        return;
                    }

                    // Create tab for each expiry date (show all dates)
                    records.expiryDates.forEach(expiry => {
                        const tab = document.createElement('button');
                        tab.className = `px-4 py-2 text-xs rounded-md border text-nowrap font-medium ${expiry === currentExpiry ?
                            'bg-blue-500 text-white' : 'bg-gray-100 text-gray-800'}`;

                        // Format the date for display (e.g., "24-Apr-2025")
                        const dateParts = expiry.split('-');
                        if (dateParts.length >= 3) {
                            const date = new Date(expiry);
                            const day = date.getDate();
                            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                            const month = monthNames[date.getMonth()];
                            const year = date.getFullYear();
                            tab.textContent = `${day}-${month}-${year}`;
                        } else {
                            tab.textContent = expiry;
                        }

                        tab.dataset.expiry = expiry;
                        tab.addEventListener('click', () => {
                            // Update active tab styling
                            document.querySelectorAll('#expiry-tabs-container button').forEach(btn => {
                                btn.classList.remove('bg-blue-500', 'text-white');
                                btn.classList.add('bg-gray-100', 'text-gray-800');
                            });
                            tab.classList.remove('bg-gray-100', 'text-gray-800');
                            tab.classList.add('bg-blue-500', 'text-white');

                            // Update current expiry and re-render
                            currentExpiry = expiry;
                            renderFullTable(lastRecords);
                        });

                        expiryTabsContainer.appendChild(tab);
                    });
                }

                function renderFullTable(records) {
                    if (!records.expiryDates || !records.expiryDates.length) {
                        showAlert('No expiry data found.');
                        return;
                    }

                    // Use the currently selected expiry
                    const validExpiry = currentExpiry;

                    // Check if this expiry has valid data
                    const callOptions = records.data.filter(opt => opt.CE && opt.expiryDate === validExpiry);
                    const putOptions = records.data.filter(opt => opt.PE && opt.expiryDate === validExpiry);

                    if (callOptions.length === 0 || putOptions.length === 0) {
                        // Show empty state when no valid data exists for this expiry
                        spreadTableBody.innerHTML = '';

                        const emptyRow = document.createElement('tr');
                        const emptyCell = document.createElement('td');
                        emptyCell.colSpan = 11;
                        emptyCell.className = 'p-6 text-center text-gray-500';
                        emptyCell.innerHTML = `
                            <div class="flex flex-col items-center justify-center py-8">
                                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mb-3 text-gray-400">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="8" x2="12" y2="12"></line>
                                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                </svg>
                                <p class="text-lg font-medium">No complete option data available for this expiry date</p>
                                <p class="mt-1">Please select a different expiry date</p>
                            </div>
                        `;
                        emptyRow.appendChild(emptyCell);
                        spreadTableBody.appendChild(emptyRow);
                        return;
                    }

                    // Build mappings
                    const callMapping = {};
                    callOptions.forEach(opt => {
                        if (!callMapping[opt.CE.strikePrice]) {
                            callMapping[opt.CE.strikePrice] = opt.CE;
                        }
                    });
                    const putMapping = {};
                    putOptions.forEach(opt => {
                        if (!putMapping[opt.PE.strikePrice]) {
                            putMapping[opt.PE.strikePrice] = opt.PE;
                        }
                    });

                    // Union of strikes
                    const strikesSet = new Set();
                    Object.keys(callMapping).forEach(s => strikesSet.add(Number(s)));
                    Object.keys(putMapping).forEach(s => strikesSet.add(Number(s)));
                    const strikes = Array.from(strikesSet).sort((a, b) => a - b);

                    spreadTableBody.innerHTML = '';
                    strikes.forEach((strike, idx) => {
                        // Insert a spot row if needed
                        if (idx > 0 && window.currentStockPrice > strikes[idx - 1] && window.currentStockPrice < strike) {
                            const spotRow = document.createElement('tr');
                            spotRow.className = 'spot-row';
                            const spotTd = document.createElement('td');
                            spotTd.colSpan = 11;
                            spotTd.className = 'p-3 text-center border';
                            spotTd.style.textAlign = 'center';
                            spotTd.innerHTML = `<div class="w-full text-center">Spot Price: ${window.currentStockPrice.toFixed(2)}</div>`;
                            spotRow.appendChild(spotTd);
                            spreadTableBody.appendChild(spotRow);
                        }

                        const row = document.createElement('tr');

                        // Add left spread cells directly
                        row.innerHTML += generateSpreadGrid('left', strike, strikes.filter(s => s < strike).slice(-5), callMapping, putMapping);

                        // Center Strike
                        row.innerHTML += `
                            <td class="border p-2 text-center center-strike-cell">
                                <div class="font-semibold mb-2">${strike}</div>
                                <div class="text-xs flex justify-center gap-2">
                                    <span>C: ${callMapping[strike] ? callMapping[strike].lastPrice : '-'}</span>
                                    <span class="text-gray-400">|</span>
                                    <span>P: ${putMapping[strike] ? putMapping[strike].lastPrice : '-'}</span>
                                </div>
                            </td>
                        `;

                        // Add right spread cells directly
                        row.innerHTML += generateSpreadGrid('right', strike, strikes.filter(s => s > strike).slice(0, 5), callMapping, putMapping);

                        spreadTableBody.appendChild(row);
                    });
                }

                function generateSpreadGrid(side, centralStrike, spreadStrikes, callMapping, putMapping) {
                    const numCols = 5;
                    const arr = [...spreadStrikes];
                    while (arr.length < numCols) arr.push(null);

                    let html = '';
                    arr.forEach(s => {
                        if (!s) {
                            html += `<td class="border p-2"></td>`;
                            return;
                        }
                        if (side === 'left') {
                            const lowerCall = callMapping[s];
                            const centralCall = callMapping[centralStrike];
                            if (!lowerCall || !centralCall) {
                                html += `<td class="border p-2"></td>`;
                                return;
                            }
                            // Volume check
                            if (lowerCall.totalTradedVolume < minVolume || centralCall.totalTradedVolume < minVolume) {
                                html += `<td class="border p-2"></td>`;
                                return;
                            }
                            // Net
                            const netValue = parseFloat(lowerCall.lastPrice) - parseFloat(centralCall.lastPrice);
                            if (netValue === 0) {
                                html += `<td class="border p-2"></td>`;
                                return;
                            }
                            // ITM
                            const lowerITM = s < window.currentStockPrice;
                            const centralITM = centralStrike < window.currentStockPrice;
                            let highlightClass = '';
                            if (lowerITM && centralITM) highlightClass = 'itm-strong';
                            else if (lowerITM || centralITM) highlightClass = 'itm-light';

                            html += buildSpreadCell({
                                side,
                                highlightClass,
                                netValue,
                                strike: s,
                                centralStrike,
                                centralOpt: centralCall,
                                spreadOpt: lowerCall
                            });
                        } else {
                            // side === 'right'
                            const higherPut = putMapping[s];
                            const centralPut = putMapping[centralStrike];
                            if (!higherPut || !centralPut) {
                                html += `<td class="border p-2"></td>`;
                                return;
                            }
                            // Volume check
                            if (higherPut.totalTradedVolume < minVolume || centralPut.totalTradedVolume < minVolume) {
                                html += `<td class="border p-2"></td>`;
                                return;
                            }
                            const netValue = parseFloat(centralPut.lastPrice) - parseFloat(higherPut.lastPrice);
                            if (netValue === 0) {
                                html += `<td class="border p-2"></td>`;
                                return;
                            }
                            // ITM
                            const higherITM = s > window.currentStockPrice;
                            const centralITM = centralStrike > window.currentStockPrice;
                            let highlightClass = '';
                            if (higherITM && centralITM) highlightClass = 'itm-strong';
                            else if (higherITM || centralITM) highlightClass = 'itm-light';

                            html += buildSpreadCell({
                                side,
                                highlightClass,
                                netValue,
                                strike: s,
                                centralStrike,
                                centralOpt: centralPut,
                                spreadOpt: higherPut
                            });
                        }
                    });
                    return html;
                }

                function buildSpreadCell({ side, highlightClass, netValue, strike, centralStrike, centralOpt, spreadOpt }) {
                    return `
                        <td 
                            class="border p-2 text-center cursor-pointer ${highlightClass}"
                            data-side="${side}"
                            data-central-strike="${centralStrike}"
                            data-spread-strike="${strike}"
                            data-central-last="${centralOpt.lastPrice}"
                            data-central-bid="${centralOpt.bidprice}"
                            data-central-ask="${centralOpt.askPrice}"
                            data-central-volume="${centralOpt.totalTradedVolume}"
                            data-spread-last="${spreadOpt.lastPrice}"
                            data-spread-bid="${spreadOpt.bidprice}"
                            data-spread-ask="${spreadOpt.askPrice}"
                            data-spread-volume="${spreadOpt.totalTradedVolume}"
                            data-net="${netValue.toFixed(2)}"
                            data-strategy="${currentStrategy}"
                            onclick="showSpreadPreview(event)"
                        >
                            <div class="font-medium">${strike}</div>
                            <div class="text-xs">${netValue.toFixed(2)}</div>
                        </td>
                    `;
                }

                // Show preview in modal
                window.showSpreadPreview = function (event) {
                    event.stopPropagation();
                    const cell = event.currentTarget;
                    const side = cell.dataset.side;
                    const net = cell.dataset.net;
                    const strategy = cell.dataset.strategy;
                    const centralStrike = cell.dataset.centralStrike;
                    const spreadStrike = cell.dataset.spreadStrike;

                    const centralLast = cell.dataset.centralLast;
                    const centralBid = cell.dataset.centralBid;
                    const centralAsk = cell.dataset.centralAsk;
                    const centralVolume = cell.dataset.centralVolume;

                    const spreadLast = cell.dataset.spreadLast;
                    const spreadBid = cell.dataset.spreadBid;
                    const spreadAsk = cell.dataset.spreadAsk;
                    const spreadVolume = cell.dataset.spreadVolume;

                    let previewHtml = `<div style="font-size:0.9em;">`;
                    previewHtml += `<div style="position: absolute; top: 0; right: 0; cursor: pointer; font-size: 18px;" onclick="document.getElementById('spread-preview').style.display='none'">×</div>`;

                    if (side === 'left') {
                        if (strategy === 'bull') {
                            previewHtml += `<h3 class="font-medium">Bull Call Spread</h3>`;
                            previewHtml += buildPreviewTable({
                                lines: [
                                    [`Sell ${centralStrike} call`, `@ ${centralLast}`, `Bid: ${centralBid}, Vol: ${centralVolume}`],
                                    [`Buy ${spreadStrike} call`, `@ ${spreadLast}`, `Ask: ${spreadAsk}, Vol: ${spreadVolume}`]
                                ],
                                netType: 'Net Debit',
                                netValue: net
                            });
                        } else {
                            previewHtml += `<h3 class="font-medium">Bear Call Spread</h3>`;
                            previewHtml += buildPreviewTable({
                                lines: [
                                    [`Sell ${spreadStrike} call`, `@ ${spreadLast}`, `Bid: ${spreadBid}, Vol: ${spreadVolume}`],
                                    [`Buy ${centralStrike} call`, `@ ${centralLast}`, `Ask: ${centralAsk}, Vol: ${centralVolume}`]
                                ],
                                netType: 'Net Credit',
                                netValue: net
                            });
                        }
                    } else {
                        // side === 'right'
                        if (strategy === 'bull') {
                            previewHtml += `<h3 class="font-medium">Bull Put Spread</h3>`;
                            previewHtml += buildPreviewTable({
                                lines: [
                                    [`Sell ${centralStrike} put`, `@ ${centralLast}`, `Bid: ${centralBid}, Vol: ${centralVolume}`],
                                    [`Buy ${spreadStrike} put`, `@ ${spreadLast}`, `Ask: ${spreadAsk}, Vol: ${spreadVolume}`]
                                ],
                                netType: 'Net Credit',
                                netValue: net
                            });
                        } else {
                            previewHtml += `<h3 class="font-medium">Bear Put Spread</h3>`;
                            previewHtml += buildPreviewTable({
                                lines: [
                                    [`Sell ${spreadStrike} put`, `@ ${spreadLast}`, `Bid: ${spreadBid}, Vol: ${spreadVolume}`],
                                    [`Buy ${centralStrike} put`, `@ ${centralLast}`, `Ask: ${centralAsk}, Vol: ${centralVolume}`]
                                ],
                                netType: 'Net Debit',
                                netValue: net
                            });
                        }
                    }
                    previewHtml += `</div>`;

                    document.getElementById('spread-preview-content').innerHTML = previewHtml;
                    document.getElementById('spread-preview').style.display = 'block';
                };

                // Build the preview table
                function buildPreviewTable({ lines, netType, netValue }) {
                    let tbl = `<table style="width:100%; border-collapse: collapse; margin-top: 8px; font-size: 0.85em;">`;
                    lines.forEach(line => {
                        tbl += `<tr>
                          <td style="border:1px solid #ccc; padding:4px;">${line[0]}</td>
                          <td style="border:1px solid #ccc; padding:4px;">${line[1]}</td>
                          <td style="border:1px solid #ccc; padding:4px;">${line[2]}</td>
                        </tr>`;
                    });
                    tbl += `<tr>
                        <td colspan="3" style="border:1px solid #ccc; padding:4px; text-align:center;">
                          <strong>${netType}: ${netValue}</strong>
                        </td>
                    </tr>`;
                    tbl += `</table>`;
                    return tbl;
                }
            });
        </script>
    </div>
</body>

</html>