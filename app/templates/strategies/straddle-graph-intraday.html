<!DOCTYPE html>
<html lang="en">

<head>
    <title>Intraday Straddle Graph - AI-Powered Options Analysis | AI Bull</title>
    <meta name="description"
        content="Analyze intraday straddle opportunities with AI-powered options analysis at AIBull. Find optimal strategies for straddle trades." />
    <meta property="og:title" content="Intraday Straddle Graph - AI-Powered Options Analysis" />
    <meta property="og:description"
        content="Discover intraday straddle opportunities using our AI-powered analysis. Optimize your options strategy for maximum profit." />
    <meta name="twitter:title" content="Intraday Straddle Graph - AI-Powered Options Analysis" />
    <meta name="twitter:description"
        content="Discover intraday straddle opportunities with AI-powered analysis to optimize your options strategy." />
    <meta property="og:image" content="https://theaibull.com/static/images/ai-bull-1.png" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />

    {% include 'blocks/head.html' %}
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {% include 'blocks/common.html' %}
    {% include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <main>
            <div class="p-3 mx-auto max-w-7xl w-full">
                <div
                    class="flex flex-col md:flex-row justify-between bg-white dark:bg-neutral-800 px-3 py-2 rounded-lg mb-3">
                    <div class="flex items-center">
                        <h1 class="text-xl font-semibold tracking-tight">
                            Intraday Straddle Analysis
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">
                            Discover profitable opportunities with AI-driven insights
                        </p>
                    </div>
                </div>

                <!-- Search Tab Content -->
                <div id="search-content" class="tab-content">
                    <div class="flex flex-col gap-3">
                        <!-- Left Column: Search and Most Active -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                            {% with show_submit_button=true %}
                            {% include 'blocks/search-active-recent-stocks.html' %}
                            {% endwith %}
                        </div>
                    </div>
                </div>

                <!-- Selection Section -->
                <div class="flex flex-col gap-3">
                    <!-- Expiration Date Selection -->
                    <div class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                        <label for="select-expiration"
                            class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select Expiration
                            Date</label>
                        <div class="flex items-center">
                            <select id="select-expiration"
                                class="appearance-none bg-transparent border border-gray-200 dark:bg-neutral-700 dark:border-neutral-600 focus:border-blue-500 focus:outline-0 px-3 py-2.5 rounded-md text-sm transition-all w-full">
                                <option value="">Select Expiration Date</option>
                            </select>
                            <div id="loading-expiration" class="ml-2 hidden">
                                <div class="w-6 h-6 border-4 border-blue-500 border-dashed rounded-full animate-spin">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Date Selection -->
                    <div class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                        <label for="select-date"
                            class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select Date</label>
                        <input type="date" id="select-date" value="2025-07-27"
                            class="appearance-none bg-transparent border border-gray-200 dark:bg-neutral-700 dark:border-neutral-600 focus:border-blue-500 focus:outline-0 px-3 py-2.5 rounded-md text-sm transition-all w-full">
                    </div>

                    <!-- Time Selection -->
                    <div class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                        <label for="select-time"
                            class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select Time</label>
                        <input type="time" id="select-time" value="10:00"
                            class="appearance-none bg-transparent border border-gray-200 dark:bg-neutral-700 dark:border-neutral-600 focus:border-blue-500 focus:outline-0 px-3 py-2.5 rounded-md text-sm transition-all w-full">
                    </div>

                    <!-- Separate Strike Selection for PE and CE -->
                    <div id="strike-container" class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                        <label for="select-strike-pe"
                            class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select
                            Strike for PE</label>
                        <div class="flex items-center">
                            <select id="select-strike-pe"
                                class="appearance-none bg-transparent border border-gray-200 dark:bg-neutral-700 dark:border-neutral-600 focus:border-blue-500 focus:outline-0 px-3 py-2.5 rounded-md text-sm transition-all w-full">
                                <option value="">Select Strike for PE</option>
                            </select>
                            <div id="loading-strike-pe" class="ml-2 hidden">
                                <div class="w-6 h-6 border-4 border-blue-500 border-dashed rounded-full animate-spin">
                                </div>
                            </div>
                            <button id="fetch-strike-btn"
                                class="ml-2 bg-blue-500 text-white px-3 py-2 rounded-md text-sm">Get</button>
                        </div>

                        <label for="select-strike-ce"
                            class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 mt-4">Select
                            Strike for CE</label>
                        <div class="flex items-center">
                            <select id="select-strike-ce"
                                class="appearance-none bg-transparent border border-gray-200 dark:bg-neutral-700 dark:border-neutral-600 focus:border-blue-500 focus:outline-0 px-3 py-2.5 rounded-md text-sm transition-all w-full">
                                <option value="">Select Strike for CE</option>
                            </select>
                            <div id="loading-strike-ce" class="ml-2 hidden">
                                <div class="w-6 h-6 border-4 border-blue-500 border-dashed rounded-full animate-spin">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Generate Graph Button -->
                    <div class="flex flex-col w-full">
                        <button id="generate-graph-btn"
                            class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200 opacity-50 cursor-not-allowed"
                            disabled>
                            Generate Graph
                        </button>
                    </div>
                </div>

                <!-- Chart Container -->
                <div id="straddle-chart-container" class="h-[500px] relative hidden">
                    <canvas id="straddleChart" class="mt-4"></canvas>
                </div>
                <!-- Loading indicator for graph data -->
                <div id="loading-indicator"></div>

                <!-- Optional: Add controls for zoom/pan/reset -->
                <div id="zoom-options" class="flex justify-center gap-2 mt-2 hidden">
                    <button id="reset-zoom-btn"
                        class="text-sm bg-gray-200 dark:bg-neutral-700 hover:bg-gray-300 dark:hover:bg-neutral-600 px-3 py-1 rounded-md transition-colors">
                        Reset View
                    </button>
                    <button id="zoom-in-btn"
                        class="text-sm bg-gray-200 dark:bg-neutral-700 hover:bg-gray-300 dark:hover:bg-neutral-600 px-3 py-1 rounded-md transition-colors">
                        Zoom In
                    </button>
                    <button id="zoom-out-btn"
                        class="text-sm bg-gray-200 dark:bg-neutral-700 hover:bg-gray-300 dark:hover:bg-neutral-600 px-3 py-1 rounded-md transition-colors">
                        Zoom Out
                    </button>
                </div>

                <!-- Improved Data Summary Table -->
                <div class="p-4 mt-4 bg-white dark:bg-neutral-800 rounded-lg">
                    <h2 class="text-lg font-semibold mb-2">Data Summary</h2>
                    <table class="w-full table-auto border-collapse border border-gray-300 dark:border-neutral-600">
                        <thead>
                            <tr>
                                <th class="border border-gray-300 dark:border-neutral-600 px-2 py-1">Label</th>
                                <th class="border border-gray-300 dark:border-neutral-600 px-2 py-1">PE</th>
                                <th class="border border-gray-300 dark:border-neutral-600 px-2 py-1">CE</th>
                                <th class="border border-gray-300 dark:border-neutral-600 px-2 py-1">Sum</th>
                                <th class="border border-gray-300 dark:border-neutral-600 px-2 py-1"
                                    id="symbol-col-header">Symbol
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1">Start</td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="start-pe"></td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="start-ce"></td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="start-sum">
                                </td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="start-symbol">
                                </td>
                            </tr>
                            <tr>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1">Strike (Selected
                                    Time)
                                </td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="strike-pe">
                                </td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="strike-ce">
                                </td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="strike-sum">
                                </td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="strike-symbol">
                                </td>
                            </tr>
                            <tr>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1">End</td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="end-pe"></td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="end-ce"></td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="end-sum"></td>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1" id="end-symbol">
                                </td>
                            </tr>
                            <tr>
                                <td class="border border-gray-300 dark:border-neutral-600 px-2 py-1 font-bold">P/L</td>
                                <td colspan="4"
                                    class="border border-gray-300 dark:border-neutral-600 px-2 py-1 font-bold"
                                    id="pl-cell"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <script>
                    const API_BASE_URL = 'https://iam.theaibull.com/v1/wg7ttpouv7';

                    const formatExpirationDate = (expiration) => {
                        const [day, month, year] = expiration.split('-');
                        const monthAbbreviation = month.slice(0, 3); // Extract the first three letters of the month
                        return `${monthAbbreviation}${year}`; // Format as Jul2025
                    };

                    const formatExpirationDateForAPI = (expiration) => {
                        const [day, month, year] = expiration.split('-');
                        const monthNumber = new Date(`${month} 1, ${year}`).getMonth() + 1; // Convert month name to number
                        return `${year}-${String(monthNumber).padStart(2, '0')}-${day}`; // Format as 2025-07-31
                    };

                    // Fixed bug to remove old graph containers before creating a new graph
                    // Updated logic to handle separate strikes for PE and CE
                    const generateIntradayStraddleGraph = async () => {
                        const symbol = 'SY'; // Static symbol
                        const expirationRaw = document.getElementById('select-expiration').value;
                        const expiration = formatExpirationDate(expirationRaw); // Format expiration date
                        const expirationAPIFormat = formatExpirationDateForAPI(expirationRaw);
                        const date = document.getElementById('select-date').value;
                        const time = document.getElementById('select-time').value;
                        const strikePE = document.getElementById('select-strike-pe').value;
                        const strikeCE = document.getElementById('select-strike-ce').value;
                        const generateBtn = document.getElementById('generate-graph-btn');
                        const straddleChartContainer = document.getElementById('straddle-chart-container');

                        if (straddleChartContainer) straddleChartContainer.classList.add("hidden");
                        if (!expiration || !date || !time || !strikePE || !strikeCE) {
                            alert('Please complete all selections.');
                            return;
                        }

                        const loadingIndicator = document.getElementById('loading-indicator');
                        if (loadingIndicator) {
                            loadingIndicator.innerHTML = `<div class="text-center">
                                <div class="w-14 h-14 border-4 border-blue-500 border-dashed rounded-full animate-spin mx-auto"></div>
                                <p class="font-semibold mt-4">Loading CE & PE data...</p></div>`;
                        }

                        generateBtn.textContent = 'Loading...';
                        generateBtn.disabled = true;

                        try {
                            const todayDate = new Date().toISOString().split('T')[0];
                            const symbol = document.getElementById('stock-input')?.value.trim().toUpperCase();

                            const optionSymbolPE = `SY-${symbol}-${expiration}-${strikePE}-PE_${expirationAPIFormat}`;
                            const optionSymbolCE = `SY-${symbol}-${expiration}-${strikeCE}-CE_${expirationAPIFormat}`;

                            const urlSymbol = `${API_BASE_URL}/backtesting/ohlcd/${symbol}?start=${date}&end=${date}`;
                            const urlPE = `${API_BASE_URL}/backtesting/ohlcd/${optionSymbolPE}?start=${date}&end=${date}`;
                            const urlCE = `${API_BASE_URL}/backtesting/ohlcd/${optionSymbolCE}?start=${date}&end=${date}`;

                            const [ohlcDataPE, ohlcDataCE, ohlcDataSymbol] = await Promise.all([
                                fetch(urlPE).then(r => r.json()),
                                fetch(urlCE).then(r => r.json()),
                                fetch(urlSymbol).then(r => r.json())
                            ]);

                            const combinedData = {};
                            const allTimestamps = new Set([...Object.keys(ohlcDataPE), ...Object.keys(ohlcDataCE), ...Object.keys(ohlcDataSymbol)]);
                            if (allTimestamps.size === 0) {
                                alert('No data available for the selected date. Please choose a different date (market holiday or no trading data).');
                                generateBtn.textContent = 'Generate Graph';
                                generateBtn.disabled = false;
                                if (loadingIndicator) loadingIndicator.innerHTML = "";
                                return;
                            }
                            allTimestamps.forEach(ts => {
                                const peClose = (ohlcDataPE[ts] && ohlcDataPE[ts].c) || 0;
                                const ceClose = (ohlcDataCE[ts] && ohlcDataCE[ts].c) || 0;
                                const symbolClose = (ohlcDataSymbol[ts] && ohlcDataSymbol[ts].c) || 0;
                                combinedData[ts] = { pe: peClose, ce: ceClose, sum: peClose + ceClose, symbol: symbolClose };
                            });

                            // Remove old graph containers
                            const oldSymbolChartContainer = document.getElementById('symbol-chart-container');
                            if (oldSymbolChartContainer) {
                                oldSymbolChartContainer.remove();
                            }

                            if (window.renderGraph) {
                                window.renderGraph(combinedData, { pe: true, ce: true, sum: true, symbol: true });
                            }
                            updateSummaryTable(combinedData);

                            // Render symbol chart
                            const timestamps = Object.keys(combinedData).sort((a, b) => a - b).map(ts => new Date(parseInt(ts) * 1000).toLocaleTimeString());
                            const symbolData = timestamps.map((_, i) => combinedData[Object.keys(combinedData)[i]].symbol);

                            const symbolChartContainer = document.createElement('div');
                            symbolChartContainer.id = 'symbol-chart-container';
                            symbolChartContainer.className = 'h-[500px] relative';
                            const symbolCanvas = document.createElement('canvas');
                            symbolCanvas.id = 'symbolChart';
                            symbolChartContainer.appendChild(symbolCanvas);
                            straddleChartContainer.parentNode.insertBefore(symbolChartContainer, straddleChartContainer.nextSibling);

                            const ctxSymbol = document.getElementById('symbolChart').getContext('2d');
                            new Chart(ctxSymbol, {
                                type: 'line',
                                data: {
                                    labels: timestamps,
                                    datasets: [
                                        {
                                            label: 'Symbol',
                                            data: symbolData,
                                            borderColor: 'rgba(255, 205, 86, 1)',
                                            borderWidth: 1,
                                            fill: false,
                                            pointRadius: 0
                                        }
                                    ]
                                },
                                options: {
                                    responsive: true,
                                    plugins: {
                                        tooltip: {
                                            callbacks: {
                                                label: function (context) {
                                                    const index = context.dataIndex;
                                                    const symbol = symbolData[index];
                                                    return `Symbol: ${symbol}`;
                                                }
                                            }
                                        },
                                        zoom: {
                                            zoom: {
                                                wheel: {
                                                    enabled: true
                                                },
                                                pinch: {
                                                    enabled: true
                                                },
                                                mode: 'xy'
                                            }
                                        }
                                    }
                                }
                            });
                        } catch (error) {
                            alert(`Error generating graph: ${error.message}`);
                        } finally {
                            generateBtn.textContent = 'Generate Graph';
                            generateBtn.disabled = false;
                            if (loadingIndicator) {
                                loadingIndicator.innerHTML = ""; // Remove loader after completion
                                straddleChartContainer.classList.remove("hidden");
                            }
                        }
                    };

                    // Chart X-axis: Remove seconds from time labels
                    window.renderGraph = function (data, options) {
                        const timestamps = Object.keys(data).sort((a, b) => a - b).map(ts => {
                            const d = new Date(parseInt(ts) * 1000);
                            return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                        });
                        const peData = timestamps.map((_, i) => data[Object.keys(data)[i]].pe);
                        const ceData = timestamps.map((_, i) => data[Object.keys(data)[i]].ce);
                        const sumData = timestamps.map((_, i) => data[Object.keys(data)[i]].sum);
                        const symbolData = timestamps.map((_, i) => data[Object.keys(data)[i]].symbol);

                        if (window.chartInstance) {
                            window.chartInstance.destroy();
                        }

                        const ctx = document.getElementById('straddleChart').getContext('2d');
                        window.chartInstance = new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: timestamps,
                                datasets: [
                                    {
                                        label: 'PE',
                                        data: peData,
                                        borderColor: 'rgba(255, 99, 132, 1)',
                                        borderWidth: 1,
                                        fill: false,
                                        pointRadius: 0,
                                        yAxisID: 'y-left'
                                    },
                                    {
                                        label: 'CE',
                                        data: ceData,
                                        borderColor: 'rgba(54, 162, 235, 1)',
                                        borderWidth: 1,
                                        fill: false,
                                        pointRadius: 0,
                                        yAxisID: 'y-left'
                                    },
                                    {
                                        label: 'Sum',
                                        data: sumData,
                                        borderColor: 'rgba(75, 192, 192, 1)',
                                        borderWidth: 1,
                                        fill: false,
                                        pointRadius: 0,
                                        yAxisID: 'y-left'
                                    },
                                    {
                                        label: 'Symbol',
                                        data: symbolData,
                                        borderColor: 'rgba(255, 205, 86, 1)',
                                        borderWidth: 1,
                                        fill: false,
                                        pointRadius: 0,
                                        yAxisID: 'y-right',
                                        hidden: true // Symbol dataset is hidden by default
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                scales: {
                                    'y-left': {
                                        type: 'linear',
                                        position: 'left',
                                        title: {
                                            display: true,
                                            text: 'PE, CE, Sum'
                                        }
                                    },
                                    'y-right': {
                                        type: 'linear',
                                        position: 'right',
                                        title: {
                                            display: true,
                                            text: 'Symbol'
                                        },
                                        grid: {
                                            drawOnChartArea: false
                                        }
                                    }
                                },
                                plugins: {
                                    tooltip: {
                                        callbacks: {
                                            label: function (context) {
                                                const index = context.dataIndex;
                                                const pe = peData[index];
                                                const ce = ceData[index];
                                                const sum = sumData[index];
                                                const symbol = symbolData[index];
                                                if (context.dataset.label === 'Symbol') {
                                                    return `Symbol: ${symbol}`;
                                                }
                                                return `Sum: ${sum}, PE: ${pe}, CE: ${ce}`;
                                            }
                                        }
                                    },
                                    zoom: {
                                        zoom: {
                                            wheel: {
                                                enabled: true
                                            },
                                            pinch: {
                                                enabled: true
                                            },
                                            mode: 'xy'
                                        }
                                    }
                                }
                            }
                        });
                    };

                    document.addEventListener('stockSearchTriggered', async (e) => {
                        const symbol = e.detail.symbol;
                        console.log('Fetching expiration dates for symbol:', symbol);
                        const currentYear = new Date().getFullYear();
                        const expirationSelect = document.getElementById('select-expiration');
                        const loadingExpiration = document.getElementById('loading-expiration');
                        if (loadingExpiration) loadingExpiration.classList.remove('hidden');
                        const url = `${API_BASE_URL}/symbol/expirationDates/${symbol}/${currentYear}`;
                        const data = await fetch(url).then(response => response.json());
                        expirationSelect.innerHTML = data.expirationDates.map(date => `<option value="${date}">${date}</option>`).join('');
                        // Automatically select the closest date to today's date
                        const today = new Date();
                        const closestDate = data.expirationDates.reduce((prev, curr) => {
                            const prevDate = new Date(prev);
                            const currDate = new Date(curr);
                            return currDate >= today && (prevDate < today || currDate < prevDate) ? curr : prev;
                        });
                        expirationSelect.value = closestDate;
                        if (loadingExpiration) loadingExpiration.classList.add('hidden');
                        // Trigger strike dropdown update after expiration is set
                        const dateInput = document.getElementById('select-date');
                        const timeInput = document.getElementById('select-time');
                        if (dateInput && timeInput) {
                            const date = dateInput.value;
                            const time = timeInput.value;
                            if (symbol && closestDate && date && time) {
                                await updateStrikeDropdowns(symbol, date, time, closestDate);
                            }
                        }
                    });

                    document.addEventListener('DOMContentLoaded', () => {
                        const today = new Date().toISOString().split('T')[0];
                        document.getElementById('select-date').value = today;
                    });

                    // Helper: Find closest timestamp to selected time
                    function findClosestTimestamp(ohlcData, selectedTime) {
                        // selectedTime: 'HH:MM' string
                        const [selectedHour, selectedMinute] = selectedTime.split(':').map(Number);
                        const selectedSeconds = selectedHour * 3600 + selectedMinute * 60;
                        const timestamps = Object.keys(ohlcData);
                        return timestamps.reduce((closest, ts) => {
                            const d = new Date(parseInt(ts) * 1000);
                            const tsSeconds = d.getHours() * 3600 + d.getMinutes() * 60;
                            return Math.abs(tsSeconds - selectedSeconds) < Math.abs(new Date(parseInt(closest) * 1000).getHours() * 3600 + new Date(parseInt(closest) * 1000).getMinutes() * 60 - selectedSeconds) ? ts : closest;
                        }, timestamps[0]);
                    }
                    // Helper: Find ATM strike
                    function findATMStrike(strikes, underlyingValue) {
                        if (!underlyingValue || strikes.length === 0) return null;
                        return strikes.reduce((prev, curr) => Math.abs(curr - underlyingValue) < Math.abs(prev - underlyingValue) ? curr : prev, strikes[0]);
                    }
                    // Helper: Find ATM strike at a specific time using OHLC data
                    async function findATMStrikeAtTime(symbol, date, time, strikes) {
                        const ohlcUrl = `${API_BASE_URL}/backtesting/ohlcd/${symbol}?start=${date}&end=${date}`;
                        const ohlcData = await fetch(ohlcUrl).then(r => r.json());
                        const closestTs = findClosestTimestamp(ohlcData, time);
                        const underlyingValue = ohlcData[closestTs]?.c || 0;
                        if (!underlyingValue || strikes.length === 0) return null;
                        return strikes.reduce((prev, curr) => Math.abs(curr - underlyingValue) < Math.abs(prev - underlyingValue) ? curr : prev, strikes[0]);
                    }
                    // Show spot value above Select Strike
                    async function showSpotValue(symbol, date, time) {
                        const ohlcUrl = `${API_BASE_URL}/backtesting/ohlcd/${symbol}?start=${date}&end=${date}`;
                        const ohlcData = await fetch(ohlcUrl).then(r => r.json());
                        const closestTs = findClosestTimestamp(ohlcData, time);
                        const spotValue = ohlcData[closestTs]?.c || '';
                        let spotDiv = document.getElementById('spot-value-div');
                        if (!spotDiv) {
                            spotDiv = document.createElement('div');
                            spotDiv.id = 'spot-value-div';
                            spotDiv.className = 'mb-2 text-sm font-semibold text-blue-700 dark:text-blue-300';
                            const strikeContainer = document.getElementById('strike-container');
                            if (strikeContainer) strikeContainer.parentNode.insertBefore(spotDiv, strikeContainer);
                        }
                        spotDiv.textContent = `Spot Value at ${time}: ${spotValue}`;
                    }
                    // Update strike dropdowns for PE and CE
                    const updateStrikeDropdowns = async (symbol, date, time, expiration) => {
                        await showSpotValue(symbol, date, time);
                        const loadingStrikePE = document.getElementById('loading-strike-pe');
                        const loadingStrikeCE = document.getElementById('loading-strike-ce');
                        loadingStrikePE.classList.remove('hidden');
                        loadingStrikeCE.classList.remove('hidden');
                        try {
                            let url;
                            if (symbol === 'NIFTY') {
                                url = `${API_BASE_URL}/indices/options/${symbol}?date=${date}&expiration=${expiration}`;
                            } else {
                                url = `${API_BASE_URL}/symbol/options/${symbol}?date=${date}&time=${time}&expiration=${expiration}`;
                            }
                            const data = await fetch(url).then(response => response.json());
                            // Get strikes
                            const peStrikes = [...new Set(data.records.data.filter(r => r.PE).map(r => r.strikePrice).filter(Boolean))].sort((a, b) => a - b);
                            const ceStrikes = [...new Set(data.records.data.filter(r => r.CE).map(r => r.strikePrice).filter(Boolean))].sort((a, b) => a - b);
                            const strikeSelectPE = document.getElementById('select-strike-pe');
                            const strikeSelectCE = document.getElementById('select-strike-ce');
                            // Find ATM strikes at selected time using OHLC
                            const atmStrikePE = await findATMStrikeAtTime(symbol, date, time, peStrikes);
                            const atmStrikeCE = await findATMStrikeAtTime(symbol, date, time, ceStrikes);
                            // Clear existing options
                            strikeSelectPE.innerHTML = '<option value="">Select Strike for PE</option>';
                            strikeSelectCE.innerHTML = '<option value="">Select Strike for CE</option>';
                            // Append PE options with ATM selected
                            peStrikes.forEach(s => {
                                const option = document.createElement('option');
                                option.value = s;
                                option.textContent = `${s} PE`;
                                if (s === atmStrikePE) option.selected = true;
                                strikeSelectPE.appendChild(option);
                            });
                            // Append CE options with ATM selected
                            ceStrikes.forEach(s => {
                                const option = document.createElement('option');
                                option.value = s;
                                option.textContent = `${s} CE`;
                                if (s === atmStrikeCE) option.selected = true;
                                strikeSelectCE.appendChild(option);
                            });
                            strikeSelectPE.dispatchEvent(new Event('change'));
                            strikeSelectCE.dispatchEvent(new Event('change'));
                        } catch (error) {
                            console.error('Error fetching strikes:', error);
                        } finally {
                            loadingStrikePE.classList.add('hidden');
                            loadingStrikeCE.classList.add('hidden');
                        }
                    };

                    // Ensure elements exist before adding event listeners
                    const strikeSelectEventListeners = () => {
                        const strikeSelectPE = document.getElementById('select-strike-pe');
                        const strikeSelectCE = document.getElementById('select-strike-ce');
                        const selectDate = document.getElementById('select-date');
                        const selectExpiration = document.getElementById('select-expiration');
                        const fetchStrikeBtn = document.getElementById('fetch-strike-btn');

                        if (strikeSelectPE && strikeSelectCE && selectDate && selectExpiration && fetchStrikeBtn) {
                            selectDate.addEventListener('change', async () => {
                                const date = selectDate.value;
                                const time = document.getElementById('select-time').value;
                                const symbol = document.getElementById('stock-input')?.value.trim().toUpperCase();
                                const expiration = selectExpiration.value;
                                if (date && time && symbol && expiration) {
                                    await updateStrikeDropdowns(symbol, date, time, expiration);
                                }
                            });

                            selectExpiration.addEventListener('change', async () => {
                                const expiration = selectExpiration.value;
                                const date = selectDate.value;
                                const time = document.getElementById('select-time').value;
                                const symbol = document.getElementById('stock-input')?.value.trim().toUpperCase();
                                if (expiration && date && time && symbol) {
                                    await updateStrikeDropdowns(symbol, date, time, expiration);
                                }
                            });

                            fetchStrikeBtn.addEventListener('click', async () => {
                                const expiration = selectExpiration.value;
                                const date = selectDate.value;
                                const time = document.getElementById('select-time').value;
                                const symbol = document.getElementById('stock-input')?.value.trim().toUpperCase();
                                if (expiration && date && time && symbol) {
                                    await updateStrikeDropdowns(symbol, date, time, expiration);
                                }
                            });
                        } else {
                            console.error('One or more required elements are missing for event listeners.');
                        }
                    };

                    strikeSelectEventListeners();

                    // Highlight the Generate Graph button when both PE and CE strikes are selected
                    const strikeSelectPE = document.getElementById('select-strike-pe');
                    const strikeSelectCE = document.getElementById('select-strike-ce');
                    const generateBtn = document.getElementById('generate-graph-btn');

                    const updateGenerateButtonState = () => {
                        if (strikeSelectPE.value && strikeSelectCE.value) {
                            generateBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                            generateBtn.disabled = false;
                        } else {
                            generateBtn.classList.add('opacity-50', 'cursor-not-allowed');
                            generateBtn.disabled = true;
                        }
                    };

                    strikeSelectPE.addEventListener('change', updateGenerateButtonState);
                    strikeSelectCE.addEventListener('change', updateGenerateButtonState);

                    // Trigger strike dropdown update on DOMContentLoaded if all inputs are set
                    window.addEventListener('DOMContentLoaded', async () => {
                        const today = new Date().toISOString().split('T')[0];
                        const dateInput = document.getElementById('select-date');
                        const expirationSelect = document.getElementById('select-expiration');
                        const symbolInput = document.getElementById('stock-input');
                        const timeInput = document.getElementById('select-time');

                        if (dateInput) dateInput.value = today;

                        if (symbolInput && expirationSelect && dateInput && timeInput) {
                            const symbol = symbolInput.value.trim().toUpperCase();
                            const expiration = expirationSelect.value;
                            const date = dateInput.value;
                            const time = timeInput.value;
                            if (symbol && expiration && date && time) {
                                await updateStrikeDropdowns(symbol, date, time, expiration);
                            }
                        }
                    });

                    // Hide P/L table by default, show after generate
                    const summaryTableDiv = document.querySelector('.p-4.mt-4.bg-white');
                    if (summaryTableDiv) summaryTableDiv.style.display = 'none';
                    function updateSummaryTable(combinedData) {
                        const timestamps = Object.keys(combinedData).sort((a, b) => a - b);
                        if (timestamps.length === 0) return;
                        const startTs = timestamps[0];
                        const endTs = timestamps[timestamps.length - 1];
                        // Find the timestamp closest to the selected time
                        const timeInput = document.getElementById('select-time').value;
                        let selectedTs = startTs;
                        if (timeInput) {
                            // Convert selected time to seconds since midnight
                            const [selectedHour, selectedMinute] = timeInput.split(':').map(Number);
                            const selectedSeconds = selectedHour * 3600 + selectedMinute * 60;
                            // Find the timestamp closest to selectedSeconds
                            selectedTs = timestamps.reduce((closest, ts) => {
                                const d = new Date(parseInt(ts) * 1000);
                                const tsSeconds = d.getHours() * 3600 + d.getMinutes() * 60;
                                return Math.abs(tsSeconds - selectedSeconds) < Math.abs(new Date(parseInt(closest) * 1000).getHours() * 3600 + new Date(parseInt(closest) * 1000).getMinutes() * 60 - selectedSeconds) ? ts : closest;
                            }, startTs);
                        }
                        const start = combinedData[startTs];
                        const end = combinedData[endTs];
                        const selected = combinedData[selectedTs];
                        document.getElementById('start-pe').textContent = start.pe;
                        document.getElementById('start-ce').textContent = start.ce;
                        document.getElementById('start-sum').textContent = start.sum;
                        document.getElementById('start-symbol').textContent = start.symbol;
                        document.getElementById('strike-pe').textContent = selected.pe;
                        document.getElementById('strike-ce').textContent = selected.ce;
                        document.getElementById('strike-sum').textContent = selected.sum;
                        document.getElementById('strike-symbol').textContent = selected.symbol;
                        document.getElementById('end-pe').textContent = end.pe;
                        document.getElementById('end-ce').textContent = end.ce;
                        document.getElementById('end-sum').textContent = end.sum;
                        document.getElementById('end-symbol').textContent = end.symbol;
                        const pl = selected.sum - end.sum;
                        // Calculate symbol % move and delta points from Strike to End
                        const symbolStrike = selected.symbol;
                        const symbolEnd = end.symbol;
                        let symbolMovePct = 0;
                        let symbolDelta = 0;
                        if (symbolStrike && symbolEnd) {
                            symbolDelta = symbolEnd - symbolStrike;
                            symbolMovePct = (symbolDelta / symbolStrike) * 100;
                        }
                        // Get symbol name from input
                        const symbolName = document.getElementById('stock-input')?.value.trim().toUpperCase() || 'Symbol';
                        const plText = pl > 0 ? `Profit: ${pl.toFixed(2)}` : `Loss: ${(-pl).toFixed(2)}`;
                        const symbolText = `${symbolName}: ${symbolDelta.toFixed(2)} pts (${symbolMovePct.toFixed(2)}%)`;
                        document.getElementById('pl-cell').textContent = `${plText} | ${symbolText}`;
                        const summaryTableDiv = document.querySelector('.p-4.mt-4.bg-white');
                        if (summaryTableDiv) summaryTableDiv.style.display = 'block';
                        // Update column header
                        const symbolColHeader = document.getElementById('symbol-col-header');
                        if (symbolColHeader) symbolColHeader.textContent = symbolName;
                    }

                    // Left-align table labels
                    const style = document.createElement('style');
                    style.innerHTML = `
    .data-summary-table th, .data-summary-table td { text-align: left !important; }
`;
                    document.head.appendChild(style);
                    // Add class to table
                    const summaryTable = document.querySelector('.p-4.mt-4.bg-white table');
                    if (summaryTable) summaryTable.classList.add('data-summary-table');

                    document.getElementById('generate-graph-btn').addEventListener('click', generateIntradayStraddleGraph);

                    document.addEventListener('stockSearchTriggered', async (e) => {
                        const symbol = e.detail.symbol;
                        console.log('Fetching expiration dates for symbol:', symbol);
                        const currentYear = new Date().getFullYear();
                        const expirationSelect = document.getElementById('select-expiration');
                        const loadingExpiration = document.getElementById('loading-expiration');
                        if (loadingExpiration) loadingExpiration.classList.remove('hidden');
                        const url = `${API_BASE_URL}/symbol/expirationDates/${symbol}/${currentYear}`;
                        const data = await fetch(url).then(response => response.json());
                        expirationSelect.innerHTML = data.expirationDates.map(date => `<option value="${date}">${date}</option>`).join('');

                        // Automatically select the closest date to today's date
                        const today = new Date();
                        const closestDate = data.expirationDates.reduce((prev, curr) => {
                            const prevDate = new Date(prev);
                            const currDate = new Date(curr);
                            return currDate >= today && (prevDate < today || currDate < prevDate) ? curr : prev;
                        });

                        expirationSelect.value = closestDate;
                        if (loadingExpiration) loadingExpiration.classList.add('hidden');
                        // Trigger strike dropdown update after expiration is set
                        const dateInput = document.getElementById('select-date');
                        const timeInput = document.getElementById('select-time');
                        if (dateInput && timeInput) {
                            const date = dateInput.value;
                            const time = timeInput.value;
                            if (symbol && closestDate && date && time) {
                                await updateStrikeDropdowns(symbol, date, time, closestDate);
                            }
                        }
                    });

                    document.addEventListener('DOMContentLoaded', () => {
                        const today = new Date().toISOString().split('T')[0];
                        document.getElementById('select-date').value = today;
                    });
                </script>
            </div>
        </main>
    </div>

    {% include 'blocks/stock-symbols.html' %}
    {% include 'blocks/options.html' %}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
</body>

</html>