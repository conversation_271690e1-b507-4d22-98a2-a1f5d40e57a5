<main class="h-full">
    <!-- Add these script tags at the top -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.css" />

    <div class="">
        <div class="">
            <div class="w-full">
                <!-- Stock Category Selector -->
                <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-3">
                    <div>
                        <div class="flex gap-4">
                            <label class="flex items-center">
                                <input type="radio" name="stock-category" value="nifty" checked class="mr-2" />
                                Nifty Stocks
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="stock-category" value="fo" class="mr-2" />
                                F&O Stocks
                            </label>
                        </div>
                    </div>
                </div>
                <!-- Ticker Selection Section -->
                <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 flex flex-col h-[350px] lg:max-h-[calc(60vh)]">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="font-semibold">Select Stocks</h3>
                        <div class="flex gap-2 items-center">
                            <button id="select-all" class="text-xs hover:text-blue-500 text-neutral-700 font-medium">
                                Select All
                            </button>
                            <span class="text-gray-400 text-xs">|</span>
                            <button id="unselect-all" class="text-xs hover:text-red-500 text-neutral-700 font-medium">
                                Unselect All
                            </button>
                        </div>
                    </div>
                    <div id="ticker-selection" class="mb-4 flex-1 overflow-y-auto custom-scroll"></div>
                    <div class="pt-4 border-t mt-auto">
                        <div class="flex gap-2 justify-between">
                            <button id="scan-button"
                                class="bg-blue-500 block focus:outline-0 font-medium hover:bg-blue-600 px-6 py-3 rounded-md text-white transition-colors w-full">
                                Scan Selected
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <!-- Filter Section (initially hidden) -->
                <div id="results-filter-section" class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4 hidden">
                    <div class="md:flex justify-between mb-4">
                        <h3 class="font-semibold">Filter Scanned Results</h3>
                    </div>

                    <div class="grid md:grid-cols-3 md:gap-12 gap-4 mb-4">
                        <!-- Stock Search Filter -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Search Stocks
                            </label>
                            <input id="stock-filter" onkeyup="filterResults()" type="text"
                                placeholder="Filter stocks..."
                                class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all" />
                        </div>

                        <!-- Premium Range Slider -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Minimum Premium (<span id="premium-label-value">1.25</span>%)
                            </label>
                            <div id="premium-range-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span id="premium-min-value">0%</span>
                                <span id="premium-max-value">10%</span>
                            </div>
                        </div>

                        <!-- Profit Range Slider -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Minimum Profit (<span id="profit-label-value">4</span>%)
                            </label>
                            <div id="profit-range-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span id="profit-min-value">0%</span>
                                <span id="profit-max-value">10%</span>
                            </div>
                        </div>

                        <!-- Volume Range Slider -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Minimum Volume (<span id="volume-label-value">100</span>)
                            </label>
                            <div id="volume-range-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span id="volume-min-value">0</span>
                                <span id="volume-max-value">1000</span>
                            </div>
                        </div>

                        <!-- Probability Range Slider -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Minimum Prob. of Sale (<span id="prob-label-value">20</span>%)
                            </label>
                            <div id="prob-range-slider" class="mt-6"></div>
                            <div class="flex justify-between text-sm mt-1">
                                <span id="prob-min-value">0%</span>
                                <span id="prob-max-value">100%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Expiry Tabs -->
                    <div id="covered-call-scanner-expiry-tabs"
                        class="border flex gap-0 mb-4 overflow-x-auto p-1 rounded-md mt-4 md:mt-0 overflow-auto text-nowrap">
                    </div>

                    <!-- Results Counter and Summary Button -->
                    <div class="flex justify-between items-center mt-4 pt-4 border-t">
                        <div id="results-counter" class="text-sm text-gray-600 hidden md:inline-flex"></div>
                        <button id="view-summary-btn" class="text-blue-500 text-blue-800 underline text-sm">
                            View Top Stocks Summary
                        </button>
                    </div>
                </div>

                <!-- Scanner Results -->
                <div id="scanner-results"></div>
            </div>
        </div>
    </div>

    <!-- Summary Modal -->
    <div id="summaryModal"
        class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center hidden z-50 p-4">
        <div
            class="modal-content bg-white dark:bg-neutral-800 relative w-full max-w-7xl max-h-[90vh] rounded-lg shadow-lg flex flex-col">
            <!-- Modal Header -->
            <div class="p-4 border-b dark:border-neutral-700">
                <h2 class="text-lg font-semibold">Top Stocks Summary</h2>
                <button id="closeSummaryModal"
                    class="absolute top-5 right-5 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 text-2xl font-bold">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-x-icon lucide-x">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Body with Scrollable Content -->
            <div class="flex-1 overflow-auto p-4">
                <div id="summaryTableContainer"></div>
            </div>
        </div>
    </div>
</main>

{%include 'blocks/stock-symbols.html' %}

<script>


    // Global variables
    let currentTickers = niftyTickers;
    const scannedData = {};
    let selectedExpiry = null;
    let scannedTickers = [];
    let processedCount = 0;
    let scanComplete = false;

    // DOM elements
    const tickerSelectionContainer = document.getElementById("ticker-selection");
    const selectAllBtn = document.getElementById("select-all");
    const unselectAllBtn = document.getElementById("unselect-all");
    const scanButton = document.getElementById("scan-button");
    const scannerResults = document.getElementById("scanner-results");
    const expiryTabsContainer = document.getElementById(
        "covered-call-scanner-expiry-tabs"
    );
    const stockCategoryRadios = document.getElementsByName("stock-category");
    const stockFilter = document.getElementById("stock-filter");

    // Initialize sliders immediately
    const premiumSlider = document.getElementById("premium-range-slider");
    const premiumMinValue = document.getElementById("premium-min-value");
    const profitSlider = document.getElementById("profit-range-slider");
    const profitMinValue = document.getElementById("profit-min-value");

    noUiSlider.create(premiumSlider, {
        start: [1.25],
        connect: [true, false],
        range: {
            'min': 0,
            'max': 10
        },
        step: 0.05,
        format: {
            to: value => value.toFixed(2),
            from: value => parseFloat(value)
        }
    });

    noUiSlider.create(profitSlider, {
        start: [4],
        connect: [true, false],
        range: {
            'min': 0,
            'max': 10
        },
        step: 0.05,
        format: {
            to: value => value.toFixed(2),
            from: value => parseFloat(value)
        }
    });

    premiumSlider.noUiSlider.on("update", function (values) {
        document.getElementById("premium-label-value").textContent = values[0];
        if (scanComplete) filterResults();
    });

    profitSlider.noUiSlider.on("update", function (values) {
        document.getElementById("profit-label-value").textContent = values[0];
        if (scanComplete) filterResults();
    });

    // Initialize volume slider
    const volumeSlider = document.getElementById("volume-range-slider");
    noUiSlider.create(volumeSlider, {
        start: [100],
        connect: [true, false],
        range: {
            'min': 0,
            'max': 1000
        },
        step: 10,
        format: {
            to: value => Math.round(value),
            from: value => parseInt(value)
        }
    });

    // Initialize probability slider
    const probSlider = document.getElementById("prob-range-slider");
    noUiSlider.create(probSlider, {
        start: [20],
        connect: [true, false],
        range: {
            'min': 0,
            'max': 100
        },
        step: 1,
        format: {
            to: value => Math.round(value),
            from: value => parseInt(value)
        }
    });

    // Add update handlers
    volumeSlider.noUiSlider.on("update", function (values) {
        document.getElementById("volume-label-value").textContent = values[0];
        if (scanComplete) filterResults();
    });

    probSlider.noUiSlider.on("update", function (values) {
        document.getElementById("prob-label-value").textContent = values[0];
        if (scanComplete) filterResults();
    });

    // Build ticker selection checkboxes
    function buildTickerSelection(tickerArray) {
        let html =
            '<div class="border border-l-0 divide-x divide-y grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 md:grid-cols-4">';
        tickerArray.forEach((ticker) => {
            html += `
                    <label class="flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-neutral-700 first:border-l text-sm">
                        <input type="checkbox" value="${ticker}" class="ticker-checkbox" checked>
                        <span>${ticker}</span>
                    </label>
                `;
        });
        html += "</div>";
        tickerSelectionContainer.innerHTML = html;
    }

    // Initial build
    buildTickerSelection(currentTickers);

    // Stock category radio change handler
    stockCategoryRadios.forEach((radio) => {
        radio.addEventListener("change", function () {
            currentTickers = this.value === "nifty" ? niftyTickers : foTickers;
            buildTickerSelection(currentTickers);
        });
    });

    // Select/Unselect all handlers
    selectAllBtn.addEventListener("click", () => {
        document
            .querySelectorAll(".ticker-checkbox")
            .forEach((cb) => (cb.checked = true));
    });
    unselectAllBtn.addEventListener("click", () => {
        document
            .querySelectorAll(".ticker-checkbox")
            .forEach((cb) => (cb.checked = false));
    });

    // Main scan button handler
    scanButton.addEventListener("click", async () => {
        scanComplete = false; // Reset at start of scan
        // Reset previous data
        expiryTabsContainer.innerHTML = "";
        scannerResults.innerHTML = "";
        Object.keys(scannedData).forEach((key) => delete scannedData[key]);
        scannedTickers = [];
        selectedExpiry = null;
        processedCount = 0;

        // Hide filter section at start of new scan
        document.getElementById("results-filter-section").classList.add("hidden");

        // Get selected tickers
        const selected = Array.from(
            document.querySelectorAll(".ticker-checkbox:checked")
        ).map((cb) => cb.value);
        if (selected.length === 0) {
            showAlert("Please select at least one ticker.");
            return;
        }

        scannedTickers = selected;

        // Process all tickers
        try {
            await Promise.all(scannedTickers.map(processTicker));

            const firstTicker = scannedTickers[0];
            if (scannedData[firstTicker]?.expiryDates?.length > 0) {
                buildExpiryTabs(scannedData[firstTicker].expiryDates);
                selectedExpiry = scannedData[firstTicker].expiryDates[0];

                // Show filter section only if we have valid data
                if (Object.keys(scannedData).some(ticker =>
                    scannedData[ticker].callsByExpiry[selectedExpiry]?.length > 0)) {
                    document.getElementById("results-filter-section").classList.remove("hidden");
                }

                scanComplete = true; // Set to true after scan completes
                filterResults(); // Initial filter after scan
            } else {
                expiryTabsContainer.innerHTML = `
                    <p class="text-red-500">No expiry data available for ${firstTicker}</p>
                `;
            }
        } catch (err) {
            console.error("Error processing tickers:", err);
            // Hide filter section on error
            document.getElementById("results-filter-section").classList.add("hidden");
            scannerResults.innerHTML = `
                <p class="text-red-500">Error processing tickers. Please try again.</p>
            `;
        }
    });

    // Process individual ticker
    async function processTicker(ticker) {
        let tickerContainer = document.getElementById("ticker-" + ticker);
        if (!tickerContainer) {
            tickerContainer = document.createElement("div");
            tickerContainer.id = "ticker-" + ticker;
            tickerContainer.className =
                "mb-3 p-3 rounded-md bg-white dark:bg-neutral-800 dark:border-neutral-700 rounded-lg";
            tickerContainer.innerHTML = `
                    <h2 class="text-base font-medium">${ticker} - Scanning...</h2>
                    <div id="ticker-content-${ticker}"></div>
                `;
            scannerResults.appendChild(tickerContainer);
        }

        const contentContainer = document.getElementById(
            "ticker-content-" + ticker
        );

        try {
            // Fetch options chain data directly

            // const optionsResponse = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${ticker}`);

            const optionsResponse = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${ticker}`);
            const optionsData = await optionsResponse.json();
            const records = optionsData.records;
            const { expiryDates, data } = records;

            // Extract purchasePrice from the underlyingValue of the first available option (CE)
            let purchasePrice = 0;
            if (data && data.length > 0) {
                for (const opt of data) {
                    if (opt.CE && opt.CE.underlyingValue) {
                        purchasePrice = opt.CE.underlyingValue;
                        break;
                    }
                }
            }

            // Process options data to group calls by expiry date
            const callsByExpiry = {};
            if (expiryDates?.length > 0) {
                expiryDates.forEach((ed) => {
                    callsByExpiry[ed] = [];
                });
                data.forEach((opt) => {
                    if (opt.CE) {
                        if (!callsByExpiry[opt.expiryDate])
                            callsByExpiry[opt.expiryDate] = [];
                        callsByExpiry[opt.expiryDate].push(opt);
                    }
                });
            }

            // Store the processed data
            scannedData[ticker] = {
                purchasePrice,
                // You can still store the original price info if needed:
                // priceInfo: priceData.priceInfo,
                expiryDates: expiryDates || [],
                callsByExpiry,
            };

            // Update ticker header with the extracted price
            tickerContainer.querySelector("h2").innerHTML = `
                    ${ticker} - Price: ${purchasePrice.toFixed(2)}
                `;
        } catch (err) {
            console.error(`Error processing ${ticker}:`, err);
            contentContainer.innerHTML = `
                    <p class="text-red-500">Failed to fetch data for ${ticker}</p>
                `;
        }
    }

    // Build expiry tabs
    function buildExpiryTabs(expiryDates) {
        const tabsHtml = expiryDates
            .map(
                (date, idx) => `
                <button 
                    class="tab-btn px-4 py-1.5 rounded-sm text-xs font-medium 
                           ${idx === 0
                        ? "bg-blue-500 text-white"
                        : "bg-white dark:bg-neutral-800  dark:hover:bg-neutral-700"
                    }"
                    data-expiry="${date}"
                >
                    ${date}
                </button>
            `
            )
            .join("");

        expiryTabsContainer.innerHTML = tabsHtml;

        // Add tab click handlers
        document.querySelectorAll(".tab-btn").forEach((btn) => {
            btn.addEventListener("click", function () {
                document.querySelectorAll(".tab-btn").forEach((b) => {
                    b.classList.remove("bg-blue-500", "text-white");
                    b.classList.add("bg-white", "dark:bg-neutral-800");
                });
                this.classList.remove("bg-white", "dark:bg-neutral-800");
                this.classList.add("bg-blue-500", "text-white");

                selectedExpiry = this.dataset.expiry;
                filterResults();
            });
        });
    }

    // Build summary modal
    function buildSummaryModal() {
        const summaryData = [];
        const premiumMin = parseFloat(premiumSlider.noUiSlider.get());
        const profitMin = parseFloat(profitSlider.noUiSlider.get());

        scannedTickers.forEach((ticker) => {
            const tickerData = scannedData[ticker];
            if (!tickerData) return;

            const purchasePrice = tickerData.purchasePrice;
            const callOptions = tickerData.callsByExpiry[selectedExpiry] || [];

            callOptions.forEach((opt) => {
                const ce = opt.CE;
                if (!ce) return;

                const strike = ce.strikePrice;
                const premium = ce.lastPrice || 0;
                const volume = ce.totalTradedVolume || 0;
                const bid = ce.bidprice || 0;
                const ask = ce.askPrice || 0;
                const premiumPercent =
                    purchasePrice > 0 ? (premium / purchasePrice) * 100 : 0;
                const profitIfSold =
                    purchasePrice > 0 ? strike - purchasePrice + premium : 0;
                const profitPercent =
                    purchasePrice > 0 ? (profitIfSold / purchasePrice) * 100 : 0;
                const probSalePct = ce.delta != null ? ce.delta * 100 : 0;

                // Apply slider filters
                if (volume === 0 && probSalePct === 0) return;
                if (
                    premiumPercent < premiumMin ||
                    profitPercent < profitMin
                ) {
                    return;
                }

                summaryData.push({
                    ticker,
                    purchasePrice,
                    strike,
                    bid,
                    ask,
                    premium,
                    premiumPercent,
                    profitIfSold,
                    profitPercent,
                    volume,
                    probSalePct,
                });
            });
        });

        summaryData.sort((a, b) => b.profitPercent - a.profitPercent);

        if (summaryData.length === 0) {
            document.getElementById("summaryTableContainer").innerHTML = `
                <div class="bg-white dark:bg-neutral-800 border rounded-md p-8 text-center">
                    <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold mb-2">No matching stocks found</h3>
                    <p class="text-gray-500">
                        Try adjusting your filters:<br>
                        Minimum Premium: ${premiumMin}%<br>
                        Minimum Profit: ${profitMin}%
                    </p>
                </div>
            `;
            return;
        }

        let summaryHtml = `
                <div class="overflow-x-auto [&_th:first-child]:sticky [&_th:first-child]:left-0  [&_th:first-child]:bg-gray-100 [&_td:first-child]:sticky [&_td:first-child]:left-0 [&_td:first-child]:bg-white [&_td:first-child]:z-10 [&_th:first-child]:z-10 [&_td:first-child]:border-r">
                    <table class="w-full border-collapse border dark:border-neutral-700 text-xs" id="summary-table">
                        <thead>
                            <tr class="bg-gray-100 text-neutral-700 text-left">
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="ticker">Ticker</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="purchasePrice">Stock Price</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="strike">Strike Price</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="bid">Bid</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="ask">Ask</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="premium">LTP</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="premiumPercent">Prem %</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium text-nowrap" data-col="profitIfSold">Stock Profit</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium text-nowrap" data-col="volume">Volume</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium text-nowrap" data-col="profitPercent">Profit %</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium text-nowrap" data-col="probSalePct">Prob. of Sale</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

        summaryData.forEach((item) => {
            summaryHtml += `
                    <tr class="hover:bg-gray-50 dark:hover:bg-neutral-700/50 odd:bg-white even:bg-gray-100">
                        <td class="border dark:border-neutral-600 p-2 text-left font-medium">${item.ticker
                }</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${item.purchasePrice.toFixed(
                    2
                )}</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${item.strike
                }</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${item.bid.toFixed(
                    2
                )}</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${item.ask.toFixed(
                    2
                )}</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${item.premium.toFixed(
                    2
                )}</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${item.premiumPercent.toFixed(
                    2
                )}%</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${item.profitIfSold.toFixed(
                    2
                )}</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${item.volume
                }</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${item.profitPercent.toFixed(
                    2
                )}%</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${item.probSalePct.toFixed(
                    1
                )}%</td>
                    </tr>
                `;
        });

        summaryHtml += `</tbody></table></div>`;
        document.getElementById("summaryTableContainer").innerHTML = summaryHtml;
        // Use shared sorting function
        setTimeout(() => window.addSorting('summary-table'), 0);
    }

    // Render individual ticker data
    function renderTickerData(ticker, expiry) {
        const tickerData = scannedData[ticker];
        if (!tickerData) return;

        // Get current slider values
        const premiumMin = parseFloat(premiumSlider.noUiSlider.get());
        const profitMin = parseFloat(profitSlider.noUiSlider.get());
        const volumeMin = parseInt(volumeSlider.noUiSlider.get());
        const probMin = parseInt(probSlider.noUiSlider.get());

        // Ensure ticker container exists
        let tickerContainer = document.getElementById("ticker-" + ticker);
        if (!tickerContainer) {
            tickerContainer = document.createElement("div");
            tickerContainer.id = "ticker-" + ticker;
            tickerContainer.className =
                "mb-3 p-4  rounded-lg bg-white dark:bg-neutral-800 dark:border-neutral-700";
            tickerContainer.innerHTML = `<div class="md:flex justify-between w-full">
                <h2 class="text-base font-medium mb-2">${ticker} - Price: ${tickerData.purchasePrice.toFixed(2)}</h2>
                <div class="mb-2 text-gray-600 dark:text-gray-300 text-xs px-2 py-1 bg-red-50 inline-flex">
                    <span class="text-neutral-600 ">Expiry:</span><span class="font-medium pl-2 text-neutral-800">${expiry}</span>
                </div>
            </div>
                <div id="ticker-content-${ticker}"></div>
            `;
            scannerResults.appendChild(tickerContainer);
        }

        const purchasePrice = tickerData.purchasePrice;
        const callOptions = tickerData.callsByExpiry[expiry] || [];
        const contentContainer = document.getElementById("ticker-content-" + ticker);

        // Process options data and apply filters
        let rowData = callOptions
            .map((opt) => {
                const ce = opt.CE;
                if (!ce) return null;

                const strike = ce.strikePrice;
                const premium = ce.lastPrice || 0;
                const volume = ce.totalTradedVolume || 0;
                const bid = ce.bidprice || 0;
                const ask = ce.askPrice || 0;
                const probSalePct = ce.delta != null ? ce.delta * 100 : 0;

                const premiumPercent = purchasePrice > 0 ? (premium / purchasePrice) * 100 : 0;
                const profitIfSold = purchasePrice > 0 ? strike - purchasePrice + premium : 0;
                const profitPercent = purchasePrice > 0 ? (profitIfSold / purchasePrice) * 100 : 0;

                // Apply all filters
                if (
                    premiumPercent < premiumMin ||
                    profitPercent < profitMin ||
                    volume < volumeMin ||
                    probSalePct < probMin
                ) {
                    return null;
                }

                return {
                    strikePrice: strike,
                    volume,
                    bid,
                    ask,
                    premium,
                    premiumPercent,
                    profitIfSold,
                    profitPercent,
                    probSalePct,
                };
            })
            .filter(Boolean); // Remove null entries

        if (rowData.length === 0) {
            tickerContainer.classList.add("hidden");
            return;
        }

        tickerContainer.classList.remove("hidden");
        rowData.sort((a, b) => b.profitPercent - a.profitPercent);

        // Build table HTML
        let tableHtml = `
   
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border dark:border-neutral-700 xl:text-sm text-xs" id="cover-call-table-${ticker}">
                        <thead class="bg-gray-100 text-neutral-700 text-left">
                            <tr>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="strikePrice">Strike</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="volume">Volume</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="bid">Bid</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="ask">Ask</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer font-medium" data-col="premium">LTP</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer text-nowrap font-medium" data-col="premiumPercent">Prem %</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer text-nowrap font-medium" data-col="profitIfSold">Profit If Sold</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer text-nowrap font-medium" data-col="profitPercent">Profit %</th>
                                <th class="border dark:border-neutral-600 p-2 cursor-pointer text-nowrap font-medium" data-col="probSalePct">Prob. of Sale</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

        rowData.forEach((row) => {
            const colorPremPct =
                row.premiumPercent < 0 ? "text-red-500" : "text-green-600";
            const colorProf =
                row.profitIfSold < 0 ? "text-red-500" : "text-green-600";
            const colorProfPct =
                row.profitPercent < 0 ? "text-red-500" : "text-green-600";
            const colorProbSale =
                row.probSalePct < 0 ? "text-red-500" : "text-green-600";

            tableHtml += `
                    <tr class="hover:bg-gray-50 dark:hover:bg-neutral-700/50">
                        <td class="border dark:border-neutral-600 p-2 text-left">${row.strikePrice
                }</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${row.volume
                }</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${row.bid.toFixed(
                    2
                )}</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${row.ask.toFixed(
                    2
                )}</td>
                        <td class="border dark:border-neutral-600 p-2 text-left">${row.premium.toFixed(
                    2
                )}</td>
                        <td class="border dark:border-neutral-600 p-2 text-left ${colorPremPct}">${row.premiumPercent.toFixed(
                    2
                )}%</td>
                        <td class="border dark:border-neutral-600 p-2 text-left ${colorProf}">${row.profitIfSold.toFixed(
                    2
                )}</td>
                        <td class="border dark:border-neutral-600 p-2 text-left ${colorProfPct}">${row.profitPercent.toFixed(
                    2
                )}%</td>
                        <td class="border dark:border-neutral-600 p-2 text-left ${colorProbSale}">${row.probSalePct.toFixed(
                    1
                )}%</td>
                    </tr>
                `;
        });

        tableHtml += `</tbody></table></div>`;
        contentContainer.innerHTML = tableHtml;
        // Use shared sorting function
        setTimeout(() => window.addSorting('cover-call-table-' + ticker), 0);
    }

    // Close summary modal handler
    document.getElementById("closeSummaryModal").addEventListener("click", () => {
        document.getElementById("summaryModal").classList.add("hidden");
    });

    function filterResults() {
        if (!scanComplete || !stockFilter) return;

        const searchTerm = stockFilter.value.toLowerCase();
        const premiumMin = parseFloat(premiumSlider.noUiSlider.get());
        const profitMin = parseFloat(profitSlider.noUiSlider.get());
        const volumeMin = parseInt(volumeSlider.noUiSlider.get());
        const probMin = parseInt(probSlider.noUiSlider.get());

        // Filter the scanned tickers based on criteria
        const filteredTickers = scannedTickers.filter((ticker) => {
            if (!ticker.toLowerCase().includes(searchTerm)) {
                return false;
            }

            const tickerData = scannedData[ticker];
            if (!tickerData || !selectedExpiry) return false;

            const callOptions = tickerData.callsByExpiry[selectedExpiry] || [];

            return callOptions.some((opt) => {
                const ce = opt.CE;
                if (!ce) return false;

                const purchasePrice = tickerData.purchasePrice;
                const premium = ce.lastPrice || 0;
                const strike = ce.strikePrice;
                const volume = ce.totalTradedVolume || 0;
                const probSalePct = ce.delta != null ? ce.delta * 100 : 0;

                const premiumPercent = purchasePrice > 0 ? (premium / purchasePrice) * 100 : 0;
                const profitIfSold = purchasePrice > 0 ? strike - purchasePrice + premium : 0;
                const profitPercent = purchasePrice > 0 ? (profitIfSold / purchasePrice) * 100 : 0;

                return (
                    premiumPercent >= premiumMin &&
                    profitPercent >= profitMin &&
                    volume >= volumeMin &&
                    probSalePct >= probMin
                );
            });
        });

        // Clear all existing results
        scannerResults.innerHTML = "";

        if (filteredTickers.length === 0) {
            // Add empty state message
            scannerResults.innerHTML = `
                <div class="bg-white dark:bg-neutral-800 border rounded-md p-8 text-center">
                    <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold mb-2">No matching stocks found</h3>
                    <p class="text-gray-500">
                        Try adjusting your filters:<br>
                        Minimum Premium: ${premiumMin}%<br>
                        Minimum Profit: ${profitMin}%
                    </p>
                </div>
            `;
        } else {
            // Show filter section if we have results
            document.getElementById("results-filter-section").classList.remove("hidden");

            // Render filtered results
            filteredTickers.forEach((ticker) => {
                renderTickerData(ticker, selectedExpiry);
            });
        }

        // Update results counter
        const counterEl = document.querySelector("#results-counter");
        if (counterEl) {
            // Count total valid stocks (those with options data)
            const totalValidStocks = scannedTickers.filter((ticker) => {
                const tickerData = scannedData[ticker];
                return (
                    tickerData &&
                    selectedExpiry &&
                    tickerData.callsByExpiry[selectedExpiry]?.length > 0
                );
            }).length;

            counterEl.textContent = `Showing ${filteredTickers.length} of ${totalValidStocks} stocks`;
        }
    }

    document.getElementById("view-summary-btn").addEventListener("click", () => {
        buildSummaryModal();
        document.getElementById("summaryModal").classList.remove("hidden");
    });

    document.getElementById("closeSummaryModal").addEventListener("click", () => {
        document.getElementById("summaryModal").classList.add("hidden");
    });
</script>