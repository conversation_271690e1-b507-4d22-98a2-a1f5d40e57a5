<!DOCTYPE html>
<html lang="en">

<head>

    <title>Covered Call Scanner with AI-Powered Analysis | AI Bull</title>
    <meta name="description"
        content="Discover the best covered call opportunities using AI-powered analysis to maximize your income from options trading. Find optimal stocks for covered call strategies with ease." />
    <link rel="canonical" href="https://theaibull.com/strategies/covered-call" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/strategies/covered-call" />
    <meta property="og:title"
        content="Covered Call Scanner - Find the Best Covered Call Opportunities with AI-Powered Analysis" />
    <meta property="og:description"
        content="Discover the best covered call opportunities using AI-powered analysis to maximize your income from options trading." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title"
        content="Covered Call Scanner - Find the Best Covered Call Opportunities with AI-Powered Analysis" />
    <meta name="twitter:description"
        content="Find the best covered call opportunities with AI-powered analysis to optimize your options trading strategy." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {%include 'blocks/head.html' %}
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {%include 'blocks/common.html' %}
    {%include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">

        <main>
            <div class="p-3 mx-auto max-w-7xl w-full">

                <!-- Header Section -->
                <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2  rounded-lg mb-3">
                    <!-- Title and Tabs Row -->
                    <div class="md:flex justify-between items-center">
                        <div class="flex items-center">
                            <h1 class="md:text-xl text-lg font-semibold tracking-tight">Covered Call Scanner</h1>
                            <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden xl:block">Find the best covered call
                                opportunities
                                with
                                AI-powered analysis</h2>
                            <!-- Button to trigger modal open -->
                            <button id="openModalBtn" onclick="openModal()" class="ml-2">

                                <div class="flex group text-sm">
                                    <div
                                        class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                        <div
                                            class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-circle-help">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                    <path d="M12 17h.01"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div
                                            class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                            <div
                                                class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                                How to Use This Page</div>
                                        </div>
                                    </div>
                                </div>
                            </button>
                        </div>

                        <div class="rounded-lg bg-slate-100 p-0.5 w-[300px] mt-2 md:mt-0">
                            <nav class="-mb-px flex" aria-label="Tabs">
                                <button id="search-tab"
                                    class="tab-button w-1/2 py-2.5 px-1  text-center shadow-md font-medium text-sm text-blue-600 dark:text-blue-500 bg-white  rounded-md"
                                    aria-current="page">
                                    Search Stock
                                </button>
                                <button id="all-stocks-tab"
                                    class="tab-button w-1/2 py-2.5 px-1 text-center font-medium text-sm  text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300">
                                    Scan All Stocks
                                </button>
                            </nav>
                        </div>
                    </div>

                </div>

                <!-- info Section -->


                <!-- Search Tab Content -->
                <div id="search-content" class="tab-content">
                    <div class="flex flex-col gap-3">
                        <!-- Search Input Area -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                            <!-- Stock Input Form -->
                            <div>
                                <div class="flex flex-col gap-6">
                                    <div>
                                        {% with show_submit_button=false, show_indices=false %}
                                        {% include 'blocks/search-active-recent-stocks.html' %}
                                        {% endwith %}
                                    </div>

                                    <div class="flex flex-col md:flex-row gap-4 md:items-end">
                                        <div class="w-full md:w-1/3">
                                            <label class="block font-medium mb-2 text-sm">Purchase Price</label>
                                            <input id="price-input" type="number" step="0.01" placeholder="Optional"
                                                class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                                        </div>

                                        <div class="w-full md:w-1/3">
                                            <label class="block font-medium mb-2 text-sm">Min Premium %</label>
                                            <input id="min-premium-percent" type="number" step="0.1" value="1"
                                                class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                                        </div>

                                        <div class="w-full md:w-1/3">
                                            <button id="stock-input-submit-btn"
                                                class="bg-blue-500 block focus:outline-0 font-medium hover:bg-blue-600 px-6 py-2.5 rounded-md text-white transition-colors w-full">
                                                Show Options
                                            </button>
                                        </div>
                                    </div>

                                </div>

                            </div>
                        </div>

                        <!-- Price Info Area -->
                        <div id="price-info-container" class="bg-white dark:bg-neutral-800 rounded-lg p-4 hidden">
                            <div id="price-info"></div>
                        </div>

                        <!-- Options Table Area -->
                        <div id="options-table-container" class="bg-white dark:bg-neutral-800 rounded-lg p-4 hidden">
                            <div id="expiry-tabs"
                                class="border flex gap-0 mb-4 overflow-x-auto p-1 rounded-md text-nowrap"></div>
                            <div id="cover-call-table" class="overflow-x-auto"></div>
                        </div>
                    </div>
                </div>

                <!-- All Stocks Tab Content -->
                <div id="all-stocks-content" class="tab-content hidden">
                    {% include 'strategies/covered-call/covered-call-scanner.html' %}
                </div>


                <!-- FAQ Section in Accordion Format -->
                <div class="mt-8 text-sm">
                    <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
                    <div class="space-y-4">
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What is a covered call?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    A covered call is an options strategy where an investor holds a long position in a
                                    stock and sells (writes) a call
                                    option on the same stock. This generates income through premiums from the sold call
                                    options.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How does a covered call work?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    In a covered call, you buy shares of a stock and simultaneously sell a call option
                                    on those shares. If the stock price
                                    rises above the strike price of the call option, the stock may be "called away," and
                                    you sell the shares at that price.
                                    If the stock stays below the strike price, you keep both the stock and the premium
                                    received from the option sale.

                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What is the best expiration date for a covered call?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    The expiration date depends on your goals. Short-term expirations (weekly or
                                    monthly) provide quicker income but may
                                    involve more frequent management. Longer expiration dates offer larger premiums but
                                    tie up capital for a longer period.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                Can I sell multiple calls on the same stock?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    You can sell multiple call options on the same stock, but you need to own enough
                                    shares to cover each contract. One
                                    options contract typically represents 100 shares of the underlying stock.

                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What happens if the stock price stays below the strike price?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    If the stock price remains below the strike price, the option expires worthless. You
                                    keep the premium income, and your
                                    stock position is unaffected, allowing you to potentially sell another call option.
                                </p>
                            </div>
                        </details>
                    </div>
                </div>

            </div>
        </main>

        <div class="mt-auto">
            {% include 'blocks/footer.html' %}
        </div>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.js"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.css">

        <style>
            /* Make sliders show pointer cursor on hover */
            .noUi-target,
            .noUi-handle,
            .noUi-connect {
                cursor: pointer !important;
            }
        </style>
        {% include 'blocks/table-sorting.html' %}
        <script>
            document.addEventListener('DOMContentLoaded', async () => {
                const stockInput = document.getElementById('stock-input');
                const priceInput = document.getElementById('price-input');
                const minPremiumPercentInput = document.getElementById('min-premium-percent');
                const submitButton = document.getElementById('stock-input-submit-btn');

                const priceInfoContainer = document.getElementById('price-info');
                const expiryTabsContainer = document.getElementById('expiry-tabs');
                const tableContainer = document.getElementById('cover-call-table');

                document.addEventListener('stockSearchTriggered', async (e) => {
                    const symbol = e.detail.symbol;
                    let purchasePrice = parseFloat(priceInput.value) || 0;
                    if (window.lastSearchedSymbol != symbol) {
                        purchasePrice = 0;
                    }
                    const minPremPct = parseFloat(minPremiumPercentInput.value) || 1;

                    if (!symbol) {
                        showAlert('Please enter a valid Stock Symbol.');
                        return;
                    }

                    // Hide containers initially
                    document.getElementById('price-info-container').classList.add('hidden');
                    document.getElementById('options-table-container').classList.add('hidden');

                    // Clear old data
                    priceInfoContainer.innerHTML = '';
                    expiryTabsContainer.innerHTML = '';
                    tableContainer.innerHTML = '';

                    // If user did not supply a purchasePrice, fetch price details first
                    try {

                        const priceResponse = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/price/${symbol}`);
                        const dataPrice = await priceResponse.json();
                        // Extract the lastPrice from dataPrice.priceInfo.lastPrice
                        const lastPrice = dataPrice.priceInfo ? dataPrice.priceInfo.lastPrice : 0;
                        // Show price info table
                        purchasePrice = purchasePrice || lastPrice || 0; // use last price as cost
                        renderPriceInfo(dataPrice);
                    } catch (err) {
                        console.error('Error fetching symbol price:', err);
                        document.getElementById('price-info-container').classList.add('hidden');
                        document.getElementById('options-table-container').classList.add('hidden');
                        tableContainer.innerHTML = '<p class="text-red-500">Failed to fetch symbol price</p>';
                        return;
                    }

                    // Fetch the option chain
                    try {
                        const response = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${symbol}`);
                        const data = await response.json();

                        if (window.lastSearchedSymbol != symbol) {
                            priceInput.value = purchasePrice.toFixed(2);
                        }
                        window.lastSearchedSymbol = symbol;
                        buildExpiryData(data.records, purchasePrice, minPremPct);
                    } catch (err) {
                        console.error('Error fetching option chain:', err);
                        document.getElementById('options-table-container').classList.add('hidden');
                        tableContainer.innerHTML = '<p class="text-red-500">Failed to fetch option chain</p>';
                    }
                });


                // 2) Show price info table
                function renderPriceInfo(dataPrice) {
                    let purchasePrice = parseFloat(priceInput.value) || 0;
                    const { info, priceInfo } = dataPrice;
                    if (!info || !priceInfo) {
                        priceInfoContainer.innerHTML = '<p class="text-gray-500">No price info found.</p>';
                        return;
                    }

                    // Show the container
                    document.getElementById('price-info-container').classList.remove('hidden');

                    priceInfoContainer.innerHTML = `
                        <div>
                            <h2 class="font-semibold mb-3 text-left">${info.companyName || info.symbol}</h2>
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-2">
                                <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                    <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">Last Price</div>
                                    <div class="font-medium">${(priceInfo.lastPrice || 0).toFixed(2)}</div>
                                </div>
                                
                                <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                    <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">Change</div>
                                    <div class="font-medium ${priceInfo.change > 0 ? 'text-green-600' : 'text-red-500'}">
                                        ${(priceInfo.change || 0).toFixed(2)}
                                    </div>
                                </div>
                                
                                <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                    <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">% Change</div>
                                    <div class="font-medium ${priceInfo.pChange > 0 ? 'text-green-600' : 'text-red-500'}">
                                        ${priceInfo.pChange ? priceInfo.pChange.toFixed(2) + '%' : '0%'}
                                    </div>
                                </div>
                                
                                <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                    <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">Open</div>
                                    <div class="font-medium">${(priceInfo.open || 0).toFixed(2)}</div>
                                </div>
                                
                                <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                    <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">Previous Close</div>
                                    <div class="font-medium">${(priceInfo.previousClose || 0).toFixed(2)}</div>
                                </div>
                            </div>
                            ${purchasePrice ? '' : `
                                <p class="text-xs text-gray-600 mt-2">
                                    Using Last Price = <span class="text-neutral-950 dark:text-neutral-100 font-medium px-1">${(priceInfo.lastPrice || 0).toFixed(2)}</span> as your cost basis.
                                </p>
                            `}
                        </div>
                    `;
                }

                // 3) Build expiry data
                function buildExpiryData(records, purchasePrice, minPremPct) {
                    const { expiryDates, data } = records;
                    if (!expiryDates || !expiryDates.length) {
                        tableContainer.innerHTML = '<p class="text-gray-500">No expiry data found.</p>';
                        return;
                    }

                    // Show the container
                    document.getElementById('options-table-container').classList.remove('hidden');

                    const callsByExpiry = {};
                    expiryDates.forEach(ed => { callsByExpiry[ed] = []; });

                    // Filter for CE calls
                    data.forEach(opt => {
                        if (opt.CE) {
                            if (!callsByExpiry[opt.expiryDate]) callsByExpiry[opt.expiryDate] = [];
                            callsByExpiry[opt.expiryDate].push(opt);
                        }
                    });

                    // Render tabs
                    const tabsHtml = expiryDates.map((date, idx) => `
                            <button 
                                class="tab-btn px-4 py-1.5 rounded-sm text-xs font-medium
                                       ${idx === 0 ? 'bg-blue-500 text-white' : 'bg-white dark:bg-neutral-800  dark:hover:bg-neutral-700'}"
                                data-expiry="${date}"
                            >
                                ${date}
                            </button>
                        `).join('');
                    expiryTabsContainer.innerHTML = tabsHtml;

                    // Show first expiry by default
                    showCoveredCalls(callsByExpiry[expiryDates[0]] || [], purchasePrice, expiryDates[0], minPremPct);

                    // Add tab listeners
                    document.querySelectorAll('.tab-btn').forEach(btn => {
                        btn.addEventListener('click', function () {
                            document.querySelectorAll('.tab-btn').forEach(b => {
                                b.classList.remove('bg-blue-500', 'text-white');
                                b.classList.add('bg-white', 'dark:bg-neutral-800');
                            });
                            this.classList.remove('bg-white', 'dark:bg-neutral-800');
                            this.classList.add('bg-blue-500', 'text-white');
                            const ed = this.dataset.expiry;
                            showCoveredCalls(callsByExpiry[ed], purchasePrice, ed, minPremPct);
                        });
                    });
                }

                // 4) Show covered calls table
                function showCoveredCalls(callOptions, purchasePrice, expiryDate, minPremPct) {
                    if (!callOptions.length) {
                        tableContainer.innerHTML = `<p class="text-gray-500">No Call data for ${expiryDate}</p>`;
                        return;
                    }

                    let rowData = callOptions.map(opt => {
                        const ce = opt.CE;
                        const strike = ce.strikePrice;
                        const premium = ce.lastPrice || 0;
                        const volume = ce.totalTradedVolume || 0;
                        const bid = ce.bidprice || 0;
                        const ask = ce.askPrice || 0;

                        let isITM = false;
                        if (purchasePrice > 0 && strike < purchasePrice) {
                            isITM = true;
                        }

                        const premiumPercent = purchasePrice > 0 ? (premium / purchasePrice) * 100 : 0;
                        const profitIfSold = purchasePrice > 0 ? ((strike - purchasePrice) + premium) : 0;
                        const profitPercent = purchasePrice > 0 ? (profitIfSold / purchasePrice) * 100 : 0;
                        let probSalePct = ce.delta != null ? ce.delta * 100 : 0;

                        return {
                            strikePrice: strike,
                            volume,
                            bid,
                            ask,
                            premium,
                            premiumPercent,
                            profitIfSold,
                            profitPercent,
                            isITM,
                            probSalePct
                        };
                    });

                    rowData = rowData.filter(row => !row.isITM && row.premiumPercent >= minPremPct);

                    if (!rowData.length) {
                        tableContainer.innerHTML = `
                                <p class="text-red-500 text-sm py-2">
                                    No calls match your criteria (OTM & min ${minPremPct}% premium).
                                </p>
                            `;
                        return;
                    }

                    rowData.sort((a, b) => b.profitPercent - a.profitPercent);

                    let tableHtml = `
                            <div class="mb-2 md:flex justify-between items-center">
                               
                                <span class="block text-xs text-neutral-800 font-medium">
                                    <span class="text-neutral-500">Min Premium%</span> : ${minPremPct.toFixed(2)}% ; <span class="text-neutral-500">Cost =</span> ${purchasePrice.toFixed(2)}
                                </span>
                                <div class="text-gray-600 dark:text-gray-300 text-xs px-2 py-1 bg-red-50 inline-flex mt-2 md:mt-0">
                                 <spanclass="text-neutral-600 ">Expiry:</span><span class="font-medium pl-2 text-neutral-800">${expiryDate}</span>
                                </div>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="w-full border-collapse border dark:border-neutral-700 text-xs xl:text-sm w-full" id="cover-call-table-el">
                                    <thead class="bg-gray-50 dark:bg-neutral-700 text-neutral-600">
                                        <tr>
                                            <th class="border cursor-pointer dark:border-neutral-600 p-2 text-neutral-800 font-medium" data-col="strikePrice">Strike</th>
                                            <th class="border cursor-pointer dark:border-neutral-600 p-2 text-neutral-800 font-medium" data-col="volume">Volume</th>
                                            <th class="border cursor-pointer dark:border-neutral-600 p-2 text-neutral-800 font-medium" data-col="bid">Bid</th>
                                            <th class="border cursor-pointer dark:border-neutral-600 p-2 text-neutral-800 font-medium" data-col="ask">Ask</th>
                                            <th class="border cursor-pointer dark:border-neutral-600 p-2 text-neutral-800 font-medium" data-col="premium">LTP</th>
                                            <th class="border cursor-pointer dark:border-neutral-600 p-2 text-neutral-800 font-medium text-nowrap" data-col="premiumPercent">Prem %</th>
                                            <th class="border cursor-pointer dark:border-neutral-600 p-2 text-neutral-800 font-medium text-nowrap" data-col="profitIfSold">Profit If Sold</th>
                                            <th class="border cursor-pointer dark:border-neutral-600 p-2 text-neutral-800 font-medium text-nowrap" data-col="profitPercent">Profit %</th>
                                            <th class="border cursor-pointer dark:border-neutral-600 p-2 text-neutral-800 font-medium text-nowrap" data-col="probSalePct">Prob. of Sale</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;

                    rowData.forEach((row, idx) => {
                        const colorPremPct = row.premiumPercent < 0 ? 'text-red-500' : 'text-green-600';
                        const colorProf = row.profitIfSold < 0 ? 'text-red-500' : 'text-green-600';
                        const colorProfPct = row.profitPercent < 0 ? 'text-red-500' : 'text-green-600';
                        const colorProbSale = row.probSalePct < 0 ? 'text-red-500' : 'text-green-600';
                        const highlightRow = idx < 3 ? 'bg-yellow-50 dark:bg-yellow-900/20' : '';

                        tableHtml += `
                                <tr class="hover:bg-gray-50 dark:hover:bg-neutral-700/50 ${highlightRow}">
                                    <td class="border dark:border-neutral-600 p-2 text-center">${row.strikePrice}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center">${row.volume}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center">${row.bid.toFixed(2)}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center">${row.ask.toFixed(2)}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center">${row.premium.toFixed(2)}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center ${colorPremPct}">${row.premiumPercent.toFixed(2)}%</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center ${colorProf}">${row.profitIfSold.toFixed(2)}</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center ${colorProfPct}">${row.profitPercent.toFixed(2)}%</td>
                                    <td class="border dark:border-neutral-600 p-2 text-center ${colorProbSale}">${row.probSalePct.toFixed(1)}%</td>
                                </tr>
                            `;
                    });

                    tableHtml += `</tbody></table></div>`;
                    tableContainer.innerHTML = tableHtml;

                    setTimeout(() => window.addSorting('cover-call-table-el'), 0); // Call shared sorting
                }

                // Add tab switching functionality
                const searchTab = document.getElementById('search-tab');
                const allStocksTab = document.getElementById('all-stocks-tab');
                const searchContent = document.getElementById('search-content');
                const allStocksContent = document.getElementById('all-stocks-content');

                function switchTab(activeTab, activeContent, inactiveTab, inactiveContent) {
                    // Update tab styles
                    activeTab.classList.remove('text-gray-500', 'border-transparent', 'hover:text-gray-700', 'hover:border-gray-300');
                    activeTab.classList.add('bg-white', 'text-blue-600', 'rounded-md', 'shadow-md');

                    inactiveTab.classList.add('text-gray-500', 'border-transparent', 'hover:text-gray-700', 'hover:border-gray-300');
                    inactiveTab.classList.remove('bg-white', 'text-blue-600', 'rounded-md', 'shadow-md');

                    // Show/hide content
                    activeContent.classList.remove('hidden');
                    inactiveContent.classList.add('hidden');
                }

                searchTab.addEventListener('click', () => {
                    switchTab(searchTab, searchContent, allStocksTab, allStocksContent);
                });

                allStocksTab.addEventListener('click', () => {
                    switchTab(allStocksTab, allStocksContent, searchTab, searchContent);
                });
            });
        </script>


    </div>

</body>

</html>