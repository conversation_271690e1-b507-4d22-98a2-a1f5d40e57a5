<!DOCTYPE html>
<html lang="en">

<head>
    <title>Straddle Graph on Expiration Dates - AI-Powered Options Analysis | AI Bull</title>
    <meta name="description"
        content="Analyze straddle opportunities based on expiration dates with AI-powered options analysis at AIBull. Find optimal strategies for straddle trades." />
    <meta property="og:title" content="Straddle Graph on Expiration Dates - AI-Powered Options Analysis" />
    <meta property="og:description"
        content="Discover straddle opportunities based on expiration dates using our AI-powered analysis. Optimize your options strategy for maximum profit." />
    <meta name="twitter:title" content="Straddle Graph on Expiration Dates - AI-Powered Options Analysis" />
    <meta name="twitter:description"
        content="Discover straddle opportunities based on expiration dates with AI-powered analysis to optimize your options strategy." />
    <meta property="og:title"
        content="Long Straddle Graph - Find the Best Long Straddle Opportunities with AI-Powered Analysis" />
    <meta property="og:description"
        content="Find the best long straddle opportunities with AI-powered analysis. Optimize your strategy by identifying options with high potential for profit from significant price movements." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title"
        content="Long Straddle Graph - Find the Best Long Straddle Opportunities with AI-Powered Analysis" />
    <meta name="twitter:description"
        content="Find the best long straddle opportunities with AI-powered analysis to optimize your options strategy and profit from significant price movements." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />

    {% include 'blocks/head.html' %}
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {% include 'blocks/common.html' %}
    {% include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <main>
            <div class="p-3 mx-auto max-w-7xl w-full">
                <div
                    class="flex flex-col md:flex-row justify-between bg-white dark:bg-neutral-800 px-3 py-2  rounded-lg mb-3">
                    <div class="flex items-center">
                        <h1 class="text-xl font-semibold tracking-tight">
                            Straddle Analysis
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">
                            Discover profitable opportunities with AI-driven insights
                        </p>
                        <!-- Button to trigger modal open -->
                        <button id="openModalBtn" onclick="openModal()" class="ml-2">

                            <div class="flex group text-sm">
                                <div
                                    class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                    <div
                                        class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                        <div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-circle-help">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                <path d="M12 17h.01"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div
                                        class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                        <div
                                            class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                            How to Use This Page</div>
                                    </div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>
                <!-- Search Tab Content -->
                <div id="search-content" class="tab-content">
                    <div class="flex flex-col gap-3">
                        <!-- Left Column: Search and Most Active -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                            {% with show_submit_button=true %}
                            {% include 'blocks/search-active-recent-stocks.html' %}
                            {% endwith %}
                        </div>

                        <!-- Right Column: Results -->
                        <div id="right-section-sg" class="flex-1 bg-white dark:bg-neutral-800 rounded-lg p-4 hidden">
                            <div id="loading-expirations"></div>
                            <div class="flex flex-col md:flex-row gap-3  items-end justify-between mb-2">
                                <!-- Expiration Dates (Initially Hidden) -->

                                <div id="expiration-container" class="flex flex-col w-full hidden">
                                    <label for="select-expiration"
                                        class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select
                                        Expiration
                                        Date</label>
                                    <select id="select-expiration"
                                        class="appearance-none bg-transparent border border-gray-200 dark:bg-neutral-700 dark:border-neutral-600 focus:border-blue-500 focus:outline-0 px-3 py-2.5 rounded-md text-sm transition-all w-full">
                                        <option value="">Select Expiration Date</option>
                                    </select>
                                </div>

                                <!-- Strike Selection (Initially Hidden) -->
                                <div id="strike-container" class="flex flex-col w-full hidden">
                                    <label for="select-strike"
                                        class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select
                                        Strike</label>
                                    <select id="select-strike"
                                        class="appearance-none bg-transparent border border-gray-200 dark:bg-neutral-700 dark:border-neutral-600 focus:border-blue-500 focus:outline-0 px-3 py-2.5 rounded-md text-sm transition-all w-full">
                                        <option value="">Select Strike</option>
                                    </select>
                                </div>

                                <!-- Loading indicator for strike prices -->
                                <div id="loading-strikes" class="w-full hidden">
                                    <div class="text-center">
                                        <div
                                            class="w-6 h-6 border-4 border-blue-500 border-dashed rounded-full animate-spin mx-auto">
                                        </div>
                                        <p class="font-semibold mt-4">Loading Strike Prices...</p>
                                    </div>
                                </div>

                                <!-- Generate Graph Button (Initially Disabled) -->
                                <div class="flex flex-col w-full">
                                    <button id="generate-graph-btn"
                                        class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200 opacity-50 cursor-not-allowed"
                                        disabled>
                                        Generate Graph
                                    </button>
                                </div>
                            </div>

                            <!-- Chart Container -->
                            <div id="straddle-chart-container" class="h-[500px] relative hidden">
                                <canvas id="straddleChart" class="mt-4"></canvas>
                            </div>
                            <!-- Loading indicator for graph data -->
                            <div id="loading-indicator"></div>

                            <!-- Optional: Add controls for zoom/pan/reset -->
                            <div id="zoom-options" class="flex justify-center gap-2 mt-2 hidden">
                                <button id="reset-zoom-btn"
                                    class="text-sm bg-gray-200 dark:bg-neutral-700 hover:bg-gray-300 dark:hover:bg-neutral-600 px-3 py-1 rounded-md transition-colors">
                                    Reset View
                                </button>
                                <button id="zoom-in-btn"
                                    class="text-sm bg-gray-200 dark:bg-neutral-700 hover:bg-gray-300 dark:hover:bg-neutral-600 px-3 py-1 rounded-md transition-colors">
                                    Zoom In
                                </button>
                                <button id="zoom-out-btn"
                                    class="text-sm bg-gray-200 dark:bg-neutral-700 hover:bg-gray-300 dark:hover:bg-neutral-600 px-3 py-1 rounded-md transition-colors">
                                    Zoom Out
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- FAQ Section for Straddle Analysis -->
                <div class="mt-8 text-sm">
                    <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
                    <div class="space-y-4">
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4">
                                What is a straddle in options trading?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    A straddle is an options strategy where an investor buys both a call and a put
                                    option with the same
                                    strike price and expiration date. It allows the trader to profit from significant
                                    price movements in
                                    either direction, whether the stock price goes up or down.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4">
                                How does straddle analysis work?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Straddle analysis involves evaluating the potential profitability of a straddle
                                    position based on
                                    the stock's volatility and other market factors. The analysis helps identify
                                    profitable
                                    opportunities by comparing the potential price movements of both call and put
                                    options, along with
                                    associated premiums.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4">
                                What is the best time to use a straddle strategy?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    A straddle strategy is most effective when you expect a significant price move in a
                                    stock but are
                                    uncertain of the direction. This strategy is often used around events like earnings
                                    reports, product
                                    launches, or market announcements that may trigger high volatility.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4">
                                Can AI assist in straddle analysis?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Yes, AI-driven insights can help by analyzing historical data, identifying patterns,
                                    and predicting
                                    potential price movements. This can provide investors with more accurate and timely
                                    information to
                                    make better decisions regarding straddle strategies.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4">
                                What happens if the stock price doesn't move significantly?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    If the stock price doesn't move significantly, both the call and put options may
                                    expire worthless.
                                    The loss in this case is limited to the premiums paid for the options. Therefore,
                                    straddle
                                    strategies require a large price movement to be profitable.
                                </p>
                            </div>
                        </details>
                    </div>
                </div>


            </div>
        </main>
    </div>

    {% include 'blocks/stock-symbols.html' %}
    {% include 'blocks/options.html' %}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>

    <script>
        // Constants and Configuration
        const API_BASE_URL = 'https://iam.theaibull.com/v1/wg7ttpouv7';
        const MONTHS = { Jan: '01', Feb: '02', Mar: '03', Apr: '04', May: '05', Jun: '06', Jul: '07', Aug: '08', Sep: '09', Oct: '10', Nov: '11', Dec: '12' };
        const CHART_COLORS = { straddle: 'rgba(0, 128, 128, 1)', index: 'rgba(50, 50, 50, 1)', synthetic: 'rgba(150, 150, 150, 1)' };

        // Retained functions
        const fetchData = async (url) => {
            const response = await fetch(url);
            if (!response.ok) {
                return {};
            }
            return response.json();
        };

        const fetchStrikePrices = async (symbol, expiration) => {
            const strikeSelect = document.getElementById('select-strike');
            const loadingStrikesEl = document.getElementById('loading-strikes');

            // Show loading indicator for strike prices
            loadingStrikesEl.classList.remove('hidden');
            document.getElementById('strike-container').classList.add('hidden');

            const formatDateForAPI = (date) => {
                let day = String(date.getDate()).padStart(2, '0');
                let month = String(date.getMonth() + 1).padStart(2, '0');
                let year = date.getFullYear();
                return `${day}-${month}-${year}`;
            };
            let data, underlyingValue = 0, attempts = 0;
            while (attempts < 4) {
                let tryDate = new Date();
                tryDate.setDate(tryDate.getDate() - attempts);
                const dateParam = formatDateForAPI(tryDate);
                try {
                    data = await fetchData(`${API_BASE_URL}/symbol/options/${symbol}?date=${dateParam}&expiryDate=${expiration}`);
                    const record = data.records.data.find(r => (r.PE && r.PE.underlyingValue) || (r.CE && r.CE.underlyingValue));
                    underlyingValue = record ? (record.PE?.underlyingValue || record.CE?.underlyingValue) : 0;
                    if (underlyingValue) break;
                } catch (err) { }
                attempts++;
            }
            if (!underlyingValue) {
                showAlert('Could not determine underlying value after multiple attempts.');
                strikeSelect.innerHTML = '<option value="ATM">ATM</option>';
                loadingStrikesEl.classList.add('hidden');
                document.getElementById('strike-container').classList.remove('hidden');
                return [];
            }
            console.log('Underlying Value:', underlyingValue);
            const strikes = [...new Set(data.records.data.map(r => r.strikePrice).filter(Boolean))].sort((a, b) => a - b);
            const atmStrike = strikes.reduce((prev, curr) =>
                Math.abs(curr - underlyingValue) < Math.abs(prev - underlyingValue) ? curr : prev, strikes[0]);
            // Set the ATM strike as the default by including the 'selected' attribute:
            strikeSelect.innerHTML = `<option value="${atmStrike}" selected>ATM (${atmStrike})</option>` +
                strikes.filter(s => s !== atmStrike).map(s => `<option value="${s}">${s}</option>`).join('');
            // Explicitly set the default selected value as a string.
            // strikeSelect.value = atmStrike.toString();
            strikeSelect.dispatchEvent(new Event('change'));
            loadingStrikesEl.classList.add('hidden');
            document.getElementById('strike-container').classList.remove('hidden');
            return strikes;
        };

        const generateStraddleGraph = async () => {
            const symbol = document.getElementById('stock-input')?.value.trim().toUpperCase();
            const expiration = document.getElementById('select-expiration').value;
            const strike = document.getElementById('select-strike').value;
            const generateBtn = document.getElementById('generate-graph-btn');
            const straddleChartContainer = document.getElementById('straddle-chart-container');
            if (straddleChartContainer) straddleChartContainer.classList.add("hidden");
            if (!symbol || !expiration || !strike) {
                showAlert('Please complete all selections.');
                return;
            }
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.innerHTML = `<div class="text-center">
                    <div class="w-14 h-14 border-4 border-blue-500 border-dashed rounded-full animate-spin mx-auto"></div>
                    <p class="font-semibold mt-4">Loading CE & PE data...</p></div>`;
            }
            generateBtn.textContent = 'Loading...';
            generateBtn.disabled = true;
            try {
                const parts = expiration.split('-'); // e.g. ["24", "APR", "2025"]
                const monthNum = MONTHS[parts[1]] ? parseInt(MONTHS[parts[1]]) - 1 : new Date(Date.parse(parts[1] + " 1, 2000")).getMonth();
                const expDate = new Date(parts[2], monthNum, parts[0]);
                const today = new Date();
                const startDate = new Date(expDate);
                startDate.setMonth(startDate.getMonth() - 1);
                const endDate = expDate > today ? today : expDate;
                const pad = d => ('0' + (d.getMonth() + 1)).slice(-2);
                const formatDate = d => `${d.getFullYear()}-${pad(d)}-${('0' + d.getDate()).slice(-2)}`;

                // Compute option symbols for both PE and CE.
                const optionSymbolPE = `FO_${symbol}_${parts[1].toUpperCase()}_${parts[0]}_${parts[2]}_${strike}_PE`;
                const optionSymbolCE = `FO_${symbol}_${parts[1].toUpperCase()}_${parts[0]}_${parts[2]}_${strike}_CE`;

                const urlPE = `${API_BASE_URL}/backtesting/ohlcd/${optionSymbolPE}?interval=1m&start=${formatDate(startDate)}&end=${formatDate(endDate)}`;
                const urlCE = `${API_BASE_URL}/backtesting/ohlcd/${optionSymbolCE}?interval=1m&start=${formatDate(startDate)}&end=${formatDate(endDate)}`;

                const [ohlcDataPE, ohlcDataCE] = await Promise.all([
                    fetch(urlPE).then(r => r.json()),
                    fetch(urlCE).then(r => r.json())
                ]);

                // Combine data: for each timestamp, obtain PE, CE, and their sum.
                const combinedData = {};
                const allTimestamps = new Set([...Object.keys(ohlcDataPE), ...Object.keys(ohlcDataCE)]);
                allTimestamps.forEach(ts => {
                    const peClose = (ohlcDataPE[ts] && ohlcDataPE[ts].c) || 0;
                    const ceClose = (ohlcDataCE[ts] && ohlcDataCE[ts].c) || 0;
                    combinedData[ts] = { pe: peClose, ce: ceClose, sum: peClose + ceClose };
                });

                if (window.renderGraph) {
                    window.renderGraph(combinedData, { pe: true, ce: true, sum: true });
                }
            } catch (error) {
                showAlert(`Error generating graph: ${error.message}`);
            } finally {
                generateBtn.textContent = 'Generate Graph';
                generateBtn.disabled = false;
                if (loadingIndicator) {
                    loadingIndicator.innerHTML = ""; // Remove loader after completion
                    straddleChartContainer.classList.remove("hidden");
                }
            }
        };

        // Removed unused functions:
        // - fetchExpirationDates, fetchStockData, fetchStraddleData,
        // - fetchIndexOHLCData, fetchOptionOHLCData, parseStraddleData, createStraddleChart,
        // - formatExpiration (unused)

        function setupEventListeners() {
            // ...existing event listeners...
            document.getElementById('select-expiration').addEventListener('change', async (e) => {
                resetDefaultsStraddle();
                const symbol = document.getElementById('stock-input')?.value.trim().toUpperCase();
                if (e.target.value) {
                    // Disable Generate Graph button until a valid strike is chosen.
                    const btn = document.getElementById('generate-graph-btn');
                    btn.disabled = true;
                    btn.classList.add('opacity-50', 'cursor-not-allowed');
                    // Fetch strikes and show loading-strikes feedback if needed.
                    await fetchStrikePrices(symbol, e.target.value);
                    // The Generate Graph button will be enabled in the onChange event of the strike select.
                }
            });

            // Add an event listener to the strike select to enable the Generate Graph button when a valid strike is chosen.
            document.getElementById('select-strike').addEventListener('change', (e) => {
                resetDefaultsStraddle();
                // Unhide the strike container.
                document.getElementById('strike-container').classList.remove('hidden');
                const btn = document.getElementById('generate-graph-btn');
                if (e.target.value) {
                    btn.disabled = false;
                    btn.classList.remove('opacity-50', 'cursor-not-allowed');
                } else {
                    btn.disabled = true;
                    btn.classList.add('opacity-50', 'cursor-not-allowed');
                }
            });

            document.getElementById('generate-graph-btn').addEventListener('click', generateStraddleGraph);

            ['reset-zoom-btn', 'zoom-in-btn', 'zoom-out-btn'].forEach(id => {
                document.getElementById(id).addEventListener('click', () => {
                    if (window.straddleChart) {
                        const actions = {
                            'reset-zoom-btn': 'resetZoom',
                            'zoom-in-btn': () => window.straddleChart.zoom(1.2),
                            'zoom-out-btn': () => window.straddleChart.zoom(0.8)
                        };
                        typeof actions[id] === 'function' ? actions[id]() : window.straddleChart[actions[id]]();
                    }
                });
            });

            document.addEventListener('stockSearchTriggered', async (e) => {
                resetDefaultsStraddle();
                const symbol = e.detail.symbol;
                const loadingExp = document.getElementById('loading-expirations');
                if (loadingExp) {
                    loadingExp.innerHTML = `<div class="text-center">
                        <div class="w-14 h-14 border-4 border-blue-500 border-dashed rounded-full animate-spin mx-auto"></div>
                            <p class="font-semibold mt-4">Loading Expiration Dates...</p></div>`; // Loader message for expiration dates
                }
                console.log('Fetching expiration dates for symbol:', symbol);
                const currentYear = new Date().getFullYear();
                const url = `${API_BASE_URL}/symbol/expirationDates/${symbol}/${currentYear}`;
                const data = await fetchData(url);
                if (loadingExp) {
                    loadingExp.innerHTML = ""; // Remove loader after fetching expiration dates
                }
                if (!data?.expirationDates) return;
                const expirationDates = data.expirationDates;
                const today = new Date();
                const prevMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const currMonth = new Date(today.getFullYear(), today.getMonth(), 1);
                const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
                const filteredExpiries = expirationDates.filter(dateStr => {
                    const parts = dateStr.split('-'); // expected "DD-MMM-YYYY"
                    const monthNum = MONTHS[parts[1]] ? parseInt(MONTHS[parts[1]]) - 1 : new Date(Date.parse(parts[1] + " 1, 2000")).getMonth();
                    const d = new Date(parts[2], monthNum, parts[0]);
                    return (
                        (d.getMonth() === prevMonth.getMonth() && d.getFullYear() === prevMonth.getFullYear()) ||
                        (d.getMonth() === currMonth.getMonth() && d.getFullYear() === currMonth.getFullYear()) ||
                        (d.getMonth() === nextMonth.getMonth() && d.getFullYear() === nextMonth.getFullYear())
                    );
                });
                const expirationSelect = document.getElementById('select-expiration');
                if (expirationSelect) {
                    expirationSelect.innerHTML = `<option value="">Select Expiration Date</option>` +
                        filteredExpiries.map(exp => `<option value="${exp}">${exp}</option>`).join('');
                    document.getElementById('expiration-container').classList.remove('hidden');
                }
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            setupEventListeners();
        });

        // Add renderGraph function to display the graph using Chart.js
        window.renderGraph = function (data, options) {
            const timestamps = Object.keys(data).sort((a, b) => a - b);
            const peData = timestamps.map(ts => data[ts].pe);
            const ceData = timestamps.map(ts => data[ts].ce);
            const sumData = timestamps.map(ts => data[ts].sum);

            document.getElementById('zoom-options').classList.remove("hidden");
            const ctx = document.getElementById('straddleChart').getContext('2d');
            if (window.straddleChart && typeof window.straddleChart.destroy === 'function') {
                window.straddleChart.destroy();
            }
            window.straddleChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: timestamps,
                    datasets: [
                        {
                            label: 'PE (Close)',
                            data: peData,
                            borderColor: 'rgba(255, 99, 132, 1)',
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            fill: false
                        },
                        {
                            label: 'CE (Close)',
                            data: ceData,
                            borderColor: 'rgba(54, 162, 235, 1)',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            fill: false
                        },
                        {
                            label: 'Straddle (Sum)',
                            data: sumData,
                            borderColor: 'rgba(75, 192, 192, 1)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            ticks: {
                                // Converts epoch to a localized date and time string.
                                callback: function (value, index, ticks) {
                                    // this.getLabelForValue() returns the timestamp label as a string.
                                    let label = this.getLabelForValue(value);
                                    // If label appears to be in seconds (length 10), multiply by 1000.
                                    if (label.length === 10) {
                                        return new Date(parseInt(label) * 1000).toLocaleString();
                                    } else {
                                        return new Date(parseInt(label)).toLocaleString();
                                    }
                                }
                            }
                        }
                    },
                    plugins: {
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                mode: 'xy'
                            },
                            pan: {
                                enabled: true,
                                mode: 'xy'
                            }
                        }
                    }
                }
            });
        };

        function resetDefaultsStraddle() {
            document.getElementById('right-section-sg').classList.remove('hidden');
            const straddleChartContainer = document.getElementById('straddle-chart-container');
            const strikeContainer = document.getElementById('strike-container');
            const loadingStrikesEl = document.getElementById('loading-strikes');
            const zoomOptions = document.getElementById('zoom-options');
            const generateBtn = document.getElementById('generate-graph-btn');
            generateBtn.textContent = 'Generate Graph';
            generateBtn.disabled = true;
            if (straddleChartContainer) straddleChartContainer.classList.add("hidden");
            if (strikeContainer) strikeContainer.classList.add("hidden");
            if (loadingStrikesEl) loadingStrikesEl.classList.add("hidden");
            if (zoomOptions) zoomOptions.classList.add("hidden");
        }
    </script>

    {% include 'blocks/footer.html' %}

</body>

</html>