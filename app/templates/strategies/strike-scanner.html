<!DOCTYPE html>
<html lang="en">

<head>
    <title>AI-Powered Delta Scanner for Analyzing Opportunities | AI Bull</title>
    <meta name="description"
        content="Discover the best covered call opportunities with the AI-powered Delta Scanner. Analyze Delta values to optimize your covered call strategy and maximize options trading profits." />
    <link rel="canonical" href="https://theaibull.com/strategies/strike-scanner" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/strategies/strike-scanner" />
    <meta property="og:title"
        content="Delta Scanner - AI-Powered Delta Scanner for Analyzing Covered Call Opportunities" />
    <meta property="og:description"
        content="Discover the best covered call opportunities with the AI-powered Delta Scanner. Analyze Delta values to optimize your covered call strategy and maximize options trading profits." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title"
        content="Delta Scanner - AI-Powered Delta Scanner for Analyzing Covered Call Opportunities" />
    <meta name="twitter:description"
        content="Use the AI-powered Delta Scanner to find the best covered call opportunities by analyzing Delta values and optimizing your options trading strategy." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {%include 'blocks/head.html' %}
</head>
<style>
    /* Styling for the small detail text below strike values */
    .detail-info {
        font-size: 0.8rem;
        color: #2563eb;
    }
</style>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {%include 'blocks/common.html' %}

    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">

        {% include 'blocks/spreads-preview.html' %}
        {% include 'blocks/timestamp.html' %}
        {% include 'blocks/lot-sizes.html' %}
        {%include 'blocks/info.html' %}
        <main>
            <div class="p-3 mx-auto max-w-7xl w-full">
                <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2  rounded-lg mb-3">
                    <!-- Title and Tabs Row -->
                    <div class="md:flex justify-between items-center">
                        <div class="flex items-center">
                            <h1 class="md:text-xl text-lg font-semibold tracking-tight">Delta Scanner</h1>
                            <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">AI-Powered Delta Scanner
                                for Analyzing Covered Call Opportunities</h2>
                            <!-- Button to trigger modal open -->
                            <button id="openModalBtn" onclick="openModal()" class="ml-2">

                                <div class="flex group text-sm">
                                    <div
                                        class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                        <div
                                            class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-circle-help">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                    <path d="M12 17h.01"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div
                                            class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                            <div
                                                class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                                How to Use This Page</div>
                                        </div>
                                    </div>
                                </div>
                            </button>
                        </div>

                    </div>
                </div>


                <div>
                    <div class="w-full">
                        <!-- Stock Category Selector -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-3">
                            <div class="flex gap-4 flex-wrap">
                                <label class="flex items-center">
                                    <input type="radio" name="stock-category" value="index" checked class="mr-2">
                                    Indices
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="stock-category" value="nifty" class="mr-2">
                                    Nifty Stocks
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="stock-category" value="fo" class="mr-2">
                                    F&amp;O Stocks
                                </label>
                            </div>
                        </div>
                        <!-- Ticker Selection Section -->
                        <div
                            class="bg-white dark:bg-neutral-800 rounded-lg p-4 flex flex-col max-h-[350px] lg:max-h-[calc(60vh)] mb-3">
                            <div class="md:flex justify-between items-center mb-3">
                                <h3 class="font-semibold mb-2">Select Stocks/Indices</h3>
                                <div class="flex gap-2 items-center">
                                    <button id="select-all"
                                        class="text-xs hover:text-blue-500 text-neutral-700 font-medium">Select
                                        All</button>
                                    <span class="text-gray-400 text-xs">|</span>
                                    <button id="unselect-all"
                                        class="text-xs hover:text-red-500 text-neutral-700 font-medium">Unselect
                                        All</button>
                                </div>

                            </div>
                            <div id="ticker-selection" class="mb-4 flex-1 overflow-y-auto custom-scroll"></div>
                        </div>
                        <!-- Global Filter Inputs -->
                        <div
                            class="bg-white dark:bg-neutral-800 gap-3 grid md:grid-cols-4 grid grid-cols-1 items-end p-4 rounded-lg ">
                            <div>
                                <label class="block font-medium mb-2 text-sm">Max Delta <span
                                        class="text-xs text-gray-400">(default
                                        0.1)</span></label>
                                <input id="max-delta" type="number" step="0.01" value="0.1"
                                    class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                            </div>
                            <div>
                                <label class="block font-medium mb-2 text-sm">Jump Strikes <span
                                        class="text-xs text-gray-400">(default
                                        2)</span></label>
                                <input id="jump-strikes" type="number" value="2"
                                    class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                            </div>
                            <div>
                                <label class="block font-medium mb-2 text-sm">Min Call Volume <span
                                        class="text-xs text-gray-400">(default
                                        500)</span></label>
                                <input id="min-volume" type="number" value="500"
                                    class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                            </div>
                            {% with id='strike-scanner-use-bid-ask' %}
                            {% include 'blocks/use-ltp-checkbox.html' %}
                            {% endwith %}
                            <button id="show-button"
                                class="bg-blue-500 block focus:outline-0 font-medium hover:bg-blue-600 px-6 py-3 rounded-md text-white transition-colors w-full">Scan
                                Stocks</button>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div id="filter-section" class="bg-white lg:mt-0 mt-3 p-4 rounded-lg hidden">
                            <!-- Expiration Tabs -->
                            <div class="font-medium mb-2 text-sm">Expiry</div>
                            <div id="expiry-tabs"
                                class="flex gap-2 overflow-x-auto p-1 rounded-md overflow-auto text-nowrap flex-wrap bg-white">
                            </div>

                        </div>
                        <!-- Delta Pairs, Summary & Wings Tables -->
                        <div id="cash-put-table"
                            class="lg:max-h-[calc(100vh_-_100px)] custom-scroll overflow-y-auto mt-3">
                        </div>

                        <div id="dataContainer"
                            class="mt-3 mb-3 p-4 rounded-lg bg-white dark:bg-neutral-800 dark:border-neutral-700 hidden">
                            <div class="bg-white flex flex-wrap lg:mt-0 mt-3 p-4 rounded-lg justify-between gap-3">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- FAQ Section in Accordion Format -->
                <div class="mt-8 text-sm">
                    <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
                    <div class="space-y-4">
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What is a Delta Scanner?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    A Delta Scanner analyzes options' Delta values to identify the best covered call
                                    opportunities, helping you optimize
                                    your options strategy by evaluating the sensitivity of option prices to changes in
                                    the underlying asset.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How does Delta impact covered calls?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Delta measures the rate of change in an option's price relative to changes in the
                                    price of the underlying asset. In
                                    covered calls, a higher Delta means the option is more sensitive to the asset's
                                    price movements, potentially leading to
                                    higher premiums.

                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How do I set the desired Delta range?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    You can set the Delta range in the scanner's settings. Adjust it to focus on options
                                    with specific sensitivities to
                                    price movements, helping you find the most optimal covered call opportunities.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What types of assets can I analyze with the Delta Scanner?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    You can use the Delta Scanner to analyze a wide range of stocks and indices for
                                    covered call opportunities. Simply enter
                                    the ticker symbol of the asset you wish to explore.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What are the key benefits of using the Delta Scanner?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    The Delta Scanner helps you find the best covered call opportunities by focusing on
                                    options with favorable Delta values.
                                    This leads to optimized returns by selecting options that react more predictably to
                                    market movements.
                                </p>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
        </main>

        <!-- Summary Modal -->
        <div id="summaryModal"
            class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center hidden z-50 p-4">
            <div
                class="modal-content bg-white dark:bg-neutral-800 relative w-full max-w-7xl max-h-[90vh] rounded-lg shadow-lg flex flex-col">
                <!-- Modal Header -->
                <div class="p-4 border-b dark:border-neutral-700">
                    <h2 class="text-xl font-semibold">Summary - Top 25 Pairs</h2>
                    <button id="closeSummaryModal"
                        class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 text-2xl font-bold">
                        &times;
                    </button>
                </div>
                <!-- Modal Body with Scrollable Content -->
                <div class="flex-1 overflow-auto p-4">
                    <div id="summaryTableContainer"></div>
                </div>
            </div>
        </div>

        <!-- Wings Modal -->
        <div id="wingsModal"
            class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center hidden z-50 p-4">
            <div
                class="modal-content bg-white dark:bg-neutral-800 relative w-full max-w-7xl max-h-[90vh] rounded-lg shadow-lg flex flex-col">
                <!-- Modal Header -->
                <div class="p-4 border-b dark:border-neutral-700">
                    <h2 class="text-xl font-bold">Wings Table</h2>
                    <button id="closeWingsModal"
                        class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 text-2xl font-bold">
                        &times;
                    </button>
                </div>
                <!-- Modal Body with Scrollable Content -->
                <div class="flex-1 overflow-auto p-4">
                    <div id="wingsTableContainer"></div>
                </div>
            </div>
        </div>

        <div class="mt-auto">
            {% include 'blocks/footer.html' %}
        </div>
        {% include 'blocks/stock-symbols.html' %}
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log("Delta Scanner DOM fully loaded.");

            // Global array to accumulate summary pairs for the current expiry
            let globalSummary = [];

            let currentTickers = indexTickers;
            let currentCategory = "index";
            const scannedData = {};
            let scannedTickers = [];
            let selectedExpiry = null;


            // ------------------------------------------------------------------
            // DOM Elements
            // ------------------------------------------------------------------
            const tickerSelectionContainer = document.getElementById('ticker-selection');
            const selectAllBtn = document.getElementById('select-all');
            const unselectAllBtn = document.getElementById('unselect-all');
            const scanButton = document.getElementById('show-button');
            const dataContainer = document.getElementById('dataContainer');
            const expiryTabsContainer = document.getElementById('expiry-tabs');
            const tableContainer = document.getElementById('cash-put-table');

            // ------------------------------------------------------------------
            // Build Ticker Selection Checkboxes
            // ------------------------------------------------------------------
            function buildTickerSelection(tickerArray) {
                let html = '<div class="border border-l-0 divide-x divide-y grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 md:grid-cols-4">';
                tickerArray.forEach(ticker => {
                    html += `
            <label class="flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-neutral-700 first:border-l text-sm">
              <input type="checkbox" value="${ticker}" class="ticker-checkbox" checked>
              <span>${ticker}</span>
            </label>
          `;
                });
                html += '</div>';
                tickerSelectionContainer.innerHTML = html;
            }
            buildTickerSelection(currentTickers);

            // ------------------------------------------------------------------
            // Handle Stock Category Radio Changes
            // ------------------------------------------------------------------
            const stockCategoryRadios = document.getElementsByName('stock-category');
            stockCategoryRadios.forEach(radio => {
                radio.addEventListener('change', function () {
                    if (this.checked) {
                        currentCategory = this.value;
                        if (currentCategory === "nifty") currentTickers = niftyTickers;
                        else if (currentCategory === "fo") currentTickers = foTickers;
                        else if (currentCategory === "index") currentTickers = indexTickers;
                        buildTickerSelection(currentTickers);
                    }
                });
            });

            // ------------------------------------------------------------------
            // Select All / Unselect All Buttons
            // ------------------------------------------------------------------
            selectAllBtn.addEventListener('click', () => {
                document.querySelectorAll('.ticker-checkbox').forEach(cb => cb.checked = true);
            });
            unselectAllBtn.addEventListener('click', () => {
                document.querySelectorAll('.ticker-checkbox').forEach(cb => cb.checked = false);
            });

            // ------------------------------------------------------------------
            // Process a Single Ticker: Fetch Option Chain Data
            // ------------------------------------------------------------------
            async function processTicker(ticker) {
                let tickerContainer = document.getElementById("ticker-" + ticker);
                if (!tickerContainer) {
                    tickerContainer = document.createElement("div");
                    tickerContainer.id = "ticker-" + ticker;
                    tickerContainer.className = "mb-3 p-3 rounded-md bg-white dark:bg-neutral-800 dark:border-neutral-700 rounded-lg";
                    tickerContainer.innerHTML = `
                        <h2 id="ticker-header-${ticker}" class="flex flex-col md:flex-row gap-3 font-medium md:items-center justify-between mb-2 text-base">${ticker} - Scanning...</h2>
                        <div id="ticker-content-${ticker}"></div>
                    `;
                    tableContainer.appendChild(tickerContainer);
                }

                const contentContainer = document.getElementById("ticker-content-" + ticker);
                let lotSize = LotSizes[ticker.toUpperCase()] || 1;
                try {
                    const optionsUrl = (currentCategory === "index") ?
                        `https://iam.theaibull.com/v1/wg7ttpouv7/indices/options/${ticker}` :
                        `https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${ticker}`;
                    const resOptions = await fetch(optionsUrl);
                    const data = await resOptions.json();
                    const records = data.records;
                    const expiryDates = records.expiryDates || [];
                    const optionData = records.data || [];
                    let purchasePrice = 0;
                    if (optionData && optionData.length > 0) {
                        for (const opt of optionData) {
                            if (opt.CE && opt.CE.underlyingValue) {
                                purchasePrice = opt.CE.underlyingValue;
                                break;
                            }
                        }
                    }
                    scannedData[ticker] = { expiryDates, optionData, lotSize };
                    // Update ticker header with the extracted price
                    tickerContainer.querySelector("h2").innerHTML = `${ticker} - Price: ${purchasePrice.toFixed(2)}`;
                } catch (err) {
                    console.error("Error fetching options for " + ticker, err);
                    contentContainer.innerHTML = `<p class="text-red-500">Failed to fetch data for ${ticker}</p>`;
                }
            }

            // ------------------------------------------------------------------
            // Build Expiration Tabs (using expiry dates from first ticker)
            // ------------------------------------------------------------------
            function buildExpiryTabs(expiries) {
                let tabsHtml = expiries.map((exp, idx) => `
          <button class="tab-btn px-2 py-1.5 text-xs rounded font-medium ${idx === 0 ? 'bg-blue-500 text-white' : 'bg-gray-100'}" 
                  data-expiry="${exp}">
            ${exp}
          </button>
        `).join('');
                expiryTabsContainer.innerHTML = tabsHtml;
                selectedExpiry = expiries[0];
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        document.querySelectorAll('.tab-btn').forEach(b => {
                            b.classList.remove('bg-blue-500', 'text-white');
                            b.classList.add('bg-gray-100', 'dark:bg-neutral-800');
                        });
                        this.classList.add('bg-blue-500', 'text-white');
                        this.classList.remove('bg-gray-100', 'dark:bg-neutral-800');
                        selectedExpiry = this.dataset.expiry;
                        globalSummary = [];
                        scannedTickers.forEach(ticker => renderDeltaPairs(ticker, selectedExpiry));
                        buildSummaryTable();
                        buildWingsTable();
                    });
                });
            }

            // ------------------------------------------------------------------
            // Render Delta Pairs for a Given Ticker and Expiry; update header & accumulate summary
            // ------------------------------------------------------------------
            function renderDeltaPairs(ticker, expiry) {
                const tickerData = scannedData[ticker];
                if (!tickerData) return;
                let spot = null;
                for (let item of tickerData.optionData) {
                    if (item.CE && item.CE.underlyingValue) { spot = parseFloat(item.CE.underlyingValue); break; }
                    if (item.PE && item.PE.underlyingValue) { spot = parseFloat(item.PE.underlyingValue); break; }
                }
                let lotSize = tickerData.lotSize;

                let tickerContainer = document.getElementById('ticker-' + ticker);
                if (!tickerContainer) {
                    tickerContainer = document.createElement('div');
                    tickerContainer.id = 'ticker-' + ticker;
                    tickerContainer.className = 'mb-3 p-4 rounded-lg bg-white dark:bg-neutral-800 dark:border-neutral-700';
                    tickerContainer.innerHTML = `<div id="ticker-header-${ticker}" class="flex flex-col md:flex-row gap-3 font-medium md:items-center justify-between mb-2 text-base">${ticker}</div>
                                       <div id="ticker-content-${ticker}"></div>`;
                    tableContainer.appendChild(tickerContainer);
                }
                const contentContainer = document.getElementById('ticker-content-' + ticker);

                document.getElementById("ticker-header-" + ticker).innerHTML =
                    `<div class="font-medium">${ticker} ${spot ? "(Spot Price: " + spot.toFixed(2) + ")</div>" : "(Spot Price: N/A)"}<div class="flex font-medium gap-3">
          <p class="bg-red-50 px-1 py-0.5 rounded-md text-sm"> Expiry: ${selectedExpiry}</p>
           <p class="bg-blue-50 px-1 py-0.5 rounded-md text-sm">Lot Size: ${lotSize}</p></div>`;

                let filtered = tickerData.optionData.filter(item => item.expiryDate === selectedExpiry);
                let callOptions = filtered.filter(item =>
                    item.CE && item.CE.lastPrice && item.CE.totalTradedVolume &&
                    typeof item.CE.delta !== "undefined" && item.CE.underlyingValue
                );
                let putOptions = filtered.filter(item =>
                    item.PE && item.PE.lastPrice && item.PE.totalTradedVolume &&
                    typeof item.PE.delta !== "undefined" && item.PE.underlyingValue
                );

                const maxDelta = parseFloat(document.getElementById('max-delta').value) || 0.1;
                const jumpInput = parseInt(document.getElementById('jump-strikes').value) || 1;
                const minVolume = parseInt(document.getElementById('min-volume').value) || 500;

                // For call options: sort ascending by strike
                let sortedCalls = [...callOptions].sort((a, b) => a.strikePrice - b.strikePrice);
                let allowedJumpCalls = jumpInput;
                if (sortedCalls.length > 1) {
                    let steps = [];
                    for (let i = 1; i < sortedCalls.length; i++) {
                        steps.push(sortedCalls[i].strikePrice - sortedCalls[i - 1].strikePrice);
                    }
                    let minStep = Math.min(...steps);
                    allowedJumpCalls = jumpInput * minStep;
                }
                let callPairs = [];
                for (let i = 0; i < sortedCalls.length; i++) {
                    for (let j = i + 1; j < sortedCalls.length; j++) {
                        let diff = sortedCalls[j].strikePrice - sortedCalls[i].strikePrice;
                        if (diff <= allowedJumpCalls) {
                            // Use bid/ask prices if checkbox is checked
                            let ltp1 = !window.useLTPForPrices ?
                                (sortedCalls[i].CE.bidprice || sortedCalls[i].CE.lastPrice) :
                                sortedCalls[i].CE.lastPrice;
                            let ltp2 = !window.useLTPForPrices ?
                                (sortedCalls[j].CE.askPrice || sortedCalls[j].CE.lastPrice) :
                                sortedCalls[j].CE.lastPrice;
                            let delta1 = Math.abs(parseFloat(sortedCalls[i].CE.delta));
                            let delta2 = Math.abs(parseFloat(sortedCalls[j].CE.delta));
                            let vol1 = parseFloat(sortedCalls[i].CE.totalTradedVolume);
                            let vol2 = parseFloat(sortedCalls[j].CE.totalTradedVolume);
                            if (delta1 > maxDelta || delta2 > maxDelta) continue;
                            if (vol1 < minVolume || vol2 < minVolume) continue;
                            let priceDiff = ltp2 - ltp1;
                            let breakeven = sortedCalls[i].strikePrice + (ltp1 - ltp2);
                            let breakevenPct = ((breakeven - parseFloat(sortedCalls[i].CE.underlyingValue)) / parseFloat(sortedCalls[i].CE.underlyingValue)) * 100;
                            let pair = {
                                stock: ticker,
                                type: "CE",
                                strike1: sortedCalls[i].strikePrice,
                                ltp1,
                                vol1,
                                delta1,
                                strike2: sortedCalls[j].strikePrice,
                                ltp2,
                                vol2,
                                delta2,
                                priceDiff,
                                breakeven,
                                breakevenPct,
                                premiumCollected: (-priceDiff) * lotSize
                            };
                            callPairs.push(pair);
                            globalSummary.push(pair);
                        }
                    }
                }
                callPairs.sort((a, b) => b.premiumCollected - a.premiumCollected);
                callPairs = callPairs.slice(0, 3);

                // For put options: sort descending by strike
                let sortedPuts = [...putOptions].sort((a, b) => b.strikePrice - a.strikePrice);
                let allowedJumpPuts = jumpInput;
                if (sortedPuts.length > 1) {
                    let steps = [];
                    for (let i = 1; i < sortedPuts.length; i++) {
                        steps.push(sortedPuts[i - 1].strikePrice - sortedPuts[i].strikePrice);
                    }
                    let minStep = Math.min(...steps);
                    allowedJumpPuts = jumpInput * minStep;
                }
                let putPairs = [];
                for (let i = 0; i < sortedPuts.length; i++) {
                    for (let j = i + 1; j < sortedPuts.length; j++) {
                        let diff = sortedPuts[i].strikePrice - sortedPuts[j].strikePrice;
                        if (diff <= allowedJumpPuts) {
                            // Use bid/ask prices if checkbox is checked
                            let ltp1 = !window.useLTPForPrices ?
                                (sortedPuts[i].PE.bidprice || sortedPuts[i].PE.lastPrice) :
                                sortedPuts[i].PE.lastPrice;
                            let ltp2 = !window.useLTPForPrices ?
                                (sortedPuts[j].PE.askPrice || sortedPuts[j].PE.lastPrice) :
                                sortedPuts[j].PE.lastPrice;
                            let delta1 = Math.abs(parseFloat(sortedPuts[i].PE.delta));
                            let delta2 = Math.abs(parseFloat(sortedPuts[j].PE.delta));
                            let vol1 = parseFloat(sortedPuts[i].PE.totalTradedVolume);
                            let vol2 = parseFloat(sortedPuts[j].PE.totalTradedVolume);
                            if (delta1 > maxDelta || delta2 > maxDelta) continue;
                            if (vol1 < minVolume || vol2 < minVolume) continue;
                            let priceDiff = ltp1 - ltp2;
                            let breakeven = sortedPuts[i].strikePrice - (ltp1 - ltp2);
                            let breakevenPct = ((breakeven - parseFloat(sortedPuts[i].PE.underlyingValue)) / parseFloat(sortedPuts[i].PE.underlyingValue)) * 100;
                            let pair = {
                                stock: ticker,
                                type: "PE",
                                strike1: sortedPuts[i].strikePrice,
                                ltp1,
                                vol1,
                                delta1,
                                strike2: sortedPuts[j].strikePrice,
                                ltp2,
                                vol2,
                                delta2,
                                priceDiff,
                                breakeven,
                                breakevenPct,
                                premiumCollected: priceDiff * lotSize
                            };
                            putPairs.push(pair);
                            globalSummary.push(pair);
                        }
                    }
                }
                putPairs.sort((a, b) => b.premiumCollected - a.premiumCollected);
                putPairs = putPairs.slice(0, 3);

                function buildPairRow(pair) {
                    let priceLot = pair.premiumCollected;
                    let beSign = pair.breakevenPct >= 0 ? '+' : '';

                    // Convert pair data to legs format for preview
                    const legs = [
                        {
                            type: pair.type,
                            action: 'sell',
                            strike: pair.strike1,
                            price: pair.ltp1,
                            expiryDate: selectedExpiry,
                            lots: 1
                        },
                        {
                            type: pair.type,
                            action: 'buy',
                            strike: pair.strike2,
                            price: pair.ltp2,
                            expiryDate: selectedExpiry,
                            lots: 1
                        }
                    ];

                    return `
            <tr class="hover:bg-gray-50">
              <td class="border p-1 text-center">
                ${pair.strike1}<br><span class="detail-info">LTP: ${pair.ltp1.toFixed(2)}, Vol: ${pair.vol1}, Δ: ${pair.delta1.toFixed(2)}</span>
              </td>
              <td class="border p-1 text-center">
                ${pair.strike2}<br><span class="detail-info">LTP: ${pair.ltp2.toFixed(2)}, Vol: ${pair.vol2}, Δ: ${pair.delta2.toFixed(2)}</span>
              </td>
              <td class="border p-1 text-center">${pair.priceDiff.toFixed(2)}</td>
              <td class="border p-1 text-center">${pair.breakeven.toFixed(2)} (${beSign}${pair.breakevenPct.toFixed(0)}%)</td>
              <td class="border p-1 text-center">${priceLot.toFixed(2)}</td>
              <td class="border p-1 text-center">
                <button 
                    onclick='window.PreviewModal.show({
                        legs: ${JSON.stringify(legs)},
                        symbol: "${pair.stock}",
                        spotPrice: ${spot || 0},
                        optionChain: ${JSON.stringify(scannedData[pair.stock].optionData)},
                        expiryDates: ${JSON.stringify(scannedData[pair.stock].expiryDates)},
                        allowedTypes: ["CE", "PE"]
                    })' 
                    title="Preview Strategy"
                    class="p-1 hover:bg-gray-100 rounded">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                         stroke-linecap="round" stroke-linejoin="round" 
                         class="lucide lucide-chart-candlestick text-gray-500">
                        <path d="M9 5v4"></path>
                        <rect width="4" height="6" x="7" y="9" rx="1"></rect>
                        <path d="M9 15v2"></path>
                        <path d="M17 3v2"></path>
                        <rect width="4" height="8" x="15" y="5" rx="1"></rect>
                        <path d="M17 13v3"></path>
                        <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
                    </svg>
                </button>
              </td>
            </tr>
          `;
                }

                let contentHTML = "";
                if (callPairs.length > 0) {
                    let callTable = `
            <h4 class="font-medium mb-1 text-neutral-800 text-sm">Call Delta Pairs (Top 3)</h4>
            <div class="overflow-auto">
            <table class="w-full border-collapse border text-sm mb-4">
              <thead>
                <tr class="bg-gray-100">
                  <th class="border p-2 text-nowrap" data-col="strike1">Strike 1 (LTP, Vol, Δ)</th>
                  <th class="border p-2 text-nowrap" data-col="strike2">Strike 2 (LTP, Vol, Δ)</th>
                  <th class="border p-2 text-nowrap" data-col="priceDiff">Price Diff</th>
                  <th class="border p-2 text-nowrap" data-col="breakeven">Breakeven (%)</th>
                  <th class="border p-2 text-nowrap" data-col="priceLot">Price (Lot)</th>
                  <th class="border p-2 text-nowrap">Preview</th>
                </tr>
              </thead>
              <tbody>
          `;
                    callPairs.forEach(pair => { callTable += buildPairRow(pair); });
                    callTable += `</tbody></table></div>`;
                    contentHTML += callTable;
                } else {
                    contentHTML += `<p class="text-red-500 my-4 text-sm">No Call pairs found matching criteria.</p>`;
                }
                if (putPairs.length > 0) {
                    let putTable = `
            <h4 class="font-medium mb-1 text-neutral-800 text-sm">Put Delta Pairs (Top 3)</h4>
            <div class="overflow-auto">
            <table class="w-full border-collapse border text-sm">
              <thead>
                <tr class="bg-gray-100">
                  <th class="border p-2 text-nowrap">Strike 1 (LTP, Vol, Δ)</th>
                  <th class="border p-2 text-nowrap">Strike 2 (LTP, Vol, Δ)</th>
                  <th class="border p-2 text-nowrap">Price Diff</th>
                  <th class="border p-2 text-nowrap">Breakeven (%)</th>
                  <th class="border p-2 text-nowrap">Price (Lot)</th>
                  <th class="border p-2 text-nowrap">Preview</th>
                </tr>
              </thead>
              <tbody>
          `;
                    putPairs.forEach(pair => { putTable += buildPairRow(pair); });
                    putTable += `</tbody></table>
              </div>`;
                    contentHTML += putTable;
                } else {
                    contentHTML += `<p class="text-red-500 text-sm my-4">No Put pairs found matching criteria.</p>`;
                }
                document.getElementById('ticker-content-' + ticker).innerHTML = contentHTML;
            }

            // ------------------------------------------------------------------
            // Build Summary Table (Top 25 pairs for current expiry)
            // Strike cells now include LTP (in detail-info), volume and delta.
            // ------------------------------------------------------------------
            function buildSummaryTable() {
                let sortedSummary = [...globalSummary].sort((a, b) => b.premiumCollected - a.premiumCollected).slice(0, 25);
                let summaryHTML = `
                <div class="overflow-x-auto bg-white text-sm">
                    <h2 class="text-xl font-bold mb-2">Summary - Top 25 Pairs</h2>

                    <table class="w-full border-collapse border dark:border-neutral-700" id="summary-table">
                        <thead class="bg-gray-50 dark:bg-neutral-700">
                            <tr>
                               <th class="border p-2 text-nowrap" data-col="stock">Stock</th>
                               <th class="border p-2 text-nowrap" data-col="type">Type</th>
                               <th class="border p-2 text-nowrap" data-col="strike1">Strike 1 (LTP, Vol, Δ)</th>
                               <th class="border p-2 text-nowrap" data-col="strike2">Strike 2 (LTP, Vol, Δ)</th>
                               <th class="border p-2 text-nowrap" data-col="breakeven">Breakeven (%)</th>
                               <th class="border p-2 text-nowrap" data-col="premium">Premium Collected (Lot)</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                sortedSummary.forEach(pair => {
                    let beSign = pair.breakevenPct >= 0 ? '+' : '';
                    summaryHTML += `
            <tr class="hover:bg-gray-50">
              <td class="border p-1 text-center">${pair.stock}</td>
              <td class="border p-1 text-center">${pair.type}</td>
              <td class="border p-1 text-center">
                ${pair.strike1}<br>
                <span class="detail-info">LTP: ${pair.ltp1.toFixed(2)}, Vol: ${pair.vol1}, Δ: ${pair.delta1.toFixed(2)}</span>
              </td>
              <td class="border p-1 text-center">
                ${pair.strike2}<br>
                <span class="detail-info">LTP: ${pair.ltp2.toFixed(2)}, Vol: ${pair.vol2}, Δ: ${pair.delta2.toFixed(2)}</span>
              </td>
              <td class="border p-1 text-center">${pair.breakeven.toFixed(2)} (${beSign}${pair.breakevenPct.toFixed(0)}%)</td>
              <td class="border p-1 text-center">${pair.premiumCollected.toFixed(2)}</td>
            </tr>
          `;
                });
                summaryHTML += `
                </tbody>
            </table>
        </div>`;

                return summaryHTML;
            }

            // ------------------------------------------------------------------
            // Build Wings Table – group best call and put per stock; show Premium 1, Premium 2, and Total Premium.
            // ------------------------------------------------------------------
            function buildWingsTable() {
                let wingsData = {};
                globalSummary.forEach(pair => {
                    if (!wingsData[pair.stock]) {
                        wingsData[pair.stock] = { CE: null, PE: null };
                    }
                    if (pair.type === "CE") {
                        if (!wingsData[pair.stock].CE || pair.premiumCollected > wingsData[pair.stock].CE.premiumCollected) {
                            wingsData[pair.stock].CE = pair;
                        }
                    } else if (pair.type === "PE") {
                        if (!wingsData[pair.stock].PE || pair.premiumCollected > wingsData[pair.stock].PE.premiumCollected) {
                            wingsData[pair.stock].PE = pair;
                        }
                    }
                });
                let wingsRows = [];
                for (let stock in wingsData) {
                    let callPair = wingsData[stock].CE;
                    let putPair = wingsData[stock].PE;
                    let premium1 = callPair ? callPair.premiumCollected : 0;
                    let premium2 = putPair ? putPair.premiumCollected : 0;
                    let totalPremium = premium1 + premium2;
                    wingsRows.push({ stock, callPair, putPair, premium1, premium2, totalPremium });
                }
                wingsRows.sort((a, b) => b.totalPremium - a.totalPremium);
                let wingsHTML = `
                <div class=" mt-3 overflow-x-auto bg-white">
                    <h2 class="text-xl font-semibold mb-2">Wings Table</h2>
                    <table class="w-full border-collapse border dark:border-neutral-700" id="wings-table">
                        <thead class="bg-gray-50 dark:bg-neutral-700">
                            <tr>
                               <th class="border p-2 text-nowrap" data-col="stock">Stock</th>
                               <th class="border p-2 text-nowrap" data-col="callWing">Call Wing (Strike1/Strike2)</th>
                               <th class="border p-2 text-nowrap" data-col="putWing">Put Wing (Strike1/Strike2)</th>
                               <th class="border p-2 text-nowrap" data-col="premium1">Premium 1</th>
                               <th class="border p-2 text-nowrap" data-col="premium2">Premium 2</th>
                               <th class="border p-2 text-nowrap" data-col="totalPremium">Total Premium</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                wingsRows.forEach(row => {
                    let callWing = row.callPair
                        ? `${row.callPair.strike1} <span class="detail-info">(LTP: ${row.callPair.ltp1.toFixed(2)}, Vol: ${row.callPair.vol1}, Δ: ${row.callPair.delta1.toFixed(2)})</span> / ${row.callPair.strike2} <span class="detail-info">(LTP: ${row.callPair.ltp2.toFixed(2)}, Vol: ${row.callPair.vol2}, Δ: ${row.callPair.delta2.toFixed(2)})</span>`
                        : "N/A";
                    let putWing = row.putPair
                        ? `${row.putPair.strike1} <span class="detail-info">(LTP: ${row.putPair.ltp1.toFixed(2)}, Vol: ${row.putPair.vol1}, Δ: ${row.putPair.delta1.toFixed(2)})</span> / ${row.putPair.strike2} <span class="detail-info">(LTP: ${row.putPair.ltp2.toFixed(2)}, Vol: ${row.putPair.vol2}, Δ: ${row.putPair.delta2.toFixed(2)})</span>`
                        : "N/A";
                    wingsHTML += `
            <tr class="hover:bg-gray-50">
              <td class="border p-1 text-center">${row.stock}</td>
              <td class="border p-1 text-center">${callWing}</td>
              <td class="border p-1 text-center">${putWing}</td>
              <td class="border p-1 text-center">${row.premium1.toFixed(2)}</td>
              <td class="border p-1 text-center">${row.premium2.toFixed(2)}</td>
              <td class="border p-1 text-center">${row.totalPremium.toFixed(2)}</td>
            </tr>
          `;
                });
                wingsHTML += `
                </tbody>
            </table>
        </div>`;

                return wingsHTML;
            }

            // ------------------------------------------------------------------
            // Scan Selected Stocks and Build Expiry Tabs, Summary & Wings Tables
            // ------------------------------------------------------------------
            scanButton.addEventListener('click', async () => {
                for (let key in scannedData) { delete scannedData[key]; }
                globalSummary = [];
                scannedTickers = [];
                dataContainer.innerHTML = "";
                // Hide filter section at start of new scan
                document.getElementById('filter-section').classList.add('hidden');
                const selected = Array.from(document.querySelectorAll('.ticker-checkbox')).filter(cb => cb.checked).map(cb => cb.value);
                if (selected.length === 0) {
                    showAlert("Please select at least one ticker.");
                    return;
                }
                tableContainer.innerHTML = '';
                // Build both tables automatically after scanning stocks

                scannedTickers = selected;
                Promise.all(scannedTickers.map(ticker => processTicker(ticker)))
                    .then(() => {
                        tableContainer.innerHTML = '';
                        const firstTicker = scannedTickers[0];
                        const expiries = scannedData[firstTicker].expiryDates;
                        if (expiries && expiries.length > 0) {
                            buildExpiryTabs(expiries);
                            selectedExpiry = expiries[0];
                            scannedTickers.forEach(ticker => renderDeltaPairs(ticker, selectedExpiry));

                            // Build tables and update dataContainer with all HTML at once
                            dataContainer.innerHTML = buildSummaryTable() + buildWingsTable();

                            // Add sorting after tables are in the DOM
                            addSorting(document.getElementById('summary-table'));
                            addSorting(document.getElementById('wings-table'));

                            document.getElementById('filter-section').classList.remove('hidden');
                            document.getElementById('dataContainer').classList.remove('hidden');
                        } else {
                            expiryTabsContainer.innerHTML = `<p class="text-red-500">No expiry data available for ${firstTicker}.</p>`;
                            // Hide filter section if no expiry data
                            document.getElementById('filter-section').classList.add('hidden');
                        }
                    })
                    .catch(err => {
                        console.error("Error processing tickers", err);
                        // Hide filter section on error
                        document.getElementById('filter-section').classList.add('hidden');
                    });
            });

            // Listen for the custom event from the checkbox template
            document.addEventListener('ltp-preference-changed', function (event) {
                // Only respond to events from our specific checkbox or if no source is specified
                if (event.detail.source && event.detail.source !== 'strike-scanner-use-bid-ask') return;

                // Update your UI or re-calculate values based on the new setting
                if (scannedTickers.length > 0) {
                    globalSummary = [];
                    scannedTickers.forEach(ticker => renderDeltaPairs(ticker, selectedExpiry));

                    // Update dataContainer with all HTML at once
                    dataContainer.innerHTML = buildSummaryTable() + buildWingsTable();

                    // Add sorting after tables are in the DOM
                    addSorting(document.getElementById('summary-table'));
                    addSorting(document.getElementById('wings-table'));
                }
            });

            function addSorting(tableEl) {
                if (!tableEl) return;

                const thList = tableEl.querySelectorAll("thead th[data-col]");
                thList.forEach((th) => {
                    th.addEventListener("click", function () {
                        const colName = this.dataset.col;
                        let sortOrder = this.getAttribute("data-sort") || "desc";
                        sortOrder = sortOrder === "desc" ? "asc" : "desc";
                        this.setAttribute("data-sort", sortOrder);

                        const tbody = tableEl.querySelector("tbody");
                        const rows = Array.from(tbody.querySelectorAll("tr"));

                        rows.sort((a, b) => {
                            const aValue = parseFloat(
                                a.children[Array.from(thList).indexOf(this)].innerText.replace(
                                    /[^0-9.-]+/g,
                                    ""
                                )
                            );
                            const bValue = parseFloat(
                                b.children[Array.from(thList).indexOf(this)].innerText.replace(
                                    /[^0-9.-]+/g,
                                    ""
                                )
                            );
                            return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
                        });

                        tbody.innerHTML = "";
                        rows.forEach((row) => tbody.appendChild(row));
                    });
                });
            }


            // Modal handling
            const summaryModal = document.getElementById('summaryModal');
            const wingsModal = document.getElementById('wingsModal');
            const viewSummaryBtn = document.getElementById('view-summary-btn');
            const viewWingsBtn = document.getElementById('view-wings-btn');
            const closeSummaryModal = document.getElementById('closeSummaryModal');
            const closeWingsModal = document.getElementById('closeWingsModal');

            // Close modals when clicking outside
            window.addEventListener('click', (e) => {
                if (e.target === summaryModal) {
                    summaryModal.classList.add('hidden');
                }
                if (e.target === wingsModal) {
                    wingsModal.classList.add('hidden');
                }
            });
        });
    </script>
</body>

</html>