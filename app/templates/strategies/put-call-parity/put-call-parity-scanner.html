{% include 'blocks/timestamp.html' %}
{% include 'blocks/lot-sizes.html' %}

<main class="h-full">
    <div>
        <div>
            <div class="w-full">
                <!-- Stock Category Selector -->
                <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-3">
                    <div>
                        <div class="flex flex-wrap gap-4">
                            <label class="flex items-center">
                                <input type="radio" name="stock-category" value="index" checked class="mr-2" />
                                Indices
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="stock-category" value="nifty" class="mr-2" />
                                Nifty Stocks
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="stock-category" value="fo" class="mr-2" />
                                F&O Stocks
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Ticker Selection Section -->
                <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 flex flex-col h-[350px] lg:max-h-[calc(60vh)]">
                    <div class="flex justify-between items-center mb-3">
                        <h2 class="font-semibold">Select Stocks</h2>
                        <div class="flex gap-2 items-center">
                            <button id="select-all" class="text-xs hover:text-blue-500 text-neutral-700 font-medium">
                                Select All
                            </button>
                            <span class="text-gray-400 text-xs">|</span>
                            <button id="unselect-all" class="text-xs hover:text-red-500 text-neutral-700 font-medium">
                                Unselect All
                            </button>
                        </div>
                    </div>
                    <div id="ticker-selection" class="mb-4 flex-1 overflow-y-auto custom-scroll"></div>
                    <div class="pt-4 border-t mt-auto">
                        <button id="scan-button"
                            class="bg-blue-500 block focus:outline-0 font-medium hover:bg-blue-600 px-6 py-3 rounded-md text-white transition-colors w-full">
                            Scan Selected
                        </button>
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <!-- Filter Section -->
                <div id="results-filter-section" class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4 hidden">
                    <div class="flex md:flex-row flex-col mb-3 justify-between">
                        <h3 class="font-semibold">Filter Results</h3>
                        {% with id='put-call-parity-scanner-use-bid-ask' %}
                        {% include 'blocks/use-ltp-checkbox.html' %}
                        {% endwith %}
                    </div>

                    <div class="gap-6 grid items-center mb-4 md:grid-cols-3">
                        <!-- Stock Search Filter -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Search Stocks
                            </label>
                            <input id="stock-filter" type="text" placeholder="Filter stocks..."
                                class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 trasition-all" />
                        </div>

                        <!-- Minimum Parity Filter -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Minimum Parity % (<span id="min-parity-value">1</span>%)
                            </label>
                            <div id="min-parity-slider"></div>
                        </div>
                        <!-- Volume Filter -->
                        <div class="ml-4">
                            <label class="block font-medium mb-2 text-sm">
                                Minimum Volume (<span id="min-volume-value">20</span>)
                            </label>
                            <div id="min-volume-slider"></div>
                        </div>
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Call Bid Qty (&gt;<span id="call-bid-value">20</span>)
                            </label>
                            <div id="call-bid-min-value"></div>
                        </div>
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Call Ask Qty (&gt;<span id="call-ask-value">20</span>)
                            </label>
                            <div id="call-ask-min-value"></div>
                        </div>
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Call OI (&gt;<span id="call-oi-value">20</span>)
                            </label>
                            <div id="call-oi-min-value"></div>
                        </div>
                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Put Bid Qty (&gt;<span id="put-bid-value">20</span>)
                            </label>
                            <div id="put-bid-min-value"></div>
                        </div>

                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Put Ask Qty (&gt;<span id="put-ask-value">20</span>)
                            </label>
                            <div id="put-ask-min-value"></div>
                        </div>

                        <div>
                            <label class="block font-medium mb-2 text-sm">
                                Put OI (&gt;<span id="put-oi-value">20</span>)
                            </label>
                            <div id="put-oi-min-value"></div>
                        </div>
                    </div>


                </div>

                <!-- Expiry Tabs -->
                <div id="put-call-parity-scanner-expiry-tabs"
                    class="border flex gap-2 mb-4 overflow-x-auto p-1 rounded-md bg-white text-nowrap">
                </div>


                <!-- Scanner Results -->
                <div id="scanner-results" class="max-h-[calc(100vh_-_368px)] custom-scroll overflow-y-auto"></div>
            </div>
        </div>
    </div>
</main>

{%include 'blocks/stock-symbols.html' %}

<script>
    // Global variables
    let currentTickers = indexTickers;
    let currentCategory = "index";
    const scannedData = {};
    let selectedExpiry = null;
    let scannedTickers = [];
    let processedCount = 0;
    let scanComplete = false;

    // DOM elements
    const tickerSelectionContainer = document.getElementById("ticker-selection");
    const selectAllBtn = document.getElementById("select-all");
    const unselectAllBtn = document.getElementById("unselect-all");
    const scanButton = document.getElementById("scan-button");
    const scannerResults = document.getElementById("scanner-results");
    const expiryTabsContainer = document.getElementById("put-call-parity-scanner-expiry-tabs");
    const stockCategoryRadios = document.getElementsByName("stock-category");
    const stockFilter = document.getElementById("stock-filter");

    // Add this after the existing DOM elements declarations
    stockFilter.addEventListener('keyup', filterResults);

    // Build ticker selection checkboxes
    function buildTickerSelection(tickerArray) {
        let html = '<div class="border border-l-0 divide-x divide-y grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 md:grid-cols-4">';
        tickerArray.forEach((ticker) => {
            html += `
        <label class="flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-neutral-700 first:border-l text-sm">
          <input type="checkbox" value="${ticker}" class="ticker-checkbox" checked>
          <span>${ticker}</span>
        </label>
      `;
        });
        html += "</div>";
        tickerSelectionContainer.innerHTML = html;
    }

    // Initial build
    buildTickerSelection(currentTickers);

    // Stock category radio change handler
    stockCategoryRadios.forEach((radio) => {
        radio.addEventListener("change", function () {
            currentCategory = this.value;
            if (currentCategory === "nifty") {
                currentTickers = niftyTickers;
            } else if (currentCategory === "fo") {
                currentTickers = foTickers;
            } else if (currentCategory === "index") {
                currentTickers = indexTickers;
            }
            buildTickerSelection(currentTickers);
        });
    });

    // Select/Unselect all handlers
    selectAllBtn.addEventListener("click", () => {
        document.querySelectorAll(".ticker-checkbox").forEach((cb) => (cb.checked = true));
    });

    unselectAllBtn.addEventListener("click", () => {
        document.querySelectorAll(".ticker-checkbox").forEach((cb) => (cb.checked = false));
    });

    // Main scan button handler
    scanButton.addEventListener("click", () => {
        scanComplete = false;
        // Clear previous data
        scannerResults.innerHTML = '';
        expiryTabsContainer.innerHTML = '';
        Object.keys(scannedData).forEach(key => delete scannedData[key]);
        scannedTickers = [];
        selectedExpiry = null;
        processedCount = 0;

        // Get selected tickers
        const selected = Array.from(document.querySelectorAll('.ticker-checkbox:checked')).map(cb => cb.value);
        if (selected.length === 0) {
            showAlert('Please select at least one ticker.');
            return;
        }
        scannedTickers = selected;

        // Process all tickers
        Promise.all(scannedTickers.map(processTicker))
            .then(() => {
                const firstTicker = scannedTickers[0];
                if (scannedData[firstTicker]?.expiryDates?.length > 0) {
                    buildExpiryTabs(scannedData[firstTicker].expiryDates);
                    selectedExpiry = scannedData[firstTicker].expiryDates[0];
                    document.getElementById('results-filter-section').classList.remove('hidden');
                    scanComplete = true; // Set scan complete
                    filterResults(); // Initial filter after scan
                } else {
                    expiryTabsContainer.innerHTML = `
                        <p class="text-red-500">No expiry data available for ${firstTicker}</p>
                    `;
                }
            })
            .catch(err => {
                console.error('Error processing tickers:', err);
                scannerResults.innerHTML = `
                    <p class="text-red-500 text-sm">Error processing tickers. Please try again.</p>
                `;
            });
    });

    async function processTicker(ticker) {
        let tickerContainer = document.getElementById('ticker-' + ticker);
        if (!tickerContainer) {
            tickerContainer = document.createElement('div');
            tickerContainer.id = 'ticker-' + ticker;
            tickerContainer.className = 'mb-3 p-4 rounded-lg bg-white dark:bg-neutral-800 dark:border-neutral-700';
            tickerContainer.innerHTML = `
            <h2 class="text-base font-medium mb-2">${ticker} - Scanning...</h2>
            <div id="ticker-content-${ticker}"></div>
        `;
            scannerResults.appendChild(tickerContainer);
        }

        const contentContainer = document.getElementById('ticker-content-' + ticker);

        try {
            // Fetch options chain data
            const optionsUrl = (currentCategory === "index") ?
                `https://iam.theaibull.com/v1/wg7ttpouv7/indices/options/${ticker}` :
                `https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${ticker}`;
            const optionsResponse = await fetch(optionsUrl);

            const text = await optionsResponse.text(); // Get response as text first
            // Replace NaN with null in the response text
            const sanitizedText = text.replace(/:\s*NaN/g, ': null');
            const optionsData = JSON.parse(sanitizedText);
            const records = optionsData.records;

            // Compute underlying value from option data (iterate until one is found)
            let underlyingValue = null;
            for (const record of (records.data || [])) {
                if (record.CE && record.CE.underlyingValue) {
                    underlyingValue = record.CE.underlyingValue;
                    break;
                }
                if (record.PE && record.PE.underlyingValue) {
                    underlyingValue = record.PE.underlyingValue;
                    break;
                }
            }

            // Fetch futures data
            const futures = await fetchFutures(ticker) || [];

            // Store processed data
            scannedData[ticker] = {
                expiryDates: records.expiryDates || [],
                optionData: records.data || [],
                futurePrices: futures
            };

            // Update ticker header with the computed underlying value (if available)
            tickerContainer.querySelector('h2').innerHTML = `
            ${ticker} - Price: ${underlyingValue ? underlyingValue.toFixed(2) : 'N/A'}
        `;

            if (selectedExpiry) {
                renderPCPData(ticker, selectedExpiry);
            }
        } catch (err) {
            console.error(`Error processing ${ticker}:`, err);
            contentContainer.innerHTML = `
            <p class="text-red-500">Failed to fetch data for ${ticker}</p>
        `;
        }
    }
    // Fetch futures data
    async function fetchFutures(ticker) {
        try {
            const response = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/futures/${ticker}`);
            const data = await response.json();
            return data;
        } catch (error) {
            console.error("Error fetching futures for " + ticker, error);
            return null;
        }
    }

    // Build expiry tabs
    function buildExpiryTabs(expiryDates) {
        const tabsHtml = expiryDates.map((date, idx) => `
            <button
                class="tab-btn px-4 py-1.5 rounded-sm text-xs font-medium
                       ${idx === 0 ? 'bg-blue-500 text-white' : 'bg-white dark:bg-neutral-800 dark:hover:bg-neutral-700'}"
                data-expiry="${date}"
            >
                ${date}
            </button>
        `).join('');

        expiryTabsContainer.innerHTML = tabsHtml;

        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                document.querySelectorAll('.tab-btn').forEach(b => {
                    b.classList.remove('bg-blue-500', 'text-white');
                    b.classList.add('bg-white', 'dark:bg-neutral-800');
                });
                this.classList.remove('bg-white', 'dark:bg-neutral-800');
                this.classList.add('bg-blue-500', 'text-white');

                selectedExpiry = this.dataset.expiry;
                filterResults(); // Call filterResults instead of directly rendering
            });
        });
    }

    // Add this event listener after other DOM element declarations
    document.addEventListener('ltp-preference-changed', () => {
        // Only respond to events from our specific checkbox or if no source is specified
        if (event.detail.source && event.detail.source !== 'put-call-parity-scanner-use-bid-ask') return;

        if (scanComplete && selectedExpiry) {
            filterResults();
        }
    });

    // Render PCP data for one ticker
    function renderPCPData(ticker, expiry) {
        const tickerData = scannedData[ticker];
        if (!tickerData) return;

        // Create or get ticker container
        let tickerContainer = document.getElementById('ticker-' + ticker);
        if (!tickerContainer) {
            tickerContainer = document.createElement('div');
            tickerContainer.id = 'ticker-' + ticker;
            tickerContainer.className = 'mb-3 p-4 rounded-lg bg-white dark:bg-neutral-800 dark:border-neutral-700';
            tickerContainer.innerHTML = `
                <div id="ticker-content-${ticker}"></div>
            `;
            scannerResults.appendChild(tickerContainer);
        }

        const contentContainer = document.getElementById('ticker-content-' + ticker);
        if (!contentContainer) return;

        const allOptions = tickerData.optionData;
        let filtered = allOptions.filter(item => item.expiryDate === expiry);
        filtered = filtered.filter(item => item.strikePrice && item.strikePrice != 0);
        filtered = filtered.filter(item => item.CE && item.PE);

        // Use Bid/Ask or LTP based on checkbox
        const useBidAsk = !window.useLTPForPrices;

        filtered = filtered.filter(item => {
            // Always filter using LTP for existence check
            return item.CE.lastPrice != null && item.CE.lastPrice > 0 &&
                item.PE.lastPrice != null && item.PE.lastPrice > 0;
        });

        // Filter based on minimum parity
        const minParity = parseFloat(document.getElementById('min-parity-slider').noUiSlider.get());
        const minVolume = parseInt(document.getElementById('min-volume-slider').noUiSlider.get());
        const callBidQtyValue = parseInt(document.getElementById('call-bid-min-value').noUiSlider.get());
        const callAskQtyValue = parseInt(document.getElementById('call-ask-min-value').noUiSlider.get());
        const callOIQtyValue = parseInt(document.getElementById('call-oi-min-value').noUiSlider.get());
        const putBidQtyValue = parseInt(document.getElementById('put-bid-min-value').noUiSlider.get());
        const putAskQtyValue = parseInt(document.getElementById('put-ask-min-value').noUiSlider.get());
        const putOIQtyValue = parseInt(document.getElementById('put-oi-min-value').noUiSlider.get());

        // Add volume filter here instead
        filtered = filtered.filter(item => {
            let callVol = parseFloat(item.CE.totalTradedVolume);
            let putVol = parseFloat(item.PE.totalTradedVolume);
            return !isNaN(callVol) && !isNaN(putVol) &&
                callVol >= minVolume && putVol >= minVolume &&
                item.CE?.bidQty >= callBidQtyValue &&
                item.CE?.askQty >= callAskQtyValue &&
                item.CE?.openInterest >= callOIQtyValue &&
                item.PE?.bidQty >= putBidQtyValue &&
                item.PE?.askQty >= putAskQtyValue &&
                item.PE?.openInterest >= putOIQtyValue;
        });

        const futures = tickerData.futurePrices;

        if (!futures || !futures[expiry]) {
            tickerContainer.classList.add('hidden');
            return;
        }

        const futurePrice = futures[expiry];

        // Apply both volume and parity filters
        filtered = filtered.filter(item => {
            let callPrice = useBidAsk ? (item.CE.bidprice || item.CE.askPrice) : item.CE.lastPrice;
            let putPrice = useBidAsk ? (item.PE.bidprice || item.PE.askPrice) : item.PE.lastPrice;
            let strike = item.strikePrice;
            const computedDiff = callPrice - putPrice;
            const expectedDiff = futurePrice - strike;
            const parityPct = ((computedDiff - expectedDiff) / futurePrice * 100).toFixed(2);

            return parityPct >= minParity;
        });

        // Show/hide panel based on results
        if (filtered.length > 0) {
            tickerContainer.classList.remove('hidden');
        } else {
            tickerContainer.classList.add('hidden');
            return;
        }

        // Build the PCP table
        contentContainer.innerHTML = buildPCPTable(ticker, expiry, futurePrice, filtered, useBidAsk);
    }

    // Helper function to convert PCP data to legs format
    function convertPCPToLegs(data, expiry, futurePrice, useBidAsk) {
        const ce = data.CE || {};
        const pe = data.PE || {};
        const strike = data.strikePrice;

        // Determine prices based on arbitrage direction and useBidAsk
        let callPrice, putPrice, callAction, putAction;
        if (useBidAsk) {
            const callOverpriced = (ce.bidprice - pe.askPrice) > (futurePrice - strike);
            callPrice = callOverpriced ? ce.bidprice : ce.askPrice;
            putPrice = callOverpriced ? pe.askPrice : pe.bidprice;
            callAction = callOverpriced ? 'sell' : 'buy';
            putAction = callOverpriced ? 'buy' : 'sell';
        } else {
            callPrice = ce.lastPrice || 0;
            putPrice = pe.lastPrice || 0;
            // Default arbitrage: sell call, buy put if parity > 0
            const parity = (ce.lastPrice - pe.lastPrice) - (futurePrice - strike);
            callAction = parity > 0 ? 'sell' : 'buy';
            putAction = parity > 0 ? 'buy' : 'sell';
        }

        return [
            {
                type: 'CE',
                action: callAction,
                strike: strike,
                price: callPrice || 0,
                expiryDate: expiry,
                lots: 1
            },
            {
                type: 'PE',
                action: putAction,
                strike: strike,
                price: putPrice || 0,
                expiryDate: expiry,
                lots: 1
            },
            {
                type: 'FUT',
                action: putAction,
                strike: strike,
                price: futurePrice,
                expiryDate: expiry,
                lots: 1
            }
        ];
    }

    // Modify the buildPCPTable function to accept useBidAsk parameter
    function buildPCPTable(ticker, expiry, futurePrice, filtered, useBidAsk) {
        const tableId = `pcp-table-${ticker}`;
        const html = `
            <div class="mb-2 text-gray-600">
                <div class="flex flex-col md:flex-row gap-3 w-full mb-2 md:items-center items-start">
               <h3 class="font-medium text-base text-neutral-900"> ${ticker}</h3>
                <span class="bg-purple-50 text-sm text-purple-800 text-neutral-900 font-medium px-3 py-1 rounded text-center">Future Price: ${futurePrice.toFixed(2)}</span>
                </div>
            </div>
            <div class="overflow-x-auto custom-scroll">
                <table class="w-full border-collapse border dark:border-neutral-700 xl:text-sm text-xs" id="${tableId}">
                    <thead>
                        <tr class="bg-gray-100 dark:bg-neutral-700">
                            <th class="border p-2" rowspan="2" data-col="futurePrice">Future Price</th>
                            <th class="border p-2" rowspan="2" data-col="strike">Strike Price</th>
                            <th class="border p-2 text-center bg-blue-50 dark:bg-blue-900/20" colspan="7">Call Option</th>
                            <th class="border p-2 text-center bg-red-50 dark:bg-red-900/20" colspan="7">Put Option</th>
                            <th class="border p-2" rowspan="2" data-col="parity">Parity</th>
                            <th class="border p-2" rowspan="2" data-col="parityPct">Parity %</th>
                            <th class="border p-2" rowspan="2" data-no-sort>Preview</th>
                        </tr>
                        <tr class="bg-gray-100 dark:bg-neutral-700">
                            <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="call">LTP</th>
                            <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="callBid">Bid</th>
                            <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="callBidQty">Bid Qty</th>
                            <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="callAsk">Ask</th>
                            <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="callAskQty">Ask Qty</th>
                            <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="callVolume">Volume</th>
                            <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="callOI">OI</th>
                            <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="put">LTP</th>
                            <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="putBid">Bid</th>
                            <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="putBidQty">Bid Qty</th>
                            <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="putAsk">Ask</th>
                            <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="putAskQty">Ask Qty</th>
                            <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="putVolume">Volume</th>
                            <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="putOI">OI</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filtered.map(opt => {
            const strike = opt.strikePrice;
            const ce = opt.CE || {};
            const pe = opt.PE || {};

            // Call option data
            const callDisplayPrice = ce.lastPrice || 0;
            const callBid = ce.bidprice || 0;
            const callBidQty = ce.bidQty || 0;
            const callAsk = ce.askPrice || 0;
            const callAskQty = ce.askQty || 0;
            const callVol = ce.totalTradedVolume || 0;
            const callOI = ce.openInterest || 0;

            const putDisplayPrice = pe.lastPrice || 0;
            const putBid = pe.bidprice || 0;
            const putBidQty = pe.bidQty || 0;
            const putAsk = pe.askPrice || 0;
            const putAskQty = pe.askQty || 0;
            const putVol = pe.totalTradedVolume || 0;
            const putOI = pe.openInterest || 0;

            // Use Bid/Ask or LTP for parity calculation based on checkbox
            const callCalcPrice = useBidAsk ? (ce.bidprice || ce.askPrice || 0) : (ce.lastPrice || 0);
            const putCalcPrice = useBidAsk ? (pe.bidprice || pe.askPrice || 0) : (pe.lastPrice || 0);

            // Calculate parity using the calculation prices
            const computedDiff = callCalcPrice - putCalcPrice;
            const expectedDiff = futurePrice - strike;
            const parity = (computedDiff - expectedDiff).toFixed(2);
            const parityPct = ((computedDiff - expectedDiff) / futurePrice * 100).toFixed(2);

            return `
                                <tr class="hover:bg-gray-50 dark:hover:bg-neutral-700">
                                    <td class="border p-2 text-center">${futurePrice.toFixed(2)}</td>
                                    <td class="border p-2 text-center">${strike}</td>

                                    <!-- Call Option Data -->
                                    <td class="border p-2 text-center">${callDisplayPrice.toFixed(2)}</td>
                                    <td class="border p-2 text-center">${callBid.toFixed(2)}</td>
                                    <td class="border p-2 text-center">${callBidQty}</td>
                                    <td class="border p-2 text-center">${callAsk.toFixed(2)}</td>
                                    <td class="border p-2 text-center">${callAskQty}</td>
                                    <td class="border p-2 text-center">${callVol}</td>
                                    <td class="border p-2 text-center">${callOI}</td>

                                    <!-- Put Option Data -->
                                    <td class="border p-2 text-center">${putDisplayPrice.toFixed(2)}</td>
                                    <td class="border p-2 text-center">${putBid.toFixed(2)}</td>
                                    <td class="border p-2 text-center">${putBidQty}</td>
                                    <td class="border p-2 text-center">${putAsk.toFixed(2)}</td>
                                    <td class="border p-2 text-center">${putAskQty}</td>
                                    <td class="border p-2 text-center">${putVol}</td>
                                    <td class="border p-2 text-center">${putOI}</td>

                                    <td class="border p-2 text-center">${parity}</td>
                                    <td class="border p-2 text-center">${parityPct}%</td>
                                    <td class="border p-1 text-center">
                                        <button
                                            onclick='window.PreviewModal.show({
                                                legs: ${JSON.stringify(convertPCPToLegs(opt, expiry, futurePrice, useBidAsk))},
                                                symbol: "${ticker}",
                                                spotPrice: ${futurePrice},
                                                optionChain: ${JSON.stringify(scannedData[ticker].optionData)},
                                                expiryDates: ${JSON.stringify(scannedData[ticker].expiryDates)},
                                                allowedTypes: ["CE", "PE", "FUT"]
                                            })'
                                            title="Preview Strategy"
                                            class="p-1 hover:bg-gray-100 rounded">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                 viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                 stroke-linecap="round" stroke-linejoin="round"
                                                 class="lucide lucide-chart-candlestick text-gray-500">
                                                <path d="M9 5v4"></path>
                                                <rect width="4" height="6" x="7" y="9" rx="1"></rect>
                                                <path d="M9 15v2"></path>
                                                <path d="M17 3v2"></path>
                                                <rect width="4" height="8" x="15" y="5" rx="1"></rect>
                                                <path d="M17 13v3"></path>
                                                <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
                                            </svg>
                                        </button>
                                    </td>
                                </tr>
                            `;
        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
        // Call the shared sorting function after table is generated
        setTimeout(() => window.addSorting(tableId), 0);
        return html;
    }

    // Initialize sliders after DOM content is loaded
    document.addEventListener('DOMContentLoaded', () => {
        // Initialize Min Parity Slider
        const minParitySlider = document.getElementById('min-parity-slider');
        noUiSlider.create(minParitySlider, {
            start: [1],
            connect: [true, false],
            range: {
                'min': -5,
                'max': 5
            },
            step: 0.05,
            format: {
                to: value => value.toFixed(2),
                from: value => parseFloat(value)
            }
        });

        // Initialize Min Volume Slider
        const minVolumeSlider = document.getElementById('min-volume-slider');
        noUiSlider.create(minVolumeSlider, {
            start: [5],
            connect: [true, false],
            range: {
                'min': 0,
                'max': 1000
            },
            step: 5,
            format: {
                to: value => Math.round(value),
                from: value => parseInt(value)
            }
        });

        const callBidQtySlider = document.getElementById('call-bid-min-value');
        noUiSlider.create(callBidQtySlider, {
            start: [0],
            connect: [true, false],
            range: { 'min': 0, 'max': 100 },
            step: 1,
            format: {
                to: value => Math.round(value),
                from: value => parseInt(value)
            }
        });

        const callAskQtySlider = document.getElementById('call-ask-min-value');
        noUiSlider.create(callAskQtySlider, {
            start: [0],
            connect: [true, false],
            range: { 'min': 0, 'max': 100 },
            step: 1,
            format: {
                to: value => Math.round(value),
                from: value => parseInt(value)
            }
        });

        const callOIQtySlider = document.getElementById('call-oi-min-value');
        noUiSlider.create(callOIQtySlider, {
            start: [0],
            connect: [true, false],
            range: { 'min': 0, 'max': 100 },
            step: 1,
            format: {
                to: value => Math.round(value),
                from: value => parseInt(value)
            }
        });


        const putBidQtySlider = document.getElementById('put-bid-min-value');
        noUiSlider.create(putBidQtySlider, {
            start: [0],
            connect: [true, false],
            range: { 'min': 0, 'max': 100 },
            step: 1,
            format: {
                to: value => Math.round(value),
                from: value => parseInt(value)
            }
        });

        const putAskQtySlider = document.getElementById('put-ask-min-value');
        noUiSlider.create(putAskQtySlider, {
            start: [0],
            connect: [true, false],
            range: { 'min': 0, 'max': 100 },
            step: 1,
            format: {
                to: value => Math.round(value),
                from: value => parseInt(value)
            }
        });

        const putOIQtySlider = document.getElementById('put-oi-min-value');
        noUiSlider.create(putOIQtySlider, {
            start: [0],
            connect: [true, false],
            range: { 'min': 0, 'max': 100 },
            step: 1,
            format: {
                to: value => Math.round(value),
                from: value => parseInt(value)
            }
        });

        // Update values display and trigger filter
        minParitySlider.noUiSlider.on('update', (values) => {
            document.getElementById('min-parity-value').textContent = values[0];
        });

        minParitySlider.noUiSlider.on('change', (values) => {
            if (scanComplete) filterResults();
        });

        minVolumeSlider.noUiSlider.on('update', (values) => {
            document.getElementById('min-volume-value').textContent = values[0];
        });

        minVolumeSlider.noUiSlider.on('change', (values) => {
            if (scanComplete) filterResults();
        });

        callBidQtySlider.noUiSlider.on('update', (values) => {
            document.getElementById('call-bid-value').textContent = values[0];
        });

        callBidQtySlider.noUiSlider.on('change', (values) => {
            if (scanComplete) filterResults();
        });

        callAskQtySlider.noUiSlider.on('update', (values) => {
            document.getElementById('call-ask-value').textContent = values[0];
        });

        callAskQtySlider.noUiSlider.on('change', (values) => {
            if (scanComplete) filterResults();
        });

        callOIQtySlider.noUiSlider.on('update', (values) => {
            document.getElementById('call-oi-value').textContent = values[0];
        });

        callOIQtySlider.noUiSlider.on('change', (values) => {
            if (scanComplete) filterResults();
        });

        putBidQtySlider.noUiSlider.on('update', (values) => {
            document.getElementById('put-bid-value').textContent = values[0];
        });

        putBidQtySlider.noUiSlider.on('change', (values) => {
            if (scanComplete) filterResults();
        });

        putAskQtySlider.noUiSlider.on('update', (values) => {
            document.getElementById('put-ask-value').textContent = values[0];
        });

        putAskQtySlider.noUiSlider.on('change', (values) => {
            if (scanComplete) filterResults();
        });

        putOIQtySlider.noUiSlider.on('update', (values) => {
            document.getElementById('put-oi-value').textContent = values[0];
        });

        putOIQtySlider.noUiSlider.on('change', (values) => {
            if (scanComplete) filterResults();
        });
    });

    // Update the filterResults function to use slider values
    function filterResults() {
        if (!scanComplete || !selectedExpiry) return;

        // Get filter values from sliders
        const minParity = parseFloat(document.getElementById('min-parity-slider').noUiSlider.get());
        const minVolume = parseInt(document.getElementById('min-volume-slider').noUiSlider.get());
        const callBidQty = parseInt(document.getElementById('call-bid-min-value').noUiSlider.get());
        const callAskQty = parseInt(document.getElementById('call-ask-min-value').noUiSlider.get());
        const callOIQty = parseInt(document.getElementById('call-oi-min-value').noUiSlider.get());
        const putBidQty = parseInt(document.getElementById('put-bid-min-value').noUiSlider.get());
        const putAskQty = parseInt(document.getElementById('put-ask-min-value').noUiSlider.get());
        const putOIQty = parseInt(document.getElementById('put-oi-min-value').noUiSlider.get());
        const searchTerm = stockFilter.value.toLowerCase();

        // Clear all existing results first
        scannerResults.innerHTML = "";

        // Filter and render each ticker with new volume threshold
        scannedTickers.forEach((ticker) => {
            if (ticker.toLowerCase().includes(searchTerm)) {
                const tickerData = scannedData[ticker];
                if (!tickerData) return;

                let filtered = tickerData.optionData.filter(item => item.expiryDate === selectedExpiry);
                filtered = filtered.filter(item => {
                    // Basic filters
                    if (!item.strikePrice || !item.CE || !item.PE) return false;

                    // Volume filter
                    const callVol = parseFloat(item.CE.totalTradedVolume);
                    const putVol = parseFloat(item.PE.totalTradedVolume);
                    if (isNaN(callVol) || isNaN(putVol)) return false;
                    if (callVol < minVolume || putVol < minVolume) return false;

                    // Price and parity filters
                    const callPrice = item.CE.lastPrice;
                    const putPrice = item.PE.lastPrice;
                    if (!callPrice || !putPrice) return false;

                    const futures = tickerData.futurePrices;
                    if (!futures || !futures[selectedExpiry]) return false;

                    const futurePrice = futures[selectedExpiry];
                    const strike = item.strikePrice;
                    const computedDiff = callPrice - putPrice;
                    const expectedDiff = futurePrice - strike;
                    const parityPct = ((computedDiff - expectedDiff) / futurePrice * 100);

                    return parityPct >= minParity;
                });

                if (filtered.length > 0) {
                    renderPCPData(ticker, selectedExpiry);
                }
            }
        });

        // Check if we have any visible results
        // Look for elements that don't have the 'hidden' class
        const visibleResults = Array.from(scannerResults.children).filter(el =>
            !el.classList.contains('hidden') &&
            el.id.startsWith('ticker-')
        );

        if (visibleResults.length === 0) {
            // Show empty state
            scannerResults.innerHTML = `
                <div class="mb-3 p-4 rounded-lg bg-white dark:bg-neutral-800 dark:border-neutral-700">
                    <div class="max-w-md mx-auto text-center">
                        <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                            </path>
                        </svg>
                        <h3 class="text-lg font-semibold mb-2">No matching stocks found</h3>
                        <p class="text-gray-500 text-sm mb-4">
                            Current Filters:
                        </p>
                        <ul class="text-sm text-gray-500 space-y-1 mb-4">
                            ${searchTerm ? `<li>Search Term: "${searchTerm}"</li>` : ''}
                            <li>Minimum Parity: ${minParity}%</li>
                            <li>Minimum Volume: ${minVolume}</li>
                            <li>Selected Expiry: ${selectedExpiry}</li>
                        </ul>
                        <p class="text-sm text-gray-500">
                            Try adjusting your filters to see more results
                        </p>
                    </div>
                </div>
            `;
        }

        // Update results counter
        const counterEl = document.querySelector("#results-counter");
        if (counterEl) {
            counterEl.textContent = `Showing ${visibleResults.length} of ${scannedTickers.length} stocks`;
        }
    }
</script>