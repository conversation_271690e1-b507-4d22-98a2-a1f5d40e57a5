<!DOCTYPE html>
<html lang="en">

<head>
    <title>Put-Call Parity Scanner - Find Arbitrage Opportunities | AI Bull</title>
    <meta name="description"
        content="Discover arbitrage opportunities using put-call parity by identifying price discrepancies between put and call options. Maximize your trading profits by exploiting mispriced options in the market." />
    <link rel="canonical" href="https://theaibull.com/strategies/put-call-parity" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/strategies/put-call-parity" />
    <meta property="og:title" content="Put-Call Parity Scanner - Find Arbitrage Opportunities Using Put-Call Parity" />
    <meta property="og:description"
        content="Discover arbitrage opportunities using put-call parity by identifying price discrepancies between put and call options. Maximize your trading profits by exploiting mispriced options in the market." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title" content="Put-Call Parity Scanner - Find Arbitrage Opportunities Using Put-Call Parity" />
    <meta name="twitter:description"
        content="Find arbitrage opportunities using put-call parity by identifying pricing discrepancies between put and call options to optimize your options trading strategy." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {% include 'blocks/common.html' %}
    {%include 'blocks/info.html' %}

    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <main>
            <div class="p-3 mx-auto max-w-7xl w-full">

                <div
                    class="flex flex-col md:flex-row justify-between bg-white dark:bg-neutral-800 px-3 py-2  rounded-lg mb-3">
                    <div class="flex items-center">
                        <h1 class="text-xl font-semibold tracking-tight">
                            Put-Call Parity Scanner
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">
                            Find arbitrage opportunities using put-call parity
                        </p>
                        <!-- Button to trigger modal open -->
                        <button id="openModalBtn" onclick="openModal()" class="ml-2">

                            <div class="flex group text-sm">
                                <div
                                    class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                    <div
                                        class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                        <div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-circle-help">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                <path d="M12 17h.01"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div
                                        class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                        <div
                                            class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                            How to Use This Page</div>
                                    </div>
                                </div>
                            </div>
                        </button>
                    </div>
                    <div class="rounded-lg bg-slate-100 p-0.5 w-[300px] mt-2 md:mt-0">
                        <nav class="-mb-px flex" aria-label="Tabs">
                            <button id="search-tab"
                                class="tab-button w-1/2 py-2.5 px-1 text-center shadow-md font-medium text-sm text-blue-600 dark:text-blue-500 bg-white rounded-md"
                                aria-current="page">
                                Search Stock
                            </button>
                            <button id="all-stocks-tab"
                                class="tab-button w-1/2 py-2.5 px-1 text-center font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300">
                                Scan All Stocks
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Search Tab Content -->
                <div id="search-content" class="tab-content">
                    <div class="flex flex-col gap-3">
                        <!-- Left Column: Search and Most Active -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg p-4">
                            <!-- Stock Input Form -->
                            {% include 'blocks/search-active-recent-stocks.html' %}
                        </div>

                        <!-- Right Column: Results -->
                        <div id="right-section-pcp"
                            class="flex-1 bg-white dark:bg-neutral-800 rounded-lg p-4 xl:h-[calc(100vh_-_135px)] custom-scroll">
                            <div id="filters-container" class="bg-white dark:bg-neutral-800 rounded-lg hidden">
                                <div class="flex flex-col md:flex-row gap-3 md:items-center justify-between mb-2">
                                    <div
                                        class="flex flex-col md:flex-row md:items-center justify-between md:gap-2 gap-3">
                                        <!-- Price Info -->
                                        <div id="stock-price"></div>
                                        <!-- Future Price Display -->
                                        <div id="future-price"></div>
                                    </div>
                                    {% with id='put-call-parity-single-use-bid-ask' %}
                                    {% include 'blocks/use-ltp-checkbox.html' %}
                                    {% endwith %}
                                </div>

                                <div class="grid md:grid-cols-4 md:gap-12 gap-4 mb-4">
                                    <div>
                                        <label for="call-bid-qty" class="block font-medium mb-2 text-sm">
                                            Call Bid Qty (&gt;<span id="call-bid-filter-value">0</span>)
                                        </label>
                                        <div id="call-bid-qty" class="mt-6"></div>
                                        <div class="flex justify-between text-sm mt-1">
                                            <span id="call-bid-filter-min-value">0</span>
                                            <span id="call-bid-filter-max-value">1000</span>
                                        </div>
                                    </div>

                                    <div>
                                        <label for="call-ask-qty" class="block font-medium mb-2 text-sm">
                                            Call Ask Qty (&gt;<span id="call-ask-filter-value">0</span>)
                                        </label>
                                        <div id="call-ask-qty" class="mt-6"></div>
                                        <div class="flex justify-between text-sm mt-1">
                                            <span id="call-ask-filter-min-value">0</span>
                                            <span id="call-ask-filter-max-value">1000</span>
                                        </div>
                                    </div>

                                    <div>
                                        <label for="put-bid-qty" class="block font-medium mb-2 text-sm">
                                            Put Bid Qty (&gt;<span id="put-bid-filter-value">0</span>)
                                        </label>
                                        <div id="put-bid-qty" class="mt-6"></div>
                                        <div class="flex justify-between text-sm mt-1">
                                            <span id="put-bid-filter-min-value">0</span>
                                            <span id="put-bid-filter-max-value">1000</span>
                                        </div>
                                    </div>

                                    <div>
                                        <label for="put-ask-qty" class="block font-medium mb-2 text-sm">
                                            Put Ask Qty (&gt;<span id="put-ask-filter-value">0</span>)
                                        </label>
                                        <div id="put-ask-qty" class="mt-6"></div>
                                        <div class="flex justify-between text-sm mt-1">
                                            <span id="put-ask-filter-min-value">0</span>
                                            <span id="put-ask-filter-max-value">1000</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- Volume Filter -->
                                <div id="toggles-container" class="mb-4 flex items-center md:gap-4 gap-3 flex-wrap">
                                    <div class="flex items-center gap-1 cursor-pointer">
                                        <input type="checkbox" id="filterLowVolume" class="h-4 w-4 cursor-pointer"
                                            checked />
                                        <label for="filterLowVolume" class="text-sm font-medium cursor-pointer">Filter
                                            Low Volumes
                                            (&gt;20)</label>
                                    </div>
                                </div>

                                <!-- Expiry Tabs -->
                                <div id="expiry-tabs"
                                    class="border flex gap-0 mb-2 overflow-x-auto p-1 rounded-md text-nowrap">
                                </div>
                            </div>

                            <!-- Option Chain Table -->
                            <div id="option-chain-table"
                                class="border-dashed border-t custom-scroll overflow-x-auto pt-3 xl:max-h-[calc(100vh_-_249px)]">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- All Stocks Tab Content -->
                <div id="all-stocks-content" class="tab-content hidden">
                    {% include 'strategies/put-call-parity/put-call-parity-scanner.html' %}
                </div>

                <!-- FAQ Section in Accordion Format -->
                <div class="mt-8 text-sm">
                    <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
                    <div class="space-y-4">
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What is Put-Call Parity?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Put-Call Parity is a financial principle that shows the relationship between the
                                    prices of European put and call
                                    options. It allows traders to identify arbitrage opportunities when prices deviate
                                    from the expected parity.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How does the scanner help with arbitrage?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    The scanner analyzes discrepancies between the prices of put and call options to
                                    identify arbitrage opportunities. If
                                    the options are mispriced based on put-call parity, you can exploit the price
                                    differences for profit.

                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How do I use the scanner?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Enter a stock symbol or select an index, then click "Search" or "Scan All Stocks."
                                    The scanner will display potential
                                    opportunities based on put-call parity, showing where price differences exist
                                    between the put and call options.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                Can I use the scanner for any stock or index?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Yes, you can search for both individual stocks and popular indices like Nifty or
                                    Banknifty using the scanner to identify
                                    arbitrage opportunities across various market assets.


                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What are the risks of trading using arbitrage opportunities?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    While arbitrage opportunities are typically risk-free in theory, execution risks
                                    such as slippage, transaction costs,
                                    and timing issues can impact profits. It's important to act quickly and consider
                                    these factors when utilizing the
                                    scanner.
                                </p>
                            </div>
                        </details>
                    </div>
                </div>
            </div>


        </main>
        <div class="mt-auto">
            {% include 'blocks/footer.html' %}
        </div>
    </div>
    {% include 'blocks/spreads-preview.html' %}

    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.css" />

    <style>
        /* Make sliders show pointer cursor on hover */
        .noUi-target,
        .noUi-handle,
        .noUi-connect {
            cursor: pointer !important;
        }
    </style>
    {% include 'blocks/table-sorting.html' %}
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // DOM element references
            const searchInput = document.getElementById('search-input');
            const searchButton = document.getElementById('search-button');
            const expiryTabsContainer = document.getElementById('expiry-tabs');
            const tableContainer = document.getElementById('option-chain-table');
            const stockPriceContainer = document.getElementById('stock-price');
            const futurePriceContainer = document.getElementById('future-price');
            const filterLowVolumeCheckbox = document.getElementById('filterLowVolume');
            const callBidQtySlider = document.getElementById('call-bid-qty');
            const callAskQtySlider = document.getElementById('call-ask-qty');
            const putBidQtySlider = document.getElementById('put-bid-qty');
            const putAskQtySlider = document.getElementById('put-ask-qty');
            const searchTab = document.getElementById("search-tab");
            const allStocksTab = document.getElementById("all-stocks-tab");
            const searchContent = document.getElementById("search-content");
            const allStocksContent = document.getElementById("all-stocks-content");
            let currentSearchSymbol = '';

            // Global variables
            window._lastOptionData = {};
            window.currentStockPrice = null;
            window.futurePrices = null;
            let currentSortColumn = "parityPct";
            let currentSortOrder = "desc";

            // Ensure "Filter Low Volumes (>20)" is enabled by default.
            filterLowVolumeCheckbox.checked = true;
            filterLowVolumeCheckbox.addEventListener('change', () => {
                if (window._lastOptionData) {
                    displayOptionsForExpiry(window._lastOptionData.allOptions, window._lastOptionData.currentExpiry);
                }
            });

            document.addEventListener('ltp-preference-changed', () => {
                // Only respond to events from our specific checkbox or if no source is specified
                if (event.detail.source && event.detail.source !== 'put-call-parity-single-use-bid-ask') return;

                if (window._lastOptionData) {
                    displayOptionsForExpiry(window._lastOptionData.allOptions, window._lastOptionData.currentExpiry);
                }
            });

            // Listen for custom event from search component
            document.addEventListener('stockSearchTriggered', (e) => {
                const symbol = e.detail.symbol;
                currentSearchSymbol = symbol;
                if (symbol) handleSearch(symbol);
            });

            function initializeSliders() {
                noUiSlider.create(callBidQtySlider, {
                    start: [0],
                    connect: [true, false],
                    range: { 'min': 0, 'max': 1000 },
                    step: 1,
                    format: {
                        to: value => Math.round(value),
                        from: value => parseInt(value)
                    }
                });

                noUiSlider.create(callAskQtySlider, {
                    start: [0],
                    connect: [true, false],
                    range: { 'min': 0, 'max': 1000 },
                    step: 1,
                    format: {
                        to: value => Math.round(value),
                        from: value => parseInt(value)
                    }
                });

                noUiSlider.create(putBidQtySlider, {
                    start: [0],
                    connect: [true, false],
                    range: { 'min': 0, 'max': 1000 },
                    step: 1,
                    format: {
                        to: value => Math.round(value),
                        from: value => parseInt(value)
                    }
                });

                noUiSlider.create(putAskQtySlider, {
                    start: [0],
                    connect: [true, false],
                    range: { 'min': 0, 'max': 1000 },
                    step: 1,
                    format: {
                        to: value => Math.round(value),
                        from: value => parseInt(value)
                    }
                });

                function updateParityFilters() {
                    if (window._lastOptionData.currentExpiry) {
                        const callBidQtyValue = callBidQtySlider.noUiSlider.get();
                        const callAskQtyValue = callAskQtySlider.noUiSlider.get();
                        const putBidQtyValue = putBidQtySlider.noUiSlider.get();
                        const putAskQtyValue = putAskQtySlider.noUiSlider.get();

                        // Correctly select elements
                        document.getElementById('call-bid-filter-value').textContent = callBidQtyValue; // Fixed selector

                        document.getElementById('call-ask-filter-value').textContent = callAskQtyValue; // Fixed selector

                        document.getElementById('put-bid-filter-value').textContent = putBidQtyValue; // Fixed selector

                        document.getElementById('put-ask-filter-value').textContent = putAskQtyValue; // Fixed selector

                        displayOptionsForExpiry(window._lastOptionData.allOptions, window._lastOptionData.currentExpiry);
                    }
                }

                // Add listeners to all sliders
                [callBidQtySlider].forEach(slider => {
                    slider.noUiSlider.on('change', updateParityFilters);
                });
                [callAskQtySlider].forEach(slider => {
                    slider.noUiSlider.on('change', updateParityFilters);
                });
                [putBidQtySlider].forEach(slider => {
                    slider.noUiSlider.on('change', updateParityFilters);
                });
                [putAskQtySlider].forEach(slider => {
                    slider.noUiSlider.on('change', updateParityFilters);
                });

                // Add update event listeners just for updating the display values without filtering
                [callBidQtySlider].forEach(slider => {
                    slider.noUiSlider.on('update', function (values) {
                        document.getElementById('call-bid-filter-value').textContent = values[0];
                    });
                });
                [callAskQtySlider].forEach(slider => {
                    slider.noUiSlider.on('update', function (values) {
                        document.getElementById('call-ask-filter-value').textContent = values[0];
                    });
                });
                [putBidQtySlider].forEach(slider => {
                    slider.noUiSlider.on('update', function (values) {
                        document.getElementById('put-bid-filter-value').textContent = values[0];
                    });
                });
                [putAskQtySlider].forEach(slider => {
                    slider.noUiSlider.on('update', function (values) {
                        document.getElementById('put-ask-filter-value').textContent = values[0];
                    });
                });
            }

            initializeSliders();

            // Clear UI and fetch data for a symbol.
            async function handleSearch(symbol) {
                showLoadingSpinner("right-section-pcp", `Loading data for ${symbol}...`);
                expiryTabsContainer.innerHTML = '';
                tableContainer.innerHTML = '';
                stockPriceContainer.innerHTML = '';
                futurePriceContainer.innerHTML = '';
                // document.getElementById('filters-container').classList.add('hidden'); // No longer hide here
                await fetchOptionChain(symbol); // Fetch options first
                await fetchFutures(symbol);     // Then fetch futures
                document.getElementById('filters-container').classList.remove('hidden'); // Show filters after fetches
            }

            // Fetch option chain data and compute underlyingValue from option records.
            async function fetchOptionChain(symbol) {
                try {
                    const res = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${symbol}`);
                    if (!res.ok) throw new Error(`Failed to fetch options: ${res.statusText}`);
                    const data = await res.json();
                    let underlyingValue = null;
                    if (data.records && data.records.data) {
                        for (const record of data.records.data) {
                            if (record.CE && record.CE.underlyingValue) {
                                underlyingValue = record.CE.underlyingValue; break;
                            }
                            if (record.PE && record.PE.underlyingValue) {
                                underlyingValue = record.PE.underlyingValue; break;
                            }
                        }
                    }
                    window.currentStockPrice = underlyingValue;
                    stockPriceContainer.innerHTML = `<span class="bg-sky-100 font-semibold px-3 py-1 rounded text-center">Stock Price: ${underlyingValue ? '' + underlyingValue.toFixed(2) : 'N/A'}</span>`;

                    // Render tabs (which triggers the first displayOptionsForExpiry)
                    renderExpiryTabs(data.records); // Note: Removed await as renderExpiryTabs is not async

                } catch (error) {
                    console.error('Error fetching option chain:', error);
                    tableContainer.innerHTML = `<p class="text-red-500">Failed to fetch options data. Error: ${error.message}</p>`;
                    // Ensure filters are hidden on error
                    document.getElementById('filters-container').classList.add('hidden');
                } finally {
                    // Moved hideLoadingSpinner to handleSearch after both fetches attempt
                }
            }

            // Fetch futures data.
            async function fetchFutures(symbol) {
                try {
                    const resFutures = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/futures/${symbol}`);
                    if (resFutures.ok) {
                        window.futurePrices = await resFutures.json();
                    } else {
                        console.warn(`Failed to fetch futures: ${resFutures.statusText}`);
                        window.futurePrices = null; // Reset or handle error appropriately
                    }
                } catch (error) {
                    console.error("Error fetching futures data", error);
                    window.futurePrices = null; // Ensure reset on error
                } finally {
                    hideLoadingSpinner('right-section-pcp'); // Hide spinner after futures attempt
                }
            }

            // Render expiry tabs based on the fetched option chain data.
            function renderExpiryTabs(data) {
                if (!data.expiryDates || !data.expiryDates.length) {
                    tableContainer.innerHTML = '<p>No expiry data found.</p>';
                    return;
                }
                expiryTabsContainer.innerHTML = data.expiryDates.map((date, idx) => {
                    return `
                        <button class="tab-btn px-4 py-1.5 rounded-sm text-xs font-medium 
                            ${idx === 0 ? 'bg-blue-500 text-white' : 'bg-white dark:bg-neutral-800'}"
                            data-expiry="${date}">
                            ${date}
                        </button>
                    `;
                }).join('');

                // Update tab click handler styling
                document.querySelectorAll('.tab-btn').forEach((btn) => {
                    btn.addEventListener('click', function () {
                        document.querySelectorAll('.tab-btn').forEach((b) => {
                            b.classList.remove('bg-blue-500', 'text-white');
                            b.classList.add('bg-white', 'dark:bg-neutral-800');
                        });
                        this.classList.remove('bg-white', 'dark:bg-neutral-800');
                        this.classList.add('bg-blue-500', 'text-white');
                        displayOptionsForExpiry(data.data, this.dataset.expiry);
                        window._lastOptionData.currentExpiry = this.dataset.expiry;
                    });
                });

                // Display options for the first expiry by default.
                window._lastOptionData = {
                    allOptions: data.data,
                    currentExpiry: data.expiryDates[0],
                    expiryDates: data.expiryDates || []
                };
                displayOptionsForExpiry(data.data, data.expiryDates[0]);
            }

            // Build the option chain table.
            function displayOptionsForExpiry(data, expiryDate) {
                if (!data || !data.length) {
                    tableContainer.innerHTML = '<p>No data available</p>';
                    return;
                }

                let filtered = data.filter(item => item.expiryDate === expiryDate);
                const useBidAsk = !window.useLTPForPrices;

                // Get future price for the expiry
                const futurePrice = window.futurePrices?.[expiryDate] || window.currentStockPrice || 0;

                // Update future price display
                if (futurePrice) {
                    futurePriceContainer.innerHTML = `
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-2">
                            <div class="bg-gray-50 dark:bg-neutral-700 rounded-lg p-3">
                                <div class="text-xs text-gray-600 dark:text-gray-400">Future Price</div>
                                <div class="font-medium">${futurePrice.toFixed(2)}</div>
                            </div>
                        </div>
                    `;
                }

                // Filter based on price availability
                filtered = filtered.filter(item => {
                    const callPrice = item.CE?.lastPrice;  // Always use lastPrice for filtering
                    const putPrice = item.PE?.lastPrice;   // Always use lastPrice for filtering
                    return callPrice != null && callPrice > 0 && putPrice != null && putPrice > 0 && item.CE && item.PE;
                });

                // Filter based on volumes if checkbox is checked
                if (filterLowVolumeCheckbox.checked) {
                    filtered = filtered.filter(item => {
                        const callVol = parseFloat(item.CE?.totalTradedVolume) || 0;
                        const putVol = parseFloat(item.PE?.totalTradedVolume) || 0;
                        return callVol >= 20 && putVol >= 20;
                    });
                }

                filtered = filterParityData(filtered);

                if (!filtered.length) {
                    tableContainer.innerHTML = `<p>No data available for ${expiryDate}</p>`;
                    return;
                }

                // Build table HTML
                let tableHtml = `
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border text-sm" id="pcp-table">
                            <thead>
                                <tr class="bg-gray-100 dark:bg-neutral-700">
                                    <th class="border p-2" rowspan="2" data-col="futurePrice">Future Price</th>
                                    <th class="border p-2" rowspan="2" data-col="strike">Strike Price</th>
                                    
                                    <!-- Call Options Group -->
                                    <th class="border p-2 text-center bg-blue-50 dark:bg-blue-900/20" colspan="6">Call Option</th>
                                    
                                    <!-- Put Options Group -->
                                    <th class="border p-2 text-center bg-red-50 dark:bg-red-900/20" colspan="6">Put Option</th>
                                    
                                    <th class="border p-2" rowspan="2" data-col="parity">Parity</th>
                                    <th class="border p-2" rowspan="2" data-col="parityPct">Parity %</th>
                                    <th class="border p-2" rowspan="2" data-no-sort>Preview</th>
                                </tr>
                                <tr class="bg-gray-100 dark:bg-neutral-700">
                                    <!-- Call Option Columns -->
                                    <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="call">LTP</th>
                                    <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="callBid">Bid</th>
                                    <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="callBidQty">Bid Qty</th>
                                    <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="callAsk">Ask</th>
                                    <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="callAskQty">Ask Qty</th>
                                    <th class="border p-2 bg-blue-50 dark:bg-blue-900/20" data-col="callVolume">Volume</th>
                                    
                                    <!-- Put Option Columns -->
                                    <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="put">LTP</th>
                                    <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="putBid">Bid</th>
                                    <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="putBidQty">Bid Qty</th>
                                    <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="putAsk">Ask</th>
                                    <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="putAskQty">Ask Qty</th>
                                    <th class="border p-2 bg-red-50 dark:bg-red-900/20" data-col="putVolume">Volume</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${filtered.map(opt => {
                    const strike = opt.strikePrice;
                    const ce = opt.CE || {};
                    const pe = opt.PE || {};

                    // Call option data (Price column always uses lastPrice)
                    const callPrice = ce.lastPrice || 0;
                    const callBid = ce.bidprice || 0;
                    const callBidQty = ce.bidQty || 0;
                    const callAsk = ce.askPrice || 0;
                    const callAskQty = ce.askQty || 0;
                    const callVol = ce.totalTradedVolume || 0;

                    // Put option data (Price column always uses lastPrice)
                    const putPrice = pe.lastPrice || 0;
                    const putBid = pe.bidprice || 0;
                    const putBidQty = pe.bidQty || 0;
                    const putAsk = pe.askPrice || 0;
                    const putAskQty = pe.askQty || 0;
                    const putVol = pe.totalTradedVolume || 0;

                    // Parity calculation with bid/ask logic
                    let parityCallPrice, parityPutPrice;
                    if (useBidAsk) {
                        // Arbitrage direction: if call overpriced (C - P > F - K), sell call/buy put; else buy call/sell put
                        const callOverpriced = (ce.bidprice - pe.askPrice) > (futurePrice - strike);
                        parityCallPrice = callOverpriced ? (ce.bidprice || ce.lastPrice || 0) : (ce.askPrice || ce.lastPrice || 0);
                        parityPutPrice = callOverpriced ? (pe.askPrice || pe.lastPrice || 0) : (pe.bidprice || pe.lastPrice || 0);
                    } else {
                        parityCallPrice = ce.lastPrice || 0;
                        parityPutPrice = pe.lastPrice || 0;
                    }
                    const computedDiff = parityCallPrice - parityPutPrice;
                    const expectedDiff = futurePrice - strike;
                    const parity = (computedDiff - expectedDiff).toFixed(2);
                    const parityPct = futurePrice !== 0 ? ((computedDiff - expectedDiff) / futurePrice * 100).toFixed(2) : 'N/A';

                    return `
                                        <tr class="hover:bg-gray-50 dark:hover:bg-neutral-700">
                                            <td class="border p-2 text-center">${futurePrice.toFixed(2)}</td>
                                            <td class="border p-2 text-center">${strike}</td>
                                            <td class="border p-2 text-center">${callPrice.toFixed(2)}</td>
                                            <td class="border p-2 text-center">${callBid.toFixed(2)}</td>
                                            <td class="border p-2 text-center">${callBidQty}</td>
                                            <td class="border p-2 text-center">${callAsk.toFixed(2)}</td>
                                            <td class="border p-2 text-center">${callAskQty}</td>
                                            <td class="border p-2 text-center">${callVol}</td>
                                            <td class="border p-2 text-center">${putPrice.toFixed(2)}</td>
                                            <td class="border p-2 text-center">${putBid.toFixed(2)}</td>
                                            <td class="border p-2 text-center">${putBidQty}</td>
                                            <td class="border p-2 text-center">${putAsk.toFixed(2)}</td>
                                            <td class="border p-2 text-center">${putAskQty}</td>
                                            <td class="border p-2 text-center">${putVol}</td>
                                            <td class="border p-2 text-center">${parity}</td>
                                            <td class="border p-2 text-center">${parityPct}${parityPct !== 'N/A' ? '%' : ''}</td>
                                            <td class="border p-2 text-center">
                                                <button
                                                    onclick='window.PreviewModal.show({
                                                        legs: ${JSON.stringify(convertPCPToLegs(opt, expiryDate, futurePrice, useBidAsk))},
                                                        symbol: "${currentSearchSymbol}",
                                                        spotPrice: ${futurePrice || window.currentStockPrice || 0},
                                                        optionChain: ${JSON.stringify(window._lastOptionData.allOptions)},
                                                        expiryDates: ${JSON.stringify(window._lastOptionData.expiryDates)},
                                                        allowedTypes: ["CE", "PE", "FUT"],
                                                    })'
                                                    title="Preview Arbitrage"
                                                    class="p-1 hover:bg-gray-100 rounded">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-chart-candlestick text-gray-500">
                                                        <path d="M9 5v4"></path>
                                                        <rect width="4" height="6" x="7" y="9" rx="1"></rect>
                                                        <path d="M9 15v2"></path>
                                                        <path d="M17 3v2"></path>
                                                        <rect width="4" height="8" x="15" y="5" rx="1"></rect>
                                                        <path d="M17 13v3"></path>
                                                        <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                    `;
                }).join('')}
                            </tbody>
                        </table>
                    </div>
                `;

                tableContainer.innerHTML = tableHtml;
                window.addSorting('pcp-table'); // Use the shared sorting function
            }

            function filterParityData(spreads) {
                const callBidQtyThreshold = parseInt(callBidQtySlider.noUiSlider.get());
                const callAskQtyThreshold = parseInt(callAskQtySlider.noUiSlider.get());
                const putBidQtyThreshold = parseInt(putBidQtySlider.noUiSlider.get());
                const putAskQtyThreshold = parseInt(putAskQtySlider.noUiSlider.get());

                return spreads.filter(spread => {
                    return (
                        (spread.CE?.bidQty || 0) >= callBidQtyThreshold &&
                        (spread.CE?.askQty || 0) >= callAskQtyThreshold &&
                        (spread.PE?.bidQty || 0) >= putBidQtyThreshold &&
                        (spread.PE?.askQty || 0) >= putAskQtyThreshold
                    );
                });
            }

            // Tab switching logic.
            function switchTab(activeTab, activeContent, inactiveTab, inactiveContent) {
                activeTab.classList.remove("text-gray-500", "border-transparent", "hover:text-gray-700", "hover:border-gray-300");
                activeTab.classList.add("bg-white", "text-blue-600", "rounded-md", "shadow-md");
                inactiveTab.classList.add("text-gray-500", "border-transparent", "hover:text-gray-700", "hover:border-gray-300");
                inactiveTab.classList.remove("bg-white", "text-blue-600", "rounded-md", "shadow-md");
                activeContent.classList.remove("hidden");
                inactiveContent.classList.add("hidden");
            }

            searchTab.addEventListener("click", () => {
                switchTab(searchTab, searchContent, allStocksTab, allStocksContent);
            });

            allStocksTab.addEventListener("click", () => {
                switchTab(allStocksTab, allStocksContent, searchTab, searchContent);
            });
        });
    </script>
</body>

</html>