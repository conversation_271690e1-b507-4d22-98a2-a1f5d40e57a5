<!DOCTYPE html>
<html lang="en">

<head>
    <title>Long Straddle Scanner with AI-Powered Analysis | AI Bull</title>
    <meta name="description"
        content="Find the best long straddle opportunities with AI-powered analysis. Optimize your strategy by identifying options with high potential for profit from significant price movements." />
    <link rel="canonical" href="https://theaibull.com/strategies/straddle-scanner" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/strategies/straddle-scanner" />
    <meta property="og:title"
        content="Long Straddle Scanner - Find the Best Long Straddle Opportunities with AI-Powered Analysis" />
    <meta property="og:description"
        content="Find the best long straddle opportunities with AI-powered analysis. Optimize your strategy by identifying options with high potential for profit from significant price movements." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title"
        content="Long Straddle Scanner - Find the Best Long Straddle Opportunities with AI-Powered Analysis" />
    <meta name="twitter:description"
        content="Find the best long straddle opportunities with AI-powered analysis to optimize your options strategy and profit from significant price movements." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />

    {% include 'blocks/head.html' %}
    <style>
        /* Custom styles for noUiSlider */
        .noUi-handle {
            cursor: pointer !important;
        }

        .noUi-handle-last {
            cursor: pointer !important;
        }
    </style>
</head>
{% include 'blocks/head.html' %}
</head>

<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {% include 'blocks/common.html' %}
    {%include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <main>
            <!-- Add these script tags at the top -->
            <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.6.1/nouislider.min.css" />

            <div class="p-3 mx-auto max-w-7xl">
                <!-- Header Section -->
                <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2 rounded-lg mb-3">
                    <!-- Title and Tabs Row -->
                    <div class="md:flex justify-between items-center">
                        <div class="flex items-center">
                            <h1 class="md:text-xl text-lg font-semibold tracking-tight">Straddle Scanner</h1>
                            <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">Find the best
                                straddle
                                opportunities
                                with
                                AI-powered analysis</h2>
                            <!-- Button to trigger modal open -->
                            <button id="openModalBtn" onclick="openModal()" class="ml-2">

                                <div class="flex group text-sm">
                                    <div
                                        class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                        <div
                                            class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-circle-help">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                    <path d="M12 17h.01"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div
                                            class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                            <div
                                                class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                                How to Use This Page</div>
                                        </div>
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col gap-3">
                    <div class="w-full">
                        <!-- Stock Category Selector -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-3">
                            <div>
                                <div class="flex flex-wrap gap-4">
                                    <label class="flex items-center cursor-pointer">
                                        <input type="radio" name="stock-category" checked value="index" class="mr-2">
                                        Indices
                                    </label>
                                    <label class="flex items-center cursor-pointer">
                                        <input type="radio" name="stock-category" value="nifty" class="mr-2">
                                        Nifty Stocks
                                    </label>
                                    <label class="flex items-center cursor-pointer">
                                        <input type="radio" name="stock-category" value="fo" class="mr-2">
                                        F&O Stocks
                                    </label>
                                </div>
                            </div>
                        </div>
                        <!-- Ticker Selection Section -->
                        <div
                            class="bg-white dark:bg-neutral-800 rounded-lg p-4 flex flex-col lg:max-h-[calc(60vh)] overflow-hidden">
                            <div class="flex justify-between items-center mb-3">
                                <h3 class="font-semibold">Select Stocks</h3>
                                <div class="flex gap-2 items-center">
                                    <button id="select-all"
                                        class="text-xs hover:text-blue-500 text-neutral-700 font-medium">
                                        Select All
                                    </button>
                                    <span class="text-gray-400 text-xs">|</span>
                                    <button id="unselect-all"
                                        class="text-xs hover:text-red-500 text-neutral-700 font-medium">
                                        Unselect All
                                    </button>
                                </div>
                            </div>
                            <div id="ticker-selection" class="mb-4 flex-1 overflow-y-auto custom-scroll"></div>
                            <div class="pt-3 mt-auto">
                                <div class="flex gap-2 justify-between">
                                    <button id="scan-button"
                                        class="bg-blue-500 block focus:outline-0 font-medium hover:bg-blue-600 px-6 py-3 rounded-md text-white transition-colors w-full">
                                        Scan Selected
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Panel -->
                    <div class="mt-3">
                        <!-- Filter Section -->
                        <div id="results-filter-section"
                            class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-4 hidden">
                            <div class="md:flex justify-between mb-3">
                                <h3 class="font-semibold">Filter Scanned Results</h3>
                            </div>

                            <div class="grid lg:grid-cols-3 gap-6 mb-4">
                                <!-- Stock Search Filter -->
                                <div>
                                    <label class="block font-medium mb-2 text-sm">
                                        Search Stocks
                                    </label>
                                    <input id="stock-filter" onkeyup="filterResults()" type="text"
                                        placeholder="Filter stocks..."
                                        class="w-full px-3 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all" />
                                </div>

                                <!-- Breakeven Range Slider -->
                                <div>
                                    <label class="block font-medium mb-2 text-sm">
                                        Minimum Breakeven Range (<span id="breakeven-label-value">5</span>%)
                                    </label>
                                    <div id="breakeven-range-slider" class="mt-6 cursor-pointer"></div>
                                    <div class="flex justify-between text-sm mt-1">
                                        <span id="breakeven-min-value">0%</span>
                                        <span id="breakeven-max-value">20%</span>
                                    </div>
                                </div>

                                <!-- Total Debit Range Slider -->
                                <div>
                                    <label class="block font-medium mb-2 text-sm">
                                        Minimum Total Debit (<span id="debit-label-value">5</span>%)
                                    </label>
                                    <div id="debit-range-slider" class="mt-6 cursor-pointer"></div>
                                    <div class="flex justify-between text-sm mt-1">
                                        <span id="debit-min-value">0%</span>
                                        <span id="debit-max-value">20%</span>
                                    </div>
                                </div>

                                <!-- Bid/Ask Checkbox -->
                                {% with id='straddle-scanner-use-bid-ask' %}
                                {% include 'blocks/use-ltp-checkbox.html' %}
                                {% endwith %}
                            </div>

                            <!-- Expiry Tabs Container -->
                            <div class="overflow-x-auto">
                                <div id="straddle-scanner-expiry-tabs"
                                    class="bg-yellow-50 flex flex-wrap gap-1 md:mt-0 mt-4 overflow-x-auto p-1 rounded-md">
                                </div>
                            </div>

                            <!-- Results Counter and Summary Button -->
                            <div class="flex justify-between items-center mt-4 pt-4 border-t">
                                <div id="results-counter" class="text-sm text-gray-600 hidden md:inline-flex"></div>
                                <button onclick="toggleSummaryModal()" id="view-summary-btn"
                                    class="text-blue-500 text-blue-800 underline text-sm">
                                    View Top Stocks Summary
                                </button>
                            </div>
                        </div>

                        <!-- Scanner Results -->
                        <div id="scanner-results-container">
                            <div id="scanner-results">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- FAQ Section in Accordion Format -->
                <div class="mt-8 text-sm">
                    <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
                    <div class="space-y-4">
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What is a Long Straddle?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    A long straddle involves buying both a call and put option at the same strike price
                                    and expiration, betting on
                                    significant price movement in either direction.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How does the Long Straddle Scanner work?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    The scanner analyzes multiple assets and identifies the best long straddle
                                    opportunities by considering factors like
                                    implied volatility, premiums, and market conditions to suggest the most promising
                                    trades.

                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What criteria does the tool use to find opportunities?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    The tool uses AI-powered analysis to evaluate implied volatility, option pricing,
                                    and market trends to find long
                                    straddle opportunities with the highest potential for profit.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                Can I customize the parameters for the scan?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    Yes, you can adjust parameters like strike price range, expiration dates, and
                                    volatility to tailor the scan to your
                                    trading preferences.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What are the benefits of using the Long Straddle Scanner?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300 leading-6">
                                    The scanner helps you quickly identify high-potential long straddle trades, saving
                                    time and enhancing your ability to
                                    profit from market volatility with AI-driven insights.
                                </p>
                            </div>
                        </details>
                    </div>
                </div>
            </div>

            <!-- Summary Modal -->
            <div id="summaryModal"
                class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center hidden z-50 p-4">
                <div
                    class="modal-content bg-white dark:bg-neutral-800 relative w-full max-w-[95vw] max-h-[90vh] rounded-lg shadow-lg flex flex-col">
                    <!-- Modal Header -->
                    <div class="p-4 border-b dark:border-neutral-700">
                        <h2 class="text-lg font-semibold">Top Stocks Summary</h2>
                        <button onclick="toggleSummaryModal()" id="closeSummaryModal"
                            class="absolute top-5 right-5 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 text-2xl font-bold">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-x-icon lucide-x">
                                <path d="M18 6 6 18" />
                                <path d="m6 6 12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Modal Body with Scrollable Content -->
                    <div class="flex-1 overflow-auto p-4">
                        <div id="summaryTableContainer"></div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Stock Symbols -->
    {%include 'blocks/stock-symbols.html' %}

    <script>
        // Global variables
        let currentTickers = indexTickers;
        let currentCategory = "index";
        const scannedData = {};
        let selectedExpiry = null;
        let scannedTickers = [];
        let processedCount = 0;
        let scanComplete = false;

        // DOM elements
        const tickerSelectionContainer = document.getElementById("ticker-selection");
        const selectAllBtn = document.getElementById("select-all");
        const unselectAllBtn = document.getElementById("unselect-all");
        const scanButton = document.getElementById("scan-button");
        const scannerResults = document.getElementById("scanner-results");
        const expiryTabsContainer = document.getElementById("straddle-scanner-expiry-tabs");
        const stockCategoryRadios = document.getElementsByName("stock-category");
        const stockFilter = document.getElementById("stock-filter");

        // Initialize sliders
        const breakevenSlider = document.getElementById("breakeven-range-slider");
        const debitSlider = document.getElementById("debit-range-slider");

        noUiSlider.create(breakevenSlider, {
            start: [5],
            connect: [true, false],
            range: {
                'min': 0,
                'max': 20
            },
            step: 0.5,
            format: {
                to: value => value.toFixed(2),
                from: value => parseFloat(value)
            }
        });

        noUiSlider.create(debitSlider, {
            start: [5],
            connect: [true, false],
            range: {
                'min': 0,
                'max': 20
            },
            step: 0.5,
            format: {
                to: value => value.toFixed(2),
                from: value => parseFloat(value)
            }
        });

        breakevenSlider.noUiSlider.on("update", function (values) {
            document.getElementById("breakeven-label-value").textContent = values[0];
            if (scanComplete) filterResults();
        });

        debitSlider.noUiSlider.on("update", function (values) {
            document.getElementById("debit-label-value").textContent = values[0];
            if (scanComplete) filterResults();
        });

        // Listen for LTP preference changes from the checkbox template
        document.addEventListener('ltp-preference-changed', function (event) {
            // Only respond to events from our specific checkbox or if no source is specified
            if (event.detail.source && event.detail.source !== 'straddle-scanner-use-bid-ask') return;

            if (scanComplete) {
                // Recalculate metrics for all tickers
                scannedTickers.forEach(ticker => {
                    const tickerData = scannedData[ticker];
                    if (tickerData && selectedExpiry) {
                        if (tickerData.optionsMetrics[selectedExpiry]) {
                            // Preserve original option data while updating calculated metrics
                            tickerData.optionsMetrics[selectedExpiry] = {
                                ...tickerData.optionsMetrics[selectedExpiry],
                                ...calculateMetrics(tickerData.optionsMetrics[selectedExpiry].ceAtm, tickerData.optionsMetrics[selectedExpiry].peAtm, tickerData.optionsMetrics[selectedExpiry].atmStrike, tickerData.stockPrice)
                            };
                        }
                    }
                });
                filterResults();
            }
        });

        // Build ticker selection checkboxes
        function buildTickerSelection(tickerArray) {
            let html = '<div class="border border-l-0 divide-x divide-y grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 md:grid-cols-4">';
            tickerArray.forEach((ticker) => {
                html += `
                    <label class="flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-neutral-700 first:border-l text-sm">
                        <input type="checkbox" value="${ticker}" class="ticker-checkbox" checked>
                        <span>${ticker}</span>
                    </label>
                `;
            });
            html += "</div>";
            tickerSelectionContainer.innerHTML = html;
        }

        // Initial build
        buildTickerSelection(currentTickers);

        // Stock category radio change handler
        stockCategoryRadios.forEach((radio) => {
            radio.addEventListener("change", function () {
                currentCategory = this.value;
                switch (this.value) {
                    case 'nifty':
                        currentTickers = niftyTickers;
                        break;
                    case 'fo':
                        currentTickers = foTickers;
                        break;
                    case 'index':
                        currentTickers = indexTickers;
                        break;
                }
                buildTickerSelection(currentTickers);
            });
        });

        // Select/Unselect all handlers
        selectAllBtn.addEventListener("click", () => {
            document.querySelectorAll(".ticker-checkbox").forEach((cb) => (cb.checked = true));
        });
        unselectAllBtn.addEventListener("click", () => {
            document.querySelectorAll(".ticker-checkbox").forEach((cb) => (cb.checked = false));
        });

        // Summary modal handlers
        function toggleSummaryModal() {
            if (document.getElementById("summaryModal").classList.contains("hidden")) {
                buildSummaryModal();
            }
            document.getElementById("summaryModal").classList.toggle("hidden");
        }

        // Main scan button handler
        scanButton.addEventListener("click", async () => {
            scanComplete = false;
            // Get selected tickers
            const selected = Array.from(
                document.querySelectorAll(".ticker-checkbox:checked")
            ).map((cb) => cb.value);

            if (selected.length === 0) {
                showAlert("Please select at least one ticker.");
                return;
            }
            // Reset previous data
            expiryTabsContainer.innerHTML = "";
            scannerResults.innerHTML = ``;
            Object.keys(scannedData).forEach((key) => delete scannedData[key]);
            scannedTickers = [];
            selectedExpiry = null;
            processedCount = 0;

            // Hide filter section at start of new scan
            document.getElementById("results-filter-section").classList.add("hidden");

            scannedTickers = selected;

            // Process all tickers
            try {
                await Promise.all(scannedTickers.map(processTicker));
                scannerResults.innerHTML = "";
                const firstTicker = scannedTickers.find(ticker => scannedData[ticker]?.expiryDates?.length > 0);
                if (firstTicker && scannedData[firstTicker]?.expiryDates?.length > 0) {
                    buildExpiryTabs(scannedData[firstTicker].expiryDates);
                    selectedExpiry = scannedData[firstTicker].expiryDates[0];

                    // Show filter section only if we have valid data
                    document.getElementById("results-filter-section").classList.remove("hidden");

                    scanComplete = true;
                    filterResults();
                    toggleSummaryModal();
                }
            } catch (err) {
                console.error("Error processing tickers:", err);
                document.getElementById("results-filter-section").classList.add("hidden");
                scannerResults.innerHTML = `
                    <p class="text-red-500">Error processing tickers. Please try again.</p>
                `;
            }
        });

        function calculateMetrics(ceAtm, peAtm, atmStrike, stockPrice) {
            // Calculate both long and short straddle prices
            const longCallPrice = ceAtm ? (window.useLTPForPrices ? ceAtm.lastPrice : (ceAtm.askPrice || ceAtm.lastPrice)) : 0;
            const longPutPrice = peAtm ? (window.useLTPForPrices ? peAtm.lastPrice : (peAtm.askPrice || peAtm.lastPrice)) : 0;
            const shortCallPrice = ceAtm ? (window.useLTPForPrices ? ceAtm.lastPrice : (ceAtm.bidPrice || ceAtm.lastPrice)) : 0;
            const shortPutPrice = peAtm ? (window.useLTPForPrices ? peAtm.lastPrice : (peAtm.bidPrice || peAtm.lastPrice)) : 0;

            const longTotalDebit = longCallPrice + longPutPrice;
            const shortTotalCredit = shortCallPrice + shortPutPrice;

            // Calculate breakeven points for both
            const longLowerBreakeven = atmStrike - longTotalDebit;
            const longUpperBreakeven = atmStrike + longTotalDebit;
            const shortLowerBreakeven = atmStrike - shortTotalCredit;
            const shortUpperBreakeven = atmStrike + shortTotalCredit;

            return {
                long: {
                    callPrice: longCallPrice,
                    putPrice: longPutPrice,
                    totalDebit: longTotalDebit,
                    lowerBreakeven: longLowerBreakeven,
                    upperBreakeven: longUpperBreakeven,
                    lowerBreakevenPerc: ((longLowerBreakeven - stockPrice) / stockPrice) * 100,
                    upperBreakevenPerc: ((longUpperBreakeven - stockPrice) / stockPrice) * 100
                },
                short: {
                    callPrice: shortCallPrice,
                    putPrice: shortPutPrice,
                    totalCredit: shortTotalCredit,
                    lowerBreakeven: shortLowerBreakeven,
                    upperBreakeven: shortUpperBreakeven,
                    lowerBreakevenPerc: ((shortLowerBreakeven - stockPrice) / stockPrice) * 100,
                    upperBreakevenPerc: ((shortUpperBreakeven - stockPrice) / stockPrice) * 100
                }
            };
        }

        // Process individual ticker
        async function processTicker(ticker) {
            let tickerContainer = document.getElementById("ticker-" + ticker);
            if (!tickerContainer) {
                tickerContainer = document.createElement("div");
                tickerContainer.id = "ticker-" + ticker;
                tickerContainer.className = "mb-3 p-3 rounded-md bg-white dark:bg-neutral-800 dark:border-neutral-700 rounded-lg";
                tickerContainer.innerHTML = `
                        <h2 id="ticker-header-${ticker}" class="flex flex-col md:flex-row gap-3 font-medium md:items-center justify-between  text-base">${ticker} - Scanning...</h2>
                        <div id="ticker-content-${ticker}"></div>
                    `;
                scannerResults.appendChild(tickerContainer);
            }

            const contentContainer = document.getElementById("ticker-content-" + ticker);
            try {
                // 1. Fetch options chain
                const resOptions = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/${ticker}`);
                const optionsData = await resOptions.json();
                const records = optionsData.records;
                const expiryDates = records.expiryDates || [];
                const optionsMetrics = {};
                let stockPrice = 0;
                if (records && records.data) {
                    for (const record of records.data) {
                        if (record.CE && record.CE.underlyingValue) {
                            stockPrice = record.CE.underlyingValue;
                            break;
                        }
                        if (record.PE && record.PE.underlyingValue) {
                            stockPrice = record.PE.underlyingValue;
                            break;
                        }
                    }
                }

                // 2. Process each expiry
                expiryDates.forEach(expiry => {
                    const filtered = records.data.filter(opt => opt.expiryDate === expiry);
                    let optionsByStrike = {};

                    // Group options by strike
                    filtered.forEach(opt => {
                        const strike = opt.CE ? opt.CE.strikePrice : (opt.PE ? opt.PE.strikePrice : null);
                        if (strike != null) {
                            if (!optionsByStrike[strike]) optionsByStrike[strike] = {};
                            if (opt.CE) {
                                optionsByStrike[strike].ce = {
                                    ...opt.CE,
                                    bidPrice: opt.CE.bidprice || 0,
                                    askPrice: opt.CE.askPrice || 0
                                };
                            }
                            if (opt.PE) {
                                optionsByStrike[strike].pe = {
                                    ...opt.PE,
                                    bidPrice: opt.PE.bidprice || 0,
                                    askPrice: opt.PE.askPrice || 0
                                };
                            }
                        }
                    });

                    // Find ATM strike
                    let closestStrike = null;
                    let minDiff = Infinity;
                    Object.keys(optionsByStrike).forEach(strike => {
                        const diff = Math.abs(stockPrice - parseFloat(strike));
                        if (diff < minDiff) {
                            minDiff = diff;
                            closestStrike = strike;
                        }
                    });

                    if (closestStrike) {
                        const atmStrike = parseFloat(closestStrike);
                        const ceAtm = optionsByStrike[closestStrike].ce || null;
                        const peAtm = optionsByStrike[closestStrike].pe || null;

                        optionsMetrics[expiry] = {
                            atmStrike,
                            ceAtm,
                            peAtm,
                            ...calculateMetrics(ceAtm, peAtm, atmStrike, stockPrice)
                        };
                    } else {
                        optionsMetrics[expiry] = null;
                    }
                });

                // 3. Fetch and process historical data
                const resHist = await fetch(`https://iam.theaibull.com/v1/wg7ttpouv7/symbol/historical/${ticker}`);
                const histData = await resHist.json();
                let historical = {
                    last1WChange: null,
                    last1WStartPrice: null,
                    last1WEndPrice: null,
                    last1WHigh: null,
                    last1WLow: null,
                    last2WChange: null,
                    last1MChange: null,
                    last1MStartPrice: null,
                    last1MEndPrice: null,
                    last1MHigh: null,
                    last1MLow: null
                };

                if (histData.data && histData.data.length > 0) {
                    const latest = histData.data[0].CH_CLOSING_PRICE;

                    // Weekly: use first 6 days (indices 0 to 5)
                    let weekData = histData.data.slice(0, 6);
                    if (weekData.length > 1) {
                        let weeklyHigh = Math.max(...weekData.map(day => day.CH_TRADE_HIGH_PRICE || day.CH_CLOSING_PRICE));
                        let weeklyLow = Math.min(...weekData.map(day => day.CH_TRADE_LOW_PRICE || day.CH_CLOSING_PRICE));

                        historical.last1WChange = ((latest - weekData[weekData.length - 1].CH_CLOSING_PRICE) / weekData[weekData.length - 1].CH_CLOSING_PRICE) * 100;
                        historical.last1WStartPrice = weekData[weekData.length - 1].CH_CLOSING_PRICE;
                        historical.last1WEndPrice = latest;
                        historical.last1WHigh = weeklyHigh;
                        historical.last1WLow = weeklyLow;
                    }

                    // Last 2 weeks change
                    if (histData.data.length > 10) {
                        historical.last2WChange = ((latest - histData.data[10].CH_CLOSING_PRICE) / histData.data[10].CH_CLOSING_PRICE) * 100;
                    }

                    // Monthly: use first 21 days (indices 0 to 20)
                    let monthData = histData.data.slice(0, 21);
                    if (monthData.length > 1) {
                        let monthlyHigh = Math.max(...monthData.map(day => day.CH_TRADE_HIGH_PRICE || day.CH_CLOSING_PRICE));
                        let monthlyLow = Math.min(...monthData.map(day => day.CH_TRADE_LOW_PRICE || day.CH_CLOSING_PRICE));

                        historical.last1MChange = ((latest - monthData[monthData.length - 1].CH_CLOSING_PRICE) / monthData[monthData.length - 1].CH_CLOSING_PRICE) * 100;
                        historical.last1MStartPrice = monthData[monthData.length - 1].CH_CLOSING_PRICE;
                        historical.last1MEndPrice = latest;
                        historical.last1MHigh = monthlyHigh;
                        historical.last1MLow = monthlyLow;
                    }
                }

                // Store processed data
                scannedData[ticker] = {
                    stockPrice,
                    expiryDates,
                    optionsMetrics,
                    historical
                };

                tickerContainer.querySelector("h2").innerHTML = `${ticker} - Price: ${stockPrice.toFixed(2)}`;
            } catch (err) {
                console.error(`Error processing ${ticker}:`, err);
                contentContainer.innerHTML = `<p class="text-red-500">Failed to fetch data for ${ticker}</p>`;
                throw err;
            }
        }

        // Build expiry tabs
        function buildExpiryTabs(expiryDates) {
            const tabsHtml = expiryDates.map((date, idx) => `
                <button 
                    class="tab-btn border rounded-md px-4 py-1.5 text-xs font-medium whitespace-nowrap transition-colors
                           ${idx === 0 ?
                    "bg-blue-500 text-white" :
                    "bg-white"}"
                    data-expiry="${date}"
                >
                    ${date}
                </button>
            `).join("");

            expiryTabsContainer.innerHTML = tabsHtml;

            document.querySelectorAll(".tab-btn").forEach(btn => {
                btn.addEventListener("click", function () {
                    document.querySelectorAll(".tab-btn").forEach(b => {
                        b.classList.remove("bg-blue-500", "text-white");
                        b.classList.add("bg-white", "dark:bg-neutral-800");
                    });
                    this.classList.remove("bg-white", "dark:bg-neutral-800");
                    this.classList.add("bg-blue-500", "text-white");

                    selectedExpiry = this.dataset.expiry;
                    filterResults();
                });
            });
        }

        // Filter and display results
        function filterResults() {
            if (!scanComplete) return;

            const searchTerm = stockFilter.value.toLowerCase();
            const breakevenMin = parseFloat(breakevenSlider.noUiSlider.get());
            const debitMin = parseFloat(debitSlider.noUiSlider.get());

            // Filter the scanned tickers based on criteria
            const filteredTickers = scannedTickers.filter(ticker => {
                if (!ticker.toLowerCase().includes(searchTerm)) return false;

                const tickerData = scannedData[ticker];
                if (!tickerData || !selectedExpiry) return false;

                const metrics = tickerData.optionsMetrics[selectedExpiry];
                if (!metrics) return false;

                // Calculate breakeven ranges for both strategies
                const longBreakevenRange = Math.abs(metrics.long.upperBreakevenPerc - metrics.long.lowerBreakevenPerc);
                const shortBreakevenRange = Math.abs(metrics.short.upperBreakevenPerc - metrics.short.lowerBreakevenPerc);

                // Calculate debit/credit percentages
                const longDebitPercent = (metrics.long.totalDebit / tickerData.stockPrice) * 100;
                const shortCreditPercent = (metrics.short.totalCredit / tickerData.stockPrice) * 100;

                // A ticker passes if either strategy meets the criteria
                return (longBreakevenRange >= breakevenMin && longDebitPercent >= debitMin) ||
                    (shortBreakevenRange >= breakevenMin && shortCreditPercent >= debitMin);
            });

            // Clear all existing results
            scannerResults.innerHTML = "";

            if (filteredTickers.length === 0) {
                scannerResults.innerHTML = `
                    <div class="bg-white dark:bg-neutral-800 border rounded-md p-8 text-center">
                        <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h3 class="text-lg font-semibold mb-2">No matching stocks found</h3>
                        <p class="text-gray-500">
                            Try adjusting your filters:<br>
                            Minimum Breakeven Range: ${breakevenMin}%<br>
                            Minimum Total Debit: ${debitMin}%
                        </p>
                    </div>
                `;
                return;
            }

            // Render filtered results
            filteredTickers.forEach(ticker => renderTickerData(ticker, selectedExpiry));

            // Update results counter
            const counterEl = document.querySelector("#results-counter");
            if (counterEl) {
                const totalValidStocks = scannedTickers.filter(ticker => {
                    const tickerData = scannedData[ticker];
                    return tickerData && selectedExpiry && tickerData.optionsMetrics[selectedExpiry];
                }).length;

                counterEl.textContent = `Showing ${filteredTickers.length} of ${totalValidStocks} stocks`;
            }
        }

        function createTickerContainer(ticker) {
            let tickerContainer = document.getElementById("ticker-" + ticker);
            if (!tickerContainer) {
                tickerContainer = document.createElement("div");
                tickerContainer.id = "ticker-" + ticker;
                tickerContainer.className = "mb-3 bg-white dark:bg-neutral-800 rounded-lg overflow-hidden p-3";
            }
            return tickerContainer;
        }

        // Render individual ticker data
        function renderTickerData(ticker, expiry) {
            const tickerData = scannedData[ticker];
            if (!tickerData) return;

            const breakevenMin = parseFloat(breakevenSlider.noUiSlider.get());
            const debitMin = parseFloat(debitSlider.noUiSlider.get());

            let tickerContainer = createTickerContainer(ticker);

            const metrics = tickerData.optionsMetrics[expiry];
            if (!metrics) {
                tickerContainer.classList.add("hidden");
                return;
            }

            tickerContainer.classList.remove("hidden");

            tickerContainer.innerHTML = `
                <div class="pb-3">
                    <h3 class="font-medium">${ticker} - Price: ${tickerData.stockPrice.toFixed(2)}</h3>
                </div>
                <div class="overflow-x-auto custom-scroll">
                    <table class="w-full border-collapse border dark:border-neutral-700 md:text-sm text-xs">
                        <thead class="bg-gray-50 dark:bg-neutral-700">
                            <tr>
                                <th class="border font-medium p-2 text-center text-neutral-600">Strategy</th>
                                <th class="border font-medium p-2 text-center text-neutral-600 text-nowrap">CE ATM ${window.useLTPForPrices ? '(LTP)' : '(Bid/Ask)'}</th>
                                <th class="border font-medium p-2 text-center text-neutral-600 text-nowrap">PE ATM ${window.useLTPForPrices ? '(LTP)' : '(Bid/Ask)'}</th>
                                <th class="border font-medium p-2 text-center text-neutral-600 text-nowrap">Total Debit/Credit</th>
                                <th class="border font-medium p-2 text-center text-neutral-600 text-nowrap">Breakeven Range</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="border p-2 text-center font-medium">Long</td>
                                <td class="border p-2 text-center">${metrics.long.callPrice.toFixed(2)}</td>
                                <td class="border p-2 text-center">${metrics.long.putPrice.toFixed(2)}</td>
                                <td class="border p-2 text-center text-red-500">-${metrics.long.totalDebit.toFixed(2)}</td>
                                <td class="border p-2 text-center">${metrics.long.lowerBreakeven.toFixed(2)} (${metrics.long.lowerBreakevenPerc.toFixed(2)}%) - ${metrics.long.upperBreakeven.toFixed(2)} (${metrics.long.upperBreakevenPerc.toFixed(2)}%)</td>
                            </tr>
                            <tr>
                                <td class="border p-2 text-center font-medium">Short</td>
                                <td class="border p-2 text-center">${metrics.short.callPrice.toFixed(2)}</td>
                                <td class="border p-2 text-center">${metrics.short.putPrice.toFixed(2)}</td>
                                <td class="border p-2 text-center text-green-700">+${metrics.short.totalCredit.toFixed(2)}</td>
                                <td class="border p-2 text-center">${metrics.short.lowerBreakeven.toFixed(2)} (${metrics.short.lowerBreakevenPerc.toFixed(2)}%) - ${metrics.short.upperBreakeven.toFixed(2)} (${metrics.short.upperBreakevenPerc.toFixed(2)}%)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;

            scannerResults.appendChild(tickerContainer);
        }

        // Build summary modal
        function buildSummaryModal() {
            const groups = {
                short: { excellent: [], good: [] },
                long: { excellent: [], good: [] },
                none: []
            };

            scannedTickers.forEach(ticker => {
                const tickerData = scannedData[ticker];
                if (!tickerData || !selectedExpiry) return;

                const metrics = tickerData.optionsMetrics[selectedExpiry];
                if (!metrics) return;

                // Calculate ratios for both strategies
                const longOneLeg = Math.min(
                    Math.abs(metrics.long.lowerBreakevenPerc),
                    Math.abs(metrics.long.upperBreakevenPerc)
                );
                const shortOneLeg = Math.min(
                    Math.abs(metrics.short.lowerBreakevenPerc),
                    Math.abs(metrics.short.upperBreakevenPerc)
                );

                const monthlyAbs = tickerData.historical?.last1MChange !== null ?
                    Math.abs(tickerData.historical.last1MChange) : null;

                let classification = "N/A";
                if (monthlyAbs !== null && (longOneLeg > 0 || shortOneLeg > 0)) {
                    // Calculate ratios for both strategies
                    const longRatio = monthlyAbs / longOneLeg;
                    const shortRatio = monthlyAbs / shortOneLeg;

                    // Determine which strategy is more suitable based on the ratios
                    if (longRatio >= 1.5) {
                        classification = "Excellent Long Straddle";
                    } else if (longRatio >= 1) {
                        classification = "Good Long Straddle";
                    } else if (shortRatio < 0.8) {
                        classification = "Excellent Short Straddle";
                    } else if (shortRatio < 1) {
                        classification = "Good Short Straddle";
                    } else {
                        // Add more specific classification for "none" cases
                        if (monthlyAbs < Math.min(longOneLeg, shortOneLeg)) {
                            classification = "Low Volatility - Wait";
                        } else {
                            classification = "Neutral - Monitor";
                        }
                    }
                } else {
                    // Add more specific reasons for N/A
                    if (monthlyAbs === null) {
                        classification = "No Historical Data";
                    } else {
                        classification = "Invalid Breakeven Range";
                    }
                }

                // Update weekly and monthly movement formatting to match the reference code
                const weekly = tickerData.historical?.last1WChange !== null
                    ? `${tickerData.historical.last1WChange.toFixed(2)}% (Open: ${tickerData.historical.last1WStartPrice ? tickerData.historical.last1WStartPrice.toFixed(2) : 'N/A'}, Close: ${tickerData.historical.last1WEndPrice ? tickerData.historical.last1WEndPrice.toFixed(2) : 'N/A'}, High: ${tickerData.historical.last1WHigh ? tickerData.historical.last1WHigh.toFixed(2) : 'N/A'}, Low: ${tickerData.historical.last1WLow ? tickerData.historical.last1WLow.toFixed(2) : 'N/A'})`
                    : 'N/A';
                const monthly = tickerData.historical?.last1MChange !== null
                    ? `${tickerData.historical.last1MChange.toFixed(2)}% (Open: ${tickerData.historical.last1MStartPrice ? tickerData.historical.last1MStartPrice.toFixed(2) : 'N/A'}, Close: ${tickerData.historical.last1MEndPrice ? tickerData.historical.last1MEndPrice.toFixed(2) : 'N/A'}, High: ${tickerData.historical.last1MHigh ? tickerData.historical.last1MHigh.toFixed(2) : 'N/A'}, Low: ${tickerData.historical.last1MLow ? tickerData.historical.last1MLow.toFixed(2) : 'N/A'})`
                    : 'N/A';


                const rowHtml = `
                    <tr class="hover:bg-gray-50">
                        <td class="border p-2 font-medium">${ticker}</td>
                        <td class="border p-2 text-nowrap">${tickerData.stockPrice.toFixed(2)}</td>
                        <td class="border p-2 text-nowrap">${selectedExpiry}</td>
                        <td class="border p-2 text-nowrap">${classification.includes('Short') ?
                        metrics.short.callPrice.toFixed(2) :
                        metrics.long.callPrice.toFixed(2)
                    }</td>
                        <td class="border p-2 text-nowrap">${classification.includes('Short') ?
                        metrics.short.putPrice.toFixed(2) :
                        metrics.long.putPrice.toFixed(2)
                    }</td>
                        <td class="border p-2 text-nowrap">${classification.includes('Short') ?
                        `<span class="text-green-700">+${metrics.short.totalCredit.toFixed(2)}</span>` :
                        `<span class="text-red-500">-${metrics.long.totalDebit.toFixed(2)}</span>`
                    }</td>
                        <td class="border p-2 text-nowrap">${classification.includes('Short') ?
                        `${metrics.short.lowerBreakeven.toFixed(2)} (${metrics.short.lowerBreakevenPerc.toFixed(2)}%) - 
                             ${metrics.short.upperBreakeven.toFixed(2)} (${metrics.short.upperBreakevenPerc.toFixed(2)}%)` :
                        `${metrics.long.lowerBreakeven.toFixed(2)} (${metrics.long.lowerBreakevenPerc.toFixed(2)}%) - 
                             ${metrics.long.upperBreakeven.toFixed(2)} (${metrics.long.upperBreakevenPerc.toFixed(2)}%)`
                    }</td>
                        <td class="border p-2 text-nowrap">${weekly}</td>
                        <td class="border p-2 text-nowrap">${monthly}</td>
                        <td class="border p-2 text-nowrap">${classification}</td>
                    </tr>
                `;

                if (classification === "Excellent Short Straddle" || classification === "Good Short Straddle") {
                    if (classification.startsWith("Excellent")) {
                        groups.short.excellent.push(rowHtml);
                    } else {
                        groups.short.good.push(rowHtml);
                    }
                } else if (classification === "Excellent Long Straddle" || classification === "Good Long Straddle") {
                    if (classification.startsWith("Excellent")) {
                        groups.long.excellent.push(rowHtml);
                    } else {
                        groups.long.good.push(rowHtml);
                    }
                } else {
                    groups.none.push(rowHtml);
                }
            });

            function generateTable(rowsArray) {
                return `
                    <div class="custom-scroll overflow-x-auto [&_th:first-child]:sticky [&_th:first-child]:left-0  [&_th:first-child]:bg-gray-100 [&_td:first-child]:sticky [&_td:first-child]:left-0 [&_td:first-child]:bg-white [&_td:first-child]:z-10 [&_th:first-child]:z-10 [&_td:first-child]:border-r ">
                        <table class="w-full border-collapse border text-xs mb-4" id="summary-table text-neutral-600 text-left">
                            <thead>
                                <tr class="bg-gray-100 text-neutral-700 text-left">
                                    <th class="border p-2 font-medium" data-col="ticker">Stock</th>
                                    <th class="border p-2 font-medium text-nowrap" data-col="stockPrice">Stock Price</th>
                                    <th class="border p-2 font-medium" data-col="expiry">Expiry</th>
                                    <th class="border p-2 font-medium text-nowrap" data-col="cePrice">CE ATM ${window.useLTPForPrices ? '(LTP)' : '(Bid/Ask)'}</th>
                                    <th class="border p-2  font-medium text-nowrap" data-col="pePrice">PE ATM ${window.useLTPForPrices ? '(LTP)' : '(Bid/Ask)'}</th>
                                    <th class="border p-2 font-medium text-nowrap" data-col="totalAmount">Total Debit/Credit</th>
                                    <th class="border p-2 font-medium  text-nowrap" data-col="breakevenRange">Breakeven Range</th>
                                    <th class="border p-2 font-medium  text-nowrap" data-col="weeklyMove">Weekly Movement</th>
                                    <th class="border p-2 font-medium  text-nowrap" data-col="monthlyMove">Monthly Movement</th>
                                    <th class="border p-2 font-medium " data-col="classification">Classification</th>
                                </tr>
                            </thead>
                            <tbody class="text-neutral-950">${rowsArray.join('')}</tbody>
                        </table>
                    </div>
                `;
            }

            let summaryHtml = '';

            if (groups.short.excellent.length || groups.short.good.length) {
                summaryHtml += `<h2 class="text-lg mb-2 font-semibold">Short Straddles</h2>`;
                if (groups.short.excellent.length) {
                    summaryHtml += `<h3 class="bg-green-100 font-medium mt-2 px-5 py-1.5 text-green-700 border-l-2 border-green-800">Excellent</h3>`;
                    summaryHtml += generateTable(groups.short.excellent);
                }
                if (groups.short.good.length) {
                    summaryHtml += `<h3 class="bg-yellow-100 font-medium mt-2 px-5 py-1.5 text-yellow-600 border-l-2 border-yellow-800">Good</h3>`;
                    summaryHtml += generateTable(groups.short.good);
                }
            }

            if (groups.long.excellent.length || groups.long.good.length) {
                summaryHtml += `<h2 class="text-lg mb-2 font-semibold">Long Straddles</h2>`;
                if (groups.long.excellent.length) {
                    summaryHtml += `<h3 class="bg-green-100 font-medium mt-2 px-5 py-1.5 text-green-700 border-l-2 border-green-800">Excellent</h3>`;
                    summaryHtml += generateTable(groups.long.excellent);
                }
                if (groups.long.good.length) {
                    summaryHtml += `<h3 class="bg-yellow-100 font-medium mt-2 px-5 py-1.5 text-yellow-600 border-l-2 border-yellow-800">Good</h3>`;
                    summaryHtml += generateTable(groups.long.good);
                }
            }

            if (groups.none.length) {
                summaryHtml += `<h2 class="bg-red-100 font-medium mt-2 px-5 py-1.5 text-red-600 border-l-2 border-red-800">No Matches</h2>`;
                summaryHtml += generateTable(groups.none);
            }

            document.getElementById("summaryTableContainer").innerHTML = summaryHtml;
            addSorting(document.getElementById("summary-table"));
        }

        // Add sorting functionality to tables
        function addSorting(tableEl) {
            if (!tableEl) return;

            const thList = tableEl.querySelectorAll("thead th[data-col]");
            thList.forEach((th) => {
                th.addEventListener("click", function () {
                    const colName = this.dataset.col;
                    let sortOrder = this.getAttribute("data-sort") || "desc";
                    sortOrder = sortOrder === "desc" ? "asc" : "desc";
                    this.setAttribute("data-sort", sortOrder);

                    const tbody = tableEl.querySelector("tbody");
                    const rows = Array.from(tbody.querySelectorAll("tr"));

                    rows.sort((a, b) => {
                        const aValue = parseFloat(
                            a.children[Array.from(thList).indexOf(this)].innerText.replace(
                                /[^0-9.-]+/g,
                                ""
                            )
                        );
                        const bValue = parseFloat(
                            b.children[Array.from(thList).indexOf(this)].innerText.replace(
                                /[^0-9.-]+/g,
                                ""
                            )
                        );
                        return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
                    });

                    tbody.innerHTML = "";
                    rows.forEach((row) => tbody.appendChild(row));
                });
            });
        }
    </script>

    {% include 'blocks/footer.html' %}
    </div>

</body>

</html>