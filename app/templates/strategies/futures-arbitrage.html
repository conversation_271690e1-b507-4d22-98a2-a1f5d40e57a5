<!DOCTYPE html>
<html lang="en">

<head>

    <title>Stocks vs Futures Arbitrage Opportunities | AI Bull</title>
    <meta name="description"
        content="Discover arbitrage opportunities between stocks and futures using advanced analysis. Find optimal strategies to profit from price discrepancies between stocks and their corresponding futures contracts." />
    <link rel="canonical" href="https://theaibull.com/strategies/futures-arbitrage" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.comstrategies/futures-arbitrage" />
    <meta property="og:title"
        content="Stocks vs Futures Arbitrage - Find Arbitrage Opportunities Between Stocks and Futures" />
    <meta property="og:description"
        content="Discover arbitrage opportunities between stocks and futures using advanced analysis. Find optimal strategies to profit from price discrepancies between stocks and their corresponding futures contracts." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title"
        content="Stocks vs Futures Arbitrage - Find Arbitrage Opportunities Between Stocks and Futures" />
    <meta name="twitter:description"
        content="Find arbitrage opportunities between stocks and futures using advanced analysis to optimize your trading strategy." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
</head>


<body
    class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100 [&_input[type=checkbox]]:[accent-color:#398dfb] [&_input[type=radio]]:[accent-color:#398dfb]">

    {% include 'blocks/common.html' %}
    {%include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <main>
            <div class="max-w-7xl mx-auto p-3 w-full">
                <!-- Page Title -->
                <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2  rounded-lg mb-3">
                    <div class="md:flex justify-between items-center">
                        <div class="flex items-center">
                            <h1 class="md:text-xl text-lg font-semibold tracking-tight">Stocks vs Futures Arbitrage</h1>
                            <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">Find arbitrage
                                opportunities
                                between stocks and futures</h2>
                            <!-- Button to trigger modal open -->
                            <button id="openModalBtn" onclick="openModal()" class="ml-2">

                                <div class="flex group text-sm">
                                    <div
                                        class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                        <div
                                            class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-circle-help">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                    <path d="M12 17h.01"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div
                                            class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                            <div
                                                class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                                How to Use This Page</div>
                                        </div>
                                    </div>
                                </div>
                            </button>
                        </div>

                    </div>
                </div>

                <div class="bg-white dark:bg-neutral-800 rounded-xl p-6">
                    <!-- Filter Box -->
                    <div class="mb-4">
                        <label for="filterInput" class="mr-2 font-medium">Minimum Difference %:</label>
                        <input id="filterInput" type="number" step="0.1" value="1"
                            class="border border-gray-300 dark:bg-neutral-700 dark:border-neutral-600 focus:border-blue-500 focus:outline-0 px-3 py-1.5 rounded-md transition-all w-[300px]">
                    </div>



                    <!-- Main Content Area -->
                    <div class="">
                        <div class="overflow-x-auto">
                            <table id="debugTable"
                                class="w-full border-collapse border dark:border-neutral-600 lg:text-base text-sm">
                                <thead class="">
                                    <tr>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50">
                                            Symbol</th>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50 text-nowrap">
                                            Stock Price</th>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50  text-nowrap">
                                            Future Price</th>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50 text-nowrap">
                                            Difference</th>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50 text-nowrap">
                                            Difference %</th>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50 text-nowrap">
                                            Recommended Action
                                        </th>
                                        <th
                                            class="border dark:border-neutral-600 p-3 text-center text-neutral-800 bg-gray-50 text-nowrap">
                                            Expiry Date
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in arbitrage_data %}
                                    <tr class="hover:bg-gray-50 dark:hover:bg-neutral-700">
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            <div class="font-medium">{{ item.symbol }}</div>
                                            {% if item.futures_display_name %}
                                            <div class="text-xs text-gray-500 dark:text-gray-400">{{
                                                item.futures_display_name }}</div>
                                            {% endif %}
                                        </td>
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            {{ item.stock_price | round(2) }}
                                        </td>
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            {{ item.futures_price | round(2) }}
                                        </td>
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            {{ item.difference | round(2) }}
                                        </td>
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            {{ item.difference_percent | round(2) }}%
                                        </td>
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            {{ item.action }}
                                        </td>
                                        <td class="border dark:border-neutral-600 p-3 text-center">
                                            {{ item.expiry_date }}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
                <!-- FAQ Section in Accordion Format -->
                <div class="mt-12">
                    <h2 class="text-2xl font-semibold mb-4">Frequently Asked Questions</h2>
                    <div class="space-y-4">
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What is Arbitrage?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300">
                                    Arbitrage is the practice of taking advantage of price differences between two or
                                    more markets by buying in one market and selling in another.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                What is Put Call Parity and how can I use it?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300">
                                    Put Call Parity defines the relationship between the price of European call and put
                                    options. You can use this strategy to identify mispricings. Learn more on our <a
                                        href="/strategies/put-call-parity" class="text-blue-600 hover:underline">Put
                                        Call Parity page</a>.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                How do I use this tool for arbitrage trading?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300">
                                    Use the filter to set the minimum percentage difference. The tool updates the table
                                    to show opportunities. It can be used for various strategies like long stock/short
                                    futures and others.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                Where does the data come from?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300">
                                    The stock and futures data are sourced from our real-time market APIs. There might
                                    be minor delays due to data processing.
                                </p>
                            </div>
                        </details>
                        <details class="border rounded-lg bg-white">
                            <summary class="cursor-pointer p-4 ">
                                Is the data real-time?
                            </summary>
                            <div class="p-4 border-t">
                                <p class="text-gray-700 dark:text-gray-300">
                                    Yes, the data is provided in real-time, although slight delays can occur due to API
                                    response times and market conditions.
                                </p>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
        </main>

        <div class="mt-auto">
            {% include 'blocks/footer.html' %}
        </div>

        <!-- Sorting and Filtering script -->
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                var tbody = document.querySelector("#debugTable tbody");
                var rows = Array.from(tbody.querySelectorAll("tr"));

                function filterAndSort() {
                    var filterVal = parseFloat(document.querySelector("#filterInput").value) || 0;

                    // Filter rows based on the absolute difference % (column index 4)
                    rows.forEach(function (row) {
                        var diffText = row.cells[4].innerText.replace("%", "");
                        var diffValue = parseFloat(diffText) || 0;
                        if (diffValue >= filterVal) {
                            row.style.display = "";
                        } else {
                            row.style.display = "none";
                        }
                    });

                    // Get only the visible rows for sorting
                    var visibleRows = rows.filter(row => row.style.display !== "none");
                    visibleRows.sort(function (a, b) {
                        var aText = a.cells[4].innerText.replace("%", "");
                        var bText = b.cells[4].innerText.replace("%", "");
                        var aVal = parseFloat(aText) || 0;
                        var bVal = parseFloat(bText) || 0;
                        return bVal - aVal; // descending order
                    });

                    // Clear tbody and append sorted visible rows
                    tbody.innerHTML = "";
                    visibleRows.forEach(function (row) {
                        tbody.appendChild(row);
                    });
                }

                // Initial filter and sort
                filterAndSort();

                // Re-filter and sort whenever the filter input changes
                document.querySelector("#filterInput").addEventListener("input", function () {
                    filterAndSort();
                });
            });
        </script>


    </div>
</body>

</html>