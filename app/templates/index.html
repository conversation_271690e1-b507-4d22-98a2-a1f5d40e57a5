<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Algo Trading, Options Trading & Algorithmic Trading in India | AI Bull</title>
    <meta name="description"
        content="Master algo trading and algorithmic trading with AIBull. Create and edit strategies for stock and options trading in India, leveraging computer programs to minimize human errors and optimize for market participants like pension funds and sell-side traders." />
    <link rel="canonical" href="https://theaibull.com" />
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:opsz,wght@12..96,200..800&display=swap" />
    {% include 'blocks/website/head.html' %}
</head>

<body>
    {% include 'blocks/menus/menu.html' %}
    <div class="max-w-7xl mx-auto">
        {% include 'blocks/menus/top-menu.html' %}
    </div>
    {% include 'blocks/menus/menu-scripts.html' %}
    {% include 'blocks/menus/mobile-menu.html' %}

    {% include 'blocks/website/hero1.html' %}

    <!-- Why AIBull -->
    <div class="bg-black pt-24 relative overflow-hidden">

        <div
            class="absolute inset-0 h-full w-full [mask:radial-gradient(50%_50%_at_50%,rgba(0,0,0,0.7)_35%,rgba(0,0,0,0)_100%)]">
            <div
                class="absolute inset-0 -z-10 h-full w-full text-zinc-300/10 [mask:linear-gradient(black,black_60%,transparent)]">
                <svg class="h-full w-full" aria-hidden="true">
                    <defs>
                        <pattern id="genius-pattern" width="44" height="44" x="50%" y="100%"
                            patternUnits="userSpaceOnUse">
                            <path d="M.5 200V.5H200" fill="none" stroke="currentColor"></path>
                        </pattern>
                    </defs>
                    <rect width="100%" height="100%" stroke-width="0" fill="url(#genius-pattern)"></rect>
                </svg>
            </div>
        </div>
        <div class="max-w-7xl mx-auto flex flex-col lg:flex-row items-center justify-between px-4">
            <div class="md:w-1/2 w-full relative hidden lg:block">
                <img src="{{ cdn('/static/images/why_aibull.png') }}" alt="Algorithmic Trading Platform" class="relative z-10">
                <img src="{{ cdn('/static/images/angle.png') }}"
                    class="absolute bottom-[-30px] hidden lg:hidden md:flex pr-5 right-[60px] xl:flex" alt="">
            </div>
            <div class="md:w-1/2 w-full">
                <div class="lg:max-w-md">
                    <p class="font-semibold mb-2 text-white/70 text-xl">Why Trade with</p>
                    <p class="font-bold heading mb-2 text-4xl text-white">AIBull for Algo Trading?</p>
                    <p class="text-base text-neutral-400">
                        AIBull is a leading algorithmic trading platform that uses advanced computer programs to empower
                        traders, including pension funds and sell-side market participants. Our platform leverages high
                        frequency trading (HFT) and algorithmic trading strategies to minimize human errors, offering
                        real-time market analysis, automated trading, and personalized solutions compliant with
                        securities and exchange regulations.
                    </p>
                    <p class="text-base font-medium mt-3 underline">Learn more</p>
                </div>

                <div class="flex flex-col lg:flex-row items-center justify-between mt-10">
                    <div class="w-full lg:w-[30%]">
                        <img src="{{ cdn('/static/images/up-arrow.png') }}" alt="" class="invert opacity-80 pr-8">
                    </div>
                    <div class="w-full lg:w-[70%]">
                        <p class="font-bold heading mb-2 text-4xl text-white">Trade with AIBull</p>
                        <p class="text-base text-neutral-400">
                            AIBull’s algorithmic trading platform uses AI-driven computer programs to help traders make
                            informed decisions. Trading is a method of executing orders with precision, using tools like
                            moving averages to analyze market trends, catering to both retail and institutional market
                            participants, including sell-side traders and pension funds.
                        </p>
                        <p class="text-base font-medium mt-3 underline">Learn more</p>
                    </div>
                </div>

                <img src="{{ cdn('/static/images/stars.png') }}" alt=""
                    class="absolute animate-pulse invert opacity-10 right-[60px] top-10">
            </div>

        </div>
    </div>

    <div>
        <!-- Features starts -->
        <div class="py-24 lg:px-0 px-5" style="
    background: linear-gradient(184deg, #f2efe9, #fdf9f1);
">
            {%include 'blocks/website/features.html' %}
        </div>
        <!-- FAQS starts -->
        {%include 'blocks/website/faqs.html' %}
        <!-- Testimonials starts -->
        {%include 'blocks/website/testimonials.html' %}
        <!-- blogs starts -->
        {%include 'blocks/website/blog-home.html' %}
        <!-- Footer starts -->
        {%include 'blocks/website/footer.html' %}
    </div>

    <style>
        .heading {
            font-family: Bricolage Grotesque, sans-serif !important;
        }

        body {
            font-family: "poppins-regular", sans-serif;
            font-weight: 400;
        }

        .hero-section {
            background: url(https://tradez-nuxt.vercel.app/images/hero_bg_vector.png);
            background-color: rgb(251 251 251 / 99%);
            background-position: 50%;
            background-repeat: no-repeat;
            background-size: cover;
        }

        .hero-section:before {
            background-image: linear-gradient(114deg, #f7eae4 45.73%, #dbdef0 75.72%);
            content: "";
            height: 100%;
            left: 0;
            position: absolute;
            top: 0;
            width: 45%;
            z-index: 0;
        }

        .hero-section:after {
            border-radius: 539px;
            content: "";
            filter: blur(200px);
            height: 539px;
            opacity: .35;
            position: absolute;
            right: 6%;
            top: 12%;
            width: 539px;
            z-index: -1;
        }
    </style>
</body>

</html>