<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Algo Strategy Editor | AI Bull</title>
    <meta name="description" content="Create and edit algo strategies for Stock and Option trading." />
    <link rel="canonical" href="https://theaibull.com/algos/options" />
    {% include 'blocks/head.html' %}
    <style>
        .push-btn {
            background-color: transparent;
            color: inherit;
            padding: 0.5rem 0.75rem;
            /* Reduced margin for a more compact look */
            cursor: pointer;
            transition: background-color 0.2s, border-color 0.2s;
        }

        .push-btn:hover {
            background-color: #f7f7f7;
        }

        .push-btn.active {
            background-color: #1a73e8;
            color: #ffffff;
        }

        /* Default (Desktop view) */
        .options-container {
            display: block;
        }

        /* Slide-out effect (Mobile view) */
        @media screen and (max-width: 1290px) {
            .options-container {
                position: fixed;
                top: 0;
                left: -100%;
                z-index: 30;
                width: 100%;
                height: 100%;
                background-color: white;
                transition: left 0.3s ease-in-out;
            }

            .options-container.open {
                left: 0;
            }
        }
    </style>
</head>

<body class="bg-gray-100 dark:bg-gray-900 dark:text-gray-100">
    {% include 'blocks/common.html' %}
    {% include 'blocks/onboarding.html' %}
    <div class="rhs-block duration-300 transition-width relative pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <div class="xl:flex min-h-screen">
            <!-- Left Column: Algo Strategies Table and Help Text -->

            <button
                class="mobile-toggle-btn font-medium text-base cursor-pointer xl:hidden w-full text-blue-800 p-2 text-left"
                onclick="toggleOptionsSlideout()" style="background-image: linear-gradient(45deg, #d0f1ff, #eff1f3);">
                <div class="flex items-center justify-between">
                    <p>Options</p>
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-menu-icon lucide-menu">
                            <line x1="4" x2="20" y1="12" y2="12" />
                            <line x1="4" x2="20" y1="6" y2="6" />
                            <line x1="4" x2="20" y1="18" y2="18" />
                        </svg>
                    </div>
                </div>
            </button>

            <div id="open"
                class="options-container w-64 min-h-screen bg-white dark:bg-neutral-800 border-r dark:border-neutral-700 fixed">
                <div class="flex items-center justify-between border-b border-gray-200 p-2.5">
                    <h1 class="text-lg font-semibold">Option Strategies</h1>
                    <button type="button" onclick="openStrategyModal()"
                        class="p-1 text-blue-600 bg-blue-50 text-sm font-medium px-3 rounded-md dark:hover:bg-neutral-700 hover:bg-blue-500 hover:text-white transition-all">
                        New
                    </button>
                </div>
                <div id="crud-loading" class="text-center my-4 hidden text-sm">
                    <svg class="animate-spin h-5 w-5 text-blue-500 mx-auto" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                        </circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z">
                        </path>
                    </svg>
                    <p>Loading...</p>
                </div>
                <div id="algoHelp" class="mb-4 text-sm text-gray-600 dark:text-gray-300 hidden">
                    No algo trading strategies available. <a href="javascript:void(0)" onclick="openStrategyModal()"
                        class="text-blue-600 hover:underline">Add Algo Trading Strategy</a>
                </div>
                <div id="strategyTable" class="overflow-x-auto hidden">
                    <table class="w-full text-left border-collapse text-sm">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-gray-700 cursor-none border-b">
                                <th class="p-2">Name</th>
                            </tr>
                        </thead>
                        <tbody id="algoList" class="divide-y divide-gray-200 dark:divide-gray-300"></tbody>
                    </table>
                </div>
            </div>

            <!-- Right Column: Strategy Form or FAQs -->
            <div class="flex-1 min-h-screen md:p-2 p-4 xl:pl-64 xl:ml-3">
                <div id="strategyFormContainer" class="hidden bg-white dark:bg-neutral-900 rounded-lg md:p-6 p-4">
                    <div class="flex flex-col md:flex-row gap-3 justify-between xl:items-center mb-4">
                        <h2 id="strategyTitle" class="text-xl font-semibold">New Algo Trading Strategy - Unsaved</h2>
                        <div class="flex items-center space-x-2">
                            <button type="button" id="backtestButton"
                                class="hidden px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center mr-2"
                                onclick="openBacktest()">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                                Backtest
                            </button>
                            <button type="button" id="deployButton"
                                class="hidden px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 flex items-center"
                                onclick="deployStrategy()">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                Deploy to Production
                            </button>
                            <button type="button" id="undeployButton"
                                class="hidden px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 flex items-center"
                                onclick="undeployStrategy()">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                Undeploy from Production
                            </button>
                        </div>
                    </div>
                    <div class="mb-6">
                        <p class="text-sm text-gray-600 dark:text-gray-300">
                            Build and customize <strong>algo trading</strong> strategies for <strong>stock
                                markets</strong> and options. Use <strong>computer programs</strong> to automate
                            <strong>trading orders</strong> across <strong>multiple markets</strong> with
                            <strong>defined sets</strong> of <strong>rules and instructions</strong>. This minimizes
                            <strong>human errors</strong> and streamlines your trading process.
                        </p>
                    </div>
                    <form id="algoStrategyForm" class="space-y-6" novalidate>
                        <!-- Strategy Name -->
                        <div>
                            <label class="block font-medium mb-2 text-sm" for="strategyName">Strategy Name</label>
                            <input type="text" id="strategyName" name="strategyName"
                                placeholder="Enter algo trading strategy name"
                                class="w-full px-2 text-sm py-2.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all"
                                required />
                            <p class="mt-1 text-neutral-600 text-xs">Enter a unique name for your algo trading strategy.
                            </p>
                        </div>

                        <!-- Trading Mode -->
                        <div>
                            <label class="block font-medium mb-2 text-sm">Trading Mode</label>
                            <div class="bg-gray-200 font-medium inline-flex rounded-md text-sm">
                                <button type="button" class="push-btn rounded-l-md"
                                    onclick="selectTradingMode('Delivery')">Delivery</button>
                                <button type="button" class="push-btn rounded-r-md"
                                    onclick="selectTradingMode('Intraday')">Intraday</button>
                            </div>
                            <p class="mt-1 text-neutral-600 text-xs">Delivery mode supports longer-term positions in
                                stock markets, while Intraday mode is for trades opened and closed within the same day
                                to minimize human errors.</p>
                        </div>



                        <!-- Option Details: Leg Details -->
                        <div id="optionSection" class="border-t pt-4">
                            <h3 class="text-lg font-semibold">Leg Details for Algorithmic Trading</h3>
                            <p class="mt-1 text-neutral-600 text-xs mb-4">Configure each leg for your algorithmic
                                trading strategy. Set expiry, action, option type, number of lots, and strike criteria
                                to execute trading orders efficiently across multiple markets.</p>

                            <div id="legsContainer" class="space-y-4"></div>
                            <button type="button" onclick="addLeg()"
                                class="bg-blue-50 dark:hover:bg-neutral-700 font-medium hover:bg-blue-500 hover:text-white mt-3 p-1 px-3 px-4 py-1.5 rounded-md text-blue-600 text-sm transition-all">
                                Add Leg
                            </button>
                        </div>

                        <!-- Rebalancing Conditions -->
                        {% include 'algos/options/blocks/rebalancing.html' %}

                        <!-- Entry Conditions -->
                        <div id="entryConditions" class="border-t pt-4 space-y-4">
                            <h3 class="text-lg font-semibold">Entry Conditions</h3>
                            <p class="mt-1 text-neutral-600 text-xs">Define rules and instructions for when your algo
                                trading strategy should enter a trade in stock markets. Set specific calendar days/times
                                or use chart and candlestick triggers.</p>
                            <div class="flex flex-wrap gap-3 md:gap-0 items-center">
                                <input type="checkbox" id="checkCalendarDay" class="form-checkbox mr-2">
                                <label for="checkCalendarDay" class="text-sm font-medium">Calendar Day:</label>
                                <input type="number" id="entryDay" placeholder="e.g., 15"
                                    class="w-24 px-2 text-sm py-1.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all ml-2"
                                    disabled required>
                                <div>
                                    <label class="text-sm font-medium ml-2">Time:</label>
                                    <input type="time" id="entryTime"
                                        class="w-24 px-3 text-sm py-1.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all ml-2"
                                        value="09:25" disabled required>
                                </div>
                            </div>
                        </div>

                        <!-- Exit Conditions -->
                        <div id="exitConditions" class="border-t pt-4">
                            <h3 class="text-lg font-semibold">Exit Conditions</h3>
                            <p class="mt-1 text-neutral-600 text-xs">Set defined sets of rules to exit trades, including
                                Stop Loss and Book Profits, to optimize your algo trading strategy and reduce human
                                errors.</p>
                            <div class="flex flex-wrap gap-3 md:gap-0 items-center mb-4 mt-4">
                                <input type="checkbox" id="exitStopLossCheck" class="form-checkbox mr-2">
                                <label for="exitStopLossCheck" class="text-sm font-medium">Stop Loss</label>
                                <div id="exitStopLossOptions" class="flex items-center md:ml-4" style="display: none;">
                                    <div
                                        class="px-2 bg-gray-100 text-sm py-1.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                                        <select id="stopLossType" class="outline-0 bg-transparent">
                                            <option value="fixed">Fixed</option>
                                            <option value="percent" selected>Premium %</option>
                                        </select>
                                    </div>
                                    <input type="text" id="stopLossValue" placeholder="15"
                                        class="px-2 w-32 text-sm py-1.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all ml-2"
                                        value="15" required>
                                </div>
                            </div>
                            <div class="flex flex-wrap gap-3 md:gap-0 items-center">
                                <input type="checkbox" id="exitBookProfitsCheck" class="form-checkbox mr-2">
                                <label for="exitBookProfitsCheck" class="text-sm font-medium">Book Profits</label>
                                <div id="exitBookProfitsOptions" class="flex items-center md:ml-4"
                                    style="display: none;">
                                    <div
                                        class="px-2 bg-gray-100 text-sm py-1.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                                        <select id="targetType" class="outline-0 bg-transparent">
                                            <option value="fixed">Fixed</option>
                                            <option value="percent" selected>Premium %</option>
                                        </select>
                                    </div>
                                    <input type="text" id="targetValue" placeholder="60"
                                        class="px-2 text-sm  w-32 py-1.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all ml-2"
                                        value="60" required>
                                </div>
                            </div>
                        </div>

                        <div class="flex space-x-2 justify-end border-t pt-4">
                            <button type="button" id="deleteStrategyBtn"
                                class="hidden px-4 py-2 bg-red-100 text-red-600 hover:bg-red-500 hover:text-white rounded-md text-sm hover:bg-red-700"
                                onclick="deleteStrategy()">Delete</button>
                            <button type="submit" id="saveStrategyButton"
                                class="bg-blue-500 font-medium hover:bg-blue-600 px-4 py-2 rounded-md text-sm text-white">
                                Save
                            </button>

                        </div>
                    </form>
                </div>
                {% include 'algos/options/blocks/faqs.html' %}
            </div>
        </div>
    </div>
    {% include 'blocks/footer.html' %}
    {% include 'algos/options/blocks/crud.html' %}
    {% include 'algos/options/blocks/popular-strategies-modal.html' %}
    <script>
        // --- Push Button Helper Variables ---
        let selectedTradingMode = "Delivery";
        let selectedStrategyType = "option"; // default to stock for new ones
        let selectedStockSide = "Buy";
        let selectedStockMetric = "quantity"; // "quantity" or "investment"
        let selectedStopLossType = "percent";
        let selectedTargetType = "percent";

        // --- Push Button Functions ---
        function selectTradingMode(mode) {
            selectedTradingMode = mode;
            document.querySelectorAll('button[onclick^="selectTradingMode"]').forEach(btn => btn.classList.remove('active'));
            if (mode === "Delivery") {
                document.querySelector('button[onclick="selectTradingMode(\'Delivery\')"]').classList.add('active');
            } else {
                document.querySelector('button[onclick="selectTradingMode(\'Intraday\')"]').classList.add('active');
            }
        }

        function selectExitType(which, type) {
            if (which === "stopLoss") {
                selectedStopLossType = type;
                document.getElementById("stopLossType").value = type;
            } else if (which === "target") {
                selectedTargetType = type;
                document.getElementById("targetType").value = type;
            }
        }
        // --- Checkbox Listeners for Entry & Exit Conditions ---
        document.getElementById("checkCalendarDay").addEventListener("change", function () {
            const enabled = this.checked;
            document.getElementById("entryDay").disabled = !enabled;
            document.getElementById("entryTime").disabled = !enabled;
        });

        // Exit Conditions checkboxes
        document.getElementById("exitStopLossCheck").addEventListener("change", function () {
            document.getElementById("exitStopLossOptions").style.display = this.checked ? "flex" : "none";
        });
        document.getElementById("exitBookProfitsCheck").addEventListener("change", function () {
            document.getElementById("exitBookProfitsOptions").style.display = this.checked ? "flex" : "none";
        });

        // --- Toggle Options Slideout ---
        function toggleOptionsSlideout() {
            const optionsContainers = document.querySelectorAll('.options-container');
            optionsContainers.forEach(container => {
                container.classList.toggle('open');
            });
        }

        // Close the slideout when clicking inside the slideout
        const optionsContainers = document.querySelectorAll('.options-container');
        optionsContainers.forEach(container => {
            container.addEventListener('click', function (event) {
                // Check if the click is on the container itself, not a child element
                if (event.target === container) {
                    container.classList.remove('open');
                }
            });
        });

        // Function to open the backtesting page with the current strategy
        function openBacktest() {
            if (currentStrategyId) {
                window.location.href = `/algos/backtesting?s=${currentStrategyId}`;
            } else {
                showAlert("Please save the strategy first before backtesting.");
            }
        }
    </script>
</body>

</html>