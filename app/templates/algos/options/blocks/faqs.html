<!-- FAQs Section in Accordion Format -->
<div id="faqContainer" class="mt-8 text-sm">
    <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
    <div class="space-y-4">
        <details class="border rounded-lg bg-white">
            <summary class="cursor-pointer p-4">
                What is an Algo Strategy?
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    An Algo Strategy is a set of pre-defined trading rules and conditions that can be automated to
                    execute trades in the market.
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white">
            <summary class="cursor-pointer p-4">
                What is Backtesting?
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    Backtesting is the process of testing a trading strategy on historical data to evaluate its
                    performance before deploying it live.
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white">
            <summary class="cursor-pointer p-4">
                How does it work?
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    The strategy is programmed with specific indicators and rules. It automatically scans the market
                    data, executes trades based on those rules, and can also perform risk management tasks.
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white">
            <summary class="cursor-pointer p-4">
                How do I create a new strategy?
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    Click on the “+ New” button on the left to open the strategy creation form where you can define all
                    parameters and settings.
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white">
            <summary class="cursor-pointer p-4">
                What is backtesting and why is it important?
            </summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    Backtesting allows you to simulate your strategy against historical data. This helps in
                    understanding its viability and adjusting parameters before risking real capital.
                </p>
            </div>
        </details>
    </div>
</div>