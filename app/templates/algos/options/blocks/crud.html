<script>
    // CRUD Logic for Algo Strategies
    const crud = new Crud("algo_strategies");
    let currentStrategyId = null;
    let legs = [];

    async function fetchStrategies() {
        // Check loggin status
        if (!isLoggedIn()) {
            return;
        }
        const spinner = document.getElementById("crud-loading");
        spinner.classList.remove("hidden");
        try {
            const response = await crud.get({
                filters: {
                    instrument: "option",
                },
            });
            const algoList = document.getElementById("algoList");
            const strategyTable = document.getElementById("strategyTable");
            const algoHelp = document.getElementById("algoHelp");
            algoList.innerHTML = "";
            if (response?.results?.length) {
                strategyTable.classList.remove("hidden");
                algoHelp.classList.add("hidden");
                response.results.forEach((strategy) => {
                    const tr = document.createElement("tr");
                    tr.classList.add("cursor-pointer", "hover:bg-gray-50", "dark:hover:bg-gray-700");
                    tr.setAttribute("data-strategy-id", strategy.uid);
                    if (strategy.uid === currentStrategyId) {
                        tr.classList.add("selected", "bg-blue-50", "dark:bg-blue-900");
                    }
                    tr.onclick = () => {
                        document.querySelectorAll("#algoList tr").forEach((row) => row.classList.remove("selected", "bg-blue-50", "dark:bg-blue-900"));
                        tr.classList.add("selected", "bg-blue-50", "dark:bg-blue-900");
                        loadStrategy(`${strategy.uid}`);
                    };
                    tr.innerHTML = `
                        <td class="p-2">
                            ${strategy.name || `Strategy ${strategy.uid}`}
                            ${strategy.isDeployed ? ' <span class="ml-2 px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full">Live</span>' : ''}
                        </td>
                    `;
                    algoList.appendChild(tr);
                });
            } else {
                strategyTable.classList.add("hidden");
                algoHelp.classList.remove("hidden");
            }

            // Set the first strategy as selected if none is selected
            if (!currentStrategyId) {
                // Click the first row
                algoList.querySelector("tr")?.click();
            }
        } catch (error) {
            console.error("Error fetching strategies:", error);
        } finally {
            spinner.classList.add("hidden");
        }
    }

    function loadCreateForm(resetId = true) {
        document.getElementById("algoStrategyForm").reset();
        document.getElementById("legsContainer").innerHTML = "";
        document.getElementById("strategyTitle").textContent = "New Strategy - Unsaved";
        // Clear highlighted leg
        document.querySelectorAll("#algoList tr").forEach((row) => row.classList.remove("selected", "bg-blue-50", "dark:bg-blue-900"));

        // Clear any existing rebalancing rules
        document.getElementById("rebalancingRulesContainer").innerHTML = "";

        legs = [];
        if (resetId) {
            currentStrategyId = null;
        }
        // Set defaults for new strategy: Delivery and Stock
        selectTradingMode("Delivery");
        // Reset checkboxes and their related fields
        document.getElementById("checkCalendarDay").checked = false;
        document.getElementById("entryDay").disabled = true;
        document.getElementById("entryDay").value = "";
        document.getElementById("entryTime").disabled = true;
        document.getElementById("entryTime").value = "09:25";

        document.getElementById("exitStopLossCheck").checked = false;
        document.getElementById("exitStopLossOptions").style.display = "none";
        document.getElementById("exitBookProfitsCheck").checked = false;
        document.getElementById("exitBookProfitsOptions").style.display = "none";

        // Hide buttons for new strategies
        document.getElementById("deployButton").classList.add("hidden");
        document.getElementById("backtestButton").classList.add("hidden");
        document.getElementById("undeployButton").classList.add("hidden");

        document.getElementById("saveStrategyButton").classList.remove("hidden");
        document.getElementById("deleteStrategyBtn").classList.add("hidden");
        document.getElementById("faqContainer").classList.add("hidden");
        document.getElementById("strategyFormContainer").classList.remove("hidden");
    }

    async function loadStrategy(id) {
        try {
            const strategy = await crud.getDocument(id);
            currentStrategyId = id;
            loadCreateForm(false);
            document.getElementById("strategyName").value = strategy.name;

            // Highlight the row in the table.
            document.querySelectorAll("#algoList tr").forEach(row => {
                row.classList.remove("selected", "bg-blue-50", "dark:bg-blue-900");
            });
            document.getElementById("algoList").querySelector(`tr[data-strategy-id="${id}"]`).classList.add("selected", "bg-blue-50", "dark:bg-blue-900");

            if (strategy.tradingMode) {
                selectTradingMode(strategy.tradingMode);
            } else {
                selectTradingMode("Delivery");
            }

            if (strategy.instrument === "option") {
                legs = strategy.legs || [];
                renderLegs();
            } else if (strategy.instrument === "stock" && strategy.stockDetails) {
                return;
            }
            if (strategy.entryConditions) {
                // Calendar Day
                if (strategy.entryConditions.calendarDayEnabled) {
                    document.getElementById("checkCalendarDay").checked = true;
                    document.getElementById("entryDay").disabled = false;
                    document.getElementById("entryTime").disabled = false;
                    document.getElementById("entryDay").value = strategy.entryConditions.dayOfMonth || "";
                    document.getElementById("entryTime").value = strategy.entryConditions.time || "09:25";
                } else {
                    document.getElementById("checkCalendarDay").checked = false;
                    document.getElementById("entryDay").disabled = true;
                    document.getElementById("entryTime").disabled = true;
                    document.getElementById("entryDay").value = "";
                    document.getElementById("entryTime").value = "09:25";
                }

                // Exit Conditions
                if (strategy.exitStopLossEnabled) {
                    document.getElementById("exitStopLossCheck").checked = true;
                    document.getElementById("exitStopLossOptions").style.display = "flex";
                } else {
                    document.getElementById("exitStopLossCheck").checked = false;
                    document.getElementById("exitStopLossOptions").style.display = "none";
                }
                if (strategy.exitBookProfitsEnabled) {
                    document.getElementById("exitBookProfitsCheck").checked = true;
                    document.getElementById("exitBookProfitsOptions").style.display = "flex";
                } else {
                    document.getElementById("exitBookProfitsCheck").checked = false;
                    document.getElementById("exitBookProfitsOptions").style.display = "none";
                }
                if (strategy.stopLossType) {
                    selectExitType("stopLoss", strategy.stopLossType);
                }
                document.getElementById("stopLossValue").value = strategy.stopLossValue || "15";
                if (strategy.targetType) {
                    selectExitType("target", strategy.targetType);
                }
                document.getElementById("targetValue").value = strategy.targetValue || "50";
                document.getElementById("deleteStrategyBtn").classList.remove("hidden");
                document.getElementById("strategyTitle").textContent = `Edit Strategy - ${strategy.name}`;
                document.getElementById("faqContainer").classList.add("hidden");
                document.getElementById("strategyFormContainer").classList.remove("hidden");

                // Show/hide deploy button based on deployment status
                const deployButton = document.getElementById("deployButton");
                const undeployButton = document.getElementById("undeployButton");
                const backtestButton = document.getElementById("backtestButton");

                // Always show the backtest button for existing strategies
                backtestButton.classList.remove("hidden");

                if (strategy.isDeployed) {
                    deployButton.classList.add("hidden");
                    undeployButton.classList.remove("hidden");
                } else {
                    deployButton.classList.remove("hidden");
                    undeployButton.classList.add("hidden");
                }
            }

            // Load rebalancing rules if available and the function exists
            document.getElementById("rebalancingRulesContainer").innerHTML = "";
            if (strategy.rebalancing) {
                setRebalancingRulesData(strategy.rebalancing);
            }

        } catch (error) {
            console.error("Error loading strategy:", error);
        }
    }

    document.querySelectorAll('button[onclick^="selectStrategyType"]').forEach(btn => {
        btn.addEventListener("click", function () { });
    });

    function instrumentChanged(value) {
        if (value === "option") {
            document.getElementById("optionSection").classList.remove("hidden");
            document.getElementById("stockSection").classList.add("hidden");
        } else if (value === "stock") {
            document.getElementById("stockSection").classList.remove("hidden");
            document.getElementById("optionSection").classList.add("hidden");
        }
    }

    // Modify the addLeg function to save and restore rebalancing state
    function addLeg() {
        let prevRebalancingRules = getRebalancingRulesData();
        const leg = {
            id: Date.now(),
            lots: 1,
            action: "buy",
            optionType: "Put",
            expiry: "This Week",
            strikeConfig: { condition: "is", value: "ATM" },
        };
        legs.push(leg);
        renderLegs();
        setRebalancingRulesData(prevRebalancingRules);
    }

    // Modify moveLegUp to save and restore rebalancing state
    function moveLegUp(index) {
        if (index > 0) {
            let prevRebalancingRules = getRebalancingRulesData();
            [legs[index - 1], legs[index]] = [legs[index], legs[index - 1]];
            renderLegs();
            setRebalancingRulesData(prevRebalancingRules);
        }
    }

    // Modify moveLegDown to save and restore rebalancing state
    function moveLegDown(index) {
        if (index < legs.length - 1) {
            let prevRebalancingRules = getRebalancingRulesData();
            [legs[index], legs[index + 1]] = [legs[index + 1], legs[index]];
            renderLegs();
            setRebalancingRulesData(prevRebalancingRules);
        }
    }

    // Modify removeLeg to save and restore rebalancing state
    function removeLeg(id) {
        let prevRebalancingRules = getRebalancingRulesData();
        legs = legs.filter(l => l.id !== id);
        renderLegs();
        setRebalancingRulesData(prevRebalancingRules);
    }

    // Modify updateLeg to save and restore rebalancing state if needed
    function updateLeg(id, field, value) {
        // Only save/restore state if it's changing option type which might affect labels
        const needsRebalancingPreservation = field === 'optionType' || field === 'action';
        let prevRebalancingRules; // Declare variable outside the if block so it's available later

        if (needsRebalancingPreservation) {
            prevRebalancingRules = getRebalancingRulesData();
        }

        const leg = legs.find(l => l.id === id);
        if (leg) {
            if (field === "lots") {
                leg[field] = parseInt(value) || 1; // Ensure lots is a number
            } else {
                leg[field] = value;
            }
            renderLegs();
        }

        if (needsRebalancingPreservation && prevRebalancingRules) {
            setRebalancingRulesData(prevRebalancingRules);
        }
    }

    function renderLegs() {
        const container = document.getElementById("legsContainer");
        container.innerHTML = "";
        legs.forEach((leg, index) => {
            const legDiv = document.createElement("div");
            legDiv.className = "group relative bg-white p-4 rounded-md mb-4 border";
            legDiv.innerHTML = `
        <div class="absolute -top-2 -left-2 bg-gray-200 text-gray-900 text-xs w-5 h-5 rounded-full flex items-center justify-center">
          ${index + 1}
        </div>
        <button type="button" onclick="removeLeg(${leg.id})" class="absolute -top-2 -right-2 text-gray-600 hover:text-red-500 bg-gray-200 rounded-full p-0.5 hidden group-hover:block">
         <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
        </button>
        <div class="absolute top-8 right-0 flex flex-col items-end space-y-1 hidden group-hover:flex">
          <button type="button" onclick="moveLegUp(${index})" class="text-gray-500 hover:text-gray-700 text-xs" title="Move Up">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-move-up"><path d="M8 6L12 2L16 6"/><path d="M12 2V22"/></svg>
            </button>
          <button type="button" onclick="moveLegDown(${index})" class="text-gray-500 hover:text-gray-700 text-xs" title="Move Down">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-move-down"><path d="M8 18L12 22L16 18"/><path d="M12 2V22"/></svg>
            </button>
        </div>
        <div class="md:flex grid grid-cols-2 items-center md:space-x-4 gap-3 md:gap-0 text-sm">
          <div>
            <label class="block text-sm font-medium mb-2">Expiry</label>
            <div class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
            <select onchange="updateLeg(${leg.id}, 'expiry', this.value)" class="bg-transparent">
              <option value="This Week" ${leg.expiry === "This Week" ? "selected" : ""}>This Week</option>
              <option value="Week (+1)" ${leg.expiry === "Week (+1)" ? "selected" : ""}>Week (+1)</option>
              <option value="Week (+2)" ${leg.expiry === "Week (+2)" ? "selected" : ""}>Week (+2)</option>
              <option value="Week (+3)" ${leg.expiry === "Week (+3)" ? "selected" : ""}>Week (+3)</option>
              <option value="Week (+4)" ${leg.expiry === "Week (+4)" ? "selected" : ""}>Week (+4)</option>
              <option value="Week (+5)" ${leg.expiry === "Week (+5)" ? "selected" : ""}>Week (+5)</option>
              <option value="This Month" ${leg.expiry === "This Month" ? "selected" : ""}>This Month</option>
              <option value="Month (+1)" ${leg.expiry === "Month (+1)" ? "selected" : ""}>Month (+1)</option>
              <option value="Month (+2)" ${leg.expiry === "Month (+2)" ? "selected" : ""}>Month (+2)</option>
            </select>
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">Action</label>
            <div class="flex space-x-1">
              <button type="button" onclick="updateLeg(${leg.id}, 'action', 'buy')" class="px-3 py-2 rounded ${leg.action === 'buy' ? 'bg-green-500 text-white' : 'bg-green-100 hover:bg-green-200 text-neutral-900'}">B</button>
              <button type="button" onclick="updateLeg(${leg.id}, 'action', 'sell')" class="px-3 py-2 rounded ${leg.action === 'sell' ? 'bg-red-500 text-white' : 'bg-red-100 hover:bg-red-200 text-neutral-900'}">S</button>
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">Type</label>
           <div class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
            <select onchange="updateLeg(${leg.id}, 'optionType', this.value)" class="bg-transparent outline-0">
              <option value="Put" ${leg.optionType === "Put" ? "selected" : ""}>Put</option>
              <option value="Call" ${leg.optionType === "Call" ? "selected" : ""}>Call</option>
              <option value="Future" ${leg.optionType === "Future" ? "selected" : ""}>Future</option>
            </select>
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">Lots</label>
            <input type="number" value="${leg.lots}" min="1" onchange="updateLeg(${leg.id}, 'lots', this.value)" class="w-20 px-3 text-sm py-1.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
          </div>
        </div>
        ${leg.optionType === "Future" ? "" : `<div class="mt-2">${strikeConfigTemplate(leg)}</div>`}
      `;
            container.appendChild(legDiv);
        });
    }

    function strikeConfigTemplate(leg) {
        let strikeHTML = `<div class="flex flex-col md:flex-row gap-3 md:gap-0 md:items-center md:space-x-2">
      <label class="text-sm font-medium">Strike:</label><div class="px-2 bg-gray-100 text-sm py-1.5 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
      <select onchange="updateLegStrike(${leg.id}, 'condition', this.value)" class="bg-transparent outline-0">
        <option value="is" ${leg.strikeConfig.condition === "is" ? "selected" : ""}>is</option>
        <option value="premium" ${leg.strikeConfig.condition === "premium" ? "selected" : ""}>premium</option>
        <option value="delta" ${leg.strikeConfig.condition === "delta" ? "selected" : ""}>delta</option>
      </select></div>`;
        if (leg.strikeConfig.condition === "is") {
            strikeHTML += `
            <div class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
            <select onchange="updateLegStrike(${leg.id}, 'value', this.value)" class="bg-transparent outline-0">
        ${[
                    "ITM20", "ITM19", "ITM18", "ITM17", "ITM16", "ITM15", "ITM14", "ITM13", "ITM12", "ITM11",
                    "ITM10", "ITM9", "ITM8", "ITM7", "ITM6", "ITM5", "ITM4", "ITM3", "ITM2", "ITM1",
                    "ATM",
                    "OTM1", "OTM2", "OTM3", "OTM4", "OTM5", "OTM6", "OTM7", "OTM8", "OTM9", "OTM10",
                    "OTM11", "OTM12", "OTM13", "OTM14", "OTM15", "OTM16", "OTM17", "OTM18", "OTM19", "OTM20"
                ].map(item => `<option value="${item}" ${leg.strikeConfig.value === item ? "selected" : ""}>${item}</option>`).join('')}
      </select></div>`;
        } else if (leg.strikeConfig.condition === "premium" || leg.strikeConfig.condition === "delta") {
            strikeHTML += `<div class="px-3 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                <select onchange="updateLegStrike(${leg.id}, 'criterion', this.value)" class="bg-transparent outline-0">
        <option value="closest" ${leg.strikeConfig.criterion === "closest" ? "selected" : ""}>Is closest to</option>
        <option value="gte" ${leg.strikeConfig.criterion === "gte" ? "selected" : ""}>Is greater than or equal to</option>
        <option value="lte" ${leg.strikeConfig.criterion === "lte" ? "selected" : ""}>Is less than or equal to</option>
      </select></div>
      <input type="text" value="${leg.strikeConfig.value}" placeholder="Value" onchange="updateLegStrike(${leg.id}, 'value', this.value)" class="w-20 px-3 text-sm py-1.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all" style="width:80px;">`;
        }
        strikeHTML += `</div>`;
        return strikeHTML;
    }

    function updateLegStrike(id, field, value) {
        const leg = legs.find(l => l.id === id);
        if (leg) {
            leg.strikeConfig[field] = value;
            renderLegs();
        }
    }

    async function saveStrategy() {
        // Check loggin status
        if (!isLoggedIn()) {
            showModal("Please Login/Signup to access this feature");
            return;
        }
        // HTML5 validations
        const form = document.getElementById("algoStrategyForm");
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Custom validations for triggers and calendar day if enabled
        if (document.getElementById("checkCalendarDay").checked) {
            if (!document.getElementById("entryDay").value || !document.getElementById("entryTime").value) {
                showAlert("Calendar day is enabled, please fill in day and time.");
                return;
            }
        }

        // Validate rebalancing rules if applicable
        if (selectedStrategyType === "option") {
            // Only validate if we have rebalancing rules and legs
            const rebalancingContainer = document.getElementById('rebalancingRulesContainer');
            if (rebalancingContainer && rebalancingContainer.querySelectorAll('.rebalancing-rule').length > 0) {
                if (!validateRebalancingRules()) {
                    // If validation fails, don't proceed with saving
                    return;
                }
            }
        }

        const strategyName = document.getElementById("strategyName").value.trim();
        if (!strategyName) {
            showAlert("Strategy name is required.");
            return;
        }
        const strategyType = selectedStrategyType;
        const tradingMode = selectedTradingMode;

        // Gather checkbox states and related values
        const calendarDayEnabled = document.getElementById("checkCalendarDay").checked;
        const exitStopLossEnabled = document.getElementById("exitStopLossCheck").checked;
        const exitBookProfitsEnabled = document.getElementById("exitBookProfitsCheck").checked;

        const formData = {
            uid: currentStrategyId || generateUUID(),
            instrument: strategyType,
            name: strategyName,
            tradingMode,
            entryConditions: {
                calendarDayEnabled: calendarDayEnabled,
                dayOfMonth: calendarDayEnabled ? document.getElementById("entryDay").value : "",
                time: calendarDayEnabled ? document.getElementById("entryTime").value : "",
            },
            exitStopLossEnabled: exitStopLossEnabled,
            exitBookProfitsEnabled: exitBookProfitsEnabled,
            stopLossType: document.getElementById("stopLossType").value,
            stopLossValue: document.getElementById("stopLossValue").value,
            targetType: document.getElementById("targetType").value,
            targetValue: document.getElementById("targetValue").value,
            rebalancing: getRebalancingRulesData()
        };

        formData.legs = legs;

        try {
            if (currentStrategyId) {
                await crud.update(currentStrategyId, formData);
                showAlert("Strategy updated successfully!");
            } else {
                await crud.create(formData);
                showAlert("Strategy created successfully!");
            }
            await fetchStrategies();
            loadStrategy(formData.uid);
            document.getElementById("faqContainer").classList.remove("hidden");
            document.getElementById("strategyFormContainer").classList.add("hidden");
        } catch (error) {
            console.error("Error saving strategy:", error);
            showAlert("Error saving strategy");
        }
    }

    async function deleteStrategy() {
        if (!currentStrategyId) return;
        const confirmed = await showConfirmDialog({
            title: 'Delete Strategy',
            message: 'Are you sure you want to delete this strategy?',
            confirmText: 'Delete',
            cancelText: 'Cancel',
            type: 'danger'
        });
        if (!confirmed) return;
        try {
            await crud.delete(currentStrategyId);
            showAlert("Strategy deleted successfully!");
            fetchStrategies();
            loadCreateForm();
            document.getElementById("faqContainer").classList.remove("hidden");
            document.getElementById("strategyFormContainer").classList.add("hidden");
        } catch (error) {
            console.error("Error deleting strategy:", error);
            showAlert("Error deleting strategy");
        }
    }

    async function deployStrategy() {
        if (!currentStrategyId) return;

        const confirmed = await showConfirmDialog({
            title: 'Deploy Strategy',
            message: 'Are you sure you want to deploy this strategy to production? This will start executing trades based on this strategy.',
            confirmText: 'Deploy',
            cancelText: 'Cancel',
            type: 'warning'
        });

        if (!confirmed) return;

        try {
            await crud.update(currentStrategyId, { isDeployed: true });
            showAlert("Strategy deployed successfully!");
            await fetchStrategies();
            await loadStrategy(currentStrategyId);
        } catch (error) {
            console.error("Error deploying strategy:", error);
            showAlert("Error deploying strategy");
        }
    }

    async function undeployStrategy() {
        if (!currentStrategyId) return;

        const confirmed = await showConfirmDialog({
            title: 'Undeploy Strategy',
            message: 'Are you sure you want to undeploy this strategy from production? This will halt all trading for this strategy.',
            confirmText: 'Undeploy',
            cancelText: 'Cancel',
            type: 'warning'
        });

        if (!confirmed) return;

        try {
            await crud.update(currentStrategyId, { isDeployed: false });
            showAlert("Strategy undeployed successfully!");
            await fetchStrategies();
            await loadStrategy(currentStrategyId);
        } catch (error) {
            console.error("Error undeploying strategy:", error);
            showAlert("Error undeploying strategy");
        }
    }

    document.getElementById("algoStrategyForm").addEventListener("submit", function (e) {
        e.preventDefault();
        saveStrategy();
    });

    fetchStrategies();
    document.getElementById("faqContainer").classList.remove("hidden");
    document.getElementById("strategyFormContainer").classList.add("hidden");
</script>