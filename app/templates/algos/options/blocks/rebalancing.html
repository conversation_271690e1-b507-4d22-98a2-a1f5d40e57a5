<div id="rebalancingConditions" class="border-t pt-4">
    <h3 class="text-lg font-semibold">Rebalancing Conditions</h3>
    <p class="mt-1 text-neutral-600 text-xs mb-4">Define conditions for rebalancing strategy legs during backtesting.
        These rules will be evaluated against new options in the chain as market conditions change.</p>

    <!-- Warning message about rebalancing and leg changes -->
    <div class="bg-amber-50 border-l-4 border-amber-500 p-4 mb-4 dark:bg-amber-900/30 dark:border-amber-600">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-amber-800 dark:text-amber-300">Important Note</h3>
                <div class="mt-1 text-sm text-amber-700 dark:text-amber-200">
                    When you add, remove, or modify strategy legs, please review your rebalancing rules to ensure they
                    still reference the correct legs. Rebalancing rules are tied to specific legs by their ID, and
                    changes to legs may require adjustments to your rebalancing conditions.
                </div>
            </div>
        </div>
    </div>

    <div id="rebalancingRulesContainer" class="space-y-4 my-4">
        <!-- Rules will be dynamically added here -->
    </div>

    <button type="button" onclick="addRebalancingRule()"
        class="bg-blue-50 dark:hover:bg-neutral-700 font-medium hover:bg-blue-500 hover:text-white mt-3 p-1 px-3 rounded-md text-blue-600 text-sm transition-all">
        Add Rebalancing Rule
    </button>
</div>

<!-- Rebalancing Rule Template (hidden) -->
<template id="rebalancingRuleTemplate">
    <div class="group relative bg-blue-50/30 p-4 rounded-md mb-4 border rebalancing-rule">
        <!-- Rule number indicator at top left -->
        <div
            class="absolute -top-2 -left-2 bg-gray-200 text-gray-900 dark:bg-gray-700 dark:text-gray-100 text-xs w-5 h-5 rounded-full flex items-center justify-center rule-number">
            1
        </div>

        <!-- Close button positioned at top right -->
        <button type="button" onclick="removeRebalancingRule(this)"
            class="absolute -top-2 -right-2 text-gray-600 hover:text-red-500 bg-gray-200 rounded-full p-0.5 hidden group-hover:block">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>

        <!-- Condition Section -->
        <div class="mb-4">
            <label class="block text-sm font-medium mb-2">Condition</label>
            <div class="flex flex-wrap items-center gap-2">
                <div
                    class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                    <select class="condition-leg outline-0 bg-transparent">
                        <option value="">Select Leg</option>
                    </select>
                </div>
                <div
                    class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                    <select class="condition-field  outline-0 bg-transparent">
                        <option value="premium">Premium</option>
                        <option value="strike">Strike</option>
                    </select>
                </div>
                <div
                    class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                    <select class="condition-operator  outline-0 bg-transparent">
                        <option value="gt">greater than</option>
                        <option value="gte">greater than or equals to</option>
                        <option value="lt">less than</option>
                        <option value="lte">less than or equals to</option>
                        <option value="eq">equals to</option>
                    </select>
                </div>
                <input type="text"
                    class="condition-expression w-[300px]  px-3 text-sm py-1.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all"
                    placeholder="Enter expression (e.g., 1.25 * {leg1.premium})">
            </div>
            <p class="mt-1 text-xs text-gray-500">Use leg1, leg2, etc. to refer to legs in the expression. Available
                fields: premium, spot</p>
        </div>

        <!-- Actions Section -->
        <div>
            <label class="block text-sm font-medium mb-2">Actions</label>
            <div class="actions-container space-y-2">
                <div class="action-row flex flex-wrap items-center gap-2">
                    <!-- Replace select with button group -->
                    <input type="hidden" class="action-type-value" value="square_off">
                    <div class="action-type-buttons inline-flex rounded-md shadow-sm" role="group">
                        <button type="button" data-value="square_off" onclick="selectActionType(this)"
                            class="action-btn square-off-btn px-3 py-1.5 text-sm font-medium bg-red-500 text-white dark:bg-red-600 hover:bg-red-600 dark:hover:bg-red-700 rounded-l-md">
                            Square Off
                        </button>
                        <button type="button" data-value="buy" onclick="selectActionType(this)"
                            class="action-btn buy-btn px-3 py-1.5 text-sm font-medium bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-green-500 hover:text-white dark:hover:bg-green-600">
                            Buy
                        </button>
                        <button type="button" data-value="sell" onclick="selectActionType(this)"
                            class="action-btn sell-btn px-3 py-1.5 text-sm font-medium bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-red-500 hover:text-white dark:hover:bg-red-600 rounded-r-md">
                            Sell
                        </button>
                    </div>
                    <div
                        class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                        <select class="action-leg outline-0 bg-transparent">
                            <option value="">Select Leg</option>
                        </select>
                    </div>
                   

                    <!-- Updated Strike Selection UI -->
                    <div class="strike-selection-container items-center flex gap-2">

                        <!-- Type Selection (Put, Call, Future) -->
                        <div
                            class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                            <select class="action-option-type outline-0 bg-transparent">
                                <option value="Put">Put</option>
                                <option value="Call">Call</option>
                                <option value="Future">Future</option>
                            </select>
                        </div>
                        

                        <!-- Expiry Selection -->
                        <div
                            class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                            <select class="action-expiry outline-0 bg-transparent">
                                <option value="Auto">Auto (Same as Leg)</option>
                                <option value="This Week">This Week</option>
                                <option value="Week (+1)">Week (+1)</option>
                                <option value="Week (+2)">Week (+2)</option>
                                <option value="Week (+3)">Week (+3)</option>
                                <option value="Week (+4)">Week (+4)</option>
                                <option value="Week (+5)">Week (+5)</option>
                                <option value="This Month">This Month</option>
                                <option value="Month (+1)">Month (+1)</option>
                                <option value="Month (+2)">Month (+2)</option>
                            </select>
                        </div>

                        
                       
                       <div class="strike-container flex items-center gap-2">
                        <div
                            class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                            <select class="action-strike-condition outline-0 bg-transparent"
                                onchange="updateActionStrikeUI(this)">
                                <option value="is">is</option>
                                <option value="premium">premium</option>
                                <option value="delta">delta</option>
                            </select>
                        </div>

                        <!-- Strike Value/Type Container -->
                        <div class="strike-value-container">
                            <!-- For "is" condition -->
                            <div
                                class="action-strike-is-div px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                                <select class="action-strike-is outline-0 bg-transparent">
                                    <option value="ITM20">ITM20</option>
                                    <option value="ITM19">ITM19</option>
                                    <option value="ITM18">ITM18</option>
                                    <option value="ITM17">ITM17</option>
                                    <option value="ITM16">ITM16</option>
                                    <option value="ITM15">ITM15</option>
                                    <option value="ITM14">ITM14</option>
                                    <option value="ITM13">ITM13</option>
                                    <option value="ITM12">ITM12</option>
                                    <option value="ITM11">ITM11</option>
                                    <option value="ITM10">ITM10</option>
                                    <option value="ITM9">ITM9</option>
                                    <option value="ITM8">ITM8</option>
                                    <option value="ITM7">ITM7</option>
                                    <option value="ITM6">ITM6</option>
                                    <option value="ITM5">ITM5</option>
                                    <option value="ITM4">ITM4</option>
                                    <option value="ITM3">ITM3</option>
                                    <option value="ITM2">ITM2</option>
                                    <option value="ITM1">ITM1</option>
                                    <option value="ATM">ATM</option>
                                    <option value="OTM1">OTM1</option>
                                    <option value="OTM2">OTM2</option>
                                    <option value="OTM3">OTM3</option>
                                    <option value="OTM4">OTM4</option>
                                    <option value="OTM5">OTM5</option>
                                    <option value="OTM6">OTM6</option>
                                    <option value="OTM7">OTM7</option>
                                    <option value="OTM8">OTM8</option>
                                    <option value="OTM9">OTM9</option>
                                    <option value="OTM10">OTM10</option>
                                    <option value="OTM11">OTM11</option>
                                    <option value="OTM12">OTM12</option>
                                    <option value="OTM13">OTM13</option>
                                    <option value="OTM14">OTM14</option>
                                    <option value="OTM15">OTM15</option>
                                    <option value="OTM16">OTM16</option>
                                    <option value="OTM17">OTM17</option>
                                    <option value="OTM18">OTM18</option>
                                    <option value="OTM19">OTM19</option>
                                    <option value="OTM20">OTM20</option>
                                </select>
                            </div>

                            <!-- For premium/delta condition -->
                            <div class="premium-delta-container" style="display: none;">
                                <div
                                    class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                                    <select class="action-strike-criterion outline-0 bg-transparent">
                                        <option value="closest">Is closest to</option>
                                        <option value="gte">Is greater than or equal to</option>
                                        <option value="lte">Is less than or equal to</option>
                                    </select>
                                </div>
                                <input type="text"
                                    class="action-strike-value w-20 px-3 text-sm py-1.5 border border-gray-300 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all ml-2"
                                    placeholder="Value">
                            </div>
                        </div>
                       </div>
                       <!-- Lots Input -->
                        <div
                            class="px-2 text-sm py-1.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                            <input type="number" class="action-lots w-16 outline-0 bg-transparent" min="1" value="1" default="1" placeholder="Lots">
                        </div>
                    </div>

                    <button type="button" onclick="removeAction(this)" class="text-red-500 hover:text-red-700">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            <button type="button" onclick="addAction(this)"
                class="bg-blue-50 dark:hover:bg-neutral-700 font-medium hover:bg-blue-500 hover:text-white mt-3 p-1 px-3 px-4 py-1.5 text-xs rounded-md text-blue-600 flex transition-all mt-2">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Action
            </button>
        </div>
    </div>
</template>

<script>
    // Helper functions
    function getLegOptions() {
        return legs.map((leg, index) => ({
            value: leg.id.toString(),
            label: `Leg ${index + 1} (${leg.action} ${leg.optionType})`
        }));
    }

    function updateSelect(select, options, preserveValue = true) {
        const currentValue = preserveValue ? select.value : '';
        select.innerHTML = '<option value="">Select Leg</option>' +
            options.map(opt => `<option value="${opt.value}">${opt.label}</option>`).join('');

        if (currentValue && options.some(opt => opt.value === currentValue)) {
            select.value = currentValue;
        } else if (currentValue) {
            select.value = '';
        }
    }

    // Add a new rebalancing rule
    function addRebalancingRule() {
        const container = document.getElementById('rebalancingRulesContainer');
        const template = document.getElementById('rebalancingRuleTemplate');
        const rule = template.content.cloneNode(true);
        const legOptions = getLegOptions();

        // Update all leg selectors
        rule.querySelectorAll('.condition-leg, .action-leg').forEach(select => {
            updateSelect(select, legOptions);
        });

        rule.querySelector('.condition-expression').value = "1.25 * {leg1.premium}";

        // Set up action rows
        rule.querySelectorAll('.action-row').forEach(setupActionRow);

        container.appendChild(rule);

        // Update all rule numbers
        updateRuleNumbers();
    }

    function removeRebalancingRule(button) {
        button.closest('.rebalancing-rule').remove();

        // Update all rule numbers after removing a rule
        updateRuleNumbers();
    }

    // Update the rule numbers to ensure they're sequential
    function updateRuleNumbers() {
        document.querySelectorAll('#rebalancingRulesContainer .rebalancing-rule').forEach((rule, index) => {
            rule.querySelector('.rule-number').textContent = index + 1;
        });
    }

    // Function to handle the UI update when strike condition changes
    function updateActionStrikeUI(select) {
        const actionRow = select.closest('.action-row');
        const strikeValueContainer = actionRow.querySelector('.strike-value-container');
        const isSelect = strikeValueContainer.querySelector('.action-strike-is-div');
        const premiumDeltaContainer = strikeValueContainer.querySelector('.premium-delta-container');

        // Hide all containers first
        isSelect.style.display = 'none';
        premiumDeltaContainer.style.display = 'none';

        // Show appropriate container based on selection
        if (select.value === 'is') {
            isSelect.style.display = 'inline-block';
        } else {
            premiumDeltaContainer.style.display = 'flex';
        }
    }

    // Function to handle action type button selection
    function selectActionType(button) {
        // Get the parent action row
        const actionRow = button.closest('.action-row');

        // Reset all buttons to default state
        actionRow.querySelectorAll('.action-btn').forEach(btn => {
            btn.classList.remove('bg-red-500', 'bg-green-500', 'text-white', 'dark:bg-red-600', 'dark:bg-green-600');
            btn.classList.add('bg-gray-200', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
        });

        // Set the selected button's style
        button.classList.remove('bg-gray-200', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
        if (button.dataset.value === 'buy') {
            button.classList.add('bg-green-500', 'text-white', 'dark:bg-green-600');
        } else if (button.dataset.value === 'sell') {
            button.classList.add('bg-red-500', 'text-white', 'dark:bg-red-600');
        } else { // square_off
            button.classList.add('bg-red-500', 'text-white', 'dark:bg-red-600');
        }

        // Update the hidden input value
        actionRow.querySelector('.action-type-value').value = button.dataset.value;

        // Show/hide strike selection based on action type
        const strikeSelectionContainer = actionRow.querySelector('.strike-selection-container');
        const strikeContainer = actionRow.querySelector('.strike-container')
        const isSquareOff = button.dataset.value === 'square_off';
        strikeSelectionContainer.style.display = isSquareOff ? 'none' : 'flex';
        
        // Hide strike-container when option-type is 'Future'
        const optionTypeSelect = actionRow.querySelector('.action-option-type');
        const strikeContainerElement = actionRow.querySelector('.strike-container');
        if (optionTypeSelect.value === 'Future') {
            strikeContainerElement.style.display = 'none';
        } else {
            strikeContainerElement.style.display = 'flex';
        }

        // Reset strike selection values if square_off is selected
        if (isSquareOff) {
            actionRow.querySelector('.action-strike-condition').value = 'is';
            actionRow.querySelector('.action-strike-is').value = 'ATM';
            actionRow.querySelector('.action-strike-criterion').value = 'closest';
            actionRow.querySelector('.action-strike-value').value = '';
        }
    }

    function setupActionRow(actionRow) {
        const actionValue = actionRow.querySelector('.action-type-value').value;
        const actionLeg = actionRow.querySelector('.action-leg');
        const strikeCondition = actionRow.querySelector('.action-strike-condition');

        // Make sure leg options are populated
        updateSelect(actionLeg, getLegOptions());

        // Set initial button state
        const selectedButton = actionRow.querySelector(`.action-btn[data-value="${actionValue}"]`);
        if (selectedButton) {
            selectActionType(selectedButton);
        }

        // Set initial strike selection UI based on the condition
        if (strikeCondition) {
            updateActionStrikeUI(strikeCondition);
        }

        // Add event handlers for buttons if needed
        actionRow.querySelectorAll('.action-btn').forEach(btn => {
            btn.onclick = function () {
                selectActionType(this);
            };
        });

        // Handle the option-type (Future/Call/Put)
        const optionTypeSelect = actionRow.querySelector('.action-option-type');
        optionTypeSelect.addEventListener('change', function () {
            const strikeContainerElement = actionRow.querySelector('.strike-container');
            if (this.value === 'Future') {
                strikeContainerElement.style.display = 'none';
            } else {
                strikeContainerElement.style.display = 'flex';
            }
        });
    }

    function addAction(button) {
        const actionsContainer = button.previousElementSibling;
        const firstAction = actionsContainer.querySelector('.action-row');
        const newAction = firstAction.cloneNode(true);

        // Reset values
        newAction.querySelector('.action-type-value').value = 'square_off';
        newAction.querySelectorAll('select:not(.action-leg)').forEach(select => select.selectedIndex = 0);
        newAction.querySelectorAll('input:not(.action-type-value)').forEach(input => {
            input.value = '';
        });

        // Reset leg selector
        updateSelect(newAction.querySelector('.action-leg'), getLegOptions(), false);

        // Add to DOM first
        actionsContainer.appendChild(newAction);

        // Set up the new action row (will apply correct button styling)
        setupActionRow(newAction);
    }

    function removeAction(button) {
        const actionsContainer = button.closest('.actions-container');
        if (actionsContainer.querySelectorAll('.action-row').length > 1) {
            button.closest('.action-row').remove();
        }
    }

    // Update all leg selectors in rebalancing rules
    function updateRebalancingLegSelectors() {
        const legOptions = getLegOptions();
        const legIds = legs.map(leg => leg.id.toString());

        // Update all leg selectors
        document.querySelectorAll('#rebalancingRulesContainer .condition-leg, ' +
            '#rebalancingRulesContainer .condition-compare-leg, ' +
            '#rebalancingRulesContainer .action-leg').forEach(select => {
                const previousValue = select.value;
                const isInvalid = previousValue && !legIds.includes(previousValue);

                // Update options
                updateSelect(select, legOptions);

                // Add visual indication if the previous selection is no longer valid
                if (isInvalid) {
                    select.classList.add('border-red-500', 'text-red-600');
                    select.title = "This leg reference is no longer valid. Please select another leg.";

                    // Add a small warning icon inside or next to the select
                    const warningIcon = document.createElement('span');
                    warningIcon.className = 'text-red-500 ml-1';
                    warningIcon.innerHTML = '⚠️';
                    warningIcon.title = "Invalid leg reference";

                    // Only add the icon if it doesn't already exist
                    if (!select.parentNode.querySelector('.invalid-leg-warning')) {
                        warningIcon.classList.add('invalid-leg-warning');
                        select.parentNode.insertBefore(warningIcon, select.nextSibling);
                    }
                } else {
                    // Remove error styling if now valid
                    select.classList.remove('border-red-500', 'text-red-600');
                    select.removeAttribute('title');

                    // Remove any existing warning icon
                    const existingWarning = select.parentNode.querySelector('.invalid-leg-warning');
                    if (existingWarning) {
                        existingWarning.remove();
                    }
                }
            });
    }

    // Get all rebalancing rules data for saving
    function getRebalancingRulesData() {
        const rules = [];

        document.querySelectorAll('#rebalancingRulesContainer .rebalancing-rule').forEach(ruleElement => {
            // Base condition
            const condition = {
                leg: ruleElement.querySelector('.condition-leg').value,
                field: ruleElement.querySelector('.condition-field').value,
                operator: ruleElement.querySelector('.condition-operator').value,
                expression: ruleElement.querySelector('.condition-expression').value.trim()
            };

            // Gather actions
            const actions = [];
            ruleElement.querySelectorAll('.action-row').forEach(actionRow => {
                const actionType = actionRow.querySelector('.action-type-value').value;
                
                const action = {
                    type: actionType,
                    leg: actionRow.querySelector('.action-leg').value,
                    
                };

                if (actionType !== 'square_off') {
                    const strikeCondition = actionRow.querySelector('.action-strike-condition').value;
                    const actionOptionType = actionRow.querySelector('.action-option-type').value;
                    const actionExpiry = actionRow.querySelector('.action-expiry').value;
                    const lots = actionRow.querySelector('.action-lots').value;
                    action.strikeCondition = strikeCondition;

                    if (strikeCondition === 'is') {
                        action.strikeValue = actionRow.querySelector('.action-strike-is').value;
                    } else {
                        action.strikeCriterion = actionRow.querySelector('.action-strike-criterion').value;
                        action.strikeValue = actionRow.querySelector('.action-strike-value').value;
                    }

                    action.optionType = actionOptionType,
                    action.expiry = actionExpiry,
                    action.lots = lots
                }

                actions.push(action);
            });

            rules.push({ condition, actions });
        });

        return rules;
    }

    // Set rebalancing rules when loading a strategy
    function setRebalancingRulesData(rulesData) {
        const container = document.getElementById('rebalancingRulesContainer');
        container.innerHTML = ''; // Clear existing rules

        if (!rulesData || !rulesData.length) return;

        // Create all rule elements first
        rulesData.forEach(() => addRebalancingRule());

        // Then fill in the data
        const ruleElements = container.querySelectorAll('.rebalancing-rule');
        const legOptions = getLegOptions();

        rulesData.forEach((ruleData, index) => {
            const ruleElement = ruleElements[index];
            const condition = ruleData.condition;

            // Set condition values
            ruleElement.querySelector('.condition-leg').value = condition.leg;
            ruleElement.querySelector('.condition-field').value = condition.field;
            ruleElement.querySelector('.condition-operator').value = condition.operator;

            // Set expression value
            ruleElement.querySelector('.condition-expression').value = condition.expression || '';

            // Handle actions
            const actionsContainer = ruleElement.querySelector('.actions-container');
            actionsContainer.innerHTML = ''; // Clear default action

            // Add each action
            ruleData.actions.forEach(actionData => {
                // Clone template action row
                const actionTemplate = document.getElementById('rebalancingRuleTemplate')
                    .content.querySelector('.action-row');
                const actionRow = actionTemplate.cloneNode(true);

                // Update leg selector options
                const actionLegSelector = actionRow.querySelector('.action-leg');
                updateSelect(actionLegSelector, legOptions, false);

                // Set action type value in the hidden input
                actionRow.querySelector('.action-type-value').value = actionData.type;

                // Set leg value
                actionLegSelector.value = actionData.leg;

                // Set strike selection if not square_off
                if (actionData.type !== 'square_off' && actionData.strikeCondition) {
                    const strikeCondition = actionRow.querySelector('.action-strike-condition');
                    strikeCondition.value = actionData.strikeCondition;

                    // Update UI based on strike condition
                    updateActionStrikeUI(strikeCondition);

                    if (actionData.strikeCondition === 'is') {
                        actionRow.querySelector('.action-strike-is').value = actionData.strikeValue;
                    } else {
                        actionRow.querySelector('.action-strike-criterion').value = actionData.strikeCriterion || 'closest';
                        actionRow.querySelector('.action-strike-value').value = actionData.strikeValue;
                    }

                    // Set option type and expiry
                    actionRow.querySelector('.action-option-type').value = actionData.optionType || 'Put';
                    actionRow.querySelector('.action-expiry').value = actionData.expiry || 'Auto';
                    actionRow.querySelector('.action-lots').value = actionData.lots || '1';
                }

                // Add to container
                actionsContainer.appendChild(actionRow);

                // Set up the action row (this will update the button states)
                setupActionRow(actionRow);
            });
        });

        // Update rule numbers after loading all rules
        updateRuleNumbers();
    }

    // Validate rebalancing rules
    function validateRebalancingRules() {
        const rules = getRebalancingRulesData();
        const legIds = legs.map(leg => leg.id.toString());
        let isValid = true;
        let invalidRules = [];

        // Check each rule for invalid leg references
        rules.forEach((rule, index) => {
            let ruleHasError = false;
            const ruleNum = index + 1;

            // Check condition leg
            if (!legIds.includes(rule.condition.leg)) {
                ruleHasError = true;
            }

            // Check for valid expression
            if (!rule.condition.expression || rule.condition.expression.trim() === '') {
                ruleHasError = true;
            }

            // Check action legs
            rule.actions.forEach(action => {
                if (!legIds.includes(action.leg)) {
                    ruleHasError = true;
                }

                // Check strike value for non-square_off actions
                if (action.type !== 'square_off') {
                    if (action.strikeCondition !== 'is' && (!action.strikeValue || action.strikeValue.trim() === '')) {
                        ruleHasError = true;
                    }
                }
            });

            if (ruleHasError) {
                isValid = false;
                invalidRules.push(ruleNum);
            }
        });

        // Show warning if validation fails
        if (!isValid) {
            const warningMsg = `Warning: Rebalancing rules ${invalidRules.join(', ')} have invalid leg references or expressions. Please update these rules before saving.`;

            // Display warning (can use your app's existing alert system)
            if (typeof showAlert === 'function') {
                showAlert(warningMsg, 'warning');
            } else {
                alert(warningMsg);
            }
        }

        return isValid;
    }
</script>
