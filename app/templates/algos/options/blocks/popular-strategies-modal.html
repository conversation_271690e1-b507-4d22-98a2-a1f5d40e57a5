<!-- Strategy Creation Modal -->
<div id="strategyModal" class="modal fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white dark:bg-neutral-800 rounded-lg shadow-lg w-full max-w-2xl p-6 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold">Create New Strategy</h3>
            <button type="button" onclick="closeStrategyModal()" class="text-gray-500 hover:text-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <div class="mb-6">
            <button onclick="createFromScratch()"
                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg mb-4 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create from Scratch
            </button>

            <div class="text-center my-4">
                <span class="px-4 bg-gray-200 text-gray-600 text-sm rounded-full py-1">OR</span>
            </div>

            <h4 class="text-lg font-medium mb-3">Popular Strategies</h4>
            <p class="text-sm text-gray-600 mb-4">Select a pre-configured strategy template to get started quickly.</p>

            <div class="grid grid-cols-2 gap-3">
                <div onclick="applyPopularStrategy('straddle')"
                    class="cursor-pointer bg-white border border-gray-200 hover:border-blue-500 hover:bg-blue-50 rounded-lg p-4 transition-all">
                    <h5 class="font-medium mb-1">Straddle</h5>
                    <p class="text-xs text-gray-600">For significant price movement in either direction</p>
                </div>
                <div onclick="applyPopularStrategy('strangle')"
                    class="cursor-pointer bg-white border border-gray-200 hover:border-blue-500 hover:bg-blue-50 rounded-lg p-4 transition-all">
                    <h5 class="font-medium mb-1">Strangle</h5>
                    <p class="text-xs text-gray-600">Similar to straddle but with OTM options</p>
                </div>
                <div onclick="applyPopularStrategy('bullSpread')"
                    class="cursor-pointer bg-white border border-gray-200 hover:border-blue-500 hover:bg-blue-50 rounded-lg p-4 transition-all">
                    <h5 class="font-medium mb-1">Bull Call Spread</h5>
                    <p class="text-xs text-gray-600">For moderate upward price movement</p>
                </div>
                <div onclick="applyPopularStrategy('bearSpread')"
                    class="cursor-pointer bg-white border border-gray-200 hover:border-blue-500 hover:bg-blue-50 rounded-lg p-4 transition-all">
                    <h5 class="font-medium mb-1">Bear Put Spread</h5>
                    <p class="text-xs text-gray-600">For moderate downward price movement</p>
                </div>
                <div onclick="applyPopularStrategy('ironCondor')"
                    class="cursor-pointer bg-white border border-gray-200 hover:border-blue-500 hover:bg-blue-50 rounded-lg p-4 transition-all">
                    <h5 class="font-medium mb-1">Iron Condor</h5>
                    <p class="text-xs text-gray-600">Market-neutral strategy for range-bound markets</p>
                </div>
                <div onclick="applyPopularStrategy('butterfly')"
                    class="cursor-pointer bg-white border border-gray-200 hover:border-blue-500 hover:bg-blue-50 rounded-lg p-4 transition-all">
                    <h5 class="font-medium mb-1">Butterfly</h5>
                    <p class="text-xs text-gray-600">Neutral strategy for minimal price movement</p>
                </div>
                <div onclick="applyPopularStrategy('coveredCall')"
                    class="cursor-pointer bg-white border border-gray-200 hover:border-blue-500 hover:bg-blue-50 rounded-lg p-4 transition-all">
                    <h5 class="font-medium mb-1">Covered Call</h5>
                    <p class="text-xs text-gray-600">Income generation strategy</p>
                </div>
                <div onclick="applyPopularStrategy('protectivePut')"
                    class="cursor-pointer bg-white border border-gray-200 hover:border-blue-500 hover:bg-blue-50 rounded-lg p-4 transition-all">
                    <h5 class="font-medium mb-1">Protective Put</h5>
                    <p class="text-xs text-gray-600">Downside protection strategy</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function openStrategyModal() {
        document.getElementById('strategyModal').classList.remove('hidden');
        document.body.classList.add('overflow-hidden'); // Prevent scrolling when modal is open
    }

    function closeStrategyModal() {
        document.getElementById('strategyModal').classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
    }

    function createFromScratch() {
        closeStrategyModal();
        // Clear any existing strategy data
        legs = [];
        renderLegs();
        document.getElementById("strategyName").value = "";

        // Show the strategy form
        document.getElementById("faqContainer").classList.add("hidden");
        document.getElementById("strategyFormContainer").classList.remove("hidden");

        // Call loadCreateForm to set up the form properly
        loadCreateForm();
    }

    // --- Popular Strategies Implementation ---
    const popularStrategies = {
        straddle: {
            name: "Straddle",
            description: "A straddle involves buying both a Call and a Put option with the same strike price (typically ATM) and expiration date. It's used when you expect significant price movement but are uncertain about the direction.",
            legs: [
                {
                    action: "buy",
                    optionType: "Call",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "ATM" }
                },
                {
                    action: "buy",
                    optionType: "Put",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "ATM" }
                }
            ],
            exitSettings: {
                stopLoss: { enabled: true, type: "percent", value: "25" },
                target: { enabled: true, type: "percent", value: "50" }
            }
        },
        strangle: {
            name: "Strangle",
            description: "A strangle involves buying an OTM Call and an OTM Put with the same expiration date. It's similar to a straddle but uses OTM options, making it cheaper but requiring more price movement to be profitable.",
            legs: [
                {
                    action: "buy",
                    optionType: "Call",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "OTM2" }
                },
                {
                    action: "buy",
                    optionType: "Put",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "OTM2" }
                }
            ],
            exitSettings: {
                stopLoss: { enabled: true, type: "percent", value: "25" },
                target: { enabled: true, type: "percent", value: "50" }
            }
        },
        bullSpread: {
            name: "Bull Call Spread",
            description: "A bull call spread involves buying a call option at a lower strike price and selling a call at a higher strike price with the same expiration. It's used when you expect a moderate increase in the underlying asset's price.",
            legs: [
                {
                    action: "buy",
                    optionType: "Call",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "ATM" }
                },
                {
                    action: "sell",
                    optionType: "Call",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "OTM2" }
                }
            ],
            exitSettings: {
                stopLoss: { enabled: true, type: "percent", value: "30" },
                target: { enabled: true, type: "percent", value: "60" }
            }
        },
        bearSpread: {
            name: "Bear Put Spread",
            description: "A bear put spread involves buying a put option at a higher strike price and selling a put at a lower strike price with the same expiration. It's used when you expect a moderate decrease in the underlying asset's price.",
            legs: [
                {
                    action: "buy",
                    optionType: "Put",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "ATM" }
                },
                {
                    action: "sell",
                    optionType: "Put",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "OTM2" }
                }
            ],
            exitSettings: {
                stopLoss: { enabled: true, type: "percent", value: "30" },
                target: { enabled: true, type: "percent", value: "60" }
            }
        },
        ironCondor: {
            name: "Iron Condor",
            description: "An iron condor is a market-neutral strategy that profits when the underlying asset stays within a specific price range. It involves selling an OTM put spread and an OTM call spread with the same expiration date.",
            legs: [
                {
                    action: "buy",
                    optionType: "Put",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "OTM4" }
                },
                {
                    action: "sell",
                    optionType: "Put",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "OTM2" }
                },
                {
                    action: "sell",
                    optionType: "Call",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "OTM2" }
                },
                {
                    action: "buy",
                    optionType: "Call",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "OTM4" }
                }
            ],
            exitSettings: {
                stopLoss: { enabled: true, type: "percent", value: "40" },
                target: { enabled: true, type: "percent", value: "60" }
            }
        },
        butterfly: {
            name: "Long Call Butterfly",
            description: "A butterfly spread is a neutral strategy that profits when the underlying asset stays near a specific price. It involves buying one ITM call, selling two ATM calls, and buying one OTM call with the same expiration date.",
            legs: [
                {
                    action: "buy",
                    optionType: "Call",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "ITM2" }
                },
                {
                    action: "sell",
                    optionType: "Call",
                    expiry: "This Week",
                    lots: 2,
                    strikeConfig: { condition: "is", value: "ATM" }
                },
                {
                    action: "buy",
                    optionType: "Call",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "OTM2" }
                }
            ],
            exitSettings: {
                stopLoss: { enabled: true, type: "percent", value: "30" },
                target: { enabled: true, type: "percent", value: "70" }
            }
        },
        coveredCall: {
            name: "Covered Call",
            description: "A covered call involves holding a long position in an asset and selling a call option on that same asset. This strategy generates income through the premium received from selling the call option.",
            legs: [
                {
                    action: "buy",
                    optionType: "Future",
                    expiry: "This Week",
                    lots: 1
                },
                {
                    action: "sell",
                    optionType: "Call",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "OTM2" }
                }
            ],
            exitSettings: {
                stopLoss: { enabled: true, type: "percent", value: "15" },
                target: { enabled: true, type: "percent", value: "30" }
            }
        },
        protectivePut: {
            name: "Protective Put",
            description: "A protective put involves holding a long position in an asset and buying a put option on that same asset. This strategy acts as insurance against a significant drop in the asset's price.",
            legs: [
                {
                    action: "buy",
                    optionType: "Future",
                    expiry: "This Week",
                    lots: 1
                },
                {
                    action: "buy",
                    optionType: "Put",
                    expiry: "This Week",
                    lots: 1,
                    strikeConfig: { condition: "is", value: "ATM" }
                }
            ],
            exitSettings: {
                stopLoss: { enabled: true, type: "percent", value: "15" },
                target: { enabled: true, type: "percent", value: "30" }
            }
        }
    };

    function applyPopularStrategy(strategyType) {
        closeStrategyModal();

        if (!popularStrategies[strategyType]) {
            showAlert("Strategy not found");
            return;
        }

        const strategy = popularStrategies[strategyType];

        // Call loadCreateForm to set up the form properly
        loadCreateForm();

        // Suggest a strategy name
        document.getElementById("strategyName").value = strategy.name;

        // Clear existing legs
        let prevRebalancingRules = getRebalancingRulesData();
        legs = [];

        // Add new legs from the template
        strategy.legs.forEach(legTemplate => {
            const leg = {
                id: Date.now() + Math.floor(Math.random() * 1000), // Ensure unique IDs
                lots: legTemplate.lots,
                action: legTemplate.action,
                optionType: legTemplate.optionType,
                expiry: legTemplate.expiry,
                strikeConfig: { ...legTemplate.strikeConfig }
            };
            legs.push(leg);
        });

        // Render the legs
        renderLegs();

        // Apply exit settings
        if (strategy.exitSettings) {
            if (strategy.exitSettings.stopLoss) {
                document.getElementById("exitStopLossCheck").checked = strategy.exitSettings.stopLoss.enabled;
                document.getElementById("exitStopLossOptions").style.display = strategy.exitSettings.stopLoss.enabled ? "flex" : "none";
                document.getElementById("stopLossType").value = strategy.exitSettings.stopLoss.type;
                document.getElementById("stopLossValue").value = strategy.exitSettings.stopLoss.value;
            }

            if (strategy.exitSettings.target) {
                document.getElementById("exitBookProfitsCheck").checked = strategy.exitSettings.target.enabled;
                document.getElementById("exitBookProfitsOptions").style.display = strategy.exitSettings.target.enabled ? "flex" : "none";
                document.getElementById("targetType").value = strategy.exitSettings.target.type;
                document.getElementById("targetValue").value = strategy.exitSettings.target.value;
            }
        }

        // Restore rebalancing rules
        setRebalancingRulesData(prevRebalancingRules);

        // Show success message
        showAlert(`Applied ${strategy.name} template successfully!`);
    }
</script>