<script>
    // Run intraday backtest logic moved from main.html
    async function runIntradayBacktest() {
        const symbolInput = document.getElementById('stock-input');
        const expirationSelect = document.getElementById('expirationSelect');
        const strategySelect = document.getElementById('strategySelect');
        const startDateInput = document.getElementById('startDate');
        const symbol = symbolInput?.value.trim().toUpperCase();
        const expiration = expirationSelect.value;
        const strategyUid = strategySelect.value;
        const startDate = startDateInput.value;
        if (!symbol) {
            alert('Please select a stock.');
            return;
        }
        if (!expiration) {
            alert('Please select an expiration date.');
            return;
        }
        if (!strategyUid) {
            alert('Please select a strategy.');
            return;
        }
        if (!startDate) {
            alert('Please select a start date.');
            return;
        }
        // Show loading
        const resultsSection = document.getElementById('resultsSection');
        const resultsContent = document.getElementById('results-content');
        resultsSection.classList.remove('hidden');
        resultsContent.innerHTML = `<div class=\"flex flex-col items-center justify-center p-4 text-center text-gray-600 min-h-[20px]\">\n        <div class=\"w-16 h-16 border-4 border-blue-500 border-dashed rounded-full animate-spin\"></div>\n        <span class=\"mt-4 text-sm font-medium text-gray-700\">Running intraday backtest...</span>\n    </div>`;
        // REST API call to backend
        try {
            const payload = {
                strategy: window.selectedIntradayStrategy, // send full strategy JSON
                expirationDate: expiration,
                startDate: startDate,
                symbol: symbol
            };
            const response = await fetch("/algo-backtest-intraday", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload)
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            // Render accordion UI
            const accordionHtml = renderBacktestAccordion(data.results);
            resultsContent.innerHTML = `<div class="p-4">Backtest results for <b>${symbol}</b>, expiration <b>${expiration}</b>, start date <b>${startDate}</b>, strategy <b>${window.selectedIntradayStrategy?.name || strategyUid}</b>.<br><br>${accordionHtml}</div>`;
            document.getElementById('chart-section').classList.remove('hidden');
        } catch (err) {
            console.error('Backtest error:', err);
            resultsContent.innerHTML = `<div class="p-4 text-red-600">Error: ${err.message}<br><pre>${err.stack || ''}</pre></div>`;
        }
    }

    function renderBacktestAccordion(results) {
        let html = '';
        results.forEach(day => {
            const date = day.date;
            const pnl = day.pnl;
            const entryPrices = pnl.entry_prices;
            const resultsTable = pnl.results;
            const legs = Array.isArray(day.legs) ? day.legs : [];
            // Get strikes for heading
            const legStrikes = legs.length > 0 ? legs.map(leg => `${leg.strike}`).join(', ') : '-';
            // Entry premiums only (no strikes), handle missing data
            const entryWithStrikes = legs.length > 0 ? legs.map(leg => {
                if (!entryPrices || typeof entryPrices !== 'object') return '-';
                const premium = entryPrices[leg.id];
                if (typeof premium === 'number') return premium.toFixed(2);
                return '-';
            }).join(', ') : '-';
            // Get last entry for exit premiums
            const resultsTableValues = resultsTable && typeof resultsTable === 'object' ? Object.values(resultsTable) : [];
            const lastRow = resultsTableValues.length > 0 ? resultsTableValues[resultsTableValues.length - 1] : null;
            // For exit premiums, handle missing leg data gracefully
            const exitPremiums = legs.length > 0 ? legs.map(leg => {
                if (!lastRow || !lastRow.premiums || typeof lastRow.premiums !== 'object') return '-';
                // Defensive: check if leg.id exists in premiums and is a number
                const premium = lastRow.premiums[leg.id];
                if (typeof premium === 'number') return premium.toFixed(2);
                return '-';
            }).join(', ') : '-';
            // Calculate per-leg and overall P&L, handle missing data
            const overallPL = legs.length > 0 ? legs.reduce((sum, leg) => {
                if (!entryPrices || typeof entryPrices !== 'object') return sum;
                const entry = typeof entryPrices[leg.id] === 'number' ? entryPrices[leg.id] : 0;
                const exit = lastRow && lastRow.premiums && typeof lastRow.premiums === 'object' && typeof lastRow.premiums[leg.id] === 'number' ? lastRow.premiums[leg.id] : 0;
                return sum + (exit - entry);
            }, 0) : 0;
            // Use daily_pnl from backend for accordion header
            const dailyPL = typeof pnl.daily_pnl === 'number' ? pnl.daily_pnl : 0;
            const plColor = dailyPL > 0 ? 'text-green-600' : (dailyPL < 0 ? 'text-red-600' : 'text-gray-700');
            // Get exit result if available
            const exitResult = pnl.exit_result ? `<span class='ml-2 text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-800 border border-yellow-300'>${pnl.exit_result}</span>` : '';
            // Get day of week abbreviation
            let dayOfWeek = '';
            if (date) {
                const dateObj = new Date(date);
                if (!isNaN(dateObj)) {
                    dayOfWeek = dateObj.toLocaleDateString('en-US', { weekday: 'short' });
                }
            }
            // Get NIFTY values from backend response
            const spotPrice = typeof day.spot_price === 'number' ? day.spot_price.toFixed(2) : '-';
            const niftyFirst = typeof day.nifty_first_value === 'number' ? day.nifty_first_value.toFixed(2) : '-';
            const niftyLast = typeof day.nifty_last_value === 'number' ? day.nifty_last_value.toFixed(2) : '-';
            const niftyChange = typeof day.nifty_change_percent === 'number' ? day.nifty_change_percent.toFixed(2) + '%' : '-';
            // Accordion header
            html += `<div class="border rounded mb-2">
                <button class="w-full text-left px-4 py-3 bg-white font-semibold accordion-toggle flex flex-wrap items-center gap-x-4 gap-y-2 justify-start" data-date="${date}">
                    <span class="font-bold text-gray-800 whitespace-nowrap">${date}${dayOfWeek ? `&nbsp;<span class='text-gray-500'>(${dayOfWeek})</span>` : ''}</span>
                    <span class="text-gray-700 whitespace-nowrap">Spot: ${spotPrice}</span>
                    <span class="text-gray-700 whitespace-nowrap">NIFTY: ${niftyFirst} → ${niftyLast} (${niftyChange})</span>
                    <span class="text-gray-600 whitespace-nowrap">Strikes: ${legStrikes}</span>
                    <span class="text-blue-700 whitespace-nowrap">Entry: ${entryWithStrikes}</span>
                    <span class="text-purple-700 whitespace-nowrap">Exit: ${exitPremiums}</span>
                    <span class="font-bold ${plColor} whitespace-nowrap">P&L: ${dailyPL.toFixed(2)}</span>
                    ${exitResult ? `<span style='min-width:120px;display:inline-block;'>${exitResult}</span>` : ''}
                </button>
                <div class="accordion-content hidden px-4 py-2">
                    <table class="min-w-full text-xs border border-gray-300 rounded-lg overflow-hidden shadow-sm" style="margin-bottom: 0.5rem;">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="px-2 py-2 border-b border-gray-200 text-left">Time</th>
                                <th class="px-2 py-2 border-b border-gray-200 text-left">Premiums</th>
                                <th class="px-2 py-2 border-b border-gray-200 text-left">Total Premium</th>
                                <th class="px-2 py-2 border-b border-gray-200 text-left">P&L</th>
                            </tr>
                        </thead>
                        <tbody>
                        ${resultsTableValues.length > 0 ? Object.entries(resultsTable).map(([ts, row], idx) => {
                const time = new Date(Number(ts) * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                // For each leg, handle missing premium data
                const premiums = legs.length > 0 ? legs.map(leg => {
                    if (!row.premiums || typeof row.premiums !== 'object') return '-';
                    const premium = row.premiums[leg.id];
                    if (typeof premium === 'number') return premium.toFixed(2);
                    return '-';
                }).join(', ') : '-';
                // Use tick P&L from backend if available
                let tickPL = typeof row.pnl === 'number' ? row.pnl : 0;
                // Fix color logic: negative is red, positive is green, zero is gray
                return `<tr>
                    <td class="px-2 py-1 border-b border-gray-100">${time}</td>
                    <td class="px-2 py-1 border-b border-gray-100">${premiums}</td>
                    <td class="px-2 py-1 border-b border-gray-100">${row.total_premium.toFixed(2)}</td>
                    <td class="px-2 py-1 border-b border-gray-100 font-semibold ${tickPL > 0 ? 'text-green-600' : (tickPL < 0 ? 'text-red-600' : 'text-gray-700')}">${tickPL.toFixed(2)}</td>
                </tr>`;
            }).join('') : `<tr><td colspan='4' class='text-center text-gray-500 py-2'>No data (holiday or missing)</td></tr>`}
                        </tbody>
                    </table>
                </div>
            </div>`;
        });
        return html;
    }

    document.addEventListener('click', function (e) {
        if (e.target.classList.contains('accordion-toggle')) {
            const content = e.target.nextElementSibling;
            if (content) { content.classList.toggle('hidden'); }
        }
    });
</script>