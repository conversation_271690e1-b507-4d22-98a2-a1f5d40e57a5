<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Intraday Algo Backtesting Dashboard | AI Bull</title>
    <meta name="description"
        content="Backtest intraday option strategies using historical data and AI Bull's dashboard." />
    <link rel="canonical" href="https://theaibull.com/algos/backtesting-intraday" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/algos/backtesting-intraday" />
    <meta property="og:title" content="Intraday Algo Backtesting Dashboard" />
    <meta property="og:description"
        content="Select a stock, expiration date, and intraday strategy to analyze historical intraday performance." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Intraday Algo Backtesting Dashboard" />
    <meta name="twitter:description"
        content="Select a stock, expiration date, and intraday strategy to analyze historical intraday performance." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
    {% include 'algos/backtesting/css.html' %}
</head>

<body class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100">
    {% include 'blocks/common.html' %}
    {% include 'blocks/stock-symbols.html' %}
    {% include 'blocks/info.html' %}
    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <main class="p-3 mx-auto max-w-7xl w-full">
            <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2 rounded-lg mb-3">
                <div class="md:flex justify-between items-center">
                    <div class="flex items-center">
                        <h1 class="md:text-xl text-lg font-semibold tracking-tight">Intraday Algo Backtesting Dashboard
                        </h1>
                        <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">Analyze intraday option
                            strategies using historical data</h2>
                    </div>
                </div>
            </div>
            <!-- Stock Search Block -->
            <div class="mx-auto container mb-4">
                <div class="flex">
                    <div class="w-full rounded-lg md:p-8 p-4 border border-white"
                        style="background-image: linear-gradient(45deg, #f0faff, #ffffff)">
                        {% with enable_backtesting=false, show_submit_button=true %}
                        {% include 'blocks/search-active-recent-stocks.html' %}
                        {% endwith %}
                    </div>
                </div>
            </div>
            <!-- Expiration Date & Strategy Selection -->
            <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-6">
                <div class="flex flex-col md:flex-row gap-4 md:items-end">
                    <!-- Strategies Dropdown (Intraday only) -->
                    <div id="strategySelectContainer" class="w-full md:w-1/2">
                        <label for="strategySelect" class="block font-medium mb-2 text-sm">Choose Intraday
                            Strategy</label>
                        <div
                            class="w-full px-3 text-sm py-2.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600">
                            <select id="strategySelect" name="strategySelect"
                                class="outline-0 bg-transparent w-full cursor-pointer">
                                <option value="">-- Choose Strategy --</option>
                            </select>
                        </div>
                    </div>
                    <!-- Expiration Date Dropdown -->
                    <div id="expirationSelectContainer" class="w-full md:w-1/2">
                        <label for="expirationSelect" class="block font-medium mb-2 text-sm">Select Expiration
                            Date</label>
                        <div
                            class="w-full px-3 text-sm py-2.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600">
                            <select id="expirationSelect" name="expirationSelect"
                                class="outline-0 bg-transparent w-full cursor-pointer" disabled>
                                <option value="">-- Choose Expiration --</option>
                            </select>
                        </div>
                    </div>
                    <!-- Start Date Input -->
                    <div id="startDateContainer" class="w-full md:w-1/2">
                        <label for="startDate" class="block font-medium mb-2 text-sm">Start Date (+-5 days)
                        </label>
                        <div
                            class="w-full px-3 text-sm py-2.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600">
                            <input type="date" id="startDate" name="startDate"
                                class="outline-0 bg-transparent w-full cursor-pointer" />
                        </div>
                    </div>
                    <!-- Run Backtest Button -->
                    <div class="w-full md:w-1/3 md:flex md:gap-2">
                        <button onclick="runIntradayBacktest()" id="run-intraday-backtest-btn"
                            class="bg-blue-500 block flex focus:outline-0 font-medium gap-2 hover:bg-blue-600 items-center justify-center px-6 py-2.5 rounded-md text-white transition-colors w-full">Run
                            Intraday Backtest</button>
                    </div>
                </div>
            </div>
            <!-- Results Section -->
            <div id="resultsSection" class="border mb-6 p-4 rounded-lg hidden"
                style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb);">
                <div id="results-content" class="overflow-x-auto"></div>
                <div id="chart-section" class="hidden">
                    <div class="mt-3 bg-white rounded-lg p-4">
                        <div>
                            <h3 class="text-lg font-semibold">Intraday Backtest Chart</h3>
                        </div>
                    </div>
                    <div class="chart-container bg-white rounded-md p-4 mb-4">
                        <canvas id="intradayChart"></canvas>
                        <div class="text-center mt-4">
                            <button id="resetZoom">Reset Zoom</button>
                        </div>
                    </div>
                </div>
            </div>
            {% include 'algos/backtesting/faqs.html' %}
        </main>
        <div class="mt-auto">{% include 'blocks/footer.html' %}</div>
        <!-- Script Dependencies -->
        <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
        {% include 'algos/backtesting/options.html' %}
        {% include 'algos/backtesting-intraday/backend.html' %}
        <script>
            // Store intraday strategies
            window.intradayStrategies = [];
            window.selectedIntradayStrategy = null;
            window.intradayBacktestResults = {};
            // Initialize the CRUD object for algo strategies.
            const crud = new Crud("algo_strategies");
            // Fetch strategies and filter for Intraday
            async function fetchIntradayStrategies() {
                try {
                    const response = await crud.get({
                        filters: {
                            instrument: "option",
                            tradingMode: "Intraday"
                        }
                    });
                    const strategySelect = document.getElementById("strategySelect");
                    strategySelect.innerHTML = `<option value="">-- Choose Strategy --</option>`;
                    window.intradayStrategies = [];
                    if (response?.results?.length) {
                        response.results.forEach((strategy) => {
                            if (strategy.uid) {
                                window.intradayStrategies.push(strategy);
                                const option = document.createElement("option");
                                option.value = strategy.uid;
                                option.textContent = strategy.name || `Strategy ${strategy.uid}`;
                                if (strategy.isDeployed) {
                                    option.textContent += " (Live)";
                                }
                                strategySelect.appendChild(option);
                            }
                        });
                    }
                } catch (error) {
                    console.error("Error fetching intraday strategies:", error);
                }
            }
            // Listen for stock selection and fetch expiration dates
            document.addEventListener('stockSearchTriggered', async (e) => {
                const symbol = e.detail.symbol;
                const expirationSelect = document.getElementById('expirationSelect');
                expirationSelect.innerHTML = `<option value="">Loading...</option>`;
                try {
                    const currentYear = new Date().getFullYear();
                    const url = `https://iam.theaibull.com/v1/wg7ttpouv7/symbol/expirationDates/${symbol}/${currentYear}`;
                    const data = await axios.get(url);
                    expirationSelect.innerHTML = data.data.expirationDates.map(date => `<option value="${date}">${date}</option>`).join('');
                } catch (error) {
                    expirationSelect.innerHTML = `<option value="">Error loading dates</option>`;
                }
            });
            // When strategy is selected, store in global
            document.getElementById("strategySelect").addEventListener("change", function () {
                const selectedId = this.value;
                const found = window.intradayStrategies.find(s => s.uid === selectedId);
                window.selectedIntradayStrategy = found || null;
            });
            // On page load, fetch intraday strategies
            document.addEventListener("DOMContentLoaded", function () {
                fetchIntradayStrategies();
                // Disable expiration dropdown initially
                const expirationSelect = document.getElementById('expirationSelect');
                expirationSelect.disabled = true;
                // Set start date to today by default
                const startDateInput = document.getElementById('startDate');
                if (startDateInput) {
                    const today = new Date().toISOString().split('T')[0];
                    startDateInput.value = today;
                }
            });
            // Enable expiration dropdown and fetch dates only after both stock and strategy are selected
            let selectedStock = null;
            let selectedStrategy = null;
            document.addEventListener('stockSearchTriggered', function (e) {
                selectedStock = e.detail.symbol;
                // Only enable expiration if strategy is selected
                if (selectedStrategy) {
                    fetchAndEnableExpiration(selectedStock);
                } else {
                    document.getElementById('expirationSelect').disabled = true;
                }
            });
            document.getElementById("strategySelect").addEventListener("change", function () {
                selectedStrategy = this.value;
                // Only enable expiration if stock is selected
                if (selectedStock) {
                    fetchAndEnableExpiration(selectedStock);
                } else {
                    document.getElementById('expirationSelect').disabled = true;
                }
            });
            async function fetchAndEnableExpiration(symbol) {
                const expirationSelect = document.getElementById('expirationSelect');
                expirationSelect.disabled = false;
                expirationSelect.innerHTML = `<option value="">Loading...</option>`;
                try {
                    const currentYear = new Date().getFullYear();
                    const url = `https://iam.theaibull.com/v1/wg7ttpouv7/symbol/expirationDates/${symbol}/${currentYear}`;
                    const data = await axios.get(url);
                    // Filter only future dates
                    const today = new Date();
                    const futureDates = data.data.expirationDates.filter(dateStr => {
                        // dateStr format: YYYY-MM-DD or DD-MMM-YYYY
                        let d;
                        if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                            d = new Date(dateStr);
                        } else if (/^\d{2}-[A-Za-z]{3,}-\d{4}$/.test(dateStr)) {
                            // e.g. 31-Jul-2025
                            const [day, month, year] = dateStr.split('-');
                            d = new Date(`${month} ${day}, ${year}`);
                        } else {
                            d = new Date(dateStr);
                        }
                        return d >= today;
                    });
                    expirationSelect.innerHTML = futureDates.map(date => `<option value="${date}">${date}</option>`).join('');
                    // Auto-select the first future date if available
                    if (futureDates.length > 0) {
                        expirationSelect.value = futureDates[0];
                    }
                } catch (error) {
                    expirationSelect.innerHTML = `<option value="">Error loading dates</option>`;
                }
            }
        </script>
    </div>
</body>

</html>