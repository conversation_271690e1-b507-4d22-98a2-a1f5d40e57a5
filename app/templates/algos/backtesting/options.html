<script>

    // Render month tabs for all 12 months of the test year.
    function renderMonthTabsForYear(testYear) {
        const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const yearSuffix = testYear.toString().substring(2);
        const monthTabsContainer = document.getElementById("month-tabs");

        // First, add the overview table if running all months
        const overviewTable = generateMonthlyOverviewTable(testYear)

        // Create the main parent div for the month tabs - now a scrollable row
        const monthTabsParent = `<div class="month-tabs-parent bg-white flex overflow-x-auto gap-2 p-3 rounded-lg">`;

        // Then add the simplified month tabs without descriptions
        const monthTabs = months.map(month => `
            <button class="month-tab-header px-4 py-2 border border-gray-200 rounded-md whitespace-nowrap text-sm font-medium"
                data-month="${month}"
                onclick="${`fetchBulkBacktestForMonth('${month}')`}">
                ${month} ${yearSuffix}
            </button>
        `).join("");

        // Close the main parent div
        const monthTabsEnd = `</div>`;

        // To create a container for global summary
        const summaryHTML = displayGlobalSummary();

        monthTabsContainer.innerHTML = summaryHTML + overviewTable + monthTabsParent + monthTabs + monthTabsEnd;

        // Attach click events to the header for highlighting and showing results
        document.querySelectorAll(".month-tab-header").forEach(header => {
            header.onclick = function () {
                const selectedMonth = this.getAttribute("data-month");
                document.querySelectorAll(".month-tab-header").forEach(h => {
                    h.classList.remove("active", "bg-blue-500", "text-white");
                    h.classList.add("bg-white");
                });
                this.classList.add("active", "bg-blue-500", "text-white");
                this.classList.remove("bg-white");

                // If we have results for this month, display them
                const chartsContainer = document.querySelector(".chart-container");
                const computedContainer = document.getElementById("computedLegs");
                const container = document.getElementById("results-content");
                const dateTabsHeader = document.querySelector(".date-tabs-header");
                const selectedData = window.backtestResults?.[selectedMonth];

                const hasData = !!selectedData;

                chartsContainer?.classList.toggle("hidden", !hasData);
                dateTabsHeader?.classList.toggle("hidden", !hasData);

                if (hasData) {
                    updateBulkBacktestResults(selectedData);
                } else {
                    computedContainer.innerHTML = "";
                    container.innerHTML = `<p class="text-center text-gray-500 py-14">No bulk data available.</p>`;

                    // Destroy chart if it exists
                    window.bulkChartInstance?.destroy();
                }
            };
        });
    }

    // Fetch bulk backtest data for a given month or all months.
    async function fetchBulkBacktestForMonth(selectedMonth) {
        const testYear = document.getElementById("testYear").value;
        const symbol = document.getElementById("stock-input").value;
        const strategy = document.getElementById("strategySelect").value;

        if (!testYear || !symbol || !window.selectedStrategy) {
            showAlert("Please ensure test year, symbol, and strategy are selected.");
            return;
        }

        // Set backtest in progress flag
        window.backtestInProgress = true;

        // Update UI to show loading state
        const runButton = document.getElementById("run-backtest-btn");
        runButton.disabled = true;
        runButton.innerHTML = `<div class="w-4 h-4 border-4 border-white border-dashed rounded-full animate-spin"></div> Processing...`;

        // If we're running all months, selectedMonth will be null
        const monthToFetch = null;

        const payload = {
            strategy: selectedStrategy,
            testMonth: monthToFetch,
            testYear: parseInt(testYear),
            symbol: symbol
        };

        // Ensure results section is visible.
        document.getElementById("resultsSection").classList.remove("hidden");

        showLocalLoading(`Loading backtest data for all months of ${testYear}...`);


        try {
            const response = await fetch("/algo-backtest/", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            window.backtestYear = testYear;
            renderMonthTabsForYear(parseInt(testYear));
            // Update the UI with the fetched data
            displayGlobalSummary(data.global_summary);

            if (data.all_months) {
                // Process all months data
                window.backtestResults = {};

                const monthlyOverviewTable = document.getElementById("monthly-overview-table");
                if (monthlyOverviewTable) {
                    monthlyOverviewTable.classList.remove("hidden");
                }

                // Store results for each month
                Object.entries(data.all_months).forEach(([month, monthData]) => {
                    if (!monthData.error) {
                        window.backtestResults[month] = monthData;
                        updateOverviewTableRow(month, monthData);

                        // Mark this month as computed in the UI
                        const monthTab = document.querySelector(`.month-tab-header[data-month="${month}"]`);
                        if (monthTab) {
                            monthTab.classList.add("computed");
                        }
                    }
                });

                // Display the first month with valid results
                const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
                let displayedMonth = false;

                for (const month of months) {
                    if (window.backtestResults[month]) {
                        // Activate this month tab
                        document.querySelectorAll(".month-tab-header").forEach(h => {
                            if (h.getAttribute("data-month") === month) {
                                h.classList.add("active");
                            } else {
                                h.classList.remove("active");
                            }
                        });

                        // Display this month's results
                        updateBulkBacktestResults(window.backtestResults[month]);
                        displayedMonth = true;
                        break;
                    }
                }

                if (!displayedMonth) {
                    showLocalLoading("No valid backtest results were found for any month.", false);
                    // Hide Global summary and overview table, chart section and month tabs.
                    const globalSummary = document.getElementById("global-summary");
                    if (globalSummary) {
                        globalSummary.classList.add("hidden");
                    }
                    const monthlyOverviewTable = document.getElementById("monthly-overview-table");
                    if (monthlyOverviewTable) {
                        monthlyOverviewTable.classList.add("hidden");
                    }
                    document.querySelector(".month-tabs-parent")?.classList.add("hidden");
                    document.getElementById("chart-section").classList.add("hidden");
                } else {
                    document.querySelector(".month-tabs-parent")?.classList.remove("hidden");
                    document.getElementById("chart-section").classList.remove("hidden");
                }
            }
        } catch (err) {
            console.error("Error fetching backtest data:", err);
            // Update the specific month tab to show the error state
            const monthTab = document.querySelector(`.month-tab-header[data-month="${selectedMonth}"]`);
            if (monthTab) {
                monthTab.classList.add("bg-red-100", "border-red-300");
                monthTab.title = `Error: ${err.message}`;
            }

            console.log("Error fetching data for", selectedMonth, ":", err.message);
            showLocalLoading(`Error fetching data for ${selectedMonth}: ${err.message}`, false);
        } finally {
            // Reset UI state
            window.backtestInProgress = false;
            runButton.disabled = false;
            runButton.textContent = "Run Backtest";

            // No need to reset month buttons since we always run all months
        }
    }

    // Render computed legs section with Entry Date and Overall Expiry Date.
    function renderComputedLegs(data) {
        const computedContainer = document.getElementById("computedLegs");
        if (!data || !data["Computed Legs"] || !data["Computed Legs"].length) {
            computedContainer.innerHTML = "";
            return;
        }
        let headerHTML = "<div class='flex gap-3 header-container mb-3 text-sm'>";
        if (data["Entry Date"]) {
            headerHTML += `<p class="bg-blue-50 px-3 py-0.5 rounded-md">Entry Date:<span class="font-medium pl-2">${data["Entry Date"]}</span></p>`;
        }
        if (data["Overall Expiry Date"]) {
            headerHTML += `<p class="bg-rose-50 px-3 py-0.5 rounded-md">Overall Expiry Date:<span class="font-medium pl-2">${data["Overall Expiry Date"]}</span></p>`;
        }

        headerHTML += "</div>";  // Close the parent div

        computedContainer.innerHTML = headerHTML;

        let legsHTML = `<table class="min-w-full text-left text-sm">
            <thead class="bg-gray-100 text-left">
              <tr>
                <th class="px-2 py-1 border font-medium">Type</th>
                <th class="px-2 py-1 border font-medium">Strike</th>
                <th class="px-2 py-1 border font-medium">LTP</th>
                <th class="px-2 py-1 border font-medium">Expiry Date</th>
                <th class="px-2 py-1 border font-medium">Expiry Days</th>
                
              </tr>
            </thead>
            <tbody>`;
        data["Computed Legs"].forEach(leg => {
            let ltp_display = leg.ltp !== null && leg.ltp !== undefined ? leg.ltp : leg.op_pr;

            legsHTML += `<tr class="border-b text-left">
                <td class="px-2 py-1 border">${leg.type}</td>
                <td class="px-2 py-1 border">${leg.strike}</td>
                <td class="px-2 py-1 border">${ltp_display}</td>
                <td class="px-2 py-1 border">${leg.expiryDate}</td>
                <td class="px-2 py-1 border">${leg.expiry_days}</td>
              </tr>`;
        });
        legsHTML += `</tbody></table>`;
        computedContainer.innerHTML = `
            <div class="bg-white rounded-lg p-4 mb-4">
              <h3 class="font-semibold mb-2">Computed Legs</h3>
              ${headerHTML}
              ${legsHTML}
            </div>`;
    }

    // Function to calculate monthly summary statistics
    function calculateMonthlySummary(data) {
        if (!data || !data.results || !data.results.length) {
            return null;
        }

        let maxProfit = { value: -Infinity, date: '', index: -1 };
        let maxLoss = { value: Infinity, date: '', index: -1 };
        let expiryPL = { value: 0, date: data["Overall Expiry Date"] || '' };

        // Find max profit and max loss
        data.results.forEach((result, index) => {
            const pl = result["Profit/Loss"];
            const date = result["Timestamp"];

            if (pl > maxProfit.value) {
                maxProfit.value = pl;
                maxProfit.date = date;
                maxProfit.index = index;
            }

            if (pl < maxLoss.value) {
                maxLoss.value = pl;
                maxLoss.date = date;
                maxLoss.index = index;
            }
        });

        // Find expiry day P/L (last day in results)
        if (data.results.length > 0) {
            const lastResult = data.results[data.results.length - 1];
            expiryPL.value = lastResult["Profit/Loss"];
            expiryPL.date = lastResult["Timestamp"];
        }

        return {
            maxProfit,
            maxLoss,
            expiryPL
        };
    }

    // Function to render monthly summary
    function renderMonthlySummary(data) {
        const summary = calculateMonthlySummary(data);
        if (!summary) return '';

        return `
            <div class="bg-white rounded-lg p-4 mb-4">
                <h3 class="font-semibold mb-2">Monthly Summary</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="border rounded-md p-3 bg-white">
                        <h4 class="font-medium text-sm mb-1">Maximum Profit</h4>
                        <p class="text-green-600 font-bold">${summary.maxProfit.value.toFixed(2)}</p>
                        <p class="text-xs text-gray-500">Date: ${summary.maxProfit.date}</p>
                    </div>
                    <div class="border rounded-md p-3 bg-white">
                        <h4 class="font-medium text-sm mb-1">Maximum Loss</h4>
                        <p class="text-red-600 font-bold">${summary.maxLoss.value.toFixed(2)}</p>
                        <p class="text-xs text-gray-500">Date: ${summary.maxLoss.date}</p>
                    </div>
                    <div class="border rounded-md p-3 bg-white">
                        <h4 class="font-medium text-sm mb-1">Expiry Day P & L</h4>
                        <p class="${summary.expiryPL.value >= 0 ? 'text-green-600' : 'text-red-600'} font-bold">${summary.expiryPL.value.toFixed(2)}</p>
                        <p class="text-xs text-gray-500">Date: ${summary.expiryPL.date}</p>
                    </div>
                </div>
            </div>`;
    }

    // Function to render rebalancing actions
    function renderRebalancingActions(data) {
        let rebalancingHTML = '';

        
        const newLegs = data?.new_legs;
        const actions = data?.rules?.flatMap(rule => rule.actions); // Flatten actions from all rules
        const rebalancePl = data?.rebalance_pl || 0;

        // Render New Legs
        if (newLegs && Object.keys(newLegs).length > 0) {
            rebalancingHTML += `<h3 class="font-semibold text-lg text-neutral-800 mt-5">New Legs</h3><ul class="list-disc pl-5">`;

            Object.keys(newLegs).forEach(legId => {
                const newLeg = newLegs[legId];

                const newLegType = newLeg.type || 'N/A';
                const newLegStrike = newLeg.strike || 'N/A';
                const newLegTrType = newLeg.tr_type || 'N/A';
                const newLegLtp = newLeg.ltp || 'N/A';

                // Build the display for new legs
                rebalancingHTML += `
                    <li class="text-sm text-neutral-800">
                        <strong>Leg ID:</strong> ${legId} <br/>
                        <strong>Strike:</strong> ${newLegStrike} 
                        <strong>Type:</strong> ${newLegType} 
                        <strong>Tr Type:</strong> ${newLegTrType} 
                        <strong>LTP:</strong> ${newLegLtp} 
                    </li><br/>
                `;
            });

            rebalancingHTML += `</ul>`;
        } else {
            rebalancingHTML += `<p class="text-gray-500">NA</p>`;
        }

        if (rebalancePl) {
            rebalancingHTML += `<h3 class="font-semibold text-lg text-neutral-800 mt-5">Rebalance P/L: ${rebalancePl}</h3><ul class="list-disc pl-5">`;
        }

        return rebalancingHTML;
    }


    // Update bulk backtest results.
    function updateBulkBacktestResults(data) {
        // Render computed legs first.
        renderComputedLegs(data);

        const container = document.getElementById("results-content");
        if (!data || !data.results || !data.results.length) {
            container.innerHTML = `<p class="text-center text-gray-500">No bulk data available.</p>`;
            return;
        }

        // Render monthly summary
        const monthlySummaryHTML = renderMonthlySummary(data);

        let tableRows = '';
        const timestamps = [];
        const spotPLs = [];
        const spotValues = [];
        data.results.forEach((result, index) => {
            const ts = result.Timestamp;
            timestamps.push(ts);
            spotPLs.push(result["Profit/Loss"]);
            spotValues.push(Math.round(result["Spot"]));

            const rebalancingActions = result.rebalancing_actions || {};
            console.log("rebalancingActions", rebalancingActions)
            let rebalancingHTML = renderRebalancingActions(rebalancingActions);

            let detailsHTML = `<table class="min-w-full text-sm text-left bg-white"><thead class="bg-gray-100"><tr>
                <th class="px-2 py-1 border">Strike</th>
                <th class="px-2 py-1 border">Type</th>
                <th class="px-2 py-1 border">Tr_Type</th>
                <th class="px-2 py-1 border">Price</th>
                <th class="px-2 py-1 border">LTP</th>
                <th class="px-2 py-1 border">Lots</th>
                <th class="px-2 py-1 border">P & L</th>
            </tr></thead><tbody>`;

            result.legs.forEach(leg => {
                detailsHTML += `<tr class="border-b">
                    <td class="px-2 py-1 border">${leg.strike}</td>
                    <td class="px-2 py-1 border">${leg.type}</td>
                    <td class="px-2 py-1 border">${leg.tr_type}</td>
                    <td class="px-2 py-1 border">${leg.price ?? leg.op_pr ?? "N/A"}</td>
                    <td class="px-2 py-1 border">${leg.ltp ?? "N/A"}</td>
                    <td class="px-2 py-1 border">${leg.lots || 1}</td>
                    <td class="px-2 py-1 border">${leg.profit_loss * (leg.lots || 1)}</td>
                </tr>`;
            });
            detailsHTML += `</tbody></table>`;

            // Apply color based on Profit/Loss.
            let plClass = "";
            if (result["Profit/Loss"] < 0) {
                plClass = "pl-negative";
            } else if (result["Profit/Loss"] > 0) {
                plClass = "pl-positive";
            }
            tableRows += `
                <tr class="hover:bg-gray-50">
                    <td class="px-4 py-2 border">
                        ${ts} <button class="expand-btn px-1 hover:bg-blue-50 border rounded text-[15px] text-neutral-500 ml-0.5" data-index="${index}">+</button>
                    </td>
                    <td class="px-4 py-2 border ${plClass}">${result["Profit/Loss"]}</td>
                    <td class="px-4 py-2 border">${Math.round(result["Spot"])}</td>
                    <td class="px-4 py-2 border">
                        ${rebalancingHTML}  
                    </td>
                </tr>
                <tr class="detail-row" id="detail-row-${index}" style="display:none;">
                    <td class="px-4 py-2 border bg-blue-50" colspan="3">
                        ${detailsHTML}
                    </td>
                </tr>`;
        });
        container.innerHTML = `
                ${monthlySummaryHTML}
                <div class="bg-white rounded-lg p-4 mt-4">
                    <h3 class="font-semibold mb-2">Bulk Backtest Results</h3>
                    <table class="min-w-full text-sm">
                        <thead>
                            <tr class="bg-gray-100 text-left">
                                <th class="px-4 py-2 border">Timestamp</th>
                                <th class="px-4 py-2 border">Profit/Loss</th>
                                <th class="px-4 py-2 border">Spot</th>
                                <th class="px-4 py-2 border">Rebalancing Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${tableRows}
                        </tbody>
                    </table>
                </div>`;
        document.querySelectorAll(".expand-btn").forEach(btn => {
            btn.addEventListener("click", function () {
                const idx = this.getAttribute("data-index");
                const detailRow = document.getElementById("detail-row-" + idx);
                if (detailRow.style.display === "none") {
                    detailRow.style.display = "table-row";
                    this.textContent = "-";
                } else {
                    detailRow.style.display = "none";
                    this.textContent = "+";
                }
            });
        });

        // Render chart with improved handling
        renderBulkChart(timestamps, spotPLs, spotValues);
    }

    // Separate function for chart rendering
    function renderBulkChart(timestamps, spotPLs, spotValues) {
        console.log("Rendering chart with timestamps:", timestamps.length);
        const ctx = document.getElementById("bulkChart").getContext("2d");

        // Destroy existing chart if it exists
        if (window.bulkChartInstance) {
            window.bulkChartInstance.destroy();
        }

        // Limit data points if too many
        const maxPoints = 50; // reduced for better performance
        let chartTimestamps = timestamps;
        let chartSpotPLs = spotPLs;
        let chartSpotValues = spotValues;

        if (timestamps.length > maxPoints) {
            // Sample every nth data point
            const step = Math.ceil(timestamps.length / maxPoints);
            chartTimestamps = [];
            chartSpotPLs = [];
            chartSpotValues = [];

            for (let i = 0; i < timestamps.length; i += step) {
                chartTimestamps.push(timestamps[i]);
                chartSpotPLs.push(spotPLs[i]);
                chartSpotValues.push(spotValues[i]);
            }
        }

        console.log("Timestamps:", chartTimestamps.length);
        console.log("Spot P/L:", chartSpotPLs.length);
        console.log("Spot Values:", chartSpotValues.length);

        window.bulkChartInstance = new Chart(ctx, {
            type: "line",
            data: {
                labels: chartTimestamps,
                datasets: [
                    {
                        label: "Profit/Loss",
                        data: chartSpotPLs,
                        pointRadius: 2,
                        borderWidth: 2,
                        fill: false,
                        borderColor: "rgba(75, 192, 192, 1)",
                        backgroundColor: "rgba(75, 192, 192, 0.2)",
                        yAxisID: 'y',
                    },
                    {
                        label: "Spot Price",
                        data: chartSpotValues,
                        pointRadius: 2,
                        borderWidth: 2,
                        fill: false,
                        borderColor: "rgba(54, 162, 235, 1)",
                        backgroundColor: "rgba(54, 162, 235, 0.2)",
                        yAxisID: 'y1',
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Profit/Loss'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Spot Price'
                        },
                        grid: {
                            drawOnChartArea: false,
                        }
                    }
                }
            }
        });
    }

    // Function to clear previous results and UI state
    function clearPreviousResults() {
        // Clear global results object
        window.backtestResults = {};

        // Clear computed legs section
        document.getElementById("computedLegs").innerHTML = "";

        // Clear results content
        document.getElementById("results-content").innerHTML = "";

        // Clear chart if it exists
        if (window.bulkChartInstance) {
            window.bulkChartInstance.destroy();
            window.bulkChartInstance = null;
        }

        const monthTabsContainer = document.getElementById("month-tabs");
        monthTabsContainer.innerHTML = "";

        // Clear overview table rows if it exists
        document.querySelectorAll('[id^="overview-"]').forEach(row => {
            if (row.tagName === 'TR') {
                const month = row.id.replace('overview-', '');
                row.innerHTML = `
                    <td class="px-4 py-2 border">${month}</td>
                    <td class="px-4 py-2 border">-</td>
                    <td class="px-4 py-2 border">-</td>
                    <td class="px-4 py-2 border text-center">-</td>
                    <td class="px-4 py-2 border text-center">-</td>
                `;
            }
        });
    }
</script>

<script>
    function generateMonthlyOverviewTable(testYear) {
        const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        return `
            <div id="monthly-overview-table" class="w-full bg-white rounded-lg p-4 hidden">
                <h3 class="font-semibold mb-2">Overview for ${testYear}</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full text-sm">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="px-4 py-2 border">Month</th>
                                <th class="px-4 py-2 border">Max Profit (Day)</th>
                                <th class="px-4 py-2 border">Max Loss (Day)</th>
                                <th class="px-4 py-2 border">Trade Premium</th>
                                <th class="px-4 py-2 border">Stop Loss</th>
                                <th class="px-4 py-2 border">Target Profit</th>
                                <th class="px-4 py-2 border">Spot (Entry/Exit)</th>
                                <th class="px-4 py-2 border">Exit Condition</th>
                                <th class="px-4 py-2 border">Rebalance Count</th>

                            </tr>
                        </thead>
                        <tbody>
                            ${months.map(month => `
                                <tr class="hover:bg-gray-50" id="overview-${month}">
                                    <td class="px-4 py-2 border">${month}</td>
                                    <td class="px-4 py-2 border">-</td>
                                    <td class="px-4 py-2 border">-</td>
                                    <td class="px-4 py-2 border text-center">-</td>
                                    <td class="px-4 py-2 border text-center">-</td>
                                    <td class="px-4 py-2 border text-center">-</td>
                                    <td class="px-4 py-2 border text-center">-</td>
                                    <td class="px-4 py-2 border text-center">-</td>
                                    <td class="px-4 py-2 border text-center">-</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>`;
    }

    function updateOverviewTableRow(month, data) {
        if (!data || !data.summary) return;

        const summary = data.summary;
        const row = document.getElementById(`overview-${month}`);
        if (!row) return;

        let exitConditionText = "";
        if (summary.trade.result.exit_condition === "target_profit") {
            exitConditionText = `Target Profit on ${summary.trade.result.exit_condition_date}`;
        } else if (summary.trade.result.exit_condition === "stop_loss") {
            exitConditionText = `Stop Loss on ${summary.trade.result.exit_condition_date}`;
        } else if (summary.trade.result.exit_condition === "expiry") {
            exitConditionText = `Expiry Date on ${summary.trade.result.exit_condition_date}`;
        } else {
            exitConditionText = "N/A";
        }

        row.innerHTML = `
            <td class="px-4 py-2 border">${month}</td>
            <td class="px-4 py-2 border text-green-600">
                ${summary.max_profit.value.toFixed(2)}
                <div class="text-xs text-gray-500">${summary.max_profit.timestamp}</div>
            </td>
            <td class="px-4 py-2 border text-red-600">
                ${summary.max_loss.value.toFixed(2)}
                <div class="text-xs text-gray-500">${summary.max_loss.timestamp}</div>
            </td>
            <td class="px-4 py-2 border text-center">${summary.trade.premium.toFixed(2)}</td>
            <td class="px-4 py-2 border text-center">${summary.trade.stop_loss.toFixed(2)}</td>
            <td class="px-4 py-2 border text-center">${summary.trade.target_profit.toFixed(2)}</td>
            <td class="px-4 py-2 border text-center">
                Entry: ${summary.trade.spot.entry} <br/>
                Exit: ${summary.trade.spot.exit}
            </td>
            <td class="px-4 py-2 border text-center">
                ${exitConditionText}
            </td>
            <td class="px-4 py-2 border text-center">
                ${summary.trade.rebalancing_actions_per_ts.length}
            </td>
        `;
    }

    function displayGlobalSummary(globalSummary) {
        // Create the summary HTML content.
        const summaryHTML = `
            <div id="global-summary" class="bg-white dark:bg-neutral-800 rounded-lg p-4 w-full hidden">
                <h3 class="text-lg font-semibold text-neutral-800">Global Summary</h3>
                <div id="global-summary-content" class="mt-2 dark:text-gray-400 grid grid-cols-2 md:grid-cols-4 gap-4 justify-center">
                </div>
            </div>
        `;

        const existingSummaryElement = document.getElementById('global-summary');

        if (!globalSummary) return summaryHTML;

        if (existingSummaryElement) {
            existingSummaryElement.classList.remove('hidden');
            existingSummaryElement.querySelector('#global-summary-content').innerHTML = `
                <p class="text-neutral-600">Total Trades:<span class="text-neutral-900 font-medium pl-1">${globalSummary.total_trades || 0}</span></p>
                <p class="text-neutral-600">Stop Loss:<span class="text-neutral-900 font-medium pl-1">${globalSummary.stop_loss || 0}</span></p>
                <p class="text-neutral-600">Target Profit:<span class="text-neutral-900 font-medium pl-1">${globalSummary.target_profit || 0}</span></p>
                <p class="text-neutral-600">Expiry Dates:<span  class="text-neutral-900 font-medium pl-1">${globalSummary.expiry_dates || 0}</span></p>
            `;
        } else {
            return summaryHTML;
        }
    }


</script>