<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Algo Backtesting Dashboard - Analyze Historical Data | AI Bull</title>
    <meta name="description"
        content="Backtest your algo trading strategies using historical data with our algo backtesting dashboard." />
    <link rel="canonical" href="https://theaibull.com/algos/backtesting" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" size="60x60" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/algos/backtesting" />
    <meta property="og:title" content="Algo Backtesting Dashboard - Analyze Historical Data" />
    <meta property="og:description"
        content="Select a strategy and a test year to analyze the historical performance of your algo trading strategies with our algo backtesting dashboard." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title" content="Algo Backtesting Dashboard - Analyze Historical Data" />
    <meta name="twitter:description"
        content="Select a strategy and a test year to analyze the historical performance of your algo trading strategies with our algo backtesting dashboard." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    {% include 'blocks/head.html' %}
    {% include 'algos/backtesting/css.html' %}
</head>

<body class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100">
    {% include 'blocks/common.html' %}
    {% include 'blocks/stock-symbols.html' %}
    {% include 'blocks/info.html' %}

    <!-- Global Spinner -->
    <div id="loadingSpinner">
        <img src="https://i.imgur.com/llF5iyg.gif" alt="Loading..." />
    </div>

    <div class="rhs-block duration-300 transition-width flex flex-col min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="
        background-image: radial-gradient(
          circle at 100% 0%,
          #fff5d7,
          #f0fdff,
          #f5f5f5,
          #f8f8f8,
          #f3f3f3,
          #f5f5f500,
          #e2e7eb
        );
      ">
        <main class="p-3 mx-auto max-w-7xl w-full">
            <!-- Header Section -->
            <div class="flex flex-col bg-white dark:bg-neutral-800 px-3 py-2 rounded-lg mb-3">
                <div class="md:flex justify-between items-center">
                    <div class="flex items-center">
                        <h1 class="md:text-xl text-lg font-semibold tracking-tight">
                            Algo Backtesting Dashboard
                        </h1>
                        <h2 class="text-gray-600 dark:text-gray-400 ml-2 hidden lg:block">
                            Analyze your algorithmic trading strategies using historical data
                        </h2>
                        <button id="openModalBtn" onclick="openModal()" class="ml-2">

                            <div class="flex group text-sm">
                                <div
                                    class="rounded-full shadow-[0_6px_20px_0_#10182814] whitespace-nowrap cursor-pointer bg-white/40 group-hover:bg-white border border-[#e9ecf666] flex items-center font-medium transition-[background-color] duration-600">
                                    <div
                                        class="bg-white duration-[.10s] flex items-center justify-center p-1 rounded-full shadow-[0_2px_10px_0_#10182814,0_0_0_1px_#e6eaf4] text-[#0d0f2c] transition-colors z-10">
                                        <div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-circle-help">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                                <path d="M12 17h.01"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div
                                        class="absolute bg-white border duration-500 ease-in-out grid grid-cols-[0fr] group-hover:grid-cols-[1fr] group-hover:mx-[1.4rem] group-hover:opacity-100 opacity-0 px-2 rounded-r-xl shadow-[0_6px_20px_0_#10182814] text-[#333b52] transition-all z-1">
                                        <div
                                            class="bg-clip-text bg-gradient-to-r from-blue-600 inline-block overflow-hidden py-1 text-transparent text-xs to-indigo-600 via-pink-600">
                                            How to Use This Page</div>
                                    </div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Active Stocks Search Block -->
            <div class="mx-auto container mb-4">
                <div class="flex">
                    <div class="w-full rounded-lg md:p-8 p-4 border border-white"
                        style="background-image: linear-gradient(45deg, #f0faff, #ffffff)">
                        {% with enable_backtesting=false, show_submit_button=false %} {%
                        include 'blocks/search-active-recent-stocks.html' %} {% endwith %}
                    </div>
                </div>
            </div>

            <!-- Combined Backtest Controls -->
            <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 mb-6">
                <div class="flex flex-col md:flex-row gap-4 md:items-end">
                    <!-- Strategies Dropdown -->
                    <div id="strategySelectContainer" class="w-full md:w-1/2">
                        <label for="strategySelect" class="block font-medium mb-2 text-sm">Choose Algo Trading
                            Strategy</label>
                        <div
                            class="w-full px-3 cursor-pointer text-sm py-2.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                            <select id="strategySelect" name="strategySelect"
                                class="outline-0 bg-transparent w-full cursor-pointer">
                                <option value="">-- Choose Strategy --</option>
                            </select>
                        </div>
                    </div>
                    <!-- Test Year Dropdown -->
                    <div class="w-full md:w-1/2 hidden" id="testYearContainer">
                        <label for="testYear" class="block font-medium mb-2 text-sm">Select Test Year for
                            Backtesting</label>
                        <div
                            class="w-full px-3 cursor-pointer text-sm py-2.5 bg-gray-100 border border-gray-200 rounded-md dark:bg-neutral-700 dark:border-neutral-600 focus:outline-0 focus:border-blue-500 transition-all">
                            <select id="testYear" name="testYear"
                                class="outline-0 bg-transparent w-full cursor-pointer">
                                <option value="">-- Choose Year --</option>
                                <option value="2025">2025</option>
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                                <option value="2022">2022</option>
                            </select>
                        </div>
                    </div>
                    <!-- Run Backtest Button -->
                    <div class="w-full md:w-1/3 md:flex md:gap-2">
                        <button onclick="runBacktest()" id="run-backtest-btn"
                            class="bg-blue-500 block flex focus:outline-0 font-medium gap-2 hover:bg-blue-600 items-center justify-center px-6 py-2.5 rounded-md text-white transition-colors w-full">
                            Run Backtest
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div id="resultsSection" class="border mb-6 p-4 rounded-lg hidden" style="
    background-image: radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb);
">
                <!-- Dynamic Date Tabs Container -->
                <div id="month-tabs" class="hidden"></div>
                <!-- Computed Legs Section -->
                <div id="computedLegs" class="mb-4"></div>
                <!-- Content Area for Backtest Results -->
                <div id="results-content" class="overflow-x-auto"></div>
                <!-- Chart Container with enhanced date tabs -->
                <div id="chart-section" class="hidden">
                    <div class="mt-3 bg-white rounded-lg p-4">
                        <div>
                            <h3 class="text-lg font-semibold">Historical Algo Trading Data</h3>
                        </div>
                    </div>
                    <div class="chart-container bg-white rounded-md p-4 mb-4">
                        <canvas id="bulkChart"></canvas>
                        <div class="text-center mt-4">
                            <button id="resetZoom">Reset Zoom</button>
                        </div>
                    </div>
                </div>
            </div>

            {% include 'algos/backtesting/faqs.html' %}
        </main>

        <div class="mt-auto">
            {% include 'blocks/footer.html' %}
        </div>

        <!-- Script Dependencies -->
        <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>

        {% include 'algos/backtesting/options.html' %}

        <script>
            // Global variables to store strategies.
            window.allStrategies = [];
            window.selectedStrategy = null;
            window.backtestInProgress = false;
            window.backtestResults = {}; // Store results by month

            // Initialize the CRUD object for algo strategies.
            const crud = new Crud("algo_strategies");

            async function fetchStrategiesForBacktesting() {
                try {
                    const response = await crud.get({
                        filters: {
                            instrument: "option"
                        }
                    });
                    const strategySelect = document.getElementById("strategySelect");
                    strategySelect.innerHTML = `<option value="">-- Choose Strategy --</option>`;
                    window.allStrategies = [];

                    if (response?.results?.length) {
                        response.results.forEach((strategy) => {
                            if (strategy.uid) {
                                window.allStrategies.push(strategy);
                                const option = document.createElement("option");
                                option.value = strategy.uid;
                                option.textContent = strategy.name || `Strategy ${strategy.uid}`;
                                if (strategy.isDeployed) {
                                    option.textContent += " (Live)";
                                }
                                strategySelect.appendChild(option);
                            } else {
                                console.warn("Strategy missing uid:", strategy);
                            }
                        });
                        console.log("Strategies loaded successfully.");

                        // Strategy from the url
                        const urlParams = new URLSearchParams(window.location.search);
                        const strategyUid = urlParams.get('s');
                        if (strategyUid) {
                            window.selectedStrategy = window.allStrategies.find(s => s.uid === strategyUid) || null;
                        }

                        // If a strategy is selected, update the strategy select dropdown.
                        if (window.selectedStrategy) {
                            document.getElementById("strategySelect").value = window.selectedStrategy.uid;
                            // Fire the change event to trigger the strategy select dropdown.
                            document.getElementById("strategySelect").dispatchEvent(new Event("change"));
                        }
                    } else {
                        console.log("No strategies found in response.");
                    }
                } catch (error) {
                    console.error("Error fetching strategies:", error);
                }
            }

            // Local loading indicator inside results-content.
            function showLocalLoading(message, circle = true) {
                const resultsContent = document.getElementById("results-content");
                resultsContent.innerHTML = `<div class="flex flex-col items-center justify-center p-4 text-center text-gray-600 min-h-[20px]">
                        <div class="flex flex-col items-center">
                            ${circle ? `<div class="w-16 h-16 border-4 border-blue-500 border-dashed rounded-full animate-spin"></div>` : ""}
                            <span class="mt-4 text-sm font-medium text-gray-700">${message}</span>
                        </div>
                    </div>
                `;
            }

            // When a strategy is selected, store the full strategy JSON in a global variable.
            document.getElementById("strategySelect").addEventListener("change", function () {
                const selectedId = this.value;
                const found = window.allStrategies.find(s => s.uid === selectedId);
                window.selectedStrategy = found || null;
                console.log("Selected strategy:", window.selectedStrategy);

                // Get the test year container and select element
                const testYearContainer = document.getElementById("testYearContainer");
                const testYearSelect = document.getElementById("testYear");

                if (window.selectedStrategy) {
                    // Show the test year container
                    testYearContainer.classList.remove("hidden");
                    // Reset test year selection
                    testYearSelect.value = "";
                } else {
                    // Hide the test year container
                    testYearContainer.classList.add("hidden");
                    // Reset test year selection
                    testYearSelect.value = "";
                }
            });

            // runBacktest function modified for stock instruments.
            async function runBacktest() {
                clearPreviousResults();
                const strategyUid = document.getElementById("strategySelect").value;
                const testYear = document.getElementById("testYear").value;
                const chartSection = document.getElementById("chart-section");
                chartSection.classList.add("hidden");
                if (!strategyUid) {
                    showAlert("Please select a strategy.");
                    return;
                }
                if (!testYear) {
                    showAlert("Please select a test year.");
                    return;
                }

                // Always fetch all months
                await fetchBulkBacktestForMonth(null);
            }

            document.addEventListener("DOMContentLoaded", function () {
                // Check loggin status
                if (!isLoggedIn()) {
                    const strategySelectContainer = document.getElementById("strategySelectContainer");
                    strategySelectContainer.onclick = function () {
                        showModal("Please Login/Signup to access this feature");
                    }
                    return;
                }
                console.log("Fetching strategies for backtesting...");
                fetchStrategiesForBacktesting();
            });
        </script>
    </div>
</body>

</html>