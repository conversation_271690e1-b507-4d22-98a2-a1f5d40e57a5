<!-- FAQ Section -->
<div class="mt-8 text-sm">
    <h2 class="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
    <div class="space-y-4">
        <details class="border rounded-lg bg-white">
            <summary class="cursor-pointer p-4">What is backtesting?</summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    Backtesting involves testing a trading strategy using historical data to evaluate its
                    performance.
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white">
            <summary class="cursor-pointer p-4">How do I select the test date?</summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    Use the dropdown above to choose the desired test year. The system will fetch historical
                    data accordingly.
                </p>
            </div>
        </details>
        <details class="border rounded-lg bg-white">
            <summary class="cursor-pointer p-4">What data is displayed?</summary>
            <div class="p-4 border-t">
                <p class="text-gray-700 dark:text-gray-300 leading-6">
                    For option strategies using calendar entry, month tabs will be shown for the test year.
                    Each tab contains a header (month and year), a help text, and a Compute button in the
                    body.
                    The computed legs (with Entry Date and Overall Expiry Date) will be displayed above the
                    bulk results.
                </p>
            </div>
        </details>
    </div>
</div>