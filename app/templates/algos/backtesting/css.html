<style>
    /* Global spinner */
    #loadingSpinner {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
    }

    /* Date tabs styling - enhanced */
    #month-tabs {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
        gap: 0.25rem;
        justify-content: flex-start;
        background: #f8f9fa;
        border-radius: 8px;
        scrollbar-width: thin;
        scrollbar-color: #cbd5e0 #f8f9fa;
    }

    #month-tabs::-webkit-scrollbar {
        height: 6px;
    }

    #month-tabs::-webkit-scrollbar-track {
        background: #f8f9fa;
    }

    #month-tabs::-webkit-scrollbar-thumb {
        background-color: #cbd5e0;
        border-radius: 6px;
    }

    .date-tab {
        min-width: 90px;
        padding: 0.5rem 0.75rem;
        background-color: white;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        font-size: 0.9rem;
        transition: all 0.2s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .date-tab .day-name {
        font-weight: 600;
        color: #4a5568;
        font-size: 0.75rem;
    }

    .date-tab .date-num {
        font-size: 1.1rem;
        font-weight: 700;
        color: #2d3748;
    }

    .date-tab .month-name {
        font-size: 0.7rem;
        color: #718096;
        text-transform: uppercase;
    }

    .date-tab:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border-color: #cbd5e0;
    }

    .date-tab.active {
        background: linear-gradient(135deg, #3182ce, #4299e1);
        border-color: #3182ce;
        color: white;
    }

    .date-tab.active .day-name,
    .date-tab.active .date-num,
    .date-tab.active .month-name {
        color: white;
    }

    .date-tab.today {
        border-color: #3182ce;
    }

    .date-tab.today::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: #3182ce;
        border-radius: 3px 3px 0 0;
    }

    .date-tabs-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .date-nav-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .date-nav-btn {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 0.25rem;
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .date-nav-btn:hover {
        background: #f7fafc;
        border-color: #cbd5e0;
    }

    /* Dynamic date tabs styling - updated for scrollable row */
    #month-tabs {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .month-tabs-parent {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
        gap: 0.5rem;
        padding: 0.75rem;
        scrollbar-width: thin;
        scrollbar-color: #cbd5e0 #f8f9fa;
    }

    .month-tabs-parent::-webkit-scrollbar {
        height: 6px;
    }

    .month-tabs-parent::-webkit-scrollbar-track {
        background: #f8f9fa;
    }

    .month-tabs-parent::-webkit-scrollbar-thumb {
        background-color: #cbd5e0;
        border-radius: 6px;
    }

    .month-tab-header {
        font-weight: 500;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.2s ease;
        min-width: 80px;
    }

    .month-tab-header:hover {
        background-color: #f0f9ff;
        border-color: #90cdf4;
    }

    .month-tab-header.active {
        background-color: #3182ce !important;
        color: #fff !important;
        border-color: #2c5282;
    }

    /* Profit/Loss coloring */
    .pl-negative {
        color: red;
    }

    .pl-positive {
        color: green;
    }

    /* Chart container styling */
    .chart-container {
        position: relative;
        height: 600px;
        /* Increased from 400px to 550px for better visibility */
        width: 100%;
        margin-top: 10px;
    }

    .month-tab-header.computed {
        background-color: #e6f7ff;
        border-bottom: 2px solid #1890ff;
    }

    .spinner {
        display: inline-block;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    /* Enhanced Reset Zoom button styling */
    #resetZoom {
        display: inline-block;
        margin-top: 12px;
        padding: 8px 16px;
        background-color: #111111;
        color: white;
        font-weight: 600;
        font-size: 10px;
        border-radius: 6px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: none;
        transition: all 0.2s ease;
        cursor: pointer;
        position: relative;
        z-index: 10;
    }

    #resetZoom:hover {
        background-color: #2c5282;
        box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    #resetZoom:before {
        content: "🔍";
        margin-right: 6px;
    }
</style>