<!-- Main Controls for Stock Scalping -->
{% include 'algos/scalping/default-filters.html' %}

<div>
    <div>
        <div class="bg-white rounded-lg p-4 my-3"
            style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #ffffff, #f8f8f8, #ffffff, #f5f5f500, #ffffff)">

            <!-- Strategy Type Section -->
            <div>
                <p class="block font-medium mb-1">Strategy
                    Type</p>
                <p class="text-gray-600 mb-4 text-sm">Choose your market direction: Bullish or Bearish stance.</p>
                <div class="flex w-full max-w-[20rem] bg-gray-100 dark:bg-gray-800 p-1 rounded-lg text-sm">
                    <label class="flex-1">
                        <input type="radio" name="strategy-type" value="Bullish" class="hidden peer" checked />
                        <div
                            class="text-center p-2 rounded-md peer-checked:bg-green-500 peer-checked:text-white cursor-pointer transition-all duration-200 hover:bg-gray-300 dark:hover:bg-gray-700">
                            <span class="font-medium">Bullish</span>
                        </div>
                    </label>
                    <label class="flex-1">
                        <input type="radio" name="strategy-type" value="Bearish" class="hidden peer" />
                        <div
                            class="text-center p-2 rounded-md peer-checked:bg-red-500 peer-checked:text-white cursor-pointer transition-all duration-200 hover:bg-gray-300 dark:hover:bg-gray-700">
                            <span class="font-medium">Bearish</span>
                        </div>
                    </label>
                </div>
            </div>
        </div>
        <!-- Filter Groups for AND/OR logic -->
        <div class="bg-white rounded-lg p-4 mb-3">
            <div class="flex justify-between items-center mb-4">
                <label class="block font-medium mb-1">Data Filters
                    (AND/OR Groups)</label>
                <div class="relative group">
                    <div
                        class="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 transition-all duration-300 group-hover:bg-blue-200 dark:group-hover:bg-blue-800">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 dark:text-blue-400"
                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div
                        class="absolute z-10 hidden group-hover:block w-64 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-xl right-0 text-sm text-gray-600 dark:text-gray-300 transform transition-all duration-300 origin-top-right">
                        <p class="mb-3 font-semibold text-blue-600 dark:text-blue-400">
                            Available fields:
                        </p>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{rsi}</code>
                                <span class="ml-2">RSI</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{ema}</code>
                                <span class="ml-2">EMA</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{c}</code>
                                <span class="ml-2">Close</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{bb_upper}</code>
                                <span class="ml-2">BB Upper</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{bb_lower}</code>
                                <span class="ml-2">BB Lower</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{adx}</code>
                                <span class="ml-2">ADX</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{cci}</code>
                                <span class="ml-2">CCI</span>
                            </div>
                        </div>
                        <!-- Additional available fields -->
                        <div class="space-y-2 mt-2">
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{sma}</code>
                                <span class="ml-2">SMA</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{macd}</code>
                                <span class="ml-2">MACD</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{macds}</code>
                                <span class="ml-2">MACD Signal</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{macdh}</code>
                                <span class="ml-2">MACD Hist</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{bb_middle}</code>
                                <span class="ml-2">BB Middle</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{atr}</code>
                                <span class="ml-2">ATR</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{tema}</code>
                                <span class="ml-2">TEMA</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{stoch_k}</code>
                                <span class="ml-2">Stoch K</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{stoch_d}</code>
                                <span class="ml-2">Stoch D</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{mfi}</code>
                                <span class="ml-2">MFI</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{vwap}</code>
                                <span class="ml-2">VWAP</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{ema9}</code>
                                <span class="ml-2">EMA 9</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{ema20}</code>
                                <span class="ml-2">EMA 20</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{ema50}</code>
                                <span class="ml-2">EMA 50</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{ema100}</code>
                                <span class="ml-2">EMA 100</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{ema200}</code>
                                <span class="ml-2">EMA 200</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{sar}</code>
                                <span class="ml-2">SAR</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{support_breakdown}</code>
                                <span class="ml-2">Support Breakdown</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{resistance_breakout}</code>
                                <span class="ml-2">Resistance Breakout</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{rsi_divergence}</code>
                                <span class="ml-2">RSI Divergence</span>
                            </div>
                            <div class="flex items-center">
                                <code
                                    class="inline-block w-24 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded text-blue-700 dark:text-blue-300 font-mono">{rsi_convergence}</code>
                                <span class="ml-2">RSI Convergence</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Inserted Default Filters Section here -->
            <div id="default-filters" class="bg-amber-50 mb-4 p-3 rounded-lg">
                <p class="block font-medium mb-2">Default Filters</p>
                <ul class="flex flex-wrap gap-2" id="default-filters-list">
                    <!-- Default filters will be populated here -->
                </ul>
            </div>
            <!-- Container for all filter groups -->
            <div id="filter-groups-container" class="space-y-5">
                <!-- Example Filter Group -->
                <div class="bg-gray-50 border dark:from-blue-900/20 duration-300 rounded-xl transition-all"
                    data-filter-group>
                    <div class="flex items-center justify-between p-2 bg-white rounded-t-xl border-b">
                        <div class="flex items-center space-x-3">
                            <div>
                                <label class="text-sm font-medium">Group
                                    Logic:</label>
                            </div>
                            <div
                                class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400 focus:outline-0 px-2 py-0.5 rounded-md shadow-sm text-sm">
                                <select name="group-operator" class="outline-0 bg-transparent shadow-none">
                                    <option value="AND">AND</option>
                                    <option value="OR">OR</option>
                                </select>
                            </div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 italic hidden md:block">(How filters
                                combine)</span>
                        </div>
                        <!-- Delete button for the filter group -->
                        <button type="button" onclick="removeFilterGroup(this)"
                            class="border border-red-500 duration-300 flex from-red-500 hover:bg-red-500 hover:from-red-600 hover:shadow hover:text-white hover:to-pink-600 items-center px-2 py-1.5 rounded-lg shadow-sm space-x-1 text-red-500 text-xs to-pink-500 transition-all">
                            <span>Delete Group</span>
                        </button>
                    </div>
                    <div class="space-y-3 filter-rows-container p-2" data-filters-list>
                        <div class="flex flex-wrap md:flex-nowrap gap-3 p-4 bg-white dark:bg-gray-800 rounded-md  dark:border-gray-700  transition-all duration-300 transform hover:bg-blue-50 filter-row-animate"
                            data-filter-row>
                            <div class="w-full md:w-1/3">
                                <input type="text" name="filter-field" placeholder="Field (e.g., {rsi})"
                                    class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400 focus:outline-0 px-2 py-2.5 rounded-md shadow-sm text-sm w-full" />
                            </div>
                            <div class="w-full md:w-1/3">
                                <div
                                    class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400 focus:outline-0 px-2 py-2.5 rounded-md shadow-sm text-sm w-full bg-white">
                                    <select name="filter-condition"
                                        class="outline-0 bg-transparent w-full focus:shadow-none">
                                        <option value="" disabled selected>
                                            Select condition
                                        </option>
                                        <option value="gte">Greater than or equal (≥)</option>
                                        <option value="gt">Greater than (>)</option>
                                        <option value="lte">Less than or equal (≤)</option>
                                        <option value="lt">Less than (<) </option>
                                        <option value="eq">Equal to (=)</option>
                                        <option value="neq">Not equal to (≠)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="w-full md:w-1/4">
                                <input type="text" name="filter-value" placeholder="Value"
                                    class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400 focus:outline-0 px-2 py-2.5 rounded-md shadow-sm text-sm w-full" />
                            </div>
                            <div class="flex items-center">
                                <button type="button" onclick="removeFilterRow(this)"
                                    class="bg-gradient-to-br duration-300 flex from-red-50 h-10 hover:from-red-50 hover:scale-105 hover:shadow hover:to-pink-200 items-center justify-center rounded-lg text-red-400 to-pink-100 transform transition-all w-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-x-icon lucide-x">
                                        <path d="M18 6 6 18" />
                                        <path d="m6 6 12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="p-2 border-t">
                        <button type="button" onclick="addFilterRow(this)"
                            class="flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium transition-all duration-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 px-3 py-2 rounded-lg shadow-sm hover:shadow">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            <span>Add Filter</span>
                        </button>
                    </div>
                </div>
            </div>
            <button type="button" onclick="addFilterGroup()"
                class="bg-blue-500 block flex focus:outline-0 font-medium hover:bg-blue-600 mt-4 px-3 py-2.5 rounded-md text-sm text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Filter Group
            </button>
        </div>

        <div class="bg-white rounded-lg p-4 my-3">
            <label class="block font-medium mb-3">
                Candlestick Patterns
            </label>
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 mt-2">
                <!-- Bullish Patterns Column -->
                <div class="bg-green-50 p-3 rounded-lg" data-pattern-type="bullish-candlestick">
                    <!-- Header container with buttons -->
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="text-sm font-medium">Bullish Patterns</h4>
                        <div class="flex gap-2 items-center">
                            <button type="button" id="select-all-bullish-candlestick"
                                class="text-xs hover:text-blue-500 text-neutral-700 font-medium">
                                Select All
                            </button>
                            <span class="text-gray-400 text-xs">|</span>
                            <button type="button" id="deselect-all-bullish-candlestick"
                                class="text-xs hover:text-red-500 text-neutral-700 font-medium">
                                Deselect All
                            </button>
                        </div>
                    </div>
                    <div class="gap-3 grid lg:grid-cols-2 md:grid-cols-2 grid-cols-1">
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Bullish Marubozu"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Bullish Marubozu</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Hammer"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Hammer</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Bullish Engulfing"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Bullish Engulfing</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Piercing Pattern"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Piercing Pattern</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Morning Star"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Morning Star</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Three White Soldiers"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Three White Soldiers</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Bullish Harami"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Bullish Harami</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Abandoned Baby"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Abandoned Baby</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Three Stars In South"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Three Stars In South</span>
                        </label>
                    </div>
                </div>

                <!-- Neutral Patterns Column -->
                <div class="bg-blue-50 p-3 rounded-lg" data-pattern-type="neutral-candlestick">
                    <!-- Header container with buttons -->
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="text-sm font-medium">Neutral Patterns</h4>
                        <div class="flex gap-2 items-center">
                            <button type="button" id="select-all-neutral-candlestick"
                                class="text-xs hover:text-blue-500 text-neutral-700 font-medium">
                                Select All
                            </button>
                            <span class="text-gray-400 text-xs">|</span>
                            <button type="button" id="deselect-all-neutral-candlestick"
                                class="text-xs hover:text-red-500 text-neutral-700 font-medium">
                                Deselect All
                            </button>
                        </div>
                    </div>
                    <div class="gap-3 grid lg:grid-cols-2 md:grid-cols-2 grid-cols-1">
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Doji"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Doji</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Dragonfly Doji"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Dragonfly Doji</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Long-legged Doji"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Long-legged Doji</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Spinning Top"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Spinning Top</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="High Wave Candle"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">High Wave Candle</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Three Inside Up/Down"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Three Inside Up/Down</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Three Outside"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Three Outside</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Inverted Hammer"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Inverted Hammer</span>
                        </label>
                    </div>
                </div>

                <!-- Bearish Patterns Column -->
                <div class="bg-red-50 p-3 rounded-lg" data-pattern-type="bearish-candlestick">
                    <!-- Header container with buttons -->
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="text-sm font-medium">Bearish Patterns</h4>
                        <div class="flex gap-2 items-center">
                            <button type="button" id="select-all-bearish-candlestick"
                                class="text-xs hover:text-blue-500 text-neutral-700 font-medium">
                                Select All
                            </button>
                            <span class="text-gray-400 text-xs">|</span>
                            <button type="button" id="deselect-all-bearish-candlestick"
                                class="text-xs hover:text-red-500 text-neutral-700 font-medium">
                                Deselect All
                            </button>
                        </div>
                    </div>
                    <div class="gap-3 grid lg:grid-cols-2 md:grid-cols-2 grid-cols-1">
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Bearish Marubozu"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Bearish Marubozu</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Bearish Harami"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Bearish Harami</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Shooting Star"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Shooting Star</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Hanging Man"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Hanging Man</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Bearish Engulfing"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Bearish Engulfing</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Dark Cloud Cover"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Dark Cloud Cover</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Evening Star"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Evening Star</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Three Black Crows"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Three Black Crows</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Gravestone Doji"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Gravestone Doji</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="candlestick-pattern" value="Two Crows"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Two Crows</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chart Patterns -->
        <div class="bg-white rounded-lg p-4 my-3">
            <label class="block font-medium mb-3">
                Chart Patterns
            </label>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2">
                <!-- Bullish Patterns Column -->
                <div class="bg-green-50 p-3 rounded-lg" data-pattern-type="bullish-chart">
                    <!-- Header container with buttons -->
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="text-sm font-medium">Bullish Patterns</h4>
                        <div class="flex gap-2 items-center">
                            <button type="button" id="select-all-bullish-chart"
                                class="text-xs hover:text-blue-500 text-neutral-700 font-medium">
                                Select All
                            </button>
                            <span class="text-gray-400 text-xs">|</span>
                            <button type="button" id="deselect-all-bullish-chart"
                                class="text-xs hover:text-red-500 text-neutral-700 font-medium">
                                Deselect All
                            </button>
                        </div>
                    </div>
                    <div class="gap-3 grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1">
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="chart-pattern" value="Double Bottom"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Double Bottom</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <!-- Value updated to match backend -->
                            <input type="checkbox" name="chart-pattern" value="Inverse Head and Shoulder"
                                class="form-checkbox text-blue-500 rounded" />
                            <!-- Label kept short for UI -->
                            <span class="text-xs">Inverse H&S</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="chart-pattern" value="Ascending Triangle"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Ascending Triangle</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <!-- Added Multiple Bottom -->
                            <input type="checkbox" name="chart-pattern" value="Multiple Bottom"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Multiple Bottom</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <!-- Updated Bullish Wedge to Wedge Up -->
                            <input type="checkbox" name="chart-pattern" value="Wedge Up"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Wedge Up</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <!-- Added Channel Up -->
                            <input type="checkbox" name="chart-pattern" value="Channel Up"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Channel Up</span>
                        </label>
                        <!-- Removed: Cup And Handle, Bullish Rectangle, Bullish Flag, Bullish Pennant, Triple Bottom -->
                    </div>
                </div>
                <!-- Bearish Patterns Column -->
                <div class="bg-red-50 p-3 rounded-lg" data-pattern-type="bearish-chart">
                    <!-- Header container with buttons -->
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="text-sm font-medium">Bearish Patterns</h4>
                        <div class="flex gap-2 items-center">
                            <button type="button" id="select-all-bearish-chart"
                                class="text-xs hover:text-blue-500 text-neutral-700 font-medium">
                                Select All
                            </button>
                            <span class="text-gray-400 text-xs">|</span>
                            <button type="button" id="deselect-all-bearish-chart"
                                class="text-xs hover:text-red-500 text-neutral-700 font-medium">
                                Deselect All
                            </button>
                        </div>
                    </div>
                    <!-- Updated Bearish Patterns Grid -->
                    <div class="gap-3 grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1">
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="chart-pattern" value="Double Top"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Double Top</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <!-- Value updated to match backend -->
                            <input type="checkbox" name="chart-pattern" value="Head and Shoulder"
                                class="form-checkbox text-blue-500 rounded" />
                            <!-- Label kept short for UI -->
                            <span class="text-xs">Head & Shoulders</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <input type="checkbox" name="chart-pattern" value="Descending Triangle"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Descending Triangle</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <!-- Added Multiple Top -->
                            <input type="checkbox" name="chart-pattern" value="Multiple Top"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Multiple Top</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <!-- Updated Bearish Wedge to Wedge Down -->
                            <input type="checkbox" name="chart-pattern" value="Wedge Down"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Wedge Down</span>
                        </label>
                        <label
                            class="flex items-center space-x-2 p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors bg-white">
                            <!-- Added Channel Down -->
                            <input type="checkbox" name="chart-pattern" value="Channel Down"
                                class="form-checkbox text-blue-500 rounded" />
                            <span class="text-xs">Channel Down</span>
                        </label>
                        <!-- Removed: Bearish Rectangle, Bearish Flag, Bearish Pennant, Triple Top, Rising Wedge -->
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg p-4 my-3">
            <!-- Stoploss Coefficient (ATR) -->
            <div class="p-4 rounded-md"
                style="background-image: radial-gradient(circle at 0% 100%, #fff5d7, #f0fdff, #ffffff, #f8f8f8, #ffffff, #ffeaea91, #ffffff);">
                <div>
                    <label class="block font-medium">
                        Stoploss Coefficient (× ATR)
                    </label>
                    <div class="flex items-center space-x-4">
                        <!-- Range limited to 1, 2, or 3 -->
                        <input type="range" id="stoploss-range" min="1" max="3" step="1" value="2"
                            class="w-full max-w-md h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" />
                        <span id="stoploss-value"
                            class="w-12 h-12 flex items-center justify-center bg-blue-500 text-white rounded-full text-lg font-bold">2</span>
                        <input type="hidden" id="stoploss-coefficient" name="stoploss-coefficient" value="2" />
                    </div>
                    <div class="flex justify-between max-w-md mt-1 text-xs text-gray-700 dark:text-gray-400">
                        <span>Conservative</span>
                        <span>Balanced</span>
                        <span>Aggressive</span>
                    </div>
                </div>
            </div>

            <!-- Duration -->
            <div class="mt-5">
                <p class="block font-medium mb-1">Duration</p>
                <p class="text-gray-600 mb-4 text-sm">Select your preferred trading time interval for analysis.</p>
                <div class="flex w-full max-w-[20rem] bg-gray-100 dark:bg-gray-800 p-1 rounded-lg text-sm">
                    <label class="flex-1">
                        <input type="radio" name="duration" value="1m" class="hidden peer" checked />
                        <div
                            class="text-center p-2 rounded-md peer-checked:bg-blue-500 peer-checked:text-white cursor-pointer transition-all duration-200 hover:bg-gray-300 dark:hover:bg-gray-200">
                            <span class="font-medium">1 min</span>
                        </div>
                    </label>
                    <label class="flex-1">
                        <input type="radio" name="duration" value="5m" class="hidden peer" />
                        <div
                            class="text-center p-2 rounded-md peer-checked:bg-blue-500 peer-checked:text-white cursor-pointer transition-all duration-200 hover:bg-gray-300 dark:hover:bg-gray-200">
                            <span class="font-medium">5 mins</span>
                        </div>
                    </label>
                    <label class="flex-1">
                        <input type="radio" name="duration" value="15m" class="hidden peer" />
                        <div
                            class="text-center p-2 rounded-md peer-checked:bg-blue-500 peer-checked:text-white cursor-pointer transition-all duration-200 hover:bg-gray-300 dark:hover:bg-gray-200">
                            <span class="font-medium">15 mins</span>
                        </div>
                    </label>
                </div>
            </div>

            <!-- Date Range -->
            <div class="mt-5">
                <label class="block font-medium">
                    Date Range
                </label>
                <!-- Removed preset buttons -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2 w-full items-end">
                    <div>
                        <label class="block text-sm mb-2 font-medium ">Start Date</label>
                        <input type="date" id="start-date" name="start-date"
                            class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400 focus:outline-0 px-2 py-2.5 rounded-md shadow-sm text-sm w-full"
                            onchange="validateDateRange()" />
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">End Date</label>
                        <input type="date" id="end-date" name="end-date"
                            class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400 focus:outline-0 px-2 py-2.5 rounded-md shadow-sm text-sm w-full"
                            onchange="validateDateRange()" />
                    </div>

                    <!-- Run Backtest Button -->
                    <div class="flex gap-2">
                        <button id="save-strategy-btn" onclick="openSaveScalpingStrategyModal(event)"
                            class="hidden bg-green-500 block flex focus:outline-0 font-medium gap-2 hover:bg-green-600 items-center justify-center px-6 py-2.5 rounded-md text-white transition-colors w-full mb-2 md:mb-0">
                            Save Strategy
                        </button>
                        <button id="run-backtest-btn" onclick="runBacktest()"
                            class="w-full bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800 text-white font-medium py-3 px-6 rounded-lg text-base shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Run Backtest
                        </button>
                    </div>
                </div>
                <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    *Select a date range of up to 30 days only.
                </p>
            </div>
        </div>
    </div>


    <!-- Results Section (Initially Hidden) -->
    <div id="resultsSection" class="bg-white rounded-lg p-4 my-3 hidden">
        <div class="md:flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
                Backtest Results
            </h3>
            <div class="flex space-x-2">
                <button id="resetZoomBtn"
                    class="text-sm bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-md transition-colors">
                    Reset Zoom
                </button>
                <button id="downloadChartBtn"
                    class="text-sm bg-green-100 hover:bg-green-200 dark:bg-green-900 dark:hover:bg-green-800 text-green-700 dark:text-green-300 px-3 py-1 rounded-md transition-colors">
                    Download
                </button>
            </div>
        </div>

        <!-- Chart Container -->
        <div id="results-content" class="mb-4">
            <!-- Chart will be rendered here -->
        </div>

        <!-- Trades Summary -->
        <div id="trades-summary"
            class="mt-6 bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Trades Summary
            </h4>
            <div id="trades-table-container">
                <!-- Trades table will be inserted here -->
            </div>
        </div>
    </div>
</div>


<script>
    // Function to initialize default dates on page load
    function initializeDefaultDates() {
        const startDateInput = document.getElementById("start-date");
        const endDateInput = document.getElementById("end-date");

        // Format dates as YYYY-MM-DD
        const formatDate = (date) => {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, "0");
            const day = String(date.getDate()).padStart(2, "0");
            return `${year}-${month}-${day}`;
        };

        // Get current date for END date
        const today = new Date();
        const endDate = formatDate(today);
        endDateInput.value = endDate;
        console.log("Initialized default end date (today):", endDate);

        // Set start date to end date minus 29 days (for a 30-day range)
        const startDateObj = new Date(today);
        startDateObj.setDate(today.getDate() - 29);

        const startDate = formatDate(startDateObj);
        startDateInput.value = startDate;
        console.log(
            "Initialized default start date (today - 29 days):",
            startDate
        );
    }


    // Validate date range (max 10 trading days, excluding weekends)
    function validateDateRange() {
        const startDateInput = document.getElementById("start-date");
        const endDateInput = document.getElementById("end-date");

        // Store original values before validation
        const originalStartDate = startDateInput.value;
        const originalEndDate = endDateInput.value;

        // Only proceed with validation if both dates are filled
        if (!originalStartDate || !originalEndDate) return false;

        const start = new Date(originalStartDate);
        const end = new Date(originalEndDate);

        // Check if dates are valid
        if (isNaN(start.getTime()) || isNaN(end.getTime())) {
            showAlert("Please enter valid dates.");
            return false;
        }

        // Check if end date is before start date
        if (end < start) {
            showAlert("End date cannot be before start date.");
            return false;
        }

        // Count all days (including weekends)
        let totalDays = 0;
        let currentDate = new Date(start);

        while (currentDate <= end) {
            totalDays++;
            currentDate.setDate(currentDate.getDate() + 1);
        }

        // Check if we have too many days
        if (totalDays > 30) {
            showAlert("Please select a date range with at most 30 days.");
            return false;
        }

        // All validation passed
        return true;
    }

    // Update the default filters list based on the selected strategy
    function updateDefaultFiltersList() {
        const strategyRadios = document.getElementsByName("strategy-type");
        let selectedStrategy = "Bullish"; // default
        strategyRadios.forEach(radio => {
            if (radio.checked) selectedStrategy = radio.value;
        });
        const filtersListEl = document.getElementById("default-filters-list");
        filtersListEl.innerHTML = ""; // clear list
        defaultDataFilters
            .filter(f => f.strategy.toLowerCase() === selectedStrategy.toLowerCase())
            .forEach(filter => {
                const li = document.createElement("li");
                li.className = "px-2 py-1.5 text-neutral-900 text-xs border bg-white dark:border-neutral-600 rounded cursor-pointer hover:bg-blue-100 dark:hover:bg-neutral-700 transition-colors";
                li.textContent = filter.name;
                li.onclick = () => populateDefaultFilter(filter);
                filtersListEl.appendChild(li);
            });
    }

    // Populate default filter into the filter groups UI.
    // Uses an empty filter row if available; otherwise, creates a new group.
    function populateDefaultFilter(filterData) {
        // Look for an existing filter group whose first filter is empty
        let targetRow;
        const groups = document.querySelectorAll("[data-filter-group]");
        groups.forEach(group => {
            if (!targetRow) {
                const firstRow = group.querySelector("[data-filter-row] input[name='filter-field']");
                if (firstRow && firstRow.value.trim() === "") {
                    targetRow = group.querySelector("[data-filter-row]");
                }
            }
        });
        // If no such empty group exists, create a new filter group
        if (!targetRow) {
            addFilterGroup();
            const newGroup = document.querySelector("[data-filter-group]:last-of-type");
            targetRow = newGroup.querySelector("[data-filter-row]");
        }
        // Get the container of rows of this group for adding extra rows
        const groupContainer = targetRow.closest("[data-filters-list]");

        // Populate the first filter in targetRow
        const firstFilter = filterData.groups[0].filters[0];
        targetRow.querySelector("input[name='filter-field']").value = firstFilter.field;
        const condSelect = targetRow.querySelector("select[name='filter-condition']");
        if (condSelect) { condSelect.value = firstFilter.condition; }
        const valInput = targetRow.querySelector("input[name='filter-value']");
        if (valInput) { valInput.value = firstFilter.value; }

        // For additional filters, always add new rows in the same group container
        for (let i = 1; i < filterData.groups[0].filters.length; i++) {
            addFilterRow(groupContainer);
            // After adding, get the newly added row from the container
            const rows = groupContainer.querySelectorAll("[data-filter-row]");
            const newRow = rows[rows.length - 1];
            const filter = filterData.groups[0].filters[i];
            newRow.querySelector("input[name='filter-field']").value = filter.field;
            const newCond = newRow.querySelector("select[name='filter-condition']");
            if (newCond) { newCond.value = filter.condition; }
            const newVal = newRow.querySelector("input[name='filter-value']");
            if (newVal) { newVal.value = filter.value; }
        }
    }

    // Update default filters when strategy type changes.
    document.querySelectorAll('input[name="strategy-type"]').forEach(radio => {
        radio.addEventListener("change", updateDefaultFiltersList);
    });

    document.addEventListener("DOMContentLoaded", () => {
        updateDefaultFiltersList();
        // ...existing code...
    });

    // ...existing code...
</script>

{% include 'algos/scalping/scroll-button.html' %}