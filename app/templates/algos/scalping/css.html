<style>
    /* Animations for filter components */
    @keyframes pulse-border {
        0% {
            border-color: rgba(59, 130, 246, 0.3);
        }

        50% {
            border-color: rgba(59, 130, 246, 0.6);
        }

        100% {
            border-color: rgba(59, 130, 246, 0.3);
        }
    }

    .filter-row-animate:focus-within {
        animation: pulse-border 2s infinite ease-in-out;
        border-color: rgba(59, 130, 246, 0.6);
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }

    /* Subtle background animation for filter groups */
    @keyframes gradient-shift {
        0% {
            background-position: 0% 50%;
        }

        50% {
            background-position: 100% 50%;
        }

        100% {
            background-position: 0% 50%;
        }
    }

    [data-filter-group] {
        background-size: 200% 200%;
        animation: gradient-shift 15s ease infinite;
    }

    /* Improve focus styles */
    input:focus,
    select:focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    }

    /* Chart styles */
    .main-chart {
        background-color: #fff;
        border-radius: 0.5rem;
    }

    .dark .main-chart {
        background-color: #1f2937;
    }

    .x-axis path,
    .y-axis path {
        stroke: #9ca3af;
    }

    .x-axis text,
    .y-axis text {
        fill: #6b7280;
        font-size: 10px;
    }

    .dark .x-axis text,
    .dark .y-axis text {
        fill: #9ca3af;
    }

    .trade-marker {
        cursor: pointer;
        transition: r 0.2s;
    }

    .trade-marker:hover {
        r: 8;
    }

    /* Helper class for transitions */
    .transition-all {
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 300ms;
    }

    /* Chart tooltip */
    .chart-tooltip {
        transition: opacity 0.2s;
        max-width: 250px;
        color: #374151;
    }

    .dark .chart-tooltip {
        background-color: rgba(31, 41, 55, 0.9) !important;
        color: #e5e7eb;
        border-color: #4b5563 !important;
    }

    /* Hover effects for chart elements */
    .candle .body {
        transition: stroke-width 0.2s;
    }

    .candle:hover .wick {
        stroke-width: 1.5;
    }

    /* Enhanced tooltip styling */
    .chartjs-tooltip {
        background-color: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
        border-radius: 10px !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
        border: 1px solid rgba(226, 232, 240, 0.8) !important;
        padding: 12px !important;
        color: #1e293b !important;
        transition: opacity 0.2s ease, transform 0.2s ease !important;
        transform: scale(1) !important;
        font-weight: 500 !important;
    }

    .dark .chartjs-tooltip {
        background-color: rgba(15, 23, 42, 0.95) !important;
        border: 1px solid rgba(51, 65, 85, 0.8) !important;
        color: #e2e8f0 !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25) !important;
    }

    .chartjs-tooltip:hover {
        transform: scale(1.02) !important;
    }

    /* Add subtle glow effect to chart elements */
    .hovering-candle {
        filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.5));
    }

    .dark .hovering-candle {
        filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.7));
    }

    /* Make chart zooming more intuitive */
    #backtestChart.zooming {
        cursor: zoom-in !important;
    }

    #backtestChart.panning {
        cursor: grab !important;
    }

    #backtestChart.panning:active {
        cursor: grabbing !important;
    }

    /* Fix for legend items */
    #price-legend,
    #indicators-legend,
    #patterns-legend {
        max-height: 120px;
        overflow-y: auto;
    }

    /* Modern chart styling with wow factor */
    #backtestChart {
        border-radius: 12px;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        background: linear-gradient(to bottom,
                rgba(255, 255, 255, 0.8),
                rgba(255, 255, 255, 1));
    }

    .dark #backtestChart {
        background: linear-gradient(to bottom,
                rgba(30, 41, 59, 0.8),
                rgba(15, 23, 42, 0.95));
    }

    /* Enhanced interactive elements */
    #resetZoomBtn,
    #downloadChartBtn {
        transition: all 0.3s ease;
        transform: translateY(0);
        position: relative;
        overflow: hidden;
    }

    #resetZoomBtn:hover,
    #downloadChartBtn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    #resetZoomBtn:active,
    #downloadChartBtn:active {
        transform: translateY(1px);
    }

    #resetZoomBtn:after,
    #downloadChartBtn:after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, 0.5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
    }

    #resetZoomBtn:focus:not(:active)::after,
    #downloadChartBtn:focus:not(:active)::after {
        animation: ripple 1s ease-out;
    }

    /* Modern animations */
    @keyframes ripple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }

        20% {
            transform: scale(25, 25);
            opacity: 0.3;
        }

        100% {
            opacity: 0;
            transform: scale(40, 40);
        }
    }

    @keyframes chart-pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
        }

        70% {
            box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
        }

        100% {
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
        }
    }

    /* Results section animation */
    #resultsSection:not(.hidden) {
        animation: slide-in-bottom 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }

    @keyframes slide-in-bottom {
        0% {
            transform: translateY(20px);
            opacity: 0;
        }

        100% {
            transform: translateY(0);
            opacity: 1;
        }
    }
</style>