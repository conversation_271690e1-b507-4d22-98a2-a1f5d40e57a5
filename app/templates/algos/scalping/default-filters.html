<script>
    // Extended filter definitions based on the IG article "Four Simple Scalping Trading Strategies"
    const defaultDataFilters = [
        {
            "name": "Deathcross",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{ema50}", "condition": "lt", "value": "{ema200}" },
                        { "field": "{ema50_prev_1}", "condition": "gt", "value": "{ema200_prev_1}" }
                    ]
                }
            ]
        },
        // 1) EMA(9–20) Crossover — Bearish
        {
            "name": "EMA (9-20) Crossover - Bearish",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{ema9_prev_1}", "condition": "gt", "value": "{ema20_prev_1}" },
                        { "field": "{ema9}", "condition": "lt", "value": "{ema20}" }
                    ]
                }
            ]
        },

        // 2) EMA(9–20) + MACD & ADX Confirmation — Bearish
        {
            "name": "EMA (9-20) Crossover with MACD & ADX Confirmation - Bearish",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{ema9_prev_1}", "condition": "gt", "value": "{ema20_prev_1}" },
                        { "field": "{ema9}", "condition": "lt", "value": "{ema20}" },
                        { "field": "{macd}", "condition": "lt", "value": "0" },
                        { "field": "{adx}", "condition": "gt", "value": "20" }
                    ]
                }
            ]
        },

        // 3) RSI 70 Crossover — Bearish (overbought cross down)
        {
            "name": "RSI 70 Crossover",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{rsi_prev_1}", "condition": "gt", "value": "70" },
                        { "field": "{rsi}", "condition": "lte", "value": "70" }
                    ]
                }
            ]
        },

        // 4) Stochastic Oscillator Crossover — Bearish
        {
            "name": "Stochastic Oscillator Crossover - Bearish",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{stoch_k_prev_1}", "condition": "gt", "value": "80" },
                        { "field": "{stoch_k}", "condition": "lte", "value": "80" },
                        { "field": "{stoch_k}", "condition": "lt", "value": "{stoch_d}" }
                    ]
                }
            ]
        },

        // 5) Bollinger Bounce — Bearish (price taps upper band then reverses)
        {
            "name": "Bollinger Bounce - Bearish",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{c_prev_1}", "condition": "gt", "value": "{bb_upper_prev_1}" },
                        { "field": "{c}", "condition": "lte", "value": "{bb_upper}" }
                    ]
                }
            ]
        },

        // 6) Bollinger Breakout — Bearish (price breaks below lower band)
        {
            "name": "Bollinger Breakout - Bearish",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{c_prev_1}", "condition": "gt", "value": "{bb_lower_prev_1}" },
                        { "field": "{c}", "condition": "lt", "value": "{bb_lower}" }
                    ]
                }
            ]
        },

        // 7) RSI Divergence — Bearish (negative divergence)
        {
            "name": "RSI Divergence - Bearish",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{rsi_divergence}", "condition": "eq", "value": "-1" },
                        { "field": "{c}", "condition": "lt", "value": "{c_prev_1}" }
                    ]
                }
            ]
        },

        // 8) Donchian Channel Breakdown — Bearish
        {
            "name": "Donchian Channel Breakdown",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{low}", "condition": "lt", "value": "{donchian_lower}" },
                        { "field": "{low_prev_1}", "condition": "gte", "value": "{donchian_lower_prev_1}" }
                    ]
                }
            ]
        },
        {
            "name": "Deathcross + RSI (RSI between 30 and 50)",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{ema50}", "condition": "lt", "value": "{ema200}" },
                        { "field": "{ema50_prev_1}", "condition": "gt", "value": "{ema200_prev_1}" },
                        { "field": "{rsi}", "condition": "gte", "value": "30" },
                        { "field": "{rsi}", "condition": "lte", "value": "50" }
                    ]
                }
            ]
        },
        {
            "name": "GoldenCross - EMA (50-200) Crossover",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{ema50}", "condition": "gt", "value": "{ema200}" },
                        { "field": "{ema50_prev_1}", "condition": "lt", "value": "{ema200_prev_1}" }
                    ]
                }
            ]
        },
        {
            "name": "EMA (9-20) Crossover",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{ema9_prev_1}", "condition": "lt", "value": "{ema20_prev_1}" },
                        { "field": "{ema9}", "condition": "gt", "value": "{ema20}" }
                    ]
                }
            ]
        },
        {
            "name": "EMA (9-20) Crossover with MACD (and ADX) Confirmation",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{ema9_prev_1}", "condition": "lt", "value": "{ema20_prev_1}" },
                        { "field": "{ema9}", "condition": "gt", "value": "{ema20}" },
                        { "field": "{macd}", "condition": "gt", "value": "0" },
                        { "field": "{adx}", "condition": "gt", "value": "20" }
                    ]
                }
            ]
        },
        {
            "name": "Enhanced EMA (9-20) Crossover with Double MACD & ADX Confirmation",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{ema9_prev_1}", "condition": "lt", "value": "{ema20_prev_1}" },
                        { "field": "{ema9}", "condition": "gt", "value": "{ema20}" },
                        { "field": "{macd}", "condition": "gt", "value": "0" },
                        { "field": "{macdh}", "condition": "gt", "value": "0" },
                        { "field": "{adx}", "condition": "gt", "value": "20" }
                    ]
                }
            ]
        },
        {
            "name": "Enhanced EMA (9-20) Crossover with MACD, ADX & RSI Confirmation",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{ema9_prev_1}", "condition": "lt", "value": "{ema20_prev_1}" },
                        { "field": "{ema9}", "condition": "gt", "value": "{ema20}" },
                        { "field": "{macd}", "condition": "gt", "value": "0" },
                        { "field": "{macdh}", "condition": "gt", "value": "0" },
                        { "field": "{adx}", "condition": "gt", "value": "20" },
                        { "field": "{rsi}", "condition": "lt", "value": "60" }
                    ]
                }
            ]
        }
        ,
        {
            "name": "RSI 30 Crossover",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{rsi_prev_1}", "condition": "lt", "value": "30" },
                        { "field": "{rsi}", "condition": "gte", "value": "30" }
                    ]
                }
            ]
        },
        {
            "name": "VWAP Reversion - Bullish",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{c_prev_1}", "condition": "lt", "value": "{vwap_prev_1}" },
                        { "field": "{c}", "condition": "gte", "value": "{vwap}" }
                    ]
                }
            ]
        },
        {
            "name": "VWAP Reversion - Bearish",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{c_prev_1}", "condition": "gt", "value": "{vwap_prev_1}" },
                        { "field": "{c}", "condition": "lte", "value": "{vwap}" }
                    ]
                }
            ]
        }
        ,
        {
            "name": "Stochastic Oscillator Crossover",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{stoch_k_prev_1}", "condition": "lt", "value": "20" },
                        { "field": "{stoch_k}", "condition": "gte", "value": "20" },
                        { "field": "{stoch_k}", "condition": "gt", "value": "{stoch_d}" }
                    ]
                }
            ]
        },
        {
            "name": "Scalping - Parabolic SAR Strategy (Bearish)",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{sar}", "condition": "gt", "value": "{c}" }
                    ]
                }
            ]
        },
        // New filters using MACD
        {
            "name": "MACD Crossover - Bullish",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{macd_prev_1}", "condition": "lt", "value": "{macds_prev_1}" },
                        { "field": "{macd}", "condition": "gte", "value": "{macds}" }
                    ]
                }
            ]
        },
        {
            "name": "MACD Crossover - Bearish",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{macd_prev_1}", "condition": "gt", "value": "{macds_prev_1}" },
                        { "field": "{macd}", "condition": "lte", "value": "{macds}" }
                    ]
                }
            ]
        },
        // New filters using Bollinger Bands
        {
            "name": "Bollinger Bounce - Bullish",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{c_prev_1}", "condition": "lt", "value": "{bb_lower_prev_1}" },
                        { "field": "{c}", "condition": "gte", "value": "{bb_lower}" }
                    ]
                }
            ]
        },
        {
            "name": "Bollinger Breakout - Bullish",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{c_prev_1}", "condition": "lt", "value": "{bb_upper_prev_1}" },
                        { "field": "{c}", "condition": "gte", "value": "{bb_upper}" }
                    ]
                }
            ]
        },
        {
            "name": "RSI Divergence - Bullish",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{rsi_divergence}", "condition": "eq", "value": "1" },
                        { "field": "{c}", "condition": "gt", "value": "{c_prev_1}" }
                    ]
                }
            ]
        }
        ,
        {
            "name": "Resistance Breakout",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{resistance_breakout}", "condition": "eq", "value": "1" }
                    ]
                }
            ]
        }
        ,
        {
            "name": "Support Breakdown",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{support_breakdown}", "condition": "eq", "value": "1" }
                    ]
                }
            ]
        }
        ,
        // New filters using ATR
        {
            "name": "ATR High - Volatility Spike",
            "strategy": "Bullish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{atr}", "condition": "gt", "value": "3.0" }
                    ]
                }
            ]
        },
        {
            "name": "ATR Low - Low Volatility",
            "strategy": "Bearish",
            "groups": [
                {
                    "groupOperator": "AND",
                    "filters": [
                        { "field": "{atr}", "condition": "lt", "value": "1.5" }
                    ]
                }
            ]
        }
    ];
</script>