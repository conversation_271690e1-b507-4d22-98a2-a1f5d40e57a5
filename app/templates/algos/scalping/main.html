<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Algos Scalping - Analyze Historical Data for Day Trading | AI Bull</title>
    <meta name="description"
        content="Backtest your scalping strategies using historical data. Optimize risk management, analyze price fluctuations, and profit from small price moves with our advanced dashboard for scalping traders and day traders." />
    <link rel="canonical" href="https://theaibull.com/algos/scalping" />
    <meta property="og:site_name" content="AI Bull" />
    <meta property="og:logo" content="{{ cdn('/static/images/logo.png') }}" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://theaibull.com/algos/scalping" />
    <meta property="og:title" content="Backtesting Dashboard - Scalping and Day Trading Analysis" />
    <meta property="og:description"
        content="Leverage technical analysis, moving averages, MACD, and RSI to backtest short-term trades. Analyze historical performance with stop losses and high liquidity for quick profits." />
    <meta property="og:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image:height" content="350" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="" />
    <meta name="twitter:title" content="Backtesting Dashboard - Scalping and Day Trading Analysis" />
    <meta name="twitter:description"
        content="Use our dashboard to backtest scalping strategies, focusing on small moves, price fluctuations, and risk management. Utilize technical indicators like moving averages, MACD, and RSI for day trading success." />
    <meta name="twitter:image" content="{{ cdn('/static/images/ai-bull-1.png') }}" />
    <!-- Additional head includes -->
    {% include 'blocks/head.html' %}
</head>

<body class="bg-neutral-100 dark:bg-neutral-900 dark:text-neutral-100">
    {% include 'blocks/common.html' %}
    {% include 'blocks/stock-symbols.html' %}
    {% include 'blocks/onboarding.html' %}

    <!-- Global Spinner -->
    <div id="loadingSpinner" class="hidden fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
        <img src="https://i.imgur.com/llF5iyg.gif" alt="Loading..." />
    </div>

    <div class="rhs-block duration-300 transition-width flex flex-col justify-between min-h-screen bg-neutral-100 dark:bg-neutral-900 pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <div class="max-w-7xl mx-auto p-3 w-full">
            <!-- Header Section -->
            <div class="flex flex-col bg-white dark:bg-gray-800 px-3 py-2 rounded-lg mb-3">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <h1 class="text-lg md:text-xl font-semibold tracking-tight">
                            Algos Scalping for Day Traders
                        </h1>
                        <h2 class="ml-2 hidden lg:block text-gray-600 dark:text-gray-400">
                            Analyze short-term trades with technical analysis and risk management
                        </h2>
                    </div>
                </div>
            </div>


            <!-- Active Stocks Search Block -->
            <div class="w-full rounded-lg md:p-6 p-4 mb-4 border border-white"
                style="background-image: linear-gradient(45deg, #f0faff, #ffffff);">
                {% with load_scalping_strategies="true", enable_backtesting=false, show_submit_button=false %}
                {% include 'blocks/search-active-recent-stocks.html' %}
                {% endwith %}
            </div>


            {% include 'algos/scalping/ui.html' %}
            {% include 'algos/backtesting/faqs.html' %}
        </div>

        <div class="mt-auto">
            {% include 'blocks/footer.html' %}
        </div>
    </div>
    <!-- Script Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <script src="/static/js/chartjs-chart-financial.min.js"></script>
    {% include 'algos/scalping/filter.html' %}

    <script>
        /***********************************************************************
         * Filter Group and Date Range Functions
         ***********************************************************************/

        // Local loading indicator for results
        function showLocalLoading(message, circle = true) {
            const resultsContent = document.getElementById("results-content");
            if (!resultsContent) {
                console.warn("Results content element not found");
                return;
            }

            resultsContent.innerHTML = `
          <div class="flex flex-col items-center justify-center p-4 text-center text-gray-600 min-h-[20px]">
            <div class="flex flex-col items-center">
              ${circle ? `<div class="w-16 h-16 border-4 border-blue-500 border-dashed rounded-full animate-spin"></div>` : ""}
              <span class="mt-4 text-sm font-medium text-gray-700">${message}</span>
            </div>
          </div>
        `;
        }

        // Run Backtest function
        function runBacktest() {
            // Check loggin status
            if (!isLoggedIn()) {
                showModal("Please Login/Signup to access this feature");
                return;
            }

            const symbol = document.getElementById("stock-input")?.value || "";
            // Added validation: Ensure symbol is provided
            if (!symbol.trim()) {
                showAlert("Please enter a valid stock symbol.");
                return;
            }
            // Added validation: Ensure at least one data filter is set
            const filterRows = document.querySelectorAll("[data-filter-row]");
            if (filterRows.length === 0) {
                showAlert("Please set at least one data filter.");
                return;
            }

            // Validate the date range
            const isDateRangeValid = validateDateRange();
            if (!isDateRangeValid) {
                showAlert("Please select a date range with at most 30 trading days.");
                return;
            }

            console.log("Running backtest for symbol:", symbol);
            // Additional check: ensure at least one filter row has a non-empty filter field
            const hasValidFilterField = Array.from(filterRows).some(row => {
                const filterField = row.querySelector("input[name='filter-field']");
                return filterField && filterField.value.trim();
            });
            if (!hasValidFilterField) {
                showAlert("Please set at least one valid filter field.");
                return;
            }

            // Show results section
            const resultsSection = document.getElementById("resultsSection");
            if (resultsSection) {
                resultsSection.classList.remove("hidden");
            }

            // Show loading indicator in results content
            const resultsContent = document.getElementById("results-content");
            if (resultsContent) {
                showLocalLoading(
                    "Analyzing historical data and computing backtest results..."
                );
            }

            // Hide the trades summary section
            const tradesSummary = document.getElementById("trades-summary");
            if (tradesSummary) {
                tradesSummary.classList.add("hidden");
            }


            let startDate = document.getElementById("start-date").value;
            let endDate = document.getElementById("end-date").value;
            const stoplossCoefficient = document.getElementById(
                "stoploss-coefficient"
            ).value;

            console.log("Initial dates:", { startDate, endDate });

            // Set default dates if not selected
            if (!startDate || !endDate) {
                // Format dates as YYYY-MM-DD
                const formatDate = (date) => {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, "0");
                    const day = String(date.getDate()).padStart(2, "0");
                    return `${year}-${month}-${day}`;
                };

                // Get current date for END date (reversed from original)
                const today = new Date();

                // If end date isn't set, use current date
                if (!endDate) {
                    endDate = formatDate(today);
                    document.getElementById("end-date").value = endDate;
                    console.log("Set default end date (today):", endDate);
                }

                // If start date isn't set, use end date minus 1 day
                if (!startDate) {
                    const endDateObj = new Date(endDate);
                    const startDateObj = new Date(endDateObj);
                    startDateObj.setDate(endDateObj.getDate() - 1);

                    // Skip weekends for start date
                    if (startDateObj.getDay() === 0) {
                        // Sunday
                        startDateObj.setDate(startDateObj.getDate() - 2); // Go to Friday
                        console.log("Start date adjusted from Sunday to Friday");
                    } else if (startDateObj.getDay() === 6) {
                        // Saturday
                        startDateObj.setDate(startDateObj.getDate() - 1); // Go to Friday
                        console.log("Start date adjusted from Saturday to Friday");
                    }

                    startDate = formatDate(startDateObj);
                    document.getElementById("start-date").value = startDate;
                    console.log("Set default start date (end date - 1):", startDate);
                }

                console.log(
                    `Using default dates: Start=${startDate}, End=${endDate}`
                );
            }

            const duration =
                document.querySelector('input[name="duration"]:checked')?.value ||
                "1m";

            // NEW: Get the strategy type selection
            const strategyType =
                document.querySelector('input[name="strategy-type"]:checked')
                    ?.value || "Bullish";

            // Collect filter groups with detailed logging
            const filterGroups = [];
            document
                .querySelectorAll("[data-filter-group]")
                .forEach((group, groupIndex) => {
                    const groupOperator = group.querySelector(
                        "select[name='group-operator']"
                    ).value;
                    const filters = [];

                    group
                        .querySelectorAll("[data-filter-row]")
                        .forEach((row, rowIndex) => {
                            const field = row.querySelector(
                                "input[name='filter-field']"
                            ).value;
                            const condition = row.querySelector(
                                "select[name='filter-condition']"
                            ).value;
                            const value = row.querySelector(
                                "input[name='filter-value']"
                            ).value;

                            if (field && condition && value) {
                                filters.push({ field, condition, value });
                                console.log(
                                    `Filter ${groupIndex + 1}.${rowIndex + 1
                                    }: ${field} ${condition} ${value}`
                                );
                            }
                        });

                    if (filters.length > 0) {
                        filterGroups.push({ groupOperator, filters });
                        console.log(
                            `Group ${groupIndex + 1} Logic: ${groupOperator} with ${filters.length
                            } filters`
                        );
                    }
                });

            // Collect candlestick patterns with logging and format them properly
            const candlestickPatterns = [];
            document
                .querySelectorAll('input[name="candlestick-pattern"]:checked')
                .forEach((cb) => {
                    // Convert to lowercase and replace spaces with hyphens
                    const formattedPattern = cb.value
                        .toLowerCase()
                        .replace(/\s+/g, "-");
                    candlestickPatterns.push(formattedPattern);
                });
            console.log(
                `Selected ${candlestickPatterns.length} candlestick patterns:`,
                candlestickPatterns
            );

            // Collect chart patterns with logging
            const chartPatterns = [];
            document
                .querySelectorAll('input[name="chart-pattern"]:checked')
                .forEach((cb) => {
                    // Also format chart patterns consistently
                    const formattedPattern = cb.value
                        .toLowerCase()
                        .replace(/\s+/g, "-");
                    chartPatterns.push(formattedPattern);
                });
            console.log(
                `Selected ${chartPatterns.length} chart patterns:`,
                chartPatterns
            );

            const payload = {
                symbol,
                duration,
                start: startDate,
                end: endDate,
                stoplossCoefficient,
                filterGroups,
                candlestickPatterns,
                chartPatterns,
                strategyType,
            };

            // Updated endpoint from /ohlc to /ohlc3
            axios
                .post("/ohlc3/ohlc-od/", payload)
                .then((response) => {
                    console.log("Backtest result:", response.data);
                    // Render visualization with the data
                    renderBacktestVisualization(response.data, symbol);
                    // Display the save strategy button
                    document.getElementById("save-strategy-btn").classList.remove("hidden");
                })
                .catch((error) => {
                    // Hide the save strategy button
                    document.getElementById("save-strategy-btn").classList.add("hidden");
                    console.error("Error during backtest:", error);
                    if (resultsContent) {
                        resultsContent.innerHTML = `
                <div class="p-4 bg-red-50 text-red-700 rounded-lg border border-red-200">
                  <h3 class="font-semibold mb-2">Error Running Backtest</h3>
                  <p>${error.response?.data?.message ||
                            error.message ||
                            "Unknown error occurred"
                            }</p>
                </div>
              `;
                    }
                });
        }

        /**
         * Transform API data for Chart.js
         */
        function transformDataForChart(ohlcData) {
            console.log("Transforming OHLC data:", Object.keys(ohlcData).length, "data points");
            const chartData = [];
            Object.entries(ohlcData).forEach(([timestamp, data]) => {
                // Validate the data point
                if (!data || typeof data !== "object") {
                    console.warn(`Invalid data at timestamp ${timestamp}:`, data);
                    return;
                }
                const timestampNum = parseInt(timestamp, 10);
                if (isNaN(timestampNum)) {
                    console.warn(`Invalid timestamp: ${timestamp}`);
                    return;
                }
                const date = new Date(timestampNum * 1000);
                // Copy all keys from the original object and add a date field
                const dataPoint = { date, ...data };
                // Convert known numeric fields (if present) to numbers
                const numericKeys = [
                    "o", "h", "l", "c", "ema", "sma", "bb_upper", "bb_lower", "rsi",
                    "macd", "macd_signal", "adx", "cci", "macds", "macdh", "atr",
                    "stoch_k", "stoch_d", "ema9", "ema20", "ema50", "rsi_divergence", "rsi_convergence"
                ];
                numericKeys.forEach(key => {
                    if (data[key] !== undefined && dataPoint[key] !== null) {
                        const parsed = parseFloat(data[key]);
                        dataPoint[key] = isNaN(parsed) ? data[key] : parsed;
                    }
                });
                chartData.push(dataPoint);
            });
            const sortedData = chartData.sort((a, b) => a.date - b.date);
            console.log(`Transformed ${sortedData.length} data points`);
            return sortedData;
        }

        function getUniqueDates(chartData) {
            const dates = new Set();
            chartData.forEach(data => {
                const date = data.date.toISOString().split('T')[0];
                dates.add(date);
            });
            return Array.from(dates).sort();
        }

        /**
         * Renders the backtest visualization using Chart.js
         * @param {Object} data - The data from the API
         * @param {string} symbol - The stock symbol
         */
        function renderBacktestVisualization(data, symbol) {
            console.log("Rendering backtest visualization with data:", data);
            const resultsContent = document.getElementById("results-content");
            if (!resultsContent) {
                console.error("Results content element not found");
                return;
            }

            resultsContent.innerHTML = ""; // Clear previous chart

            try {
                // Check if ohlc_data exists in the response
                if (!data.ohlc_data || Object.keys(data.ohlc_data).length === 0) {
                    resultsContent.innerHTML = `
                <div class="p-4 bg-yellow-50 text-yellow-700 rounded-lg border border-yellow-200">
                    <h3 class="font-semibold mb-2">No Data Available</h3>
                    <p>The API returned empty or invalid OHLC data. Please check your date range and try again.</p>
                </div>
            `;
                    console.warn("No OHLC data in API response", data);
                    return;
                }

                // Prepare data for Chart.js
                const chartData = transformDataForChart(data.ohlc_data);
                if (!chartData || chartData.length === 0) {
                    resultsContent.innerHTML = `
                <div class="p-4 bg-yellow-50 text-yellow-700 rounded-lg border border-yellow-200">
                    <h3 class="font-semibold mb-2">No Data Available</h3>
                    <p>Unable to render the chart due to missing or invalid data.</p>
                </div>
            `;
                    console.warn("Transformed chart data is empty or invalid");
                    return;
                }

                // Map trades to timestamps for easier lookup
                const tradeMap = new Map();
                if (data.payoff && data.payoff.trades) {
                    data.payoff.trades.forEach(trade => {
                        const timestamp = parseInt(trade.Time) * 1000;
                        tradeMap.set(timestamp, trade);
                    });
                }

                const uniqueDates = getUniqueDates(chartData);
                const tabsContainer = document.createElement("div");
                tabsContainer.className = "date-tabs-container mb-4 border-b border-gray-200";
                tabsContainer.innerHTML = `
                <div class="date-tabs flex flex-wrap gap-1">
                    ${uniqueDates.map(date => `
                        <div class="date-tab font-medium text-sm px-4 py-2 rounded-t-lg bg-gray-100 cursor-pointer hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors" data-date="${date}">${date}</div>
                    `).join('')}
                </div>
            `;
                resultsContent.appendChild(tabsContainer);

                // Create canvas for Chart.js
                const scrollContainer = document.createElement("div");
                scrollContainer.className = "overflow-x-auto w-full border border-gray-200 rounded";
                scrollContainer.style.position = "relative"; // For absolute positioning of controls

                const canvas = document.createElement("canvas");
                canvas.id = "backtestChart";
                canvas.style.height = "400px";
                scrollContainer.appendChild(canvas);
                resultsContent.appendChild(scrollContainer);

                const ctx = canvas.getContext("2d");
                if (!ctx) {
                    console.error("Failed to get canvas context");
                    return;
                }

                // Track visibility state for each legend category
                const visibilityState = {
                    "Price": true,
                    "Stop Loss": false,
                    "Trades: Long": true,
                    "Trades: Short": true,
                    "Patterns: Bullish": false,
                    "Patterns: Bearish": false,
                    "Patterns: Neutral": false,
                    "Signals: Long": false,
                    "Signals: Short": false,
                    "Score: Positive": false,
                    "Score: Negative": false,
                    "Score: Neutral": false
                };

                // Function to filter data by date
                function filterDataByDate(date) {
                    return chartData.filter(d => d.date.toISOString().split('T')[0] === date);
                }

                // Function to update chart with filtered data
                function updateChart(selectedDate) {
                    const filteredData = filterDataByDate(selectedDate);
                    const labels = filteredData.map(d => d.date.toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    }));
                    const prices = filteredData.map(d => d.c);
                    const stopLossLevels = filteredData.map(d => d.c - (d.atr * parseFloat(document.getElementById("stoploss-coefficient").value)));
                    const pointBackgroundColors = [];
                    const pointStyles = [];
                    const pointRadius = [];

                    filteredData.forEach(point => {
                        let color = 'blue';
                        let style = 'circle';
                        let radius = 3;
                        let visible = false;

                        const tradeTimestamp = point.date.getTime();
                        const trade = tradeMap.get(tradeTimestamp);
                        if (trade) {
                            style = 'triangle';
                            color = trade.Signal.toLowerCase() === "long" ? 'green' : 'red';
                            visible = trade.Signal.toLowerCase() === "long" ? visibilityState["Trades: Long"] : visibilityState["Trades: Short"];
                        } else if (point.trade_signal && point.trade_signal.trim() !== "") {
                            style = 'triangle';
                            color = point.trade_signal.toLowerCase() === "long" ? 'green' : 'red';
                            visible = point.trade_signal.toLowerCase() === "long" ? visibilityState["Signals: Long"] : visibilityState["Signals: Short"];
                        } else if (point.p && point.p.trim() !== "") {
                            style = 'circle';
                            // Enhanced bearish pattern detection
                            const patternLower = point.p.toLowerCase();
                            if (patternLower.includes("bearish") || patternLower.includes("death")) {
                                color = 'red';
                                visible = visibilityState["Patterns: Bearish"];
                            } else if (patternLower.includes("bullish") || patternLower.includes("golden")) {
                                color = 'green';
                                visible = visibilityState["Patterns: Bullish"];
                            } else {
                                color = 'yellow';
                                visible = visibilityState["Patterns: Neutral"];
                            }
                        } else if (point.score !== undefined) {
                            style = 'circle';
                            if (point.score > 10) {
                                color = 'green';
                                visible = visibilityState["Score: Positive"];
                            } else if (point.score < -10) {
                                color = 'red';
                                visible = visibilityState["Score: Negative"];
                            } else {
                                color = 'yellow';
                                visible = visibilityState["Score: Neutral"];
                            }
                        }

                        pointBackgroundColors.push(color);
                        pointStyles.push(style);
                        pointRadius.push(visible ? (trade || point.trade_signal || point.p || Math.abs(point.score) > 10 ? 8 : 3) : 0);
                    });

                    const datasets = [
                        {
                            label: 'Price',
                            data: prices,
                            fill: false,
                            borderColor: '#8b5cf6',
                            borderWidth: 2,
                            tension: 0.4,
                            pointBackgroundColor: pointBackgroundColors,
                            pointStyle: pointStyles,
                            pointRadius: pointRadius,
                            hidden: !visibilityState["Price"]
                        },
                        {
                            label: 'Stop Loss Level',
                            data: stopLossLevels,
                            fill: false,
                            borderColor: 'red',
                            borderWidth: 1,
                            borderDash: [5, 5],
                            pointRadius: 0,
                            tension: 0,
                            hidden: !visibilityState["Stop Loss"]
                        }
                    ];

                    // This ensures the chart is wide enough for all data
                    const minRequiredWidth = Math.max(1200, filteredData.length * 10); // Increased multiplier
                    canvas.width = minRequiredWidth;

                    const tabs = tabsContainer.querySelectorAll('.date-tab');
                    tabs.forEach(tab => {
                        tab.classList.toggle('bg-blue-500', tab.getAttribute('data-date') === selectedDate);
                        tab.classList.toggle('text-white', tab.getAttribute('data-date') === selectedDate);
                        tab.classList.toggle('font-medium', tab.getAttribute('data-date') === selectedDate);
                        tab.classList.toggle('bg-gray-100', tab.getAttribute('data-date') !== selectedDate);
                        tab.classList.toggle('hover:bg-gray-200', tab.getAttribute('data-date') !== selectedDate);
                    });

                    if (window.backtestChart && typeof window.backtestChart.destroy === 'function') {
                        window.backtestChart.destroy();
                    }

                    window.backtestChart = new Chart(ctx, {
                        type: 'line',
                        data: { labels, datasets },
                        options: {
                            responsive: false,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: "top",
                                    align: "start",
                                    labels: {
                                        usePointStyle: true,
                                        padding: 15,
                                        generateLabels: function (chart) {
                                            return [
                                                { text: 'Price', fillStyle: '#8b5cf6', pointStyle: 'line', hidden: !visibilityState["Price"], datasetIndex: 0 },
                                                { text: 'Stop Loss', fillStyle: 'red', pointStyle: 'line', hidden: !visibilityState["Stop Loss"], datasetIndex: 1 },
                                                { text: 'Trades: Long', fillStyle: 'green', pointStyle: 'triangle', hidden: !visibilityState["Trades: Long"] },
                                                { text: 'Trades: Short', fillStyle: 'red', pointStyle: 'triangle', hidden: !visibilityState["Trades: Short"] },
                                                { text: 'Patterns: Bullish', fillStyle: 'green', pointStyle: 'circle', hidden: !visibilityState["Patterns: Bullish"] },
                                                { text: 'Patterns: Bearish', fillStyle: 'red', pointStyle: 'circle', hidden: !visibilityState["Patterns: Bearish"] },
                                                { text: 'Patterns: Neutral', fillStyle: 'yellow', pointStyle: 'circle', hidden: !visibilityState["Patterns: Neutral"] },
                                                { text: 'Signals: Long', fillStyle: 'green', pointStyle: 'triangle', hidden: !visibilityState["Signals: Long"] },
                                                { text: 'Signals: Short', fillStyle: 'red', pointStyle: 'triangle', hidden: !visibilityState["Signals: Short"] },
                                                { text: 'Score: Positive', fillStyle: 'green', pointStyle: 'circle', hidden: !visibilityState["Score: Positive"] },
                                                { text: 'Score: Negative', fillStyle: 'red', pointStyle: 'circle', hidden: !visibilityState["Score: Negative"] },
                                                { text: 'Score: Neutral', fillStyle: 'yellow', pointStyle: 'circle', hidden: !visibilityState["Score: Neutral"] }
                                            ].filter(item => {
                                                // Only show legend items that correspond to data present in the chart
                                                if (item.text === 'Price' || item.text === 'Stop Loss') return true;
                                                if (item.text.startsWith('Trades')) {
                                                    return filteredData.some(p => tradeMap.get(p.date.getTime()));
                                                }
                                                if (item.text.startsWith('Patterns')) {
                                                    return filteredData.some(p => p.p && p.p.trim() !== "" && (
                                                        (item.text.includes('Bullish') && (p.p.toLowerCase().includes('bullish') || p.p.toLowerCase().includes('golden'))) ||
                                                        (item.text.includes('Bearish') && (p.p.toLowerCase().includes('bearish') || p.p.toLowerCase().includes('death'))) ||
                                                        (item.text.includes('Neutral') && !(p.p.toLowerCase().includes('bullish') || p.p.toLowerCase().includes('golden') || p.p.toLowerCase().includes('bearish') || p.p.toLowerCase().includes('death')))
                                                    ));
                                                }
                                                if (item.text.startsWith('Signals')) {
                                                    return filteredData.some(p => p.trade_signal && p.trade_signal.trim() !== "");
                                                }
                                                if (item.text.startsWith('Score')) {
                                                    return filteredData.some(p => p.score !== undefined && (
                                                        (item.text.includes('Positive') && p.score > 10) ||
                                                        (item.text.includes('Negative') && p.score < -10) ||
                                                        (item.text.includes('Neutral') && Math.abs(p.score) <= 10)
                                                    ));
                                                }
                                                return false;
                                            });
                                        }
                                    },
                                    onClick: function (e, legendItem) {
                                        const label = legendItem.text;
                                        visibilityState[label] = !visibilityState[label];

                                        if (label === 'Price' || label === 'Stop Loss') {
                                            const index = legendItem.datasetIndex;
                                            const meta = this.chart.getDatasetMeta(index);
                                            meta.hidden = !meta.hidden;
                                        }

                                        updateChart(selectedDate); // Re-render chart with updated visibility
                                    }
                                },
                                tooltip: {
                                    mode: "index",
                                    intersect: false,
                                    callbacks: {
                                        label: function (context) {
                                            const formatNumber = (n) => (typeof n === "number" ? n.toFixed(2) : n);
                                            const index = context.dataIndex;
                                            const point = filteredData[index];
                                            const trade = tradeMap.get(point.date.getTime());
                                            const lines = [
                                                `Stop Loss: ${stopLossLevels[index].toFixed(2)}`
                                            ];
                                            if (point.o !== undefined) lines.push(`Open: ${formatNumber(point.o)}`);
                                            if (point.h !== undefined) lines.push(`High: ${formatNumber(point.h)}`);
                                            if (point.l !== undefined) lines.push(`Low: ${formatNumber(point.l)}`);
                                            if (point.c !== undefined) lines.push(`Close: ${formatNumber(point.c)}`);
                                            if (point.ema !== undefined) lines.push(`EMA: ${formatNumber(point.ema)}`);
                                            if (point.sma !== undefined) lines.push(`SMA: ${formatNumber(point.sma)}`);
                                            if (point.bb_upper !== undefined) lines.push(`BB Upper: ${formatNumber(point.bb_upper)}`);
                                            if (point.bb_lower !== undefined) lines.push(`BB Lower: ${formatNumber(point.bb_lower)}`);
                                            if (point.rsi !== undefined) lines.push(`RSI: ${formatNumber(point.rsi)}`);
                                            if (point.macd !== undefined) lines.push(`MACD: ${formatNumber(point.macd)}`);
                                            if (point.macd_signal !== undefined) lines.push(`MACD Signal: ${formatNumber(point.macd_signal)}`);
                                            if (point.adx !== undefined) lines.push(`ADX: ${formatNumber(point.adx)}`);
                                            if (point.cci !== undefined) lines.push(`CCI: ${formatNumber(point.cci)}`);
                                            if (point.atr !== undefined) lines.push(`ATR: ${formatNumber(point.atr)}`);
                                            if (point.stoch_k !== undefined) lines.push(`Stoch %K: ${formatNumber(point.stoch_k)}`);
                                            if (point.stoch_d !== undefined) lines.push(`Stoch %D: ${formatNumber(point.stoch_d)}`);
                                            if (point.score !== undefined) lines.push(`Score: ${point.score || 'N/A'}`);
                                            if (point.s !== undefined) lines.push(`Signal: ${point.s && point.s.trim() !== "" ? point.s : "None"}`);
                                            if (trade) {
                                                lines.push(`Trade Signal: ${trade.Signal}`, `Profit: ${trade.ProfitPoints.toFixed(2)}`);
                                            }
                                            if (point.p) lines.push(`Candle Patterns: ${point.p}`);
                                            if (point.cp) lines.push(`Chart Patterns: ${point.cp}`);
                                            return lines;
                                        }
                                    }
                                },
                                zoom: {
                                    pan: {
                                        enabled: true,
                                        mode: "x",
                                        scaleMode: 'position',
                                        overScaleMode: 'x'
                                    },
                                    zoom: {
                                        wheel: {
                                            enabled: true,
                                            speed: 0.05
                                        },
                                        pinch: {
                                            enabled: true
                                        },
                                        mode: "x",
                                    },
                                }
                            },
                            scales: {
                                x: {
                                    type: "category",
                                    ticks: {
                                        maxRotation: 0,
                                        autoSkip: true,
                                        maxTicksLimit: 12
                                    },
                                }
                            },
                            // Add event handlers for better interaction
                            onResize: function (chart, size) {
                                // Ensure chart container is scrollable when chart is wider than viewport
                                const container = chart.canvas.parentNode;
                                if (container) {
                                    container.style.overflowX = 'auto';
                                }
                            }
                        }
                    });

                    const scrollContainer = canvas.parentNode;
                    if (scrollContainer) {
                        scrollContainer.style.overflowX = 'auto';
                        scrollContainer.style.width = '100%';
                        scrollContainer.scrollLeft = 0; // Reset scroll position when changing dates
                    }

                    // Manually trigger a resize to ensure everything is properly laid out
                    window.backtestChart.resize();

                    // Add UI buttons for zoom control
                    addZoomControls(canvas, window.backtestChart);
                }

                const tabs = tabsContainer.querySelectorAll('.date-tab');
                tabs.forEach(tab => {
                    tab.addEventListener('click', () => updateChart(tab.getAttribute('data-date')));
                });
                // Display the trades summary section   
                document.getElementById("trades-summary").classList.remove("hidden");
                // Render the chart for the first date by default
                if (uniqueDates.length > 0) {
                    updateChart(uniqueDates[0]);
                } else {
                    resultsContent.innerHTML = `
                        <div class="p-4 bg-yellow-50 text-yellow-700 rounded-lg border border-yellow-200">
                            <h3 class="font-semibold mb-2">No Dates Available</h3>
                            <p>No unique dates were found in the data to display.</p>
                        </div>
                    `;
                }

                if (data.payoff) {
                    generateTradesSummary(data.payoff, chartData);
                } else {
                    document.getElementById("trades-table-container").innerHTML =
                        '<p class="text-sm text-gray-500">No trade data available for this backtest.</p>';
                }
            } catch (error) {
                console.error("Error rendering chart:", error);
                resultsContent.innerHTML = `
                <div class="p-4 bg-red-50 text-red-700 rounded-lg border border-red-200">
                    <h3 class="font-semibold mb-2">Error Rendering Chart</h3>
                    <p>${error.message || "Unknown error occurred during chart creation"}</p>
                    <div class="mt-2 p-2 bg-white rounded overflow-auto max-h-40">
                        <pre class="text-xs">${error.stack || ""}</pre>
                    </div>
                </div>
            `;
            }
        }

        /**
         * Helper function to find the closest price data point for a given timestamp
         */
        function getClosestPriceForTimestamp(chartData, timestamp) {
            if (!chartData || chartData.length === 0) return null;

            const targetDate = new Date(timestamp);
            let closestPoint = chartData[0];
            let minDiff = Math.abs(targetDate - chartData[0].date);

            for (let i = 1; i < chartData.length; i++) {
                const diff = Math.abs(targetDate - chartData[i].date);
                if (diff < minDiff) {
                    minDiff = diff;
                    closestPoint = chartData[i];
                }
            }

            // Return the price from the "c" key
            return closestPoint.c;
        }

        function generateTradesSummary(payoff, candleData) {
            if (!payoff || !payoff.trades || payoff.trades.length === 0) {
                document.getElementById("trades-table-container").innerHTML =
                    '<p class="text-sm text-gray-500">No trades were executed during this backtest.</p>';
                return;
            }

            // Calculate summary values
            const totalTrades = payoff.trades.length;
            const successfulTrades = payoff.trades.filter(
                (trade) => trade.ProfitPoints > 0
            ).length;
            const hitRatio = ((successfulTrades / totalTrades) * 100).toFixed(2);
            const netProfit = calculateNetProfit(payoff.trades);

            // Format the trades for display including a Details column with a toggle button
            const tradesHtml = `
    <div class="overflow-x-auto">
      <table class="min-w-full text-sm">
        <thead>
          <tr class="bg-gray-100 dark:bg-gray-800">
            <th class="px-4 py-2 text-left font-medium">Time</th>
            <th class="px-4 py-2 text-left font-medium">Signal</th>
            <th class="px-4 py-2 text-left font-medium">Score</th>
            <th class="px-4 py-2 text-left font-medium">Profit</th>
            <th class="px-4 py-2 text-left font-medium">Duration</th>
            <th class="px-4 py-2 text-left font-medium">Status</th>
            <th class="px-4 py-2 text-left font-medium">Details</th>
          </tr>
        </thead>
        <tbody>
          ${payoff.trades
                    .map((trade) => {
                        const closestCandle = getClosestCandle(candleData, trade.Time);
                        return `
            <tr class="border-b dark:border-gray-700">
              <td class="px-4 py-2">${formatTimestamp(trade.Time)}</td>
              <td class="px-4 py-2">
                <span class="px-2 py-1 rounded-full text-xs ${trade.Signal === "Long"
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"}">
                  ${trade.Signal}
                </span>
              </td>
              <td class="px-4 py-2">${trade.Score}</td>
              <td class="px-4 py-2 ${trade.ProfitPoints > 0 ? "text-green-600" : "text-red-600"}">
                ${trade.ProfitPoints > 0 ? "+" : ""}${trade.ProfitPoints.toFixed(2)}
              </td>
              <td class="px-4 py-2">${formatDuration(trade.Duration)}</td>
              <td class="px-4 py-2 ${trade.Status.includes("Stop Loss")
                                ? "text-red-600"
                                : "text-green-600"}">
                ${trade.Status}
              </td>
              <td class="px-4 py-2">
                <button onclick="toggleTradeDetails(this)" class="px-2 py-1 bg-blue-100 rounded">+</button>
              </td>
            </tr>
            <tr class="trade-details" style="display:none;">
              <td colspan="7" class="px-4 py-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <strong class="block mb-2">Trade Details:</strong>
                    ${formatTradeDetails(trade)}
                  </div>
                  <div>
                    <strong class="block mb-2">Closest Candle Data:</strong>
                    ${formatCandleDetails(closestCandle)}
                  </div>
                </div>
              </td>
            </tr>
          `;
                    })
                    .join("")}
        </tbody>
      </table>
    </div>
    
    <div class="mt-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg">
      <h5 class="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">Summary</h5>
      <div class="grid lg:grid-cols-3 md:grid-cols-2 grid-col-1 gap-4 text-sm">
        <div>
          <span class="text-gray-600 dark:text-gray-400">Total Trades:</span>
          <span class="font-medium ml-2">${totalTrades}</span>
        </div>
        <div>
          <span class="text-gray-600 dark:text-gray-400">Net Profit:</span>
          <span class="font-medium ml-2 ${netProfit >= 0 ? "text-green-600" : "text-red-600"}">
            ${netProfit > 0 ? "+" : ""}${netProfit.toFixed(2)} points
          </span>
        </div>
        <div>
          <span class="text-gray-600 dark:text-gray-400">Hit Ratio:</span>
          <span class="font-medium ml-2">${successfulTrades}/${totalTrades} (${hitRatio}%)</span>
        </div>
      </div>
    </div>
  `;

            document.getElementById("trades-table-container").innerHTML = tradesHtml;
        }

        // Toggle function to show/hide trade details
        function toggleTradeDetails(btn) {
            const detailsRow = btn.parentElement.parentElement.nextElementSibling;
            if (detailsRow.style.display === "none") {
                detailsRow.style.display = "table-row";
                btn.textContent = "-";
            } else {
                detailsRow.style.display = "none";
                btn.textContent = "+";
            }
        }

        /**
         * Format Unix timestamp to readable date/time
         */
        function formatTimestamp(timestamp) {
            const date = new Date(parseInt(timestamp) * 1000);
            return date.toLocaleString("en-US", {
                month: "short",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
            });
        }

        /**
         * Format duration seconds to readable format
         */
        function formatDuration(seconds) {
            const minutes = Math.floor(seconds / 60);
            if (minutes < 60) {
                return `${minutes} min`;
            } else {
                const hours = Math.floor(minutes / 60);
                const remainingMinutes = minutes % 60;
                return `${hours}h ${remainingMinutes}m`;
            }
        }

        /**
         * Calculate net profit from trades
         */
        function calculateNetProfit(trades) {
            return trades.reduce((total, trade) => total + trade.ProfitPoints, 0);
        }

        // Stoploss slider functionality and other event listeners
        document.addEventListener("DOMContentLoaded", function () {
            const stoplossRange = document.getElementById("stoploss-range");
            const stoplossValue = document.getElementById("stoploss-value");
            const stoplossCoefficient = document.getElementById(
                "stoploss-coefficient"
            );

            // Initialize default dates on page load
            initializeDefaultDates();

            stoplossRange.addEventListener("input", function () {
                stoplossValue.textContent = this.value;
                stoplossCoefficient.value = this.value;
            });

            // --- Helper function for Select/Deselect ---
            function setupSelectDeselect(selectId, deselectId, selector) {
                const selectBtn = document.getElementById(selectId);
                const deselectBtn = document.getElementById(deselectId);

                if (selectBtn) {
                    selectBtn.addEventListener("click", function () {
                        document.querySelectorAll(selector).forEach((cb) => {
                            cb.checked = true;
                        });
                    });
                } else {
                    console.warn(`Button with ID "${selectId}" not found.`);
                }

                if (deselectBtn) {
                    deselectBtn.addEventListener("click", function () {
                        document.querySelectorAll(selector).forEach((cb) => {
                            cb.checked = false;
                        });
                    });
                } else {
                    console.warn(`Button with ID "${deselectId}" not found.`);
                }
            }

            // --- Setup Specific Select/Deselect Listeners ---

            // Candlestick Patterns
            setupSelectDeselect('select-all-bullish-candlestick', 'deselect-all-bullish-candlestick', '[data-pattern-type="bullish-candlestick"] input[name="candlestick-pattern"]');
            setupSelectDeselect('select-all-bearish-candlestick', 'deselect-all-bearish-candlestick', '[data-pattern-type="bearish-candlestick"] input[name="candlestick-pattern"]');
            setupSelectDeselect('select-all-neutral-candlestick', 'deselect-all-neutral-candlestick', '[data-pattern-type="neutral-candlestick"] input[name="candlestick-pattern"]');

            // Chart Patterns
            setupSelectDeselect('select-all-bullish-chart', 'deselect-all-bullish-chart', '[data-pattern-type="bullish-chart"] input[name="chart-pattern"]');
            setupSelectDeselect('select-all-bearish-chart', 'deselect-all-bearish-chart', '[data-pattern-type="bearish-chart"] input[name="chart-pattern"]');

        });

        // New helper: getClosestCandle returns the candle data with the closest date to the given timestamp
        function getClosestCandle(candleData, tradeTimestamp) {
            if (!candleData || candleData.length === 0) return null;
            const targetDate = new Date(parseInt(tradeTimestamp) * 1000);
            let closest = candleData[0];
            let closestDate = (closest.date instanceof Date) ? closest.date : new Date(closest.date);
            let minDiff = Math.abs(targetDate - closestDate);
            for (let i = 1; i < candleData.length; i++) {
                let currentDate = (candleData[i].date instanceof Date) ? candleData[i].date : new Date(candleData[i].date);
                const diff = Math.abs(targetDate - currentDate);
                if (diff < minDiff) {
                    minDiff = diff;
                    closest = candleData[i];
                }
            }
            return closest;
        }

        // Add this helper function before using it in generateTradesSummary
        function stringifyAll(obj) {
            console.log("Object to stringify:", obj);
            if (!obj) return "";
            const allKeys = Object.getOwnPropertyNames(obj);
            const result = {};
            allKeys.forEach(key => result[key] = obj[key]);
            return JSON.stringify(result, null, 2);
        }

        function formatCandleDetails(candle) {
            if (!candle) return "No candle data available";
            return `
                <table class="text-xs w-full">
                    <tr class="border-b"><td class="font-semibold pr-2 py-1">Open:</td><td>${candle.o?.toFixed(2) || 'N/A'}</td></tr>
                    <tr class="border-b"><td class="font-semibold pr-2 py-1">High:</td><td>${candle.h?.toFixed(2) || 'N/A'}</td></tr>
                    <tr class="border-b"><td class="font-semibold pr-2 py-1">Low:</td><td>${candle.l?.toFixed(2) || 'N/A'}</td></tr>
                    <tr class="border-b"><td class="font-semibold pr-2 py-1">Close:</td><td>${candle.c?.toFixed(2) || 'N/A'}</td></tr>
                    <tr class="border-b"><td class="font-semibold pr-2 py-1">Pattern:</td><td>${candle.p || 'None'}</td></tr>
                </table>
            `;
        }

        function formatTradeDetails(trade) {
            if (!trade) return "No trade data available";
            return `
                <table class="text-xs w-full border-collapse">
                    <tr class="border-b"><td class="font-semibold pr-2 py-1">Time:</td><td>${formatTimestamp(trade.Time)}</td></tr>
                    <tr class="border-b"><td class="font-semibold pr-2 py-1">Signal:</td><td>${trade.Signal || 'N/A'}</td></tr>
                    <tr class="border-b"><td class="font-semibold pr-2 py-1">Score:</td><td>${trade.Score || 'N/A'}</td></tr>
                    <tr class="border-b"><td class="font-semibold pr-2 py-1">Profit:</td><td>${trade.ProfitPoints?.toFixed(2) || 'N/A'}</td></tr>
                    <tr class="border-b"><td class="font-semibold pr-2 py-1">Duration:</td><td>${formatDuration(trade.Duration)}</td></tr>
                    <tr class="border-b"><td class="font-semibold pr-2 py-1">Status:</td><td>${trade.Status || 'N/A'}</td></tr>
                    <tr><td class="font-semibold pr-2 py-1">Additional Data:</td><td>${trade.AdditionalData || 'None'}</td></tr>
                </table>
            `;
        }

        function addZoomControls(canvas, chart) {
            // Remove existing controls if any
            const existingControls = document.getElementById('chart-zoom-controls');
            if (existingControls) {
                existingControls.remove();
            }

            // Create control container
            const controlsDiv = document.createElement('div');
            controlsDiv.id = 'chart-zoom-controls';
            controlsDiv.className = 'flex items-center justify-center gap-2 mt-2 mb-2';

            // Create buttons
            const zoomInBtn = document.createElement('button');
            zoomInBtn.className = 'px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600';
            zoomInBtn.innerHTML = '<span>Zoom In</span>';
            zoomInBtn.onclick = function () {
                chart.zoom(1.2);
            };

            const zoomOutBtn = document.createElement('button');
            zoomOutBtn.className = 'px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600';
            zoomOutBtn.innerHTML = '<span>Zoom Out</span>';
            zoomOutBtn.onclick = function () {
                chart.zoom(0.8);
            };

            const resetBtn = document.createElement('button');
            resetBtn.className = 'px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600';
            resetBtn.innerHTML = '<span>Reset View</span>';
            resetBtn.onclick = function () {
                chart.resetZoom();
            };

            const fitAllBtn = document.createElement('button');
            fitAllBtn.className = 'px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600';
            fitAllBtn.innerHTML = '<span>Fit All Data</span>';
            fitAllBtn.onclick = function () {
                chart.resetZoom();
                // Additional logic to fit all data points
                const meta = chart.getDatasetMeta(0);
                if (meta && meta.data && meta.data.length > 0) {
                    chart.zoom(0.1); // Extreme zoom out to see all data
                }
            };

            // Add buttons to container
            controlsDiv.appendChild(zoomInBtn);
            controlsDiv.appendChild(zoomOutBtn);
            controlsDiv.appendChild(resetBtn);
            controlsDiv.appendChild(fitAllBtn);

            // Add container after the canvas
            canvas.parentNode.parentNode.insertBefore(controlsDiv, canvas.parentNode.nextSibling);
        }
    </script>

    {% include 'algos/scalping/css.html' %}

</body>

</html>