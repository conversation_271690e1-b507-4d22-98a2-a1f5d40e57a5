<!-- Scroll to Top Button -->
<button id="scrollToTopBtn"
    class="fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white rounded-full p-3 shadow-lg transition-all duration-300 z-50 opacity-0"
    onclick="scrollToTop()" aria-label="Scroll to top">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-up">
        <path d="m18 15-6-6-6 6" />
    </svg>
</button>

<script>
    // Function to scroll to the top of the page
    function scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // Show/hide the button based on scroll position
    window.addEventListener('scroll', function () {
        const scrollToTopBtn = document.getElementById('scrollToTopBtn');
        if (window.scrollY > 300) {
            scrollToTopBtn.classList.remove('opacity-0', 'pointer-events-none');
            scrollToTopBtn.classList.add('opacity-100');
        } else {
            scrollToTopBtn.classList.remove('opacity-100');
            scrollToTopBtn.classList.add('opacity-0', 'pointer-events-none');
        }
    });

    // Hide button initially
    document.addEventListener('DOMContentLoaded', function () {
        const scrollToTopBtn = document.getElementById('scrollToTopBtn');
        if (window.scrollY <= 300) {
            scrollToTopBtn.classList.add('opacity-0', 'pointer-events-none');
        }
    });
</script>