<script>   // Fix the function definitions to make them properly defined in the global scope

    // Adds a new filter group (used by Add Filter Group button)
    function addFilterGroup() {
        const container = document.getElementById("filter-groups-container");

        // Create group container
        const groupDiv = document.createElement("div");
        groupDiv.className =
            "bg-gray-50 border dark:from-blue-900/20 duration-300 rounded-xl transition-all";
        groupDiv.setAttribute("data-filter-group", "");

        // Add with animation
        groupDiv.style.opacity = "0";
        groupDiv.style.transform = "translateY(20px)";

        groupDiv.innerHTML = `
          <div class="flex items-center justify-between p-2 bg-white rounded-t-xl border-b">
            <div class="flex items-center space-x-3">
              <div>
                <label class="text-sm font-medium">Group Logic:</label>
              </div>
              <div class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400 focus:outline-0 px-2 py-0.5 rounded-md shadow-sm text-sm">
              <select name="group-operator" class="bg-transparent outline-0 focus:shadow-nonw">
                <option value="AND">AND</option>
                <option value="OR">OR</option>
              </select>
            </div>
              <span class="text-xs text-gray-500 dark:text-gray-400 italic hidden md:block">(How filters combine)</span>
            </div>
            <button type="button" onclick="removeFilterGroup(this)" class="border border-red-500 duration-300 flex from-red-500 hover:bg-red-500 hover:from-red-600 hover:shadow hover:text-white hover:to-pink-600 items-center px-2 py-1.5 rounded-lg shadow-sm space-x-1 text-red-500 text-xs to-pink-500 transition-all">
             
              <span>Delete Group</span>
            </button>
          </div>
          <div class="space-y-3 filter-rows-container p-2" data-filters-list>
            <div class="flex flex-wrap md:flex-nowrap gap-3 p-4 bg-white dark:bg-gray-800 rounded-md  dark:border-gray-700  transition-all duration-300 transform hover:bg-blue-50 filter-row-animate" data-filter-row>
              <div class="w-full md:w-1/3">
                <input type="text" name="filter-field" placeholder="Field (e.g., {rsi})" class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400  focus:outline-0 px-2 py-2.5 rounded-md shadow-sm text-sm w-full" />
              </div>
              <div class="w-full md:w-1/3">
                <div class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400  focus:outline-0 px-2 py-2.5 rounded-md shadow-sm text-sm w-full bg-white">
                <select name="filter-condition" class="bg-transparent outline-0 focus:shadow-none w-full">
                  <option value="" disabled selected>Select condition</option>
                  <option value="gte">Greater than or equal (≥)</option>
                  <option value="gt">Greater than (>)</option>
                  <option value="lte">Less than or equal (≤)</option>
                  <option value="lt">Less than (<)</option>
                  <option value="eq">Equal to (=)</option>
                  <option value="neq">Not equal to (≠)</option>
                </select>
            </div>
              </div>
              <div class="w-full md:w-1/4">
                <input type="text" name="filter-value" placeholder="Value" class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400  focus:outline-0 px-2 py-2.5 rounded-md shadow-sm text-sm w-full" />
              </div>
              <div class="flex items-center">
                <button type="button" onclick="removeFilterRow(this)" class="bg-gradient-to-br duration-300 flex from-red-50 h-10 hover:from-red-50 hover:scale-105 hover:shadow hover:to-pink-200 items-center justify-center rounded-lg text-red-400 to-pink-100 transform transition-all w-10">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                </button>
              </div>
            </div>
          </div>
          <div class="p-2 border-t">
          <button type="button" onclick="addFilterRow(this)" class="flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium transition-all duration-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 px-3 py-2 rounded-lg shadow-sm hover:shadow">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span>Add Filter</span>
          </button></div>
        `;
        container.appendChild(groupDiv);

        // Animate entry
        setTimeout(() => {
            groupDiv.style.transition = "opacity 0.5s ease, transform 0.5s ease";
            groupDiv.style.opacity = "1";
            groupDiv.style.transform = "translateY(0)";
        }, 10);
    }

    // Adds a new filter row within the current group
    function addFilterRow(groupButton) {
        const groupDiv = groupButton.closest("[data-filter-group]");
        const filtersList = groupDiv.querySelector("[data-filters-list]");

        const row = document.createElement("div");
        row.className =
            "flex flex-wrap md:flex-nowrap gap-3 p-4 bg-white dark:bg-gray-800 rounded-md  dark:border-gray-700  transition-all duration-300 transform hover:bg-blue-50 filter-row-animate";
        row.setAttribute("data-filter-row", "");

        // Add with animation
        row.style.opacity = "0";
        row.style.maxHeight = "0";
        row.style.overflow = "hidden";

        row.innerHTML = `
          <div class="w-full md:w-1/3">
            <input type="text" name="filter-field" placeholder="Field (e.g., {rsi})" class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400  focus:outline-0 px-2 py-2.5 rounded-md shadow-sm text-sm w-full" />
          </div>
          <div class="w-full md:w-1/3">
            <div class="border border-gray-300 bg-white cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400  focus:outline-0 px-2 py-2.5 rounded-md shadow-sm text-sm w-full">
            <select name="filter-condition" class="bg-transparent outline-0 focus:shadow-none w-full">
              <option value="" disabled selected>Select condition</option>
              <option value="gte">Greater than or equal (≥)</option>
              <option value="gt">Greater than (>)</option>
              <option value="lte">Less than or equal (≤)</option>
              <option value="lt">Less than (<)</option>
              <option value="eq">Equal to (=)</option>
              <option value="neq">Not equal to (≠)</option>
            </select>
        </div>
          </div>
          <div class="w-full md:w-1/4">
            <input type="text" name="filter-value" placeholder="Value" class="border border-gray-300 cursor-pointer dark:bg-neutral-700 dark:border-neutral-600 focus:border focus:border-blue-400  focus:outline-0 px-2 py-2.5 rounded-md shadow-sm text-sm w-full" />
          </div>
          <div class="flex items-center">
            <button type="button" onclick="removeFilterRow(this)" class="bg-gradient-to-br duration-300 flex from-red-50 h-10 hover:from-red-50 hover:scale-105 hover:shadow hover:to-pink-200 items-center justify-center rounded-lg text-red-400 to-pink-100 transform transition-all w-10">
             <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
            </button>
          </div>
        `;
        filtersList.appendChild(row);

        // Animate entry
        setTimeout(() => {
            row.style.transition = "opacity 0.4s ease, max-height 0.4s ease";
            row.style.opacity = "1";
            row.style.maxHeight = "";
        }, 10);
    }

    // Removes a single filter row
    function removeFilterRow(rowButton) {
        const row = rowButton.closest("[data-filter-row]");

        // Animate exit
        row.style.transition = "opacity 0.3s ease, transform 0.3s ease";
        row.style.opacity = "0";
        row.style.transform = "translateX(20px)";

        setTimeout(() => {
            row.remove();
        }, 300);
    }

    // Removes the entire filter group
    function removeFilterGroup(groupButton) {
        const groupDiv = groupButton.closest("[data-filter-group]");

        // Animate exit
        groupDiv.style.transition = "opacity 0.4s ease, transform 0.4s ease";
        groupDiv.style.opacity = "0";
        groupDiv.style.transform = "scale(0.95)";

        setTimeout(() => {
            groupDiv.remove();
        }, 400);
    }

</script>