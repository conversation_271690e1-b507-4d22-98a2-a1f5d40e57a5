<!-- Scalping Strategy Container -->
<div id="scalping-strategy-load-save-container" class="flex flex-col gap-2 relative">
    <!-- Load the strategies in scrollable rows -->
    <div id="scalping-strategies-tab-container" class="hidden border border-white mt-3 p-2 rounded-md"
        style="background-image: linear-gradient(45deg, #f0faff, #ffffff);">
        <div class="flex justify-between items-center mb-2">
            <h3 class="font-medium text-neutral-600 uppercase text-xs">Saved Strategies</h3>
        </div>
        <div id="scalping-strategies-tab-list"
            class="flex flex-col gap-1 max-h-[108px] overflow-y-auto custom-scroll overflow-x-hidden">
            <div class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-1">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Save Strategy Dropdown -->
    <div id="saveScalpingStrategyModal"
        class="modal hidden fixed bg-white rounded-xl shadow-xl p-4 w-[28rem] z-50 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-gray-800">Save Scalping Strategy</h2>
            <button onclick="closeSaveScalpingStrategyModal()"
                class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <div class="mb-4">
            <label for="scalpingStrategyName" class="block text-sm font-medium text-gray-700 mb-2">Strategy Name</label>
            <input type="text" id="scalpingStrategyName" placeholder="Enter strategy name"
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
        </div>
        <div class="flex justify-end gap-3">
            <button onclick="closeSaveScalpingStrategyModal()"
                class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors">
                Cancel
            </button>
            <button onclick="saveScalpingStrategy()"
                class="px-4 py-2 text-sm font-medium text-white bg-green-500 hover:bg-green-600 rounded-lg transition-colors">
                Save Strategy
            </button>
        </div>
    </div>
</div>

<script>
    // Initialize CRUD instance for scalping strategies
    const scalpingStrategiesCrud = new Crud('scalping-strategies');

    // Function to open the save strategy modal
    function openSaveScalpingStrategyModal(event) {
        event.stopPropagation();

        try {
            // Validate all strategy data before opening the modal
            collectStrategyData();

            // If validation passes, open the modal
            const modal = document.getElementById('saveScalpingStrategyModal');
            modal.classList.remove('hidden');

            // Position the modal next to the save button
            const buttonRect = event.target.getBoundingClientRect();
            modal.style.top = `${buttonRect.top}px`;
            modal.style.left = `${buttonRect.right + 10}px`; // 10px gap

            // Check if modal would overflow the viewport
            setTimeout(() => {
                const modalRect = modal.getBoundingClientRect();
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // Adjust horizontal position if needed
                if (modalRect.right > viewportWidth) {
                    modal.style.left = `${buttonRect.left - modalRect.width - 10}px`;
                }

                // Adjust vertical position if needed
                if (modalRect.bottom > viewportHeight) {
                    modal.style.top = `${viewportHeight - modalRect.height - 10}px`;
                }
            }, 0);

            document.getElementById('scalpingStrategyName').focus();
        } catch (error) {
            // Show alert with the validation error
            showAlert(error.message);
            return;
        }
    }

    // Function to close the save strategy modal
    function closeSaveScalpingStrategyModal() {
        document.getElementById('saveScalpingStrategyModal').classList.add('hidden');
    }

    // Function to collect strategy data from the UI
    function collectStrategyData() {
        // Validate symbol
        const symbol = document.getElementById('stock-input')?.value;
        if (!symbol || symbol.trim() === '') {
            throw new Error('Please select a valid stock symbol');
        }

        // Get the strategy type (Bullish/Bearish)
        const strategyType = document.querySelector('input[name="strategy-type"]:checked').value;

        // Validate and get filter groups
        const filterGroups = [];
        const filterGroupElements = document.querySelectorAll('[data-filter-group]');
        if (filterGroupElements.length === 0) {
            throw new Error('Please add at least one filter group');
        }

        // Check if any filter group has valid filters
        let hasValidFilter = false;
        filterGroupElements.forEach(group => {
            const groupOperator = group.querySelector('select[name="group-operator"]').value;
            const filters = [];

            group.querySelectorAll('[data-filter-row]').forEach(row => {
                const field = row.querySelector('input[name="filter-field"]').value;
                const condition = row.querySelector('select[name="filter-condition"]').value;
                const value = row.querySelector('input[name="filter-value"]').value;

                if (field && field.trim() !== '' && condition && value && value.trim() !== '') {
                    filters.push({ field, condition, value });
                    hasValidFilter = true;
                }
            });

            if (filters.length > 0) {
                filterGroups.push({ operator: groupOperator, filters });
            }
        });

        if (!hasValidFilter) {
            throw new Error('Please add at least one valid filter condition');
        }

        // Get selected candlestick patterns
        const candlestickPatterns = [];
        document.querySelectorAll('input[name="candlestick-pattern"]:checked').forEach(checkbox => {
            // Format pattern consistently like in runBacktest
            const formattedPattern = checkbox.value;
            candlestickPatterns.push(formattedPattern);
        });

        // Get selected chart patterns
        const chartPatterns = [];
        document.querySelectorAll('input[name="chart-pattern"]:checked').forEach(checkbox => {
            // Format pattern consistently like in runBacktest
            const formattedPattern = checkbox.value;
            chartPatterns.push(formattedPattern);
        });

        // Get stoploss coefficient
        const stoplossCoefficient = document.getElementById('stoploss-coefficient').value;

        // Get duration
        const duration = document.querySelector('input[name="duration"]:checked').value;

        // Validate and get date range
        const isDateRangeValid = validateDateRange();
        if (!isDateRangeValid) {
            throw new Error('Please select a date range with at most 30 trading days.');
        }
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;
        if (!startDate || !endDate) {
            throw new Error('Please set both start and end dates');
        }

        return {
            symbol,
            strategyType,
            filterGroups,
            candlestickPatterns,
            chartPatterns,
            stoplossCoefficient,
            duration,
            dateRange: { startDate, endDate }
        };
    }

    // Function to save the current strategy
    async function saveScalpingStrategy() {
        const nameInput = document.getElementById('scalpingStrategyName');
        const name = nameInput.value.trim();

        if (!name) {
            showAlert('Please enter a strategy name');
            return;
        }

        if (name.length > 30) {
            showAlert('Strategy name cannot exceed 30 characters');
            nameInput.value = name.substring(0, 30);
            return;
        }

        try {
            // Collect and validate strategy data
            const strategyData = {
                name: name,
                data: collectStrategyData(),
                uid: generateUUID(),
                createdAt: new Date().toISOString()
            };

            await scalpingStrategiesCrud.create(strategyData);
            closeSaveScalpingStrategyModal();
            document.getElementById('scalpingStrategyName').value = '';
            showAlert('Strategy saved successfully!');
            loadScalpingStrategiesTab(); // Refresh the strategies view
        } catch (error) {
            console.error('Error saving strategy:', error);
            showAlert(error.message || 'Error saving strategy', 'error');
            // Don't close the modal if there's a validation error
        }
    }

    // Function to load a saved strategy
    function loadScalpingStrategy(strategyData) {
        // Hide the save strategy button
        document.getElementById("save-strategy-btn").classList.add("hidden");
        // Populate symbol
        const searchInput = document.getElementById('stock-input');
        if (searchInput) {
            searchInput.value = strategyData?.data?.symbol;
        }

        const data = strategyData.data;

        // Set strategy type
        document.querySelectorAll('input[name="strategy-type"]').forEach(radio => {
            radio.checked = radio.value === data.strategyType;
        });

        // Clear existing filter groups
        const filterGroupsContainer = document.getElementById('filter-groups-container');
        filterGroupsContainer.innerHTML = '';

        // Add filter groups
        data.filterGroups.forEach(group => {
            addFilterGroup();
            const newGroup = document.querySelector('[data-filter-group]:last-of-type');
            newGroup.querySelector('select[name="group-operator"]').value = group.operator;

            // Remove the default filter row
            const firstFilterRow = newGroup.querySelector('[data-filter-row]');
            firstFilterRow.remove();

            // Add each filter
            group.filters.forEach(filter => {
                addFilterRow(newGroup.querySelector('[data-filters-list]'));
                const newRow = newGroup.querySelector('[data-filter-row]:last-of-type');
                newRow.querySelector('input[name="filter-field"]').value = filter.field;
                newRow.querySelector('select[name="filter-condition"]').value = filter.condition;
                newRow.querySelector('input[name="filter-value"]').value = filter.value;
            });
        });

        // Set candlestick patterns
        document.querySelectorAll('input[name="candlestick-pattern"]').forEach(checkbox => {
            checkbox.checked = data.candlestickPatterns.includes(checkbox.value);
        });

        // Set chart patterns
        document.querySelectorAll('input[name="chart-pattern"]').forEach(checkbox => {
            checkbox.checked = data.chartPatterns.includes(checkbox.value);
        });

        // Set stoploss coefficient
        const stoplossRange = document.getElementById('stoploss-range');
        const stoplossValue = document.getElementById('stoploss-value');
        const stoplossCoefficient = document.getElementById('stoploss-coefficient');
        const coef = data.stoplossCoefficient || "2";
        stoplossRange.value = coef;
        stoplossValue.textContent = coef;
        stoplossCoefficient.value = coef;

        // Set duration
        document.querySelectorAll('input[name="duration"]').forEach(radio => {
            radio.checked = radio.value === data.duration;
        });

        // Set date range
        if (data.dateRange) {
            document.getElementById('start-date').value = data.dateRange.startDate;
            document.getElementById('end-date').value = data.dateRange.endDate;
        }

        // Scroll to the run backtest button
        const runBacktestBtn = document.getElementById('run-backtest-btn');
        runBacktestBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });

        showAlert('Strategy loaded successfully!');
    }

    // Function to delete a saved strategy
    async function deleteScalpingStrategy(id) {
        const confirmed = await showConfirmDialog({
            title: 'Delete Strategy',
            message: 'Are you sure you want to delete this strategy?',
            confirmText: 'Delete',
            cancelText: 'Cancel',
            type: 'danger'
        });

        if (!confirmed) {
            return;
        }

        try {
            await scalpingStrategiesCrud.delete(id);
            loadScalpingStrategiesTab(); // Refresh the strategies view
            showAlert('Strategy deleted successfully!');
        } catch (error) {
            console.error('Error deleting strategy:', error);
            showAlert('Error deleting strategy', 'error');
        }
    }

    // Setup function when the document is loaded
    document.addEventListener('DOMContentLoaded', () => {
        // Check if the user is logged in
        if (!isLoggedIn()) return;
        // Function to load and render strategies in tab
        const strategiesTabContainer = document.getElementById('scalping-strategies-tab-container');
        strategiesTabContainer.classList.remove('hidden');
        window.loadScalpingStrategiesTab = async function () {
            try {
                const response = await scalpingStrategiesCrud.get({
                    limit: 100,
                    sort: 'desc'
                });

                const strategiesTabList = strategiesTabContainer.querySelector('.grid');

                if (!response.results?.length) {
                    strategiesTabContainer.style.display = 'none';
                    return;
                }

                strategiesTabContainer.style.display = 'block';
                strategiesTabList.innerHTML = response.results.map(strategy => `
                    <div class="relative px-2 py-1.5 text-neutral-700 text-sm border bg-white dark:border-neutral-600 
                                rounded cursor-pointer hover:bg-blue-100 dark:hover:bg-neutral-700 transition-colors group"
                        data-strategy-id="${strategy.uid}">
                        <button onclick="deleteScalpingStrategy('${strategy.uid}')" 
                            class="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity 
                                   bg-white hover:bg-gray-100 rounded-full p-1 border shadow-sm z-10">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" 
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" 
                                stroke-linejoin="round" class="text-gray-500 hover:text-red-600">
                                <path d="M18 6 6 18"/>
                                <path d="m6 6 12 12"/>
                            </svg>
                        </button>
                        <div class="whitespace-nowrap overflow-hidden" onclick="loadScalpingStrategy(${JSON.stringify(strategy).replace(/"/g, '&quot;')})" 
                             title="${strategy.name} (${strategy?.data?.symbol})">
                            <span class="font-medium text-xs block truncate">
                                ${strategy.name} (${strategy?.data?.symbol})
                            </span>
                        </div>
                    </div>
                `).join('');

            } catch (error) {
                console.error('Error loading strategies tab:', error);
                strategiesTabContainer.style.display = 'block';
                strategiesTabContainer.querySelector('.grid').innerHTML = '<div class="col-span-full text-center text-sm text-gray-500">Failed to load strategies</div>';
            }
        }

        // Load strategies tab initially
        loadScalpingStrategiesTab();
    });
</script>