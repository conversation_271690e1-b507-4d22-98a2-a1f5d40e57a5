<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Trading Knowledge Quiz | AI Bull</title>
    <meta name="description" content="Test your knowledge of Stock and Option trading with our interactive quizzes." />
    <link rel="canonical" href="https://theaibull.com/quiz" />
    {% include 'blocks/head.html' %}
</head>

<body class="bg-gray-100 dark:bg-gray-900 dark:text-gray-100">
    {% include 'blocks/common.html' %}
    {%include 'blocks/onboarding.html' %}
    <div class="rhs-block duration-300 transition-width relative pb-[62px] xl:pb-0"
        style="background-image:radial-gradient(circle at 100% 0%, #fff5d7, #f0fdff, #f5f5f5, #f8f8f8, #f3f3f3, #f5f5f500, #e2e7eb)">
        <div class="xl:flex min-h-screen">
            <!-- Quiz Container -->
            <div class="container mx-auto px-4 py-8 max-w-7xl">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-800 dark:text-white">Trading Knowledge Quiz</h1>
                    <p class="text-gray-600 dark:text-gray-300 mt-2">Test your knowledge and improve your trading skills
                        with our interactive quizzes.</p>
                </div>

                <!-- Main Content -->
                <div class="flex flex-col md:flex-row gap-6">
                    <!-- Topics Sidebar -->
                    <div class="w-full md:w-1/4">
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Quiz Topics</h2>
                            <div id="quiz-topics" class="space-y-2">
                                <!-- Topics will be loaded here via JavaScript -->
                                <div class="animate-pulse">
                                    <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                                    <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                                    <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                                    <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                                    <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quiz Content Area -->
                    <div class="w-full md:w-3/4">
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                            <!-- Welcome Screen (default view) -->
                            <div id="welcome-screen">
                                <div class="text-center py-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 mx-auto text-blue-500 mb-4"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">Welcome to the
                                        Trading Knowledge Quiz</h2>
                                    <p class="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                                        Select a topic from the sidebar to start a quiz. Each quiz contains 5 questions
                                        to test your knowledge.
                                        After completing the quiz, you'll receive an AI-powered analysis of your
                                        performance.
                                    </p>
                                    <p class="text-gray-600 dark:text-gray-300 mb-6 italic">
                                        "The more you learn, the more you earn." - Warren Buffett
                                    </p>
                                </div>
                            </div>

                            <!-- Quiz Questions Screen (hidden initially) -->
                            <div id="quiz-screen" class="hidden">
                                <div class="mb-6 flex justify-between items-center">
                                    <h2 id="quiz-topic-title" class="text-2xl font-bold text-gray-800 dark:text-white">
                                    </h2>
                                    <div class="text-sm font-medium text-gray-600 dark:text-gray-300">
                                        Question <span id="current-question">1</span> of <span
                                            id="total-questions">5</span>
                                    </div>
                                </div>

                                <div class="mb-6">
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                        <div id="progress-bar" class="bg-blue-600 h-2.5 rounded-full"
                                            style="width: 20%"></div>
                                    </div>
                                </div>

                                <div id="question-container" class="mb-8">
                                    <h3 id="question-text"
                                        class="text-xl font-medium text-gray-800 dark:text-white mb-4"></h3>
                                    <div id="options-container" class="space-y-3">
                                        <!-- Options will be loaded here via JavaScript -->
                                    </div>
                                    <div id="selection-reminder"
                                        class="mt-4 text-amber-600 dark:text-amber-400 text-sm font-medium hidden">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1"
                                            viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd"
                                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                                clip-rule="evenodd" />
                                        </svg>
                                        Please select an option to continue
                                    </div>
                                </div>

                                <div class="flex justify-between">
                                    <button id="prev-btn"
                                        class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                                        Previous
                                    </button>
                                    <button id="next-btn"
                                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                        Next
                                    </button>
                                </div>
                            </div>

                            <!-- Results Screen (hidden initially) -->
                            <div id="results-screen" class="hidden">
                                <div class="text-center py-6">
                                    <div id="score-circle"
                                        class="w-32 h-32 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl font-bold bg-blue-100 text-blue-600">
                                        <!-- Score will be displayed here -->
                                    </div>
                                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">Quiz Results</h2>
                                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                                        You've completed the <span id="completed-topic" class="font-medium"></span>
                                        quiz!
                                    </p>
                                </div>

                                <div class="mb-8">
                                    <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">AI Analysis
                                    </h3>
                                    <div id="ai-analysis"
                                        class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg text-gray-700 dark:text-gray-300">
                                        <!-- AI analysis will be displayed here -->
                                    </div>
                                </div>

                                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button id="review-answers-btn"
                                        class="px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                                        Review Answers
                                    </button>
                                    <button id="retake-quiz-btn"
                                        class="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                        Retake Quiz
                                    </button>
                                    <button id="new-topic-btn"
                                        class="px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                                        Try Another Topic
                                    </button>
                                </div>
                            </div>

                            <!-- Answer Review Screen (hidden initially) -->
                            <div id="review-screen" class="hidden">
                                <div class="mb-6 flex justify-between items-center">
                                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Review Answers</h2>
                                    <button id="back-to-results-btn"
                                        class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                                        Back to Results
                                    </button>
                                </div>
                                <div id="review-container" class="space-y-6">
                                    <!-- Review content will be loaded here via JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% include 'blocks/footer.html' %}

    {% include 'games/crud.html' %}
</body>

</html>