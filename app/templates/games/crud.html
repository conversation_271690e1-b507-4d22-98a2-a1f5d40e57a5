<script>
    /**
     * Trading Quiz Application
     *
     * This script handles the functionality for the trading quiz feature.
     * It manages quiz topics, questions, user interactions, and results.
     */
    // DOM Elements
    const quizTopicsContainer = document.getElementById('quiz-topics');
    const welcomeScreen = document.getElementById('welcome-screen');
    const quizScreen = document.getElementById('quiz-screen');
    const resultsScreen = document.getElementById('results-screen');
    const reviewScreen = document.getElementById('review-screen');

    const quizTopicTitle = document.getElementById('quiz-topic-title');
    const currentQuestionSpan = document.getElementById('current-question');
    const totalQuestionsSpan = document.getElementById('total-questions');
    const progressBar = document.getElementById('progress-bar');

    const questionText = document.getElementById('question-text');
    const optionsContainer = document.getElementById('options-container');

    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');

    const scoreCircle = document.getElementById('score-circle');
    const completedTopic = document.getElementById('completed-topic');
    const aiAnalysis = document.getElementById('ai-analysis');

    const reviewAnswersBtn = document.getElementById('review-answers-btn');
    const retakeQuizBtn = document.getElementById('retake-quiz-btn');
    const newTopicBtn = document.getElementById('new-topic-btn');
    const backToResultsBtn = document.getElementById('back-to-results-btn');
    const reviewContainer = document.getElementById('review-container');

    // Quiz State
    let currentTopic = null;
    let currentQuestionIndex = 0;
    let userAnswers = [];
    let quizStartTime = null;
    let quizTopics = {{ topics | tojson | safe }};
    let quizQuestions = [];

    // Initialize the quiz
    async function initQuiz() {
        // Set up event listeners
        setupEventListeners();

        if (quizTopics) {
            renderTopics();
        } else {
            console.error('Quiz topics not found');
        }

    }

    // Fetch questions for a specific topic
    async function fetchQuestions(topicId) {
        try {
            // Show loading state
            quizScreen.classList.add('opacity-50', 'pointer-events-none');
            const loadingIndicator = document.createElement('div');
            loadingIndicator.id = 'quiz-loading';
            loadingIndicator.className = 'fixed inset-0 flex items-center justify-center z-50';
            loadingIndicator.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg flex items-center">
                    <svg class="animate-spin h-6 w-6 text-blue-600 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-gray-800 dark:text-gray-200">Loading questions...</span>
                </div>
            `;
            document.body.appendChild(loadingIndicator);

            const response = await fetch(`/quiz/questions?topic_id=${topicId}`);
            if (!response.ok) {
                throw new Error('Failed to fetch questions');
            }

            const data = await response.json();

            // Remove loading indicator
            document.getElementById('quiz-loading')?.remove();
            quizScreen.classList.remove('opacity-50', 'pointer-events-none');

            return data;
        } catch (error) {
            console.error('Error fetching questions:', error);
            // Remove loading indicator
            document.getElementById('quiz-loading')?.remove();
            quizScreen.classList.remove('opacity-50', 'pointer-events-none');
            throw error;
        }
    }

    // Render the list of quiz topics
    function renderTopics() {
        quizTopicsContainer.innerHTML = '';

        quizTopics.forEach(topic => {
            const topicElement = document.createElement('button');
            topicElement.className = 'w-full text-left px-4 py-3 rounded-lg transition-colors hover:bg-blue-50 dark:hover:bg-blue-900/30 focus:outline-none';
            topicElement.id = `topic-${topic.id}`;
            topicElement.dataset.topicId = topic.id;

            topicElement.innerHTML = `
                <div class="font-medium text-gray-800 dark:text-white">${topic.title}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">${topic.description}</div>
            `;

            topicElement.addEventListener('click', () => {
                // Remove active class from all topics
                document.querySelectorAll('#quiz-topics button').forEach(btn => {
                    btn.classList.remove('bg-blue-50', 'dark:bg-blue-900/30', 'border-blue-500', 'border-2');
                    btn.classList.add('border', 'border-transparent');
                });

                // Add active class to clicked topic
                topicElement.classList.add('bg-blue-50', 'dark:bg-blue-900/30', 'border-blue-500', 'border-2');
                topicElement.classList.remove('border', 'border-transparent');

                startQuiz(topic.id);
            });

            // Add transparent border by default (to prevent layout shift when selected)
            topicElement.classList.add('border', 'border-transparent');

            quizTopicsContainer.appendChild(topicElement);
        });
    }

    // Set up event listeners
    function setupEventListeners() {
        prevBtn.addEventListener('click', showPreviousQuestion);
        nextBtn.addEventListener('click', handleNextButtonClick);

        reviewAnswersBtn.addEventListener('click', showReviewScreen);
        retakeQuizBtn.addEventListener('click', retakeQuiz);
        newTopicBtn.addEventListener('click', showTopicSelection);
        backToResultsBtn.addEventListener('click', showResultsScreen);
    }

    // Start a quiz for a specific topic
    async function startQuiz(topicId) {
        try {
            // Find the topic in our local data
            const topicInfo = quizTopics.find(topic => topic.id === topicId);
            if (!topicInfo) {
                throw new Error('Topic not found');
            }

            // Highlight the selected topic in the sidebar
            document.querySelectorAll('#quiz-topics button').forEach(btn => {
                btn.classList.remove('bg-blue-50', 'dark:bg-blue-900/30', 'border-blue-500', 'border-2');
                btn.classList.add('border', 'border-transparent');
            });

            const selectedTopicBtn = document.getElementById(`topic-${topicId}`);
            if (selectedTopicBtn) {
                selectedTopicBtn.classList.add('bg-blue-50', 'dark:bg-blue-900/30', 'border-blue-500', 'border-2');
                selectedTopicBtn.classList.remove('border', 'border-transparent');
            }

            // Fetch questions from the API
            const data = await fetchQuestions(topicId);

            // Set current topic and questions
            currentTopic = data.topic;
            quizQuestions = data.questions;

            // Reset quiz state
            currentQuestionIndex = 0;
            userAnswers = Array(quizQuestions.length).fill(null);
            quizStartTime = new Date();

            // Update UI
            quizTopicTitle.textContent = currentTopic.title;
            totalQuestionsSpan.textContent = quizQuestions.length;

            // Reset Next button to disabled state initially
            nextBtn.disabled = true;
            nextBtn.classList.add('opacity-50', 'cursor-not-allowed', 'bg-blue-400');
            nextBtn.classList.remove('hover:bg-blue-700', 'bg-blue-600');

            showQuizScreen();
            renderQuestion();
        } catch (error) {
            console.error('Error starting quiz:', error);
            alert('Failed to load quiz questions. Please try again.');
        }
    }

    // Render the current question
    function renderQuestion() {
        const question = quizQuestions[currentQuestionIndex];

        // Update question number and progress bar
        currentQuestionSpan.textContent = currentQuestionIndex + 1;
        const progressPercentage = ((currentQuestionIndex + 1) / quizQuestions.length) * 100;
        progressBar.style.width = `${progressPercentage}%`;

        // Update question text
        questionText.textContent = question.question;

        // Render options
        optionsContainer.innerHTML = '';
        question.options.forEach((option, index) => {
            const optionElement = document.createElement('div');
            const isSelected = userAnswers[currentQuestionIndex] === index;

            optionElement.className = `p-3 rounded-lg border ${isSelected ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30' : 'border-gray-200 dark:border-gray-700'} hover:border-blue-500 cursor-pointer transition-colors`;

            optionElement.innerHTML = `
                <div class="flex items-center">
                    <div class="w-6 h-6 flex-shrink-0 mr-2 rounded-full border-2 ${isSelected ? 'border-blue-500 bg-blue-500' : 'border-gray-300 dark:border-gray-600'} flex items-center justify-center">
                        ${isSelected ? '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>' : ''}
                    </div>
                    <span class="text-gray-800 dark:text-gray-200">${option}</span>
                </div>
            `;

            optionElement.addEventListener('click', () => selectOption(index));
            optionsContainer.appendChild(optionElement);
        });

        // Update button states
        prevBtn.disabled = currentQuestionIndex === 0;
        prevBtn.classList.toggle('opacity-50', currentQuestionIndex === 0);

        // Check if an option is selected for the current question
        const hasSelectedOption = userAnswers[currentQuestionIndex] !== null;

        // Disable next button if no option is selected
        nextBtn.disabled = !hasSelectedOption;
        nextBtn.classList.toggle('opacity-50', !hasSelectedOption);
        nextBtn.classList.toggle('cursor-not-allowed', !hasSelectedOption);
        nextBtn.classList.toggle('hover:bg-blue-700', hasSelectedOption);
        nextBtn.classList.toggle('bg-blue-400', !hasSelectedOption);
        nextBtn.classList.toggle('bg-blue-600', hasSelectedOption);

        // Show/hide selection reminder
        const selectionReminder = document.getElementById('selection-reminder');
        if (selectionReminder) {
            selectionReminder.classList.toggle('hidden', hasSelectedOption);
        }

        if (currentQuestionIndex === quizQuestions.length - 1) {
            nextBtn.textContent = 'Submit Quiz';
        } else {
            nextBtn.textContent = 'Next';
        }
    }

    // Handle option selection
    function selectOption(optionIndex) {
        userAnswers[currentQuestionIndex] = optionIndex;
        renderQuestion();
    }

    // Show the previous question
    function showPreviousQuestion() {
        if (currentQuestionIndex > 0) {
            currentQuestionIndex--;
            renderQuestion();
        }
    }

    // Handle next button click
    function handleNextButtonClick() {
        // Only proceed if an option is selected
        if (userAnswers[currentQuestionIndex] === null) {
            return; // Do nothing if no option is selected
        }

        if (currentQuestionIndex === quizQuestions.length - 1) {
            // This is the last question, submit the quiz
            submitQuiz();
        } else {
            // Go to the next question
            currentQuestionIndex++;
            renderQuestion();
        }
    }

    // Submit the quiz and show results
    function submitQuiz() {
        // Check if all questions have been answered
        const allQuestionsAnswered = userAnswers.every(answer => answer !== null);
        if (!allQuestionsAnswered) {
            // If not all questions are answered, don't submit
            alert("Please answer all questions before submitting the quiz.");
            return;
        }

        const quizEndTime = new Date();
        const quizDuration = Math.floor((quizEndTime - quizStartTime) / 1000); // in seconds

        // Calculate score
        let correctAnswers = 0;
        userAnswers.forEach((answer, index) => {
            if (answer === quizQuestions[index].correctAnswer) {
                correctAnswers++;
            }
        });

        const score = Math.round((correctAnswers / quizQuestions.length) * 100);

        // Update results screen
        scoreCircle.textContent = `${score}%`;
        completedTopic.textContent = currentTopic.title;

        // Set background color based on score
        if (score >= 80) {
            scoreCircle.className = 'w-32 h-32 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl font-bold bg-green-100 text-green-600';
        } else if (score >= 60) {
            scoreCircle.className = 'w-32 h-32 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl font-bold bg-blue-100 text-blue-600';
        } else {
            scoreCircle.className = 'w-32 h-32 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl font-bold bg-red-100 text-red-600';
        }

        // Generate analysis (simulated for now)
        generateAnalysis(score, correctAnswers, quizDuration);

        showResultsScreen();
    }

    // Generate analysis of quiz results
    function generateAnalysis(score, correctAnswers, quizDuration) {
        // For now, we'll use static responses based on the score

        let analysis = '';

        if (score >= 80) {
            analysis = `
                <p class="mb-3"><strong>Excellent performance!</strong> You answered ${correctAnswers} out of ${quizQuestions.length} questions correctly in ${formatTime(quizDuration)}.</p>
                <p class="mb-3">Your understanding of ${currentTopic.title.toLowerCase()} concepts is strong. You demonstrate a solid grasp of the key principles in this area.</p>
                <p>To further enhance your knowledge, consider exploring advanced topics in ${currentTopic.title.toLowerCase()} and applying these concepts in real-world trading scenarios.</p>
            `;
        } else if (score >= 60) {
            analysis = `
                <p class="mb-3"><strong>Good effort!</strong> You answered ${correctAnswers} out of ${quizQuestions.length} questions correctly in ${formatTime(quizDuration)}.</p>
                <p class="mb-3">You have a decent understanding of ${currentTopic.title.toLowerCase()}, but there's room for improvement in some areas.</p>
                <p>Consider reviewing the questions you missed and strengthening your knowledge of those specific concepts. Practice applying these principles in different market scenarios.</p>
            `;
        } else {
            analysis = `
                <p class="mb-3"><strong>Keep learning!</strong> You answered ${correctAnswers} out of ${quizQuestions.length} questions correctly in ${formatTime(quizDuration)}.</p>
                <p class="mb-3">It seems you might benefit from additional study in ${currentTopic.title.toLowerCase()}. These concepts are important for successful trading.</p>
                <p>I recommend reviewing the fundamental principles of ${currentTopic.title.toLowerCase()} and perhaps exploring some educational resources before retaking the quiz.</p>
            `;
        }

        aiAnalysis.innerHTML = analysis;
    }

    // Format time in seconds to minutes and seconds
    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;

        if (minutes === 0) {
            return `${remainingSeconds} seconds`;
        } else if (minutes === 1) {
            return `1 minute and ${remainingSeconds} seconds`;
        } else {
            return `${minutes} minutes and ${remainingSeconds} seconds`;
        }
    }

    // Show the review screen with all questions and answers
    function showReviewScreen() {
        reviewContainer.innerHTML = '';

        quizQuestions.forEach((question, index) => {
            const userAnswer = userAnswers[index];
            const isCorrect = userAnswer === question.correctAnswer;

            const questionElement = document.createElement('div');
            questionElement.className = 'p-4 rounded-lg border border-gray-200 dark:border-gray-700';

            let optionsHtml = '';
            question.options.forEach((option, optionIndex) => {
                const isUserSelection = optionIndex === userAnswer;
                const isCorrectAnswer = optionIndex === question.correctAnswer;

                let optionClass = 'p-3 rounded-lg border border-gray-200 dark:border-gray-700 mb-2';

                if (isUserSelection && isCorrectAnswer) {
                    optionClass = 'p-3 rounded-lg border border-green-500 bg-green-50 dark:bg-green-900/30 mb-2';
                } else if (isUserSelection && !isCorrectAnswer) {
                    optionClass = 'p-3 rounded-lg border border-red-500 bg-red-50 dark:bg-red-900/30 mb-2';
                } else if (isCorrectAnswer) {
                    optionClass = 'p-3 rounded-lg border border-green-500 bg-green-50 dark:bg-green-900/30 mb-2';
                }

                optionsHtml += `
                    <div class="${optionClass}">
                        <div class="flex items-center">
                            <span class="text-gray-800 dark:text-gray-200">${option}</span>
                            ${isCorrectAnswer ? '<span class="ml-auto text-green-600"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg></span>' : ''}
                            ${isUserSelection && !isCorrectAnswer ? '<span class="ml-auto text-red-600"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></span>' : ''}
                        </div>
                    </div>
                `;
            });

            questionElement.innerHTML = `
                <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-3">
                    ${index + 1}. ${question.question}
                </h3>
                <div class="mb-4">
                    ${optionsHtml}
                </div>
                <div class="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg text-gray-700 dark:text-gray-300">
                    <strong>Explanation:</strong> ${question.explanation}
                </div>
            `;

            reviewContainer.appendChild(questionElement);
        });

        hideAllScreens();
        reviewScreen.classList.remove('hidden');
    }

    // Retake the current quiz
    function retakeQuiz() {
        if (currentTopic && currentTopic.id) {
            startQuiz(currentTopic.id);
        } else {
            // Fallback in case currentTopic is not properly set
            showTopicSelection();
            alert("Please select a topic to start the quiz again.");
        }
    }

    // Show topic selection screen
    function showTopicSelection() {
        hideAllScreens();
        welcomeScreen.classList.remove('hidden');
    }

    // Show the quiz screen
    function showQuizScreen() {
        hideAllScreens();
        quizScreen.classList.remove('hidden');
    }

    // Show the results screen
    function showResultsScreen() {
        hideAllScreens();
        resultsScreen.classList.remove('hidden');
    }

    // Hide all screens
    function hideAllScreens() {
        welcomeScreen.classList.add('hidden');
        quizScreen.classList.add('hidden');
        resultsScreen.classList.add('hidden');
        reviewScreen.classList.add('hidden');
    }

    // Initialize the quiz when the page loads
    initQuiz();
</script>