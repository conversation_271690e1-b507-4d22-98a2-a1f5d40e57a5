<script>
    // Elements
    const calculatorList = document.getElementById('calculator-list');
    const welcomeScreen = document.getElementById('welcome-screen');
    const calculatorScreen = document.getElementById('calculator-screen');
    const calculatorTitle = document.getElementById('calculator-title');
    const calculatorContainer = document.getElementById('calculator-container');

    // Map calculator IDs to new URL slugs
    const calculatorSlugMap = {
        sip: 'sip-calculator',
        emi: 'emi-calculator',
        'compound-interest': 'compound-interest-calculator',
        fd: 'fd-calculator',
        rd: 'rd-calculator',
    };

    // Reverse map for lookup
    const slugToCalculatorId = Object.fromEntries(
        Object.entries(calculatorSlugMap).map(([k, v]) => [v, k])
    );

    // Utility function to format numbers with Indian Rupee
    const formatINR = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 2,
        }).format(amount);
    };

    // Common input field template
    const createInputField = (id, label, type = 'number', step = null, required = true, min = null) => `
        <div>
            <label for="${id}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">${label}</label>
            <input
                type="${type}"
                id="${id}"
                ${step ? `step="${step}"` : ''}
                ${min ? `min="${min}"` : ''}
                class="block w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 ease-in-out text-gray-900 dark:text-gray-100"
                ${required ? 'required' : ''}
            >
        </div>
    `;

    // Common form template
    const createForm = (id, fields) => `
        <form id="${id}-form" class="space-y-6 px-4">
            ${fields.join('')}
            <button
                type="submit"
                class="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg shadow-md hover:from-blue-700 hover:to-indigo-700 hover:scale-105 transition-all duration-300 ease-in-out font-semibold"
            >
                Calculate
            </button>
        </form>
        <div id="${id}-additional" class="mt-8"></div>
    `;

    const createToggle = (id, defaultType = 'sip') => `
        <div class="flex space-x-4 mb-6">
            <button id="${id}-sip-toggle" class="px-4 py-2 rounded-full font-medium text-sm transition-colors ${defaultType === 'sip' ? 'bg-green-500 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'}">Monthly SIP</button>
            <button id="${id}-onetime-toggle" class="px-4 py-2 rounded-full font-medium text-sm transition-colors ${defaultType === 'onetime' ? 'bg-green-500 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'}">One-Time</button>
        </div>
    `;

    const createAmountSlider = (id, label, min, max, step, defaultValue) => `
        <div class="mb-8">
            <div class="flex items-center justify-between mb-2">
                <label class="block text-lg font-medium text-gray-800 dark:text-gray-300">${label}</label>
                <span id="${id}-display" class="text-lg font-semibold text-gray-800 dark:text-white">${formatINR(defaultValue)} per month</span>
            </div>
            <input
                type="range"
                id="${id}-slider"
                min="${min}"
                max="${max}"
                step="${step}"
                value="${defaultValue}"
                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 accent-green-500"
            >
        </div>
    `;

    const createTimePeriodButtons = (id) => `
        <div class="mb-8">
            <label class="block text-lg font-medium text-gray-800 dark:text-gray-300 mb-2">Over the past</label>
            <div class="flex space-x-4">
                <button id="${id}-1year" class="px-4 py-2 rounded-full font-medium text-sm bg-green-500 text-white transition-colors">1 year</button>
                <button id="${id}-3year" class="px-4 py-2 rounded-full font-medium text-sm bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">3 years</button>
                <button id="${id}-5year" class="px-4 py-2 rounded-full font-medium text-sm bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">5 years</button>
            </div>
        </div>
    `;

    const createResultDisplay = (id, fields) => `
        <div id="${id}-result" class="mt-6">
            ${fields.map(field => `
                <p class="text-gray-600 dark:text-gray-300 text-lg mb-2">${field.label} <span id="${field.id}" class="font-semibold text-gray-800 dark:text-white"></span></p>
            `).join('')}
        </div>
    `;

    // Highlight selected calculator
    const highlightCalculator = (calculatorId) => {
        const links = calculatorList.querySelectorAll('.calculator-link');
        links.forEach(link => {
            link.classList.remove('bg-blue-500', 'text-white');
            link.classList.add('bg-gray-100', 'dark:bg-gray-700', 'text-gray-800', 'dark:text-white');
        });
        const selectedLink = calculatorList.querySelector(`[data-calculator="${calculatorId}"]`);
        if (selectedLink) {
            selectedLink.classList.remove('bg-gray-100', 'dark:bg-gray-700', 'text-gray-800', 'dark:text-white');
            selectedLink.classList.add('bg-blue-500', 'text-white');
        }
    };

    // Calculator configurations
    const calculators = {
        sip: {
            title: 'SIP Calculator',
            form: createForm('sip', [
                createInputField('min-amount', 'Minimum Investment Amount (₹)', 'number', null, true, 100),
                createInputField('interest-rate', 'Expected Return (% p.a.)', 'number', '0.01', true),
            ]),
            calculate: (amount, expectedReturn, tenure, isSIP) => {
                try {
                    const monthlyRate = expectedReturn / 1200;
                    const months = tenure * 12;
                    let investedAmount, futureValue;

                    if (isNaN(amount) || isNaN(expectedReturn) || isNaN(tenure)) {
                        throw new Error('Invalid calculation inputs');
                    }

                    if (isSIP) {
                        const monthlyInvestment = amount;
                        futureValue = monthlyInvestment * ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate) * (1 + monthlyRate);
                        investedAmount = monthlyInvestment * months;
                    } else {
                        futureValue = amount * Math.pow(1 + monthlyRate, months);
                        investedAmount = amount;
                    }

                    const percentageGain = ((futureValue - investedAmount) / investedAmount * 100).toFixed(2);

                    const resultDiv = document.getElementById('sip-result');
                    if (resultDiv) {
                        resultDiv.innerHTML = `
                            <p class="text-gray-600 dark:text-gray-300 text-lg mb-2">Total Investment of <span id="invested-amount" class="font-semibold text-gray-800 dark:text-white">${formatINR(investedAmount)}</span></p>
                            <p class="text-gray-600 dark:text-gray-300 text-lg mb-2">Would have become <span id="future-value" class="font-semibold text-gray-800 dark:text-white">${formatINR(futureValue)} (+${percentageGain}%)</span></p>
                        `;
                    }
                } catch (error) {
                    console.error('SIP Calculation Error:', error);
                    alert('Calculation failed. Please check your inputs.');
                }
            },
            renderAdditionalControls: (minAmount, expectedReturn, container) => {
                try {
                    const additionalContent = `
                        ${createToggle('sip', 'sip')}
                        ${createAmountSlider('investment-amount', 'Investment Amount', minAmount, 100000, 1000, minAmount)}
                        ${createTimePeriodButtons('sip')}
                        ${createResultDisplay('sip', [
                        { id: 'invested-amount', label: 'Total Investment of' },
                        { id: 'future-value', label: 'Would have become' },
                    ])}
                    `;
                    const additionalDiv = container.querySelector('#sip-additional');
                    if (additionalDiv) {
                        additionalDiv.innerHTML = additionalContent;
                    } else {
                        console.error('Additional div not found');
                    }
                } catch (error) {
                    console.error('Render Additional Controls Error:', error);
                }
            },
        },
        emi: {
            title: 'EMI Calculator',
            form: createForm('emi', [
                createInputField('loan-amount', 'Loan Amount (₹)'),
                createInputField('interest-rate', 'Interest Rate (% p.a.)', 'number', '0.01'),
                createInputField('loan-tenure', 'Loan Tenure (Years)'),
            ]),
            calculate: (form) => {
                try {
                    const loanAmount = parseFloat(form['loan-amount'].value);
                    const interestRate = parseFloat(form['interest-rate'].value) / 1200;
                    const loanTenure = parseFloat(form['loan-tenure'].value) * 12;

                    if (isNaN(loanAmount) || isNaN(interestRate) || isNaN(loanTenure)) {
                        throw new Error('Invalid input values');
                    }

                    const emi = loanAmount * interestRate * (Math.pow(1 + interestRate, loanTenure) / (Math.pow(1 + interestRate, loanTenure) - 1));
                    const totalAmount = emi * loanTenure;
                    const totalInterest = totalAmount - loanAmount;

                    const resultDiv = document.getElementById('emi-additional');
                    resultDiv.innerHTML = `
                        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">Results</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-lg">Monthly EMI: <span class="font-semibold text-gray-900 dark:text-white">${formatINR(emi)}</span></p>
                        <p class="text-gray-600 dark:text-gray-300 text-lg">Total Interest Payable: <span class="font-semibold text-gray-900 dark:text-white">${formatINR(totalInterest)}</span></p>
                        <p class="text-gray-600 dark:text-gray-300 text-lg">Total Amount Payable: <span class="font-semibold text-gray-900 dark:text-white">${formatINR(totalAmount)}</span></p>
                    `;
                } catch (error) {
                    console.error('EMI Calculation Error:', error);
                    alert('Please enter valid input values.');
                }
            },
        },
        'compound-interest': {
            title: 'Compound Interest Calculator',
            form: createForm('compound-interest', [
                createInputField('principal', 'Principal Amount (₹)'),
                createInputField('interest-rate', 'Interest Rate (% p.a.)', 'number', '0.01'),
                createInputField('time-period', 'Time Period (Years)'),
                createInputField('compounding-frequency', 'Compounding Frequency (Per Year)', 'number'),
            ]),
            calculate: (form) => {
                try {
                    const principal = parseFloat(form['principal'].value);
                    const interestRate = parseFloat(form['interest-rate'].value) / 100;
                    const timePeriod = parseFloat(form['time-period'].value);
                    const compoundingFrequency = parseFloat(form['compounding-frequency'].value);

                    if (isNaN(principal) || isNaN(interestRate) || isNaN(timePeriod) || isNaN(compoundingFrequency)) {
                        throw new Error('Invalid input values');
                    }

                    const futureValue = principal * Math.pow(1 + interestRate / compoundingFrequency, compoundingFrequency * timePeriod);
                    const totalInterest = futureValue - principal;

                    const resultDiv = document.getElementById('compound-interest-additional');
                    resultDiv.innerHTML = `
                        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">Results</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-lg">Future Value: <span class="font-semibold text-gray-900 dark:text-white">${formatINR(futureValue)}</span></p>
                        <p class="text-gray-600 dark:text-gray-300 text-lg">Total Interest Earned: <span class="font-semibold text-gray-900 dark:text-white">${formatINR(totalInterest)}</span></p>
                    `;
                } catch (error) {
                    console.error('Compound Interest Calculation Error:', error);
                    alert('Please enter valid input values.');
                }
            },
        },
        fd: {
            title: 'Fixed Deposit Calculator',
            form: createForm('fd', [
                createInputField('principal', 'Deposit Amount (₹)'),
                createInputField('interest-rate', 'Interest Rate (% p.a.)', 'number', '0.01'),
                createInputField('tenure', 'Tenure (Years)'),
            ]),
            calculate: (form) => {
                try {
                    const principal = parseFloat(form['principal'].value);
                    const interestRate = parseFloat(form['interest-rate'].value) / 100;
                    const tenure = parseFloat(form['tenure'].value);

                    if (isNaN(principal) || isNaN(interestRate) || isNaN(tenure)) {
                        throw new Error('Invalid input values');
                    }

                    const maturityAmount = principal * Math.pow(1 + interestRate, tenure);
                    const interestEarned = maturityAmount - principal;

                    const resultDiv = document.getElementById('fd-additional');
                    resultDiv.innerHTML = `
                        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">Results</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-lg">Maturity Amount: <span class="font-semibold text-gray-900 dark:text-white">${formatINR(maturityAmount)}</span></p>
                        <p class="text-gray-600 dark:text-gray-300 text-lg">Interest Earned: <span class="font-semibold text-gray-900 dark:text-white">${formatINR(interestEarned)}</span></p>
                    `;
                } catch (error) {
                    console.error('Fixed Deposit Calculation Error:', error);
                    alert('Please enter valid input values.');
                }
            },
        },
        rd: {
            title: 'Recurring Deposit Calculator',
            form: createForm('rd', [
                createInputField('monthly-deposit', 'Monthly Deposit (₹)'),
                createInputField('interest-rate', 'Interest Rate (% p.a.)', 'number', '0.01'),
                createInputField('tenure', 'Tenure (Years)'),
            ]),
            calculate: (form) => {
                try {
                    const monthlyDeposit = parseFloat(form['monthly-deposit'].value);
                    const interestRate = parseFloat(form['interest-rate'].value) / 100;
                    const tenure = parseFloat(form['tenure'].value);
                    const months = tenure * 12;

                    if (isNaN(monthlyDeposit) || isNaN(interestRate) || isNaN(tenure)) {
                        throw new Error('Invalid input values');
                    }

                    let maturityAmount = 0;
                    for (let i = 1; i <= months; i++) {
                        maturityAmount += monthlyDeposit * Math.pow(1 + interestRate / 4, Math.floor((months - i + 1) / 3));
                    }
                    const totalDeposited = monthlyDeposit * months;
                    const interestEarned = maturityAmount - totalDeposited;

                    const resultDiv = document.getElementById('rd-additional');
                    resultDiv.innerHTML = `
                        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">Results</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-lg">Maturity Amount: <span class="font-semibold text-gray-900 dark:text-white">${formatINR(maturityAmount)}</span></p>
                        <p class="text-gray-600 dark:text-gray-300 text-lg">Total Deposited: <span class="font-semibold text-gray-900 dark:text-white">${formatINR(totalDeposited)}</span></p>
                        <p class="text-gray-600 dark:text-gray-300 text-lg">Interest Earned: <span class="font-semibold text-gray-900 dark:text-white">${formatINR(interestEarned)}</span></p>
                    `;
                } catch (error) {
                    console.error('Recurring Deposit Calculation Error:', error);
                    alert('Please enter valid input values.');
                }
            },
        },
    };

    // Handle calculator selection
    calculatorList.addEventListener('click', (e) => {
        e.preventDefault();
        const target = e.target.closest('a');
        if (!target) return;

        const calculatorId = target.getAttribute('data-calculator');
        const calculator = calculators[calculatorId];

        if (calculator) {
            // Update URL without reloading (use new slug)
            const slug = calculatorSlugMap[calculatorId] || calculatorId;
            history.pushState({ calculatorId }, '', `/${slug}`);

            // Highlight selected calculator
            highlightCalculator(calculatorId);

            // Show calculator content
            welcomeScreen.classList.add('hidden');
            calculatorScreen.classList.remove('hidden');
            calculatorTitle.textContent = calculator.title;
            calculatorContainer.innerHTML = calculator.form;

            // Handle form submission for calculators
            const form = document.getElementById(`${calculatorId}-form`);
            if (form) {
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    if (calculatorId === 'sip') {
                        try {
                            let minAmount = parseFloat(form['min-amount'].value) || 500;
                            let expectedReturn = parseFloat(form['interest-rate'].value) || 12;
                            let amount = minAmount;
                            let isSIP = true;
                            let tenure = 1;

                            form.classList.add('hidden');
                            calculator.renderAdditionalControls(minAmount, expectedReturn, calculatorContainer);
                            calculator.calculate(amount, expectedReturn, tenure, isSIP);

                            // Setup event listeners for additional controls
                            const amountSlider = document.getElementById('investment-amount-slider');
                            const amountDisplay = document.getElementById('investment-amount-display');
                            if (!amountSlider || !amountDisplay) {
                                throw new Error('Slider or display element not found');
                            }

                            amountSlider.addEventListener('input', () => {
                                amount = parseFloat(amountSlider.value);
                                amountDisplay.textContent = `${formatINR(amount)} ${isSIP ? 'per month' : ''}`;
                                calculator.calculate(amount, expectedReturn, tenure, isSIP);
                            });

                            const sipToggle = document.getElementById('sip-sip-toggle');
                            const onetimeToggle = document.getElementById('sip-onetime-toggle');
                            if (sipToggle && onetimeToggle) {
                                sipToggle.addEventListener('click', () => {
                                    isSIP = true;
                                    sipToggle.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-600', 'dark:text-gray-300');
                                    sipToggle.classList.add('bg-green-500', 'text-white');
                                    onetimeToggle.classList.remove('bg-green-500', 'text-white');
                                    onetimeToggle.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-600', 'dark:text-gray-300');
                                    amountDisplay.textContent = `${formatINR(amount)} per month`;
                                    calculator.calculate(amount, expectedReturn, tenure, isSIP);
                                });
                                onetimeToggle.addEventListener('click', () => {
                                    isSIP = false;
                                    onetimeToggle.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-600', 'dark:text-gray-300');
                                    onetimeToggle.classList.add('bg-green-500', 'text-white');
                                    sipToggle.classList.remove('bg-green-500', 'text-white');
                                    sipToggle.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-600', 'dark:text-gray-300');
                                    amountDisplay.textContent = `${formatINR(amount)}`;
                                    calculator.calculate(amount, expectedReturn, tenure, isSIP);
                                });
                            }

                            const timeButtons = [
                                document.getElementById('sip-1year'),
                                document.getElementById('sip-3year'),
                                document.getElementById('sip-5year'),
                            ];
                            timeButtons.forEach((btn, index) => {
                                if (btn) {
                                    const years = [1, 3, 5][index];
                                    btn.addEventListener('click', (e) => {
                                        e.preventDefault();
                                        tenure = years;
                                        timeButtons.forEach(b => {
                                            if (b) {
                                                b.classList.remove('bg-green-500', 'text-white');
                                                b.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-600', 'dark:text-gray-300');
                                            }
                                        });
                                        btn.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-600', 'dark:text-gray-300');
                                        btn.classList.add('bg-green-500', 'text-white');
                                        calculator.calculate(amount, expectedReturn, tenure, isSIP);
                                    });
                                }
                            });
                        } catch (error) {
                            console.error('SIP Form Submission Error:', error);
                            alert('An error occurred. Please try again.');
                        }
                    } else {
                        calculator.calculate(form);
                    }
                });
            }
        }
    });

    document.addEventListener('DOMContentLoaded', () => {
        // Handle browser back/forward navigation
        window.addEventListener('popstate', (event) => {
            const calculatorId = event.state?.calculatorId;
            if (calculatorId && calculators[calculatorId]) {
                highlightCalculator(calculatorId);
                welcomeScreen.classList.add('hidden');
                calculatorScreen.classList.remove('hidden');
                calculatorTitle.textContent = calculators[calculatorId].title;
                calculatorContainer.innerHTML = calculators[calculatorId].form;

                const form = document.getElementById(`${calculatorId}-form`);
                if (form) {
                    form.addEventListener('submit', (e) => {
                        e.preventDefault();
                        calculators[calculatorId].calculate(form);
                    });
                }
            } else {
                welcomeScreen.classList.remove('hidden');
                calculatorScreen.classList.add('hidden');
                highlightCalculator(null);
            }
        });

        // Initial highlight based on URL
        const currentPath = window.location.pathname.split('/').pop();
        const calculatorId = slugToCalculatorId[currentPath] || currentPath;
        if (calculators[calculatorId]) {
            highlightCalculator(calculatorId);
            welcomeScreen.classList.add('hidden');
            calculatorScreen.classList.remove('hidden');
            calculatorTitle.textContent = calculators[calculatorId].title;
            calculatorContainer.innerHTML = calculators[calculatorId].form;

            const form = document.getElementById(`${calculatorId}-form`);
            if (form && calculatorId !== 'sip') {
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    calculators[calculatorId].calculate(form);
                });
            }
        }
    });
</script>