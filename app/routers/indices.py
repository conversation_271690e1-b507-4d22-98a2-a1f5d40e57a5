from typing import Any, Dict, List
from datetime import datetime, timedelta
import asyncio
import httpx

from fastapi import HTTPException
from async_lru import alru_cache

# Define the global markets data structure
GLOBAL_MARKETS = {
    "US": [
        "Dow Jones",
        "S&P 500",
        "NASDAQ",
        "Russell 2000",
        "VIX"
    ],
    "Asia": [
        "Nikkei 225",
        "Hang Seng",
        "Shanghai Composite",
        "ASX 200",
        "Kospi"
    ],
    "India": [
        "Nifty 50",
        "Sensex",
        "Bank Nifty",
        "Nifty IT",
        "Nifty Financial Services"
    ],
    "Europe": [
        "STOXX 600",
        "DAX",
        "FTSE 100",
        "CAC 40",
        "EURO STOXX 50"
    ],
    "Crypto": [
        "Bitcoin",
        "Ether",
        "Solana",
        "XRP",
        "Binance Coin"
    ],
    "Bonds": [
        "US 10-YR",
        "US 30-YR",
        "US 5-YR",
        "US 2-YR",
        "US 3M-YR"
    ],
    "Oil": [
        "Crude Oil WTI",
        "Crude Oil Brent",
        "Natural Gas",
        "Heating Oil",
        "RBOB Gasoline"
    ],
    "Commodities": [
        "Gold",
        "Silver",
        "Copper",
        "Crude Oil WTI",
        "Crude Oil Brent",
    ],
    "Forex": [
        "EUR/USD",
        "USD/JPY",
        "GBP/USD",
        "USD/CAD",
        "AUD/USD"
    ]
}


def format_date(date: datetime) -> str:
    """Format date for API calls"""
    year = date.year
    month = str(date.month).zfill(2)
    day = str(date.day).zfill(2)
    return f"{year}-{month}-{day}"


# Direct implementation of fetch_ohlc_data without caching
async def direct_fetch_ohlc_data(symbol: str, start: str, end: str, interval: str) -> Dict[str, Any]:
    """
    Directly fetch OHLC data without using the cache.
    """
    base_url = "https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/yohlc-out"
    params = {"symbol": symbol, "start": start, "end": end, "interval": interval}
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(base_url, params=params)
            if response.status_code != 200:
                raise HTTPException(status_code=response.status_code, detail=f"Error fetching OHLC data for {symbol}")
            return response.json()
    except Exception as e:
        print(f"Error fetching OHLC data for {symbol}: {e}")
        return {}


def calculate_price_change(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate price change and percentage from OHLC data.
    """
    if not data or len(data) < 2:
        return {"currentPrice": 0, "change": 0, "percentChange": 0}

    # Get timestamps array which is already sorted low to high
    timestamps = list(data.keys())
    last_index = len(timestamps) - 1

    if len(timestamps) < 2:
        latest_data = data[timestamps[last_index]]
        return {
            "currentPrice": latest_data["c"],
            "change": 0,
            "percentChange": 0
        }

    latest_data = data[timestamps[last_index]]
    previous_data = data[timestamps[last_index - 1]]

    current_price = latest_data["c"]
    previous_price = previous_data["c"]
    change = current_price - previous_price
    percent_change = (change / previous_price) * 100

    return {
        "currentPrice": current_price,
        "change": change,
        "percentChange": percent_change
    }


async def fetch_indices_data(region: str = "Asia") -> Dict[str, Any]:
    """
    Fetch OHLC data for all indices in a specific region.
    This function is used for server-side rendering of the initial data.
    """
    # Calculate dates for the past five days
    today = datetime.now()
    five_days_ago = today - timedelta(days=5)

    # Format the start and end dates for API calls
    start_date = format_date(five_days_ago)
    end_date = format_date(today)

    # Retrieve the list of indices for the specified region
    region_indices = GLOBAL_MARKETS.get(region, [])
    if not region_indices:
        # Return an error message if no indices are found for the region
        return {"error": f"No indices found for region: {region}"}

    # Initialize a dictionary to store fetched data and tasks
    indices_data = {}
    fetch_tasks = []

    # Create asynchronous tasks for fetching data for each index
    for index_name in region_indices:
        # Schedule the direct_fetch_ohlc_data coroutine for each index
        task = asyncio.create_task(
            direct_fetch_ohlc_data(index_name, start_date, end_date, "1d")
        )
        fetch_tasks.append((index_name, task))

    # Await the completion of all tasks
    for index_name, task in fetch_tasks:
        try:
            # Await the result of each task
            data = await task
            # Calculate price change data
            price_data = calculate_price_change(data)
            indices_data[index_name] = {
                "data": data,
                "price_data": price_data
            }
        except Exception as e:
            # Handle any exceptions that occur during data fetching
            print(f"Error fetching data for {index_name}: {e}")
            indices_data[index_name] = {
                "data": None,
                "error": str(e)
            }

    # Return the collected data along with the region and timestamp formatted in ISO format
    return {
        "region": region,
        "indices": indices_data,
        "timestamp": datetime.now().strftime("%I:%M:%S %p")  # Format time as "10:59:02 AM"
    }


# Cache expiration time in seconds (30 minutes)
CACHE_EXPIRY = 30 * 60

@alru_cache(maxsize=1, ttl=CACHE_EXPIRY)
async def fetch_all_indices_data() -> Dict[str, Any]:
    """
    Fetch OHLC data for all indices across all regions with a 30-minute cache.
    This function is used for the /app route to efficiently load all market data.
    """
    # Cache expired or empty, fetch fresh data
    print("Fetching fresh indices data for all regions")
    
    # Get all regions
    regions = list(GLOBAL_MARKETS.keys())
    all_indices: Dict[str, Any] = {}

    # Create tasks for all regions
    tasks = [fetch_indices_data(region) for region in regions]

    # Run all region fetches concurrently
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Collect results
    for region, result in zip(regions, results):
        if isinstance(result, Exception):
            print(f"Error fetching data for region {region}: {result}")
            all_indices[region] = {"error": str(result)}
        else:
            all_indices[region] = result
    
    return all_indices