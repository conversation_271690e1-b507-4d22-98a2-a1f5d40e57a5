import asyncio
import traceback
from typing import Any, Dict

import websockets
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>pen<PERSON>, HTTPEx<PERSON>, Request, WebSocket
from fastapi.responses import HTMLResponse

from app.routers.auth import check_current_user, get_user_data
from app.util.ai.app_functions import find_functions, get_app_category, get_app_from_key
from app.util.ai.ws import create_session_direct, listen_to_openai, send_audio_bytes
from app.util.settings import app_settings
from app.util.templates import templates
from app.util.menu import menu

router = APIRouter()


@router.get("/live")
async def read_root(request: Request, access_token: str = <PERSON><PERSON>(None), current_user: Dict[str, Any] = Depends(check_current_user)) -> HTMLResponse:
    return templates.TemplateResponse(
        "ai-agent/index.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
        },
    )


@router.websocket("/rtmps/agent")
async def websocket_endpoint_gharpe(websocket: WebSocket, access_token: str = <PERSON><PERSON>(None), current_user: Dict[str, Any] = Depends(check_current_user)):
    await websocket.accept()

    user_data = get_user_data(access_token)  # type: ignore
    apps = user_data.get("User_Data", {}).get("apps", [])  # type: ignore

    platforms = get_app_category()

    platforms_str = ", ".join(platforms)  # type: ignore

    if len(apps) == 0 or platforms is None:  # type: ignore
        await websocket.close()
        raise HTTPException(status_code=404, detail=f"Platform not found in user's apps")

    app_data = []
    for platform in platforms:  # type: ignore
        for app in apps:  # type: ignore
            if app.get("key") == platform:  # type: ignore
                _app_data = get_app_from_key(platform)  # type: ignore
                app_data.append(_app_data)  # type: ignore

    functions_list = [f"{app['key']}:{func['function']['name']}" for app in app_data for func in app.get("functions", [])]  # type: ignore

    app_tools = find_functions(functions_list)

    async with websockets.connect(
        app_settings.OAI_REALTIME_EP, extra_headers={"Authorization": f"Bearer {app_settings.OAI_SECRET.get_secret_value()}", "OpenAI-Beta": "realtime=v1"}
    ) as websocket_openai:
        print("Connected to OpenAI WebSocket API.")
        await websocket.send_text("Connected. Please send your message.")

        openai_listener_task = asyncio.create_task(listen_to_openai(websocket_openai, websocket, current_user))

        try:
            try:
                await create_session_direct(websocket_openai=websocket_openai, app_tools=app_tools, platform=platforms_str)  # type: ignore
                while True:
                    user_text = await websocket.receive_json()
                    await send_audio_bytes(websocket_openai=websocket_openai, audio_bytes=user_text.get("audio_data"))
            except Exception as e:
                print(e)
                await websocket.send_json({"response.type.error_kong": True, "message": "pl check with bat"})
                await websocket.close()

        except Exception:
            traceback.print_exc()
            print(f"Connection closed with exception")
        finally:
            try:
                openai_listener_task.cancel()
                await websocket.close()
            except Exception:
                traceback.print_exc()
                print(f"Connection alredy closed")
