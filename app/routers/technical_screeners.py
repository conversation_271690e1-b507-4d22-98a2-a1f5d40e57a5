from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional

import pytz
from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel

from app.routers.ohlc.ta import find_patterns, find_ta
from app.util.dhan import fetch_ohlc_data, filter_ohlc_data_for_date
from app.util.intraday_rules import apply_intraday_rules
from app.util.p_l import compute_p_l
from app.util.stocks import get_all_stocks

router = APIRouter()


class RuleConfig(BaseModel):
    timeframe: str
    rules_config: str  # Now a string, not a dict


class ScreenerMultiRequest(BaseModel):
    date: str
    symbol: str  # Only one symbol per request now
    entry_rules: List[RuleConfig]
    exit_rules: Optional[List[RuleConfig]] = None
    trade: Optional[Dict[str, Any]] = None


class ScreenerOHLCRequest(BaseModel):
    symbol: str
    timeframes: Dict[str, Any]  # e.g. {"5m": {"start": "2025-08-02", "end": "2025-08-02"}, ...}


@router.get("/autocomplete")
async def autocomplete_stocks(q: str):
    """
    Returns a list of stock symbols matching the query string (case-insensitive, startswith or contains).
    """
    all_stocks = get_all_stocks()
    q_lower = q.strip().lower()
    matches = [s for s in all_stocks if q_lower in s.lower() or q_lower in all_stocks[s].get("name", "").lower()]
    # Return up to 20 matches with symbol and name
    return [{"symbol": s, "name": all_stocks[s].get("name", s)} for s in matches][:20]


async def check_rule(symbol: str, rule_config: RuleConfig, date: str) -> Dict[str, Any]:
    """
    For the given symbol and rule_config, fetch OHLC data for the specified timeframe and date,
    then evaluate the rule. Returns a dict with 'passed': bool, 'ohlc_data', and optional 'details'.
    """
    timeframe = rule_config.timeframe
    if not timeframe:
        return {"passed": False, "error": "No timeframe specified in rule_config."}
    fetch_start = (datetime.strptime(date, "%Y-%m-%d") - timedelta(days=20)).strftime("%Y-%m-%d")
    data = await fetch_ohlc_data(symbol, fetch_start, date, timeframe)
    if not data:
        return {"passed": False, "error": "No OHLC data found."}
    processed = find_patterns(data)
    processed = find_ta(processed, None)
    filtered = filter_ohlc_data_for_date(processed, date)
    # Apply intraday rules to filtered OHLC data
    matching_timestamps = apply_intraday_rules(filtered, rule_config.rules_config)
    matched_ohlc = {ts: filtered[ts] for ts in matching_timestamps}

    return {
        "passed": bool(matched_ohlc),
        "ohlc_data": filtered,  # all filtered candles for the date
        "matched": matching_timestamps,  # list of timestamps that matched all rules
        "details": f"Matched {len(matched_ohlc)} candles.",
    }


async def exec_symbol(symbol: str, request: ScreenerMultiRequest) -> List[Any]:

    # Check all entry rules for the symbol
    results = []
    for rule in request.entry_rules:
        rule_result = await check_rule(symbol, rule, request.date)
        results.append(rule_result)

    # Get all matched from results
    all_matched = set()
    all_ohlc = None
    for rule_result in results:
        if "matched" in rule_result and rule_result["matched"]:
            all_matched.update(rule_result["matched"])
        if all_ohlc is None and "ohlc_data" in rule_result:
            all_ohlc = rule_result["ohlc_data"]

    # Find P&L
    if all_ohlc and all_matched:
        trade = getattr(request, "trade", None) or getattr(request, "trades", None)
        pl_details = compute_p_l(all_ohlc, list(all_matched), trade)
        if results:
            results[0]["pl_details"] = pl_details

    return results


@router.post("/intraday-screen")
async def get_multi_ohlc_for_screener(request: ScreenerMultiRequest):
    """
    Accepts date, symbol, entry_rules (list of {timeframe, rules_config}), exit_rules, and trades.
    Returns OHLC data for the symbol and each entry_rule timeframe.
    """
    return {request.symbol: await exec_symbol(request.symbol, request)}
