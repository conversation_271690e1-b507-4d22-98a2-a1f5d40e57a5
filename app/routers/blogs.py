from typing import Any, Dict, List, Set
import httpx
from async_lru import alru_cache
from bs4 import BeautifulSoup
from fastapi import APIRouter, <PERSON><PERSON>, HTTPException, Request
import markdown

from app.util.menu import menu
from app.util.templates import templates

from .auth import get_user_data

router = APIRouter()


@alru_cache(maxsize=1, ttl=60 * 60)  # Cache the function result for 15 minutes (900 seconds)
async def get_blogs_data(limit: int = 100) -> List[Dict[str, Any]]:
    """
    Fetch blogs data from the API and return it.
    This function can be used by other modules to get blog data.

    Args:
        limit: Maximum number of blogs to fetch

    Returns:
        List of blog data dictionaries
    """
    # Hardcoded API server and app name
    MANTRA_BLOGS_SERVER = "https://api.blogs.jobpe.com"
    APP_NAME = "ai_bull"

    # Fetch the list of blogs
    blogs_url = f"{MANTRA_BLOGS_SERVER}/blogs/publish?app_name={APP_NAME}&limit={limit}"
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(blogs_url)
            response.raise_for_status()
            return response.json().get("data", [])
    except Exception as e:
        # Log the error or handle it as needed
        print(f"Error fetching blogs: {str(e)}")
        return []


@router.get("")
async def get_blogs(request: Request, access_token: str = Cookie(None)):
    """
    Fetch a list of blogs and render list.html.
    """
    # Fetch blogs data using the new function
    blogs = await get_blogs_data()

    metadata = {
        "title": "Expert Insights on AI-Powered Stock Trading | AI Bull Blog",
        "description": "Discover expert insights on AI-powered stock trading, market analysis, and investment strategies. Read our latest blogs on stock market trends, technical analysis, and trading tips.",
        "site_name": "AI Bull",
        "keywords": "AI trading, stock market blog, trading strategies, market analysis, investment insights, stock trading tips, AI Bull, technical analysis",
        "canonical_url": "https://theaibull.com/blogs",
        "og_image": "https://assets.theaibull.com/images/ai-bull.png",
        "og_image_width": "750",
        "og_image_height": "350",
        "twitter_site": "@aibull"
    }
    
    header = {
        "heading": "Explore Insights with AIBull Blog",
        "subheading": "Discover expert tips, in-depth guides, and AI-powered trading strategies to help investors excel in the stock market.",
    }

    # Render the template with the blogs list
    return templates.TemplateResponse(
        "/blogs/home.html",
        {"request": request, "blogs": blogs, **menu, "metadata": metadata, **get_user_data(access_token), "header": header},
    )


def addStylesToHTML(html_content):
    # Parse the HTML content
    soup = BeautifulSoup(html_content, "html.parser")

    # Define Tailwind classes for different elements
    style_map = {
        "h1": "text-[42px] font-[900] leading-tight mb-[0.75rem] tracking-[-0.025em] text-neutral-900 font-bricolage",
        "h2": "text-[34px] font-bold leading-[1.2] mb-[1rem] tracking-[-0.025em] text-neutral-900 pt-[1rem] font-bricolage",
        "h3": "text-[28px] font-semibold leading-[1.2] mb-[8px] tracking-[-0.025em] text-neutral-900 pt-[1rem] font-bricolage",
        "h4": "text-[24px] font-medium leading-[1.2] mb-[8px] tracking-[-0.025em] text-neutral-900 pt-[1rem] font-bricolage",
        "h5": "text-[20px] font-medium leading-[1.2] mb-[10px] tracking-[-0.025em] text-neutral-900 pt-[1rem] font-bricolage",
        "h6": "text-[18px] font-medium leading-[1.2] mb-[10px] tracking-[-0.025em] text-neutral-900 pt-[1rem] font-bricolage",
        "p": "text-[16px] font-normal leading-[1.5rem] text-neutral-600 mb-[2rem] font-poppins",
        "blockquote": "my-[20px] mx-[15px]",
        "b": "font-bold",
        "strong": "font-bold",
        "ul": "pl-[30px]",
        "li": "list-disc text-[16px] font-normal leading-[1.5rem] text-neutral-600 mb-[8px]",
        "a": "text-black font-semibold no-underline",
        "d-block": "block",
        "appsignup-widget": "hidden",
        "quotationtext": "text-[18px] font-normal leading-[1.4] text-left",
        "bg-secondary": "bg-[#506690]",
        "appsignup-widget:after": "bg-[#335eea] absolute content-[''] w-[6px] h-full top-0 left-0",
        "appsignup-widget:widget-content": "px-[36px] py-[32px] bg-[#f0f2f9] my-[20px] relative",
        "appsignup-widget .h_2": "text-[1.5rem] leading-[1.4] mb-0 font-poppins",
        "d-md-flex": "flex",
        "price-signup-btn": "hidden",
        "twitter-tweet-rendered": "mx-auto",
        "inner-blog-content-sec h4": "leading-[1.4] text-[24px]",
        "hero-section": "bg-[radial-gradient(circle_farthest-corner_at_125%_100%,#fff_19%,#fff0_41%),linear-gradient(325deg,#fff0_78%,#ffffff),linear-gradient(343deg,#fff_10%,#fff0_22%),linear-gradient(152deg,#fff,#fff0_91%),radial-gradient(circle_farthest-corner_at_0_-50%,#fff0_15%,#70cdff00_38%,#fff0_65%),repeating-linear-gradient(214deg,#fff0,#ffffff4d_25%,#fff0_71%),repeating-linear-gradient(135deg,#0000_51%,#0098f938_64%,#fff0_80%)]",
        "hero-gradient": "bg-[linear-gradient(135deg,rgb(240_255_232/_47%)_8%,rgb(232_242_249/_85%)_16%,rgb(227_213_247/_25%)_32%,rgb(231_248_255/_53%)_69%,rgb(239_255_230/_38%)_72%,rgb(229_251_255/_88%)_100%)]",
    }

    # Apply Tailwind classes to elements
    for tag, classes in style_map.items():
        for element in soup.find_all(tag):
            # Special handling for pre > code blocks
            if tag == "code" and element.parent.name == "pre":
                element["class"] = "text-sm text-gray-600 font-mono dark:text-white"
            else:
                # Preserve existing classes and add new ones
                existing_classes = element.get("class", [])
                if isinstance(existing_classes, str):
                    existing_classes = existing_classes.split()
                new_classes = classes.split()
                element["class"] = " ".join(existing_classes + new_classes)

    # Special handling for pre > code blocks
    for pre in soup.find_all("pre"):
        code = pre.find("code")
        if code:
            # If there's a language class (e.g., language-python), preserve it
            lang_class = None
            for cls in code.get("class", []):
                if cls.startswith("language-"):
                    lang_class = cls
                    break

            if lang_class:
                code["class"] = f"text-sm text-gray-600 font-mono {lang_class}"
    return str(soup)


@router.get("/{blog}")
@alru_cache(maxsize=10, ttl=60 * 60)  # Cache the function result for 1 hour (3600 seconds)
async def handle_dynamic_routes(request: Request, blog: str, access_token: str = Cookie(None)):
    # Fetch all blogs from cached data
    all_blogs: List[Dict[str, Any]] = await get_blogs_data(limit=100)

    # Find the current blog by slug
    current_blog = next((b for b in all_blogs if b.get("slug") == blog), None)

    if not current_blog:
        raise HTTPException(status_code=404, detail="Blog not found")

    # Convert Markdown to HTML
    try:
        html_content = markdown.markdown(current_blog["blog_description"], extensions=["extra", "codehilite", "toc"], output_format="html5")
    except Exception as e:
        print(f"Error converting Markdown to HTML: {str(e)}")
        html_content = current_blog["blog_description"]  # Fallback to raw content

    # Apply Tailwind styles to the HTML content
    current_blog["blog_description"] = addStylesToHTML(html_content)

    # Get related blogs
    curated_blogs = current_blog.get("related_blogs", [])
    related_blogs = get_related_blogs_list(curated_blogs, all_blogs)
    current_blog["related_blogs"] = related_blogs

    # Get more blogs
    more_blogs = get_more_blogs_list(current_blog, all_blogs, min_more=6)
    current_blog["more_blogs"] = more_blogs

    metadata = {
        "title": f"{current_blog['meta_title']} | AI Bull Blog",
        "description": current_blog["meta_description"],
        "canonicalUrl": current_blog["slug"],
        "site_name": "AI Bull",
        "ogImage": "/static/images/logo.png",
    }

    return templates.TemplateResponse(
        "/blogs/details.html",
        {"request": request, "blog": current_blog, **menu, **get_user_data(access_token), "metadata": metadata},
    )


def get_related_blogs_list(curated_blogs: List[Any], all_blogs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Get related blogs based on curated blog IDs"""
    related_blogs: List[Dict[str, Any]] = []

    # Convert curated blog IDs into full blog objects from all_blogs
    for blog in all_blogs:
        if blog.get("uuid") in curated_blogs:
            related_blogs.append(blog)

    return related_blogs


def get_more_blogs_list(current_blog: Dict[str, Any], all_blogs: List[Dict[str, Any]], min_more: int = 10) -> List[Dict[str, Any]]:
    """Get the last 5 and next 5 blogs for interlinking, sorted by creation date"""
    more_blogs: List[Dict[str, Any]] = []
    seen_slugs: Set[str] = {current_blog.get("slug", "")}

    def add_blog(blog: Dict[str, Any]) -> bool:
        slug = blog.get("slug")
        if slug and slug not in seen_slugs and len(more_blogs) < min_more:
            more_blogs.append(blog)
            seen_slugs.add(slug)
            return True
        return False

    if all_blogs:
        sorted_blogs = sorted(all_blogs, key=lambda x: x.get("created_date", ""), reverse=True)

        current_index = next(
            (i for i, b in enumerate(sorted_blogs) if b.get("slug") == current_blog.get("slug")),
            -1,
        )

        if current_index != -1:
            before_blogs = sorted_blogs[max(0, current_index - 5) : current_index]
            after_blogs = sorted_blogs[current_index + 1 : current_index + 6]

            for blog in reversed(after_blogs):
                add_blog(blog)

            for blog in before_blogs:
                add_blog(blog)

        if len(more_blogs) < min_more:
            for blog in sorted_blogs:
                add_blog(blog)

    more_blogs.sort(key=lambda x: x.get("created_date", ""), reverse=True)
    return more_blogs[:min_more]
