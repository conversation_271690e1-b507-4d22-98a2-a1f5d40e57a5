from typing import Any, Dict

import httpx
from async_lru import alru_cache
from fastapi import APIRouter, HTTPException

router = APIRouter()

from app.util.settings import app_settings


@alru_cache(maxsize=100, ttl=60 * 60 * 24)  # Cache for 1 day
async def fetch_screener_data(symbol: str, data_type: str) -> Dict[str, Any]:
    """Fetch data from the screener API with caching"""
    async with httpx.AsyncClient(timeout=10) as client:
        try:
            response = await client.get(f"{app_settings.AIBULL_SCREENER_INDIVIDUAL}/{symbol}", params={"t": data_type})
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            raise HTTPException(status_code=500, detail=f"Error fetching {data_type} data: {str(e)}")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


@router.get("/{symbol}/key-metrics")
async def get_key_metrics(symbol: str):
    return await fetch_screener_data(symbol, "key-metrics")


@router.get("/{symbol}/quarterly-results")
async def get_quarterly_results(symbol: str):
    return await fetch_screener_data(symbol, "quarterly-results")


@router.get("/{symbol}/shareholding")
async def get_shareholding(symbol: str):
    return await fetch_screener_data(symbol, "shareholding")


@router.get("/{symbol}/cash-flow")
async def get_cash_flow(symbol: str):
    return await fetch_screener_data(symbol, "cash-flow")


@router.get("/{symbol}/balance-sheet")
async def get_balance_sheet(symbol: str):
    return await fetch_screener_data(symbol, "balance-sheet")


@router.get("/{symbol}/profit-loss")
async def get_profit_loss(symbol: str):
    return await fetch_screener_data(symbol, "profit-loss")


@router.get("/{symbol}/ratios")
async def get_ratios(symbol: str):
    return await fetch_screener_data(symbol, "ratios")


@router.get("/{symbol}/peers")
async def get_peers(symbol: str):
    return await fetch_screener_data(symbol, "peers")
