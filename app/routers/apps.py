from typing import Dict, Any, Optional, List, cast
from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Query, Body, HTTPException
from app.util.apps import is_app_authenticated, make_trading_api_call
from app.routers.auth import get_current_user_data
from app.util.instruments import get_instrument_id_for_leg
from pydantic import BaseModel, Field
import json
from datetime import datetime

router = APIRouter()


@router.get("/authenticated-apps")
async def get_authenticated_apps_route(access_token: Optional[str] = Cookie(None)):
    """Get authenticated apps"""
    user_data: Dict[str, Any] = get_current_user_data(access_token or "")
    valid_apps: List[str] = []
    # Process apps
    for app in user_data.get("apps", []):
        if app.get("category") == "Stocks":
            if not await is_app_authenticated(app):
                continue

            valid_apps.append(cast(str, app.get("uuid")))
    return {"apps": valid_apps}


@router.post("/get-instrument-ids-for-legs")
async def get_instrument_ids_for_legs_route(
    broker: str,
    legs: List[Dict[str, Any]] = Body(...),
) -> Dict[str, List[Dict[str, Any]]]:
    """Get instrument IDs for multiple legs for a specific broker

    Args:
        broker: Broker name (upstox, zerodha, dhan)
        legs: List of leg objects, each with:
            - symbol: Trading symbol (e.g., RELIANCE)
            - type: Instrument type (CE, PE, FUT)
            - expiryDate: Expiry date in format DD-MMM-YYYY (e.g., 27-Mar-2025)
            - strike: Strike price (required for options)

    Returns:
        List of legs with their corresponding trading_symbol and instrument_id values
    """
    results = []

    for leg in legs:
        trading_symbol, instrument_id = get_instrument_id_for_leg(leg, broker)
        # Return the original leg with the trading symbol and instrument ID added
        result_leg = leg.copy()
        result_leg["trading_symbol"] = trading_symbol
        result_leg["instrument_id"] = instrument_id
        results.append(result_leg)

    return {"legs": results}

class OrderDetails(BaseModel):
    """Represents the details of a single order"""

    symbol: str = Field(..., description="Trading symbol e.g. NIFTY")
    id: str = Field(..., description="Unique identifier for the leg")
    type: str = Field(..., description="Instrument type (CE, PE, FUT)")
    strike: str = Field(..., description="Strike price (required for options)")
    expiryDate: str = Field(..., description="Expiry date in format DD-MMM-YYYY")
    action: str = Field(..., description="Action (buy, sell)")
    product_type: str = Field(..., description="Product type (INTRADAY, DELIVERY)")
    order_type: str = Field(..., description="Order type (LIMIT, MARKET)")
    quantity: str = Field(..., description="Order quantity")
    validity: str = Field("DAY", description="Order validity (DAY, IOC)")
    price: str = Field("0", description="Order price (required for LIMIT orders)")
    tag: str = Field("AIBULL", description="Order tag")
    transaction_type: str = Field(..., description="Transaction type (BUY, SELL)")
    disclosed_quantity: Optional[str] = Field("", description="Disclosed quantity")
    trigger_price: Optional[str] = Field("0", description="Trigger price (for SL/SLM orders)")
    is_amo: str = Field("FALSE", description="Whether the order is AMO (TRUE, FALSE)")
    exchange: str = Field("NFO", description="Exchange (NFO, NSE, BSE, MCX)")
    variety: str = Field("NORMAL", description="Order variety (regular, AMO, NORMAL)")


class PlaceOrdersRequest(BaseModel):
    """Request model for placing orders"""

    app_uuid: str = Field(..., description="UUID of the trading app")
    orders: List[OrderDetails] = Field(..., description="List of orders to place")


@router.post("/place-orders")
async def place_orders_route(request: PlaceOrdersRequest, access_token: Optional[str] = Cookie(None)) -> Dict[str, Any]:
    """Place multiple orders for a given app

    Args:
        request: Request object containing app UUID and list of orders
        access_token: Optional access token for authentication

    Returns:
        Dictionary containing results of each order placement
    """
    user_data: Dict[str, Any] = get_current_user_data(access_token or "")
    app: Optional[Dict[str, Any]] = next((a for a in user_data.get("apps", []) if a.get("uuid") == request.app_uuid), None)
    if not app:
        raise HTTPException(status_code=400, detail="App not found or not authenticated")

    broker: Optional[str] = app.get("key")
    if not broker:
        raise HTTPException(status_code=400, detail="Could not determine broker from app")

    results: List[Dict[str, Any]] = []
    for order in request.orders:
        # Convert Pydantic model to dict for use with get_instrument_id_for_leg
        order_dict = order.model_dump()
        trading_symbol, instrument_id = get_instrument_id_for_leg(order_dict, broker)

        if not trading_symbol or not instrument_id:
            results.append({"order": order_dict, "error": "Instrument not found"})
            continue

        # Map to CreateOrderInput
        create_order_input = {
            "trading_symbol": trading_symbol,  # Formatted trading symbol
            "security_id": str(instrument_id),  # Unique identifier for the instrument.
            "instrument_token": str(instrument_id),  # Unique identifier for the instrument.
            "exchange_segment": "NFO",  # Exchange segment. Possible values: NSE, BSE, MCX.
            "exchange": order.exchange,  # Exchange. Possible values: NFO, NSE, BSE, MCX.
            "transaction_type": order.transaction_type,  # Type of transaction. Possible values: BUY, SELL.
            "quantity": order.quantity,  # Quantity of the order.
            "product_type_dhan": "INTRADAY" if order.product_type == "INTRADAY" else "CNC",  # Type of product.  Possible values: CNC, INTRADAY, MARGIN, MTF, CO, BO.
            "product_type": "INTRADAY" if order.product_type == "INTRADAY" else "DELIVERY",  # Type of product.  Possible values: INTRADAY, DELIVERY, CARRYFORWARD, MARGIN, BO.
            "product": "I" if order.product_type == "INTRADAY" else "D",  # Type of product. Possible values: INTRADAY, DELIVERY.
            "order_type": order.order_type,  # Type of order. Possible values: MARKET, LIMIT, SL, SLM.
            "price": order.price if order.order_type == "LIMIT" else "0",  # Price for LIMIT orders.
            "validity": order.validity,  # Validity of the order. Possible values: DAY, IOC.
            "is_amo": order.is_amo,  # Whether the order is an AMO order. Possible values: TRUE, FALSE.
            "variety": order.variety,  # Variety of the order. Possible values: regular, AMO, NORMAL.
            "tag": order.tag,  # Tag for the order.
            "trigger_price": order.trigger_price,  # Trigger price for SL/SLM orders.
            "disclosed_quantity": order.disclosed_quantity,  # Disclosed quantity.

            #Below are for ICICI Direct
            "stock_code": order.symbol,  # Stock code for the order.
            "exchange_code": order.exchange,  # Exchange code for the order.
            "product_icici_direct": "futures" if order.type == "FUT" else "options",  # Specifies the product type for the given stock_code in the NFO or BFO segment. Possible values: futures , options , optionplus , cash , btst , margin.
            "user_remark": order.tag or "AIBULL",  # User remark for the order.
        }
        
        # Additional mappings for ICICI Direct
        if broker == "icici-direct":
            
            # Format validity_date (current date in ISO format)
            # validity_date = datetime.now().strftime("%Y-%m-%d")
            # create_order_input["validity_date"] = validity_date
            
            # Format expiry_date (convert DD-MMM-YYYY to YYYY-MM-DD)
            try:
                expiry_date = datetime.strptime(order.expiryDate, "%d-%b-%Y").strftime("%Y-%m-%d")
                create_order_input["expiry_date"] = expiry_date
            except ValueError:
                # If parsing fails, use the original format
                create_order_input["expiry_date"] = f"{order.expiryDate}"
            
            # Set right field for options (call, put, others)
            if order.type == "CE":
                create_order_input["right"] = "call"
            elif order.type == "PE":
                create_order_input["right"] = "put"
            else:
                create_order_input["right"] = "others"
            
            # Add strike_price
            if order.type != "FUT":
                create_order_input["strike_price"] = order.strike
        
        request_data = {"params": create_order_input, "auth": app.get("fields", {})}
        create_order_input["id"] = order.id
        try:
            response = await make_trading_api_call(app, "create_order", request_data)
            if response.get("result", {}).get("status") == "Success":
                results.append({"order": create_order_input, "result": response})
            else:
                results.append({"order": create_order_input, "error": response.get("result", {}).get("error") or response.get("result", {}).get("message")})
        except Exception as e:
            results.append({"order": create_order_input, "error": str(e)})

    return {"results": results}


class OrderDetailsRequest(BaseModel):
    """Request model for fetching order details"""

    app_uuid: str = Field(..., description="UUID of the trading app")
    order_id: str = Field(..., description="Order ID to fetch details for")
    unique_order_id: Optional[str] = Field(None, description="Unique order ID to fetch details for")  # AngelOne returns unique order id for each order


@router.post("/get-order-history")
async def get_order_history_route(request: OrderDetailsRequest, access_token: Optional[str] = Cookie(None)) -> Dict[str, Any]:
    """Fetch details of a specific order

    Args:
        request: Request object containing app UUID and order ID
        access_token: Optional access token for authentication

    Returns:
        Dictionary containing the order details
    """
    user_data: Dict[str, Any] = get_current_user_data(access_token or "")
    app: Optional[Dict[str, Any]] = next((a for a in user_data.get("apps", []) if a.get("uuid") == request.app_uuid), None)

    if not app:
        raise HTTPException(status_code=400, detail="App not found or not authenticated")

    # Prepare the request data for the trading API
    request_data = {"params": {"order_id": request.order_id, "unique_order_id": request.unique_order_id}, "auth": app.get("fields", {})}

    try:
        # Call the trading API to get order details
        response = await make_trading_api_call(app, "get_order_history", request_data)

        if response.get("result", {}).get("status") != "Success":
            return {"status": "error", "message": response.get("result", {}).get("error") or response.get("result", {}).get("message")}

        return {"status": "success", "order": response.get("result", {})}
    except Exception as e:
        return {"status": "error", "message": str(e)}


class BatchMarketQuoteRequest(BaseModel):
    """Request model for fetching batch market quote data"""

    app_uuid: str = Field(..., description="UUID of the trading app")
    legs: List[Dict[str, Any]] = Field(..., description="List of leg objects to fetch quote data for")
    interval: Optional[str] = Field(None, description="Interval for data (1d, I1, I30)")
    exchange: str = Field("NFO", description="Exchange code (NFO, NSE, BSE)")


@router.post("/get-batch-market-quote")
async def get_batch_market_quote_route(request: BatchMarketQuoteRequest, access_token: Optional[str] = Cookie(None)) -> Dict[str, Any]:
    """Fetch market quote data for multiple instruments in a single call

    Args:
        request: Request object containing app UUID and legs details
        access_token: Optional access token for authentication

    Returns:
        Dictionary containing market quote data for all requested instruments
    """
    user_data: Dict[str, Any] = get_current_user_data(access_token or "")
    app: Optional[Dict[str, Any]] = next((a for a in user_data.get("apps", []) if a.get("uuid") == request.app_uuid), None)

    if not app:
        raise HTTPException(status_code=400, detail="App not found or not authenticated")

    broker: Optional[str] = app.get("key")
    if not broker:
        raise HTTPException(status_code=400, detail="Could not determine broker from app")

    # Get instrument details for all legs
    instrument_details = []
    leg_id_map = {}  # Map instrument_id back to leg_id
    trading_symbol_map = {}  # Map trading_symbol back to leg_id

    for leg in request.legs:
        # Skip disabled legs
        if leg.get("enabled") is False:
            continue

        # Get instrument ID for this leg
        leg_copy = leg.copy()
        trading_symbol, instrument_id = get_instrument_id_for_leg(leg_copy, broker)

        if not trading_symbol or not instrument_id:
            continue

        leg_id = leg.get("id")
        instrument_details.append((trading_symbol, instrument_id, leg_id))
        leg_id_map[str(instrument_id)] = leg_id
        trading_symbol_map[trading_symbol] = leg_id

    if not instrument_details:
        return {"status": "error", "message": "No valid instruments found"}

    # Prepare broker-specific parameters
    params = {
        "interval": request.interval,
        # "auth": app.get("fields", {}) # Auth should be passed in request_data
    }

    if broker == "dhan":
        instrument_ids = [det[1] for det in instrument_details]
        securities_dict = {"NSE_FNO": instrument_ids}  # Assuming NSE_FNO for options
        params["securities"] = json.dumps(securities_dict)
    elif broker == "angelone" or broker == "angeloneapi":
        instrument_ids = [det[1] for det in instrument_details]
        exchange_tokens = {"NFO": [str(id) for id in instrument_ids]}  # AngelOne expects NFO segment and string tokens
        params["exchange_tokens"] = json.dumps(exchange_tokens)
    elif broker == "upstox" or broker == "upstoxapi":
        # Construct Upstox instrument key: SEGMENT|SYMBOL, comma-separated
        # Use the instrument_id obtained from get_instrument_id_for_leg
        valid_instrument_ids = [det[1] for det in instrument_details if isinstance(det[1], str) and det[1]]
        if not valid_instrument_ids:
            raise HTTPException(status_code=400, detail="Could not find valid instrument IDs for Upstox.")

        instrument_key = ",".join(valid_instrument_ids)
        params["instrument_key"] = instrument_key
    else:
        # Handle unsupported brokers
        raise HTTPException(status_code=400, detail=f"Broker '{broker}' not supported for batch market quotes.")

    request_data = {"params": params, "auth": app.get("fields", {})}

    try:
        # Call the trading API to get market quote data for all instruments
        response = await make_trading_api_call(app, "get_full_market_quote", request_data)

        if response.get("result", {}).get("status") != "Success":
            return {"status": "error", "message": response.get("result", {}).get("error") or response.get("result", {}).get("message")}

        # Process the response - map quotes back to leg IDs
        quotes_by_leg_id = {}
        result_data = response.get("result", {})

        # Check if the result contains a 'details' field (generic structure) or is directly the data
        data_to_process = result_data.get("details", result_data)

        if isinstance(data_to_process, dict):
            # Likely Dhan or Upstox
            if broker == "upstox":
                # Upstox result keyed by full instrument_key (e.g., "NSE_FO|SYMBOL")
                for full_key, quote_data in data_to_process.items():
                    try:
                        # Extract symbol to map back to leg_id
                        segment, symbol = full_key.split("|", 1)
                        leg_id = trading_symbol_map.get(symbol)
                        if leg_id:
                            quotes_by_leg_id[leg_id] = quote_data
                    except ValueError:
                        print(f"Warning: Could not parse Upstox key: {full_key}")
            else:
                # Likely Dhan (keyed by instrument_id) or other dictionary format
                for instrument_id, quote_data in data_to_process.items():
                    leg_id = leg_id_map.get(str(instrument_id))
                    if leg_id:
                        quotes_by_leg_id[leg_id] = quote_data
        elif isinstance(data_to_process, list):
            # Likely AngelOne (list of quote objects)
            for quote_data in data_to_process:
                # Try matching by instrument_token first (more reliable)
                token = str(quote_data.get("instrument_token") or quote_data.get("token"))  # Angel might use 'token'
                leg_id = leg_id_map.get(token)
                if leg_id:
                    quotes_by_leg_id[leg_id] = quote_data
                    continue  # Found match by token

                # Fallback to matching by trading symbol
                symbol = quote_data.get("symbol")  # Angel might use 'symbol'
                if symbol:
                    leg_id = trading_symbol_map.get(symbol)
                    if leg_id:
                        quotes_by_leg_id[leg_id] = quote_data

        return {"status": "success", "quotes": quotes_by_leg_id}

    except Exception as e:
        return {"status": "error", "message": str(e)}
