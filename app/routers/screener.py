import json
from typing import Any, Dict, List, Optional

from cachetools import T<PERSON><PERSON><PERSON>, cached
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>TTP<PERSON>x<PERSON>, Query, Request
from app.util.jinja_functions import format_url_to_text
from app.util.menu import menu
from app.routers.auth import get_user_data
from app.routers.screener_api.screen import fetch_screener_data
from app.util.stocks import get_stock_details
from app.util.templates import templates
from pydantic import BaseModel
import asyncio
from app.util.db.redis import get_redis_client

router = APIRouter()

# In‑memory cache for the aggregated screener data (from key "SCREENER:ALL")
all_screener_cache = TTLCache(maxsize=1, ttl=60 * 60 * 10)

# Cache for search results; key will be a JSON string of filters
search_cache = TTLCache(maxsize=1024, ttl=60 * 60 * 10)


# screener.py

# Mapping for SEO-friendly URLs to filter parameters
SEO_STOCK_FILTER_MAPPINGS = {
    "long-term-gems": {"max_close_price": 1300, "min_market_cap": 1000},
    "momentum-monsters": {"min_forward_pe": 15, "max_forward_pe": 25, "min_beta": 0.5, "max_beta": 1.0},
    "dividend-darlings": {"min_dividend_yield": 2, "max_debt_to_equity": 100},
    "blue-chip-bargains": {"min_market_cap": 50000, "max_beta": 1.2},
    "value-picks": {"min_forward_pe": 10, "max_forward_pe": 20, "min_market_cap": 1000},
    "high-volatility-ventures": {"min_beta": 1.2, "min_close_price": 500},
    "growth-giants": {"min_market_cap": 10000, "max_forward_pe": 30},
    "income-innovators": {"max_close_price": 1000, "min_dividend_yield": 1},
    "small-cap-stars": {"min_market_cap": 500, "max_debt_to_equity": 50},
    "stable-value-bets": {"min_forward_pe": 5, "max_beta": 0.8},
    # Sector-based mappings
    "technology-stocks": {"sector": "Technology"},
    "healthcare-stocks": {"sector": "Healthcare"},
    "financial-stocks": {"sector": "Financial Services"},
}

# SEO metadata for each filter
SEO_STOCK_METADATA = {
    "long-term-gems": {
        "title": "Long Term Gems - Top Stocks Near 52-Week Lows",
        "description": "Discover stocks with strong 5-year revenue growth trading near their 52-week lows, ideal for long-term investment.",
    },
    "momentum-monsters": {
        "title": "Momentum Monsters - High Momentum Stocks",
        "description": "Find high-momentum stocks with strong forward PE ratios and moderate volatility for aggressive investors.",
    },
    "dividend-darlings": {
        "title": "Dividend Darlings - High Yield Low Debt Stocks",
        "description": "Explore stocks with high dividend yields and low debt-to-equity ratios for stable income.",
    },
    "blue-chip-bargains": {
        "title": "Blue Chip Bargains - Large Cap Stable Stocks",
        "description": "Discover large-cap stocks with stable beta for lower volatility and reliable returns.",
    },
    "value-picks": {
        "title": "Value Picks - Undervalued Stocks",
        "description": "Find undervalued stocks with reasonable forward PE and market cap for value investors.",
    },
    "high-volatility-ventures": {
        "title": "High Volatility Ventures - High Beta Stocks",
        "description": "Explore high-beta stocks with significant price movement potential for risk-tolerant investors.",
    },
    "growth-giants": {
        "title": "Growth Giants - Mid to Large Cap Stocks",
        "description": "Discover mid to large-cap stocks with strong growth potential and attractive valuations.",
    },
    "income-innovators": {
        "title": "Income Innovators - Affordable Dividend Stocks",
        "description": "Find affordable stocks with consistent dividend payouts for income-focused investors.",
    },
    "small-cap-stars": {
        "title": "Small Cap Stars - Low Debt Small Caps",
        "description": "Explore small-cap stocks with low debt levels for high-growth potential.",
    },
    "stable-value-bets": {
        "title": "Stable Value Bets - Low PE Low Beta Stocks",
        "description": "Discover low PE and low beta stocks for conservative investors seeking stability.",
    },
    "technology-stocks": {
        "title": "Technology Stocks - Top Tech Investment Opportunities",
        "description": "Explore top technology stocks. Filter by market cap, PE ratio, and more to find the best tech investments.",
    },
    "healthcare-stocks": {
        "title": "Healthcare Stocks - Top Healthcare Investments",
        "description": "Find top healthcare stocks. Filter by market cap, dividend yield, and more for healthcare investment opportunities.",
    },
    "financial-stocks": {
        "title": "Financial Stocks - Top Financial Services Stocks",
        "description": "Discover top financial services stocks. Filter by market cap, beta, and more to find the best financial investments.",
    },
}


class ScreenerFilter(BaseModel):
    longName: Optional[str] = None
    sector: Optional[str] = None
    industry: Optional[str] = None
    min_market_cap: Optional[float] = None
    max_market_cap: Optional[float] = None
    min_close_price: Optional[float] = None  # Uses regularMarketPrice
    max_close_price: Optional[float] = None
    min_debt_to_equity: Optional[float] = None
    max_debt_to_equity: Optional[float] = None
    min_5y_hist_rev_growth: Optional[float] = None  # Assumes field "5YHistRevGrowth"
    max_5y_hist_rev_growth: Optional[float] = None
    min_1y_hist_rev_growth: Optional[float] = None  # Assumes field "1YHistRevGrowth"
    max_1y_hist_rev_growth: Optional[float] = None
    min_ROCE: Optional[float] = None  # Assumes field "ROCE"
    max_ROCE: Optional[float] = None
    min_5y_avg_roi: Optional[float] = None  # Assumes field "5YAvgROI"
    max_5y_avg_roi: Optional[float] = None
    min_trailing_pe: Optional[float] = None  # Assumes field "trailingPE"
    max_trailing_pe: Optional[float] = None
    min_forward_pe: Optional[float] = None  # Assumes field "forwardPE"
    max_forward_pe: Optional[float] = None
    min_beta: Optional[float] = None  # Assumes field "beta"
    max_beta: Optional[float] = None
    min_dividend_yield: Optional[float] = None  # Assumes field "dividendYield"
    max_dividend_yield: Optional[float] = None
    page: Optional[int] = 1  # Default page is 1
    limit: Optional[int] = 100  # Default limit is 100

    # New field for column selection
    columns: Optional[List[str]] = None


@cached(all_screener_cache)
def get_all_screener_data() -> dict:
    """
    Retrieve the aggregated screener data from Redis stored under the key "SCREENER:ALL".
    The data is expected to have had its bulky "history" field removed.
    """
    redis_client = get_redis_client()
    val = redis_client.get("SCREENER:ALL")
    if not val:
        raise HTTPException(status_code=404, detail="No aggregated screener data available")
    try:
        data = json.loads(val)
    except Exception as e:
        raise HTTPException(status_code=500, detail="Error decoding screener data: " + str(e))
    return data


def _search_screener_cached(filter_key: str) -> List[dict]:
    """
    Internal function to search screener data.
    filter_key is a JSON string (with sorted keys) representing the filters.
    This function is cached.
    """
    filters = ScreenerFilter.parse_raw(filter_key)
    aggregated_data = get_all_screener_data()
    matching_stocks = []
    for stock_data in aggregated_data.values():
        if stock_matches_filter(stock_data, filters):
            matching_stocks.append(stock_data)

    page = max(1, filters.page or 1)
    limit = max(1, min(filters.limit or 100, 100))  # Clamp limit to [1, 100]
    start = (page - 1) * limit
    end = start + limit
    paginated_stocks = matching_stocks[start:end]

    return paginated_stocks, len(matching_stocks)


@cached(search_cache)
def search_screener_cached(filter_key: str) -> List[dict]:
    """
    Cached wrapper for the search. It uses filter_key as the cache key.
    """
    return _search_screener_cached(filter_key)


async def fetch_symbol_data(symbol: str) -> dict:
    """
    Fetch detailed data for a given stock symbol.
    """
    stock_data = {}
    try:
        # print(f"{ "*" * 5 } Fetching data for {symbol} { "*" * 5 }")
        # Fetch basic stock details
        stock_data = get_stock_details(symbol)

        # Fetch all screener data concurrently
        tasks = [
            fetch_screener_data(symbol, "key-metrics"),
            fetch_screener_data(symbol, "shareholding"),
            fetch_screener_data(symbol, "cash-flow"),
            fetch_screener_data(symbol, "balance-sheet"),
            fetch_screener_data(symbol, "ratios"),
            fetch_screener_data(symbol, "quarterly-results"),
        ]
        results = await asyncio.gather(*tasks)

        key_metrics, shareholding, cash_flow, balance_sheet, ratios, quarterly_results = results

        stock_data["info"].update({
            "key_metrics": key_metrics.get("key_metrics", {}),
            "shareholding": shareholding,
            "cash_flow": cash_flow,
            "balance_sheet": balance_sheet,
            "ratios": ratios,
            "quarterly_results": quarterly_results,
        })
        
        return stock_data

    except Exception as e:
        print(f"Error fetching data for {symbol}: {str(e)}")
        return stock_data


@router.get("/filter-data")
async def get_filter_data(path_param: Optional[str] = Query(None)) -> Dict[str, Any]:
    """
    API route to fetch filter-related data for the stock screener
    """
    try:
        filter_options = get_filter_options()
        preset_filters: Dict[str, Optional[Any]] = {}
        filter_fields = [
            "longName",
            "sector",
            "industry",
            "min_market_cap",
            "max_market_cap",
            "min_close_price",
            "max_close_price",
            "min_debt_to_equity",
            "max_debt_to_equity",
            "min_beta",
            "max_beta",
            "min_forward_pe",
            "max_forward_pe",
            "min_dividend_yield",
            "max_dividend_yield",
            "min_5y_hist_rev_growth",
            "max_5y_hist_rev_growth",
            "min_1y_hist_rev_growth",
            "max_1y_hist_rev_growth",
            "min_ROCE",
            "max_ROCE",
            "min_5y_avg_roi",
            "max_5y_avg_roi",
            "min_trailing_pe",
            "max_trailing_pe",
            "page",
            "limit",
            "columns",
        ]

        if path_param:
            path_param = path_param.strip("/").lower()
            is_sector_route = path_param.endswith("-sector")
            is_industry_route = path_param.endswith("-industry")

            if is_sector_route or is_industry_route:
                # Extract sector or industry name and format it
                filter_value = path_param.replace("-sector", "").replace("-industry", "")
                filter_value = format_url_to_text(filter_value)
                preset_filters = {field: None for field in filter_fields}
                preset_filters.update(
                    {
                        "sector" if is_sector_route else "industry": filter_value,
                        "page": 1,
                        "limit": 100,
                        "columns": ["symbol", "name", "sector", "industry", "marketCap", "regularMarketPrice", "debtToEquity", "beta", "forwardPE", "dividendYield"],
                    }
                )
            elif path_param in SEO_STOCK_FILTER_MAPPINGS:
                preset_filters = {field: None for field in filter_fields}
                preset_filters.update(
                    {
                        "sector": "",
                        "page": 1,
                        "limit": 100,
                        "columns": ["symbol", "name", "sector", "industry", "marketCap", "regularMarketPrice", "debtToEquity", "beta", "forwardPE", "dividendYield"],
                    }
                )
                filter_params = SEO_STOCK_FILTER_MAPPINGS[path_param]
                preset_filters.update({field: filter_params.get(field, preset_filters[field]) for field in filter_fields if field in filter_params})

        return {
            "preset_filters": preset_filters, 
            "sectors": filter_options["sectors"], 
            "sectorIndustryMapping": filter_options["sectorIndustryMapping"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="An error occurred while fetching filter data.")


@router.get("/compare")
@router.get("/compare/{stocks_path:path}")
async def compare_stocks(
    request: Request,
    stocks_path: str = "",
    access_token: Optional[str] = Cookie(None),
):
    result = await process_stock_comparison(stocks_path)
    context = {
        "request": request,
        **menu,  # Assuming menu is defined elsewhere
        **result,
        **get_user_data(access_token),
    }
    return templates.TemplateResponse("/stocks/compare.html", context)


async def process_stock_comparison(stocks_path: str) -> Dict[str, Any]:
    """Shared logic for processing stock comparison data"""
    # Construct the canonical URL
    canonical_url = f"https://theaibull.com/screener/compare" if not stocks_path or stocks_path == "/" else f"https://theaibull.com/screener/compare/{stocks_path}"

    # Handle empty path
    if not stocks_path or stocks_path == "/":
        return {
            "stocks": [],
            "error": "Add at least two stocks to compare.",
            "recommended_stocks": [],
            "seo_meta_data": {"canonical_url": canonical_url},
        }

    # Split the stocks_path to extract symbols
    valid_symbols = [symbol.strip().upper() for symbol in stocks_path.split("/") if symbol.strip()]

    # Validate number of stocks
    if len(valid_symbols) > 5:
        logger.warning(f"Too many stock symbols provided: {len(valid_symbols)}")
        raise HTTPException(status_code=400, detail="Cannot compare more than 5 stocks.")

    # Create a list of tasks to run concurrently
    tasks = [fetch_symbol_data(symbol.replace(".NS", "")) for symbol in valid_symbols]

    # Wait for all the tasks to finish concurrently
    stocks = await asyncio.gather(*tasks)

    # Validate minimum stocks
    if not stocks or len(valid_symbols) < 2:
        return {
            "stocks": stocks if stocks else [],
            "error": "Add at least two stocks to compare.",
            "recommended_stocks": [],
            "seo_meta_data": {"canonical_url": canonical_url},
        }

    if not stocks or all(not stock for stock in stocks):
        raise HTTPException(status_code=500, detail="No data available for the selected stocks.")

    # Fetch recommended stocks
    recommended_stocks = []
    if stocks:
        # Get the primary industry (use the first stock's industry)
        primary_industry = stocks[0].get("info", {}).get("industry", None)
        if primary_industry:
            # Fetch all screener data
            all_stocks = get_all_screener_data()
            # Filter stocks in the same industry
            industry_stocks = [{**stock, "symbol": symbol} for symbol, stock in all_stocks.items() if stock.get("info", {}).get("industry") == primary_industry]
            # Sort by market cap (descending) and 1-year return as tiebreaker
            industry_stocks.sort(
                key=lambda x: (
                    float(x.get("info", {}).get("marketCap", 0) or 0),
                    float(x.get("info", {}).get("fiftyTwoWeekChangePercent", 0) or 0),
                ),
                reverse=True,
            )
            # Find indices of selected stocks
            selected_symbols = {stock["info"]["symbol"].replace(".NS", "").upper() for stock in stocks}
            indices = [i for i, stock in enumerate(industry_stocks) if stock["symbol"].replace(".NS", "").upper() in selected_symbols]
            if indices:
                min_index = min(indices)
                max_index = max(indices)
                # Select 5 before and 5 after, excluding selected stocks
                start_index = max(0, min_index - 5)
                end_index = min(len(industry_stocks), max_index + 6)  # +6 to account for up to 5 after
                recommended_stocks = [stock for stock in industry_stocks[start_index:end_index] if stock["symbol"].replace(".NS", "").upper() not in selected_symbols][:10]
                # Ensure exactly 10 stocks
                if len(recommended_stocks) < 10:
                    additional_stocks = [stock for stock in industry_stocks if stock["symbol"].replace(".NS", "").upper() not in selected_symbols and stock not in recommended_stocks][
                        : 10 - len(recommended_stocks)
                    ]
                    recommended_stocks.extend(additional_stocks)

    return {
        "stocks": stocks,
        "error": None,
        "recommended_stocks": recommended_stocks,
        "seo_meta_data": {"canonical_url": canonical_url},
    }


# New API route returning only stocks
@router.get("/api/compare")
@router.get("/api/compare/{stocks_path:path}")
async def compare_stocks_api(
    stocks_path: str = ""
) -> Dict[str, Any]:
    result = await process_stock_comparison(stocks_path)
    if result["error"]:
        return {"error": result["error"]}
    return {"stocks":result["stocks"]}


@router.get("/api/{symbol}")
def get_stock_symbol_details(symbol: str) -> Dict[str, Any]:
    symbol = symbol.upper()
    try:
        details = get_stock_details(symbol)  # type: ignore
        if isinstance(details, dict):
            return details  # type: ignore
        else:
            return {}
    except Exception:
        return {}


@router.post("/search")
def search_screener(filters: ScreenerFilter) -> dict:
    """
    POST endpoint that receives filter criteria and returns a list of matching stocks
    from the aggregated screener data. The results are cached for 60 seconds.

    If the `columns` field is provided in the filters, only the specified columns are returned.
    """
    filter_key = json.dumps(filters.dict(), sort_keys=True)
    results, total = _search_screener_cached(filter_key)

    # If specific columns are requested, only return those.
    if filters.columns:
        projected_results = []
        for stock in results:
            projected_stock = {}
            for col in filters.columns:
                if col == "symbol":
                    # Handle symbol from nested "info" or top-level; remove any ".NS" suffix
                    sym = (stock.get("info", {}).get("symbol") or stock.get("symbol", "")).replace(".NS", "")
                    projected_stock["symbol"] = sym.upper() if sym else ""
                elif col == "name":
                    # Combine logic for name from longName and shortName
                    projected_stock["name"] = stock.get("info", {}).get("longName") or stock.get("info", {}).get("shortName", "")
                else:
                    # Look in top-level first then in info
                    projected_stock[col] = stock.get(col) or stock.get("info", {}).get(col, "")
            projected_results.append(projected_stock)
        return {"count": total, "stocks": projected_results}

    return {"count": total, "stocks": results}


def stock_matches_filter(screener_data: dict, filters: ScreenerFilter) -> bool:
    """
    Check whether the provided screener_data meets all the filter criteria.
    """
    info = screener_data.get("info", {})

    # Sector and Industry (from info)
    if filters.sector and info.get("sector") != filters.sector:
        return False
    if filters.industry and info.get("industry") != filters.industry:
        return False
    if filters.longName:
        longName = str(info.get("longName", "")).lower()
        if filters.longName.lower() not in longName:
            return False

    # Market Cap (from info)
    market_cap = None
    try:
        market_cap_raw = info.get("marketCap")
        if market_cap_raw is not None:
            market_cap = float(str(market_cap_raw).replace(",", ""))  # Handle string numbers with commas
    except (ValueError, TypeError):
        pass

    # If market cap filter is applied but we don't have the data, exclude this stock
    if (filters.min_market_cap is not None or filters.max_market_cap is not None) and market_cap is None:
        return False

    if market_cap is not None:
        if filters.min_market_cap is not None and market_cap < filters.min_market_cap:
            return False
        if filters.max_market_cap is not None and market_cap > filters.max_market_cap:
            return False

    # Close Price (using regularMarketPrice from info)
    close_price = info.get("regularMarketPrice")

    # If close price filter is applied but we don't have the data, exclude this stock
    if (filters.min_close_price is not None or filters.max_close_price is not None) and close_price is None:
        return False

    if close_price is not None:
        if filters.min_close_price is not None and close_price < filters.min_close_price:
            return False
        if filters.max_close_price is not None and close_price > filters.max_close_price:
            return False

    # Debt to Equity (from info)
    debt_to_equity = info.get("debtToEquity")

    # If debt to equity filter is applied but we don't have the data, exclude this stock
    if (filters.min_debt_to_equity is not None or filters.max_debt_to_equity is not None) and debt_to_equity is None:
        return False

    if debt_to_equity is not None:
        if filters.min_debt_to_equity is not None and debt_to_equity < filters.min_debt_to_equity:
            return False
        if filters.max_debt_to_equity is not None and debt_to_equity > filters.max_debt_to_equity:
            return False

    # 5Y Historical Revenue Growth (from info)
    hist_rev_5y = info.get("5YHistRevGrowth")

    # If 5Y growth filter is applied but we don't have the data, exclude this stock
    if (filters.min_5y_hist_rev_growth is not None or filters.max_5y_hist_rev_growth is not None) and hist_rev_5y is None:
        return False

    if hist_rev_5y is not None:
        if filters.min_5y_hist_rev_growth is not None and hist_rev_5y < filters.min_5y_hist_rev_growth:
            return False
        if filters.max_5y_hist_rev_growth is not None and hist_rev_5y > filters.max_5y_hist_rev_growth:
            return False

    # 1Y Historical Revenue Growth (from info)
    hist_rev_1y = info.get("1YHistRevGrowth")

    # If 1Y growth filter is applied but we don't have the data, exclude this stock
    if (filters.min_1y_hist_rev_growth is not None or filters.max_1y_hist_rev_growth is not None) and hist_rev_1y is None:
        return False

    if hist_rev_1y is not None:
        if filters.min_1y_hist_rev_growth is not None and hist_rev_1y < filters.min_1y_hist_rev_growth:
            return False
        if filters.max_1y_hist_rev_growth is not None and hist_rev_1y > filters.max_1y_hist_rev_growth:
            return False

    # ROCE (from info)
    roce = info.get("ROCE")

    # If ROCE filter is applied but we don't have the data, exclude this stock
    if (filters.min_ROCE is not None or filters.max_ROCE is not None) and roce is None:
        return False

    if roce is not None:
        if filters.min_ROCE is not None and roce < filters.min_ROCE:
            return False
        if filters.max_ROCE is not None and roce > filters.max_ROCE:
            return False

    # 5Y Average ROI (from info)
    avg_roi_5y = info.get("5YAvgROI")

    # If 5Y ROI filter is applied but we don't have the data, exclude this stock
    if (filters.min_5y_avg_roi is not None or filters.max_5y_avg_roi is not None) and avg_roi_5y is None:
        return False

    if avg_roi_5y is not None:
        if filters.min_5y_avg_roi is not None and avg_roi_5y < filters.min_5y_avg_roi:
            return False
        if filters.max_5y_avg_roi is not None and avg_roi_5y > filters.max_5y_avg_roi:
            return False

    # Trailing PE (from info)
    trailing_pe = info.get("trailingPE")

    # If trailing PE filter is applied but we don't have the data, exclude this stock
    if (filters.min_trailing_pe is not None or filters.max_trailing_pe is not None) and trailing_pe is None:
        return False

    if trailing_pe is not None:
        if filters.min_trailing_pe is not None and trailing_pe < filters.min_trailing_pe:
            return False
        if filters.max_trailing_pe is not None and trailing_pe > filters.max_trailing_pe:
            return False

    # Forward PE (from info)
    forward_pe = info.get("forwardPE")

    # If forward PE filter is applied but we don't have the data, exclude this stock
    if (filters.min_forward_pe is not None or filters.max_forward_pe is not None) and forward_pe is None:
        return False

    if forward_pe is not None:
        if filters.min_forward_pe is not None and forward_pe < filters.min_forward_pe:
            return False
        if filters.max_forward_pe is not None and forward_pe > filters.max_forward_pe:
            return False

    # Beta (from info)
    beta = info.get("beta")

    # If beta filter is applied but we don't have the data, exclude this stock
    if (filters.min_beta is not None or filters.max_beta is not None) and beta is None:
        return False

    if beta is not None:
        if filters.min_beta is not None and beta < filters.min_beta:
            return False
        if filters.max_beta is not None and beta > filters.max_beta:
            return False

    # Dividend Yield (from info)
    dividend_yield = info.get("dividendYield")

    # If dividend yield filter is applied but we don't have the data, exclude this stock
    if (filters.min_dividend_yield is not None or filters.max_dividend_yield is not None) and dividend_yield is None:
        return False

    if dividend_yield is not None:
        if filters.min_dividend_yield is not None and dividend_yield < filters.min_dividend_yield:
            return False
        if filters.max_dividend_yield is not None and dividend_yield > filters.max_dividend_yield:
            return False

    return True


@cached(TTLCache(maxsize=1, ttl=3600))
def get_filter_options():
    """
    Returns a dictionary with:
      - "sectors": a sorted list of distinct sectors.
      - "sectorIndustryMapping": a dictionary mapping each sector to a sorted list of its industries.
    """
    aggregated_data = get_all_screener_data()
    sectorIndustryMapping = {}
    for stock in aggregated_data.values():
        info = stock.get("info", {})
        sector = info.get("sector")
        industry = info.get("industry")
        if sector:
            if sector not in sectorIndustryMapping:
                sectorIndustryMapping[sector] = set()
            if industry:
                sectorIndustryMapping[sector].add(industry)
    # Convert sets to sorted lists
    for sector in sectorIndustryMapping:
        sectorIndustryMapping[sector] = sorted(list(sectorIndustryMapping[sector]))
    sectors = sorted(sectorIndustryMapping.keys())
    return {"sectors": sectors, "sectorIndustryMapping": sectorIndustryMapping}
