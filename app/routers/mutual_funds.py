import asyncio
import json
import logging
import random
from functools import lru_cache
from typing import Any, Dict, List, Optional

import diskcache as dc
import pandas as pd
from fastapi import APIRouter, <PERSON><PERSON>, HTTPException, Query, Request
from pydantic import BaseModel

from app.routers.auth import get_user_data
from app.util.db.redis import get_redis_client
from app.util.faqs import generate_mutual_funds_faqs
from app.util.menu import menu
from app.util.templates import templates

# --- Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

router = APIRouter()

# Constants
REDIS_NAMESPACE = "MF_GROWW"
MF_GLOBAL_KEY = "all"
CACHE_DIR = "/var/tmp/mutual-funds-cache"
CACHE_TTL = 24 * 60 * 60  # 1 day in seconds

# Mapping for SEO-friendly URLs to filter parameters
SEO_FILTER_MAPPINGS = {
    # Category-only mappings
    "equity": {"category": "Equity"},
    "debt": {"category": "Debt"},
    "hybrid": {"category": "Hybrid"},
    "commodities": {"category": "Commodities"},
    "elss": {"category": "ELSS"},
    # Category + Sub-category mappings
    "equity-large-cap": {"category": "Equity", "sub_category": "Large Cap Fund"},
    "equity-mid-cap": {"category": "Equity", "sub_category": "Mid Cap Fund"},
    "equity-small-cap": {"category": "Equity", "sub_category": "Small Cap Fund"},
    "equity-multi-cap": {"category": "Equity", "sub_category": "Multi Cap Fund"},
    "equity-flexi-cap": {"category": "Equity", "sub_category": "Flexi Cap Fund"},
    "debt-liquid": {"category": "Debt", "sub_category": "Liquid Fund"},
    "debt-low-duration": {"category": "Debt", "sub_category": "Low Duration Fund"},
    "debt-corporate-bond": {"category": "Debt", "sub_category": "Corporate Bond Fund"},
    "hybrid-aggressive": {"category": "Hybrid", "sub_category": "Aggressive Hybrid Fund"},
    "hybrid-conservative": {"category": "Hybrid", "sub_category": "Conservative Hybrid Fund"},
    # Top-rated mappings
    "top-multi-cap": {"category": "Equity", "sub_category": "Multi Cap Fund", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    "top-flexi-cap": {"category": "Equity", "sub_category": "Flexi Cap Fund", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    "top-liquid": {"category": "Debt", "sub_category": "Liquid Fund", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    "top-low-duration": {"category": "Debt", "sub_category": "Low Duration Fund", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    "top-corporate-bond": {"category": "Debt", "sub_category": "Corporate Bond Fund", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    "top-aggressive-hybrid": {"category": "Hybrid", "sub_category": "Aggressive Hybrid Fund", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    "top-conservative-hybrid": {"category": "Hybrid", "sub_category": "Conservative Hybrid Fund", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    # Prebuilt screeners as top-level SEO-friendly URLs
    "high-growth-equity": {"category": "Equity", "rating": "4-5", "plan_type": "Direct", "sort_key": "rating", "sort_direction": "desc"},
    "stable-debt-funds": {"category": "Debt", "expense_ratio_range": "0-1", "risk": "Low", "sort_key": "expense_ratio", "sort_direction": "asc"},
    "balanced-hybrid-picks": {"category": "Hybrid", "fund_size_range": "1000-10000", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    "top-small-cap": {"category": "Equity", "sub_category": "Small Cap Fund", "rating": "4-5", "return3y_min": 15, "sort_key": "returns_3y", "sort_direction": "desc"},
    "top-mid-cap": {"category": "Equity", "sub_category": "Mid Cap Fund", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    "top-large-cap": {"category": "Equity", "sub_category": "Large Cap Fund", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    "best-3-year-returns": {"return3y_min": 12, "sort_key": "returns_3y", "sort_direction": "desc"},
    "best-1-year-returns": {"return1y_min": 10, "sort_key": "returns_1y", "sort_direction": "desc"},
    "low-volatility-debt": {"category": "Debt", "risk": "Low", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    "high-return-small-caps": {"category": "Equity", "sub_category": "Small Cap Fund", "return3y_min": 15, "plan_type": "Direct", "sort_key": "returns_3y", "sort_direction": "desc"},
    "volatility-large-caps": {"category": "Equity", "sub_category": "Large Cap Fund", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
    "short-term-debt": {"category": "Debt", "sub_category": "Low Duration Fund", "expense_ratio_range": "0-0.5", "sort_key": "expense_ratio", "sort_direction": "asc"},
    "aggressive-hybrid-growth": {"category": "Hybrid", "sub_category": "Aggressive Hybrid Fund", "return3y_min": 12, "plan_type": "Direct", "sort_key": "returns_3y", "sort_direction": "desc"},
    "elss-funds": {"category": "Equity", "sub_category": "ELSS", "rating": "4-5", "sort_key": "rating", "sort_direction": "desc"},
}

# SEO metadata for each filter
SEO_METADATA = {
    "equity": {
        "title": "Equity Mutual Funds - Top Equity Fund Picks",
        "description": "Explore top equity mutual funds to diversify your portfolio. Filter by NAV, fund size, and more to find the best equity funds.",
    },
    "debt": {
        "title": "Debt Mutual Funds - Best Debt Fund Options",
        "description": "Discover top debt mutual funds for stable returns. Filter by NAV, fund size, and AMC to find the best debt funds for you.",
    },
    "hybrid": {
        "title": "Hybrid Mutual Funds - Top Hybrid Funds",
        "description": "Find top hybrid mutual funds for balanced growth. Filter by NAV, fund size, and risk to choose the best hybrid funds.",
    },
    "commodities": {
        "title": "Commodities Mutual Funds - Top Picks",
        "description": "Explore commodities mutual funds for diversification. Filter by NAV and fund size to find the best commodity funds.",
    },
    "elss": {
        "title": "ELSS Mutual Funds - Top Tax-Saving Funds",
        "description": "Discover top ELSS mutual funds for tax savings and growth. Filter by NAV, fund size, and AMC to find the best ELSS funds.",
    },
    **{
        f"equity-{sub}": {
            "title": f"{sub.replace('-', ' ').title()} Equity Funds - Top Picks",
            "description": f"Find top {sub.replace('-', ' ')} equity funds. Filter by AMC, NAV, and fund size to choose the best funds.",
        }
        for sub in ["large-cap", "mid-cap", "small-cap", "multi-cap", "flexi-cap"]
    },
    **{
        f"debt-{sub}": {
            "title": f"{sub.replace('-', ' ').title()} Debt Funds - Top Picks",
            "description": f"Find top {sub.replace('-', ' ')} debt funds. Filter by AMC, NAV, and fund size to choose the best funds.",
        }
        for sub in ["liquid", "low-duration", "corporate-bond"]
    },
    **{
        f"hybrid-{sub}": {
            "title": f"{sub.replace('-', ' ').title()} Hybrid Funds - Top Picks",
            "description": f"Find top {sub.replace('-', ' ')} hybrid funds. Filter by AMC, NAV, and fund size to choose the best funds.",
        }
        for sub in ["aggressive", "conservative"]
    },
    **{
        f"top-{sub}": {
            "title": f"Top {sub.replace('-', ' ').title()} Mutual Funds - Best Picks",
            "description": f"Find the best {sub.replace('-', ' ')} funds with high ratings. Screen top funds by performance, NAV, and fund size.",
        }
        for sub in ["large-cap", "mid-cap", "small-cap", "multi-cap", "flexi-cap", "liquid", "low-duration", "corporate-bond", "aggressive-hybrid", "conservative-hybrid"]
    },
    "high-growth-equity": {
        "title": "High Growth Equity Mutual Funds",
        "description": "Explore top-rated equity mutual funds with direct plans for maximum growth.",
    },
    "stable-debt-funds": {
        "title": "Stable Debt Mutual Funds",
        "description": "Discover low-risk debt funds with low expense ratios for stable returns.",
    },
    "balanced-hybrid-picks": {
        "title": "Balanced Hybrid Mutual Funds",
        "description": "Find hybrid funds with strong ratings and medium fund size for balanced growth.",
    },
    "best-3-year-returns": {
        "title": "Mutual Funds with Best 3-Year Returns",
        "description": "Find funds with top 3-year returns across all categories.",
    },
    "best-1-year-returns": {
        "title": "Mutual Funds with Best 1-Year Returns",
        "description": "Explore funds with top 1-year returns across all categories.",
    },
    "top-mid-cap-funds": {
        "title": "Top Mid Cap Mutual Funds",
        "description": "Discover high-rated Mid Cap funds with strong performance.",
    },
    "low-volatility-debt": {
        "title": "Low Volatility Debt Mutual Funds",
        "description": "Find low-risk debt funds with high ratings for stable investments.",
    },
    "high-return-small-caps": {
        "title": "High Return Small Cap Mutual Funds",
        "description": "Explore Small Cap funds with strong 3-year returns and direct plans.",
    },
    "volatility-large-caps": {
        "title": "Stable Large Cap Mutual Funds",
        "description": "Discover stable Large Cap funds with high ratings.",
    },
    "short-term-debt": {
        "title": "Short-Term Debt Mutual Funds",
        "description": "Explore low-duration debt funds with minimal expenses.",
    },
    "aggressive-hybrid-growth": {
        "title": "Aggressive Hybrid Growth Funds",
        "description": "Find high-return aggressive hybrid funds with direct plans.",
    },
    "elss-funds": {
        "title": "ELSS Mutual Funds",
        "description": "Explore Equity Linked Savings Schemes with tax benefits and high ratings.",
    },
}


# Initialize diskcache
# all_funds_cache = dc.Cache(f"{CACHE_DIR}/all_funds")
# search_cache = dc.Cache(f"{CACHE_DIR}/search")
# filter_options_cache = dc.Cache(f"{CACHE_DIR}/filter_options")


# --- Models ---
class MutualFundFilter(BaseModel):
    scheme_name: Optional[str] = None
    category: Optional[str] = None
    sub_category: Optional[str] = None
    amc: Optional[str] = None
    min_nav: Optional[float] = None
    max_nav: Optional[float] = None
    min_aum: Optional[float] = None
    max_aum: Optional[float] = None
    rating: Optional[str] = None
    risk: Optional[str] = None
    plan_type: Optional[str] = None
    min_expense_ratio: Optional[float] = None
    max_expense_ratio: Optional[float] = None
    return3y_min: Optional[float] = None
    return1y_min: Optional[float] = None
    columns: Optional[List[str]] = None
    sort_key: Optional[str] = None
    sort_direction: Optional[str] = "asc"
    page: Optional[int] = 1
    limit: Optional[int] = 100


class SchemeInfoRequest(BaseModel):
    scheme_code: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None


class CalculationRequest(BaseModel):
    scheme_code: str
    units: Optional[float] = None
    balanced_units: Optional[float] = None
    monthly_sip: Optional[float] = None
    investment_months: Optional[int] = None


# --- Cache Manager & Data Fetching ---
class CacheManager:
    """Handle Redis and disk caching."""

    @staticmethod
    def get_client() -> Any:
        """Return the Redis client."""
        return get_redis_client()

    @staticmethod
    def get_redis(key: str) -> Optional[Any]:
        """Retrieve a value from Redis by key."""
        try:
            redis_client = CacheManager.get_client()
            logger.info(f"Retrieving data from Redis for key: {key}")
            data = redis_client.get(f"{REDIS_NAMESPACE}:{key}")
            if not data:
                return None
            parsed_data = json.loads(data)
            logger.info(f"Retrieved data for key {key} from Redis")
            return parsed_data
        except Exception as e:
            logger.error(f"Failed to retrieve {key} from Redis: {e}")
            return None


class MutualFundData:
    """Handle mutual fund data fetching and processing from Redis."""

    @staticmethod
    @lru_cache(maxsize=1)
    def fetch_all_funds() -> Dict[str, Dict[str, Any]]:
        """Fetch all mutual fund data, using diskcache-based caching and Redis as fallback."""
        # Fetch from Redis
        cached_data = CacheManager.get_redis(MF_GLOBAL_KEY)
        if cached_data:
            logger.info(f"Retrieved {len(cached_data)} funds from Redis")
            # Update key from groww_rating to rating
            for fund in cached_data.values():
                if "groww_rating" in fund or "logo_url" in fund:
                    amc_name = fund.get("fund_house", "")
                    if amc_name:
                        amc_name = amc_name.lower().replace(" ", "-")
                        fund["logo_url"] = f"https://assets.theaibull.com/images/mf/{amc_name}.png"
                    else:
                        fund["logo_url"] = f"https://assets.theaibull.com/images/mf/{fund['search_id']}.png"
                    fund["rating"] = fund.pop("groww_rating")
            return cached_data

        logger.error("No fund data found in Redis")
        return {}

    @staticmethod
    def filter_funds(filters: MutualFundFilter) -> tuple[List[Dict[str, Any]], int]:
        """Filter and sort funds based on provided criteria."""
        funds = MutualFundData.fetch_all_funds()
        if not funds:
            return [], 0

        matching_funds: List[Dict[str, Any]] = []
        for scheme_code, fund in funds.items():
            fund_with_code = fund.copy()
            fund_with_code["scheme_code"] = scheme_code

            if filters.category:
                category_list = [c.strip() for c in filters.category.split(",")]
                if fund.get("category") not in category_list:
                    continue
            if filters.sub_category:
                sub_list = [s.strip() for s in filters.sub_category.split(",")]
                if fund.get("sub_category") not in sub_list:
                    continue
            if filters.amc:
                amc_list = [a.strip() for a in filters.amc.split(",")]
                if fund.get("fund_house") not in amc_list:
                    continue
            if filters.rating:
                rating_list = []
                for part in filters.rating.split(","):
                    if "-" in part:
                        start, end = part.split("-")
                        rating_list.extend(range(int(start), int(end) + 1))
                    elif part:
                        rating_list.append(int(part))

                if fund.get("rating") not in rating_list:
                    continue
            if filters.plan_type:
                plan_list = [p.strip() for p in filters.plan_type.split(",")]
                if fund.get("plan_type") not in plan_list:
                    continue
            if filters.risk:
                risk_list = [r.strip() for r in filters.risk.split(",")]
                if fund.get("return_stats", [{}])[0].get("risk") not in risk_list:
                    continue
            if filters.scheme_name and filters.scheme_name.lower() not in str(fund.get("scheme_name")).lower():
                continue

            nav = fund.get("nav")
            if nav is not None:
                try:
                    nav_val = float(nav)
                    if filters.min_nav is not None and nav_val < filters.min_nav:
                        continue
                    if filters.max_nav is not None and nav_val > filters.max_nav:
                        continue
                except (ValueError, TypeError):
                    continue

            aum = fund.get("aum")
            if aum is not None:
                try:
                    aum_val = float(aum)
                    if filters.min_aum is not None and aum_val < filters.min_aum:
                        continue
                    if filters.max_aum is not None and aum_val > filters.max_aum:
                        continue
                except (ValueError, TypeError):
                    continue

            exp_ratio = fund.get("expense_ratio")
            if exp_ratio is not None:
                try:
                    er_val = float(exp_ratio)
                    if filters.min_expense_ratio is not None and er_val <= filters.min_expense_ratio:
                        continue
                    if filters.max_expense_ratio is not None and er_val >= filters.max_expense_ratio:
                        continue
                except (ValueError, TypeError):
                    continue

            # Filter by 3-year return
            return3y = fund.get("return_stats", [{}])[0].get("return3y")
            if filters.return3y_min is not None and return3y is not None:
                try:
                    return3y_val = float(return3y)
                    if return3y_val < filters.return3y_min:
                        continue
                except (ValueError, TypeError):
                    continue

            # Filter by 1-year return
            return1y = fund.get("return_stats", [{}])[0].get("return1y")
            if filters.return1y_min is not None and return1y is not None:
                try:
                    return1y_val = float(return1y)
                    if return1y_val < filters.return1y_min:
                        continue
                except (ValueError, TypeError):
                    continue

            matching_funds.append(fund_with_code)

        # Sorting
        if filters.sort_key:
            valid_keys = ["scheme_name", "nav", "aum", "category", "sub_category", "rating", "expense_ratio", "returns_1m", "returns_3m", "returns_6m", "returns_1y", "returns_3y", "returns_5y"]
            if filters.sort_key in valid_keys:
                numeric = filters.sort_key in ["nav", "aum", "returns_1m", "returns_3m", "returns_6m", "returns_1y", "returns_3y", "returns_5y"]
                reverse = filters.sort_direction.lower() == "desc"

                def sort_val(f: Dict[str, Any]):
                    if filters.sort_key.startswith("returns_"):
                        return_stats = f.get("return_stats", [{}])[0]
                        v = return_stats.get(filters.sort_key.replace("returns_", "return"))
                    else:
                        v = f.get(filters.sort_key)

                    if numeric:
                        try:
                            return float(v) if v is not None else float("-inf")
                        except (TypeError, ValueError):
                            return float("-inf")
                    return str(v).lower() if v is not None else ""

                matching_funds.sort(key=sort_val, reverse=reverse)
            else:
                logger.warning(f"Ignoring invalid sort_key: {filters.sort_key}")
        
        page = max(1, filters.page or 1)
        limit = max(1, min(filters.limit or 100, 100))  # Clamp limit to [1, 100]
        start = (page - 1) * limit
        end = start + limit
        paginated_funds = matching_funds[start:end]

        return paginated_funds, len(matching_funds)

    @staticmethod
    def get_filter_options_sync() -> Dict[str, Any]:
        """Synchronous version of get_filter_options for caching."""
        funds = MutualFundData.fetch_all_funds()
        if not funds:
            return {"categories": [], "fund_house_summary": {"count": 0, "fund_houses": {}}}

        rows = []
        for code, f in funds.items():
            rows.append(
                {
                    "fund_house": f.get("fund_house"),
                    "nav": f.get("nav"),
                    "date": f.get("nav_date"),
                    "category": f.get("category"),
                    "sub_category": f.get("sub_category"),
                }
            )

        df = pd.DataFrame(rows)

        summary: Dict[str, Any] = {}
        for fh, grp in df.groupby("fund_house"):
            navs = grp["nav"]
            dates = grp["date"]
            avg_nav = float(navs.mean(skipna=True)) if not navs.isna().all() else None
            latest_date = pd.to_datetime(dates, format="%d-%b-%Y", errors="coerce").max().strftime("%Y-%m-%d") if not dates.isna().all() else "N/A"
            summary[fh] = {
                "fund_count": len(grp),
                "latest_nav": avg_nav,
                "latest_date": latest_date,
            }

        category_counts = df.groupby("category").size().to_dict()
        cat_sub: Dict[str, List[str]] = {}
        for (cat, sub), _ in df.groupby(["category", "sub_category"]):
            if pd.notna(cat) and pd.notna(sub):
                cat_sub.setdefault(cat, []).append(sub)

        categories_list = []
        for cat, subs in cat_sub.items():
            categories_list.append({"name": cat, "subcategories": sorted(set(subs)), "fund_count": category_counts.get(cat, 0)})
        categories_list.sort(key=lambda x: x["name"])

        return {"categories": categories_list, "fund_house_summary": {"count": len(summary), "fund_houses": summary}}

    @staticmethod
    async def get_filter_options() -> Dict[str, Any]:
        """Get distinct categories and AMCs, running sync function in executor."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, MutualFundData.get_filter_options_sync)


def derive_sector_allocation(holdings):
    """
    Aggregates holdings by sector and returns a list of dictionaries with sector name and allocation.
    """
    if not holdings:
        return []

    sector_map = {}
    for holding in holdings:
        sector = holding.get("sector_name", "Unknown")
        allocation = float(holding.get("corpus_per", 0)) if holding.get("corpus_per") else 0
        if allocation > 0:
            sector_map[sector] = sector_map.get(sector, 0) + allocation

    return [{"name": sector_name, "allocation": allocation} for sector_name, allocation in sector_map.items()]


# --- In-Memory LRU Cache Helpers ---
@lru_cache(maxsize=128)
def _search_cached(filter_key: str) -> tuple[List[Dict[str, Any]], int]:
    return MutualFundData.filter_funds(MutualFundFilter.parse_raw(filter_key))


@lru_cache(maxsize=1)
def _get_schemes() -> List[Dict[str, Any]]:
    funds = MutualFundData.fetch_all_funds()
    schemes: List[Dict[str, Any]] = []
    for code, f in funds.items():
        copy_f = f.copy()
        copy_f["scheme_code"] = code
        schemes.append(copy_f)
    return schemes


# --- Routes ---
@router.get("")
async def get_screener(
    request: Request,
    access_token: Optional[str] = Cookie(None),
    page: int = Query(1, ge=1),
):
    filter_options = await MutualFundData.get_filter_options()
    default_filters = MutualFundFilter(
        columns=["logo_url", "scheme_name", "category", "sub_category", "aum", "nav", "rating", "scheme_code", "return_stats"],
        page=page,
        limit=100
    )
    search_result = await search_funds(default_filters)
    funds = search_result["funds"]
    total = search_result["count"]

    # Define default SEO metadata for the root screener page
    seo_metadata = {
        "title": "Mutual Funds - Explore Top Investment Options",
        "description": "Discover the best mutual funds to diversify your portfolio. Filter by category, NAV, AUM, and more to find top-performing funds.",
    }

    # Generate FAQs for the root screener page
    faqs = generate_mutual_funds_faqs(path_param="mutual-funds", seo_filter_mappings=SEO_FILTER_MAPPINGS, seo_metadata=seo_metadata)

    context = {
        "request": request,
        **menu,
        **get_user_data(access_token),
        "categories": filter_options["categories"],
        "fund_house_summary": filter_options["fund_house_summary"],
        "preset_filters": {},
        "initial_funds": funds,
        "total_funds": total,
        "seo_title": seo_metadata["title"],
        "seo_description": seo_metadata["description"],
        "canonical_url": f"https://theaibull.com/mutual-funds",
        "total_pages": (total + 100 - 1) // 100 if total else 1,
        "current_page": page,
        "limit": 100,
        "faqs": faqs,
        "path_param": "mutual-funds"
    }
    return templates.TemplateResponse("/mutual-funds/main.html", context)


@router.post("/search")
async def search_funds(filters: MutualFundFilter) -> Dict[str, Any]:
    filter_dict = filters.dict()
    filter_key = json.dumps(
        {
            "scheme_name": filter_dict.get("scheme_name"),
            "category": filter_dict.get("category"),
            "sub_category": filter_dict.get("sub_category"),
            "amc": filter_dict.get("amc"),
            "min_nav": filter_dict.get("min_nav"),
            "max_nav": filter_dict.get("max_nav"),
            "min_aum": filter_dict.get("min_aum"),
            "max_aum": filter_dict.get("max_aum"),
            "rating": filter_dict.get("rating"),
            "risk": filter_dict.get("risk"),
            "plan_type": filter_dict.get("plan_type"),
            "min_expense_ratio": filter_dict.get("min_expense_ratio"),
            "max_expense_ratio": filter_dict.get("max_expense_ratio"),
            "return3y_min": filter_dict.get("return3y_min"),
            "return1y_min": filter_dict.get("return1y_min"),
            "sort_key": filter_dict.get("sort_key"),
            "sort_direction": filter_dict.get("sort_direction"),
            "page": filter_dict.get("page"),
            "limit": filter_dict.get("limit"),
        },
        sort_keys=True,
    )

    funds, total = _search_cached(filter_key)

    if filters.columns:
        projected: List[Dict[str, Any]] = []
        for f in funds:
            p: Dict[str, Any] = {}
            for col in filters.columns:
                key = "scheme_name" if col == "name" else col
                p[key] = f.get("scheme_name" if col == "name" else col, "")
            projected.append(p)
        funds = projected

    return {"count": total, "funds": funds}


@router.get("/filter-data")
async def get_filter_data(path_param: Optional[str] = Query(None)) -> Dict[str, Any]:
    """API route to fetch filter-related data for the mutual funds screener."""
    try:
        # Fetch filter options (categories and fund house summary)
        filter_options = await MutualFundData.get_filter_options()

        # Define preset filters
        preset_filters: Dict[str, Optional[Any]] = {
            "category": "",
            "sub_category": "",
            "rating": None,
            "risk": None,
            "plan_type": None,
            "return3y_min": None,
            "return1y_min": None,
            "expense_ratio_range": None,
            "fund_size_range": None,
            "sort_key": None,
            "sort_direction": "asc",
        }

        # If path_param is provided and exists in SEO_FILTER_MAPPINGS, use those filters
        if path_param and path_param in SEO_FILTER_MAPPINGS:
            filter_params = SEO_FILTER_MAPPINGS[path_param]
            preset_filters.update({
                "category": filter_params.get("category", ""),
                "sub_category": filter_params.get("sub_category", ""),
                "rating": filter_params.get("rating", None),
                "risk": filter_params.get("risk", None),
                "plan_type": filter_params.get("plan_type", None),
                "return3y_min": filter_params.get("return3y_min", None),
                "return1y_min": filter_params.get("return1y_min", None),
                "expense_ratio_range": filter_params.get("expense_ratio_range", None),
                "fund_size_range": filter_params.get("fund_size_range", None),
                "sort_key": filter_params.get("sort_key", None),
                "sort_direction": filter_params.get("sort_direction", "asc"),
            })

        return {
            "preset_filters": preset_filters,
            "categories": filter_options["categories"],
            "fund_house_summary": filter_options["fund_house_summary"],
        }
    except Exception as e:
        logger.error(f"Error fetching filter data: {e}")
        raise HTTPException(status_code=500, detail="An error occurred while fetching filter data.")


@router.get("/compare")
@router.get("/compare/{funds_path:path}")
async def compare_funds(
    request: Request,
    funds_path: str = "",
    access_token: Optional[str] = Cookie(None),
):
    result = await process_fund_comparison(funds_path)
    context = {
        "request": request,
        **menu,  # Assuming menu is defined elsewhere
        **result,
        **get_user_data(access_token),
    }
    return templates.TemplateResponse("/mutual-funds/compare.html", context)


async def process_fund_comparison(funds_path: str) -> Dict[str, Any]:
    """Shared logic for processing mutual fund comparison data"""
    # Construct the canonical URL
    canonical_url = f"https://theaibull.com/mutual-funds/compare" if not funds_path or funds_path == "/" else f"https://theaibull.com/mutual-funds/compare/{funds_path}"

    # Handle empty path
    if not funds_path or funds_path == "/":
        return {
            "funds": [],
            "error": "Add at least two mutual funds to compare.",
            "recommended_funds": [],
            "seo_meta_data": {"canonical_url": canonical_url},
        }

    # Split and validate scheme codes
    valid_schemes = [code.strip() for code in funds_path.split("/") if code.strip()]

    # Validate number of funds
    if len(valid_schemes) > 5:
        logger.warning(f"Too many scheme codes provided: {len(valid_schemes)}")
        raise HTTPException(status_code=400, detail="Cannot compare more than 5 mutual funds.")

    # Fetch fund data for each scheme code
    funds = []
    all_funds = MutualFundData.fetch_all_funds()
    seen_codes = set()  # Track duplicates

    for index, scheme_code in enumerate(valid_schemes):
        if scheme_code in seen_codes:
            logger.warning(f"Duplicate scheme code ignored: {scheme_code}")
            continue  # Skip duplicates
        fund_data = all_funds.get(scheme_code)
        if not fund_data:
            logger.warning(f"Scheme {scheme_code} not found")
            continue

        fund_data = {**fund_data, "scheme_code": scheme_code}
        funds.append((index, fund_data))  # Store slot index with fund data
        seen_codes.add(scheme_code)

    # Sort funds by slot index to maintain order
    funds = [fund_data for _, fund_data in sorted(funds, key=lambda x: x[0])]

    # Validate minimum funds
    if len(valid_schemes) < 2:
        return {
            "funds": funds,
            "error": "Add at least two mutual funds to compare.",
            "recommended_funds": [],
            "seo_meta_data": {"canonical_url": canonical_url},
        }

    # Fetch recommended funds
    recommended_funds = []
    if funds:
        # Get the primary category (use the first fund's category, assuming similar categories)
        primary_category = funds[0].get("category", "Equity")  # Fallback to 'Equity' if not found
        # Filter funds in the same category
        category_funds = [{**fund, "scheme_code": code} for code, fund in all_funds.items() if fund.get("category") == primary_category]
        # Sort by rating (descending) and 3-year returns as a tiebreaker
        category_funds.sort(key=lambda x: (float(x.get("rating", 0) or 0), float(x.get("return_stats", [{}])[0].get("return3y", 0) or 0)), reverse=True)
        # Find indices of selected funds
        selected_codes = {fund["scheme_code"] for fund in funds}
        indices = [i for i, fund in enumerate(category_funds) if fund["scheme_code"] in selected_codes]
        if indices:
            min_index = min(indices)
            max_index = max(indices)
            # Select 5 before and 5 after, excluding selected funds
            start_index = max(0, min_index - 5)
            end_index = min(len(category_funds), max_index + 6)  # +6 to account for up to 5 funds after
            recommended_funds = [fund for fund in category_funds[start_index:end_index] if fund["scheme_code"] not in selected_codes][:10]
            # Ensure exactly 10 funds (fill with others if needed)
            if len(recommended_funds) < 10:
                additional_funds = [fund for fund in category_funds if fund["scheme_code"] not in selected_codes and fund not in recommended_funds][: 10 - len(recommended_funds)]
                recommended_funds.extend(additional_funds)

    return {
        "funds": funds,
        "error": None,
        "recommended_funds": recommended_funds,
        "seo_meta_data": {"canonical_url": canonical_url},
    }


# New API route returning only funds
@router.get("/api/compare")
@router.get("/api/compare/{funds_path:path}")
async def compare_funds_api(
    funds_path: str = ""
) -> Dict[str, Any]:
    result = await process_fund_comparison(funds_path)
    if result["error"]:
        return {"error": result["error"]}
    return {"funds": result["funds"]}


@router.get("/schemes")
async def get_schemes_endpoint() -> List[Dict[str, Any]]:
    """
    Return every scheme’s full details (including scheme_code).
    Cached in-process via lru_cache.
    """
    return _get_schemes()


@router.get("/{path_param:path}")
async def get_mutual_fund_data(
    request: Request,
    path_param: str = "",
    access_token: Optional[str] = Cookie(None),
):
    try:
        # Normalize path parameter (remove leading/trailing slashes)
        path_param = path_param.strip("/").lower()

        # Initialize variables
        page = 1  # Default page
        actual_path_param = path_param
        is_default_screener = False

        # Handle default screener cases: "" (page 1) or a number (e.g., "5")
        if not path_param or path_param.isdigit():
            is_default_screener = True
            if path_param.isdigit():
                page = int(path_param)
            actual_path_param = "mutual-funds"
        # Handle SEO filter with page (e.g., "stable-debt-funds/2")
        elif "/" in path_param:
            parts = path_param.rsplit("/", 1)
            try:
                page = int(parts[1])
                actual_path_param = parts[0]
            except ValueError:
                # If the last part isn't an integer, treat the whole path as path_param
                page = 1
                actual_path_param = path_param

        # Ensure page is at least 1
        page = max(1, page)

        # Check if actual_path_param is an SEO filter slug or default screener
        if is_default_screener or actual_path_param in SEO_FILTER_MAPPINGS:
            # Handle default screener or SEO filter
            filter_params = SEO_FILTER_MAPPINGS.get(actual_path_param, {})
            preset_filters = {
                "category": filter_params.get("category", ""),
                "sub_category": filter_params.get("sub_category", ""),
                "rating": filter_params.get("rating", None),
                "risk": filter_params.get("risk", None),
                "plan_type": filter_params.get("plan_type", None),
                "return3y_min": filter_params.get("return3y_min", None),
                "return1y_min": filter_params.get("return1y_min", None),
                "expense_ratio_range": filter_params.get("expense_ratio_range", None),
                "fund_size_range": filter_params.get("fund_size_range", None),
                "sort_key": filter_params.get("sort_key", None),
                "sort_direction": filter_params.get("sort_direction", "asc"),
            }

            # Fetch filter options
            filter_options = await MutualFundData.get_filter_options()
            # Fetch funds for the preset filters
            filters = MutualFundFilter(
                category=preset_filters["category"],
                sub_category=preset_filters["sub_category"].removesuffix(" Fund"),
                rating=preset_filters["rating"],
                risk=preset_filters["risk"],
                plan_type=preset_filters["plan_type"],
                return3y_min=preset_filters["return3y_min"],
                return1y_min=preset_filters["return1y_min"],
                min_expense_ratio=float(preset_filters["expense_ratio_range"].split("-")[0]) if preset_filters.get("expense_ratio_range") else None,
                max_expense_ratio=float(preset_filters["expense_ratio_range"].split("-")[1]) if preset_filters.get("expense_ratio_range") else None,
                min_aum=float(preset_filters["fund_size_range"].split("-")[0]) if preset_filters.get("fund_size_range") else None,
                max_aum=float(preset_filters["fund_size_range"].split("-")[1]) if preset_filters.get("fund_size_range") else None,
                sort_key=preset_filters["sort_key"],
                sort_direction=preset_filters["sort_direction"],
                columns=["logo_url", "scheme_name", "category", "sub_category", "nav", "aum", "rating", "scheme_code", "return_stats"],
                page=page,
                limit=100
            )
            funds, total = MutualFundData.filter_funds(filters)
            seo_metadata = SEO_METADATA.get(
                actual_path_param,
                {
                    "title": "Mutual Funds - Explore Top Investment Options",
                    "description": "Discover the best mutual funds to diversify your portfolio. Filter by category, NAV, AUM, and more to find top-performing funds.",
                },
            )

            # Generate FAQs for SEO filter or default screener
            faqs = generate_mutual_funds_faqs(actual_path_param, SEO_FILTER_MAPPINGS, seo_metadata)

            # Special case for the main screener page or SEO filter pages
            if page == 1:
                canonical_url = f"https://theaibull.com/mutual-funds" if actual_path_param == "mutual-funds" else f"https://theaibull.com/mutual-funds/{actual_path_param}"
            else:
                canonical_url = f"https://theaibull.com/mutual-funds" if actual_path_param == "mutual-funds" else f"https://theaibull.com/mutual-funds/{actual_path_param}"
                canonical_url = f"{canonical_url}/{page}"
            canonical_url = canonical_url.rstrip("/")

            context = {
                "request": request,
                **menu,
                **get_user_data(access_token),
                "categories": filter_options["categories"],
                "fund_house_summary": filter_options["fund_house_summary"],
                "preset_filters": preset_filters,
                "seo_title": seo_metadata["title"],
                "seo_description": seo_metadata["description"],
                "canonical_url": canonical_url,
                "total_pages": (total + 100 - 1) // 100 if total else 1,
                "current_page": page,
                "limit": 100,
                "initial_funds": funds,
                "total_funds": total,
                "faqs": faqs,
                "path_param": actual_path_param
            }
            return templates.TemplateResponse("/mutual-funds/main.html", context)
        else:
            # Handle scheme code details (e.g., sbi-magnum-midcap-fund-direct-growth)
            scheme_code = actual_path_param.strip()
            user_data = get_user_data(access_token)

            all_funds = MutualFundData.fetch_all_funds()
            fund_data = all_funds.get(scheme_code)
            if not fund_data:
                logger.error(f"Scheme {scheme_code} not found")
                raise HTTPException(status_code=404, detail=f"Scheme {scheme_code} not found")
            fund_data = {**fund_data, "scheme_code": scheme_code}

            # --- Generate SEO Filter Suggestions ---
            filter_keys = list(SEO_FILTER_MAPPINGS.keys())

            category = fund_data.get("category", "").lower().replace(" ", "-")
            sub_category = fund_data.get("sub_category", "").lower().replace(" ", "-")
            combined_key = f"{category}-{sub_category}" if sub_category else category

            # Get current index in filter list
            current_index = filter_keys.index(combined_key) if combined_key in filter_keys else filter_keys.index(category) if category in filter_keys else 0

            # Select nearby filter keys for SEO suggestions
            selected_filter_keys = filter_keys[max(0, current_index - 4) : current_index] + filter_keys[current_index + 1 : min(len(filter_keys), current_index + 5)]

            # Fill remaining slots (up to 8) if needed
            while len(selected_filter_keys) < 8 and (start := max(0, current_index - 5)) > 0:
                if start > 0 and filter_keys[start] not in selected_filter_keys:
                    selected_filter_keys.insert(0, filter_keys[start])
                elif current_index + len(selected_filter_keys) + 1 < len(filter_keys):
                    selected_filter_keys.append(filter_keys[current_index + len(selected_filter_keys) + 1])
                else:
                    break

            selected_filter_keys = selected_filter_keys[:8]

            random_filters = [
                {
                    "url": key,
                    "title": SEO_METADATA[key]["title"].replace(" - Top Picks", "").replace(" - Best Picks", ""),
                    "description": SEO_METADATA[key]["description"],
                }
                for key in selected_filter_keys
            ]

            # --- Fetch Funds in Same Category ---
            category_name = fund_data.get("category")
            category_funds = [{**fund, "scheme_code": code} for code, fund in all_funds.items() if fund.get("category") == category_name]

            # Sort by integer rating (default to 0 if missing)
            category_funds.sort(key=lambda x: int(x.get("rating") or 0))

            # Get nearby interested funds
            current_index = next((i for i, fund in enumerate(category_funds) if fund["scheme_code"] == scheme_code), -1)
            interested_funds = []

            if current_index != -1:
                funds_before = category_funds[max(0, current_index - 5) : current_index]
                funds_after = category_funds[current_index + 1 : current_index + 6]
                interested_funds = (funds_before + funds_after)[:10]

            interested_funds = [
                {
                    "url": fund["scheme_code"],
                    "title": fund.get("scheme_name", "Unknown Fund"),
                    "description": fund.get("sub_category", "Explore this mutual fund."),
                }
                for fund in interested_funds
            ]

            # Precompute sector allocation
            sector_allocation = derive_sector_allocation(fund_data.get("holdings", []))

            # --- Render Template ---
            context = {
                "request": request,
                **menu,
                **user_data,
                "scheme_code": scheme_code,
                "fund_data": fund_data,
                "random_filters": random_filters,
                "interested_funds": interested_funds,
                "sector_allocation": sector_allocation,
            }
            return templates.TemplateResponse("/mutual-funds/scheme-details.html", context)

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"Error processing mutual fund data for {path_param}: {e}")
        raise HTTPException(status_code=500, detail="An error occurred while loading the mutual funds page.")
