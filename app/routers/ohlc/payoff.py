# payoff.py

from datetime import datetime
from typing import Any, Dict


def calculate_payoff(trade_type: str, signals: Dict[str, Any], processed_data: Dict[str, Any], sl_coef: float = 2.0) -> Dict[str, Any]:
    """
    Simulates trade outcomes based on trade signals in the data.

    Args:
        trade_type: The type of trade ("Long" or "Short")
        signals: Dictionary containing trade signals
        processed_data: Dictionary containing OHLC data
        sl_coef: Stop loss coefficient to use when calculating stop loss levels

    Returns:
        A dictionary with:
        - 'trades': a list of trade records with 'Time', 'Signal', 'Score',
          'ProfitPoints', 'Duration', and 'Status'
        - 'summary': a mapping of entry date (YYYY-MM-DD) to the success percentage.
    """
    sorted_keys = sorted(processed_data.keys(), key=lambda k: int(k))
    trades = []

    # Simulate each trade signal.
    for i, ts in enumerate(sorted_keys):
        # Proceed only if there is a signal candle for the current timestamp
        if ts not in signals:
            continue
        # Use trade_type parameter instead of extracting from signals
        record = processed_data[ts]
        signal = trade_type  # Trade direction provided as parameter
        if signal not in ["Long", "Short"]:
            print("Invalid trade type. Skipping...")
            continue

        entry_time = ts
        entry_price = float(record["c"])
        target = record.get("target")
        stop_loss = record.get("stop_loss")

        # Calculate target and stop_loss if not provided
        if target is None or stop_loss is None:
            # Use ATR if available for more dynamic calculations
            atr = record.get("atr")

            if atr is not None and isinstance(atr, (int, float)) and atr > 0:
                atr_value = float(atr)
                # Use ATR with sl_coef to calculate stop loss and target
                if signal == "Long":
                    if stop_loss is None:
                        stop_loss = entry_price - (atr_value * sl_coef)
                    if target is None:
                        # Use a 1.5:1 reward-to-risk ratio
                        risk = entry_price - stop_loss
                        target = entry_price + (risk * 1.5)
                else:  # Short
                    if stop_loss is None:
                        stop_loss = entry_price + (atr_value * sl_coef)
                    if target is None:
                        # Use a 1.5:1 reward-to-risk ratio
                        risk = stop_loss - entry_price
                        target = entry_price - (risk * 1.5)
            else:
                # Simple percentage-based calculation if ATR is not available
                if signal == "Long":
                    if stop_loss is None:
                        stop_loss = entry_price * (1 - (0.01 * sl_coef))  # Use sl_coef% as stop distance
                    if target is None:
                        # Use a 1.5:1 reward-to-risk ratio
                        risk = entry_price - stop_loss
                        target = entry_price + (risk * 1.5)
                else:  # Short
                    if stop_loss is None:
                        stop_loss = entry_price * (1 + (0.01 * sl_coef))  # Use sl_coef% as stop distance
                    if target is None:
                        # Use a 1.5:1 reward-to-risk ratio
                        risk = stop_loss - entry_price
                        target = entry_price - (risk * 1.5)

        # Skip if we still don't have target or stop_loss
        if target is None or stop_loss is None:
            continue

        outcome = "Not Closed"
        exit_time = None
        exit_price = None

        # Iterate over subsequent candles.
        for j in range(i + 1, len(sorted_keys)):
            future_ts = sorted_keys[j]
            future_record = processed_data[future_ts]
            high = float(future_record["h"])
            low = float(future_record["l"])

            if signal == "Long":
                if low <= stop_loss:
                    outcome = "Stop Loss Hit"
                    exit_time = future_ts
                    exit_price = stop_loss
                    break
                if high >= target:
                    outcome = "Booked Profit"
                    exit_time = future_ts
                    exit_price = target
                    break
            elif signal == "Short":
                if high >= stop_loss:
                    outcome = "Stop Loss Hit"
                    exit_time = future_ts
                    exit_price = stop_loss
                    break
                if low <= target:
                    outcome = "Booked Profit"
                    exit_time = future_ts
                    exit_price = target
                    break

        if exit_time is None:
            exit_time = sorted_keys[-1]
            exit_price = float(processed_data[exit_time]["c"])

        entry_ts = int(entry_time)
        exit_ts = int(exit_time)
        duration = exit_ts - entry_ts
        profit_points = (exit_price - entry_price) if signal == "Long" else (entry_price - exit_price)

        trades.append({"Time": entry_time, "Signal": signal, "Score": record.get("signal_score", record.get("score")), "ProfitPoints": profit_points, "Duration": duration, "Status": outcome})

    # Aggregate success percentage by entry date.
    trades_by_date = {}
    for trade in trades:
        ts = trade["Time"]
        dt = datetime.fromtimestamp(int(ts))
        date_str = dt.strftime("%Y-%m-%d")
        trades_by_date.setdefault(date_str, []).append(trade)

    summary = {}
    for date_str, trades_list in trades_by_date.items():
        closed_trades = [t for t in trades_list if t["Status"] in ["Booked Profit", "Stop Loss Hit"]]
        if closed_trades:
            success_count = sum(1 for t in closed_trades if t["Status"] == "Booked Profit")
            percentage = (success_count / len(closed_trades)) * 100
        else:
            percentage = None
        summary[date_str] = percentage

    return {"trades": trades, "summary": summary}
