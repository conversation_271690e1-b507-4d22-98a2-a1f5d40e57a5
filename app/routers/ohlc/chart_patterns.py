import pandas as pd  # Data manipulation and analysis library
import numpy as np  # Numerical operations library
import traceback  # For printing stack traces during error handling
from typing import Dict, Any, List  # Type hinting imports
from patternpy.tradingpatterns import detect_head_shoulder, detect_multiple_tops_bottoms, detect_triangle_pattern, detect_double_top_bottom, calculate_support_resistance


def detect_wedge_custom(df: pd.DataFrame, window: int = 3) -> pd.DataFrame:
    """
    Custom implementation for detecting wedge patterns (Up and Down).

    Args:
        df (pd.DataFrame): DataFrame with 'High' and 'Low' price columns.
        window (int): The rolling window size for trend calculation.

    Returns:
        pd.DataFrame: The input DataFrame with an added 'wedge_pattern' column
                      indicating 'Wedge Up' or 'Wedge Down' where detected.
    """
    # Define the rolling window size
    roll_window = window
    # Calculate rolling maximum of High prices
    df["high_roll_max"] = df["High"].rolling(window=roll_window).max()
    # Calculate rolling minimum of Low prices
    df["low_roll_min"] = df["Low"].rolling(window=roll_window).min()

    # Calculate the trend of High prices over the window (1 for up, -1 for down, 0 for flat)
    # Uses numpy arrays to handle potential negative indexing issues in lambda with rolling apply
    df["trend_high"] = (
        df["High"].rolling(window=roll_window).apply(lambda x: 1 if (np.array(x)[-1] - np.array(x)[0]) > 0 else -1 if (np.array(x)[-1] - np.array(x)[0]) < 0 else 0, raw=True)
    )  # raw=True for performance
    # Calculate the trend of Low prices over the window
    df["trend_low"] = (
        df["Low"].rolling(window=roll_window).apply(lambda x: 1 if (np.array(x)[-1] - np.array(x)[0]) > 0 else -1 if (np.array(x)[-1] - np.array(x)[0]) < 0 else 0, raw=True)
    )  # raw=True for performance

    # Define conditions for a Wedge Up pattern:
    # - Current high is contained within the previous window's high/low range
    # - Current low is contained within the previous window's high/low range
    # - Both high and low prices are trending upwards
    mask_wedge_up = (df["high_roll_max"] >= df["High"].shift(1)) & (df["low_roll_min"] <= df["Low"].shift(1)) & (df["trend_high"] == 1) & (df["trend_low"] == 1)

    # Define conditions for a Wedge Down pattern:
    # - Current high is contained within the previous window's high/low range
    # - Current low is contained within the previous window's high/low range
    # - Both high and low prices are trending downwards
    mask_wedge_down = (df["high_roll_max"] <= df["High"].shift(1)) & (df["low_roll_min"] >= df["Low"].shift(1)) & (df["trend_high"] == -1) & (df["trend_low"] == -1)

    # Initialize the 'wedge_pattern' column with empty strings
    df["wedge_pattern"] = ""
    # Apply the masks to label the patterns in the DataFrame
    df.loc[mask_wedge_up, "wedge_pattern"] = "Wedge Up"
    df.loc[mask_wedge_down, "wedge_pattern"] = "Wedge Down"

    # Return the DataFrame with the added pattern column
    return df


def detect_channel_custom(df: pd.DataFrame, window: int = 3) -> pd.DataFrame:
    """
    Custom implementation for detecting channel patterns (Up and Down).

    Args:
        df (pd.DataFrame): DataFrame with 'High' and 'Low' price columns.
        window (int): The rolling window size for trend and range calculation.

    Returns:
        pd.DataFrame: The input DataFrame with an added 'channel_pattern' column
                      indicating 'Channel Up' or 'Channel Down' where detected.
    """
    # Define the rolling window size
    roll_window = window
    # Define a factor to determine the maximum allowed range relative to the average price for a channel
    channel_range = 0.1
    # Calculate rolling maximum of High prices
    df["high_roll_max"] = df["High"].rolling(window=roll_window).max()
    # Calculate rolling minimum of Low prices
    df["low_roll_min"] = df["Low"].rolling(window=roll_window).min()

    # Calculate the trend of High prices over the window (1 for up, -1 for down, 0 for flat)
    df["trend_high"] = df["High"].rolling(window=roll_window).apply(lambda x: 1 if (np.array(x)[-1] - np.array(x)[0]) > 0 else -1 if (np.array(x)[-1] - np.array(x)[0]) < 0 else 0, raw=True)
    # Calculate the trend of Low prices over the window
    df["trend_low"] = df["Low"].rolling(window=roll_window).apply(lambda x: 1 if (np.array(x)[-1] - np.array(x)[0]) > 0 else -1 if (np.array(x)[-1] - np.array(x)[0]) < 0 else 0, raw=True)

    # Calculate the average price within the rolling window for range comparison
    avg_price = (df["high_roll_max"] + df["low_roll_min"]) / 2
    # Calculate the price range within the rolling window
    price_range = df["high_roll_max"] - df["low_roll_min"]

    # Define conditions for a Channel Up pattern:
    # - Current high/low are within the previous window's range
    # - The price range is relatively narrow (within channel_range * average price)
    # - Both high and low prices are trending upwards
    mask_channel_up = (
        (df["high_roll_max"] >= df["High"].shift(1))
        & (df["low_roll_min"] <= df["Low"].shift(1))
        & (price_range <= channel_range * avg_price)  # Check if range is narrow
        & (df["trend_high"] == 1)
        & (df["trend_low"] == 1)
    )

    # Define conditions for a Channel Down pattern:
    # - Current high/low are within the previous window's range
    # - The price range is relatively narrow
    # - Both high and low prices are trending downwards
    mask_channel_down = (
        (df["high_roll_max"] <= df["High"].shift(1))
        & (df["low_roll_min"] >= df["Low"].shift(1))
        & (price_range <= channel_range * avg_price)  # Check if range is narrow
        & (df["trend_high"] == -1)
        & (df["trend_low"] == -1)
    )

    # Initialize the 'channel_pattern' column
    df["channel_pattern"] = ""
    # Apply the masks to label the patterns
    df.loc[mask_channel_up, "channel_pattern"] = "Channel Up"
    df.loc[mask_channel_down, "channel_pattern"] = "Channel Down"

    # Return the DataFrame with the added pattern column
    return df


def find_chart_patterns(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Identify chart patterns in price data using the PatternPy library.

    Args:
        data (Dict[str, Any]): A dictionary where keys are timestamps (as strings)
                               and values are dictionaries containing OHLC data
                               (e.g., {'o': open, 'h': high, 'l': low, 'c': close}).

    Returns:
        Dict[str, Any]: The input dictionary updated with detected chart patterns
                        under the 'p' key and support/resistance levels.
                        Returns the original data if an error occurs or if there's
                        insufficient data.
    """
    # Sort timestamps numerically to ensure correct order for time series analysis
    sorted_keys = sorted(data.keys(), key=lambda k: int(k))

    # Check if there are enough data points to perform pattern detection (minimum 30 bars)
    if len(sorted_keys) < 30:  # Need enough bars to detect patterns
        print("Not enough data points for chart pattern detection (< 30).")  # Added log
        return data

    try:
        # Prepare data in a pandas DataFrame format required by PatternPy
        df_data = {
            "timestamp": [int(k) for k in sorted_keys],
            "Open": [data[k]["o"] for k in sorted_keys],
            "High": [data[k]["h"] for k in sorted_keys],
            "Low": [data[k]["l"] for k in sorted_keys],
            "Close": [data[k]["c"] for k in sorted_keys],
        }
        df = pd.DataFrame(df_data)

        # Ensure OHLC columns are numeric, converting errors to NaN
        for col in ["Open", "High", "Low", "Close"]:
            df[col] = pd.to_numeric(df[col], errors="coerce")

        # Drop rows with NaN in essential columns to avoid errors in calculations
        df.dropna(subset=["Open", "High", "Low", "Close"], inplace=True)
        if df.empty:
            print("DataFrame became empty after dropping NaN values.")  # Added log
            return data  # Return original data if df is empty

        # Define the names of the pattern columns to be added
        pattern_columns = ["head_shoulder_pattern", "multiple_top_bottom_pattern", "triangle_pattern", "wedge_pattern", "channel_pattern", "double_pattern"]
        # Pre-initialize pattern columns with empty strings to avoid potential dtype issues later
        for col in pattern_columns:
            df[col] = ""

        # Apply various pattern detection functions from the PatternPy library
        # Wrap each call in a try-except block to handle potential errors gracefully
        try:
            df = detect_head_shoulder(df.copy())  # Use copy to avoid modifying df inplace within function if it does so
        except Exception as e:
            print(f"Error during detect_head_shoulder: {e}")
        try:
            df = detect_multiple_tops_bottoms(df.copy())
        except Exception as e:
            print(f"Error during detect_multiple_tops_bottoms: {e}")
        try:
            df = detect_triangle_pattern(df.copy())
        except Exception as e:
            print(f"Error during detect_triangle_pattern: {e}")
        try:
            # Use custom implementations for channel and wedge detection
            df = detect_channel_custom(df.copy())
        except Exception as e:
            print(f"Error during detect_channel_custom: {e}")
        try:
            df = detect_wedge_custom(df.copy())
        except Exception as e:
            print(f"Error during detect_wedge_custom: {e}")
        try:
            df = detect_double_top_bottom(df.copy())
        except Exception as e:
            print(f"Error during detect_double_top_bottom: {e}")
        try:
            # Calculate support and resistance levels
            df = calculate_support_resistance(df.copy())
        except Exception as e:
            print(f"Error during calculate_support_resistance: {e}")

        # Create a dictionary to store detected patterns, keyed by timestamp
        chart_patterns_by_ts: Dict[str, List[str]] = {}

        # Create timestamp_str column once for all processing - optimized to avoid creating it twice
        if "timestamp" in df.columns:
            df["timestamp_str"] = df["timestamp"].astype(str)
        else:
            print("Timestamp column missing after pattern detection.")  # Added log
            # Attempt to recover or return; here we return original data
            # This case might indicate a severe issue in one of the detection functions
            return data

        # Iterate through each defined pattern column name
        for pattern_col in pattern_columns:
            # Check if the pattern column actually exists in the DataFrame
            if pattern_col in df.columns:
                # Create a boolean mask to find rows where this pattern is present
                # Checks for non-null/NaN and non-empty string values
                mask = df[pattern_col].notna() & (df[pattern_col] != "")

                # If any rows match the mask for the current pattern
                if mask.any():
                    # Select the timestamp string and the pattern value for the matching rows
                    patterns_found = df.loc[mask, ["timestamp_str", pattern_col]]

                    # Iterate through the (timestamp_str, pattern_name) pairs found
                    for ts, pattern_name in patterns_found.values:
                        # Use setdefault to initialize an empty list if the timestamp key doesn't exist,
                        # then append the found pattern name to the list for that timestamp.
                        chart_patterns_by_ts.setdefault(ts, []).append(pattern_name)

        # Integrate the detected patterns back into the original input data dictionary
        for ts, patterns in chart_patterns_by_ts.items():
            # Skip if the timestamp from DataFrame isn't in the original data (shouldn't happen ideally)
            if ts not in data:
                print(f"Timestamp {ts} found in patterns but not in original data.")  # Added log
                continue
            
            # Join the newly detected patterns into a single string
            new_patterns = ", ".join(patterns)

            data[ts]["cp"] = new_patterns

        # Process support and resistance levels
        # The timestamp_str column is already created above, so we don't need to create it again
        for level in ["support", "resistance"]:
            # Check if the level column exists in the DataFrame
            if level in df.columns:
                # Create a mask for non-NaN values in the current level column
                mask = df[level].notna()

                # If there are any valid levels found
                if mask.any():
                    # Ensure timestamp_str column exists before using it
                    if "timestamp_str" not in df.columns:
                        print(f"timestamp_str column missing when processing {level}.")  # Added log
                        continue  # Skip processing this level if timestamp_str is missing

                    # Select the timestamp string and the level value for valid rows
                    valid_levels = df.loc[mask, ["timestamp_str", level]]

                    # Iterate through the (timestamp_str, level_value) pairs
                    for ts_str, level_value in valid_levels.values:
                        # Check if the timestamp exists in the original data dictionary
                        if ts_str in data:
                            # Add the level as a float to the data dictionary
                            data[ts_str][level] = float(level_value)
                        else:
                            print(f"Timestamp {ts_str} for {level} not found in original data.")  # Added log

    # Catch any exceptions during the pattern detection process
    except Exception as e:
        # Print an error message and the stack trace for debugging
        print(f"Error in chart pattern detection processing: {e}")
        traceback.print_exc()
        # Return the original data unmodified in case of error
        return data

    # Return the data dictionary, now updated with patterns and levels
    return data
