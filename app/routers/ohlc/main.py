from typing import Any, Dict, List, Optional

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from app.routers.ohlc.chart_patterns import find_chart_patterns
from app.routers.ohlc.data import adjust_start_date_for_interval, fetch_ohlc_data
from app.routers.ohlc.payoff import calculate_payoff
from app.routers.ohlc.ta import find_patterns, find_ta, print_pattern_summary

router = APIRouter()


class Filter(BaseModel):
    field: str
    condition: str
    value: str


class FilterGroup(BaseModel):
    groupOperator: str
    filters: List[Filter]


class OHLCRequest(BaseModel):
    symbol: str
    duration: str
    start: Optional[str] = None
    end: Optional[str] = None
    stoplossCoefficient: Optional[str] = "2"
    filterGroups: Optional[List[FilterGroup]] = []
    candlestickPatterns: Optional[List[str]] = []
    chartPatterns: Optional[List[str]] = []
    strategyType: Optional[str] = "bullish"


@router.post("/ohlc-od/", response_model=Dict[str, Any])
async def get_ohlc_on_demand(request: OHLCRequest):
    """
    Process OHLC data with custom filters and patterns.

    Accepts a JSON body with:
    - symbol: Stock symbol
    - duration: Time interval (1m, 5m, 15m)
    - start/end: Date range
    - stoplossCoefficient: For calculating stop loss
    - filterGroups: Custom filters with AND/OR logic
    - candlestickPatterns: List of candlestick patterns to detect
    - chartPatterns: List of chart patterns to detect
    """
    # Extract basic parameters
    symbol = request.symbol
    interval = request.duration
    original_start = request.start
    original_end = request.end

    try:
        # Fetch base OHLC data
        data = await fetch_ohlc_data(symbol, original_start, original_end, interval)
        if not data:
            raise HTTPException(status_code=404, detail="No OHLC data found.")

        # Process technical indicators and patterns
        processed_data = find_patterns(data)
        processed_data = find_ta(processed_data, None)  # No forward looking by default

        # Chart pattern detection
        processed_data = find_chart_patterns(processed_data)

        # Apply custom filters from the request
        signals = processed_data
        if request.filterGroups:
            signals = filter_data(processed_data, request.filterGroups)

        print_pattern_summary(signals)

        # Call new function to print payload table with signals for matched status
        print_payload(processed_data, signals)

        # Apply candlestick pattern filters if specified
        if request.candlestickPatterns:
            signals = filter_candlestick_patterns(signals, request.candlestickPatterns)

        # Apply chart pattern filters if specified
        if request.chartPatterns:
            signals = filter_chart_patterns(signals, request.chartPatterns)

        # Calculate payoff with the specified stop loss coefficient
        sl_coef = float(request.stoplossCoefficient) if request.stoplossCoefficient else 2.0

        # Map frontend strategy type to expected values.
        # Frontend sends "bullish" or "bearish" (case-insensitive).
        # We'll map "bearish" -> "Short" and default to "Long" otherwise.
        strategy_raw = (request.strategyType or "bullish").lower()
        strategy = "Short" if strategy_raw == "bearish" else "Long"

        payoff = calculate_payoff(strategy, signals, processed_data, sl_coef)

        return {
            "ohlc_data": processed_data,
            "payoff": payoff,
        }

    except Exception as e:
        print(e)  # Added exception logging

        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error processing OHLC data: {str(e)}")


def resolve_dynamic_value(field_key: str, current_candle: Dict[str, Any], sorted_keys: List[str], current_index: int, data: Dict[str, Any]) -> Any:
    """
    Resolve a dynamic field value which may refer to a previous candle.
    Examples:
      - "c" returns current_candle.get("c")
      - "c_prev" returns the 'c' value from 1 candle ago.
      - "c_prev_5" returns the 'c' value from 5 candles ago.
    """
    if "prev" not in field_key:
        return current_candle.get(field_key)

    # Split the field_key on '_prev'
    parts = field_key.split("_prev")
    base_field = parts[0]
    # Default offset is 1 if not provided
    offset = 1
    if len(parts) > 1 and parts[1]:
        try:
            # parts[1] may start with an underscore (e.g. "_5")
            offset = int(parts[1].lstrip("_"))
        except ValueError:
            offset = 1
    prev_index = current_index - offset
    if prev_index < 0:
        return None
    prev_ts = sorted_keys[prev_index]
    prev_candle = data.get(prev_ts)
    if prev_candle:
        return prev_candle.get(base_field)
    return None


def filter_data(data: Dict[str, Any], filter_groups: List[FilterGroup]) -> Dict[str, Any]:
    """
    Apply filter groups to OHLC data. Each group can have AND/OR logic.
    Each filter checks if a specific field meets a condition with a value.
    Now supports dynamic references like {c_prev} or {c_prev_5}.
    """
    if not filter_groups or len(filter_groups) == 0:
        return data

    result = {}
    # Sort keys to be able to reference previous candles
    sorted_keys = sorted(data.keys(), key=lambda k: int(k))

    for current_index, ts in enumerate(sorted_keys):
        candle = data[ts]
        include_candle = True

        for group in filter_groups:
            if group.groupOperator == "AND":
                group_result = all(check_filter(candle, filter_item, sorted_keys, current_index, data) for filter_item in group.filters)
            else:
                group_result = any(check_filter(candle, filter_item, sorted_keys, current_index, data) for filter_item in group.filters)

            if not group_result:
                include_candle = False
                break

        if include_candle:
            result[ts] = candle

    return result


def check_filter(candle: Dict[str, Any], filter_item: Filter, sorted_keys: List[str], current_index: int, data: Dict[str, Any]) -> bool:
    """
    Check if a single filter passes for a given candle.
    Supports dynamic field references like {c_prev} or {c_prev_5}.
    """
    # Resolve field value (dynamic if enclosed in {})
    if filter_item.field.startswith("{") and filter_item.field.endswith("}"):
        raw_field_key = filter_item.field[1:-1].strip().lower()
        field_value = resolve_dynamic_value(raw_field_key, candle, sorted_keys, current_index, data)
    else:
        field_key = filter_item.field.strip().lower()
        field_value = candle.get(field_key)
    if field_value is None:
        return False

    # Resolve the comparison value (dynamic if enclosed in {})
    if filter_item.value.startswith("{") and filter_item.value.endswith("}"):
        raw_value_key = filter_item.value[1:-1].strip().lower()
        compare_value = resolve_dynamic_value(raw_value_key, candle, sorted_keys, current_index, data)
        if compare_value is None:
            return False
    else:
        try:
            compare_value = type(field_value)(filter_item.value.strip())
        except (ValueError, TypeError):
            try:
                compare_value = float(filter_item.value.strip())
                field_value = float(field_value)
            except (ValueError, TypeError):
                return False

    # Ensure both values are converted to float; return False if not possible
    try:
        field_value_num = float(field_value)
        compare_value_num = float(compare_value)
    except (ValueError, TypeError):
        return False

    # Apply the condition
    if filter_item.condition == "gt":
        return field_value_num > compare_value_num
    elif filter_item.condition == "gte":
        return field_value_num >= compare_value_num
    elif filter_item.condition == "lt":
        return field_value_num < compare_value_num
    elif filter_item.condition == "lte":
        return field_value_num <= compare_value_num
    elif filter_item.condition == "eq":
        return field_value_num == compare_value_num
    elif filter_item.condition == "neq":
        return field_value_num != compare_value_num
    else:
        return False


def filter_candlestick_patterns(data: Dict[str, Any], patterns: List[str]) -> Dict[str, Any]:
    """
    Filter OHLC data by candlestick patterns using OR filtering and partial matching.

    Each candle with a pattern field ('p') that contains at least one of the
    requested patterns (after normalization) is included in the result.
    """
    if not patterns:
        return data

    result = {}

    # Normalize requested patterns (replace hyphens with spaces, lower-case)
    normalized_requested = [p.lower().replace("-", " ") for p in patterns]

    for ts, candle in data.items():
        candle_patterns = candle.get("p", "")
        if candle_patterns:
            # Convert the comma-separated string into a list of normalized (lower-case, hyphen replaced) patterns.
            candle_pattern_list = [p.strip().lower().replace("-", " ") for p in candle_patterns.split(",")]
            # Check if any normalized requested pattern is a substring of any candle pattern
            if any(req in cp for req in normalized_requested for cp in candle_pattern_list):
                result[ts] = candle
    return result


def filter_chart_patterns(data: Dict[str, Dict[str, Any]], patterns: List[str]) -> Dict[str, Dict[str, Any]]:
    """
    Filter OHLC data by patterns using set operations.

    Args:
        data: Dictionary with timestamp keys and OHLC values with patterns
        patterns: List of pattern names to filter for

    Returns:
        Dictionary with filtered data containing only the requested patterns
    """
    if not data:
        return {}
    if not patterns:
        return data

    filtered_data: Dict[str, Dict[str, Any]] = {}

    # Normalize requested patterns (replace hyphens with spaces, lowercase)
    target_patterns: set[str] = set(p.lower().replace("-", " ").strip() for p in patterns)

    for timestamp, candle in data.items():
        pattern_str = candle.get("cp", "")
        if not pattern_str:
            continue

        # Convert the comma-separated string into a set of normalized patterns
        candle_patterns: set[str] = set(p.strip().lower().replace("-", " ") for p in pattern_str.split(","))

        # Check if any normalized pattern matches
        if candle_patterns & target_patterns:
            filtered_data[timestamp] = candle

    return filtered_data


def print_payload(data: Dict[str, Any], signals: Dict[str, Any], count: Optional[int] = None) -> None:
    import csv
    from datetime import datetime, timedelta, timezone

    # Determine keys to output
    sorted_keys = sorted(data.keys(), key=lambda k: int(k))
    keys_to_output = sorted_keys if count is None else sorted_keys[-count:]

    # Updated header with new "Matched" column
    header = ["Date/Time", "O", "H", "L", "C", "RSI", "EMA9", "EMA50", "EMA100", "EMA200", "Patterns", "Matched", "VWAP"]
    with open("payload.csv", "w", newline="") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(header)

        for ts in keys_to_output:
            hr_ts = datetime.fromtimestamp(int(ts), tz=timezone(timedelta(hours=5, minutes=30))).strftime("%Y-%m-%d %H:%M:%S")
            candle = data[ts]
            o = candle.get("o", "N/A")
            h = candle.get("h", "N/A")
            l = candle.get("l", "N/A")
            c = candle.get("c", "N/A")
            rsi = candle.get("rsi", "N/A")
            ema9 = candle.get("ema9", "N/A")
            ema50 = candle.get("ema50", "N/A")
            ema100 = candle.get("ema100", "N/A")
            ema200 = candle.get("ema200", "N/A")
            # VWAP is not present in the original data, so we set it to "N/A"
            vwap = candle.get("vwap", "N/A")
            patterns = candle.get("p", "")
            # Determine if this candle is in signals (matched)
            matched = "True" if ts in signals else "False"
            writer.writerow([hr_ts, o, h, l, c, rsi, ema9, ema50, ema100, ema200, patterns, matched, vwap])
