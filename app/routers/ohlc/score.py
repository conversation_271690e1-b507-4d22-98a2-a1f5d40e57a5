# score.py

from typing import Any, Dict, List

import numpy as np
import talib


def bullish_harami(o, h, l, c):
    prev_o = np.roll(o, 1)
    prev_c = np.roll(c, 1)
    prev_bearish = prev_c < prev_o
    curr_bullish = c > o
    contained = (o > prev_c) & (c < prev_o)
    result = np.where(prev_bearish & curr_bullish & contained, 1, 0)
    result[0] = 0
    return result


def bearish_harami(o, h, l, c):
    prev_o = np.roll(o, 1)
    prev_c = np.roll(c, 1)
    prev_bullish = prev_c > prev_o
    curr_bearish = c < o
    contained = (o < prev_c) & (c > prev_o)
    result = np.where(prev_bullish & curr_bearish & contained, 1, 0)
    result[0] = 0
    return result


def compute_candle_score(candle: Dict[str, Any], support: List[float], resistance: List[float]) -> int:
    """
    Computes a score for a single candle based on candlestick patterns and technical indicators.
    Positive scores favor a long (bullish) signal, while negative scores suggest bearish conditions.
    """
    score = 0
    patterns = candle.get("p", "")
    rsi = candle.get("rsi")
    ema = candle.get("ema")
    close = candle.get("c")
    bb_upper = candle.get("bb_upper")
    bb_lower = candle.get("bb_lower")
    adx = candle.get("adx")
    cci = candle.get("cci")
    tema = candle.get("tema")
    stoch_k = candle.get("stoch_k")
    stoch_d = candle.get("stoch_d")
    mfi = candle.get("mfi")
    atr = candle.get("atr")
    macd = candle.get("macd")
    macds = candle.get("macds")
    macdh = candle.get("macdh")

    # Pattern-based scoring.
    if "Bullish Engulfing" in patterns:
        score += 10
    if "Three White Soldiers" in patterns:
        score += 15
    if "Hammer" in patterns:
        score += 5
    if "Bullish Harami" in patterns:
        score += 5
    if "Bearish Engulfing" in patterns:
        score -= 10
    if "Three Black Crows" in patterns:
        score -= 15
    if "Hanging Man" in patterns:
        score -= 5
    if "Bearish Harami" in patterns:
        score -= 5

    # RSI-based scoring.
    if rsi is not None:
        if rsi < 30:
            score += 5
        elif rsi > 70:
            score -= 5

    # EMA comparison.
    if ema is not None and close is not None:
        if close > ema:
            score += 3
        elif close < ema:
            score -= 3

    # Bollinger Bands.
    if close is not None and bb_lower is not None and bb_upper is not None:
        if close < bb_lower * 1.005:
            score += 3
        elif close > bb_upper * 0.995:
            score -= 3

    # ADX.
    if adx is not None and ema is not None and close is not None:
        if adx > 25:
            score += 2 if close > ema else -2

    # CCI.
    if cci is not None:
        if cci < -100:
            score += 3
        elif cci > 100:
            score -= 3

    # TEMA.
    if tema is not None and close is not None:
        score += 2 if close > tema else -2

    # Stochastic oscillator.
    if stoch_k is not None:
        if stoch_k < 20:
            score += 2
        elif stoch_k > 80:
            score -= 2

    # Stochastic crossover.
    if stoch_k is not None and stoch_d is not None:
        if stoch_k > stoch_d and stoch_k < 20 and stoch_d < 20:
            score += 2
        elif stoch_k < stoch_d and stoch_k > 80 and stoch_d > 80:
            score -= 2

    # MFI.
    if mfi is not None:
        if mfi < 20:
            score += 3
        elif mfi > 80:
            score -= 3

    # MACD signal.
    if macd is not None and macds is not None and macdh is not None:
        if macd > macds and macdh > 0:
            score += 3
        elif macd < macds and macdh < 0:
            score -= 3

    # Divergence check (RSI).
    if rsi is not None and close is not None and "o" in candle and "l" in candle:
        open_price = candle["o"]
        if close > open_price and rsi < 30:
            score += 2
        elif close < open_price and rsi > 70:
            score -= 2

    # Support/Resistance Influence.
    if close and atr:
        if support and close < support[0] + 0.5 * atr:
            score += 4
        elif resistance and close > resistance[0] - 0.5 * atr:
            score -= 4

    return score
