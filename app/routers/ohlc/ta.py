# ta.py

import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import requests
import talib

from app.routers.ohlc.score import bearish_harami, bullish_harami, compute_candle_score


def print_pattern_summary(data: Dict[str, Any]) -> None:
    """
    Prints a summary of candlestick patterns found in the data.
    It expects that each record in data has a key 'p' containing a comma-separated
    string of triggered patterns.
    """
    summary = {}
    for ts, record in data.items():
        patterns_str = record.get("p", "")
        if patterns_str:
            patterns = [p.strip() for p in patterns_str.split(",") if p.strip()]
            for p in patterns:
                summary[p] = summary.get(p, 0) + 1

    # print("Summary of patterns found:")
    # for pattern, count in summary.items():
    #     print(f"{pattern}: {count}")


def find_patterns(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Computes candlestick patterns for the provided OHLC data and attaches a
    comma-separated string of triggered patterns (under key 'p') to each record.
    After processing, it calls print_pattern_summary to display the summary.
    """
    sorted_keys = sorted(data.keys(), key=lambda k: int(k))
    try:
        opens = np.array([data[k]["o"] for k in sorted_keys], dtype=np.float64)
        highs = np.array([data[k]["h"] for k in sorted_keys], dtype=np.float64)
        lows = np.array([data[k]["l"] for k in sorted_keys], dtype=np.float64)
        closes = np.array([data[k]["c"] for k in sorted_keys], dtype=np.float64)
    except Exception as e:
        print(f"Error computing arrays in find_patterns: {e}")
        return data

    pattern_functions = {
        "Bullish Marubozu": lambda o, h, l, c: np.where(talib.CDLMARUBOZU(o, h, l, c) > 0, 100, 0),
        "Bearish Marubozu": lambda o, h, l, c: np.where(talib.CDLMARUBOZU(o, h, l, c) < 0, -100, 0),
        "Hammer": lambda o, h, l, c: talib.CDLHAMMER(o, h, l, c),
        "Inverted Hammer": lambda o, h, l, c: talib.CDLINVERTEDHAMMER(o, h, l, c),
        "Bullish Engulfing": lambda o, h, l, c: np.where(talib.CDLENGULFING(o, h, l, c) > 0, 100, 0),
        "Piercing Pattern": lambda o, h, l, c: talib.CDLPIERCING(o, h, l, c),
        "Morning Star": lambda o, h, l, c: talib.CDLMORNINGSTAR(o, h, l, c),
        "Three White Soldiers": lambda o, h, l, c: talib.CDL3WHITESOLDIERS(o, h, l, c),
        "Bullish Harami": lambda o, h, l, c: bullish_harami(o, h, l, c),
        "Shooting Star": lambda o, h, l, c: talib.CDLSHOOTINGSTAR(o, h, l, c),
        "Hanging Man": lambda o, h, l, c: talib.CDLHANGINGMAN(o, h, l, c),
        "Bearish Engulfing": lambda o, h, l, c: np.where(talib.CDLENGULFING(o, h, l, c) < 0, -100, 0),
        "Dark Cloud Cover": lambda o, h, l, c: talib.CDLDARKCLOUDCOVER(o, h, l, c),
        "Evening Star": lambda o, h, l, c: talib.CDLEVENINGSTAR(o, h, l, c),
        "Three Black Crows": lambda o, h, l, c: talib.CDL3BLACKCROWS(o, h, l, c),
        "Bearish Harami": lambda o, h, l, c: bearish_harami(o, h, l, c),
        "Doji": lambda o, h, l, c: talib.CDLDOJI(o, h, l, c),
        "Dragonfly Doji": lambda o, h, l, c: talib.CDLDRAGONFLYDOJI(o, h, l, c),
        "Gravestone Doji": lambda o, h, l, c: talib.CDLGRAVESTONEDOJI(o, h, l, c),
        "Long-legged Doji": lambda o, h, l, c: talib.CDLLONGLEGGEDDOJI(o, h, l, c),
        "Spinning Top": lambda o, h, l, c: talib.CDLSPINNINGTOP(o, h, l, c),
        "High Wave Candle": lambda o, h, l, c: talib.CDLHIGHWAVE(o, h, l, c),
        "Two Crows": lambda o, h, l, c: talib.CDL2CROWS(o, h, l, c),
        "Three Inside Up/Down": lambda o, h, l, c: talib.CDL3INSIDE(o, h, l, c),
        "Three-Line Strike": lambda o, h, l, c: talib.CDL3LINESTRIKE(o, h, l, c),
        "Three Outside": lambda o, h, l, c: talib.CDL3OUTSIDE(o, h, l, c),
        "Three Stars In South": lambda o, h, l, c: talib.CDL3STARSINSOUTH(o, h, l, c),
        "Abandoned Baby": lambda o, h, l, c: talib.CDLABANDONEDBABY(o, h, l, c),
        # ... add more patterns as needed ...
    }

    pattern_results = {}
    for pattern_name, func in pattern_functions.items():
        try:
            pattern_array = func(opens, highs, lows, closes)
            pattern_results[pattern_name] = pattern_array
        except Exception as e:
            pattern_results[pattern_name] = np.zeros_like(opens)
            print(f"Error computing pattern {pattern_name}: {e}")

    # Attach triggered patterns per timestamp.
    patterns_by_timestamp = {}
    for idx, ts in enumerate(sorted_keys):
        triggered = [name for name, arr in pattern_results.items() if arr[idx] != 0]
        patterns_by_timestamp[ts] = ", ".join(triggered) if triggered else ""

    for ts in sorted_keys:
        data[ts]["p"] = patterns_by_timestamp[ts]

    # Now call the helper function with the updated data.
    print_pattern_summary(data)

    return data


def detect_support_resistance(levels: List[float]) -> Tuple[List[float], List[float]]:
    """
    Identify local minima and maxima as support and resistance levels.
    """
    support: List[float] = []
    resistance: List[float] = []
    for i in range(2, len(levels) - 2):
        if levels[i] < levels[i - 1] and levels[i] < levels[i + 1]:
            support.append(levels[i])
        elif levels[i] > levels[i - 1] and levels[i] > levels[i + 1]:
            resistance.append(levels[i])
    return sorted(support), sorted(resistance, reverse=True)


def compute_supertrend(high, low, close, period=10, multiplier=3):
    atr = talib.ATR(high, low, close, timeperiod=period)
    hl2 = (high + low) / 2
    final_upperband = np.full_like(close, np.nan)
    final_lowerband = np.full_like(close, np.nan)
    supertrend = np.full_like(close, np.nan)
    direction = np.full_like(close, np.nan)
    for i in range(len(close)):
        if np.isnan(atr[i]):
            continue
        basic_upperband = hl2[i] + multiplier * atr[i]
        basic_lowerband = hl2[i] - multiplier * atr[i]
        if i == 0 or np.isnan(supertrend[i - 1]):
            final_upperband[i] = basic_upperband
            final_lowerband[i] = basic_lowerband
            supertrend[i] = basic_upperband
            direction[i] = 1
        else:
            final_upperband[i] = basic_upperband if (basic_upperband < final_upperband[i - 1] or close[i - 1] > final_upperband[i - 1]) else final_upperband[i - 1]
            final_lowerband[i] = basic_lowerband if (basic_lowerband > final_lowerband[i - 1] or close[i - 1] < final_lowerband[i - 1]) else final_lowerband[i - 1]
            if supertrend[i - 1] == final_upperband[i - 1]:
                if close[i] <= final_upperband[i]:
                    supertrend[i] = final_upperband[i]
                    direction[i] = -1
                else:
                    supertrend[i] = final_lowerband[i]
                    direction[i] = 1
            elif supertrend[i - 1] == final_lowerband[i - 1]:
                if close[i] >= final_lowerband[i]:
                    supertrend[i] = final_lowerband[i]
                    direction[i] = 1
                else:
                    supertrend[i] = final_upperband[i]
                    direction[i] = -1
    return supertrend, direction


def find_ta(data: Dict[str, Any], forward_looking: Optional[int]) -> Dict[str, Any]:
    """
    Computes technical indicators, appends them to the data, and retains golden/death cross signals.
    """
    sorted_keys = sorted(data.keys(), key=lambda k: int(k))
    try:
        opens = np.array([data[k]["o"] for k in sorted_keys], dtype=np.float64)
        highs = np.array([data[k]["h"] for k in sorted_keys], dtype=np.float64)
        lows = np.array([data[k]["l"] for k in sorted_keys], dtype=np.float64)
        closes = np.array([data[k]["c"] for k in sorted_keys], dtype=np.float64)
        volumes = None
        if "v" in data[sorted_keys[0]]:
            volumes = np.array([data[k].get("v", 0) for k in sorted_keys], dtype=np.float64)
    except Exception as e:
        print(f"Error computing arrays in find_signals: {e}")
        return data

    try:
        rsi = compute_rsi_ema(closes, period=14)
        ema = talib.EMA(closes, timeperiod=9)  # already computed EMA9
        sma = talib.SMA(closes, timeperiod=14)
        macd, macdsignal, macdhist = talib.MACD(closes, fastperiod=12, slowperiod=26, signalperiod=9)
        upper, middle, lower = talib.BBANDS(closes, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
        atr = talib.ATR(highs, lows, closes, timeperiod=14)
        adx = talib.ADX(highs, lows, closes, timeperiod=14)
        cci = talib.CCI(highs, lows, closes, timeperiod=14)
        tema = talib.TEMA(closes, timeperiod=30)
        slowk, slowd = talib.STOCH(highs, lows, closes, fastk_period=14, slowk_period=3, slowk_matype=0, slowd_period=3, slowd_matype=0)
        if volumes is not None:
            mfi = talib.MFI(highs, lows, closes, volumes, timeperiod=14)
        else:
            mfi = np.array([None] * len(closes))
        # Compute additional EMA series for different periods
        ema9 = ema
        ema20 = talib.EMA(closes, timeperiod=20)
        ema50 = talib.EMA(closes, timeperiod=50)
        ema100 = talib.EMA(closes, timeperiod=100)
        ema200 = talib.EMA(closes, timeperiod=200)
        sar = talib.SAR(highs, lows, acceleration=0.02, maximum=0.2)
        # Supertrend
        supertrend, supertrend_dir = compute_supertrend(highs, lows, closes, period=10, multiplier=3)
    except Exception as e:
        print(f"Error computing technical indicators: {e}")
        return data

    # Compute support and resistance levels if not already taken
    low_supports, _ = detect_support_resistance(lows)
    _, high_resistances = detect_support_resistance(highs)

    # VWAP: reset at the start of each trading day, use hlc3 as price
    if volumes is not None and np.sum(volumes) != 0:
        vwap = [None] * len(closes)
        cum_vol = 0
        cum_vol_price = 0
        prev_date = None
        for idx, ts in enumerate(sorted_keys):
            dt = datetime.utcfromtimestamp(int(ts)) + timedelta(hours=5, minutes=30)
            cur_date = dt.date()
            if prev_date is None or cur_date != prev_date:
                cum_vol = 0
                cum_vol_price = 0
            vol = volumes[idx]
            hlc3 = (highs[idx] + lows[idx] + closes[idx]) / 3
            cum_vol += vol
            cum_vol_price += hlc3 * vol
            vwap[idx] = round(float(cum_vol_price / cum_vol), 2) if cum_vol != 0 else None
            prev_date = cur_date
    else:
        vwap = ["N/A"] * len(closes)

    for idx, ts in enumerate(sorted_keys):
        # Convert ts to IST in 24hr format (e.g., 0930 or 2130)
        time_obj = datetime.utcfromtimestamp(int(ts)) + timedelta(hours=5, minutes=30)
        data[ts]["current_time_ist"] = time_obj.strftime("%H%M")

        data[ts]["rsi"] = round(float(rsi[idx]), 2) if not np.isnan(rsi[idx]) else None
        data[ts]["ema"] = round(float(ema[idx]), 2) if not np.isnan(ema[idx]) else None
        data[ts]["sma"] = round(float(sma[idx]), 2) if not np.isnan(sma[idx]) else None
        data[ts]["macd"] = round(float(macd[idx]), 2) if not np.isnan(macd[idx]) else None
        data[ts]["macds"] = round(float(macdsignal[idx]), 2) if not np.isnan(macdsignal[idx]) else None
        data[ts]["macdh"] = round(float(macdhist[idx]), 2) if not np.isnan(macdhist[idx]) else None
        data[ts]["bb_upper"] = round(float(upper[idx]), 2) if not np.isnan(upper[idx]) else None
        data[ts]["bb_middle"] = round(float(middle[idx]), 2) if not np.isnan(middle[idx]) else None
        data[ts]["bb_lower"] = round(float(lower[idx]), 2) if not np.isnan(lower[idx]) else None
        data[ts]["atr"] = round(float(atr[idx]), 2) if not np.isnan(atr[idx]) else None
        data[ts]["adx"] = round(float(adx[idx]), 2) if not np.isnan(adx[idx]) else None
        data[ts]["cci"] = round(float(cci[idx]), 2) if not np.isnan(cci[idx]) else None
        data[ts]["tema"] = round(float(tema[idx]), 2) if not np.isnan(tema[idx]) else None
        data[ts]["stoch_k"] = round(float(slowk[idx]), 2) if not np.isnan(slowk[idx]) else None
        data[ts]["stoch_d"] = round(float(slowd[idx]), 2) if not np.isnan(slowd[idx]) else None
        data[ts]["mfi"] = round(float(mfi[idx]), 2) if volumes is not None and mfi[idx] is not None and not np.isnan(mfi[idx]) else None
        data[ts]["vwap"] = round(float(vwap[idx]), 2) if vwap[idx] != "N/A" else "N/A"
        data[ts]["ema9"] = round(float(ema9[idx]), 2) if not np.isnan(ema9[idx]) else None
        data[ts]["ema20"] = round(float(ema20[idx]), 2) if not np.isnan(ema20[idx]) else None
        data[ts]["ema50"] = round(float(ema50[idx]), 2) if not np.isnan(ema50[idx]) else None
        data[ts]["ema100"] = round(float(ema100[idx]), 2) if not np.isnan(ema100[idx]) else None
        data[ts]["ema200"] = round(float(ema200[idx]), 2) if not np.isnan(ema200[idx]) else None
        data[ts]["sar"] = round(float(sar[idx]), 2) if not np.isnan(sar[idx]) else None
        data[ts]["supertrend"] = round(float(supertrend[idx]), 2) if not np.isnan(supertrend[idx]) else None
        data[ts]["supertrend_dir"] = int(supertrend_dir[idx]) if not np.isnan(supertrend_dir[idx]) else None

        # Add support and resistance levels if not already taken
        if "support" not in data[ts]:
            data[ts]["support"] = low_supports
        if "resistance" not in data[ts]:
            data[ts]["resistance"] = high_resistances

        nearest_support = max([s for s in low_supports if s < closes[idx]], default=None)
        nearest_resistance = min([r for r in high_resistances if r > closes[idx]], default=None)

        data[ts]["support_breakdown"] = 1 if nearest_support and closes[idx] < nearest_support else 0
        data[ts]["resistance_breakout"] = 1 if nearest_resistance and closes[idx] > nearest_resistance else 0

        # Calculate RSI divergence and convergence.
        # For simplicity, we compare the current bar with the previous one:
        # Divergence: price moves in one direction while RSI moves in the opposite.
        # Convergence: price and RSI move in the same direction.
        if idx == 0:
            data[ts]["rsi_divergence"] = 0
            data[ts]["rsi_convergence"] = 0
        else:
            if (closes[idx] < closes[idx - 1] and rsi[idx] > rsi[idx - 1]) or (closes[idx] > closes[idx - 1] and rsi[idx] < rsi[idx - 1]):
                data[ts]["rsi_divergence"] = 1
            else:
                data[ts]["rsi_divergence"] = 0

            if (closes[idx] < closes[idx - 1] and rsi[idx] < rsi[idx - 1]) or (closes[idx] > closes[idx - 1] and rsi[idx] > rsi[idx - 1]):
                data[ts]["rsi_convergence"] = 1
            else:
                data[ts]["rsi_convergence"] = 0

    return data


def ema(data, period):
    """Compute Exponential Moving Average (EMA) for a numpy array using TradingView-style smoothing (alpha=1/period)."""
    return pd.Series(data).ewm(alpha=1 / period, adjust=False).mean().to_numpy()


def compute_rsi_ema(prices, period=14):
    prices = np.array(prices, dtype=float)
    delta = np.diff(prices, prepend=prices[0])
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)

    avg_gain = ema(gain, period)
    avg_loss = ema(loss, period)

    # Avoid division by zero and suppress warnings
    with np.errstate(divide="ignore", invalid="ignore"):
        rs = np.where(avg_loss == 0, np.nan, avg_gain / avg_loss)
        rsi = 100 - (100 / (1 + rs))
        # If avg_loss is zero (no down moves), RSI is 100
        rsi[avg_loss == 0] = 100
        # If both avg_gain and avg_loss are zero, set RSI to 50 (neutral)
        rsi[(avg_gain == 0) & (avg_loss == 0)] = 50
    return rsi


def main():

    # Read data from URL https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/ohlcd/RELIANCE?start=2025-07-05&end=2025-08-01&interval=15m
    # httpx
    data = requests.get("https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/ohlcd/RELIANCE", params={"start": "2025-07-31", "end": "2025-08-01", "interval": "15m"}).json()

    # Sort by timestamp
    closes = [v["c"] for k, v in sorted(data.items())]
    rsi = compute_rsi_ema(closes, period=14)

    for idx, value in enumerate(rsi):
        print(f"Tick {idx:02d}: Close={closes[idx]}, RSI={value:.2f}")


if __name__ == "__main__":
    main()
