# data.py

from datetime import datetime, timedelta
from typing import Any, Dict, Optional

import httpx
from async_lru import alru_cache


def adjust_start_date_for_interval(start: Optional[str], interval: str) -> Optional[str]:
    """
    Adjust the start date for lower timeframes (e.g. 15m or 10m) by subtracting one day.
    """
    if start is None:
        return None
    if interval in ["15m", "10m"]:
        from datetime import datetime  # local import if needed

        start_date = datetime.strptime(start, "%Y-%m-%d")
        adjusted_date = start_date - timedelta(days=1)
        # For simplicity, assume no holiday logic here.
        return adjusted_date.strftime("%Y-%m-%d")
    return start


from urllib.parse import urlencode


@alru_cache(maxsize=128)
async def fetch_ohlc_data(symbol: str, adjusted_start: Optional[str], end: Optional[str], interval: str = "1m") -> Dict[str, Any]:
    """
    Asynchronously fetch OHLC data from the remote API using the provided interval.
    Results are cached to speed up subsequent requests.
    """

    # If symbol has nasdaq, we iwll hit /yohld
    if "nasdaq" in symbol.lower():
        url = f"https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/yohlc?symbol={symbol}"
    else:
        url = f"https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/ohlcd/{symbol}"
    params = {"interval": interval}
    if adjusted_start:
        params["start"] = adjusted_start
    if end:
        params["end"] = end

    full_url = f"{url}?{urlencode(params)}"
    print("Fetching full URL:", full_url)

    async with httpx.AsyncClient(timeout=30) as client:
        response = await client.get(url, params=params)
        if response.status_code != 200:
            raise Exception("Error fetching OHLC data.")
        return response.json()
