import httpx
from typing import List, Dict, Any
from fastapi import APIRouter, <PERSON><PERSON>, Request, HTTPException
from app.util.templates import templates
from app.util.menu import menu
from .auth import get_user_data
from async_lru import alru_cache


router = APIRouter()

MANTRA_BLOGS_SERVER = "https://api.blogs.theaibull.com"
APP_NAME = "ai_bull"


# Home page - Display all pillars
@router.get("")
async def get_resources(request: Request, access_token: str = Cookie(None)):
    pillars = await fetch_resources("pillars")

    metadata = {
        "title": "AI Bull Resources - AI-Powered Trading Strategies & Tools",
        "description": "Explore comprehensive resources for AI-powered trading strategies, market analysis tools, and expert insights. Learn about stock market trends, technical analysis, and investment strategies.",
        "site_name": "AI Bull",
        "keywords": "AI trading resources, stock market tools, trading strategies, market analysis, investment guides, AI Bull, technical analysis",
        "canonicalUrl": "https://theaibull.com/resources",
        "ogImage": "https://assets.theaibull.com/images/ai-bull.png",
        "ogImageWidth": "750",
        "ogImageHeight": "350",
        "twitterSite": "@aibull"
    }

    return templates.TemplateResponse(
        "/resources/home.html",
        {"request": request, "pillars": pillars, "metadata": metadata, **menu, **get_user_data(access_token)},
    )


# Individual resource page - Display resource with 2-column layout
@router.get("/{resource_slug}")
async def get_resource(request: Request, resource_slug: str, access_token: str = Cookie(None)):
    """
    Render individual resource page with 2-column layout.
    Left: Menu with pillar and subpages
    Right: Resource content
    """
    try:
        # Extract resource_id from resource_slug (e.g. "pillar-123")
        resource_id = resource_slug.split("-")[-1]

        # Fetch the current resource
        resource = await fetch_individual_resource(resource_id)

        if not resource:
            raise HTTPException(status_code=404, detail="Resource not found")

        # Determine if this is a pillar or subpage
        resource_type = resource.get("type", "")

        if resource_type == "pillar":
            # For pillars: get all subpages
            pillar = resource
            subpages = await fetch_resources("subpages", pillar_id=resource_id)
        elif resource_type == "subpage":
            # For subpages: get the parent pillar and other subpages
            pillar_id = resource.get("pillar_id")
            if not pillar_id:
                raise HTTPException(status_code=400, detail="Subpage missing pillar_id")

            pillar = await fetch_individual_resource(pillar_id)
            all_subpages = await fetch_resources("subpages", pillar_id=pillar_id)

            # Separate current subpage from others
            subpages = all_subpages
        else:
            raise HTTPException(status_code=400, detail="Invalid resource type")

        # Build metadata
        metadata = {
            "title": f"{resource.get('meta_title', 'Resource')} - AI Bull",
            "description": resource.get('meta_description', 'AI Bull resource'),
            "site_name": "AI Bull",
            "keywords": f"AI trading resources, {resource.get('meta_title', '').lower()}, trading strategies, market analysis, investment guides, AI Bull, technical analysis",
            "canonicalUrl": f"https://theaibull.com/resources/{resource_slug}",
            "ogImage": "https://assets.theaibull.com/images/ai-bull.png",
            "ogImageWidth": "750",
            "ogImageHeight": "350",
            "twitterSite": "@aibull"
        }

        return templates.TemplateResponse(
            "/resources/resource-details.html",
            {
                "request": request,
                "page": resource,
                "resource": resource,
                "type": resource_type,
                "pillar": pillar,
                "subpages": subpages,
                "metadata": metadata,
                **menu,
                **get_user_data(access_token),
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading resource: {str(e)}")


@alru_cache(ttl=1800)  # Cache for 30 minutes
async def fetch_resources(resource_type: str, pillar_id: str | None = None, limit: int = 100) -> List[Dict[str, Any]]:
    """
    Fetch resources (pillars or subpages) from the blogs server with caching.

    Args:
        resource_type: Type of resource to fetch ('pillars' or 'subpages')
        pillar_id: Optional pillar ID for fetching subpages
        limit: Maximum number of items to return
    """
    base_url = f"{MANTRA_BLOGS_SERVER}/resources/{resource_type}?app_name={APP_NAME}&published=true&limit={limit}"
    if pillar_id:
        base_url += f"&pillar_id={pillar_id}"

    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(base_url)
            response.raise_for_status()
            return response.json().get("data", [])
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Request timed out while fetching resources")
    except httpx.ConnectError:
        raise HTTPException(status_code=503, detail="Unable to connect to blogs server")
    except httpx.HTTPStatusError as e:
        raise HTTPException(status_code=e.response.status_code, detail=f"Error fetching resources: {str(e)}")
    except httpx.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Error making request to blogs server: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error while fetching resources: {str(e)}")


@alru_cache(ttl=1800)  # Cache for 30 minutes
async def fetch_individual_resource(resource_id: str) -> Dict[str, Any]:
    """
    Fetch an individual resource (pillar or subpage) from the blogs server with caching.
    """
    base_url = f"{MANTRA_BLOGS_SERVER}/resources/{resource_id}?app_name={APP_NAME}"

    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(base_url)
            response.raise_for_status()
            return response.json().get("data", {})
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Request timed out while fetching resource")
    except httpx.ConnectError:
        raise HTTPException(status_code=503, detail="Unable to connect to blogs server")
    except httpx.HTTPStatusError as e:
        raise HTTPException(status_code=e.response.status_code, detail=f"Error fetching resource: {str(e)}")
    except httpx.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Error making request to blogs server: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error while fetching resource: {str(e)}")
