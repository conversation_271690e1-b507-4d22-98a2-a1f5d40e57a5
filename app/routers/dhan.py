from typing import List

from fastapi import APIRouter, Body
from pydantic import BaseModel

from app.util.dhan_core.client import (
    get_expirations,
    get_futures,
    get_ohlc_data,
    get_ohlc_data_multiple,
    get_option_chain,
    get_quote_data,
    get_quote_data_multiple,
    get_ticker_data,
    get_ticker_data_multiple,
    ohlc_historical,
    ohlc_intraday,
)

router = APIRouter()


# Pydantic models for multiple request bodies
class MultipleSymbolsRequest(BaseModel):
    symbols: List[str]


class MultipleOHLCHistoricalRequest(BaseModel):
    symbols: List[str]
    from_date: str
    to_date: str
    expiry_code: int = 0


class MultipleOHLCIntradayRequest(BaseModel):
    symbols: List[str]
    from_date: str
    to_date: str
    interval: int


class MultipleOptionChainRequest(BaseModel):
    symbols: List[str]
    expiry: str


@router.get("/expirations/{symbol}")
def api_get_expirations(symbol: str):
    """
    Get expiry list for a given underlying symbol (as a path parameter).
    Returns a dict with symbol and expirations.
    """
    expirations = get_expirations(symbol)
    return {"symbol": symbol, "expirations": expirations}


@router.get("/options/{symbol}/{expiry}")
def api_get_option_chain_with_expiry(symbol: str, expiry: str):
    """
    Get option chain for a symbol and a specific expiry.
    """
    return get_option_chain(symbol, expiry)


@router.get("/ohlc/latest/{symbol}")
def api_get_ohlc(symbol: str, segment: str = "NSE_EQ"):
    """
    Get OHLC data for a given symbol (as a path parameter).
    Optional segment query param (default NSE_EQ).
    """
    return get_ohlc_data(symbol, segment=segment)


@router.get("/ohlc/intraday/{symbol}/{from_date}/{to_date}/{interval}")
def api_get_ohlc_intraday(symbol: str, from_date: str, to_date: str, interval: int):
    """
    Get intraday OHLC (minute candles) for a symbol between from_date and to_date.
    interval: 1, 5, 15, 25, or 60 (minutes) as a path parameter (at the end).
    from_date and to_date as path parameters (YYYY-MM-DD).
    """
    return ohlc_intraday(symbol, from_date, to_date, interval)


@router.get("/ohlc/historical/{symbol}/{from_date}/{to_date}")
def api_get_historical_ohlc(symbol: str, from_date: str, to_date: str, expiry_code: int = 0):
    """
    Get historical daily OHLC for a symbol between from_date and to_date.
    expiry_code: 0 for stocks, 1/2/3 for derivatives if needed.
    from_date and to_date as path parameters (YYYY-MM-DD).
    """
    return ohlc_historical(symbol, from_date, to_date, expiry_code)


@router.get("/futures/{symbol}")
def api_get_futures(symbol: str):
    """
    Get all futures contracts for a given symbol (case-insensitive).
    Returns a list of dicts with expiry, security_id, display_name, and ticker_data.
    """
    results = get_futures(symbol)
    if not results:
        return []
    return results


@router.get("/quote/symbol/{symbol}")
def api_get_futures_quote(symbol: str, segment: str = "NSE_EQ"):
    """
    Get full quote data for a given symbol.
    Optional segment query param (default NSE_EQ).
    """
    return get_quote_data(symbol, segment=segment)


@router.get("/ltp/symbol/{symbol}")
def api_get_futures_ltp(symbol: str, segment: str = "NSE_EQ"):
    """
    Get LTP (ticker) data for a given symbol.
    Optional segment query param (default NSE_EQ).
    """
    return get_ticker_data(symbol, segment=segment)


# MULTIPLE ROUTES


@router.post("/ohlc/latest")
def api_get_ohlc_multiple(symbols: list[str] = Body(..., embed=True), segment: str = "NSE_EQ"):
    """
    Get OHLC data for multiple symbols in a given segment.
    POST body: {"symbols": ["SBIN", "RELIANCE"], "segment": "NSE_EQ"}
    """
    return get_ohlc_data_multiple(symbols, segment=segment)


@router.post("/quote/symbol")
def api_get_quote_multiple(symbols: list[str] = Body(..., embed=True), segment: str = "NSE_EQ"):
    """
    Get quote data for multiple symbols in a given segment.
    POST body: {"symbols": ["SBIN", "RELIANCE"], "segment": "NSE_EQ"}
    """
    return get_quote_data_multiple(symbols, segment=segment)


@router.post("/ltp/symbol")
def api_get_ticker_multiple(symbols: list[str] = Body(..., embed=True), segment: str = "NSE_EQ"):
    """
    Get ticker (LTP) data for multiple symbols in a given segment.
    POST body: {"symbols": ["SBIN", "RELIANCE"], "segment": "NSE_EQ"}
    """
    return get_ticker_data_multiple(symbols, segment=segment)


@router.post("/expirations")
def api_get_expirations_multiple(request: MultipleSymbolsRequest):
    """
    Get expiry list for multiple symbols.
    Returns a dict with symbol as key and expirations as value.
    """
    results = {}
    for symbol in request.symbols:
        try:
            expirations = get_expirations(symbol)
            results[symbol] = {"expirations": expirations, "error": None}
        except Exception as e:
            print(f"Error fetching expirations for {symbol}: {e}")
            results[symbol] = {"expirations": None, "error": str(e)}
    return results


# Below routes will fail since Dhan have api limit of 1 request per second


@router.post("/options")
def api_get_option_chain_multiple(request: MultipleOptionChainRequest):
    """
    Get option chain for multiple symbols with the same expiry.
    Returns a dict with symbol as key and option chain as value.
    """
    results = {}
    for symbol in request.symbols:
        try:
            chain = get_option_chain(symbol, request.expiry)
            results[symbol] = {"option_chain": chain, "error": None}
        except Exception as e:
            print(f"Error fetching option chain for {symbol} with expiry {request.expiry}: {e}")
            results[symbol] = {"option_chain": None, "error": str(e)}
    return results


@router.post("/futures")
def api_get_futures_multiple(request: MultipleSymbolsRequest):
    """
    Get all futures contracts for multiple symbols.
    Returns a dict with symbol as key and futures list as value.
    """
    results = {}
    for symbol in request.symbols:
        try:
            futures = get_futures(symbol)
            results[symbol] = {"futures": futures, "error": None}
        except Exception as e:
            print(f"Error fetching futures for {symbol}: {e}")
            results[symbol] = {"futures": None, "error": str(e)}
    return results
