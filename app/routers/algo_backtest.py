import asyncio
import copy
import re
from datetime import datetime, timedelta
from typing import List, Optional

import httpx
from async_lru import alru_cache
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.routers.opstrat import BulkPayoffRequest, OptionLeg
from app.routers.rebalancing import calculate_bulk_payoff_with_rebalancing

router = APIRouter()


# New request model to take strategy, test month/year, and symbol.
class StrategyPayoffRequest(BaseModel):
    strategy: dict  # The full strategy JSON object (including entryConditions, legs, etc.)
    testMonth: Optional[str] = None  # e.g., "Jan", "Feb", ... - None means all months
    testYear: int  # e.g., 2024
    symbol: str  # Required symbol


# Helper function to compute the entry date.
def compute_entry_date(entry_day: int, test_month: str, test_year: int) -> datetime:
    try:
        entry_date = datetime.strptime(f"{entry_day}-{test_month}-{test_year}", "%d-%b-%Y")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid date parameters: {str(e)}")
    return entry_date


# Helper function to validate the entry date.
async def get_valid_entry_date(symbol: str, expiration_date: datetime, entry_date: datetime) -> datetime:
    """
    Checks if the option chain data is available for the given entry_date.
    If no data is returned (e.g. because the day is a holiday), increment the entry_date by one day,
    try up to 5 times, and return the new valid entry date.
    """
    attempts = 0
    valid_entry = entry_date
    while attempts < 5:
        try:
            expiration_date_str = expiration_date.strftime("%d-%b-%Y")
            entry_date_str = valid_entry.strftime("%d-%m-%Y")
            data = await get_option_chain_data(symbol, expiration_date_str, entry_date_str)
            records = data.get("records", {}).get("data", [])
            if records and len(records) > 0:
                return valid_entry
        except Exception:
            pass
        valid_entry = valid_entry + timedelta(days=1)
        attempts += 1
    # Return the latest valid entry date (even if data is still empty)
    return valid_entry


# Cache the option chain API call.
@alru_cache(maxsize=32)
async def get_option_chain_data(symbol: str, expiration_date_str: str, entry_date_str: str) -> dict:
    url = f"https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/{symbol}"
    params = {"expiryDate": expiration_date_str, "date": entry_date_str}
    async with httpx.AsyncClient() as client:
        response = await client.get(url, params=params)
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail="Error fetching option chain data.")
        return response.json()


# Cache the expiration date lookup.
@alru_cache(maxsize=32)
async def get_target_expiration_date(symbol: str, test_year: int, leg_expiry: str, entry_date: datetime) -> datetime:
    url = f"https://iam.theaibull.com/v1/wg7ttpouv7/symbol/expirationDates/{symbol}/{test_year}"
    async with httpx.AsyncClient() as client:
        response = await client.get(url)
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail="Error fetching expiration dates.")
        expiration_dates = response.json()

    valid_dates = []
    for exp_str in expiration_dates.get("expirationDates", []):
        try:
            exp_date = datetime.strptime(exp_str, "%d-%b-%Y")
            if exp_date > entry_date:
                valid_dates.append(exp_date)
        except Exception:
            continue
    if not valid_dates:
        raise HTTPException(status_code=404, detail="No expiration dates available after entry date.")

    # Determine target expiration date based on leg_expiry.
    if leg_expiry.startswith("Week"):
        if leg_expiry == "This Week":
            offset = 0
        elif leg_expiry == "Week (+1)":
            offset = 7
        elif leg_expiry == "Week (+2)":
            offset = 14
        elif leg_expiry == "Week (+3)":
            offset = 21
        elif leg_expiry == "Week (+4)":
            offset = 28
        elif leg_expiry == "Week (+5)":
            offset = 35
        candidate = entry_date + timedelta(days=offset)
        future_dates = [d for d in valid_dates if d >= candidate]
        if future_dates:
            return min(future_dates, key=lambda d: (d - candidate).days)
        else:
            return max(valid_dates)
    elif leg_expiry.startswith("Month"):
        import re

        m = re.match(r"Month(?: \(\+(\d+)\))?", leg_expiry)
        exp_offset = int(m.group(1)) if m and m.group(1) else 0
        # Compute the target month and year.
        target_year = entry_date.year + ((entry_date.month - 1 + exp_offset) // 12)
        target_month = ((entry_date.month - 1 + exp_offset) % 12) + 1
        # Filter expiration dates that match the target month and year.
        month_dates = [d for d in valid_dates if d.year == target_year and d.month == target_month]
        if month_dates:
            return max(month_dates)
        else:
            # Fallback if no date found in the target month: pick the next available date.
            candidate = entry_date + timedelta(days=30 * (exp_offset or 1))
            future_dates = [d for d in valid_dates if d >= candidate]
            if future_dates:
                return min(future_dates, key=lambda d: (d - candidate).days)
            else:
                return max(valid_dates)
    else:
        # Fallback for other leg_expiry values.
        candidate = entry_date
        future_dates = [d for d in valid_dates if d >= candidate]
        if future_dates:
            return min(future_dates, key=lambda d: (d - candidate).days)
        else:
            return max(valid_dates)


@alru_cache(maxsize=32)
async def get_last_price(symbol: str, expiration_date: datetime, entry_date: datetime, target_strike: float, option_type: str) -> float:
    expiration_date_str = expiration_date.strftime("%d-%b-%Y")
    entry_date_str = entry_date.strftime("%d-%m-%Y")
    data = await get_option_chain_data(symbol, expiration_date_str, entry_date_str)
    records = data.get("records", {}).get("data", [])
    tolerance = 0.01
    branch = "CE" if option_type.lower() == "call" else "PE"
    for rec in records:
        rec_strike = rec.get("strikePrice")
        if rec_strike is None:
            continue
        try:
            rec_strike_val = float(rec_strike)
        except Exception:
            continue
        if abs(rec_strike_val - target_strike) < tolerance:
            branch_data = rec.get(branch, {})
            if branch_data and branch_data.get("lastPrice") is not None:
                last_price = branch_data["lastPrice"]
                if last_price == 0:
                    raise HTTPException(
                        status_code=400, detail=f"Illiquid option detected for strike {target_strike} and option type {option_type} (last price is 0). For entry {entry_date_str}/{expiration_date_str}"
                    )
                return last_price
    raise HTTPException(status_code=404, detail=f"Last price not found for strike {target_strike} and option type {option_type}.")


@alru_cache(maxsize=32)
async def get_target_strike(symbol: str, expiration_date: datetime, strike_conf_tuple: tuple, entry_date: datetime, option_type: str) -> float:
    """
    Computes the target strike price for a leg.
    For a condition of "is" (default), it uses the strike config offset from ATM.
    For a condition of "premium" or "delta", it uses the specified criterion and a target value,
    which may be substituted from merge fields (e.g. {leg1.premium} or {leg1.premium}/2).
    """
    strike_conf = dict(strike_conf_tuple)
    expiration_date_str = expiration_date.strftime("%d-%b-%Y")
    entry_date_str = entry_date.strftime("%d-%m-%Y")
    condition = strike_conf.get("condition", "is")
    if condition in ["premium", "delta"]:
        # For premium or delta condition, get criterion and target value
        criterion = strike_conf.get("criterion", "closest")
        target_value = strike_conf.get("value")
        data = await get_option_chain_data(symbol, expiration_date_str, entry_date_str)
        records = data.get("records", {}).get("data", [])
        branch_lookup = "CE" if option_type.lower() == "call" else "PE"
        valid_records = []
        for rec in records:
            sp = rec.get("strikePrice")
            if sp is None:
                continue
            try:
                sp_val = float(sp)
            except Exception:
                continue
            branch_data = rec.get(branch_lookup)
            if not branch_data:
                continue
            if condition == "premium":
                metric = branch_data.get("lastPrice")
            else:  # delta condition
                metric = branch_data.get("delta")
            if metric is not None:
                valid_records.append((sp_val, metric))
        if not valid_records:
            raise HTTPException(status_code=404, detail="No valid option chain data found for computing target strike.")
        if criterion == "closest":
            target_strike, _ = min(valid_records, key=lambda x: abs(x[1] - target_value))
        elif criterion == "gte":
            candidates = [rec for rec in valid_records if rec[1] >= target_value]
            if not candidates:
                raise HTTPException(status_code=404, detail="No strike with metric greater than or equal to target value.")
            target_strike, _ = min(candidates, key=lambda x: abs(x[1] - target_value))
        elif criterion == "lte":
            candidates = [rec for rec in valid_records if rec[1] <= target_value]
            if not candidates:
                raise HTTPException(status_code=404, detail="No strike with metric less than or equal to target value.")
            target_strike, _ = min(candidates, key=lambda x: abs(x[1] - target_value))
        else:
            raise HTTPException(status_code=400, detail="Invalid criterion for strike configuration.")
        last_price = await get_last_price(symbol, expiration_date, entry_date, target_strike, option_type)
        print(f"Computed target strike: {target_strike}, with last price: {last_price} for option type: {option_type} and strike config: {strike_conf}")
        return target_strike
    else:
        # condition "is" - existing logic.
        data = await get_option_chain_data(symbol, expiration_date_str, entry_date_str)
        records = data.get("records", {}).get("data", [])
        if not records:
            raise HTTPException(status_code=404, detail=f"No option chain data found for {symbol} on {expiration_date_str} with entry date {entry_date_str}.")
        branch_lookup = "CE" if option_type.lower() == "call" else "PE"
        spot = None
        for rec in records:
            branch_data = rec.get(branch_lookup)
            if branch_data and branch_data.get("underlyingValue") is not None:
                spot = branch_data["underlyingValue"]
                break
        if spot is None:
            raise HTTPException(status_code=500, detail=f"Spot value not found in option chain for {symbol} on {expiration_date_str}. Strike config {strike_conf} {option_type}.")
        strikes_list = []
        for record in records:
            sp = record.get("strikePrice")
            if sp is not None:
                try:
                    sp_val = float(sp)
                    strikes_list.append(sp_val)
                except Exception:
                    continue
        if not strikes_list:
            raise HTTPException(status_code=500, detail="No strikes found in option chain data.")
        strikes = sorted(set(strikes_list))
        atm_strike = min(strikes, key=lambda x: abs(x - spot))
        try:
            if strike_conf.get("value") == "ATM":
                offset = 0
            else:
                offset = int("".join(filter(str.isdigit, strike_conf.get("value", ""))))
        except Exception:
            offset = 0
        idx = strikes.index(atm_strike)
        if option_type.lower() == "call":
            if strike_conf.get("value").startswith("OTM"):
                target_idx = idx + offset
            elif strike_conf.get("value").startswith("ITM"):
                target_idx = idx - offset
            else:
                target_idx = idx
        else:
            if strike_conf.get("value").startswith("OTM"):
                target_idx = idx - offset
            elif strike_conf.get("value").startswith("ITM"):
                target_idx = idx + offset
            else:
                target_idx = idx
        if target_idx < 0:
            target_idx = 0
        elif target_idx >= len(strikes):
            target_idx = len(strikes) - 1
        target_strike = strikes[target_idx]
        last_price = await get_last_price(symbol, expiration_date, entry_date, target_strike, option_type)
        print(f"Computed target strike: {target_strike}, with last price: {last_price} for option type: {option_type} and strike config: {strike_conf}")
        return target_strike


@router.post("/")
async def algo_backtest(request: StrategyPayoffRequest):
    """
    Bulk backtesting endpoint that computes the entry date (from calendar day entry condition),
    updates each leg's target expiration date and strike using UI selections (e.g., "Week (+1)", "ITM20", etc.),
    and then runs the bulk payoff calculation over the date range from the valid entry date to the expiry date.
    If the original entry date is a holiday (no option chain data), the entry date is advanced by up to 5 days.

    If testMonth is None, returns results for all months along with a global summary.
    """
    strategy = request.strategy
    test_year = request.testYear
    symbol = request.symbol

    entry_conditions = strategy.get("entryConditions", {})
    if not entry_conditions.get("calendarDayEnabled", False):
        raise HTTPException(status_code=400, detail="Strategy does not use calendar day entry.")

    try:
        entry_day = int(entry_conditions.get("dayOfMonth"))
    except (TypeError, ValueError):
        raise HTTPException(status_code=400, detail="Invalid or missing 'dayOfMonth' in entryConditions.")

    # If testMonth is None, process all months
    if request.testMonth is None:
        months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
        all_results = {}

        for month in months:
            try:
                # Process each month
                strategy = copy.deepcopy(request.strategy)
                month_result = await process_single_month(strategy, month, test_year, symbol, entry_day)
                all_results[month] = month_result
            except Exception as e:
                # Log the error but continue with other months
                print(f"Error processing month {month}: {str(e)}")
                all_results[month] = {"error": str(e)}

        # Compute a global summary based on the monthly results.
        global_summary = compute_global_summary(all_results)
        return {"all_months": all_results, "global_summary": global_summary}
    else:
        # Process single month as before
        return await process_single_month(strategy, request.testMonth, test_year, symbol, entry_day)


def compute_global_summary(all_results: dict) -> dict:
    """
    Computes a global summary from the monthly backtest results.
    Returns a dictionary with the total number of trades and counts for each exit condition:
       - total_trades: number of months with valid summary data.
       - stop_loss: number of months where exit_condition was 'stop_loss'
       - target_profit: number of months where exit_condition was 'target_profit'
       - expiry_dates: number of months where exit_condition was 'expiry'
    """
    total_trades = 0
    stop_loss_count = 0
    target_profit_count = 0
    expiry_count = 0
    for month, data in all_results.items():
        if data and "summary" in data:
            total_trades += 1
            exit_cond = data["summary"]["trade"]["result"]["exit_condition"]
            if exit_cond == "stop_loss":
                stop_loss_count += 1
            elif exit_cond == "target_profit":
                target_profit_count += 1
            elif exit_cond == "expiry":
                expiry_count += 1
    return {"total_trades": total_trades, "stop_loss": stop_loss_count, "target_profit": target_profit_count, "expiry_dates": expiry_count}


def calculate_p_and_l(strategy: dict, bulk_results: dict, computed_legs: List[OptionLeg]) -> dict:
    """
    Computes a summary of profit and loss over the backtest time series.

    Returns a dictionary with the following structure:

    {
      "max_profit": {
          "value": <max_profit_value>,
          "timestamp": <timestamp_of_max_profit>
      },
      "max_loss": {
          "value": <max_loss_value>,
          "timestamp": <timestamp_of_max_loss>
      },
      "trade": {
          "premium": <total premium>,
          "stop_loss": <trade_stop_loss>,       # computed as - total_premium * (stopLossValue/100)
          "target_profit": <trade_target>,        # computed as total_premium * (targetValue/100)
          "spot": {
              "entry": <entry spot price>,
              "exit": <exit spot price>
          },
          "result": {
              "success": <True/False>,           # True if profit target hit first, else False
              "exit_condition": <"stop_loss" or "target_profit" or "expiry">,
              "exit_condition_date": <timestamp when condition hit or expiry date>
          }
      }
    }

    Explanation:
    - For example, if the total premium is 68.5, with a targetValue of 60 and stopLossValue of 15:
         Trade Target = 68.5 * 0.60 = 41.1
         Trade Stop Loss = -68.5 * 0.15 = -10.28
    - The exit event is determined by iterating over the sorted time series.
      If no profit target or stop loss is hit, the trade is assumed to close at expiry,
      so the exit_condition is set to "expiry" with the final timestamp.
    """
    from datetime import datetime

    # Compute total premium from all legs (assuming op_pr is the premium for each leg)
    total_premium = sum((leg.op_pr * leg.lots if leg.tr_type == "b" else -leg.op_pr * leg.lots) for leg in computed_legs if leg.op_pr is not None)

    # Always compute trade target and stop loss thresholds.
    exitStopLossEnabled = strategy.get("exitStopLossEnabled", False)
    exitBookProfitsEnabled = strategy.get("exitBookProfitsEnabled", False)
    trade_target = total_premium * (float(strategy.get("targetValue", 0)) / 100.0)
    trade_stop_loss = -total_premium * (float(strategy.get("stopLossValue", 0)) / 100.0)

    # Get the time series data from the bulk results.
    time_series = bulk_results.get("results", [])
    if not time_series:
        return {}

    # Sort the time series by timestamp (assumes format "dd-MMM-yyyy").
    time_series.sort(key=lambda x: datetime.strptime(x["Timestamp"], "%d-%b-%Y"))

    entry_spot = time_series[0].get("Spot")
    last_spot = time_series[-1].get("Spot")

    # Find the maximum profit and maximum loss points.
    max_profit_point = max(time_series, key=lambda x: x["Profit/Loss"])
    max_loss_point = min(time_series, key=lambda x: x["Profit/Loss"])
    max_profit = max_profit_point["Profit/Loss"]
    max_profit_timestamp = max_profit_point["Timestamp"]
    max_loss = max_loss_point["Profit/Loss"]
    max_loss_timestamp = max_loss_point["Timestamp"]

    exit_event = None
    exit_condition = "None"
    rebalancing_actions_timestamps = []  # New list to track timestamps with rebalancing actions

    # Iterate over the time series to find the first exit event.
    for point in time_series:
        pl = point["Profit/Loss"]
        timestamp = point["Timestamp"]
        rebalancing_actions_per_ts = point.get("rebalancing_actions", {})

        if rebalancing_actions_per_ts:
            # Capture rebalancing actions at this timestamp
            # rebalancing_actions_at_ts = next((actions for ts, actions in zip(range(len(rebalancing_actions_per_ts)), rebalancing_actions_per_ts) if actions.get("timestamp") == timestamp), [])

            # Track rebalancing actions and their timestamps
            rebalancing_actions_timestamps.append({"timestamp": timestamp, "rebalancing_actions": rebalancing_actions_per_ts})  # Include rebalancing actions for this timestamp

        # Check for exit conditions
        if exitBookProfitsEnabled and pl >= trade_target:
            exit_event = point.copy()
            exit_condition = "target_profit"
            exit_event["Event Type"] = "Profit Target"
            break
        elif exitStopLossEnabled and pl <= trade_stop_loss:
            exit_event = point.copy()
            exit_condition = "stop_loss"
            exit_event["Event Type"] = "Stop Loss"
            break

    # If no exit event was found, use the final time point (trade closed at expiry).
    if exit_event is None:
        exit_event = time_series[-1].copy()
        exit_condition = "expiry"
        exit_event["Event Type"] = "Expiry"
        success = False
    else:
        success = True if exit_event["Event Type"] == "Profit Target" else False

    summary = {
        "max_profit": {"value": round(max_profit, 2), "timestamp": max_profit_timestamp},
        "max_loss": {"value": round(max_loss, 2), "timestamp": max_loss_timestamp},
        "trade": {
            "premium": round(total_premium, 2),
            "stop_loss": round(trade_stop_loss, 2),
            "target_profit": round(trade_target, 2),
            "spot": {"entry": entry_spot, "exit": exit_event.get("Spot")},
            "result": {"success": success, "exit_condition": exit_condition, "exit_condition_date": exit_event.get("Timestamp")},
            "rebalancing_actions_per_ts": rebalancing_actions_timestamps,  # Include rebalancing actions per timestamp
        },
    }
    return summary


async def process_single_month(strategy, test_month, test_year, symbol, entry_day):
    """Process a single month's backtest data"""
    # Compute the initial entry date.
    entry_date = compute_entry_date(entry_day, test_month, test_year)

    # We'll use each leg's target expiration date to validate the entry date.
    overall_expiry_date = None
    computed_legs: List[OptionLeg] = []
    for leg in strategy.get("legs", []):
        leg_expiry_rule = leg.get("expiry")  # e.g., "Week (+3)"
        target_exp_date = await get_target_expiration_date(symbol, test_year, leg_expiry_rule, entry_date)
        # Validate the entry date: if no data is available for the entry date, adjust it.
        valid_entry_date = await get_valid_entry_date(symbol, target_exp_date, entry_date)
        if valid_entry_date != entry_date:
            print(f"Entry date adjusted from {entry_date.strftime('%d-%b-%Y')} to {valid_entry_date.strftime('%d-%b-%Y')}")
            entry_date = valid_entry_date  # Update the entry date for all legs.
        # Use the first computed leg's expiration as overall expiry.
        if overall_expiry_date is None:
            overall_expiry_date = target_exp_date
        expiry_days = (target_exp_date - entry_date).days

        # Get the complete strike configuration dictionary.
        strike_conf = leg.get("strikeConfig", {})
        option_type_frontend = leg.get("optionType", "Call")
        # Process merge fields for premium or delta conditions.
        if strike_conf.get("condition") in ["premium", "delta"]:
            value_field = strike_conf.get("value")
            # Pattern supports expressions like {leg1.premium} optionally followed by an operator and a number.
            pattern = r"^\{leg(\d+)\.(premium|delta)\}(?:\s*([\/*+\-])\s*(\d+(?:\.\d+)?))?$"
            m = re.match(pattern, str(value_field))
            if m:
                ref_leg_num = int(m.group(1))
                ref_field = m.group(2)
                operator = m.group(3)  # may be None
                operand_str = m.group(4)  # may be None
                if ref_leg_num - 1 < len(computed_legs):
                    if ref_field == "premium":
                        base_value = computed_legs[ref_leg_num - 1].op_pr
                    elif ref_field == "delta":
                        raise HTTPException(status_code=501, detail="Delta based strike configuration not implemented.")
                    result_value = base_value
                    if operator and operand_str:
                        try:
                            operand = float(operand_str)
                        except Exception:
                            raise HTTPException(status_code=400, detail=f"Invalid numeric operand in merge field: {value_field}.")
                        if operator == "/":
                            result_value = base_value / operand
                        elif operator == "*":
                            result_value = base_value * operand
                        elif operator == "+":
                            result_value = base_value + operand
                        elif operator == "-":
                            result_value = base_value - operand
                        else:
                            raise HTTPException(status_code=400, detail=f"Unsupported operator {operator} in merge field: {value_field}.")
                    value_field = result_value
                else:
                    raise HTTPException(status_code=400, detail=f"Invalid merge field reference: {value_field}. Referenced leg does not exist.")
            else:
                try:
                    value_field = float(value_field)
                except:
                    raise HTTPException(status_code=400, detail="Invalid strike configuration value. Must be a merge field or numeric.")
            strike_conf["value"] = value_field

        # Convert strike_conf to a hashable tuple for caching.
        strike_conf_hashable = tuple(sorted(strike_conf.items()))
        target_strike = await get_target_strike(symbol, target_exp_date, strike_conf_hashable, entry_date, option_type_frontend)
        last_price = await get_last_price(symbol, target_exp_date, entry_date, target_strike, option_type_frontend)

        computed_leg = OptionLeg(
            id=str(leg["id"]),
            type="CE" if option_type_frontend.lower() == "call" else "PE",
            strike=target_strike,
            op_pr=last_price,  # Set op_pr to LTP.
            tr_type=leg.get("action", "s")[0].lower(),
            lots=leg.get("lots", 1),
            expiry_days=expiry_days,  # TODO Update to use new expiry, currently we use same expiry
            iv=leg.get("iv", 20),
            expiryDate=target_exp_date.strftime("%d-%b-%Y"),  # TODO Update to use new expiry, currently we use same expiry
        )
        computed_legs.append(computed_leg)

    if overall_expiry_date is None:
        overall_expiry_date = entry_date + timedelta(days=30)  # Fallback

    start_date = entry_date.strftime("%Y-%m-%d")
    end_date = overall_expiry_date.strftime("%Y-%m-%d")  # Overall expiry date from computed leg

    bulk_request = BulkPayoffRequest(
        legs=computed_legs,
        symbol=symbol,
        start=start_date,
        end=end_date,
        interval="1d",
        lot_size=1,
        multiplier=1,
    )

    try:
        result = await calculate_bulk_payoff_with_rebalancing(strategy, bulk_request)
    except Exception as e:
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error in bulk backtest: {str(e)}")

    # Compute the profit and loss summary using the new method.
    summary = calculate_p_and_l(strategy, result, computed_legs)

    result.update(
        {"Entry Date": entry_date.strftime("%d-%b-%Y"), "Overall Expiry Date": overall_expiry_date.strftime("%d-%b-%Y"), "Computed Legs": [leg.dict() for leg in computed_legs], "summary": summary}
    )
    return result
