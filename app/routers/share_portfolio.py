import json
import uuid

from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTP<PERSON><PERSON><PERSON>, Request
from typing import Any, Dict, List
from datetime import datetime
from pymongo.collection import Collection

from app.routers.portfolio import get_portfolio_data, get_portfolio_specific_data
from app.util.db.ai_bull_db import get_db
from app.util.menu import menu
from app.util.templates import templates

from .auth import get_current_user_id, get_user_data

router = APIRouter()


SHARED_PORTFOLIOS: Collection[Dict[str, Any]] = get_db().get_collection("shared_portfolios")


@router.get("/share/{share_uid}")
async def get_portfolios_main(
    request: Request,
    access_token: str = <PERSON><PERSON>(None),
    guestUserData: str = <PERSON><PERSON>(None),
):

    return templates.TemplateResponse(
        "portfolio/share-portfolio.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
        },
    )


@router.post("/share")
async def share_portfolio(
    data: Dict[str, Any],
    access_token: str = <PERSON><PERSON>(None),
):
    portfolio_uid = data["portfolio_uid"]
    share_type = data["share_type"]  # "static" or "dynamic"
    custom_name = data["custom_name"]

    share_data: Dict[str, Any] = {
        "uid": str(uuid.uuid4()), 
        "portfolio_uid": portfolio_uid, 
        "name": custom_name, 
        "type": share_type, 
        "user_id": get_current_user_id(access_token),
        "access_token": access_token, 
        "created_at": datetime.utcnow()
    }

    if share_type == "static":
        if portfolio_uid == "all":
            response = await get_portfolio_data(share_data["access_token"])
        else:
            response = await get_portfolio_specific_data(share_data["portfolio_uid"], share_data["access_token"])
        portfolio_data = response.body.decode()
        portfolio_data = json.loads(portfolio_data)
        share_data["snapshot"] = portfolio_data
    else:
        share_data["snapshot"] = None  # Dynamic will fetch live data

    SHARED_PORTFOLIOS.insert_one(share_data)
    return {"share_url": f"https://theaibull.com/portfolio/share/{share_data['uid']}"}


@router.get("/api/share/{share_uid}")
async def get_shared_portfolio(share_uid: str) -> Dict[str, Any]:
    share_data = SHARED_PORTFOLIOS.find_one({"uid": share_uid})
    if not share_data:
        raise HTTPException(status_code=404, detail="Portfolio not found")

    if share_data["type"] == "static":
        portfolio_data = share_data["snapshot"]
    else:
        # Fetch portfolio data dynamically for non-static shares
        if share_data["portfolio_uid"] == "all":
            response = await get_portfolio_data(share_data["access_token"])
        else:
            response = await get_portfolio_specific_data(share_data["portfolio_uid"], share_data["access_token"])
        portfolio_data = response.body.decode()
        portfolio_data = json.loads(portfolio_data)

    return {"name": share_data["name"], "type": share_data["type"], "portfolio": portfolio_data}


@router.post("/share/{share_uid}/comments")
async def add_comment(share_uid: str, data: Dict[str, str]):
    comment: Dict[str, Any] = {"text": data["comment"], "date": datetime.utcnow()}
    SHARED_PORTFOLIOS.update_one({"uid": share_uid}, {"$push": {"comments": comment}})
    return {"message": "Comment added"}


@router.get("/share/{share_uid}/comments")
async def get_comments(share_uid: str) -> List[Dict[str, Any]]:
    share_data = SHARED_PORTFOLIOS.find_one({"uid": share_uid})
    if not share_data:
        raise HTTPException(status_code=404, detail="Portfolio not found")
    return share_data.get("comments", [])
