from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union

import httpx
import numpy as np
import math
from async_lru import alru_cache
from fastapi import APIRouter, HTTPException
from py_vollib.black_scholes import black_scholes
from pydantic import BaseModel
from py_vollib.black_scholes.greeks.analytical import delta, gamma, theta, vega, rho

router = APIRouter()

# Global constants
RISK_FREE_RATE = 0.068  # Risk-free rate (6.8% - Indian T-Bill rate)


# OptionLeg model
class OptionLeg(BaseModel):
    id: str
    type: str  # 'CE' (Call), 'PE' (Put), or 'FUT' (Futures)
    strike: Optional[float] = None  # Strike price (for options)
    op_pr: Optional[float] = None  # Option premium (for options)
    entry_price: Optional[float] = None  # Entry price (for futures)
    tr_type: str  # 'b' for buy, 's' for sell
    lots: int = 1  # Number of lots
    expiry_days: int  # Days until expiry for this leg
    iv: Optional[float] = 0  # Implied volatility in percentage (e.g., 20 for 20%)
    expiryDate: Optional[str] = None  # Expiry date in string format (e.g., "30-Jan-2025")
    symbol: Optional[str] = None  # Symbol of the option


# PayoffRequest model for normal endpoint
class PayoffRequest(BaseModel):
    legs: List[OptionLeg]
    spot_price: float
    lot_size: Optional[int] = 1
    strikePricesDelta: Optional[float] = 1
    multiplier: Optional[int] = 1
    target_expiry_days: Optional[int] = None  # Days to target date (for calculating target day PnL)
    target_datetime: Optional[str] = None  # Target datetime in ISO format
    target_interval: Optional[str] = None  # Target interval
    backtesting_enabled: Optional[bool] = False  # Whether to enable backtesting
    greeks_multiply_by_lot_size: Optional[bool] = True  # Whether to multiply Greeks by lot size
    greeks_multiply_by_number_of_lots: Optional[bool] = True  # Whether to multiply Greeks by number of lots
    sd_mode: Optional[str] = "fixed"  # Standard deviation mode: "fixed" or "dynamic"
    sd_days: Optional[int] = 7  # Number of days for fixed SD calculation
    per_trade_greeks_multiply_by_lot_size: Optional[bool] = True  # Whether to multiply Greeks by lot size per trade
    per_trade_greeks_multiply_by_number_of_lots: Optional[bool] = True  # Whether to multiply Greeks by number of lots per trade


# BulkPayoffRequest model for bulk backtesting endpoint (legacy request remains unchanged)
class BulkPayoffRequest(BaseModel):
    legs: List[OptionLeg]
    symbol: str
    start: str  # e.g. '2021-01-01'
    end: str  # e.g. '2021-01-31'
    interval: str  # e.g. '1d'
    lot_size: Optional[int] = 1
    multiplier: Optional[int] = 1


# -------------------------------------------------------------------
# Cached external HTTP calls
# -------------------------------------------------------------------


@alru_cache(maxsize=32)
async def fetch_ohlc_data(symbol: str, start: str, end: str, interval: str) -> dict:
    base_url = "https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/yohlc"
    params = {"symbol": symbol, "start": start, "end": end, "interval": interval}
    async with httpx.AsyncClient() as client:
        response = await client.get(base_url, params=params)
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail="Error fetching OHLC data.")
        return response.json()


@alru_cache(maxsize=32)
async def fetch_option_chain_data(symbol: str, from_date_range: str, to_date_range: str, interval: str, expiry_date: str) -> dict:
    base_url = f"https://iam.theaibull.com/v1/wg7ttpouv7/symbol/options/{symbol}"
    params = {"from": from_date_range, "to": to_date_range, "interval": interval, "expiryDate": expiry_date}
    async with httpx.AsyncClient() as client:
        response = await client.get(base_url, params=params)
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail="Error fetching option chain data.")
        return response.json()



# -------------------------------------------------------------------
# Target Day Payoff Calculation Function
# -------------------------------------------------------------------


async def calculate_target_day_payoff(
    legs: List[OptionLeg], spot_price: float, target_expiry_days: int, target_spots: Any, lot_size: int = 1, multiplier: int = 1  # Using Any for numpy array to avoid typing issues
) -> List[Tuple[float, float]]:  # More specific tuple type
    """
    Calculate the expected payoff on the target day for each spot price in target_spots.

    Args:
        legs: List of OptionLeg objects representing the strategy
        spot_price: Current spot price
        target_expiry_days: Days until the target date
        target_spots: Array of spot prices to calculate payoff for
        lot_size: Lot size for the strategy
        multiplier: Multiplier for the strategy

    Returns:
        List of tuples (spot_price, pnl) for each spot price in target_spots
    """
    r = 0.068  # Risk-free rate (6.8% - Indian T-Bill rate)
    target_day_payoff = []

    for spot in target_spots:
        # Skip invalid spot prices (important to prevent math domain errors)
        if spot <= 0:
            continue

        total_target_pnl = 0
        for leg in legs:
            # Calculate PnL for this leg at this spot price
            leg_pnl = await calculate_individual_leg_target_pnl(leg=leg, spot=spot, target_expiry_days=target_expiry_days, lot_size=lot_size, multiplier=multiplier)
            total_target_pnl += leg_pnl

        target_day_payoff.append((spot, total_target_pnl))

    return target_day_payoff


async def calculate_individual_leg_target_pnl(leg: OptionLeg, spot: float, target_expiry_days: int, lot_size: int = 1, multiplier: int = 1) -> float:
    """
    Calculate the PnL of an individual leg at the target day for a specific spot price.

    Args:
        leg: The option leg
        spot: The spot price to calculate PnL for
        target_expiry_days: Days until the target date
        lot_size: Lot size for the strategy
        multiplier: Multiplier for the strategy

    Returns:
        The PnL for this leg at the given spot price
    """
    try:
        leg_pnl = 0.0

        if leg.type in ["CE", "PE"]:
            # Get safely unwrapped values with defaults to avoid None errors
            strike = leg.strike if leg.strike is not None else 0
            op_pr = leg.op_pr if leg.op_pr is not None else 0
            iv = leg.iv if leg.iv is not None and leg.iv > 0 else 20.0  # Default IV of 20%

            # Calculate remaining time to expiry after target date
            remaining_days = max(0, leg.expiry_days - target_expiry_days)
            time_to_expiry = remaining_days / 365.0

            # Use implied volatility to price the option at the target date
            sigma = iv / 100  # Convert percentage IV to decimal

            try:
                # Black-Scholes pricing for target date
                if remaining_days > 0:  # If option hasn't expired yet
                    if spot > 0 and strike > 0 and time_to_expiry > 0 and sigma > 0:
                        try:
                            option_price_at_target = black_scholes("c" if leg.type == "CE" else "p", spot, strike, time_to_expiry, RISK_FREE_RATE, sigma)
                            if option_price_at_target is None or not math.isfinite(option_price_at_target):
                                # Fallback if BS calculation fails
                                intrinsic_payoff = max(spot - strike, 0) if leg.type == "CE" else max(strike - spot, 0)
                                option_price_at_target = intrinsic_payoff
                        except Exception as e:
                            print(f"Error in black_scholes: {e}")
                            # Fallback to intrinsic value if BS calculation fails
                            intrinsic_payoff = max(spot - strike, 0) if leg.type == "CE" else max(strike - spot, 0)
                            option_price_at_target = intrinsic_payoff
                    else:
                        # If inputs invalid for BS, treat as expired (intrinsic value)
                        intrinsic_payoff = max(spot - strike, 0) if leg.type == "CE" else max(strike - spot, 0)
                        option_price_at_target = intrinsic_payoff

                    if leg.tr_type == "b":
                        leg_pnl = (option_price_at_target - op_pr) * leg.lots * lot_size * multiplier
                    else:
                        leg_pnl = (op_pr - option_price_at_target) * leg.lots * lot_size * multiplier
                else:  # If option has expired by target date
                    intrinsic_payoff = max(spot - strike, 0) if leg.type == "CE" else max(strike - spot, 0)
                    if leg.tr_type == "b":
                        leg_pnl = (intrinsic_payoff - op_pr) * leg.lots * lot_size * multiplier
                    else:
                        leg_pnl = (op_pr - intrinsic_payoff) * leg.lots * lot_size * multiplier
            except Exception as e:
                print(f"Error calculating target day payoff for spot {spot}, leg {leg.symbol}: {e}")
                # Return 0 PnL if there's an error
                return 0.0

        elif leg.type == "FUT":
            # Get safely unwrapped values with defaults
            entry_price = leg.entry_price if leg.entry_price is not None else 0

            # Futures pricing is the same as spot at any point
            if leg.tr_type == "b":
                leg_pnl = (spot - entry_price) * leg.lots * lot_size * multiplier
            else:
                leg_pnl = (entry_price - spot) * leg.lots * lot_size * multiplier

        return leg_pnl
    except Exception as e:
        print(f"Unexpected error in calculate_individual_leg_target_pnl for spot {spot}, leg {getattr(leg, 'symbol', None)}: {e}")
        return 0.0


def calculate_individual_leg_expiry_pnl(leg: OptionLeg, spot: float, lot_size: int = 1, multiplier: int = 1) -> float:
    """
    Calculate the PnL of an individual leg at expiry for a specific spot price.
    Args:
        leg: The option leg
        spot: The spot price to calculate PnL for
        lot_size: Lot size for the strategy
        multiplier: Multiplier for the strategy
    Returns:
        The PnL for this leg at the given spot price at expiry
    """
    try:
        if leg.type in ["CE", "PE"]:
            strike = leg.strike if leg.strike is not None else 0.0
            op_pr = leg.op_pr if leg.op_pr is not None else 0.0
            intrinsic_payoff = max(spot - strike, 0) if leg.type == "CE" else max(strike - spot, 0)
            if leg.tr_type == "b":
                return (intrinsic_payoff - op_pr) * leg.lots * lot_size * multiplier
            else:
                return (op_pr - intrinsic_payoff) * leg.lots * lot_size * multiplier
        elif leg.type == "FUT":
            entry_price = leg.entry_price if leg.entry_price is not None else 0.0
            if leg.tr_type == "b":
                return (spot - entry_price) * leg.lots * lot_size * multiplier
            else:
                return (entry_price - spot) * leg.lots * lot_size * multiplier
        return 0.0
    except Exception as e:
        print(f"Unexpected error in calculate_individual_leg_expiry_pnl for spot {spot}, leg {getattr(leg, 'symbol', None)}: {e}")
        return 0.0


# Function for calculating per-trade details
async def calculate_per_trade_details(
    leg: OptionLeg,
    spot_price: float,
    target_expiry_days: Optional[int] = None,
    lot_size: int = 1,
    multiplier: int = 1,
    per_trade_greeks_multiply_by_lot_size: bool = False,
    per_trade_greeks_multiply_by_number_of_lots: bool = False,
    backtesting_enabled: bool = False,
) -> Dict[str, Any]:
    """
    Calculate per-trade details for a single leg.

    Args:
        leg: The option leg
        spot_price: Current spot price
        target_expiry_days: Days until the target date (optional)
        lot_size: Lot size for the strategy
        multiplier: Multiplier for the strategy
        per_trade_greeks_multiply_by_lot_size: Whether to multiply Greeks by lot size
        per_trade_greeks_multiply_by_number_of_lots: Whether to multiply Greeks by number of lots
        backtesting_enabled: Whether backtesting is enabled (if true, skip greek calculations)

    Returns:
        Dictionary containing details for the leg
    """
    leg_detail = {
        "tag": f"leg:{leg.symbol}{leg.expiryDate}{leg.strike:.1f}{leg.type}" if leg.type in ["CE", "PE"] else f"leg:{leg.symbol}FUT",
        "entry_price": float(leg.op_pr) if leg.op_pr is not None else float(leg.entry_price if leg.entry_price is not None else 0),
        "greeks": {"theta": 0.0, "delta": 0.0, "gamma": 0.0, "vega": 0.0, "rho": 0.0, "raw_delta": 0.0, "raw_gamma": 0.0, "raw_theta": 0.0, "raw_vega": 0.0, "raw_rho": 0.0},
        "strike": float(leg.strike) if leg.strike is not None else None,
        "optionType": str(leg.type) if leg.type is not None else None,
        "expiry": str(leg.expiryDate) if leg.expiryDate is not None else None,
    }

    # Calculate theoretical prices and greeks
    if leg.type in ["CE", "PE"] and not backtesting_enabled:
        time_years = leg.expiry_days / 365.0
        sigma = (leg.iv / 100) if (leg.iv is not None and leg.iv > 0) else 0.20

        # Calculate Greeks
        try:
            option_type = "c" if leg.type == "CE" else "p"
            strike_price = leg.strike or 0

            # Calculate Greeks using py_vollib with NaN checking
            base_delta = delta(option_type, spot_price, strike_price, time_years, RISK_FREE_RATE, sigma)
            base_gamma = gamma(option_type, spot_price, strike_price, time_years, RISK_FREE_RATE, sigma)
            base_theta = theta(option_type, spot_price, strike_price, time_years, RISK_FREE_RATE, sigma)  # Annual theta
            base_vega = vega(option_type, spot_price, strike_price, time_years, RISK_FREE_RATE, sigma)  # Raw vega
            base_rho = rho(option_type, spot_price, strike_price, time_years, RISK_FREE_RATE, sigma)  # Raw rho

            # Check for NaN values and replace with default values
            base_delta = 0.0 if math.isnan(base_delta) else base_delta
            base_gamma = 0.0 if math.isnan(base_gamma) else base_gamma
            base_theta = 0.0 if math.isnan(base_theta) else base_theta
            base_vega = 0.0 if math.isnan(base_vega) else base_vega
            base_rho = 0.0 if math.isnan(base_rho) else base_rho

            # Apply sign adjustment for sell orders
            if leg.tr_type == "s":
                base_delta = -base_delta
                base_gamma = -base_gamma
                base_theta = -base_theta
                base_vega = -base_vega
                base_rho = -base_rho

            # Store the raw values (without any multipliers)
            leg_detail["greeks"]["raw_delta"] = round(base_delta, 4)
            leg_detail["greeks"]["raw_gamma"] = round(base_gamma, 6)
            leg_detail["greeks"]["raw_theta"] = round(base_theta, 2)
            leg_detail["greeks"]["raw_vega"] = round(base_vega, 2)
            leg_detail["greeks"]["raw_rho"] = round(base_rho, 4)

            # Apply multipliers if requested
            final_delta = base_delta
            final_gamma = base_gamma
            final_theta = base_theta
            final_vega = base_vega
            final_rho = base_rho

            # Apply number of lots multiplier if requested
            if per_trade_greeks_multiply_by_number_of_lots:
                final_delta *= leg.lots
                final_gamma *= leg.lots
                final_theta *= leg.lots
                final_vega *= leg.lots
                final_rho *= leg.lots

            # Apply lot size multiplier if requested
            if per_trade_greeks_multiply_by_lot_size:
                final_delta *= lot_size
                final_gamma *= lot_size
                final_theta *= lot_size
                final_vega *= lot_size
                final_rho *= lot_size

            # Store the final values with multipliers applied
            leg_detail["greeks"]["delta"] = round(final_delta, 4)
            leg_detail["greeks"]["gamma"] = round(final_gamma, 6)
            leg_detail["greeks"]["theta"] = round(final_theta, 2)
            leg_detail["greeks"]["vega"] = round(final_vega, 2)
            leg_detail["greeks"]["rho"] = round(final_rho, 4)

        except Exception as e:
            print(f"Error calculating Greeks: {e}")
            # Keep default values if calculation fails

        # Calculate target price if target_expiry_days is provided
        if target_expiry_days is not None:
            remaining_days = max(0, leg.expiry_days - target_expiry_days)
            time_to_expiry = remaining_days / 365.0

            try:
                if remaining_days > 0:
                    leg_detail["price_at_target"] = float(black_scholes("c" if leg.type == "CE" else "p", spot_price, leg.strike or 0, time_to_expiry, RISK_FREE_RATE, sigma))
                else:
                    # If option has expired at target date, use intrinsic value
                    leg_detail["price_at_target"] = max(spot_price - (leg.strike or 0), 0) if leg.type == "CE" else max((leg.strike or 0) - spot_price, 0)

                # Calculate target P&L
                entry_price = leg.op_pr or 0  # Default to 0 if None
                target_price = leg_detail["price_at_target"]
                leg_detail["target_pnl"] = (target_price - entry_price) * leg.lots * lot_size * multiplier if leg.tr_type == "b" else (entry_price - target_price) * leg.lots * lot_size * multiplier
            except Exception as e:
                print(f"Error calculating target price: {e}")
                leg_detail["price_at_target"] = 0
                leg_detail["target_pnl"] = 0

    else:  # Futures
        # For futures, set appropriate Greeks
        base_delta = 1.0 if leg.tr_type == "b" else -1.0

        leg_detail["greeks"]["raw_delta"] = base_delta

        # Apply multipliers
        final_delta = base_delta

        if per_trade_greeks_multiply_by_number_of_lots:
            final_delta *= leg.lots

        if per_trade_greeks_multiply_by_lot_size:
            final_delta *= lot_size

        leg_detail["greeks"]["delta"] = final_delta

        if target_expiry_days is not None:
            leg_detail["price_at_target"] = spot_price
            entry_price = leg.entry_price or 0  # Default to 0 if None
            leg_detail["target_pnl"] = (spot_price - entry_price) * leg.lots * lot_size * multiplier if leg.tr_type == "b" else (entry_price - spot_price) * leg.lots * lot_size * multiplier

    return leg_detail


async def generate_tabular_payoffs(
    legs: List[OptionLeg],
    spot_price: float,
    lot_size: int,
    multiplier: int,
    target_expiry_days: Optional[int] = None,
    target_interval: Optional[str] = None,
) -> list:
    """
    Generate tabular payoffs with equal intervals centered at the current price.
    This function now directly calculates PnL values for each spot price point
    using the same logic as calculate_payoff and calculate_target_day_payoff.

    Args:
        legs: List of OptionLeg objects representing the strategy
        spot_price: Current spot price
        lot_size: Lot size for the strategy
        multiplier: Multiplier for the strategy
        target_expiry_days: Optional days until the target date
        target_interval: Optional interval between price points (defaults to 50.0)

    Returns:
        List of tabular payoff objects in the format needed for the UI
    """
    tabular_payoffs = []

    # Define the number of points above and below the current price
    NUM_POINTS_EACH_SIDE = 6  # 6 points above + 6 points below + 1 center = 13 points total

    interval = 50.0  # Default interval

    if target_interval:
        try:
            interval = float(target_interval)
        except:
            interval = 50.0  # Fallback to default if conversion fails

    # The center price (use exact spot price)
    center_price = float(spot_price)

    # Generate price points from center_price with equal intervals
    tabular_prices = []

    # Always include the exact center point first
    tabular_prices.append(center_price)

    # Calculate the nearest interval-aligned price above the center price
    # Round up to the next multiple of interval
    first_above = center_price
    if center_price % interval != 0:
        first_above = (int(center_price / interval) + 1) * interval

    # Calculate the nearest interval-aligned price below the center price
    # Round down to the next multiple of interval
    first_below = center_price
    if center_price % interval != 0:
        first_below = (int(center_price / interval) - 1) * interval

    # If center price is already aligned with the interval, adjust first_above and first_below
    if center_price % interval == 0:
        first_above = center_price + interval
        first_below = center_price - interval

    # Add points above center_price (all multiples of interval)
    for i in range(NUM_POINTS_EACH_SIDE):
        next_price = first_above + i * interval
        # Ensure generated prices are non-negative
        if next_price >= 0:
            tabular_prices.append(next_price)

    # Add points below center_price (all multiples of interval)
    for i in range(NUM_POINTS_EACH_SIDE):
        next_price = first_below - i * interval
        # Ensure generated prices are non-negative
        if next_price >= 0:
            tabular_prices.append(next_price)
    # --- End logic to generate interval-aligned prices (Reverted) ---

    # Remove duplicates and sort the prices
    tabular_prices = sorted(list(set(p for p in tabular_prices if p >= 0)))  # Ensure non-negative and unique

    # For each price point, directly calculate the payoff values at target and expiry
    for price in tabular_prices:
        if price < 0:
            continue  # Should not happen due to previous checks, but safeguard

        percent_diff = ((price - center_price) / center_price) * 100 if center_price != 0 else 0

        # Calculate payoff at expiry using the new centralized function
        total_expiry_pnl = 0
        for leg in legs:
            total_expiry_pnl += calculate_individual_leg_expiry_pnl(leg=leg, spot=price, lot_size=lot_size, multiplier=multiplier)

        # Calculate payoff at target directly using logic from calculate_target_day_payoff
        total_target_pnl = None
        if target_expiry_days is not None and target_expiry_days >= 0:
            total_target_pnl = 0.0  # Initialize as float
            for leg in legs:
                # Use the centralized async function for target day PnL
                leg_pnl = await calculate_individual_leg_target_pnl(leg=leg, spot=price, target_expiry_days=target_expiry_days, lot_size=lot_size, multiplier=multiplier)
                total_target_pnl += leg_pnl

        # Create the payoff entry
        payoff_entry = {"percent_diff_from_current_price": round(percent_diff, 2), "at": float(price)}

        # Add expiry payoff
        payoff_entry["payoff_at_expiry"] = round(total_expiry_pnl, 2)

        # Add target payoff if calculated
        if total_target_pnl is not None:
            payoff_entry["payoff_at_target"] = round(total_target_pnl, 2)

        tabular_payoffs.append(payoff_entry)

    # Sort by "at" (spot price) - sorting already happened earlier
    # tabular_payoffs.sort(key=lambda x: x["at"])

    return tabular_payoffs


# Note: Exponential extension is currently disabled, Keep it as is for now In future we can add it bac
def add_exponential_points(lower_points: list, upper_points: list, step_size: int, spot_price: float) -> (list, list):
    """Generate exponential points below the lowest and above the highest linear points."""
    exp_points_lower = []
    exp_points_upper = []
    if lower_points and upper_points:
        exp_start_lower = lower_points[0]
        exp_start_upper = upper_points[-1]
        exp_step = step_size
        upper_bound = max(spot_price * 2, exp_start_upper * 1.5)
        while True:
            next_lower = exp_start_lower - exp_step
            next_upper = exp_start_upper + exp_step
            added = False
            if next_lower >= 0:
                exp_points_lower.append(next_lower)
                exp_start_lower = next_lower
                added = True
            if next_upper <= upper_bound:
                exp_points_upper.append(next_upper)
                exp_start_upper = next_upper
                added = True
            exp_step *= 2
            if not added:
                break
    return exp_points_lower, exp_points_upper


def generate_price_points(strikePricesDelta: float, spot_price: float) -> List[float]:
    """Generate price points with dynamic step sizes equally distributed around spot price, and add exponential points beyond the linear range (symmetric above and below)."""
    # Calculate step size as integer, at least 1
    step_size = max(1, int(strikePricesDelta / 5))

    # Calculate max points based on step size
    max_points = round(spot_price / strikePricesDelta)
    if strikePricesDelta == 1:
        max_points = spot_price / 2

    # Ensure max_points is at least 10 and an even number for equal distribution, and cap at 500
    max_points = max(10, int(max_points))
    max_points = min(700, max_points)
    if max_points % 2 != 0:
        max_points += 1

    # Calculate how many points to generate on each side
    points_per_side = max_points // 2

    # Center price (round to nearest step_size)
    center_price = round(spot_price / step_size) * step_size

    # Generate points below center_price (linear)
    lower_points = [center_price - (i * step_size) for i in range(1, points_per_side + 1)]
    lower_points = [p for p in lower_points if p >= 0]
    lower_points.reverse()  # Order from lowest to highest

    # Generate points above center_price (linear)
    upper_points = [center_price + (i * step_size) for i in range(1, points_per_side + 1)]

    # Combine points with center in the middle (linear range)
    linear_points = lower_points + [center_price] + upper_points

    # Note: Exponential extension is currently disabled, Keep it as is for now In future we can add it back
    # # --- Exponential extension (symmetric above and below) ---
    # exp_points_lower, exp_points_upper = add_exponential_points(lower_points, upper_points, step_size, spot_price)

    # # Merge all points, remove duplicates, and sort
    # all_points = set(linear_points + exp_points_lower + exp_points_upper)
    # price_points = sorted(float(p) for p in all_points if p >= 0)

    return linear_points


async def calculate_payoff(
    legs: List[OptionLeg],
    spot_price: float,
    strikePricesDelta: float = 1,
    lot_size: int = 1,
    multiplier: int = 1,
    target_expiry_days: Optional[int] = None,
    target_datetime: Optional[str] = None,
    target_interval: Optional[str] = None,
    backtesting_enabled: bool = False,
    greeks_multiply_by_lot_size: bool = False,
    greeks_multiply_by_number_of_lots: bool = False,
    per_trade_greeks_multiply_by_lot_size: bool = False,
    per_trade_greeks_multiply_by_number_of_lots: bool = False,
    sd_mode: str = "fixed",
    sd_days: int = 7,
):
    """
    Calculate option payoff for given legs at various spot prices.
    If target_expiry_days or target_datetime is provided, also calculate the expected P&L on the target date.
    """
    if spot_price <= 0:
        raise ValueError("spot_price must be greater than zero.")

    # Generate price points using the new dynamic method
    spot_range = generate_price_points(strikePricesDelta, spot_price)

    # Standard payoff calculation (at expiry)
    payoff_data = []
    for spot in spot_range:
        total_pnl = 0
        for leg in legs:
            total_pnl += calculate_individual_leg_expiry_pnl(leg=leg, spot=spot, lot_size=lot_size, multiplier=multiplier)
        payoff_data.append((spot, total_pnl))

    # Target day payoff calculation (if target information is provided)
    target_day_payoff = []

    # Calculate target day payoff if target_expiry_days is provided
    if target_expiry_days is not None:
        target_day_payoff = await calculate_target_day_payoff(
            legs=legs, spot_price=spot_price, target_expiry_days=target_expiry_days, target_spots=spot_range, lot_size=lot_size, multiplier=multiplier
        )

    payoff_data.sort(key=lambda x: x[0])
    payoff_table = [{"Spot Price": round(spot, 2), "Profit/Loss": round(pnl, 2)} for spot, pnl in payoff_data]

    # Add target day P&L to payoff table if available
    target_day_map = {}
    if target_day_payoff:
        target_day_payoff.sort(key=lambda x: x[0])
        # Create a mapping of spot prices to target PnL for easier lookup
        target_day_map = {round(spot, 2): round(target_pnl, 2) for spot, target_pnl in target_day_payoff}

        # For each entry in the payoff table, find the closest matching spot price in the target_day_map
        for entry in payoff_table:
            spot = entry["Spot Price"]
            closest_spot = min(target_day_map.keys(), key=lambda x: abs(x - spot), default=None)
            if closest_spot is not None:
                entry["Target Day P&L"] = target_day_map[closest_spot]

    # Generate tabular payoffs with equal intervals centered at current price using the dedicated function
    tabular_payoffs = await generate_tabular_payoffs(legs=legs, spot_price=spot_price, lot_size=lot_size, multiplier=multiplier, target_expiry_days=target_expiry_days, target_interval=target_interval)

    # Calculate standard deviation values
    standard_deviation = None
    if sd_mode in ["fixed", "dynamic"]:
        # Determine the time period for SD calculation
        sd_time_years = 0.0
        if sd_mode == "fixed":
            sd_time_years = sd_days / 365.0
        elif sd_mode == "dynamic" and target_expiry_days is not None:
            sd_time_years = target_expiry_days / 365.0

        # Only calculate SD if we have a valid time period
        if sd_time_years > 0 and spot_price > 0:
            # Calculate weighted average IV across all legs
            total_iv = 0.0
            total_weight = 0.0

            for leg in legs:
                if leg.type in ["CE", "PE"] and leg.iv is not None and leg.iv > 0:
                    weight = leg.lots
                    total_iv += (leg.iv / 100) * weight
                    total_weight += weight

            # Use default IV if no valid IV found
            avg_iv = total_iv / total_weight if total_weight > 0 else 0.20

            # Calculate standard deviations as price levels
            standard_deviation = spot_price * avg_iv * math.sqrt(sd_time_years)

            # Store SD values for later use
            standard_deviation = {
                "standard_deviation": standard_deviation,
                "Current": spot_price,
                "IV": round(avg_iv * 100, 2),  # Convert back to percentage
                "Days": sd_days if sd_mode == "fixed" else target_expiry_days,
            }

    # Create a separate table specifically for target intervals if provided
    target_table = []
    if target_interval and target_day_payoff:
        target_table = [{"Spot Price": round(spot, 2), "Target Day P&L": round(pnl, 2)} for spot, pnl in target_day_payoff]

    breakeven_points = []
    tolerance = 1e-6
    all_pnl_zero = all(abs(entry["Profit/Loss"]) < tolerance for entry in payoff_table)

    if payoff_data and not all_pnl_zero:
        for i in range(len(payoff_data) - 1):
            spot1, pl1 = payoff_data[i]
            spot2, pl2 = payoff_data[i + 1]
            if abs(pl1) < tolerance:
                breakeven_points.append(spot1)
            elif pl1 * pl2 < 0:
                breakeven = spot1 - pl1 * (spot2 - spot1) / (pl2 - pl1)
                breakeven_points.append(breakeven)
        if abs(payoff_data[-1][1]) < tolerance:
            breakeven_points.append(payoff_data[-1][0])

    breakeven_display = []
    if breakeven_points:
        for b in breakeven_points:
            b_int = int(round(b))
            pct_diff = ((b_int - spot_price) / spot_price) * 100
            sign = "+" if pct_diff >= 0 else ""
            breakeven_display.append(f"{b_int} ({sign}{pct_diff:.2f}%)")
    else:
        breakeven_display = ["N/A"]

    max_profit = max(entry["Profit/Loss"] for entry in payoff_table)
    max_loss = min(entry["Profit/Loss"] for entry in payoff_table)
    ZERO_THRESHOLD = 1e-6
    if abs(max_loss) < ZERO_THRESHOLD:
        risk_reward_ratio = float("inf")
        risk_reward_display = "∞"
    else:
        risk_reward_ratio = abs(max_profit / max_loss)
        risk_reward_display = f"{risk_reward_ratio:.2f}"

    N = 10000
    total_pnls = np.zeros(N)
    for leg in legs:
        T = leg.expiry_days / 365.0
        sigma = (leg.iv / 100) if (leg.iv is not None and leg.iv > 0) else 0.20
        Z = np.random.normal(0, 1, N)
        S_T = spot_price * np.exp((RISK_FREE_RATE - 0.5 * sigma**2) * T + sigma * np.sqrt(T) * Z)
        if leg.type in ["CE", "PE"]:
            intrinsic_values = np.maximum(S_T - leg.strike, 0) if leg.type == "CE" else np.maximum(leg.strike - S_T, 0)
            pnls = (intrinsic_values - leg.op_pr) if leg.tr_type == "b" else (leg.op_pr - intrinsic_values)
        elif leg.type == "FUT":
            pnls = (S_T - leg.entry_price) if leg.tr_type == "b" else (leg.entry_price - S_T)
        total_pnls += pnls * leg.lots * lot_size * multiplier

    # Convert NumPy value to Python native type
    pop = float((total_pnls > 0).sum()) / N * 100

    option_legs = [leg for leg in legs if leg.type in ["CE", "PE"]]
    total_time_value = 0
    total_intrinsic_value = 0
    if option_legs:
        sum_time = 0
        sum_intrinsic = 0
        for leg in option_legs:
            leg_T = leg.expiry_days / 365.0
            sigma = (leg.iv / 100) if (leg.iv is not None and leg.iv > 0) else 0.20
            theoretical_price = (
                black_scholes("c", spot_price, leg.strike, leg_T, RISK_FREE_RATE, sigma) if leg.type == "CE" else black_scholes("p", spot_price, leg.strike, leg_T, RISK_FREE_RATE, sigma)
            )
            intrinsic = max(spot_price - leg.strike, 0) if leg.type == "CE" else max(leg.strike - spot_price, 0)
            time_val = theoretical_price - intrinsic
            sum_time += time_val
            sum_intrinsic += intrinsic
        total_time_value = sum_time / len(option_legs)
        total_intrinsic_value = sum_intrinsic

    net_premium = 0
    for leg in legs:
        if leg.type in ["CE", "PE"] and leg.op_pr is not None:
            if leg.tr_type == "b":
                net_premium += leg.op_pr * leg.lots * lot_size * multiplier
            else:
                net_premium -= leg.op_pr * leg.lots * lot_size * multiplier

    base_premium = abs(net_premium) if net_premium != 0 else 1
    max_profit_pct = (max_profit / base_premium) * 100
    max_loss_pct = (abs(max_loss) / base_premium) * 100

    effective_lot_value = f"₹{lot_size * multiplier} (1 x {lot_size * multiplier})"

    current_pl = 0
    for leg in legs:
        current_pl += calculate_individual_leg_expiry_pnl(leg=leg, spot=spot_price, lot_size=lot_size, multiplier=multiplier)

    # Add target day information if provided
    result = {
        "Max Profit": float(round(max_profit, 2)),
        "Max Loss": float(round(max_loss, 2)),
        "Breakeven": breakeven_display,
        "Risk-Reward": risk_reward_display,
        "POP": f"{round(pop, 2)}%",
        "Time Value": float(round(total_time_value, 2)),
        "Intrinsic Value": float(round(total_intrinsic_value, 2)),
        "Max Profit %": f"{round(max_profit_pct, 2)}%",
        "Max Loss %": f"{round(max_loss_pct, 2)}%",
        "Payoff Table": payoff_table,
        "Effective Lot Value": effective_lot_value,
        "Net Premium": float(round(net_premium, 2)),
        "Spot P/L": float(round(current_pl, 2)),
        "per_trade_details": [],  # Add per-trade details array
        "tabular_payoffs": tabular_payoffs,  # Add the tabular payoffs
    }

    # Add standard_deviation to the result if available
    if standard_deviation is not None:
        result["standard_deviation"] = standard_deviation

    # Calculate per-trade details for each leg
    for leg in legs:
        leg_detail = await calculate_per_trade_details(
            leg=leg,
            spot_price=spot_price,
            target_expiry_days=target_expiry_days,
            lot_size=lot_size,
            multiplier=multiplier,
            per_trade_greeks_multiply_by_lot_size=per_trade_greeks_multiply_by_lot_size,
            per_trade_greeks_multiply_by_number_of_lots=per_trade_greeks_multiply_by_number_of_lots,
            backtesting_enabled=backtesting_enabled,
        )
        result["per_trade_details"].append(leg_detail)

    # Calculate and add strategy-level Greeks to the result
    total_delta = 0
    total_gamma = 0
    total_theta = 0
    total_vega = 0
    total_rho = 0

    for detail in result["per_trade_details"]:
        if "greeks" in detail:
            # Find the corresponding leg to access its lots value
            leg_tag = detail["tag"]
            leg = next(
                (l for l in legs if (l.type in ["CE", "PE"] and f"leg:{l.symbol}{l.expiryDate}{l.strike:.1f}{l.type}" == leg_tag) or (l.type == "FUT" and f"leg:{l.symbol}FUT" == leg_tag)), None
            )

            if leg:
                # Multiply by lot size if requested, we need to use the raw values here
                if greeks_multiply_by_number_of_lots:
                    delta_val = detail["greeks"]["raw_delta"] * leg.lots
                    gamma_val = detail["greeks"]["raw_gamma"] * leg.lots
                    theta_val = detail["greeks"]["raw_theta"] * leg.lots
                    vega_val = detail["greeks"]["raw_vega"] * leg.lots
                    rho_val = detail["greeks"]["raw_rho"] * leg.lots
                else:
                    # Get the individual Greek values
                    delta_val = detail["greeks"]["raw_delta"]
                    gamma_val = detail["greeks"]["raw_gamma"]
                    theta_val = detail["greeks"]["raw_theta"]
                    vega_val = detail["greeks"]["raw_vega"]
                    rho_val = detail["greeks"]["raw_rho"]

                # Add to the totals
                total_delta += delta_val
                total_gamma += gamma_val
                total_theta += theta_val
                total_vega += vega_val
                total_rho += rho_val

    # Apply lot size multiplier if requested
    if greeks_multiply_by_lot_size:
        lot_multiplier = lot_size
    else:
        lot_multiplier = 1

    # Create the summary with the final calculated values
    if not backtesting_enabled:
        result["summary"] = {
            "greeks": {
                "delta": float(round(total_delta * lot_multiplier, 2)),
                "gamma": float(round(total_gamma * lot_multiplier, 6)),
                "theta": float(round(total_theta * lot_multiplier, 2)),
                "vega": float(round(total_vega * lot_multiplier, 2)),
                "rho": float(round(total_rho * lot_multiplier, 2)),
            },
            "lot_size": int(lot_size),
            "underlying_price": float(spot_price),
            "multipliers": {"greeks_multiply_by_lot_size": bool(greeks_multiply_by_lot_size), "greeks_multiply_by_number_of_lots": bool(greeks_multiply_by_number_of_lots)},
        }
    else:
        result["summary"] = {
            "greeks": {
                "delta": 0.0,
                "gamma": 0.0,
                "theta": 0.0,
                "vega": 0.0,
                "rho": 0.0,
            },
            "lot_size": int(lot_size),
            "underlying_price": float(spot_price),
            "multipliers": {"greeks_multiply_by_lot_size": bool(greeks_multiply_by_lot_size), "greeks_multiply_by_number_of_lots": bool(greeks_multiply_by_number_of_lots)},
        }

    # Add target days info if provided
    if target_expiry_days is not None:
        result["Target Days"] = target_expiry_days

    # Add target table if available
    if target_table:
        result["Target Table"] = target_table

    return result


@router.post("/options/payoff")
async def calculate_payoff_endpoint(request: PayoffRequest):
    try:
        if request.spot_price <= 0:
            raise HTTPException(status_code=400, detail="spot_price must be greater than zero.")
        result = await calculate_payoff(
            legs=request.legs,
            spot_price=request.spot_price,
            strikePricesDelta=request.strikePricesDelta,
            lot_size=request.lot_size,
            multiplier=request.multiplier,
            target_expiry_days=request.target_expiry_days,
            target_datetime=request.target_datetime,
            target_interval=request.target_interval,
            backtesting_enabled=request.backtesting_enabled,
            greeks_multiply_by_lot_size=request.greeks_multiply_by_lot_size,
            greeks_multiply_by_number_of_lots=request.greeks_multiply_by_number_of_lots,
            per_trade_greeks_multiply_by_lot_size=request.per_trade_greeks_multiply_by_lot_size,
            per_trade_greeks_multiply_by_number_of_lots=request.per_trade_greeks_multiply_by_number_of_lots,
            sd_mode=request.sd_mode,
            sd_days=request.sd_days,
        )
        return result
    except Exception as e:
        return {"error": str(e)}


# OLD Bulk Endpoint (retained as-is for backward compatibility)
@router.post("/options/payoff/bulk/old")
async def calculate_bulk_payoff_old(request: BulkPayoffRequest):
    """
    Bulk backtesting endpoint using OHLC data.
    This endpoint is retained for backward compatibility.
    It fetches OHLC data using the provided symbol, start, end, and interval.
    Then for each timestamp, it extracts the close price and calculates the payoff metrics.
    Returns a list of results for each timestamp.
    """
    try:
        ohlc_data: Dict[str, Any] = await fetch_ohlc_data(request.symbol, request.start, request.end, request.interval)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch OHLC data: {str(e)}")

    bulk_results = []
    for timestamp, data in ohlc_data.items():
        try:
            spot = data.get("c")
            if spot is None:
                continue
            result = await calculate_payoff(legs=request.legs, spot_price=spot, strikePricesDelta=1, lot_size=request.lot_size, multiplier=request.multiplier)
            result.update({"Timestamp": timestamp, "Spot": spot})
            bulk_results.append(result)
        except Exception as e:
            bulk_results.append({"Timestamp": timestamp, "error": str(e)})
    return {"results": bulk_results}


# NEW Bulk Endpoint: simplified output with sorted results and leg price included
@router.post("/options/payoff/bulk")
async def calculate_bulk_payoff_new(request: BulkPayoffRequest):
    """
    Bulk backtesting endpoint using option chain data.
    This endpoint uses the expiryDate provided in the legs if available; otherwise,
    it computes the expiry date automatically from the legs by taking the minimum expiry_days.
    The determined expiry date is formatted as "dd-MMM-yyyy" and the start/end dates are
    converted from "YYYY-MM-DD" to "DD-MM-YYYY" as expected by the server.

    For each distinct timestamp (grouped by the "timestamp" field), it:
      1. Updates each option leg's premium (using the "lastPrice" from the option chain data)
         by matching the leg's strike and expiry.
      2. Computes the profit/loss for each leg using the initial premium (op_pr) provided in the request.
         For a buy (tr_type "b"), profit = (current LTP - initial premium),
         and for a sell (tr_type "s"), profit = (initial premium - current LTP).
      3. Sums up the profit/loss (multiplied by the number of lots) for all legs.
      4. Returns a simplified, sorted result containing:
         - Timestamp (formatted as "dd-MMM-yyyy")
         - Spot price (determined from the chain data)
         - Total profit/loss
         - Legs: a list with each leg's strike, type, tr_type, original price (op_pr), updated LTP, and individual profit/loss.
    """
    if not request.legs:
        raise HTTPException(status_code=400, detail="No legs provided.")

    # Use expiryDate from the legs if available, otherwise compute it from expiry_days.
    expiry_date = None
    for leg in request.legs:
        if leg.expiryDate:
            expiry_date = leg.expiryDate
            break
    if not expiry_date:
        min_expiry_days = min(leg.expiry_days for leg in request.legs)
        computed_expiry_dt = datetime.now() + timedelta(days=min_expiry_days)
        expiry_date = computed_expiry_dt.strftime("%d-%b-%Y")

    # Convert request.start and request.end from YYYY-MM-DD to DD-MM-YYYY format.
    try:
        start_dt = datetime.strptime(request.start, "%Y-%m-%d")
        from_date_range = start_dt.strftime("%d-%m-%Y")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid start date format: {str(e)}")
    try:
        end_dt = datetime.strptime(request.end, "%Y-%m-%d")
        to_date_range = end_dt.strftime("%d-%m-%Y")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid end date format: {str(e)}")

    # Build the URL using the provided symbol and determined expiry date.
    # We now use the cached function for fetching option chain data.
    try:
        option_chain_data: Dict[str, Any] = await fetch_option_chain_data(request.symbol, from_date_range, to_date_range, request.interval, expiry_date)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch option chain data: {str(e)}")

    data_rows = option_chain_data.get("records", {}).get("data", [])
    if not data_rows:
        raise HTTPException(status_code=404, detail="No option chain data found.")

    # Group rows by timestamp (date)
    grouped = {}
    for row in data_rows:
        ts = row.get("timestamp")
        if not ts:
            continue
        grouped.setdefault(ts, []).append(row)

    simplified_results = []
    for ts, rows in grouped.items():
        updated_legs = []
        total_profit_loss = 0
        # For each leg, update LTP (from chain data) and compute profit/loss.
        for leg in request.legs:
            leg_dict = leg.dict()  # original data from request, including initial op_pr
            updated_leg = {"strike": leg.strike, "type": leg.type, "tr_type": leg.tr_type, "price": leg.op_pr, "ltp": None, "profit_loss": None, "lots": leg.lots}
            # For option legs, update LTP using matching chain data.
            if leg.type in ["CE", "PE"]:
                option_branch = leg.type  # "CE" or "PE"
                for row in rows:
                    chain_strike = row.get("strikePrice")
                    chain_expiry = row.get("expiryDate")
                    if chain_strike is None or chain_expiry is None:
                        continue
                    if abs(chain_strike - leg.strike) < 1e-6 and chain_expiry == expiry_date:
                        branch_data = row.get(option_branch, {})
                        if branch_data and branch_data.get("lastPrice") is not None:
                            updated_ltp = branch_data["lastPrice"]
                            updated_leg["ltp"] = updated_ltp
                        break
            else:
                # For FUT type, use entry_price as LTP.
                updated_leg["ltp"] = leg.entry_price

            # Compute profit/loss if LTP is found.
            if updated_leg["ltp"] is None:
                updated_leg["profit_loss"] = 0
            else:
                initial = leg.op_pr if leg.op_pr is not None else 0
                if leg.tr_type == "b":
                    profit = updated_leg["ltp"] - initial
                else:
                    profit = initial - updated_leg["ltp"]
                updated_leg["profit_loss"] = round(profit, 2)
                total_profit_loss += profit * leg.lots

            updated_legs.append(updated_leg)

        # Determine spot price from underlyingValue (PE preferred, else CE) using the first row.
        underlying_value = None
        if "PE" in rows[0] and rows[0]["PE"].get("underlyingValue"):
            underlying_value = rows[0]["PE"]["underlyingValue"]
        elif "CE" in rows[0] and rows[0]["CE"].get("underlyingValue"):
            underlying_value = rows[0]["CE"]["underlyingValue"]

        simplified_results.append({"Timestamp": ts, "Spot": underlying_value, "Profit/Loss": round(total_profit_loss, 2), "legs": updated_legs})

    # Sort the results by Timestamp (parsing the "dd-MMM-yyyy" format)
    try:
        simplified_results.sort(key=lambda x: datetime.strptime(x["Timestamp"], "%d-%b-%Y"))
    except Exception as e:
        print("Error sorting results by Timestamp:", e)

    return {"results": simplified_results}