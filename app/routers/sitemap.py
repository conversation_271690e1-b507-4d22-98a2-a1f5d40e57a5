import os
from xml.dom.minidom import Element
from fastapi import APIRouter, Request, Response
from fastapi.responses import PlainTextResponse
from datetime import datetime
from typing import List, Dict
from xml.etree.ElementTree import Element, SubElement, tostring
import random
from itertools import combinations
import re

from app.routers.mutual_funds import MutualFundData, SEO_FILTER_MAPPINGS
from app.routers.screener import SEO_STOCK_FILTER_MAPPINGS, get_all_screener_data

router = APIRouter()

# Base URL for generating sitemap URLs
APP_URL = "https://theaibull.com/"


def generate_comparison_urls(all_stocks, last_updated: str):
    """
    Generator function to yield comparison URLs for sectors and industries, only 4-stock combinations.
    Converts stock symbols to lowercase in the comparison URLs.
    """
    # Group stocks by sector and industry
    sector_groups: Dict[str, List[str]] = {}
    industry_groups: Dict[str, List[str]] = {}
    for stock_slug, stock_data in all_stocks.items():
        info = stock_data.get("info", {})
        sector = info.get("sector", "Unknown")
        industry = info.get("industry", "Unknown")
        if sector != "Unknown":
            if sector not in sector_groups:
                sector_groups[sector] = []
            sector_groups[sector].append(stock_slug)
        if industry != "Unknown":
            if industry not in industry_groups:
                industry_groups[industry] = []
            industry_groups[industry].append(stock_slug)

    # Maximum URLs and stocks per sector/industry
    max_urls_per_group = 1000
    max_stocks_per_group = 100

    # Helper function to sample random 4-stock combinations
    def sample_combinations(stocks: List[str], max_count: int):
        if len(stocks) < 4:
            return
        # Cap stocks to reduce combination space
        stocks = stocks[:max_stocks_per_group]
        # For small sets, use all combinations
        if len(stocks) <= 10:
            for combo in combinations(stocks, 4):
                if max_count <= 0:
                    break
                yield list(combo)
                max_count -= 1
        else:
            # For large sets, sample randomly
            for _ in range(max_count):
                combo = random.sample(stocks, 4)
                yield combo

    # Generate URLs for sectors and industries
    for group_type, groups in [("sector", sector_groups), ("industry", industry_groups)]:
        seen_urls = set()  # Reset per group type
        for group_name, stocks in groups.items():
            if len(stocks) < 4:
                continue
            url_count = 0
            for stock_group in sample_combinations(stocks, max_urls_per_group):
                if url_count >= max_urls_per_group:
                    break
                comparison_url = f"screener/compare/{'/'.join(slug.lower() for slug in stock_group)}"
                full_url = f"{APP_URL}{comparison_url}"
                if full_url not in seen_urls:
                    seen_urls.add(full_url)
                    yield {"loc": full_url, "lastmod": last_updated}
                    url_count += 1


def generate_mutual_fund_comparison_urls(all_funds: Dict[str, Dict], last_updated: str):
    """
    Generator function to yield comparison URLs for mutual funds, only 4-fund combinations, grouped by sub_category.
    """
    # Group funds by sub_category
    sub_category_groups: Dict[str, List[str]] = {}
    for fund_slug, fund_data in all_funds.items():
        sub_category = fund_data.get("sub_category", "Unknown")
        if sub_category != "Unknown":
            if sub_category not in sub_category_groups:
                sub_category_groups[sub_category] = []
            sub_category_groups[sub_category].append(fund_slug)

    # Maximum URLs and funds per sub_category
    max_urls_per_group = 1000
    max_funds_per_group = 100

    # Helper function to sample random 4-fund combinations
    def sample_combinations(funds: List[str], max_count: int):
        if len(funds) < 4:
            return
        # Cap funds to reduce combination space
        funds = funds[:max_funds_per_group]
        # For small sets, use all combinations
        if len(funds) <= 10:
            for combo in combinations(funds, 4):
                if max_count <= 0:
                    break
                yield list(combo)
                max_count -= 1
        else:
            # For large sets, sample randomly
            seen_combinations = set()
            attempts = 0
            max_attempts = max_count * 10  # Prevent infinite loops
            while attempts < max_attempts and max_count > 0:
                combo = tuple(sorted(random.sample(funds, 4)))  # Sort to ensure consistent deduplication
                if combo not in seen_combinations:
                    seen_combinations.add(combo)
                    yield list(combo)
                    max_count -= 1
                attempts += 1

    # Generate URLs for sub_categories
    seen_urls = set()
    for sub_category, funds in sub_category_groups.items():
        if len(funds) < 4:
            continue
        url_count = 0
        for fund_group in sample_combinations(funds, max_urls_per_group):
            if url_count >= max_urls_per_group:
                break
            comparison_url = f"mutual-funds/compare/{'/'.join(slug.lower() for slug in fund_group)}"
            full_url = f"{APP_URL}{comparison_url}"
            if full_url not in seen_urls:
                seen_urls.add(full_url)
                yield {"loc": full_url, "lastmod": last_updated}
                url_count += 1


def generate_sitemap(entries: List[Dict[str, str]]):
    """
    Generate an XML sitemap from a list of entries.
    """
    urlset = Element("urlset", xmlns="http://www.sitemaps.org/schemas/sitemap/0.9")
    for entry in entries:
        url_element = SubElement(urlset, "url")
        loc = SubElement(url_element, "loc")
        loc.text = entry["loc"]

        lastmod = SubElement(url_element, "lastmod")
        lastmod.text = entry["lastmod"]

    return tostring(urlset, encoding="utf-8", method="xml")


def generate_sitemap_index(sitemaps: List[Dict[str, str]]):
    """
    Generate an XML sitemap index from a list of sitemap entries.
    """
    sitemapindex = Element("sitemapindex", xmlns="http://www.sitemaps.org/schemas/sitemap/0.9")
    for sitemap in sitemaps:
        sitemap_element = SubElement(sitemapindex, "sitemap")
        loc = SubElement(sitemap_element, "loc")
        loc.text = sitemap["loc"]
        lastmod = SubElement(sitemap_element, "lastmod")
        lastmod.text = sitemap["lastmod"]

    return tostring(sitemapindex, encoding="utf-8", method="xml")


# Sitemap index route
@router.get("/sitemap.xml", response_class=PlainTextResponse)
async def sitemap_index():
    """
    Generate the sitemap index referencing all sitemaps.
    """
    last_updated = datetime.now().strftime("%Y-%m-%d")
    sitemap_entries = [
        {"loc": f"{APP_URL}sitemap-static.xml", "lastmod": last_updated},
        {"loc": f"{APP_URL}sitemap-stocks.xml", "lastmod": last_updated},
        {"loc": f"{APP_URL}sitemap-stocks-screener.xml", "lastmod": last_updated},
        {"loc": f"{APP_URL}sitemap-stocks-filters.xml", "lastmod": last_updated},
        {"loc": f"{APP_URL}sitemap-stock-comparisons.xml", "lastmod": last_updated},
        {"loc": f"{APP_URL}sitemap-mutual-funds.xml", "lastmod": last_updated},
        {"loc": f"{APP_URL}sitemap-mutual-funds-screener.xml", "lastmod": last_updated},
        {"loc": f"{APP_URL}sitemap-mutual-funds-filters.xml", "lastmod": last_updated},
        {"loc": f"{APP_URL}sitemap-mutual-fund-comparisons.xml", "lastmod": last_updated},
        {"loc": f"{APP_URL}sitemap-options-chain.xml", "lastmod": last_updated},
    ]

    sitemap_index_xml = generate_sitemap_index(sitemap_entries)
    return Response(content=sitemap_index_xml, media_type="application/xml")


# Static sitemap route
@router.get("/sitemap-static.xml", response_class=PlainTextResponse)
async def sitemap_static_xml():
    """
    Serve the static sitemap.xml file.
    """
    file_path = os.path.join("static/sitemaps", "sitemap-static.xml")

    # Check if file exists
    if os.path.exists(file_path):
        # Read and return file content
        with open(file_path, "r") as file:
            content = file.read()
        # Insert or replace <lastmod> tag with current date
        lastmod = datetime.now().strftime("%Y-%m-%d")
        if "<lastmod>" in content:
            content = re.sub(r"<lastmod>.*?</lastmod>", f"<lastmod>{lastmod}</lastmod>", content)
        else:
            # Optionally, add <lastmod> to each <url> if not present
            content = re.sub(r"(</loc>)", r"\1\n    <lastmod>{}</lastmod>".format(lastmod), content)
        return PlainTextResponse(content, media_type="application/xml")
    else:
        # Return 404 if file not found
        return PlainTextResponse("File not found", status_code=404, media_type="text/plain")


@router.get("/sitemap-mutual-funds-screener.xml")
async def sitemap_mutual_funds_screener(request: Request):
    """
    Generate sitemap for mutual funds screener paginated URLs.
    Each page contains 100 funds.
    """
    all_funds = MutualFundData.fetch_all_funds()
    total_funds = len(all_funds)
    page_size = 100
    num_pages = (total_funds + page_size - 1) // page_size  # Ceiling division

    last_updated = datetime.now().strftime("%Y-%m-%d")
    fund_entries: List[Dict[str, str]] = []
    for page in range(1, num_pages + 1):
        fund_url = "mutual-funds" if page == 1 else f"mutual-funds/{page}"
        fund_entries.append({"loc": f"{APP_URL}{fund_url}", "lastmod": last_updated})

    sitemap_xml = generate_sitemap(fund_entries)
    return Response(content=sitemap_xml, media_type="application/xml")


@router.get("/sitemap-mutual-funds.xml")
async def sitemap_mutual_funds(request: Request):
    """
    Generate the mutual funds sitemap.
    """
    all_funds = MutualFundData.fetch_all_funds()
    fund_entries: List[Dict[str, str]] = []
    all_funds_slugs = all_funds.keys()
    for fund in all_funds_slugs:
        last_updated = datetime.now().strftime("%Y-%m-%d")
        fund_url = f"mutual-funds/{fund}"
        fund_entries.append({"loc": f"{APP_URL}{fund_url}", "lastmod": last_updated})

    sitemap_xml = generate_sitemap(fund_entries)
    return Response(content=sitemap_xml, media_type="application/xml")


@router.get("/sitemap-stocks-screener.xml")
async def sitemap_stocks_screener(request: Request):
    """
    Generate sitemap for stocks screener paginated URLs.
    Each page contains 100 stocks.
    """
    all_stocks = get_all_screener_data()
    total_stocks = len(all_stocks)
    page_size = 100
    num_pages = (total_stocks + page_size - 1) // page_size

    last_updated = datetime.now().strftime("%Y-%m-%d")
    stock_entries: List[Dict[str, str]] = []
    for page in range(1, num_pages + 1):
        stock_url = "stocks" if page == 1 else f"stocks/{page}"
        stock_entries.append({"loc": f"{APP_URL}{stock_url}", "lastmod": last_updated})

    sitemap_xml = generate_sitemap(stock_entries)
    return Response(content=sitemap_xml, media_type="application/xml")


@router.get("/sitemap-stocks.xml")
async def sitemap_stocks(request: Request):
    """
    Generate the stocks sitemap.
    """
    all_stocks = get_all_screener_data()
    all_stocks_slugs = all_stocks.keys()
    stock_entries: List[Dict[str, str]] = []
    for stock in all_stocks_slugs:
        last_updated = datetime.now().strftime("%Y-%m-%d")
        stock_url = f"stocks/{str(stock).lower()}"
        stock_entries.append({"loc": f"{APP_URL}{stock_url}", "lastmod": last_updated})

    sitemap_xml = generate_sitemap(stock_entries)
    return Response(content=sitemap_xml, media_type="application/xml")


@router.get("/sitemap-options-chain.xml")
async def sitemap_options_chain(request: Request):
    """
    Generate the options chain sitemap.
    """
    all_stocks = get_all_screener_data()
    all_stocks_slugs = all_stocks.keys()
    stock_entries: List[Dict[str, str]] = []
    for stock in all_stocks_slugs:
        last_updated = datetime.now().strftime("%Y-%m-%d")
        stock_url = f"options/chain/{stock}"
        stock_entries.append({"loc": f"{APP_URL}{stock_url}", "lastmod": last_updated})

    sitemap_xml = generate_sitemap(stock_entries)
    return Response(content=sitemap_xml, media_type="application/xml")


@router.get("/sitemap-stock-comparisons.xml")
async def sitemap_comparisons(request: Request):
    """
    Generate a sitemap index for stock comparisons, referencing only required chunked sitemaps.
    """
    last_updated = datetime.now().strftime("%Y-%m-%d")
    all_stocks = get_all_screener_data()
    
    # Count actual URLs to determine the number of chunks
    chunk_size = 5000
    total_urls = 0
    for _ in generate_comparison_urls(all_stocks, last_updated):
        total_urls += 1
    
    # Calculate the number of chunks needed
    num_chunks = (total_urls + chunk_size - 1) // chunk_size
    print(f"Actual total URLs: {total_urls}, Number of chunks: {num_chunks}")
    
    # Generate sitemap index for only the required chunks
    sitemap_entries = [
        {"loc": f"{APP_URL}sitemap-stock-comparisons-{i}.xml", "lastmod": last_updated}
        for i in range(1, num_chunks + 1)
    ]
    sitemap_index_xml = generate_sitemap_index(sitemap_entries)
    return Response(content=sitemap_index_xml, media_type="application/xml")

@router.get("/sitemap-stock-comparisons-{chunk}.xml")
async def sitemap_comparisons_chunk(request: Request, chunk: int):
    """
    Generate a chunk of the stock comparisons sitemap (5,000 URLs per chunk).
    """
    if chunk < 1:
        return PlainTextResponse("Invalid chunk number", status_code=404, media_type="text/plain")
    
    last_updated = datetime.now().strftime("%Y-%m-%d")
    all_stocks = get_all_screener_data()
    chunk_size = 5000
    start_idx = (chunk - 1) * chunk_size
    end_idx = start_idx + chunk_size
    
    comparison_entries = []
    current_idx = 0
    
    # Collect URLs for the requested chunk
    for entry in generate_comparison_urls(all_stocks, last_updated):
        if current_idx >= end_idx:
            break
        if current_idx >= start_idx:
            comparison_entries.append(entry)
        current_idx += 1
    
    if not comparison_entries:
        return PlainTextResponse("Chunk not found", status_code=404, media_type="text/plain")
    
    print(f"Generated chunk {chunk}: {len(comparison_entries)} URLs")
    sitemap_xml = generate_sitemap(comparison_entries)
    return Response(content=sitemap_xml, media_type="application/xml")


@router.get("/sitemap-mutual-fund-comparisons.xml")
async def sitemap_mutual_fund_comparisons(request: Request):
    """
    Generate a sitemap index for mutual fund comparisons, referencing only required chunked sitemaps.
    """
    last_updated = datetime.now().strftime("%Y-%m-%d")
    all_funds = MutualFundData.fetch_all_funds()

    chunk_size = 5000
    total_urls = 0
    for _ in generate_mutual_fund_comparison_urls(all_funds, last_updated):
        total_urls += 1

    num_chunks = (total_urls + chunk_size - 1) // chunk_size
    print(f"Actual total mutual fund comparison URLs: {total_urls}, Number of chunks: {num_chunks}")

    sitemap_entries = [{"loc": f"{APP_URL}sitemap-mutual-fund-comparisons-{i}.xml", "lastmod": last_updated} for i in range(1, num_chunks + 1)]
    sitemap_index_xml = generate_sitemap_index(sitemap_entries)
    return Response(content=sitemap_index_xml, media_type="application/xml")


@router.get("/sitemap-mutual-fund-comparisons-{chunk}.xml")
async def sitemap_mutual_fund_comparisons_chunk(request: Request, chunk: int):
    """
    Generate a chunk of the mutual fund comparisons sitemap (5,000 URLs per chunk).
    """
    if chunk < 1:
        return PlainTextResponse("Invalid chunk number", status_code=404, media_type="text/plain")

    last_updated = datetime.now().strftime("%Y-%m-%d")
    all_funds = MutualFundData.fetch_all_funds()
    chunk_size = 5000
    start_idx = (chunk - 1) * chunk_size
    end_idx = start_idx + chunk_size

    comparison_entries = []
    current_idx = 0

    for entry in generate_mutual_fund_comparison_urls(all_funds, last_updated):
        if current_idx >= end_idx:
            break
        if current_idx >= start_idx:
            comparison_entries.append(entry)
        current_idx += 1

    if not comparison_entries:
        return PlainTextResponse("Chunk not found", status_code=404, media_type="text/plain")

    print(f"Generated mutual fund comparison chunk {chunk}: {len(comparison_entries)} URLs")
    sitemap_xml = generate_sitemap(comparison_entries)
    return Response(content=sitemap_xml, media_type="application/xml")


@router.get("/sitemap-stocks-filters.xml")
async def sitemap_stocks_seo(request: Request):
    """
    Generate the stocks SEO URLs sitemap.
    """
    last_updated = datetime.now().strftime("%Y-%m-%d")
    seo_entries: List[Dict[str, str]] = []
    for seo_key in SEO_STOCK_FILTER_MAPPINGS.keys():
        seo_url = f"stocks/{seo_key}"
        seo_entries.append({"loc": f"{APP_URL}{seo_url}", "lastmod": last_updated})

    sitemap_xml = generate_sitemap(seo_entries)
    return Response(content=sitemap_xml, media_type="application/xml")


@router.get("/sitemap-mutual-funds-filters.xml")
async def sitemap_mutual_funds_seo(request: Request):
    """
    Generate the mutual funds SEO URLs sitemap.
    """
    last_updated = datetime.now().strftime("%Y-%m-%d")
    seo_entries: List[Dict[str, str]] = []
    for seo_key in SEO_FILTER_MAPPINGS.keys():
        seo_url = f"mutual-funds/{seo_key}"
        seo_entries.append({"loc": f"{APP_URL}{seo_url}", "lastmod": last_updated})

    sitemap_xml = generate_sitemap(seo_entries)
    return Response(content=sitemap_xml, media_type="application/xml")
