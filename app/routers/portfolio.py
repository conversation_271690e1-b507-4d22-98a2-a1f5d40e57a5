import asyncio
import json
import urllib.parse
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx
from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPEx<PERSON>, Request
from fastapi.responses import JSONResponse

from app.util.db.ai_bull_db import get_portfolio, get_portfolios
from app.util.menu import menu
from app.util.f_n_o import calculate_portfolio_metrics, process_fno_position
from app.util.stocks import get_all_stocks, get_options_data
from app.util.templates import templates
from app.util.apps import make_trading_api_call
from .auth import get_current_user_data, get_user_data

router = APIRouter()

MANTRA_APPS = "https://apps.theaibull.com"

async def _get_user_portfolios(access_token: Optional[str] = None, guestUserData: Optional[str] = None, include_data: bool = False) -> tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """Helper function to get portfolios and user data"""
    if access_token:
        portfolios: List[Dict[str, Any]] = await get_portfolios(access_token)
        user_data: Dict[str, Any] = get_current_user_data(access_token)
    else:
        portfolios = []
        user_data = {}
        if guestUserData:
            try:
                user_data = json.loads(urllib.parse.unquote(guestUserData))
            except json.JSONDecodeError:
                user_data = {}

    # Add app-based portfolios
    for app in user_data.get("apps", []):
        if app.get("category") == "Stocks":
            try:
                new_portfolio = await import_portfolio(app)
                if include_data:
                    await fetch_and_process_portfolio_data(new_portfolio, app)
                portfolios.append(new_portfolio)
            except Exception as error:
                print(f"Unexpected error importing portfolio: {error}")

    return portfolios, user_data


@router.get("")
async def get_portfolios_main(
    request: Request,
    access_token: str = Cookie(None),
    guestUserData: str = Cookie(None),
):
    # Return basic template without fetching full portfolio data
    # We'll load portfolio data directly from the frontend API
    return templates.TemplateResponse(
        "portfolio/main.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "portfolio_uid": None,
        },
    )


# API Route - Gets portfolio data including holdings and positions
@router.get("/api/portfolios")
async def get_portfolio_data(
    access_token: str = Cookie(None),
    guestUserData: str = Cookie(None),
):
    stocks = get_all_stocks()

    portfolios, _ = await _get_user_portfolios(access_token, guestUserData, include_data=True)

    total_value = 0
    stock_pl = 0
    fno_pl = 0
    stock_total_cost = 0
    fno_total_cost = 0
    total_holdings = 0
    combined_holdings = {}
    all_positions = []
    combined_stock_pl_percentage = {}
    combined_fno_pl_percentage = {}

    for portfolio in portfolios:
        await calculate_portfolio_metrics(portfolio, stocks)
        total_value += portfolio["total_value"]
        stock_pl += portfolio["stock_pl"]
        fno_pl += portfolio["fno_pl"]
        stock_total_cost += sum(h["invested_amount"] for h in portfolio.get("holdings", [])) + sum(p["invested_amount"] for p in portfolio.get("positions", []) if p["instrument"] == "equity")
        fno_total_cost += sum(abs(p["invested_amount"]) for p in portfolio.get("positions", []) if p["instrument"] != "equity")
        total_holdings += portfolio["holdings_count"] + portfolio["positions_count"]

        # Aggregate holdings and positions
        for holding in portfolio.get("holdings", []):
            symbol = holding["symbol"]

            if symbol in combined_holdings:
                # Combine with existing holding
                existing = combined_holdings[symbol]

                # Update quantities and values
                total_old_value = existing["quantity"] * existing["avg_price"]
                total_new_value = holding["quantity"] * holding["avg_price"]
                total_quantity = existing["quantity"] + holding["quantity"]

                # Calculate new average price
                new_avg_price = (total_old_value + total_new_value) / total_quantity

                # Update combined holding
                existing.update(
                    {
                        "quantity": total_quantity,
                        "avg_price": new_avg_price,
                        "market_value": (existing.get("market_value") or 0) + (holding.get("market_value") or 0),
                        "pl": (existing.get("pl") or 0) + (holding.get("pl") or 0),
                        "invested_amount": (existing.get("invested_amount") or 0) + (holding.get("invested_amount") or 0),
                    }
                )

                # Calculate new P/L percentage
                if existing["market_value"]:
                    total_cost = existing["invested_amount"]  # Use total invested amount instead of recalculated avg price
                    existing["pl_percentage"] = (existing["pl"] / total_cost * 100) if total_cost > 0 else 0

                # Add order with portfolio reference
                for order in holding.get("orders", []):
                    order["portfolio_name"] = portfolio["name"]
                    existing["orders"].append(order)
            else:
                # Create new combined holding
                combined_holding = holding.copy()
                combined_holding["orders"] = holding.get("orders", [])
                for order in combined_holding["orders"]:
                    order["portfolio_name"] = portfolio["name"]
                combined_holdings[symbol] = combined_holding

        for position in portfolio.get("positions", []):
            position["portfolio_name"] = portfolio["name"]  # Add portfolio name for reference
            all_positions.append(position)

        combined_stock_pl_percentage[portfolio["uid"]] = portfolio["stock_pl_percentage"]
        combined_fno_pl_percentage[portfolio["uid"]] = portfolio["fno_pl_percentage"]

    stock_pl_percentage = (stock_pl / stock_total_cost * 100) if stock_total_cost > 0 else 0
    fno_pl_percentage = (fno_pl / fno_total_cost * 100) if fno_total_cost > 0 else 0
    all_holdings = list(combined_holdings.values())

    return JSONResponse(
        {
            "total_value": total_value,
            "stock_pl": stock_pl,
            "stock_pl_percentage": stock_pl_percentage,
            "fno_pl": fno_pl,
            "fno_pl_percentage": fno_pl_percentage,
            "total_holdings": total_holdings,
            "holdings": all_holdings,
            "positions": all_positions,
            "combined_stock_pl_percentage": combined_stock_pl_percentage,
            "combined_fno_pl_percentage": combined_fno_pl_percentage,
            "portfolios": portfolios,
        }
    )


@router.get("/{portfolio_uid}")
async def get_portfolio_detail(
    portfolio_uid: str,
    request: Request,
    access_token: str = Cookie(None),
    guestUserData: str = Cookie(None),
):
    stocks = get_all_stocks()

    portfolios, _ = await _get_user_portfolios(access_token, guestUserData)

    # Get single portfolio using new function
    portfolio = None
    if portfolio_uid.startswith("app-"):
        app_uuid = portfolio_uid.replace("app-", "", 1)
        portfolio = next((item for item in portfolios if item.get("app_id") == app_uuid), None)

    if not portfolio:
        portfolio = await get_portfolio(portfolio_uid, access_token)

    if not portfolio:
        raise HTTPException(status_code=404, detail="Portfolio not found")

    today_date = datetime.now().strftime("%Y-%m-%d")

    return templates.TemplateResponse(
        "portfolio/detail.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "portfolio": portfolio,
            "portfolios": portfolios,
            "portfolio_uid": portfolio_uid,
            "today_date": today_date,
            "stocks": stocks
        },
    )


# API route for specific portfolio data
@router.get("/api/portfolios/{portfolio_uid}")
async def get_portfolio_specific_data(
    portfolio_uid: str,
    access_token: str = Cookie(None),
    guestUserData: str = Cookie(None),
):
    stocks = get_all_stocks()

    portfolios, _ = await _get_user_portfolios(access_token, guestUserData, include_data=True)

    portfolio = None
    if portfolio_uid.startswith("app-"):
        app_uuid = portfolio_uid.replace("app-", "", 1)
        portfolio = next((item for item in portfolios if item.get("app_id") == app_uuid), None)

    if not portfolio:
        portfolio = await get_portfolio(portfolio_uid, access_token)

    if not portfolio:
        raise HTTPException(status_code=404, detail="Portfolio not found")

    await calculate_portfolio_metrics(portfolio, stocks)

    return JSONResponse(
        {
            "total_value": portfolio.get("total_value"),
            "stock_pl": portfolio.get("stock_pl"),
            "stock_pl_percentage": portfolio.get("stock_pl_percentage"),
            "fno_pl": portfolio.get("fno_pl"),
            "fno_pl_percentage": portfolio.get("fno_pl_percentage"),
            "total_holdings": portfolio.get("holdings_count") + portfolio.get("positions_count"),
            "portfolio": portfolio,
            "holdings": portfolio.get("holdings", []),
            "positions": portfolio.get("positions", []),
        }
    )


async def import_portfolio(app: Dict[str, Any]) -> Dict[str, Any]:
    app_name = app.get("name", "").lower().replace(" ", "-")
    ASSET_BASE_PATH = f"{MANTRA_APPS}/static/images/apps/{app_name}.svg"

    name = app.get("name", "")
    secondary_name = app.get("fields", {}).get("profile", {}).get("name") or app.get("fields", {}).get("name")
    if secondary_name:
        name = f"{name} - {secondary_name}"

    return {
        "uid": f"app-{app.get('uuid', '')}",
        "app_id": app.get("uuid", ""),
        "type": app.get("name", ""),
        "name": name,
        "svg": ASSET_BASE_PATH,
        "holdings": [],
        "positions": [],
    }


async def fetch_and_process_portfolio_data(portfolio: Dict[str, Any], app: Dict[str, Any]) -> None:
    try:
        holdings_response = await fetch_portfolio_data(app, "get_holdings")
        await asyncio.sleep(0.5)  # Pause to comply with AngelOne API rate limits
        positions_response = await fetch_portfolio_data(app, "get_positions")

        if positions_response.get("result", {}).get("status") == "Failure" or holdings_response.get("result", {}).get("status") == "Failure":
            portfolio["status"] = "Failure"
            portfolio["error"] = positions_response.get("result", {}).get("error") or holdings_response.get("result", {}).get("error")
            return

        process_holdings(portfolio, holdings_response)
        process_positions(portfolio, positions_response)

    except Exception as fetch_error:
        print(f"Error fetching portfolio data: {fetch_error}")
        portfolio["status"] = "Failure"
        portfolio["error"] = str(fetch_error)


async def fetch_portfolio_data(app: Dict[str, Any], endpoint: str) -> Dict[str, Any]:

    request_data: Dict[str, Any] = {"params": {}, "auth": app.get("fields", {})}

    try:
        # Use the make_trading_api_call function
        response_data = await make_trading_api_call(app, endpoint, request_data)
        return response_data

    except Exception as error:
        print(f"Unexpected error fetching portfolio data: {error}")
        return {"result": {endpoint: [], "status": "Failure"}}


def process_holdings(existing_portfolio: Dict[str, Any], holdings_response: Dict[str, Any]) -> None:
    """
    Process holdings with robust error handling for empty or None values.
    Args:
        existing_portfolio (Dict[str, Any]): The existing portfolio dictionary
        holdings_response (Dict[str, Any]): The response containing holdings information
    """
    # Ensure holdings is initialized as an empty list
    existing_portfolio["holdings"] = []

    holdings = holdings_response["result"].get("holdings", [])
    # Return early if no holdings or response is invalid
    if not holdings_response or not holdings:
        return

    for holding in holdings:
        # Validate and sanitize input data
        avg_price = holding.get("avg_price") or holding.get("average_price") or holding.get("avgCostPrice")
        symbol = holding.get("symbol") or holding.get("trading_symbol") or holding.get("tradingSymbol") or holding.get("tradingsymbol")
        quantity = holding.get("quantity") or holding.get("totalQty")

        # Skip if critical fields are missing or invalid
        if not symbol or not quantity or avg_price is None:
            continue

        # Calculate invested amount for holdings
        invested_amount = quantity * avg_price

        # Create holding order
        holding_order: Dict[str, Any] = {
            "uid": generate_uuid(),
            "quantity": quantity,
            "price": avg_price,
            "date": holding.get("date") or datetime.now().date().isoformat(),
            "type": "buy",
        }

        # Add new holding
        new_holding: Dict[str, Any] = {
            "uid": generate_uuid(),
            "symbol": symbol,
            "quantity": quantity,
            "avg_price": avg_price,
            "invested_amount": invested_amount,  # Set invested amount
            "orders": [holding_order],
        }
        existing_portfolio["holdings"].append(new_holding)


def process_positions(existing_portfolio: Dict[str, Any], positions_response: Dict[str, Any]) -> None:
    """
    Process positions with robust error handling for empty or None values.
    Args:
        existing_portfolio (Dict[str, Any]): The existing portfolio dictionary
        positions_response (Dict[str, Any]): The response containing positions information
    """
    # Ensure positions is initialized as an empty list
    existing_portfolio["positions"] = []

    positions = positions_response["result"].get("positions", [])

    if isinstance(positions, dict):
        new_positions = []
        new_positions.extend(positions.get("net", []))
        new_positions.extend(positions.get("day", []))
        positions = new_positions
    elif not isinstance(positions, list):
        positions = []

    # Return early if no positions or response is invalid
    if not positions_response or not positions:
        return

    for position in positions:
        # Validate and sanitize input data
        entry_price = position.get("entry_price") or position.get("buy_price") or position.get("costPrice", 0)
        symbol = position.get("symbol") or position.get("trading_symbol") or position.get("tradingSymbol", "")
        quantity = position.get("quantity") or position.get("buyQty", 0)
        instrument = position.get("instrument", "")
        instrument_token = position.get("instrument_token") or position.get("exchangeSegment", "") or position.get("exchange", "")

        # Skip if critical fields are missing or invalid
        if not symbol or not quantity or entry_price is None:
            continue

        # Initialize invested_amount
        invested_amount = 0

        # Process equity positions
        if "NSE_EQ" in instrument_token:
            # For equities, calculate invested amount as entry_price * quantity
            invested_amount = entry_price * quantity

            position_order: dict[str, Any] = {
                "uid": generate_uuid(),
                "quantity": quantity,
                "price": entry_price,
                "date": position.get("entry_date") or datetime.now().date().isoformat(),
                "type": "buy",
            }

            new_position: dict[str, Any] = {
                "uid": generate_uuid(),
                "symbol": symbol,
                "quantity": quantity,
                "avg_price": entry_price,
                "entry_price": entry_price,
                "invested_amount": invested_amount,  # Set invested amount for equities
                "orders": [position_order],
                "instrument": "equity",
            }
            existing_portfolio["positions"].append(new_position)

        # Process derivative positions (futures and options)
        else:
            unified_position = process_fno_position(position)
            if unified_position:
                existing_portfolio["positions"].append(unified_position)
                continue

            # Validate derivative-specific fields
            position_type = "FUT" if instrument != "option" else position.get("type", "FUT")
            strike = position.get("strike") if instrument == "option" else None
            expiry = position.get("expiry")
            side = position.get("side")
            lot_size = position.get("lot_size", 1)  # Default to 1 if not provided

            if not side or not expiry or not strike is None:
                continue

            # Calculate premium for options
            premium_received = None
            if instrument == "option":
                if side == "sell":
                    premium_received = entry_price * quantity * lot_size
                elif side == "buy":
                    premium_received = -entry_price * quantity * lot_size

            new_position = {
                "uid": generate_uuid(),
                "symbol": symbol,
                "instrument": instrument,
                "type": position_type,
                "strike": strike,
                "expiry": expiry,
                "quantity": quantity,
                "entry_price": entry_price,
                "entry_date": position.get("entry_date"),
                "side": side,
                "lot_size": lot_size,
                "premium_received": premium_received,
            }
            existing_portfolio["positions"].append(new_position)


def generate_uuid():
    """Generate a unique identifier."""
    return str(uuid.uuid4())
