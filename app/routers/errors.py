import logging
import traceback
import uuid
from datetime import datetime

from fastapi import FastAP<PERSON>, Request
from fastapi.responses import HTMLResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.util.menu import menu
from app.util.templates import templates
from app.routers.auth import get_user_data

# Configure logger for error handling
logger = logging.getLogger(__name__)


def generate_error_id() -> str:
    """Generate a unique error ID for tracking purposes."""
    return str(uuid.uuid4())[:8].upper()


def get_timestamp() -> str:
    """Get current timestamp in a readable format."""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")


async def not_found_handler(request: Request, exc: StarletteHTTPException) -> HTMLResponse:
    """
    Custom 404 error handler.

    Args:
        request: The FastAPI request object
        exc: The StarletteHTTPException that was raised

    Returns:
        HTMLResponse: Custom 404 error page
    """
    try:
        # Get user data from cookies if available
        access_token = request.cookies.get("access_token")
        user_data = get_user_data(access_token or "")

        # Build context for the error page
        context = {
            "request": request,
            **menu,
            **user_data,
            "error_code": 404,
            "error_message": "Page Not Found",
            "requested_url": str(request.url),
            "timestamp": get_timestamp(),
        }

        # Log the 404 error for monitoring
        logger.warning(f"404 Error: {request.method} {request.url} - " f"User-Agent: {request.headers.get('user-agent', 'Unknown')} - " f"Referer: {request.headers.get('referer', 'None')}")

        return templates.TemplateResponse("errors/404.html", context, status_code=404)

    except Exception as e:
        # Fallback to basic 404 response if template rendering fails
        logger.error(f"Error rendering 404 page: {str(e)}")
        return HTMLResponse(content="<h1>404 - Page Not Found</h1><p>The requested page could not be found.</p>", status_code=404)


async def internal_server_error_handler(request: Request, exc: Exception) -> HTMLResponse:
    """
    Custom 500 error handler.

    Args:
        request: The FastAPI request object
        exc: The exception that was raised

    Returns:
        HTMLResponse: Custom 500 error page
    """
    error_id = generate_error_id()
    timestamp = get_timestamp()

    try:
        # Log the full error details for debugging
        logger.error(
            f"500 Error [{error_id}]: {request.method} {request.url} - "
            f"Exception: {type(exc).__name__}: {str(exc)} - "
            f"User-Agent: {request.headers.get('user-agent', 'Unknown')} - "
            f"Traceback: {traceback.format_exc()}"
        )

        # Get user data from cookies if available
        access_token = request.cookies.get("access_token")
        user_data = get_user_data(access_token or "")

        # Build context for the error page
        context = {
            "request": request,
            **menu,
            **user_data,
            "error_code": 500,
            "error_message": "Internal Server Error",
            "error_id": error_id,
            "timestamp": timestamp,
            "requested_url": str(request.url),
        }

        return templates.TemplateResponse("errors/500.html", context, status_code=500)

    except Exception as template_error:
        # Fallback to basic 500 response if template rendering fails
        logger.error(f"Error rendering 500 page [{error_id}]: {str(template_error)} - " f"Original error: {str(exc)}")
        return HTMLResponse(
            content=f"""
            <h1>500 - Internal Server Error</h1>
            <p>We're experiencing technical difficulties. Please try again later.</p>
            <p>Error ID: {error_id}</p>
            <p>Time: {timestamp}</p>
            """,
            status_code=500,
        )


async def http_exception_handler(request: Request, exc: StarletteHTTPException) -> HTMLResponse:
    """
    Generic HTTP exception handler for other status codes.

    Args:
        request: The FastAPI request object
        exc: The StarletteHTTPException that was raised

    Returns:
        HTMLResponse: Appropriate error response
    """
    status_code = exc.status_code

    # Handle specific status codes with custom pages
    if status_code == 404:
        return await not_found_handler(request, exc)
    elif status_code >= 500:
        return await internal_server_error_handler(request, exc)

    # For other HTTP errors, provide a generic response
    try:
        access_token = request.cookies.get("access_token")
        user_data = get_user_data(access_token or "")

        context = {
            "request": request,
            **menu,
            **user_data,
            "error_code": status_code,
            "error_message": exc.detail or "An error occurred",
            "timestamp": get_timestamp(),
        }

        # Log the error
        logger.warning(f"{status_code} Error: {request.method} {request.url} - " f"Detail: {exc.detail} - " f"User-Agent: {request.headers.get('user-agent', 'Unknown')}")

        # Try to use 404 template for client errors, 500 template for server errors
        template_name = "errors/404.html" if 400 <= status_code < 500 else "errors/500.html"

        return templates.TemplateResponse(template_name, context, status_code=status_code)

    except Exception as e:
        logger.error(f"Error rendering {status_code} page: {str(e)}")
        return HTMLResponse(content=f"<h1>{status_code} - {exc.detail or 'Error'}</h1>", status_code=status_code)


# Exception handler registration functions
def register_error_handlers(app: FastAPI) -> None:
    """
    Register all custom error handlers with the FastAPI app.

    Args:
        app: The FastAPI application instance
    """
    # Handle all HTTP exceptions (including 404, 500, etc.)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)

    # Handle general exceptions that aren't HTTP exceptions
    app.add_exception_handler(Exception, internal_server_error_handler)

    logger.info("Custom error handlers registered successfully")
