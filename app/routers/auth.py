import json
import random
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, Optional
from jose import jwt

import httpx
from fastapi import (
    APIRouter,
    BackgroundTasks,
    <PERSON>ie,
    HTTPException,
    Request,
    Response,
    status,
)

from app.util.settings import app_settings


def get_current_user_data(access_token: str) -> dict:
    """
    Fetch the user data from the Mantra AI server using the access_token.
    Raise HTTPException if the token is invalid or the request fails.
    """
    if not access_token:
        return {}

    try:
        # Define the Mantra AI Server profile endpoint and Mantra AI App
        MANTRA_AI_SERVER = app_settings.MANTRA_AI_SERVER
        MANTRA_AI_APP = app_settings.MANTRA_AI_APP
        profile_endpoint = f"{MANTRA_AI_SERVER}/profile/"

        # Send a request to the Mantra AI server to fetch user profile data
        headers = {
            "Authorization": f"Bearer {access_token}",
            "X-Mantra-App": MANTRA_AI_APP,
        }
        response = httpx.get(profile_endpoint, headers=headers, follow_redirects=True)

        # Raise HTTPException if the request was not successful
        if response.status_code != 200:
            print(response.json())
            raise HTTPException(
                status_code=response.status_code,
                detail=response.json().get("detail", "Failed to fetch user data"),
            )

        # Return the user data
        user_data = response.json()
        return user_data

    except Exception:
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=401, detail="Invalid access token")


def get_current_user_id(access_token: Optional[str] = Cookie(None)) -> str:
    """
    Decode the JWT token from the access_token cookie and return the user_id.
    Raise HTTPException if the token is invalid.
    """
    user_data = get_current_user_data(access_token)
    user_id = user_data.get("_id")
    if not user_id:
        raise HTTPException(status_code=404, detail="User ID not found in user data")
    return user_id


def get_safe_current_user_data(
    access_token: Optional[str] = Cookie(None),
) -> Optional[dict]:
    """
    Fetch user data safely, returning None if any exception occurs.
    """
    try:
        return get_current_user_data(access_token)
    except Exception:
        return None


async def check_current_user(access_token: str = Cookie(None)):
    if not access_token:
        raise HTTPException(status_code=status.HTTP_303_SEE_OTHER, headers={"Location": "/"})
    user_data = get_user_data(access_token)

    # Check if user_data is None or empty
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_303_SEE_OTHER,
            headers={"Location": "/"},
            detail="User not found",
        )

    return user_data

def decode_jwt_without_verification(token: str) -> Dict[str, Any]:
    try:
        decoded_token = jwt.decode(token, "", options={"verify_signature": False})
        return decoded_token
    except jwt.JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")


def get_user_data(access_token: str) -> Dict[str, Any]:
    try:
        if not access_token:
            return {}

        # Decode the JWT token to get the subject (email or phone)
        decoded_token = decode_jwt_without_verification(access_token)
        subject = decoded_token.get("sub")

        if not subject:
            raise HTTPException(status_code=400, detail="Token subject is missing")

        user_data = {}
        current_user_data = get_safe_current_user_data(access_token=access_token)
        if current_user_data:
            user_data["User_Data"] = current_user_data

            # Check if the subject matches the email or phone in user_data
            email = current_user_data.get("email")
            phone = current_user_data.get("phone")

            if email == subject or phone == subject:
                user_data["User_Data"]["type"] = "primary"
            else:
                user_data["User_Data"]["type"] = "secondary"
                # Find the correct secondary user data
                users = current_user_data.get("users", [])
                for user in users:
                    if user.get("email") == subject or user.get("phone") == subject:
                        user_data["User_Data"]["secondary_user"] = user
                        break

        return user_data

    except Exception as e:
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))
