import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

from fastapi import HTTPException

from app.routers.opstrat import BulkPayoffRequest, OptionLeg, fetch_option_chain_data
import copy


async def calculate_bulk_payoff_with_rebalancing(strategy: dict, request: BulkPayoffRequest) -> Dict[str, List[Dict[str, Any]]]:
    """
    Main method to calculate the bulk payoff with rebalancing logic.
    """
    if not request.legs:
        raise HTTPException(status_code=400, detail="No legs provided.")

    # Fetch and group option chain data
    # option_chain_data = await fetch_option_chain_data_for_request(request)
    # grouped = group_option_data_by_timestamp(option_chain_data)

    # Initialize traded_legs with the request.legs at the start
    traded_legs = {leg.id: copy.deepcopy(leg.dict()) for leg in request.legs}

    # Fetch and group option chain data
    expiry_date = next((leg.expiryDate for leg in request.legs if leg.expiryDate), None)
    if not expiry_date:
        min_expiry_days = min(leg.expiry_days for leg in request.legs)
        computed_expiry_dt = datetime.strptime(request.start, "%Y-%m-%d") + timedelta(days=min_expiry_days)
        expiry_date = computed_expiry_dt.strftime("%d-%b-%Y")
    start_dt = datetime.strptime(request.start, "%Y-%m-%d").strftime("%d-%m-%Y")
    end_dt = datetime.strptime(request.end, "%Y-%m-%d").strftime("%d-%m-%Y")
    option_chain_data = await fetch_option_chain_data(request.symbol, start_dt, end_dt, request.interval, expiry_date)
    data_rows = option_chain_data.get("records", {}).get("data", [])
    if not data_rows:
        raise HTTPException(status_code=404, detail="No option chain data found.")

    grouped = group_option_data_by_timestamp(data_rows)

    active_legs = {leg.id: copy.deepcopy(leg.dict()) for leg in request.legs}
    realized_pl = 0
    simplified_results = []
    strategy_closed = False

    # Process each timestamp in the grouped data
    for ts, rows in grouped.items():
        if strategy_closed:
            # If strategy is closed, skip further processing for this timestamp
            underlying_value = get_underlying_value_for_timestamp(rows)
            simplified_results.append(
                {"Timestamp": ts, "Spot": underlying_value, "Profit/Loss": round(realized_pl, 2), "legs": [], "exit_condition": "closed", "rebalancing_actions": {}, "realized_pl_this_ts": 0}
            )
            continue

        # Update leg values based on the latest option chain data
        update_leg_values(active_legs, rows)
        realized_pl = 0
        total_pl = compute_total_pl(active_legs, realized_pl)

        # Step 1: Check for exit conditions (target profit or stop loss)
        exit_condition_result = check_exit_conditions(total_pl, active_legs, strategy)

        if exit_condition_result:
            # If exit condition is met, square off positions and update the result
            updated_legs, pl_realized_this_ts = exit_condition_result["updated_legs"], exit_condition_result["pl_realized_this_ts"]
            realized_pl += pl_realized_this_ts
            strategy_closed = True
            underlying_value = get_underlying_value_for_timestamp(rows)
            simplified_results.append(
                {
                    "Timestamp": ts,
                    "Spot": underlying_value,
                    "Profit/Loss": round(total_pl, 2),
                    "legs": updated_legs,
                    "exit_condition": exit_condition_result["exit_condition"],
                    "rebalancing_actions": {},
                    "realized_pl_this_ts": round(pl_realized_this_ts, 2),
                }
            )
            continue  # Skip rebalancing if exit condition is met

        # Step 2: Check for rebalancing if exit condition is not met
        actions = is_rebalancing_required(strategy, traded_legs, active_legs)
        if actions:
            print(f"Rebalancing triggered at {ts} with actions: {actions}")
            # Perform rebalancing actions
            traded_legs, active_legs, realized_pl = await rebalance(actions, traded_legs, active_legs, rows)

            # Step 3: Compute total P&L for this timestamp after rebalancing (if needed)
            total_pl = compute_total_pl(active_legs, realized_pl)

        updated_legs = [
            {
                "strike": leg["strike"],
                "type": leg["type"],
                "tr_type": leg["tr_type"],
                "price": leg["op_pr"],
                "ltp": leg["ltp"],
                "profit_loss": round((leg["ltp"] - leg["op_pr"]) if leg["tr_type"] == "b" else (leg["op_pr"] - leg["ltp"]), 2) if leg["ltp"] is not None else None,
                "lots": leg["lots"],
            }
            for leg in active_legs.values()
            if leg["lots"] > 0
        ]
        underlying_value = get_underlying_value_for_timestamp(rows)
        simplified_results.append(
            {
                "Timestamp": ts,
                "Spot": underlying_value,
                "Profit/Loss": round(total_pl, 2),
                "legs": updated_legs,
                "exit_condition": None,
                "rebalancing_actions": {"new_legs": updated_legs, "rebalance_pl": round(realized_pl, 2)} if actions else {},
                "realized_pl_this_ts": round(realized_pl, 2),
            }
        )

    return {"results": simplified_results}


# Operator mapping for condition evaluation
OPERATOR_MAP = {
    "gt": lambda x, y: x > y,
    "gte": lambda x, y: x >= y,
    "lt": lambda x, y: x < y,
    "lte": lambda x, y: x <= y,
    "eq": lambda x, y: abs(x - y) < 1e-6,
}


def evaluate_expression(expression: str, context: Dict[str, Dict[str, float]], current_context: Dict[str, Dict[str, float]] = None) -> float:
    """
    Evaluates a condition expression, which can be a direct value or a leg reference with an optional multiplier.

    Args:
        expression (str): Expression like "100", "{leg1.premium}", "{leg1.premium} * 2", or "1.25 * {leg1.delta}"
        context (dict): Mapping of leg identifiers to their field values

    Returns:
        float: Evaluated value

    Raises:
        ValueError: If the expression format is invalid
    """
    try:
        return float(expression)
    except ValueError:
        pass

    # Define regex patterns and corresponding evaluation functions for parsing and evaluating expressions
    # Each tuple contains:
    # - A regex pattern to match a specific expression format
    # - A lambda function to compute the result based on captured groups from the regex match
    # The patterns handle direct leg references, multiplication (before or after), and division involving leg fields
    patterns = [
        # Pattern 1: Direct leg reference
        # - Purpose: Matches expressions that directly reference a leg's field (e.g., "{leg1.premium}")
        (r"^\s*\{\s*leg(\d+)\.(premium|spot)\s*\}\s*$", lambda m: context[f"leg{m[0]}"][m[1]]),
        # Pattern 2: Multiplier before leg reference
        # - Purpose: Matches expressions with a number multiplied by a leg's field (e.g., "1.25 * {leg1.premium}")
        (r"^\s*(\d+\.?\d*)\s*\*\s*\{\s*leg(\d+)\.(premium|spot)\s*\}\s*$", lambda m: float(m[0]) * context[f"leg{m[1]}"][m[2]]),
        # Pattern 3: Multiplier after leg reference
        # - Purpose: Matches expressions with a leg's field multiplied by a number (e.g., "{leg1.premium} * 2")
        (r"^\s*\{\s*leg(\d+)\.(premium|spot)\s*\}\s*\*\s*(\d+\.?\d*)\s*$", lambda m: context[f"leg{m[0]}"][m[1]] * float(m[2])),
        # Pattern 4: Division by a number
        # - Purpose: Matches expressions where a leg's field is divided by a number (e.g., "{leg1.premium} / 2")
        (r"^\s*\{\s*leg(\d+)\.(premium|spot)\s*\}\s*/\s*(\d+\.?\d*)\s*$", lambda m: context[f"leg{m[0]}"][m[1]] / float(m[2])),
        # Pattern 5: leg property multiplied by a number, and then multiplied by another number
        # -Purpose: Matches expressions like "{leg1.premium * 3} * 3.55"
        (r"^\s*\{\s*leg(\d+)\.(premium|spot)\s*\*\s*(\d+\.?\d*)\s*\}\s*\*\s*(\d+\.?\d*)\s*$", lambda m: context[f"leg{m[0]}"][m[1]] * float(m[2]) * float(m[3])),
        # Pattern 6: Matches {spot} with spaces around it
        # - Purpose: Matches expressions like "{ spot }"
        (r"^\s*\{\s*spot\s*\}\s*$", lambda m: current_context["spot"]) if current_context else 0,
    ]

    for pattern, func in patterns:
        match = re.match(pattern, expression.strip())
        if match:
            return func(match.groups())

    raise ValueError(f"Invalid expression: {expression}")


async def compute_target_strike(rows: List[Dict], option_type: str, strike_conf: Dict[str, str], underlying_value: float, context: Dict[str, Dict[str, float]]) -> float:
    """
    Computes the target strike price for a 'buy' or 'sell' action based on the strike condition.

    Args:
        rows (list): Option chain data for the current timestamp
        option_type (str): "CE" for call, "PE" for put
        strike_conf (dict): Contains "condition" ("is", "premium", "delta"), "criterion", and "value"
        underlying_value (float): Current spot price
        context (dict): Leg context for evaluating merge fields

    Returns:
        float: Computed target strike price

    Raises:
        HTTPException: If data is insufficient or condition is invalid
    """
    condition = strike_conf.get("condition", "is")
    value = strike_conf["value"]

    if condition == "is":
        strikes = sorted(set(row["strikePrice"] for row in rows if "strikePrice" in row))
        if not strikes:
            raise HTTPException(status_code=404, detail="No strikes available.")
        atm_strike = min(strikes, key=lambda x: abs(x - underlying_value))
        idx = strikes.index(atm_strike)
        if value == "ATM":
            offset = 0
        else:
            # Extract numeric offset from ITM/OTM values (e.g., "ITM1" -> 1)
            offset = int("".join(filter(str.isdigit, value))) if value != "ATM" else 0
        is_call = option_type.lower() == "ce"
        if value.startswith("OTM"):
            target_idx = idx + offset if is_call else idx - offset
        elif value.startswith("ITM"):
            target_idx = idx - offset if is_call else idx + offset
        else:
            target_idx = idx
        target_idx = max(0, min(target_idx, len(strikes) - 1))
        return strikes[target_idx]

    elif condition in ["premium", "delta"]:
        # Evaluate value if it's a merge field
        try:
            target_value = evaluate_expression(value, context)
        except ValueError:
            target_value = float(value)

        branch_lookup = "CE" if option_type.lower() == "ce" else "PE"
        valid_records = []
        for row in rows:
            sp = row.get("strikePrice")
            if sp is None:
                continue
            branch_data = row.get(branch_lookup, {})
            if not branch_data:
                continue
            metric = branch_data.get("lastPrice") if condition == "premium" else branch_data.get("delta")
            if metric is not None:
                valid_records.append((float(sp), metric))

        if not valid_records:
            raise HTTPException(status_code=404, detail=f"No valid {condition} data for strike computation.")

        # Updated to handle frontend criteria: "closest", "gte", "lte"
        criterion = strike_conf.get("criterion", "closest")
        if criterion == "closest":
            target_strike, _ = min(valid_records, key=lambda x: abs(x[1] - target_value))
        elif criterion == "gte":
            candidates = [rec for rec in valid_records if rec[1] >= target_value]
            if not candidates:
                raise HTTPException(status_code=404, detail="No strike with metric >= target value.")
            target_strike, _ = min(candidates, key=lambda x: x[1] - target_value)
        elif criterion == "lte":
            candidates = [rec for rec in valid_records if rec[1] <= target_value]
            if not candidates:
                raise HTTPException(status_code=404, detail="No strike with metric <= target value.")
            target_strike, _ = max(candidates, key=lambda x: target_value - x[1])
        else:
            raise HTTPException(status_code=400, detail="Invalid criterion.")
        return target_strike

    raise HTTPException(status_code=400, detail="Invalid strike condition.")


def update_leg_values(legs: Dict[str, Dict], rows: List[Dict]) -> None:
    """
    Updates the LTP, spot for each active leg based on current option chain data.

    Args:
        legs (dict): Mapping of leg IDs to OptionLeg objects
        rows (list): Option chain data for the current timestamp
    """
    for leg_id, leg in legs.items():
        if leg["type"] in ["CE", "PE"]:
            option_branch = leg["type"]
            for row in rows:
                chain_strike = row.get("strikePrice")
                chain_expiry = row.get("expiryDate")
                if chain_strike is None or chain_expiry is None:
                    continue
                if abs(chain_strike - leg["strike"]) < 1e-6 and chain_expiry == leg["expiryDate"]:
                    branch_data = row.get(option_branch, {})
                    if branch_data:
                        leg["ltp"] = branch_data.get("lastPrice", leg["op_pr"])  # Get lastPrice for the strike at given ts, else fallback to entry price
                        leg["iv"] = branch_data.get("impliedVolatility", 0)
                        leg["spot"] = branch_data.get("underlyingValue", 0)  # Get underlying value for the strike at given ts
                        leg["strike"] = branch_data.get("strikePrice", 0)  # Get strike price for the strike at given ts
                    break
        else:
            # For FUT type, use entry_price as LTP
            leg["ltp"] = leg["entry_price"]


def build_initial_context(traded_legs: Dict[str, Dict]) -> Dict[str, Dict[str, float]]:
    """
    Builds a context dictionary using the traded_legs, which are updated after rebalancing.

    Args:
        traded_legs (Dict[str, Dict]): The updated legs after rebalancing, stored by leg ID.

    Returns:
        Dict[str, Dict[str, float]]: Context with leg field values based on updated traded_legs.
    """
    context = {}
    for i, leg_id in enumerate(traded_legs.keys(), 1):
        leg = traded_legs[leg_id]
        context[f"leg{i}"] = {
            "premium": leg["op_pr"],  # Initial entry price (e.g., option premium)
            # Add other fields like delta, theta, iv if available and needed
        }
    return context


def build_current_context(active_legs: Dict[str, Dict]) -> Dict[str, Dict[str, float]]:
    """
    Builds a context dictionary using the current values from active_legs.

    Args:
        active_legs (Dict[str, OptionLeg]): Mapping of leg IDs to current leg data.

    Returns:
        Dict[str, Dict[str, float]]: Context with leg field values based on live data.
    """
    context = {}
    spot = 0
    for i, leg_id in enumerate(active_legs.keys(), 1):
        leg = active_legs[leg_id]
        context[f"leg{i}"] = {
            "premium": leg["ltp"],  # Current last traded price
            "strike": leg["strike"],  # Current strike price
            # Add other fields like delta, theta, iv if updated in active_legs
        }
        spot = leg["spot"]

    context["spot"] = spot  # Current spot price
    return context


def is_rebalancing_required(strategy: dict, traded_legs: Dict, active_legs: Dict) -> List[Dict]:
    """
    Check if rebalancing is required based on the strategy's rebalancing rules.
    If any rebalancing rule is triggered, return the corresponding actions to perform the rebalance.

    Args:
        request (BulkPayoffRequest): The strategy request containing the legs and strategy details.

    Returns:
        List[Dict]: A list of actions to perform if rebalancing is required; an empty list otherwise.
    """

    actions = []

    # Check if rebalancing rules exist in the strategy
    if "rebalancing" not in strategy:
        return actions  # No rebalancing rules defined

    # Iterate through the rebalancing rules and check if any rule's condition is met
    for rule in strategy["rebalancing"]:
        if is_rebalancing_required_for_rule(rule, traded_legs, active_legs):
            actions.extend(rule["actions"])  # Add actions for the triggered rule

    return actions


def is_rebalancing_required_for_rule(rule: Dict, traded_legs: Dict, active_legs: Dict) -> bool:
    """
    Check if rebalancing is required for a specific rebalancing rule.

    Args:
        rule (dict): A single rebalancing rule.
        request (BulkPayoffRequest): The strategy payoff request containing the legs and strategy details.

    Returns:
        bool: True if rebalancing is required for the rule, False otherwise.
    """
    # Extract necessary data from the request and rule
    initial_context = build_initial_context(traded_legs)
    current_context = build_current_context(active_legs)

    # Map leg IDs to context keys
    leg_id_to_context_key = {leg_id: f"leg{i+1}" for i, leg_id in enumerate(active_legs.keys())}

    # Evaluate the conditions in the rule
    condition = rule.get("condition")
    if not condition:
        return False  # No condition specified, return False

    # Compare the current value against the condition
    if evaluate_rebalancing_condition(condition, initial_context, current_context, leg_id_to_context_key):
        return True  # Rebalancing is required based on the rule

    return False  # No rebalancing required for this rule


def evaluate_rebalancing_condition(condition: Dict, initial_context: Dict[str, Dict[str, float]], current_context: Dict[str, Dict[str, float]], leg_id_to_context_key: Dict[str, str]) -> bool:
    """
    Evaluate the rebalancing condition to check if it triggers a rebalancing action.

    Args:
        condition (dict): The rebalancing condition to be checked.
        initial_context (dict): The initial context of leg values.
        current_context (dict): The current context of leg values.
        leg_id_to_context_key (dict): A mapping of leg IDs to context keys.

    Returns:
        bool: True if the condition triggers a rebalancing action, False otherwise.
    """
    leg_id = str(condition.get("leg"))
    field = condition.get("field")
    operator = condition.get("operator")
    expression = condition.get("expression")

    # Check if the leg has any active lots
    leg_context_key = leg_id_to_context_key.get(leg_id)

    # Check if leg exists and has active lots
    if leg_context_key not in initial_context or current_context.get(leg_context_key) is None:
        return False

    current_value = current_context.get(leg_context_key, {}).get(field)
    if current_value is None:
        return False  # The field is missing in the current context

    # Evaluate the condition expression
    try:
        condition_value = evaluate_expression(expression, initial_context, current_context)
    except ValueError:
        return False  # Invalid expression

    # Compare current value against the condition value
    try:
        if operator in OPERATOR_MAP and OPERATOR_MAP[operator](current_value, condition_value):
            return True  # Condition met, rebalancing required
    except Exception as e:
        print(f"Error evaluating condition: {e}")
        return False

    return False  # Condition not met, no rebalancing


async def rebalance(actions: List[Dict], traded_legs: Dict[str, Dict], active_legs: Dict[str, Dict], rows: List[Dict]) -> Tuple[Dict[str, Dict], Dict[str, Dict], float]:
    """
    Executes the rebalancing actions and updates the legs in traded_legs.

    Args:
        actions (list): A list of actions to be performed, such as 'buy', 'sell', or 'square_off'.
        traded_legs (dict): The current traded legs to be updated.
        active_legs (dict): The current active legs used for P&L calculations.
        rows (list): The option chain data for the current timestamp.

    Returns:
        Tuple[Dict[str, Dict], Dict[str, Dict], float]: The updated traded_legs, active_legs, and the accumulated realized P&L.
    """
    realized_pl = 0  # To accumulate the realized P&L during rebalancing
    updated_traded_legs = copy.deepcopy(traded_legs)  # To track updates to traded_legs
    updated_active_legs = copy.deepcopy(active_legs)  # To track updates to active_legs

    # Iterate over each action in the list of actions
    for action in actions:
        action_leg_id = str(action["leg"])  # Get the leg ID from the action

        # Check if the leg ID is already in traded_legs
        if action_leg_id in active_legs:
            leg = active_legs[action_leg_id]

            if action["type"] == "square_off":
                # Square off the leg (close the position)
                if leg["lots"] > 0 and leg["ltp"] is not None:
                    # Calculate P&L based on the type of trade (buy/sell)
                    pl = (leg["ltp"] - leg["op_pr"]) * leg["lots"] if leg["tr_type"] == "b" else (leg["op_pr"] - leg["ltp"]) * leg["lots"]
                    realized_pl += pl  # Accumulate the realized P&L
                    leg["lots"] = 0  # Close the position
                    updated_active_legs[action_leg_id] = leg  # Update active_legs as well
                    print(f"Realized P&L for square off action: {pl}")

            elif action["type"] in ["buy", "sell"]:
                # Execute buy or sell action
                strike_conf = {"condition": action["strikeCondition"], "value": action["strikeValue"]}
                if action["strikeCondition"] in ["premium", "delta"]:
                    strike_conf["criterion"] = action.get("strikeCriterion", "closest")

                # Get the underlying value for the option chain (assuming 'underlyingValue' exists in rows)
                underlying_value = next((row[opt]["underlyingValue"] for row in rows for opt in ["PE", "CE"] if opt in row and row[opt].get("underlyingValue")), None)
                if not underlying_value:
                    continue  # Skip if no underlying value found

                # Compute the target strike price based on the conditions
                target_strike = await compute_target_strike(rows, leg["type"], strike_conf, underlying_value, build_current_context(updated_active_legs))

                # Get the last traded price (LTP) for the target strike
                ltp = next((row[leg["type"]]["lastPrice"] for row in rows if abs(row["strikePrice"] - target_strike) < 1e-6), None)
                if ltp is None:
                    continue  # Skip if LTP is not found for the target strike

                # Determine whether the action is a buy or sell
                tr_type = "b" if action["type"] == "buy" else "s"

                # Create a new leg based on the action
                new_leg = {
                    "id": action_leg_id,
                    "type": "CE" if action["optionType"].lower() == "call" else "PE",
                    "strike": target_strike,
                    "op_pr": ltp,
                    "tr_type": tr_type,
                    "lots": int(action["lots"]),
                    "expiry_days": leg["expiry_days"],
                    "iv": leg["iv"],
                    "expiryDate": leg["expiryDate"],
                    "ltp": ltp,
                    "spot": underlying_value,
                }

                # Update the leg in traded_legs
                updated_traded_legs[action_leg_id] = copy.deepcopy(new_leg)
                updated_active_legs[action_leg_id] = copy.deepcopy(new_leg)

    # Return both updated traded_legs and active_legs along with realized P&L
    return updated_traded_legs, updated_active_legs, realized_pl


# Helper Functions (Modularized Components)


def compute_total_pl(active_legs: Dict[str, Dict], realized_pl: float) -> float:
    """
    Computes the total P&L by calculating the unrealized profit/loss for each leg and adding the realized P&L.

    Args:
        active_legs (dict): A dictionary of active legs.
        realized_pl (float): The realized profit/loss.

    Returns:
        float: The total profit/loss (realized + unrealized).
    """
    for leg in active_legs.values():
        if not "ltp" in leg.keys():
            print("Leg", leg)
    unrealized_pl = sum(
        (leg["ltp"] - leg["op_pr"]) * leg["lots"] if leg["tr_type"] == "b" else (leg["op_pr"] - leg["ltp"]) * leg["lots"]
        for leg in active_legs.values()
        if leg["lots"] > 0 and "ltp" in leg and leg["ltp"] is not None
    )
    return realized_pl + unrealized_pl


def compute_total_premium(legs: Dict[str, Dict]) -> float:
    """
    Computes the total premium of the strategy from the legs.
    This is calculated using the initial option price (op_pr) and the number of lots for each leg.

    Args:
        legs (dict): The active legs containing leg data (option price, lots, etc.).

    Returns:
        float: The total premium of the strategy.
    """
    total_premium = 0

    for leg in legs.values():
        # Use the option price (op_pr) and number of lots to calculate the total premium
        total_premium += leg["op_pr"] * leg["lots"] if leg["tr_type"] == "b" else -leg["op_pr"] * leg["lots"]

    return total_premium


def fetch_option_chain_data_for_request(request: BulkPayoffRequest) -> Dict:
    """
    Fetches the option chain data based on the request's symbol, start and end dates, and interval.
    Args:
        request (BulkPayoffRequest): The strategy request with the necessary details.
    Returns:
        dict: The fetched option chain data.
    """
    expiry_date = next((leg.expiryDate for leg in request.legs if leg.expiryDate), None)
    if not expiry_date:
        min_expiry_days = min(leg.expiry_days for leg in request.legs)
        computed_expiry_dt = datetime.strptime(request.start, "%Y-%m-%d") + timedelta(days=min_expiry_days)
        expiry_date = computed_expiry_dt.strftime("%d-%b-%Y")
    start_dt = datetime.strptime(request.start, "%Y-%m-%d").strftime("%d-%m-%Y")
    end_dt = datetime.strptime(request.end, "%Y-%m-%d").strftime("%d-%m-%Y")
    return fetch_option_chain_data(request.symbol, start_dt, end_dt, request.interval, expiry_date)


from datetime import datetime


def group_option_data_by_timestamp(data_rows: List[Dict]) -> Dict[str, List[Dict]]:
    """
    Groups the option chain data by timestamp for easier processing.
    Args:
        data (list): The list of option chain data.
    Returns:
        dict: The data grouped by timestamp.
    """

    # Convert timestamps to datetime objects for correct chronological sorting
    timestamps = [row["timestamp"] for row in data_rows if "timestamp" in row]

    # Sort timestamps by date
    sorted_timestamps = sorted(set(timestamps), key=lambda x: datetime.strptime(x, "%d-%b-%Y"))

    # Create the grouped dictionary based on sorted timestamps
    grouped = {ts: [] for ts in sorted_timestamps}
    for row in data_rows:
        if row.get("timestamp"):
            grouped[row["timestamp"]].append(row)

    return grouped


def check_exit_conditions(total_pl: float, active_legs: Dict[str, Dict], strategy: dict) -> Optional[Dict]:
    """
    Check if exit conditions (target profit or stop loss) are met based on the total P&L and strategy settings.
    The total premium is computed using the compute_total_premium function.

    Args:
        total_pl (float): The total profit/loss calculated from all legs.
        active_legs (dict): The dictionary of active legs with their updated values (including ltp).
        strategy (dict): The strategy containing exit conditions and target values.

    Returns:
        dict: The updated leg information and realized P&L if the exit condition is met; None otherwise.
    """
    # Compute the total premium from the request's legs
    total_premium = compute_total_premium(active_legs)

    # Retrieve exit conditions flags and values from the strategy
    exitStopLossEnabled = strategy.get("exitStopLossEnabled", False)
    exitBookProfitsEnabled = strategy.get("exitBookProfitsEnabled", False)

    # Calculate the trade target and stop loss values as percentages of total premium
    trade_target = total_premium * (float(strategy.get("targetValue", 0)) / 100.0)
    trade_stop_loss = total_premium * (float(strategy.get("stopLossValue", 0)) / 100.0)

    updated_legs = []
    # Check if the exit conditions are met
    if (exitBookProfitsEnabled and total_pl >= trade_target) or (exitStopLossEnabled and total_pl <= -trade_stop_loss):
        # If the exit condition is met, prepare updated leg information
        updated_legs = [
            {
                "strike": leg["strike"],
                "type": leg["type"],
                "tr_type": leg["tr_type"],
                "price": leg["op_pr"],
                "ltp": leg["ltp"],  # Access the ltp directly from active_legs
                "profit_loss": round((leg["ltp"] - leg["op_pr"]) if leg["tr_type"] == "b" else (leg["op_pr"] - leg["ltp"]), 2) if leg["ltp"] is not None else None,
                "lots": leg["lots"],
            }
            for leg in active_legs.values()
            if leg["lots"] > 0
        ]

        # Calculate realized P&L for the timestamp
        pl_realized_this_ts = sum(
            (leg["ltp"] - leg["op_pr"]) * leg["lots"] if leg["tr_type"] == "b" else (leg["op_pr"] - leg["ltp"]) * leg["lots"]
            for leg in active_legs.values()
            if leg["lots"] > 0 and leg["ltp"] is not None
        )

        # Square off positions by setting lots to 0
        for leg in active_legs.values():
            if leg["lots"] > 0 and leg["ltp"] is not None:
                leg["lots"] = 0  # Close the leg position

        # Return the updated leg details and realized P&L
        return {"updated_legs": updated_legs, "pl_realized_this_ts": pl_realized_this_ts, "exit_condition": "target_profit" if total_pl >= trade_target else "stop_loss"}

    return None


def get_underlying_value_for_timestamp(rows: List[Dict]) -> float:
    """
    Retrieves the underlying value (spot price) for a specific timestamp.
    Args:
        rows (list): The option chain data for the timestamp.
    Returns:
        float: The spot price at the given timestamp.
    """
    return next((row[opt]["underlyingValue"] for row in rows for opt in ["PE", "CE"] if opt in row and row[opt].get("underlyingValue")), None)
