import asyncio
import copy
import re
from calendar import c
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import httpx
from async_lru import alru_cache
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.routers.opstrat import BulkPayoffRequest, OptionLeg
from app.routers.rebalancing import calculate_bulk_payoff_with_rebalancing
from app.util.ohlc_dhan import (
    get_all_strikes_for_symbol,
    get_ohlc_data_for_option,
    get_ohlc_data_for_symbol,
    get_spot_price,
)

router = APIRouter()


API_BASE_URL = "https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/ohlcd"


# Request model for intraday backtest
class IntradayBacktestRequest(BaseModel):
    strategy: dict[str, object]  # full strategy JSON object
    expirationDate: str  # e.g., "2025-08-07" or "07-Aug-2025"
    startDate: str  # e.g., "2025-07-27"
    symbol: str


def get_days_to_backtest(start_date: str, expiration_date: str, skip_today: bool = True) -> list[str]:
    """
    Returns a list of trading days between start_date and expiration_date.
    If skip_today is True, today's date is excluded.
    """
    days: list[str] = []
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(expiration_date, "%d-%b-%Y") if "-" in expiration_date else datetime.strptime(expiration_date, "%Y-%m-%d")
    today = datetime.now().date()
    current = start
    while current <= end:
        if not (skip_today and current.date() == today):
            days.append(current.strftime("%Y-%m-%d"))
        current += timedelta(days=1)
    return days


async def get_strike_prices_for_day(spot_price: float, entry_legs: list[dict[str, object]], symbol: str, expiration_date: str, date: str) -> dict[str, float]:
    """
    For each leg, computes the strike price using get_strike_from_config and returns a dict mapping leg id to strike price.
    Only passes spot_price, strike_config, symbol, expiration_date, and date.
    """
    from app.util.ohlc_dhan import get_strike_from_config

    strike_prices: dict[str, float] = {}
    for leg in entry_legs:
        leg_id = str(leg.get("id"))
        strike_config = leg.get("strikeConfig", {})
        if not isinstance(strike_config, dict):
            strike_config = {}
        option_type = str(leg.get("optionType", "")).lower()
        if option_type.lower() == "call":
            option_type = "ce"
        elif option_type.lower() == "put":
            option_type = "pe"
        # Only pass required params
        strike = await get_strike_from_config(spot_price, strike_config=strike_config, symbol=symbol, expiration_date=expiration_date, entry_date=date, option_type=option_type)
        strike_prices[leg_id] = strike
    return strike_prices


def safe_float(val: Any) -> float:
    try:
        return float(val)
    except Exception:
        return 0.0


def get_premiums(leg_ohlc_data: Dict[str, Dict[str, Any]], timestamp: int) -> Dict[str, float]:
    """
    Returns a dict of close premiums for each leg at the given timestamp.
    """
    premiums: Dict[str, float] = {}
    for leg_id, ohlc_data in leg_ohlc_data.items():
        premium = safe_float(ohlc_data.get(str(timestamp), {}).get("c", 0.0))
        premiums[leg_id] = premium
    return premiums


def get_premiums_for_legs(premiums, legs):
    # For each leg, get action - buy or sell and accordingly calculate the premium
    total_premium = 0
    for leg in legs:
        leg_id = str(leg.get("id"))
        action = str(leg.get("action", "")).lower()
        premium = premiums.get(leg_id, 0.0)
        if action == "buy":
            total_premium += premium
        elif action == "sell":
            total_premium -= premium

    return total_premium


def calculate_intraday_pnl_for_legs(leg_ohlc_data: Dict[str, Dict[str, Any]], entry_time: str, strategy_dict: Dict[str, Any], date: str, legs: list[dict[str, Any]]) -> Dict[str, Any]:
    """
    Simulates intraday P/L for each leg from entry_time to end of day, applying stop loss and target logic from strategy_dict.
    Returns per-tick premiums and total premium for each timestamp.
    """
    from datetime import datetime

    # Get Option Chain for the given date
    leg_ohlc_data = filter_option_chain_for_date(leg_ohlc_data, date, entry_time)

    # Get timestamps from the first leg OHLC data
    if not leg_ohlc_data:
        return {}
    timestamps = sorted(int(ts) for ts in next(iter(leg_ohlc_data.values())).keys())
    if not timestamps:
        return {}

    # Get entry prices at the first timestamp
    entry_prices = get_premiums(leg_ohlc_data, timestamps[0])

    # Get the premium (buy/sell for each leg)
    entry_premium = get_premiums_for_legs(entry_prices, legs)

    results: Dict[int, Dict[str, Any]] = {}
    exit_result = None

    for ts in timestamps:
        # Get premiums at this timestamp for all the legs
        premiums = get_premiums(leg_ohlc_data, ts)

        # Calculate total premium at this timestamp - this may be right as some of them can be buy and other can be sell.. we should fix it as some point looking at the strategy of the leg.
        total_premium = get_premiums_for_legs(premiums, legs)

        # pnl
        pnl = total_premium - entry_premium

        if pnl == 10.0:
            print(f"Debug: PnL is exactly 10.0 at timestamp {ts}, entry_premium: {entry_premium}, total_premium: {total_premium}")

        # Check exit conditions based on the strategy
        exit_result = check_exit(entry_premium, total_premium, pnl, strategy_dict, ts)

        results[ts] = {"premiums": premiums, "total_premium": total_premium, "pnl": pnl}
        # Append exit result if not none

        if exit_result:
            print(f"Exit condition met at {datetime.fromtimestamp(ts)}: {exit_result}")
            break

    # Total pnl for the day (entry premium - last premium)
    last_ts = list(results.keys())[-1]
    last_premium = results[last_ts]["total_premium"]
    daily_pnl = -entry_premium + last_premium

    return {
        "entry_prices": entry_prices,
        "results": results,
        "daily_pnl": daily_pnl,
        "exit_result": exit_result if exit_result else None,
    }


def filter_ohlc_by_date(ohlc_dict: dict, date: str) -> dict:
    """
    Filters an OHLC dict (epoch keys) to only those entries matching the given date (YYYY-MM-DD).
    Returns a dict of epoch keys for that date.
    """
    from datetime import datetime

    filtered = {}
    for ts_str, ohlc in ohlc_dict.items():
        try:
            ts = int(ts_str)
            ts_date = datetime.fromtimestamp(ts).strftime("%Y-%m-%d")
            if ts_date == date:
                filtered[ts_str] = ohlc
        except Exception:
            continue
    return filtered


def calculate_nifty_movement(ohlc_dict: dict, date: str) -> dict:
    """
    Given OHLC dict (epoch keys) and a date, returns dict with first/last value and percent change for NIFTY.
    """
    from datetime import datetime

    nifty_ohlc_for_date = filter_ohlc_by_date(ohlc_dict, date)
    result = {
        "nifty_first_value": 0.0,
        "nifty_last_value": 0.0,
        "nifty_change_percent": 0.0,
    }
    if nifty_ohlc_for_date:
        sorted_times = sorted(nifty_ohlc_for_date.keys(), key=lambda x: int(x))
        result["nifty_first_value"] = nifty_ohlc_for_date[sorted_times[0]].get("c", 0.0) if sorted_times else 0.0
        result["nifty_last_value"] = nifty_ohlc_for_date[sorted_times[-1]].get("c", 0.0) if sorted_times else 0.0
        # Try to get 15:30 close if available
        for ts_str in sorted_times:
            ts = int(ts_str)
            ts_dt = datetime.fromtimestamp(ts)
            if ts_dt.strftime("%H:%M") == "15:30":
                result["nifty_last_value"] = nifty_ohlc_for_date[ts_str].get("c", result["nifty_last_value"])
                break
        if result["nifty_first_value"] and result["nifty_last_value"]:
            nifty_change = result["nifty_last_value"] - result["nifty_first_value"]
            result["nifty_change_percent"] = (nifty_change / result["nifty_first_value"]) * 100 if result["nifty_first_value"] else 0.0
        else:
            result["nifty_change_percent"] = 0.0
    return result


@router.post("/")
async def backtest_intraday(request: IntradayBacktestRequest) -> dict[str, object]:
    """
    Intraday backtesting endpoint. Accepts strategy, expirationDate, startDate, and symbol.
    Returns computed results for the intraday backtest.
    """
    days = get_days_to_backtest(request.startDate, request.expirationDate, skip_today=True)
    start_date = days[0]
    end_date = days[-1]

    # For testing, we will do for 10 days
    # if len(days) > 10:
    #     days = days[:10]
    #     end_date = days[-1]

    # Fetch all symbol OHLC data for the full date range
    symbol_ohlc_all_days = await get_ohlc_data_for_symbol(request.symbol, start_date, end_date)

    # Strategy to ge the legs, entry conditions, etc.
    strategy_dict = request.strategy

    # Get entry time from strategy
    entry_conditions = strategy_dict.get("entryConditions", {})
    if not isinstance(entry_conditions, dict):
        entry_conditions = {}
    entry_time = entry_conditions.get("time", "09:15")

    # Get entry legs from strategy
    entry_legs = strategy_dict.get("legs", [])

    results = []

    for date in days:
        # Get spot price for entry time and date
        spot_price = await get_spot_price(symbol_ohlc_all_days, entry_time, date)
        print(f"Spot price for {request.symbol} on {date} at {entry_time}: {spot_price}")

        # Check if holiday or invalid spot price
        if spot_price is None or spot_price <= 0:
            print(f"Skipping date {date} due to invalid spot price: {spot_price}")
            continue

        # Get Legs strikes
        strike_prices_dict = await get_strike_prices_for_day(spot_price, entry_legs, request.symbol, request.expirationDate, date)

        print(f"Processing date: {date}, Spot Price: {spot_price}")

        # Get Option Chains for All legs for that day
        leg_ohlc_data: Dict[str, Dict[str, Any]] = {}
        for leg in entry_legs:
            leg_id = str(leg.get("id"))
            option_type = str(leg.get("optionType", ""))
            # send PE or CE if put or call
            if option_type.lower() == "call":
                option_type = "ce"
            elif option_type.lower() == "put":
                option_type = "pe"
            strike = strike_prices_dict.get(leg_id)
            if strike is None:
                continue
            # Use request.symbol, option_type, strike, request.expirationDate, date, date
            ohlc_option = await get_ohlc_data_for_option(
                request.symbol,
                option_type,
                str(int(strike)),  # Remove .0 in strike
                request.expirationDate,
                date,
                date,
            )
            leg_ohlc_data[leg_id] = ohlc_option

        # Find P/L based on the original strikes and the entry time
        pnl = calculate_intraday_pnl_for_legs(leg_ohlc_data, entry_time, strategy_dict=strategy_dict, date=date, legs=entry_legs)

        # Find nifty movement - filter symbol_ohlc_all_days for current date and get the first and last value
        nifty_movement = calculate_nifty_movement(symbol_ohlc_all_days, date)

        result = {
            "date": date,
            "spot_price": spot_price,
            "strikes": strike_prices_dict,
            "legs": [
                {"id": leg.get("id"), "action": str(leg.get("action", "")), "optionType": str(leg.get("optionType", "")), "strike": strike_prices_dict.get(str(leg.get("id")))} for leg in entry_legs
            ],
            "leg_ohlc_data": leg_ohlc_data,
            "pnl": pnl,
            **nifty_movement,
        }

        results.append(result)

    return {
        "received": {"strategy": request.strategy, "expirationDate": request.expirationDate, "startDate": request.startDate, "symbol": request.symbol},
        "days_to_backtest": days,
        "results": results,
        "status": "success",
    }


def filter_option_chain_for_date(leg_ohlc_data: Dict[str, Dict[str, Any]], date: str, entry_time: str) -> Dict[str, Dict[str, Any]]:
    """
    Filters leg_ohlc_data for a specific date and entry_time.
    For each leg, only keeps OHLC entries where the timestamp's date matches the given date.
    If entry_time is provided, only keeps entries at or after entry_time (HH:MM).
    Returns a dict with filtered data for that date.
    """
    from datetime import datetime

    filtered: Dict[str, Dict[str, Any]] = {}
    entry_time_dt = None
    if entry_time:
        try:
            entry_time_dt = datetime.strptime(f"{date} {entry_time}", "%Y-%m-%d %H:%M")
        except Exception:
            entry_time_dt = None
    for leg_id, ohlc_dict in leg_ohlc_data.items():
        filtered_leg: Dict[str, Any] = {}
        for ts_str, ohlc in ohlc_dict.items():  # type: ignore
            try:
                ts: int = int(ts_str)
                ts_dt: datetime = datetime.fromtimestamp(ts)
                ts_date: str = ts_dt.strftime("%Y-%m-%d")
                if ts_date == date:
                    if entry_time_dt is None or ts_dt >= entry_time_dt:
                        filtered_leg[ts_str] = ohlc
            except Exception:
                continue
        filtered[leg_id] = filtered_leg
    return filtered


def check_exit(entry_premium: float, total_premium: float, pnl: float, exit_config: Dict[str, Any], timestamp: int) -> Optional[str]:
    """
    Checks if exit conditions are met. Returns exit reason or None.
    Supports percent and absolute stop loss/target.
    """
    if not exit_config or entry_premium == 0:
        return None

    # Get Current time to add in Stop Loss triggered time
    current_time = datetime.fromtimestamp(timestamp).strftime("%H:%M")

    percent_change = ((total_premium - entry_premium) / entry_premium) * 100 if entry_premium else 0
    # Stop Loss
    if exit_config.get("exitStopLossEnabled"):
        stop_loss_type = exit_config.get("stopLossType", "percent")
        stop_loss_value = float(exit_config.get("stopLossValue", 0))
        if stop_loss_type == "percent" and percent_change <= -stop_loss_value:
            return f"Stop Loss Triggered at {current_time}"
        elif stop_loss_type != "percent" and pnl <= -stop_loss_value:
            return f"Stop Loss Triggered at {current_time}"
    # Target
    if exit_config.get("exitBookProfitsEnabled"):
        target_type = exit_config.get("targetType", "percent")
        target_value = float(exit_config.get("targetValue", 0))
        if target_type == "percent" and percent_change >= target_value:
            return f"Target Triggered at {current_time}"
        elif target_type != "percent" and pnl >= target_value:
            return f"Target Triggered at {current_time}"
    return None
