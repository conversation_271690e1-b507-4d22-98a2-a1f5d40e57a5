from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Request
from app.util.menu import menu
from app.util.stocks import get_most_active
from app.util.templates import templates

from .auth import get_user_data

router = APIRouter()

# Define metadata for each calculator
CALCULATOR_METADATA = {
    "sip": {"title": "SIP Calculator | AI Bull", "description": "Calculate your Systematic Investment Plan (SIP) returns with our easy-to-use SIP Calculator to plan your investments effectively."},
    "emi": {"title": "EMI Calculator | AI Bull", "description": "Determine your monthly EMI payments for loans with our accurate EMI Calculator to manage your finances better."},
    "compound-interest": {"title": "Compound Interest Calculator | AI Bull", "description": "Calculate the power of compound interest on your investments with our Compound Interest Calculator."},
    "fd": {"title": "Fixed Deposit Calculator | AI Bull", "description": "Estimate the maturity amount and interest earned on your Fixed Deposits with our FD Calculator."},
    "rd": {"title": "Recurring Deposit Calculator | AI Bull", "description": "Plan your savings with our Recurring Deposit Calculator to calculate maturity amounts and interest earned."},
    "calculators": {"title": "Financial Calculators | AI Bull", "description": "Use our modern financial calculators to plan your investments, loans, and savings effectively."},
}


async def calculator_response(request: Request, access_token: str = Cookie(None), calculator_slug: str = "calculators"):
    user_data = get_user_data(access_token)
    # Get metadata for the current calculator or default to main calculators page
    metadata = CALCULATOR_METADATA.get(calculator_slug, CALCULATOR_METADATA["calculators"])
    return templates.TemplateResponse(
        "/calculators/main.html",
        {"request": request, **menu, **user_data, "active_stocks": get_most_active(), "meta_title": metadata["title"], "meta_description": metadata["description"], "calculator_slug": calculator_slug},
    )


CALCULATOR_SLUGS = ["sip", "emi", "compound-interest", "fd", "rd"]


# Common calculator handler
async def calculator_handler(request: Request, access_token: str, slug: str):
    print("Main slug:", slug)
    return await calculator_response(request, access_token, slug)


# Base calculators route
@router.get("/calculators")
async def get_main_calculator(request: Request, access_token: str = Cookie(None)):
    return await calculator_response(request, access_token, "calculators")


# Dynamically register routes
for slug in CALCULATOR_SLUGS:
    route_path = f"/{slug}-calculator"
    route_name = f"get_{slug}_calculator"

    async def route_func(request: Request, access_token: str = Cookie(None), slug=slug):
        return await calculator_handler(request, access_token, slug)

    router.add_api_route(route_path, route_func, name=route_name)
