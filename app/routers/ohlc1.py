from typing import Any, Dict, Optional

import numpy as np
import httpx
import talib
from fastapi import APIRouter, HTTPException, Query

router = APIRouter()


# Custom candlestick pattern functions for harami patterns.
def bullish_harami(o, h, l, c):
    prev_o = np.roll(o, 1)
    prev_c = np.roll(c, 1)
    prev_bearish = prev_c < prev_o
    curr_bullish = c > o
    contained = (o > prev_c) & (c < prev_o)
    result = np.where(prev_bearish & curr_bullish & contained, 1, 0)
    result[0] = 0
    return result


def bearish_harami(o, h, l, c):
    prev_o = np.roll(o, 1)
    prev_c = np.roll(c, 1)
    prev_bullish = prev_c > prev_o
    curr_bearish = c < o
    contained = (o < prev_c) & (c > prev_o)
    result = np.where(prev_bullish & curr_bearish & contained, 1, 0)
    result[0] = 0
    return result


def compute_candle_score(candle: Dict[str, Any]) -> int:
    """
    Computes a score for a single candle based on candlestick patterns and technical indicators.

    Positive scores favor a long (bullish) signal,
    while negative scores suggest bearish conditions.
    """
    score = 0
    patterns = candle.get("p", "")
    rsi = candle.get("rsi")
    ema = candle.get("ema")
    close = candle.get("c")

    # Pattern-based scoring: Adjust weights as needed.
    if "Bullish Engulfing" in patterns:
        score += 10
    if "Three White Soldiers" in patterns:
        score += 15
    if "Hammer" in patterns:
        score += 5
    if "Bullish Harami" in patterns:
        score += 5

    if "Bearish Engulfing" in patterns:
        score -= 10
    if "Three Black Crows" in patterns:
        score -= 15
    if "Hanging Man" in patterns:
        score -= 5
    if "Bearish Harami" in patterns:
        score -= 5

    # RSI-based scoring:
    # Oversold (<30) may indicate a good long entry,
    # Overbought (>70) may signal bearish conditions.
    if rsi is not None:
        if rsi < 30:
            score += 5
        elif rsi > 70:
            score -= 5

    # EMA comparison:
    # If the close is above the EMA, add bullish points.
    if ema is not None and close is not None:
        if close > ema:
            score += 3
        elif close < ema:
            score -= 3

    return score


# This endpoint returns OHLC data enriched with candlestick pattern detection, technical indicators,
# moving average crossover signals, and a computed score for each candle.
@router.get("/", response_model=Dict[str, Any])
async def get_yohlc_data(
    symbol: str = Query(..., description="The stock symbol, e.g. INFY"),
    interval: str = Query("1d", description="Time interval: 1m, 15m, or 1d"),
    start: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
):
    """
    Fetches OHLC data from the external API, computes candlestick patterns,
    technical indicators (RSI, EMA, SMA, MACD, VWAP), moving average crossover signals,
    and a score that can help assess scaling opportunities.

    Optional query parameters "start" and "end" can be provided to limit the data range.

    A new key 'p' is added for candlestick patterns, 's' for signals, and 'score' for the computed scaling opportunity.

    Example:
      GET /ohlc?symbol=INFY&interval=15m&start=2025-03-17&end=2025-03-18
    """
    url = "https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/yohlc"
    params = {"symbol": symbol, "interval": interval}
    if start:
        params["start"] = start
    if end:
        params["end"] = end

    async with httpx.AsyncClient() as client:
        response = await client.get(url, params=params)
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail="Error fetching OHLC data.")

        data = response.json()  # Expecting a dict with timestamps as keys.
        if not data:
            raise HTTPException(status_code=404, detail="No OHLC data found.")

    # Sort timestamps (assuming keys are numeric strings)
    sorted_keys = sorted(data.keys(), key=lambda k: int(k))
    try:
        opens = np.array([data[k]["o"] for k in sorted_keys])
        highs = np.array([data[k]["h"] for k in sorted_keys])
        lows = np.array([data[k]["l"] for k in sorted_keys])
        closes = np.array([data[k]["c"] for k in sorted_keys])
        volumes = None
        if "v" in data[sorted_keys[0]]:
            volumes = np.array([data[k].get("v", 0) for k in sorted_keys])
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing OHLC data: {e}")

    # Define candlestick pattern functions.
    pattern_functions = {
        "Marubozu": talib.CDLMARUBOZU,
        "Hammer": talib.CDLHAMMER,
        "Inverted Hammer": talib.CDLINVERTEDHAMMER,
        "Bullish Engulfing": lambda o, h, l, c: np.where(talib.CDLENGULFING(o, h, l, c) > 0, 1, 0),
        "Piercing Pattern": talib.CDLPIERCING,
        "Morning Star": talib.CDLMORNINGSTAR,
        "Three White Soldiers": talib.CDL3WHITESOLDIERS,
        "Bullish Harami": bullish_harami,
        "Shooting Star": talib.CDLSHOOTINGSTAR,
        "Hanging Man": talib.CDLHANGINGMAN,
        "Bearish Engulfing": lambda o, h, l, c: np.where(talib.CDLENGULFING(o, h, l, c) < 0, 1, 0),
        "Dark Cloud Cover": talib.CDLDARKCLOUDCOVER,
        "Evening Star": talib.CDLEVENINGSTAR,
        "Three Black Crows": talib.CDL3BLACKCROWS,
        "Bearish Harami": bearish_harami,
        "Doji": talib.CDLDOJI,
        "Dragonfly Doji": talib.CDLDRAGONFLYDOJI,
        "Gravestone Doji": talib.CDLGRAVESTONEDOJI,
        "Long-legged Doji": talib.CDLLONGLEGGEDDOJI,
        "Spinning Top": talib.CDLSPINNINGTOP,
        "High Wave Candle": talib.CDLHIGHWAVE,
    }

    pattern_results = {}
    for pattern_name, func in pattern_functions.items():
        try:
            pattern_array = func(opens, highs, lows, closes)
            pattern_results[pattern_name] = pattern_array
        except Exception as e:
            pattern_results[pattern_name] = np.zeros_like(opens)
            print(f"Error computing pattern {pattern_name}: {e}")

    # Aggregate pattern names for each timestamp.
    patterns_by_timestamp = {}
    for idx, ts in enumerate(sorted_keys):
        triggered = []
        for pattern_name, arr in pattern_results.items():
            if arr[idx] != 0:
                triggered.append(pattern_name)
        patterns_by_timestamp[ts] = ", ".join(triggered) if triggered else ""
    for ts in sorted_keys:
        data[ts]["p"] = patterns_by_timestamp[ts]

    # Compute technical indicators.
    try:
        rsi = talib.RSI(closes, timeperiod=14)
        ema = talib.EMA(closes, timeperiod=9)
        sma = talib.SMA(closes, timeperiod=14)
        macd, macdsignal, macdhist = talib.MACD(closes, fastperiod=12, slowperiod=26, signalperiod=9)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error computing technical indicators: {e}")

    # VWAP: If volume data is available.
    vwap = []
    if volumes is not None and np.sum(volumes) != 0:
        cum_vol = np.cumsum(volumes)
        cum_vol_price = np.cumsum(closes * volumes)
        vwap = (cum_vol_price / cum_vol).tolist()
    else:
        vwap = ["N/A"] * len(closes)

    # Compute moving average crossover signals using a short EMA (9) and a long EMA (21).
    short_ema = talib.EMA(closes, timeperiod=9)
    long_ema = talib.EMA(closes, timeperiod=21)
    signals = np.array([""] * len(closes))
    for i in range(1, len(closes)):
        if short_ema[i - 1] <= long_ema[i - 1] and short_ema[i] > long_ema[i]:
            signals[i] = "Golden Cross"
        elif short_ema[i - 1] >= long_ema[i - 1] and short_ema[i] < long_ema[i]:
            signals[i] = "Death Cross"

    # Attach indicators and compute the score for each candle record.
    for idx, ts in enumerate(sorted_keys):
        data[ts]["rsi"] = float(rsi[idx]) if not np.isnan(rsi[idx]) else None
        data[ts]["ema"] = float(ema[idx]) if not np.isnan(ema[idx]) else None
        data[ts]["sma"] = float(sma[idx]) if not np.isnan(sma[idx]) else None
        data[ts]["macd"] = float(macd[idx]) if not np.isnan(macd[idx]) else None
        data[ts]["macds"] = float(macdsignal[idx]) if not np.isnan(macdsignal[idx]) else None
        data[ts]["macdh"] = float(macdhist[idx]) if not np.isnan(macdhist[idx]) else None
        data[ts]["vwap"] = vwap[idx] if vwap[idx] != "N/A" else "N/A"
        data[ts]["s"] = signals[idx]
        data[ts]["score"] = compute_candle_score(data[ts])

    return data
