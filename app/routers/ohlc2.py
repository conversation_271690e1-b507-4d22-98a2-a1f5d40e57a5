from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import requests
import talib
from fastapi import APIRouter, HTTPException, Query

router = APIRouter()

# --- IST Holiday Handling ---
# Define a set of IST holidays (use proper dates for your market)
IST_HOLIDAYS = {
    "2025-01-26",  # Republic Day
    "2025-08-15",  # Independence Day
    "2025-10-02",  # Gandhi Jayanti
    # Add more holidays as needed.
}


def get_last_trading_day(date: datetime) -> datetime:
    """
    Given a date, roll back until a day that is not a Saturday, Sunday,
    or an IST holiday.
    """
    while date.weekday() >= 5 or date.strftime("%Y-%m-%d") in IST_HOLIDAYS:
        date -= timedelta(days=1)
    return date


def adjust_start_date_for_interval(start: Optional[str], interval: str) -> Optional[str]:
    """
    For lower timeframes like 15m or 10m, subtract one day from the provided start date
    so that sufficient historical data is fetched to compute the indicators.
    If the resulting date falls on a weekend or an IST holiday, it rolls back further.
    """
    if start is None:
        return None
    if interval in ["15m", "10m"]:
        start_date = datetime.strptime(start, "%Y-%m-%d")
        adjusted_date = start_date - timedelta(days=1)
        adjusted_date = get_last_trading_day(adjusted_date)
        return adjusted_date.strftime("%Y-%m-%d")
    return start


# --- Candlestick Pattern Functions ---
def bullish_harami(o, h, l, c):
    prev_o = np.roll(o, 1)
    prev_c = np.roll(c, 1)
    prev_bearish = prev_c < prev_o
    curr_bullish = c > o
    contained = (o > prev_c) & (c < prev_o)
    result = np.where(prev_bearish & curr_bullish & contained, 1, 0)
    result[0] = 0
    return result


def bearish_harami(o, h, l, c):
    prev_o = np.roll(o, 1)
    prev_c = np.roll(c, 1)
    prev_bullish = prev_c > prev_o
    curr_bearish = c < o
    contained = (o < prev_c) & (c > prev_o)
    result = np.where(prev_bullish & curr_bearish & contained, 1, 0)
    result[0] = 0
    return result


def compute_candle_score(candle: Dict[str, Any], support: List[float], resistance: List[float]) -> int:
    """
    Computes a score for a single candle based on candlestick patterns and technical indicators.
    Positive scores favor a long (bullish) signal, while negative scores suggest bearish conditions.
    """
    score = 0
    patterns = candle.get("p", "")
    rsi = candle.get("rsi")
    ema = candle.get("ema")
    close = candle.get("c")
    bb_upper = candle.get("bb_upper")
    bb_lower = candle.get("bb_lower")
    adx = candle.get("adx")
    cci = candle.get("cci")
    tema = candle.get("tema")
    stoch_k = candle.get("stoch_k")
    stoch_d = candle.get("stoch_d")
    mfi = candle.get("mfi")
    atr = candle.get("atr")
    macd = candle.get("macd")
    macds = candle.get("macds")
    macdh = candle.get("macdh")
    volume = candle.get("v")

    # Pattern-based scoring.
    if "Bullish Engulfing" in patterns:
        score += 10
    if "Three White Soldiers" in patterns:
        score += 15
    if "Hammer" in patterns:
        score += 5
    if "Bullish Harami" in patterns:
        score += 5
    if "Bearish Engulfing" in patterns:
        score -= 10
    if "Three Black Crows" in patterns:
        score -= 15
    if "Hanging Man" in patterns:
        score -= 5
    if "Bearish Harami" in patterns:
        score -= 5

    # RSI-based scoring.
    if rsi is not None:
        if rsi < 30:
            score += 5
        elif rsi > 70:
            score -= 5

    # EMA comparison.
    if ema is not None and close is not None:
        if close > ema:
            score += 3
        elif close < ema:
            score -= 3

    # Bollinger Bands.
    if close is not None and bb_lower is not None and bb_upper is not None:
        if close < bb_lower * 1.005:
            score += 3
        elif close > bb_upper * 0.995:
            score -= 3

    # ADX: Trend strength reinforcement.
    if adx is not None and ema is not None and close is not None:
        if adx > 25:
            if close > ema:
                score += 2
            elif close < ema:
                score -= 2

    # CCI: Oversold or overbought conditions.
    if cci is not None:
        if cci < -100:
            score += 3
        elif cci > 100:
            score -= 3

    # TEMA comparison.
    if tema is not None and close is not None:
        if close > tema:
            score += 2
        elif close < tema:
            score -= 2

    # Stochastic oscillator.
    if stoch_k is not None:
        if stoch_k < 20:
            score += 2
        elif stoch_k > 80:
            score -= 2

    # Stochastic crossover
    if stoch_k is not None and stoch_d is not None:
        if stoch_k > stoch_d and stoch_k < 20 and stoch_d < 20:
            score += 2  # Bullish crossover in oversold territory
        elif stoch_k < stoch_d and stoch_k > 80 and stoch_d > 80:
            score -= 2  # Bearish crossover in overbought territory

    # MFI.
    if mfi is not None:
        if mfi < 20:
            score += 3
        elif mfi > 80:
            score -= 3

    # MACD signal
    if macd is not None and macds is not None and macdh is not None:
        if macd > macds and macdh > 0:
            score += 3  # Bullish MACD crossover
        elif macd < macds and macdh < 0:
            score -= 3  # Bearish MACD crossover

    # Divergence check (RSI)
    if rsi is not None and close is not None and "o" in candle and "l" in candle:
        open_price = candle["o"]
        low_price = candle["l"]
        if close > open_price and rsi < 30:  # Bullish price but oversold RSI
            score += 2
        elif close < open_price and rsi > 70:  # Bearish price but overbought RSI
            score -= 2

    # Support/Resistance Influence - with safety check for empty lists
    if close and atr:
        if support and close < support[0] + 0.5 * atr:
            score += 4  # Near Support
        elif resistance and close > resistance[0] - 0.5 * atr:
            score -= 4  # Near Resistance

    return score


def detect_support_resistance(levels: List[float]) -> Tuple[List[float], List[float]]:
    support: List[float] = []
    resistance: List[float] = []
    for i in range(2, len(levels) - 2):
        if levels[i] < levels[i - 1] and levels[i] < levels[i + 1]:
            support.append(levels[i])
        elif levels[i] > levels[i - 1] and levels[i] > levels[i + 1]:
            resistance.append(levels[i])
    return sorted(support), sorted(resistance, reverse=True)


# --- Main Endpoint ---
@router.get("/", response_model=Dict[str, Any])
async def get_ohlc_data(
    symbol: str = Query(..., description="The stock symbol, e.g. INFY"),
    interval: str = Query("1d", description="Time interval: 1m, 15m, or 1d"),
    start: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    forward_looking: Optional[int] = Query(
        None, description="If set to an integer (e.g. 1 or 2), the signal is confirmed using that many candles ahead. " "Default is None (using immediate previous candle confirmation)."
    ),
):
    # Save the original start date to trim the final output later
    original_start = start

    # Adjust start date to fetch extra historical data if needed
    adjusted_start = adjust_start_date_for_interval(start, "1m")  # Always use 1m for data fetching

    # Fetch only the 1-minute data from API
    url = f"https://iam.theaibull.com/v1/wg7ttpouv7/backtesting/ohlcd/{symbol}"
    params = {"interval": "1m"}  # Always request 1m data
    if adjusted_start:
        params["start"] = adjusted_start
    if end:
        params["end"] = end

    response = requests.get(url, params=params)
    if response.status_code != 200:
        raise HTTPException(status_code=response.status_code, detail="Error fetching OHLC data.")

    data_1m = response.json()  # Expecting a dict with timestamps as keys
    if not data_1m:
        raise HTTPException(status_code=404, detail="No OHLC data found.")

    # Create 5m and 15m data from 1m data
    data_5m = resample_ohlc(data_1m, 5)
    data_15m = resample_ohlc(data_1m, 15)

    # Process data for each timeframe
    result_1m = process_timeframe_data(data_1m, forward_looking, original_start)
    result_5m = process_timeframe_data(data_5m, forward_looking, original_start)
    result_15m = process_timeframe_data(data_15m, forward_looking, original_start)

    # Use the requested interval for the main response
    if interval == "5m":
        main_result = result_5m
    elif interval == "15m":
        main_result = result_15m
    else:
        main_result = result_1m  # Default to 1m

    # Return the structure with all timeframes
    return {"ohlc_data": main_result["ohlc_data"], "payoff": main_result["payoff"], "all_intervals": {"1m": result_1m, "5m": result_5m, "15m": result_15m}}


def resample_ohlc(data_1m: Dict[str, Any], minutes: int) -> Dict[str, Any]:
    """
    Resample 1-minute OHLC data to a higher timeframe (e.g., 5m or 15m).

    Args:
        data_1m: Dictionary with 1-minute OHLC data
        minutes: Number of minutes to aggregate (e.g., 5 for 5-minute bars)

    Returns:
        Dictionary with resampled OHLC data
    """
    if not data_1m:
        return {}

    # Sort timestamps
    sorted_keys = sorted(data_1m.keys(), key=lambda k: int(k))

    resampled_data = {}
    current_chunk = []

    for i, ts in enumerate(sorted_keys):
        candle = data_1m[ts]
        ts_int = int(ts)

        # Create timestamp for the start of the period
        # For 5m: 00:00, 00:05, 00:10, etc.
        # For 15m: 00:00, 00:15, 00:30, etc.
        dt = datetime.fromtimestamp(ts_int)
        period_start = dt.replace(minute=(dt.minute // minutes) * minutes, second=0, microsecond=0)
        period_start_ts = int(period_start.timestamp())

        # Use the period start timestamp as the key
        if str(period_start_ts) not in resampled_data:
            resampled_data[str(period_start_ts)] = {
                "o": candle["o"],  # First candle's open
                "h": candle["h"],  # Start with this candle's high
                "l": candle["l"],  # Start with this candle's low
                "c": candle["c"],  # Will be updated as we process more candles
                "v": candle.get("v", 0),  # Start summing volume
            }
        else:
            # Update high and low
            resampled_data[str(period_start_ts)]["h"] = max(resampled_data[str(period_start_ts)]["h"], candle["h"])
            resampled_data[str(period_start_ts)]["l"] = min(resampled_data[str(period_start_ts)]["l"], candle["l"])
            # Update close with the latest candle
            resampled_data[str(period_start_ts)]["c"] = candle["c"]
            # Sum volume if available
            if "v" in candle:
                resampled_data[str(period_start_ts)]["v"] += candle.get("v", 0)

    return resampled_data


def process_timeframe_data(data: Dict[str, Any], forward_looking: Optional[int], original_start: Optional[str]) -> Dict[str, Any]:
    """
    Process OHLC data for a specific timeframe.
    This function handles all the analysis that was previously in process_interval_data.
    """
    if not data:
        return {"ohlc_data": {}, "payoff": {"trades": [], "summary": {}}}

    # Sort timestamps
    sorted_keys = sorted(data.keys(), key=lambda k: int(k))

    try:
        opens = np.array([data[k]["o"] for k in sorted_keys], dtype=np.float64)
        highs = np.array([data[k]["h"] for k in sorted_keys], dtype=np.float64)
        lows = np.array([data[k]["l"] for k in sorted_keys], dtype=np.float64)
        closes = np.array([data[k]["c"] for k in sorted_keys], dtype=np.float64)
        volumes = None
        if "v" in data[sorted_keys[0]]:
            volumes = np.array([data[k].get("v", 0) for k in sorted_keys], dtype=np.float64)
    except Exception as e:
        print(f"Error processing OHLC data: {e}")
        return {"ohlc_data": {}, "payoff": {"trades": [], "summary": {}}}

    support, resistance = detect_support_resistance(closes)

    # Process candlestick patterns and indicators
    # Define candlestick pattern functions.
    pattern_functions = {
        "Marubozu": lambda o, h, l, c: talib.CDLMARUBOZU(o, h, l, c),
        "Hammer": lambda o, h, l, c: talib.CDLHAMMER(o, h, l, c),
        "Inverted Hammer": lambda o, h, l, c: talib.CDLINVERTEDHAMMER(o, h, l, c),
        "Bullish Engulfing": lambda o, h, l, c: np.where(talib.CDLENGULFING(o, h, l, c) > 0, 100, 0),
        "Piercing Pattern": lambda o, h, l, c: talib.CDLPIERCING(o, h, l, c),
        "Morning Star": lambda o, h, l, c: talib.CDLMORNINGSTAR(o, h, l, c),
        "Three White Soldiers": lambda o, h, l, c: talib.CDL3WHITESOLDIERS(o, h, l, c),
        "Bullish Harami": lambda o, h, l, c: bullish_harami(o, h, l, c),
        "Shooting Star": lambda o, h, l, c: talib.CDLSHOOTINGSTAR(o, h, l, c),
        "Hanging Man": lambda o, h, l, c: talib.CDLHANGINGMAN(o, h, l, c),
        "Bearish Engulfing": lambda o, h, l, c: np.where(talib.CDLENGULFING(o, h, l, c) < 0, -100, 0),
        "Dark Cloud Cover": lambda o, h, l, c: talib.CDLDARKCLOUDCOVER(o, h, l, c),
        "Evening Star": lambda o, h, l, c: talib.CDLEVENINGSTAR(o, h, l, c),
        "Three Black Crows": lambda o, h, l, c: talib.CDL3BLACKCROWS(o, h, l, c),
        "Bearish Harami": lambda o, h, l, c: bearish_harami(o, h, l, c),
        "Doji": lambda o, h, l, c: talib.CDLDOJI(o, h, l, c),
        "Dragonfly Doji": lambda o, h, l, c: talib.CDLDRAGONFLYDOJI(o, h, l, c),
        "Gravestone Doji": lambda o, h, l, c: talib.CDLGRAVESTONEDOJI(o, h, l, c),
        "Long-legged Doji": lambda o, h, l, c: talib.CDLLONGLEGGEDDOJI(o, h, l, c),
        "Spinning Top": lambda o, h, l, c: talib.CDLSPINNINGTOP(o, h, l, c),
        "High Wave Candle": lambda o, h, l, c: talib.CDLHIGHWAVE(o, h, l, c),
        "Two Crows": lambda o, h, l, c: talib.CDL2CROWS(o, h, l, c),
        "Three Inside Up/Down": lambda o, h, l, c: talib.CDL3INSIDE(o, h, l, c),
        "Three-Line Strike": lambda o, h, l, c: talib.CDL3LINESTRIKE(o, h, l, c),
        "Three Outside": lambda o, h, l, c: talib.CDL3OUTSIDE(o, h, l, c),
        "Three Stars In South": lambda o, h, l, c: talib.CDL3STARSINSOUTH(o, h, l, c),
        "Abandoned Baby": lambda o, h, l, c: talib.CDLABANDONEDBABY(o, h, l, c),
        "Advance Block": lambda o, h, l, c: talib.CDLADVANCEBLOCK(o, h, l, c),
        "Belt Hold": lambda o, h, l, c: talib.CDLBELTHOLD(o, h, l, c),
        "Breakaway": lambda o, h, l, c: talib.CDLBREAKAWAY(o, h, l, c),
        "Closing Marubozu": lambda o, h, l, c: talib.CDLCLOSINGMARUBOZU(o, h, l, c),
        "Concealing Baby Swallow": lambda o, h, l, c: talib.CDLCONCEALBABYSWALL(o, h, l, c),
        "Counterattack": lambda o, h, l, c: talib.CDLCOUNTERATTACK(o, h, l, c),
        "Doji Star": lambda o, h, l, c: talib.CDLDOJISTAR(o, h, l, c),
        "Evening Doji Star": lambda o, h, l, c: talib.CDLEVENINGDOJISTAR(o, h, l, c),
        "Gap Side-by-Side White": lambda o, h, l, c: talib.CDLGAPSIDESIDEWHITE(o, h, l, c),
        "Haramicross": lambda o, h, l, c: talib.CDLHARAMICROSS(o, h, l, c),
        "Hikkake": lambda o, h, l, c: talib.CDLHIKKAKE(o, h, l, c),
        "Modified Hikkake": lambda o, h, l, c: talib.CDLHIKKAKEMOD(o, h, l, c),
        "Homing Pigeon": lambda o, h, l, c: talib.CDLHOMINGPIGEON(o, h, l, c),
        "Identical Three Crows": lambda o, h, l, c: talib.CDLIDENTICAL3CROWS(o, h, l, c),
        "In-Neck": lambda o, h, l, c: talib.CDLINNECK(o, h, l, c),
        "Kicking": lambda o, h, l, c: talib.CDLKICKING(o, h, l, c),
        "Kicking - Short": lambda o, h, l, c: talib.CDLKICKINGBYLENGTH(o, h, l, c),
        "Ladder Bottom": lambda o, h, l, c: talib.CDLLADDERBOTTOM(o, h, l, c),
        "Long Line": lambda o, h, l, c: talib.CDLLONGLINE(o, h, l, c),
        "Matching Low": lambda o, h, l, c: talib.CDLMATCHINGLOW(o, h, l, c),
        "Mat Hold": lambda o, h, l, c: talib.CDLMATHOLD(o, h, l, c),
        "On-Neck": lambda o, h, l, c: talib.CDLONNECK(o, h, l, c),
        "Falling Three Methods": lambda o, h, l, c: talib.CDLRISEFALL3METHODS(o, h, l, c),
        "Separating Lines": lambda o, h, l, c: talib.CDLSEPARATINGLINES(o, h, l, c),
        "Short Line": lambda o, h, l, c: talib.CDLSHORTLINE(o, h, l, c),
        "Stalled Pattern": lambda o, h, l, c: talib.CDLSTALLEDPATTERN(o, h, l, c),
        "Stick Sandwich": lambda o, h, l, c: talib.CDLSTICKSANDWICH(o, h, l, c),
        "Takuri": lambda o, h, l, c: talib.CDLTAKURI(o, h, l, c),
        "Tasuki Gap": lambda o, h, l, c: talib.CDLTASUKIGAP(o, h, l, c),
        "Thrusting": lambda o, h, l, c: talib.CDLTHRUSTING(o, h, l, c),
        "Tristar": lambda o, h, l, c: talib.CDLTRISTAR(o, h, l, c),
        "Unique 3 River": lambda o, h, l, c: talib.CDLUNIQUE3RIVER(o, h, l, c),
        "Upside Gap Two Crows": lambda o, h, l, c: talib.CDLUPSIDEGAP2CROWS(o, h, l, c),
        "X-Side Gap Three Methods": lambda o, h, l, c: talib.CDLXSIDEGAP3METHODS(o, h, l, c),
    }

    pattern_results = {}
    for pattern_name, func in pattern_functions.items():
        try:
            pattern_array = func(opens, highs, lows, closes)
            pattern_results[pattern_name] = pattern_array
        except Exception as e:
            pattern_results[pattern_name] = np.zeros_like(opens)
            print(f"Error computing pattern {pattern_name}: {e}")

    # Aggregate pattern names for each timestamp.
    patterns_by_timestamp = {}
    for idx, ts in enumerate(sorted_keys):
        triggered = []
        for pattern_name, arr in pattern_results.items():
            if arr[idx] != 0:
                triggered.append(pattern_name)
        patterns_by_timestamp[ts] = ", ".join(triggered) if triggered else ""
    for ts in sorted_keys:
        data[ts]["p"] = patterns_by_timestamp[ts]

    # Compute technical indicators.
    try:
        rsi = talib.RSI(closes, timeperiod=14)
        ema = talib.EMA(closes, timeperiod=9)
        sma = talib.SMA(closes, timeperiod=14)
        macd, macdsignal, macdhist = talib.MACD(closes, fastperiod=12, slowperiod=26, signalperiod=9)
        upper, middle, lower = talib.BBANDS(closes, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
        atr = talib.ATR(highs, lows, closes, timeperiod=14)
        adx = talib.ADX(highs, lows, closes, timeperiod=14)
        cci = talib.CCI(highs, lows, closes, timeperiod=14)
        tema = talib.TEMA(closes, timeperiod=30)
        slowk, slowd = talib.STOCH(
            highs,
            lows,
            closes,
            fastk_period=14,
            slowk_period=3,
            slowk_matype=0,
            slowd_period=3,
            slowd_matype=0,
        )
        if volumes is not None:
            mfi = talib.MFI(highs, lows, closes, volumes, timeperiod=14)
        else:
            mfi = np.array([None] * len(closes))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error computing technical indicators: {e}")

    # VWAP: If volume data is available.
    if volumes is not None and np.sum(volumes) != 0:
        cum_vol = np.cumsum(volumes)
        cum_vol_price = np.cumsum(closes * volumes)
        vwap = (cum_vol_price / cum_vol).tolist()
    else:
        vwap = ["N/A"] * len(closes)

    # Moving average crossover signals using a short EMA (9) and a long EMA (21).
    short_ema = talib.EMA(closes, timeperiod=9)
    long_ema = talib.EMA(closes, timeperiod=21)
    signals = np.array([""] * len(closes))
    for i in range(1, len(closes)):
        if short_ema[i - 1] <= long_ema[i - 1] and short_ema[i] > long_ema[i]:
            signals[i] = "Golden Cross"
        elif short_ema[i - 1] >= long_ema[i - 1] and short_ema[i] < long_ema[i]:
            signals[i] = "Death Cross"

    # Attach indicators and compute the score for each candle record.
    for idx, ts in enumerate(sorted_keys):
        data[ts]["rsi"] = round(float(rsi[idx]), 2) if not np.isnan(rsi[idx]) else None
        data[ts]["ema"] = round(float(ema[idx]), 2) if not np.isnan(ema[idx]) else None
        data[ts]["sma"] = round(float(sma[idx]), 2) if not np.isnan(sma[idx]) else None
        data[ts]["macd"] = round(float(macd[idx]), 2) if not np.isnan(macd[idx]) else None
        data[ts]["macds"] = round(float(macdsignal[idx]), 2) if not np.isnan(macdsignal[idx]) else None
        data[ts]["macdh"] = round(float(macdhist[idx]), 2) if not np.isnan(macdhist[idx]) else None

        data[ts]["bb_upper"] = round(float(upper[idx]), 2) if not np.isnan(upper[idx]) else None
        data[ts]["bb_middle"] = round(float(middle[idx]), 2) if not np.isnan(middle[idx]) else None
        data[ts]["bb_lower"] = round(float(lower[idx]), 2) if not np.isnan(lower[idx]) else None

        data[ts]["atr"] = round(float(atr[idx]), 2) if not np.isnan(atr[idx]) else None
        data[ts]["adx"] = round(float(adx[idx]), 2) if not np.isnan(adx[idx]) else None
        data[ts]["cci"] = round(float(cci[idx]), 2) if not np.isnan(cci[idx]) else None
        data[ts]["tema"] = round(float(tema[idx]), 2) if not np.isnan(tema[idx]) else None
        data[ts]["stoch_k"] = round(float(slowk[idx]), 2) if not np.isnan(slowk[idx]) else None
        data[ts]["stoch_d"] = round(float(slowd[idx]), 2) if not np.isnan(slowd[idx]) else None
        data[ts]["mfi"] = round(float(mfi[idx]), 2) if volumes is not None and mfi[idx] is not None and not np.isnan(mfi[idx]) else None

        data[ts]["vwap"] = round(vwap[idx], 2) if vwap[idx] != "N/A" else "N/A"
        data[ts]["s"] = signals[idx]
        data[ts]["score"] = compute_candle_score(data[ts], support, resistance)

    # Add trade signal confirmation.
    bullish_threshold = 18  # Changed from 5 to 10 for stronger confirmation
    bearish_threshold = -18  # Changed from -5 to -10 for stronger confirmation
    for ts in sorted_keys:
        data[ts]["trade_signal"] = ""

    # Add a new field to store the actual score that triggered the signal
    for ts in sorted_keys:
        data[ts]["signal_score"] = None

    if forward_looking is None:
        # Use the previous candle for real-time confirmation.
        for i in range(1, len(sorted_keys)):
            prev_ts = sorted_keys[i - 1]
            curr_ts = sorted_keys[i]
            prev_score = data[prev_ts]["score"]
            prev_close = float(data[prev_ts]["c"])
            curr_close = float(data[curr_ts]["c"])
            # Check for bullish confirmation: previous candle was bullish and price increased.
            if prev_score >= bullish_threshold and curr_close > prev_close:
                data[curr_ts]["trade_signal"] = "Long"
                data[curr_ts]["signal_score"] = prev_score  # Store the score that triggered the signal
            # Check for bearish confirmation: previous candle was bearish and price decreased.
            elif prev_score <= bearish_threshold and curr_close < prev_close:
                data[curr_ts]["trade_signal"] = "Short"
                data[curr_ts]["signal_score"] = prev_score  # Store the score that triggered the signal
    else:
        # Forward-looking confirmation using the candle 'forward_looking' ahead.
        for i in range(0, len(sorted_keys) - forward_looking):
            current_ts = sorted_keys[i]
            confirm_ts = sorted_keys[i + forward_looking]
            current_score = data[current_ts]["score"]
            current_close = float(data[current_ts]["c"])
            confirm_close = float(data[confirm_ts]["c"])
            if current_score >= bullish_threshold and confirm_close > current_close:
                data[confirm_ts]["trade_signal"] = "Long"
                data[confirm_ts]["signal_score"] = current_score  # Store triggering score
            elif current_score <= bearish_threshold and confirm_close < current_close:
                data[confirm_ts]["trade_signal"] = "Short"
                data[confirm_ts]["signal_score"] = current_score  # Store triggering score

    # --- New: Add recommended target and stop loss for trade signals ---
    for ts in sorted_keys:
        trade = data[ts]
        signal = trade.get("trade_signal", "")
        if signal in ["Long", "Short"] and trade.get("atr") is not None and trade.get("c") is not None:
            entry_price = float(trade["c"])
            atr_value = float(trade["atr"])
            if signal == "Long":
                trade["target"] = round(entry_price + 2 * atr_value, 2)
                trade["stop_loss"] = round(entry_price - atr_value, 2)
            elif signal == "Short":
                trade["target"] = round(entry_price - 2 * atr_value, 2)
                trade["stop_loss"] = round(entry_price + atr_value, 2)

    # Trim extra data: If an original start date was provided, remove candles before that date.
    if original_start:
        try:
            original_start_dt = datetime.strptime(original_start, "%Y-%m-%d")
            original_start_ts = int(original_start_dt.timestamp())
            trimmed_data = {ts: record for ts, record in data.items() if int(ts) >= original_start_ts}
            data = trimmed_data
        except Exception as e:
            print(f"Error trimming data based on original start date: {e}")

    # --- New: P&L Calculation Function ---
    payoff = calculate_payoff(data)
    return {"ohlc_data": data, "payoff": payoff}


def calculate_payoff(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Looks at the trade signals in the data and simulates the trade outcomes.
    For each trade (Long or Short), it scans subsequent candles to determine if the
    recommended target or stop loss was hit first.

    Additionally, it computes:
      - Profit Points: For Long trades, exit price minus entry price;
                        for Short trades, entry price minus exit price.
      - Duration: The time (in seconds) elapsed from the entry candle to the exit candle.
    If no exit event is found, the trade is assumed to close at the final available candle,
    and the outcome remains "Not Closed".

    Each trade record includes the candle’s signal "Score" (from the entry candle).

    Returns a dictionary containing:
      - 'trades': a list of trade records with 'Time', 'Signal', 'Score', 'ProfitPoints', 'Duration', and 'Status'
      - 'summary': a mapping of entry date (YYYY-MM-DD) to the success percentage
                   (Booked Profit trades / total closed trades * 100).
    """
    sorted_keys = sorted(data.keys(), key=lambda k: int(k))
    trades = []

    # Simulate each trade signal.
    for i, ts in enumerate(sorted_keys):
        record = data[ts]
        signal = record.get("trade_signal", "")
        if signal not in ["Long", "Short"]:
            continue

        entry_time = ts
        entry_price = float(record["c"])
        target = record.get("target")
        stop_loss = record.get("stop_loss")
        if target is None or stop_loss is None:
            continue

        outcome = "Not Closed"
        exit_time = None
        exit_price = None

        # Iterate over subsequent candles to simulate trade exit.
        for j in range(i + 1, len(sorted_keys)):
            future_ts = sorted_keys[j]
            future_record = data[future_ts]
            high = float(future_record["h"])
            low = float(future_record["l"])

            if signal == "Long":
                if low <= stop_loss:
                    outcome = "Stop Loss Hit"
                    exit_time = future_ts
                    exit_price = stop_loss  # assume exit at stop loss price
                    break
                if high >= target:
                    outcome = "Booked Profit"
                    exit_time = future_ts
                    exit_price = target  # assume exit at target price
                    break
            elif signal == "Short":
                if high >= stop_loss:
                    outcome = "Stop Loss Hit"
                    exit_time = future_ts
                    exit_price = stop_loss
                    break
                if low <= target:
                    outcome = "Booked Profit"
                    exit_time = future_ts
                    exit_price = target
                    break

        # If no exit event was found, assume trade closes at the last available candle.
        if exit_time is None:
            exit_time = sorted_keys[-1]
            exit_price = float(data[exit_time]["c"])

        # Compute trade duration and profit points.
        entry_ts = int(entry_time)
        exit_ts = int(exit_time)
        duration = exit_ts - entry_ts
        if signal == "Long":
            profit_points = exit_price - entry_price
        else:  # For Short trades.
            profit_points = entry_price - exit_price

        trades.append({"Time": entry_time, "Signal": signal, "Score": record.get("signal_score", record.get("score")), "ProfitPoints": profit_points, "Duration": duration, "Status": outcome})

    # Aggregate success percentage by entry date.
    from datetime import datetime

    trades_by_date = {}
    for trade in trades:
        ts = trade["Time"]
        dt = datetime.fromtimestamp(int(ts))
        date_str = dt.strftime("%Y-%m-%d")
        trades_by_date.setdefault(date_str, []).append(trade)

    summary = {}
    for date_str, trades_list in trades_by_date.items():
        # Only consider trades that have been closed via target or stop loss for success calculation.
        closed_trades = [t for t in trades_list if t["Status"] in ["Booked Profit", "Stop Loss Hit"]]
        if closed_trades:
            success_count = sum(1 for t in closed_trades if t["Status"] == "Booked Profit")
            percentage = (success_count / len(closed_trades)) * 100
        else:
            percentage = None
        summary[date_str] = percentage

    return {"trades": trades, "summary": summary}
