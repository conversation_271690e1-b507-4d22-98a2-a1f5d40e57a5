import json
import urllib.parse
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Body, <PERSON>ie, HTTPException
from app.util.apps import is_app_authenticated
from pydantic import BaseModel, Field

from app.routers.auth import get_current_user_data  # type: ignore
from app.util.instruments import (
    calculate_dhan_margin,
    calculate_upstox_margin,
    calculate_zerodha_margin,
    calculate_angelone_margin,
    calculate_icici_direct_margin,
)

router = APIRouter()


class Leg(BaseModel):
    """Model for a trading leg"""

    symbol: str
    type: str = Field(..., description="Type of leg: CE, PE, or FUT")
    tr_type: str = Field(..., description="Transaction type: b for buy, s for sell")
    lots: int
    strike: Optional[float] = None
    expiryDate: Optional[str] = None
    entry_price: Optional[float] = None
    op_pr: Optional[float] = None
    lot_size: int = 1
    iv: Optional[float] = None


class MarginRequest(BaseModel):
    """Request model for margin computation"""

    legs_json: List[Leg]
    multiplier: int = 1


class LegMargin(BaseModel):
    """Model for margin details of a single leg"""

    securityId: str
    total_margin: float
    span_margin: float
    exposure_margin: float
    additional_margin: Optional[float] = None
    net_buy_premium: Optional[float] = None
    option_premium: Optional[float] = None
    additional: Optional[float] = None
    available_balance: Optional[float] = None
    variable_margin: Optional[float] = None
    insufficient_balance: Optional[float] = None
    brokerage: Optional[float] = None
    leverage: Optional[str] = None


class MarginDetails(BaseModel):
    """Model for margin calculation details"""

    total: float
    legs: List[LegMargin]


class MarginResult(BaseModel):
    """Model for margin calculation result from a single broker"""

    app_name: str
    display_name: str
    margin: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class BrokerMarginResult(BaseModel):
    """Detailed model for margin calculation result from a specific broker"""

    app_name: str
    margin: Optional[MarginDetails] = None
    error: Optional[str] = None


class MarginResponse(BaseModel):
    """Response model for margin computation"""

    margins: List[MarginResult]


@router.post("/compute", response_model=MarginResponse)
async def compute_margin(data: MarginRequest = Body(...), access_token: Optional[str] = Cookie(None), guestUserData: Optional[str] = Cookie(None)) -> MarginResponse:
    """Compute margin requirements for the given legs across different brokers"""
    # Get user data
    if access_token:
        user_data: Dict[str, Any] = get_current_user_data(access_token)
    else:
        user_data = {}
        if guestUserData:
            try:
                decoded_string = urllib.parse.unquote(guestUserData)
                user_data = json.loads(decoded_string)
            except json.JSONDecodeError:
                user_data = {}

    margin_results: List[MarginResult] = []

    # Process apps
    for app in user_data.get("apps", []):
        if app.get("category") == "Stocks":
            app_name = app.get("name", "").lower()
            profile_name = app.get("fields", {}).get("profile", {}).get("name")
            display_name = app.get("name", "").lower() + " (" + (profile_name if profile_name else app.get("uuid", "")) + ")"

            if not await is_app_authenticated(app):
                margin_results.append(MarginResult(app_name=app_name, display_name=display_name, error="Reauthorization required"))
                continue

            # Convert Pydantic models to dictionaries for compatibility
            legs_dict = [leg.model_dump() for leg in data.legs_json]
            multiplier = data.multiplier

            if app_name == "dhan":
                result = await calculate_dhan_margin(app, legs_dict, multiplier)
                margin_results.append(MarginResult(**result, display_name=display_name))
            elif app_name == "upstox" or app_name == "upstoxapi":
                result = await calculate_upstox_margin(app, legs_dict, multiplier)
                margin_results.append(MarginResult(**result, display_name=display_name))
            elif app_name == "zerodha" or app_name == "zerodhaapi":
                result = await calculate_zerodha_margin(app, legs_dict, multiplier)
                margin_results.append(MarginResult(**result, display_name=display_name))
            elif app_name == "angelone" or app_name == "angeloneapi":
                result = await calculate_angelone_margin(app, legs_dict, multiplier)
                margin_results.append(MarginResult(**result, display_name=display_name))
            elif app_name == "icici-direct":
                result = await calculate_icici_direct_margin(app, legs_dict, multiplier)
                margin_results.append(MarginResult(**result, display_name=display_name))

    return MarginResponse(margins=margin_results)


@router.post("/calculate-margin-for-broker", response_model=BrokerMarginResult)
async def calculate_margin_for_broker_route(
    broker: str,
    legs: List[Dict[str, Any]] = Body(...),
    multiplier: int = 1,
    access_token: Optional[str] = Cookie(None),
) -> Dict[str, Any]:
    """Calculate margin requirements for a specific broker

    Args:
        broker: Broker name (upstox, zerodha, dhan, icici-direct)
        app: App configuration with auth fields
        legs: List of leg objects for the strategy

    Returns:
        Margin calculation results with the following structure:
        - app_name: Name of the broker
        - margin: Margin details including total margin and per-leg margins
        - error: Error message if calculation failed
    """
    if access_token:
        user_data: Dict[str, Any] = get_current_user_data(access_token)
    else:
        raise HTTPException(status_code=401, detail="Unauthorized")
    app = next((app for app in user_data.get("apps", []) if app.get("key", "") == broker), None)
    if not app:
        raise HTTPException(status_code=401, detail="Unauthorized")
    if broker == "dhan":
        return await calculate_dhan_margin(app, legs, multiplier)
    elif broker == "upstox" or broker == "upstoxapi":
        return await calculate_upstox_margin(app, legs, multiplier)
    elif broker == "zerodha" or broker == "zerodhaapi":
        return await calculate_zerodha_margin(app, legs, multiplier)
    elif broker == "angelone" or broker == "angeloneapi":
        return await calculate_angelone_margin(app, legs, multiplier)
    elif broker == "icici-direct":
        return await calculate_icici_direct_margin(app, legs, multiplier)
    else:
        return {"app_name": broker, "error": f"Unsupported broker: {broker}"}
