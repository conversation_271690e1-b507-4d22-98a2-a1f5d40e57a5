from fastapi import APIRouter, Request, <PERSON><PERSON>
from app.util.templates import templates
from app.data.quiz import quiz_data
from app.routers.site import get_user_data, menu
import random
from typing import Optional

router = APIRouter()

@router.get("")
async def get_quiz(request: Request, access_token: str = <PERSON><PERSON>(None)):
    # Pass the quiz_data to the template so it's available on initial page load
        # Extract just the topics information without the questions
    topics = []
    for topic in quiz_data["topics"]:
        topics.append({
            "id": topic["id"],
            "title": topic["title"],
            "description": topic["description"]
        })
    return templates.TemplateResponse(
        "games/quiz.html",
        {
            "request": request,
            **menu,
            **get_user_data(access_token),
            "topics": topics
        }
    )


@router.get("/questions")
async def get_questions(topic_id: str, limit: Optional[int] = 5):
    """Get questions for a specific topic"""
    # Find the topic
    topic_data = None
    for topic in quiz_data["topics"]:
        if topic["id"] == topic_id:
            topic_data = topic
            break

    if not topic_data:
        return {"error": "Topic not found"}

    # Get all questions for the topic
    questions = topic_data["questions"]

    # If limit is specified and less than the total number of questions,
    # return a random subset of questions
    if limit and limit < len(questions):
        questions = random.sample(questions, limit)

    return {
        "topic": {
            "id": topic_data["id"],
            "title": topic_data["title"],
            "description": topic_data["description"]
        },
        "questions": questions
    }