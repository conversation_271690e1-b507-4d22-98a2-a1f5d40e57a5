from cachetools import T<PERSON><PERSON><PERSON>, cached
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional, List

from app.util.db.redis import get_cache
from app.util.settings import app_settings
from app.util.stocks import get_all_stocks, make_api_request, get_stock_details, get_most_active

router = APIRouter()


####### SHOULD BE DEPRECATED #######
@router.get("/symbol/price/{symbol}")
async def get_symbol_price(symbol: str):
    """Get price information for a given symbol"""
    try:
        return make_api_request(f"{app_settings.AIBULL_SYMBOL_PRICE_URL}/{symbol}")
    except Exception:
        raise HTTPException(status_code=500, detail="Error fetching price data")


@router.get("/symbol/options/{symbol}")
async def get_symbol_options(symbol: str):
    """Get options chain data for a given symbol"""
    try:
        return make_api_request(f"{app_settings.AIBULL_SYMBOL_OPTIONS_URL}/{symbol}")
    except Exception:
        raise HTTPException(status_code=500, detail="Error fetching options data")


@router.get("/futures/{symbol}")
async def get_futures(symbol: str):
    """Get futures data from AIBull endpoint"""
    try:
        return make_api_request(f"{app_settings.AIBULL_FUTURES_URL}/{symbol}")
    except Exception:
        raise HTTPException(status_code=500, detail="Error fetching futures data")


@router.get("/search/find")
async def search_symbols(query: str):
    """Search for symbols"""
    try:
        return make_api_request(f"{app_settings.AIBULL_SEARCH_URL}?query={query}")
    except Exception:
        raise HTTPException(status_code=500, detail="Error searching symbols")


@router.get("/indices/options/{query}")
async def get_indices_options(query: str):
    """Get options chain data for all indices"""
    try:
        return make_api_request(f"{app_settings.AIBULL_INDICES_OPTIONS_URL}/{query}")
    except Exception:
        raise HTTPException(status_code=500, detail="Error fetching indices options data")


# Create a separate non-async function for caching
@cached(TTLCache(maxsize=1024, ttl=3600))  # Cache data for 1 hour
def get_ticker_data_cached() -> List[Dict[str, Any]]:
    """
    Internal function to fetch and process ticker data with caching.
    Data is cached for 1 hour to balance between data freshness and API load.
    """
    items = get_most_active()
    ticker_list = []

    for data in items:
        # Extract required fields
        symbol_val: Optional[str] = data.get("symbol")
        last_price: Optional[float] = data.get("lastPrice")
        p_change: Optional[float] = data.get("pChange")
        dhan_instrument_id: Optional[str] = get_cache("dhan_instruments_nse", symbol_val)

        # Only append if we have all required data
        if symbol_val and last_price is not None and p_change is not None:
            ticker_list.append({"symbol": symbol_val, "lastPrice": last_price, "pChange": p_change, "dhan_instrument_id": dhan_instrument_id})

    return ticker_list


@router.get("/ticker")
async def get_ticker_data() -> JSONResponse:
    """
    Fetches stock ticker information (symbol, price, change) for multiple stocks,

    Returns:
        JSONResponse: List of stock tickers with their prices and changes
    """
    ticker_list = get_ticker_data_cached()
    return JSONResponse(content=ticker_list)


# Fetch the stock details for a given symbol
@router.get("/stock/{symbol}")
async def get_stock_details_route(symbol: str):
    """Get detailed information for a specific stock symbol"""
    try:
        response = get_stock_details(symbol)
        info = response.get("info", {})

        # Extract only the fields used in the frontend
        filtered_info = {
            "longName": info.get("longName", ""),
            "shortName": info.get("shortName", ""),
            "symbol": info.get("symbol", ""),
            "industryDisp": info.get("industryDisp", ""),
            "regularMarketPrice": info.get("regularMarketPrice", ""),
            "regularMarketChangePercent": info.get("regularMarketChangePercent", ""),
            "regularMarketChange": info.get("regularMarketChange", ""),
            "dayLow": info.get("dayLow", ""),
            "dayHigh": info.get("dayHigh", ""),
            "volume": info.get("volume", ""),
            "marketCap": info.get("marketCap", ""),
            "trailingPE": info.get("trailingPE", ""),
            "bookValue": info.get("bookValue", ""),
            "beta": info.get("beta", ""),
            "epsTrailingTwelveMonths": info.get("epsTrailingTwelveMonths", ""),
            "longBusinessSummary": info.get("longBusinessSummary", ""),
            "address1": info.get("address1", ""),
            "address2": info.get("address2", ""),
            "city": info.get("city", ""),
            "zip": info.get("zip", ""),
            "phone": info.get("phone", ""),
            "website": info.get("website", ""),
        }

        return {"info": filtered_info}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching stock details: {str(e)}")
