from asyncio import CancelledError
from contextlib import asynccontextmanager
from datetime import datetime

from apscheduler.schedulers.asyncio import AsyncIOScheduler  # type: ignore
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

from app.routers import (
    ai_agent,
    algo_backtest,
    algo_backtest_intraday,
    api,
    apps,
    blogs,
    calculators,
    demo,
    dhan,
    margins,
    mutual_funds,
    ohlc1,
    ohlc2,
    opstrat,
    portfolio,
    quiz,
    resources,
    screener,
    share_portfolio,
    site,
    sitemap,
    static,
)
from app.routers.errors import register_error_handlers
from app.routers.ohlc.main import router as ohlc_router
from app.routers.screener_api import screen
from app.routers.technical_screeners import router as technical_screeners_router
from app.util.middleware import USMarketMiddleware
from app.util.stocks import refresh


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Create an instance of the scheduler which calls the refresh function every 5 minutes.
    scheduler = AsyncIOScheduler()
    # Schedule the refresh task to run immediately and then every 5 minutes.
    scheduler.add_job(refresh, "interval", minutes=5, next_run_time=datetime.now())  # type: ignore

    scheduler.start()
    yield

    try:
        scheduler.shutdown()
        print("Scheduler shut down...")
    except CancelledError:
        pass


app = FastAPI(debug=True, lifespan=lifespan)

app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"])

# Add the US Market middleware
app.add_middleware(USMarketMiddleware)

app.mount("/static", StaticFiles(directory="static"), name="static")

# Register custom error handlers
register_error_handlers(app)


# Include Routers
app.include_router(sitemap.router)
app.include_router(static.router)
app.include_router(site.router)
app.include_router(calculators.router)
app.include_router(api.router, prefix="/api")
app.include_router(portfolio.router, prefix="/portfolio")
app.include_router(share_portfolio.router, prefix="/portfolio")
app.include_router(opstrat.router, prefix="/opstrat")
app.include_router(ai_agent.router, prefix="/ai-agent")
app.include_router(screener.router, prefix="/screener", tags=["Screener"])
app.include_router(mutual_funds.router, prefix="/mutual-funds")
app.include_router(screen.router, prefix="/api/screener", tags=["Screener API"])
app.include_router(algo_backtest.router, prefix="/algo-backtest")
app.include_router(algo_backtest_intraday.router, prefix="/algo-backtest-intraday")
app.include_router(ohlc1.router, prefix="/ohlc")
app.include_router(ohlc2.router, prefix="/ohlc2")
app.include_router(margins.router, prefix="/margins")
app.include_router(ohlc_router, prefix="/ohlc3")
app.include_router(apps.router, prefix="/apps")
app.include_router(quiz.router, prefix="/quiz")
app.include_router(blogs.router, prefix="/blogs")
app.include_router(demo.router, prefix="/demo")
app.include_router(resources.router, prefix="/resources")
app.include_router(technical_screeners_router, prefix="/technical-screeners", tags=["Technical Screeners"])
app.include_router(dhan.router, prefix="/data")
