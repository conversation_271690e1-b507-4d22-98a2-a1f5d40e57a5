[tool.poetry]
name = "ai-bull"
version = "0.1.0"
description = ""
authors = ["chapalamadugu <<EMAIL>>"]
readme = "README.md"
packages = [{ include = "app" }]
include = ["static/**"]


[tool.poetry.dependencies]
python = "^3.10,<3.13"
fastapi = "^0.111.0"
uvicorn = "^0.30.0"
python-jose = "^3.3.0"
pymongo = "^4.7.2"
cryptography = "^42.0.7"
httpx = "^0.27.0"
tiktoken = "^0.7.0"
jinja2 = "^3.1.4"
pydantic = "^2.9.2"
pydantic-settings = "^2.6.0"
cachetools = "^5.5.1"
redis = "^5.2.1"
opstrat = "^0.1.7"
scipy = "^1.15.2"
numpy = "^2.2.3"
apscheduler = "^3.11.0"
py-vollib = "^1.0.1"
websockets = "13.1"
websocket-client = "^1.8.0"
ta-lib = "^0.6.4"
async-lru = "^2.0.5"
m-patternpy = "^2.0.1"
diskcache = "^5.6.3"
markdown = "^3.8"
dhanhq = "^2.0.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
