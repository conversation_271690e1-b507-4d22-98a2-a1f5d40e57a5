{"scripts": {"tw-prod-build": "sh -c 'OS=$(uname -s | tr \"[:upper:]\" \"[:lower:]\"); ARCH=$(uname -m); if [ \"$ARCH\" = \"x86_64\" ] || [ \"$ARCH\" = \"amd\" ]; then ARCH=\"amd64\"; elif [ \"$ARCH\" = \"aarch64\" ] || [ \"$ARCH\" = \"arm64\" ]; then ARCH=\"arm64\"; else echo \"Unsupported architecture: $ARCH\"; exit 1; fi; URL=\"https://mantra-ui-builder.s3.ap-south-1.amazonaws.com/dbctgrshemyk/v1/mantra-ui-builder-${OS}-${ARCH}\"; OUTPUT_FILE=\"mantra-ui-builder\"; if command -v curl > /dev/null; then curl -sSL \"$URL\" -o \"$OUTPUT_FILE\"; elif command -v wget > /dev/null; then wget -q \"$URL\" -O \"$OUTPUT_FILE\"; else echo \"Neither curl nor wget is available\"; exit 1; fi; chmod +x \"$OUTPUT_FILE\"; ./$OUTPUT_FILE'"}, "devDependencies": {"@builder.io/partytown": "0.10.2", "javascript-obfuscator": "^4.1.1", "prettier": "3.2.5", "prettier-plugin-jinja-template": "1.4.0", "terser-webpack-plugin": "^5.3.10", "webpack": "^5.95.0", "webpack-cli": "^5.1.4", "webpack-obfuscator": "^3.5.1"}}