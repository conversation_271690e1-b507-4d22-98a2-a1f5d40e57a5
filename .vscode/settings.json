{
    "diffEditor.renderSideBySide": false,
    "editor.wordWrap": "wordWrapColumn",
    "editor.wordWrapColumn": 200,
    "editor.detectIndentation": false,
    "editor.insertSpaces": true,
    "editor.tabSize": 4,
    "editor.rulers": [
        200
    ],
    "python.autoComplete.extraPaths": [],
    "explorer.confirmDragAndDrop": false,
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "[markdown]": {
        "editor.formatOnSave": true,
        "editor.wordWrap": "on",
        "editor.renderWhitespace": "all",
        "editor.acceptSuggestionOnEnter": "on"
    },
    "python.analysis.extraPaths": [],
    "python.analysis.completeFunctionParens": true,
    "python.languageServer": "Pylance",
    "black-formatter.args": [
        "--line-length",
        "200"
    ],
    "[python]": {
        "editor.formatOnType": true,
        "editor.formatOnSave": true,
        "editor.formatOnSaveMode": "file",
        "editor.defaultFormatter": "ms-python.black-formatter",
        "editor.codeActionsOnSave": {
            "source.fixAll": "always",
            "source.organizeImports": "always"
        },
    },
    "isort.args": [
        "--profile",
        "black"
    ],
    "isort.check": true,
    "python.analysis.typeCheckingMode": "strict",
    "search.useIgnoreFiles": false,
    "python.analysis.autoFormatStrings": true,
    "python.analysis.autoImportCompletions": true,
    "python.analysis.gotoDefinitionInStringLiteral": true,
    "python.analysis.importFormat": "absolute",
    "python.analysis.inlayHints.callArgumentNames": "off",
    "search.exclude": {
        "**/.venv": true
    },
    "files.exclude": {
        "**/.venv": true
    }
}