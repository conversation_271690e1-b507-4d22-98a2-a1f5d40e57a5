name: AUTO-DEPLOY-PIPELINE
run-name: auto-deploy-pipeline-dev

on:
  workflow_dispatch:
  push:
    branches:
      - develop
      
jobs:
  deploy:
    runs-on: ubuntu-22.04
    env:
      SSH_AUTH_SOCK: /tmp/ssh_agent.sock

    steps:
      - uses: actions/checkout@v4
        name: "🚶‍♂️ checkout to branch"
        with:
          ref: develop

      - name: "🔩 setup ssh key"
        run: |
          #!/bin/bash

          ssh-agent -a $SSH_AUTH_SOCK > /dev/null
          ssh-add - <<< "${{ secrets.SSH_PEM }}"
        env:
          SSH_AUTH_SOCK: /tmp/ssh_agent.sock
        shell: bash
      
      - name: "🌸 install tailwindcss"
        run: |
          #!/bin/bash

          npm install
        shell: bash
        working-directory: tailwindcss
      
      - name: "🌹 build tailwindcss and minify js"
        run: |
          #!/bin/bash
          npm install
          npm run tw-prod-build
        shell: bash
        env:
          BUILDER_KEY: ${{ secrets.BUILDER_KEY }}
      
      - name: "📤 copy app/static/app.css to server"
        run: |
          #!/bin/bash
          scp -o StrictHostKeyChecking=no static/css/app.css root@************:/opt/app/ai-bull/static/css/app.css
        shell: bash

      - name: "📡 ssh and deploy"
        run: |
          #!/bin/bash

          ssh -o StrictHostKeyChecking=no root@************ <<'ENDSSH'
            cd /opt/app/ai-bull
            git stash
            git pull origin develop
            
            # Install Python dependencies
            poetry install
            
            # Restart services
            pkill uvicorn
            ps -ef | grep python | awk -F " " '{print $2}' | xargs kill -9 | true
            systemctl restart ai-bull.service
            
            # check the logs at /var/log/ai-bull.log if you want to see the logs
          ENDSSH
        shell: bash
