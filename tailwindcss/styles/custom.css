/* custom css should be added here */
@font-face {
  font-family: "poppins-black";
  src: url("/static/font/Poppins-Black.ttf");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  src: url("/static/font/Poppins-ExtraBold.ttf");
  font-family: "poppins-extrabold";
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  src: url("/static/font/Poppins-Bold.ttf");
  font-family: "poppins-bold";
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  src: url("/static/font/Poppins-SemiBold.ttf");
  font-family: "poppins-semibold";
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  src: url("/static/font/Poppins-Medium.ttf");
  font-family: "poppins-medium";
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "poppins-regular";
  src: url("/static/font/Poppins-Regular.ttf");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "poppins-light";
  src: url("/static/font/Poppins-Light.ttf");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

.font-black {
  font-family: "poppins-black", sans-serif;
}

.font-extrabold {
  font-family: "poppins-extrabold", sans-serif;
}

.font-bold {
  font-family: "poppins-bold", sans-serif;
}

.font-semiBold,.font-semibold {
  font-family: "poppins-SemiBold", sans-serif;
}

.font-medium {
  font-family: "poppins-medium", sans-serif;
}

.font-light {
  font-family: "poppins-Light", sans-serif;
}

body {
  font-family: "poppins-regular", sans-serif;
  font-weight: 400;
}

.zoom-in {
  animation: zoom-in 0.6s ease-in-out;
}

.alret-modal {
  animation: scaleUp 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

@keyframes zoom-in {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleUp {
  0% {
    transform: scale(0.8) translateY(1000px);
    opacity: 0;
  }

  100% {
    transform: scale(1) translateY(0px);
    opacity: 1;
  }
}

.fadeInLeft {
  animation: fadeInLeft 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.scrollbar::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.scrollbar::-webkit-scrollbar-thumb {
    border-radius: 4px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}/* add your custom css here dont dump everything */

html {
  scroll-behavior: smooth;
}
